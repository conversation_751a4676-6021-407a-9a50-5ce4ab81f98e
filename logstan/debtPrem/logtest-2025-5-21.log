2025-5-21 15:04:59 170|pool-26-thread-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990042716527</policyCode>
  <busiItemId>3600000373920</busiItemId>
  <dateFlag>2024-05-05 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-5-21 15:04:59 170|pool-26-thread-1|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990042716527</policyCode>
  <busiItemId>3600000373919</busiItemId>
  <dateFlag>2024-05-05 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-5-21 15:04:59 689|pool-26-thread-2|INFO|=========短期险=====990042716527|DebtPremServiceImpl.java|187|
2025-5-21 15:05:00 092|pool-26-thread-1|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>181496</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|325|
2025-5-21 15:05:01 112|pool-26-thread-1|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2024-11-01 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|60|
2025-5-21 15:05:01 112|pool-26-thread-1|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：2032|DebtPremUCCImpl.java|61|
2025-5-21 15:05:01 212|pool-26-thread-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|60|
2025-5-21 15:05:01 212|pool-26-thread-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：2135|DebtPremUCCImpl.java|61|
2025-5-21 15:05:15 283|http-bio-9090-exec-1|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990042716527</policyCode>
  <busiItemId>3600000373920</busiItemId>
  <dateFlag class="sql-timestamp">2024-05-06 00:00:00.0</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-5-21 15:05:15 298|http-bio-9090-exec-1|INFO|=========短期险=====990042716527|DebtPremServiceImpl.java|187|
2025-5-21 15:05:15 358|http-bio-9090-exec-1|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|60|
2025-5-21 15:05:15 358|http-bio-9090-exec-1|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：90|DebtPremUCCImpl.java|61|
2025-5-21 15:20:00 072|pool-29-thread-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990042716527</policyCode>
  <busiItemId>3600000373920</busiItemId>
  <dateFlag>2024-05-05 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-5-21 15:20:00 072|pool-29-thread-1|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990042716527</policyCode>
  <busiItemId>3600000373919</busiItemId>
  <dateFlag>2024-05-05 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-5-21 15:20:00 092|pool-29-thread-2|INFO|=========短期险=====990042716527|DebtPremServiceImpl.java|187|
2025-5-21 15:20:00 137|pool-29-thread-1|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>181496</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|325|
2025-5-21 15:20:00 178|pool-29-thread-1|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2024-11-01 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|60|
2025-5-21 15:20:00 178|pool-29-thread-1|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：108|DebtPremUCCImpl.java|61|
2025-5-21 15:20:00 178|pool-29-thread-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|60|
2025-5-21 15:20:00 178|pool-29-thread-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：108|DebtPremUCCImpl.java|61|
2025-5-21 15:20:02 793|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990042716527</policyCode>
  <busiItemId>3600000373920</busiItemId>
  <dateFlag class="sql-timestamp">2024-05-06 00:00:00.0</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-5-21 15:20:02 808|http-bio-9090-exec-2|INFO|=========短期险=====990042716527|DebtPremServiceImpl.java|187|
2025-5-21 15:20:02 838|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|60|
2025-5-21 15:20:02 838|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：45|DebtPremUCCImpl.java|61|
