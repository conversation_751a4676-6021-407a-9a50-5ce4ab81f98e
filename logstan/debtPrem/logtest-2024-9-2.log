2024-9-2 16:08:34 884|http-bio-9090-exec-6|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990040986715</policyCode>
  <busiItemId>3600000204664</busiItemId>
  <dateFlag>2025-12-25 08:08:34.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2024-9-2 16:08:34 940|http-bio-9090-exec-6|INFO|=========短期险=====990040986715|DebtPremServiceImpl.java|172|
2024-9-2 16:08:35 126|http-bio-9090-exec-6|INFO|=====================保证续保重算保费===========990040986715|DebtPremServiceImpl.java|200|
2024-9-2 16:08:36 069|http-bio-9090-exec-6|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>181604</businessPrdId>
      <chargeMode>1</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2024-9-2 16:08:36 170|http-bio-9090-exec-6|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>755.00</debtValue>
  <payDueDate>2025-03-29 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2024-9-2 16:08:36 171|http-bio-9090-exec-6|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：1713|DebtPremUCCImpl.java|60|
2024-9-2 16:08:36 315|http-bio-9090-exec-6|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990040986715</policyCode>
  <busiItemId>3600000204664</busiItemId>
  <dateFlag>2024-05-31 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2024-9-2 16:08:36 322|http-bio-9090-exec-6|INFO|=========短期险=====990040986715|DebtPremServiceImpl.java|172|
2024-9-2 16:08:36 341|http-bio-9090-exec-6|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>181604</businessPrdId>
      <chargeMode>1</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2024-9-2 16:08:36 350|http-bio-9090-exec-6|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-03-29 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2024-9-2 16:08:36 352|http-bio-9090-exec-6|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：35|DebtPremUCCImpl.java|60|
2024-9-2 16:20:56 918|http-bio-9090-exec-13|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990040986715</policyCode>
  <busiItemId>3600000204664</busiItemId>
  <dateFlag>2025-12-25 08:20:56.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2024-9-2 16:20:56 962|http-bio-9090-exec-13|INFO|=========短期险=====990040986715|DebtPremServiceImpl.java|172|
2024-9-2 16:20:57 050|http-bio-9090-exec-13|INFO|=====================保证续保重算保费===========990040986715|DebtPremServiceImpl.java|200|
2024-9-2 16:20:57 378|http-bio-9090-exec-13|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>181604</businessPrdId>
      <chargeMode>1</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2024-9-2 16:20:57 402|http-bio-9090-exec-13|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>755.00</debtValue>
  <payDueDate>2025-03-29 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2024-9-2 16:20:57 402|http-bio-9090-exec-13|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：486|DebtPremUCCImpl.java|60|
2024-9-2 16:20:57 497|http-bio-9090-exec-13|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990040986715</policyCode>
  <busiItemId>3600000204664</busiItemId>
  <dateFlag>2024-05-31 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2024-9-2 16:20:57 510|http-bio-9090-exec-13|INFO|=========短期险=====990040986715|DebtPremServiceImpl.java|172|
2024-9-2 16:20:57 539|http-bio-9090-exec-13|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>181604</businessPrdId>
      <chargeMode>1</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2024-9-2 16:20:57 556|http-bio-9090-exec-13|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-03-29 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2024-9-2 16:20:57 557|http-bio-9090-exec-13|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：59|DebtPremUCCImpl.java|60|
