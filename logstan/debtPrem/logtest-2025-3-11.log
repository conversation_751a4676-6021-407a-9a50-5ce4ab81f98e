2025-3-11 16:02:48 304|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 08:02:47.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:02:48 441|http-bio-9090-exec-4|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:02:48 759|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:02:48 759|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：1121|DebtPremUCCImpl.java|60|
2025-3-11 16:02:48 967|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 08:02:48.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:02:48 988|http-bio-9090-exec-4|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:02:49 001|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:02:49 002|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：33|DebtPremUCCImpl.java|60|
2025-3-11 16:02:49 004|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 08:02:49.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:02:49 021|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:02:49 021|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：17|DebtPremUCCImpl.java|60|
2025-3-11 16:02:49 051|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2020-08-18 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:02:49 066|http-bio-9090-exec-4|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:02:49 081|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:02:49 081|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：30|DebtPremUCCImpl.java|60|
2025-3-11 16:02:49 081|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2020-08-18 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:02:49 081|http-bio-9090-exec-4|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:02:49 096|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:02:49 096|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 16:02:49 115|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2020-08-18 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:02:49 141|http-bio-9090-exec-4|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157789</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:02:49 141|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:02:49 141|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：30|DebtPremUCCImpl.java|60|
2025-3-11 16:03:50 855|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 08:03:50.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:03:50 915|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:03:50 930|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:03:50 930|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：90|DebtPremUCCImpl.java|60|
2025-3-11 16:03:50 960|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 08:03:50.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:03:50 991|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:03:51 006|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:03:51 006|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：46|DebtPremUCCImpl.java|60|
2025-3-11 16:03:51 006|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 08:03:51.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:03:51 051|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:03:51 051|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：45|DebtPremUCCImpl.java|60|
2025-3-11 16:03:51 096|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2020-08-18 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:03:51 111|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:03:51 126|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:03:51 126|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：30|DebtPremUCCImpl.java|60|
2025-3-11 16:03:51 126|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2020-08-18 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:03:51 141|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:03:51 141|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:03:51 141|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 16:03:51 141|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2020-08-18 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:03:51 156|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157789</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:03:51 171|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:03:51 171|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：30|DebtPremUCCImpl.java|60|
2025-3-11 16:14:21 007|http-bio-9090-exec-6|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 08:14:21.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:14:21 052|http-bio-9090-exec-6|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:14:21 060|http-bio-9090-exec-6|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:14:21 060|http-bio-9090-exec-6|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：53|DebtPremUCCImpl.java|60|
2025-3-11 16:14:21 087|http-bio-9090-exec-6|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 08:14:21.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:14:21 123|http-bio-9090-exec-6|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:14:21 136|http-bio-9090-exec-6|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:14:21 136|http-bio-9090-exec-6|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：50|DebtPremUCCImpl.java|60|
2025-3-11 16:14:21 140|http-bio-9090-exec-6|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 08:14:21.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:14:21 166|http-bio-9090-exec-6|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:14:21 167|http-bio-9090-exec-6|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：26|DebtPremUCCImpl.java|60|
2025-3-11 16:14:21 199|http-bio-9090-exec-6|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2020-08-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:14:21 199|http-bio-9090-exec-6|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:14:21 199|http-bio-9090-exec-6|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:14:21 199|http-bio-9090-exec-6|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：0|DebtPremUCCImpl.java|60|
2025-3-11 16:14:21 199|http-bio-9090-exec-6|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2020-08-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:14:21 218|http-bio-9090-exec-6|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:14:21 222|http-bio-9090-exec-6|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:14:21 222|http-bio-9090-exec-6|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：23|DebtPremUCCImpl.java|60|
2025-3-11 16:14:21 226|http-bio-9090-exec-6|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2020-08-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:14:21 263|http-bio-9090-exec-6|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157789</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:14:21 283|http-bio-9090-exec-6|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:14:21 285|http-bio-9090-exec-6|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：57|DebtPremUCCImpl.java|60|
2025-3-11 16:17:33 774|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 08:17:33.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:17:33 835|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:17:33 850|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:17:33 850|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：76|DebtPremUCCImpl.java|60|
2025-3-11 16:17:33 871|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 08:17:33.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:17:33 886|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:17:33 896|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:17:33 896|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：25|DebtPremUCCImpl.java|60|
2025-3-11 16:17:33 896|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 08:17:33.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:17:33 911|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:17:33 911|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 16:17:33 911|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2020-08-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:17:33 926|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:17:33 926|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:17:33 926|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 16:17:33 926|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2020-08-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:17:33 934|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:17:33 936|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:17:33 936|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：10|DebtPremUCCImpl.java|60|
2025-3-11 16:17:33 938|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2020-08-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:17:33 938|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157789</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:17:33 938|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:17:33 938|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：0|DebtPremUCCImpl.java|60|
2025-3-11 16:41:56 867|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 08:41:56.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:41:56 882|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:41:56 897|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:41:56 897|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：30|DebtPremUCCImpl.java|60|
2025-3-11 16:41:56 914|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 08:41:56.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:41:56 922|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:41:56 934|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:41:56 934|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：20|DebtPremUCCImpl.java|60|
2025-3-11 16:41:56 934|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 08:41:56.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:41:56 950|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:41:56 950|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：16|DebtPremUCCImpl.java|60|
2025-3-11 16:41:56 966|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2020-08-20 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:41:56 968|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:41:56 975|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:41:56 975|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：9|DebtPremUCCImpl.java|60|
2025-3-11 16:41:56 975|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2020-08-20 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:41:56 980|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:41:56 980|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:41:56 980|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：5|DebtPremUCCImpl.java|60|
2025-3-11 16:41:56 980|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2020-08-20 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:41:56 983|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157789</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:41:56 983|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:41:56 983|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：3|DebtPremUCCImpl.java|60|
2025-3-11 16:46:04 637|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 08:46:04.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:04 667|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:46:04 679|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:04 679|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：42|DebtPremUCCImpl.java|60|
2025-3-11 16:46:04 682|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 08:46:04.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:04 697|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:46:04 728|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:04 728|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：46|DebtPremUCCImpl.java|60|
2025-3-11 16:46:04 728|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 08:46:04.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:04 750|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:04 750|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：22|DebtPremUCCImpl.java|60|
2025-3-11 16:46:04 771|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2020-08-20 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:04 771|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:46:04 779|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:04 779|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：8|DebtPremUCCImpl.java|60|
2025-3-11 16:46:04 779|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2020-08-20 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:04 781|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:46:04 781|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:04 781|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：2|DebtPremUCCImpl.java|60|
2025-3-11 16:46:04 781|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2020-08-20 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:04 796|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157789</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:46:04 801|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:04 801|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：20|DebtPremUCCImpl.java|60|
2025-3-11 16:46:35 251|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 08:46:35.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:35 266|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:46:35 266|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:35 281|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 16:46:35 281|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 08:46:35.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:35 299|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:46:35 309|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:35 309|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：28|DebtPremUCCImpl.java|60|
2025-3-11 16:46:35 311|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 08:46:35.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:35 311|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:35 311|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：0|DebtPremUCCImpl.java|60|
2025-3-11 16:46:35 335|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2020-08-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:35 335|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:46:35 335|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:35 335|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：0|DebtPremUCCImpl.java|60|
2025-3-11 16:46:35 335|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2020-08-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:35 335|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:46:35 335|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:35 335|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：0|DebtPremUCCImpl.java|60|
2025-3-11 16:46:35 335|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2020-08-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:46:35 350|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157789</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:46:35 350|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:46:35 359|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 16:47:17 092|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 08:47:17.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:47:17 118|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:47:17 123|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:47:17 123|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：31|DebtPremUCCImpl.java|60|
2025-3-11 16:47:17 131|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 08:47:17.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:47:17 159|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:47:17 163|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:47:17 163|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：32|DebtPremUCCImpl.java|60|
2025-3-11 16:47:17 163|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 08:47:17.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:47:17 174|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:47:17 178|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：11|DebtPremUCCImpl.java|60|
2025-3-11 16:47:17 193|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2020-08-20 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:47:17 204|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:47:17 204|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:47:17 209|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：11|DebtPremUCCImpl.java|60|
2025-3-11 16:47:17 209|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2020-08-20 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:47:17 214|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:47:17 214|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:47:17 214|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：5|DebtPremUCCImpl.java|60|
2025-3-11 16:47:17 214|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2020-08-20 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 16:47:17 218|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157789</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 16:47:17 236|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 16:47:17 236|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：22|DebtPremUCCImpl.java|60|
2025-3-11 17:00:40 046|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 09:00:40.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:00:40 076|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:00:40 076|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:00:40 076|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：30|DebtPremUCCImpl.java|60|
2025-3-11 17:00:40 091|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 09:00:40.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:00:40 104|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:00:40 115|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:00:40 115|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：24|DebtPremUCCImpl.java|60|
2025-3-11 17:00:40 115|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 09:00:40.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:00:40 130|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:00:40 130|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 17:00:40 145|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2020-08-23 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:00:40 145|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:00:40 160|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:00:40 162|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 17:00:40 162|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2020-08-23 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:00:40 168|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:00:40 171|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:00:40 171|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：6|DebtPremUCCImpl.java|60|
2025-3-11 17:00:40 171|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2020-08-23 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:00:40 171|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157789</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:00:40 171|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:00:40 171|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：0|DebtPremUCCImpl.java|60|
2025-3-11 17:13:30 196|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 09:13:30.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:30 213|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:13:30 227|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:30 227|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：31|DebtPremUCCImpl.java|60|
2025-3-11 17:13:30 242|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 09:13:30.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:30 253|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:13:30 253|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:30 253|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：11|DebtPremUCCImpl.java|60|
2025-3-11 17:13:30 253|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 09:13:30.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:30 268|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:30 268|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 17:13:30 297|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-09 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:30 313|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:13:30 313|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:30 313|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：16|DebtPremUCCImpl.java|60|
2025-3-11 17:13:30 313|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-09 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:30 323|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:13:30 323|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:30 323|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：10|DebtPremUCCImpl.java|60|
2025-3-11 17:13:30 323|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-09 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:30 323|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:30 323|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：0|DebtPremUCCImpl.java|60|
2025-3-11 17:13:58 321|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 09:13:58.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:58 366|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:13:58 366|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:58 366|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：45|DebtPremUCCImpl.java|60|
2025-3-11 17:13:58 396|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 09:13:58.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:58 411|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:13:58 411|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:58 411|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：30|DebtPremUCCImpl.java|60|
2025-3-11 17:13:58 411|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 09:13:58.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:58 441|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:58 441|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：30|DebtPremUCCImpl.java|60|
2025-3-11 17:13:58 456|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-09 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:58 471|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:13:58 471|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:58 471|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 17:13:58 471|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-09 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:58 486|http-bio-9090-exec-7|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 17:13:58 486|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:58 486|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 17:13:58 486|http-bio-9090-exec-7|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-09 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 17:13:58 501|http-bio-9090-exec-7|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 17:13:58 501|http-bio-9090-exec-7|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 18:22:34 036|http-bio-9090-exec-9|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2025-03-11 10:22:34.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 18:22:34 355|http-bio-9090-exec-9|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 18:22:34 370|http-bio-9090-exec-9|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>21480</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 18:22:34 370|http-bio-9090-exec-9|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：334|DebtPremUCCImpl.java|60|
2025-3-11 18:22:34 417|http-bio-9090-exec-9|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2025-03-11 10:22:34.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 18:22:34 447|http-bio-9090-exec-9|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 18:22:34 465|http-bio-9090-exec-9|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>55.84</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 18:22:34 466|http-bio-9090-exec-9|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：50|DebtPremUCCImpl.java|60|
2025-3-11 18:22:34 471|http-bio-9090-exec-9|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2025-03-11 10:22:34.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 18:22:34 530|http-bio-9090-exec-9|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>5000</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 18:22:34 532|http-bio-9090-exec-9|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：60|DebtPremUCCImpl.java|60|
2025-3-11 18:22:34 564|http-bio-9090-exec-9|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>87170</busiItemId>
  <dateFlag>2020-08-22 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 18:22:34 589|http-bio-9090-exec-9|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 18:22:34 594|http-bio-9090-exec-9|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 18:22:34 609|http-bio-9090-exec-9|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：30|DebtPremUCCImpl.java|60|
2025-3-11 18:22:34 609|http-bio-9090-exec-9|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990025608517</policyCode>
  <busiItemId>144969</busiItemId>
  <dateFlag>2020-08-22 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 18:22:34 624|http-bio-9090-exec-9|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157803</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 18:22:34 639|http-bio-9090-exec-9|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2021-08-26 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 18:22:34 639|http-bio-9090-exec-9|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：15|DebtPremUCCImpl.java|60|
2025-3-11 18:22:34 669|http-bio-9090-exec-9|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>997001214690</policyCode>
  <busiItemId>98867</busiItemId>
  <dateFlag>2020-08-22 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-11 18:22:34 744|http-bio-9090-exec-9|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>157789</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-11 18:22:34 759|http-bio-9090-exec-9|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2020-11-11 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-11 18:22:34 759|http-bio-9090-exec-9|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：90|DebtPremUCCImpl.java|60|
