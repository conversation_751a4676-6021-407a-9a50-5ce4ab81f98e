2025-1-3 16:49:46 451|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990038480799</policyCode>
  <busiItemId>3600000001410</busiItemId>
  <dateFlag>2023-09-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-1-3 16:49:46 542|http-bio-9090-exec-4|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>181547</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-1-3 16:49:46 579|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2024-08-21 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-1-3 16:49:46 579|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：187|DebtPremUCCImpl.java|60|
2025-1-3 16:49:53 240|http-bio-9090-exec-3|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990038480799</policyCode>
  <busiItemId>3600000001410</busiItemId>
  <dateFlag>2023-09-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-1-3 16:49:53 298|http-bio-9090-exec-3|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>181547</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-1-3 16:49:53 343|http-bio-9090-exec-3|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2024-08-21 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-1-3 16:49:53 343|http-bio-9090-exec-3|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：105|DebtPremUCCImpl.java|60|
2025-1-3 16:50:33 841|http-bio-9090-exec-11|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990038480799</policyCode>
  <busiItemId>3600000001410</busiItemId>
  <dateFlag>2023-09-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-1-3 16:50:33 872|http-bio-9090-exec-11|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>181547</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-1-3 16:50:34 073|http-bio-9090-exec-11|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2024-08-21 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-1-3 16:50:34 073|http-bio-9090-exec-11|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：232|DebtPremUCCImpl.java|60|
2025-1-3 17:33:36 995|http-bio-9090-exec-17|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990038480799</policyCode>
  <busiItemId>3600000001410</busiItemId>
  <dateFlag>2023-09-19 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-1-3 17:33:37 101|http-bio-9090-exec-17|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>181547</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-1-3 17:33:37 147|http-bio-9090-exec-17|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2024-08-21 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-1-3 17:33:37 147|http-bio-9090-exec-17|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：152|DebtPremUCCImpl.java|60|
