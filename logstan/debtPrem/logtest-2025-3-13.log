2025-3-13 16:15:41 471|http-bio-9090-exec-13|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2025-12-08 08:15:41.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:15:41 885|http-bio-9090-exec-13|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:15:43 280|http-bio-9090-exec-13|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>31605</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:15:43 280|http-bio-9090-exec-13|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：1953|DebtPremUCCImpl.java|60|
2025-3-13 16:15:44 088|http-bio-9090-exec-13|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2024-05-08 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:15:44 139|http-bio-9090-exec-13|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:15:44 179|http-bio-9090-exec-13|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:15:44 183|http-bio-9090-exec-13|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：91|DebtPremUCCImpl.java|60|
2025-3-13 16:16:41 597|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2025-12-08 08:16:41.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:16:41 694|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:16:41 742|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>31605</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:16:41 742|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：145|DebtPremUCCImpl.java|60|
2025-3-13 16:16:41 867|http-bio-9090-exec-2|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2024-05-08 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:16:41 912|http-bio-9090-exec-2|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:16:41 943|http-bio-9090-exec-2|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:16:41 943|http-bio-9090-exec-2|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：76|DebtPremUCCImpl.java|60|
2025-3-13 16:20:17 040|http-bio-9090-exec-3|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2025-12-08 08:20:17.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:20:17 152|http-bio-9090-exec-3|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:20:17 204|http-bio-9090-exec-3|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>31605</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:20:17 205|http-bio-9090-exec-3|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：164|DebtPremUCCImpl.java|60|
2025-3-13 16:20:17 311|http-bio-9090-exec-3|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2024-05-08 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:20:17 356|http-bio-9090-exec-3|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:20:17 386|http-bio-9090-exec-3|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:20:17 386|http-bio-9090-exec-3|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：75|DebtPremUCCImpl.java|60|
2025-3-13 16:23:05 086|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2025-12-08 08:23:05.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:23:05 132|http-bio-9090-exec-4|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:23:05 141|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>31605</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:23:05 141|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：55|DebtPremUCCImpl.java|60|
2025-3-13 16:23:05 197|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2024-05-08 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:23:05 203|http-bio-9090-exec-4|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:23:05 222|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:23:05 222|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：25|DebtPremUCCImpl.java|60|
2025-3-13 16:24:08 253|http-bio-9090-exec-9|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2025-12-08 08:24:08.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:24:08 333|http-bio-9090-exec-9|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:24:08 385|http-bio-9090-exec-9|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>31605</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:24:08 385|http-bio-9090-exec-9|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：132|DebtPremUCCImpl.java|60|
2025-3-13 16:24:08 514|http-bio-9090-exec-9|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2024-05-08 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:24:08 547|http-bio-9090-exec-9|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:24:08 575|http-bio-9090-exec-9|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:24:08 575|http-bio-9090-exec-9|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：61|DebtPremUCCImpl.java|60|
2025-3-13 16:35:38 165|http-bio-9090-exec-13|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2025-05-10 08:35:38.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:35:38 269|http-bio-9090-exec-13|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:35:38 309|http-bio-9090-exec-13|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>31605</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:35:38 309|http-bio-9090-exec-13|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：140|DebtPremUCCImpl.java|60|
2025-3-13 16:35:38 438|http-bio-9090-exec-13|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2024-05-08 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:35:38 468|http-bio-9090-exec-13|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:35:38 486|http-bio-9090-exec-13|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:35:38 486|http-bio-9090-exec-13|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：61|DebtPremUCCImpl.java|60|
2025-3-13 16:44:12 955|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2025-05-10 08:44:12.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:44:13 041|http-bio-9090-exec-4|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:44:13 077|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>31605</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:44:13 077|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：122|DebtPremUCCImpl.java|60|
2025-3-13 16:44:13 178|http-bio-9090-exec-4|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2024-05-08 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:44:13 227|http-bio-9090-exec-4|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:44:13 262|http-bio-9090-exec-4|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:44:13 262|http-bio-9090-exec-4|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：77|DebtPremUCCImpl.java|60|
2025-3-13 16:44:22 216|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2025-05-10 08:44:22.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:44:22 298|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:44:22 341|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>31605</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:44:22 341|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：125|DebtPremUCCImpl.java|60|
2025-3-13 16:44:22 537|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2024-05-08 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:44:22 584|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:44:22 622|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:44:22 622|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：87|DebtPremUCCImpl.java|60|
2025-3-13 16:44:33 563|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2025-05-10 08:44:33.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:44:33 659|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:44:33 692|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>31605</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:44:33 692|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：129|DebtPremUCCImpl.java|60|
2025-3-13 16:44:33 836|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2024-05-08 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:44:33 870|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:44:33 900|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:44:33 900|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：64|DebtPremUCCImpl.java|60|
2025-3-13 16:44:56 264|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2025-05-10 08:44:56.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:44:56 341|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:44:56 386|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>31605</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:44:56 387|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：122|DebtPremUCCImpl.java|60|
2025-3-13 16:44:56 503|http-bio-9090-exec-10|INFO|查询出险日欠缴保费入参:<com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>
  <policyCode>990041516535</policyCode>
  <busiItemId>3600000227280</busiItemId>
  <dateFlag>2024-05-08 16:00:00.0 UTC</dateFlag>
  <flag>false</flag>
</com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData>|DebtPremUCCImpl.java|53|
2025-3-13 16:44:56 546|http-bio-9090-exec-10|INFO|<com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>
  <genericOfMmsGracePeriodQueryReqVOList>
    <com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
      <urgeFlag>0</urgeFlag>
      <businessPrdId>1542</businessPrdId>
      <chargeMode>5</chargeMode>
      <delFlag>0</delFlag>
      <periodType>60002</periodType>
      <renewGracePeriod>1</renewGracePeriod>
    </com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.GenericOfMmsGracePeriodQueryReqVO>
  </genericOfMmsGracePeriodQueryReqVOList>
</com.nci.tunan.mms.interfaces.query.exports.graceperiodquery.vo.MmsGracePeriodQueryReqVO>|DebtPremServiceImpl.java|296|
2025-3-13 16:44:56 561|http-bio-9090-exec-10|INFO|查询出险日欠缴保费出参:<com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>
  <debtValue>0</debtValue>
  <payDueDate>2025-04-20 16:00:00.0 UTC</payDueDate>
</com.nci.tunan.pa.interfaces.serviceData.endcase.DebtPremResData>|DebtPremUCCImpl.java|59|
2025-3-13 16:44:56 561|http-bio-9090-exec-10|INFO|DebtPremUCCImpl.queryDebtPrem()共计耗时：58|DebtPremUCCImpl.java|60|
