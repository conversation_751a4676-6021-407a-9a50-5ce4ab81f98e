2025-2-28 10:32:24 578|http-bio-9090-exec-3|DEBUG|==>  Preparing: SELECT * FROM (select tcbp.policy_id, tcbp.policy_code, tcbp.busi_item_id, tcbp.busi_prod_code, tcbp.busi_prd_id, tcbp.validate_date, tcm.validate_date as policy_effective_date, tcbp.maturity_date from (select product_code_sys from APP___PDS__DBUSER.t_business_product tbp where tbp.product_category1 = '20002') a left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on tcbp.busi_prod_code = a.product_code_sys left join APP___PAS__DBUSER.t_contract_master tcm on tcbp.policy_id = tcm.policy_id where 1 = 1 and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01')) AND NOT EXISTS (SELECT 'X' FROM APP___PAS__DBUSER.T_LOCK_POLICY A LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID WHERE B.SUB_ID IN ('068', '067') AND B.LOCK_SERVICE_TYPE = 1 AND A.POLICY_CODE = TCBP.POLICY_CODE) AND TCBP.POLICY_CODE = ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:32:24 578|http-bio-9090-exec-3|DEBUG|==> Parameters: 990038596832(String)|BaseJdbcLogger.java|139|
2025-2-28 10:32:24 578|http-bio-9090-exec-3|DEBUG|<==      Total: 0|BaseJdbcLogger.java|139|
2025-2-28 10:32:26 267|http-bio-9090-exec-3|DEBUG|==>  Preparing: SELECT * FROM (SELECT SUM(A.BONUS_SA) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 AND A.POLICY_ID = ? AND A.ITEM_ID = ? AND A.POLICY_CODE = ? AND A.BUSI_ITEM_ID = ? AND A.BONUS_ALLOT in ('1','2','6') AND A.ALLOCATE_DUE_DATE <= ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:32:26 267|http-bio-9090-exec-3|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 10:32:24.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:32:26 282|http-bio-9090-exec-3|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:32:26 282|http-bio-9090-exec-3|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:32:26 282|http-bio-9090-exec-3|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:32:26 282|http-bio-9090-exec-3|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 10:32:24.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:32:26 282|http-bio-9090-exec-3|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:32:26 282|http-bio-9090-exec-3|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:32:26 282|http-bio-9090-exec-3|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:32:29 243|http-bio-9090-exec-3|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 10:32:29.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:32:29 243|http-bio-9090-exec-3|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:32:29 258|http-bio-9090-exec-3|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:32:29 258|http-bio-9090-exec-3|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:32:29 258|http-bio-9090-exec-3|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 10:32:29.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:32:29 258|http-bio-9090-exec-3|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:32:29 258|http-bio-9090-exec-3|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:32:29 273|http-bio-9090-exec-3|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:46:36 855|http-bio-9090-exec-9|DEBUG|==>  Preparing: SELECT * FROM (select tcbp.policy_id, tcbp.policy_code, tcbp.busi_item_id, tcbp.busi_prod_code, tcbp.busi_prd_id, tcbp.validate_date, tcm.validate_date as policy_effective_date, tcbp.maturity_date from (select product_code_sys from APP___PDS__DBUSER.t_business_product tbp where tbp.product_category1 = '20002') a left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on tcbp.busi_prod_code = a.product_code_sys left join APP___PAS__DBUSER.t_contract_master tcm on tcbp.policy_id = tcm.policy_id where 1 = 1 and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01')) AND NOT EXISTS (SELECT 'X' FROM APP___PAS__DBUSER.T_LOCK_POLICY A LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID WHERE B.SUB_ID IN ('068', '067') AND B.LOCK_SERVICE_TYPE = 1 AND A.POLICY_CODE = TCBP.POLICY_CODE) AND TCBP.POLICY_CODE = ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:46:36 870|http-bio-9090-exec-9|DEBUG|==> Parameters: 990038596832(String)|BaseJdbcLogger.java|139|
2025-2-28 10:46:36 870|http-bio-9090-exec-9|DEBUG|<==      Total: 0|BaseJdbcLogger.java|139|
2025-2-28 10:46:37 201|http-bio-9090-exec-9|DEBUG|==>  Preparing: SELECT * FROM (SELECT SUM(A.BONUS_SA) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 AND A.POLICY_ID = ? AND A.ITEM_ID = ? AND A.POLICY_CODE = ? AND A.BUSI_ITEM_ID = ? AND A.BONUS_ALLOT in ('1','2','6') AND A.ALLOCATE_DUE_DATE <= ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:46:37 201|http-bio-9090-exec-9|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 10:46:36.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:46:37 201|http-bio-9090-exec-9|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:46:37 201|http-bio-9090-exec-9|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:46:37 201|http-bio-9090-exec-9|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:46:37 216|http-bio-9090-exec-9|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 10:46:36.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:46:37 216|http-bio-9090-exec-9|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:46:37 216|http-bio-9090-exec-9|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:46:37 216|http-bio-9090-exec-9|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:46:37 668|http-bio-9090-exec-9|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 10:46:37.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:46:37 668|http-bio-9090-exec-9|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:46:37 668|http-bio-9090-exec-9|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:46:37 668|http-bio-9090-exec-9|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:46:37 668|http-bio-9090-exec-9|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 10:46:37.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:46:37 683|http-bio-9090-exec-9|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:46:37 683|http-bio-9090-exec-9|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:46:37 683|http-bio-9090-exec-9|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:51:52 470|http-bio-9090-exec-3|DEBUG|==>  Preparing: SELECT * FROM (select tcbp.policy_id, tcbp.policy_code, tcbp.busi_item_id, tcbp.busi_prod_code, tcbp.busi_prd_id, tcbp.validate_date, tcm.validate_date as policy_effective_date, tcbp.maturity_date from (select product_code_sys from APP___PDS__DBUSER.t_business_product tbp where tbp.product_category1 = '20002') a left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on tcbp.busi_prod_code = a.product_code_sys left join APP___PAS__DBUSER.t_contract_master tcm on tcbp.policy_id = tcm.policy_id where 1 = 1 and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01')) AND NOT EXISTS (SELECT 'X' FROM APP___PAS__DBUSER.T_LOCK_POLICY A LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID WHERE B.SUB_ID IN ('068', '067') AND B.LOCK_SERVICE_TYPE = 1 AND A.POLICY_CODE = TCBP.POLICY_CODE) AND TCBP.POLICY_CODE = ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:51:52 470|http-bio-9090-exec-3|DEBUG|==> Parameters: 990038596832(String)|BaseJdbcLogger.java|139|
2025-2-28 10:51:52 470|http-bio-9090-exec-3|DEBUG|<==      Total: 0|BaseJdbcLogger.java|139|
2025-2-28 10:51:52 645|http-bio-9090-exec-3|DEBUG|==>  Preparing: SELECT * FROM (SELECT SUM(A.BONUS_SA) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 AND A.POLICY_ID = ? AND A.ITEM_ID = ? AND A.POLICY_CODE = ? AND A.BUSI_ITEM_ID = ? AND A.BONUS_ALLOT in ('1','2','6') AND A.ALLOCATE_DUE_DATE <= ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:51:52 645|http-bio-9090-exec-3|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 10:51:52.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:51:52 645|http-bio-9090-exec-3|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:51:52 645|http-bio-9090-exec-3|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:51:52 645|http-bio-9090-exec-3|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:51:52 645|http-bio-9090-exec-3|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 10:51:52.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:51:52 659|http-bio-9090-exec-3|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:51:52 659|http-bio-9090-exec-3|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:51:52 659|http-bio-9090-exec-3|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:52:14 093|http-bio-9090-exec-2|DEBUG|==>  Preparing: SELECT * FROM (select tcbp.policy_id, tcbp.policy_code, tcbp.busi_item_id, tcbp.busi_prod_code, tcbp.busi_prd_id, tcbp.validate_date, tcm.validate_date as policy_effective_date, tcbp.maturity_date from (select product_code_sys from APP___PDS__DBUSER.t_business_product tbp where tbp.product_category1 = '20002') a left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on tcbp.busi_prod_code = a.product_code_sys left join APP___PAS__DBUSER.t_contract_master tcm on tcbp.policy_id = tcm.policy_id where 1 = 1 and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01')) AND NOT EXISTS (SELECT 'X' FROM APP___PAS__DBUSER.T_LOCK_POLICY A LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID WHERE B.SUB_ID IN ('068', '067') AND B.LOCK_SERVICE_TYPE = 1 AND A.POLICY_CODE = TCBP.POLICY_CODE) AND TCBP.POLICY_CODE = ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:52:14 093|http-bio-9090-exec-2|DEBUG|==> Parameters: 990038596832(String)|BaseJdbcLogger.java|139|
2025-2-28 10:52:14 093|http-bio-9090-exec-2|DEBUG|<==      Total: 0|BaseJdbcLogger.java|139|
2025-2-28 10:52:14 274|http-bio-9090-exec-2|DEBUG|==>  Preparing: SELECT * FROM (SELECT SUM(A.BONUS_SA) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 AND A.POLICY_ID = ? AND A.ITEM_ID = ? AND A.POLICY_CODE = ? AND A.BUSI_ITEM_ID = ? AND A.BONUS_ALLOT in ('1','2','6') AND A.ALLOCATE_DUE_DATE <= ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:52:14 286|http-bio-9090-exec-2|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 10:52:14.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:52:14 289|http-bio-9090-exec-2|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:52:14 289|http-bio-9090-exec-2|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:52:14 289|http-bio-9090-exec-2|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:52:14 289|http-bio-9090-exec-2|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 10:52:14.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:52:14 289|http-bio-9090-exec-2|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:52:14 289|http-bio-9090-exec-2|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:52:14 289|http-bio-9090-exec-2|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:52:33 393|http-bio-9090-exec-1|DEBUG|==>  Preparing: SELECT * FROM (select tcbp.policy_id, tcbp.policy_code, tcbp.busi_item_id, tcbp.busi_prod_code, tcbp.busi_prd_id, tcbp.validate_date, tcm.validate_date as policy_effective_date, tcbp.maturity_date from (select product_code_sys from APP___PDS__DBUSER.t_business_product tbp where tbp.product_category1 = '20002') a left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on tcbp.busi_prod_code = a.product_code_sys left join APP___PAS__DBUSER.t_contract_master tcm on tcbp.policy_id = tcm.policy_id where 1 = 1 and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01')) AND NOT EXISTS (SELECT 'X' FROM APP___PAS__DBUSER.T_LOCK_POLICY A LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID WHERE B.SUB_ID IN ('068', '067') AND B.LOCK_SERVICE_TYPE = 1 AND A.POLICY_CODE = TCBP.POLICY_CODE) AND TCBP.POLICY_CODE = ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:52:33 393|http-bio-9090-exec-1|DEBUG|==> Parameters: 990038596832(String)|BaseJdbcLogger.java|139|
2025-2-28 10:52:33 393|http-bio-9090-exec-1|DEBUG|<==      Total: 0|BaseJdbcLogger.java|139|
2025-2-28 10:52:33 573|http-bio-9090-exec-1|DEBUG|==>  Preparing: SELECT * FROM (SELECT SUM(A.BONUS_SA) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 AND A.POLICY_ID = ? AND A.ITEM_ID = ? AND A.POLICY_CODE = ? AND A.BUSI_ITEM_ID = ? AND A.BONUS_ALLOT in ('1','2','6') AND A.ALLOCATE_DUE_DATE <= ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:52:33 573|http-bio-9090-exec-1|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 10:52:33.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:52:33 573|http-bio-9090-exec-1|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:52:33 573|http-bio-9090-exec-1|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:52:33 588|http-bio-9090-exec-1|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:52:33 588|http-bio-9090-exec-1|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 10:52:33.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:52:33 588|http-bio-9090-exec-1|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:52:33 588|http-bio-9090-exec-1|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:52:33 588|http-bio-9090-exec-1|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:52:38 957|http-bio-9090-exec-1|DEBUG|==>  Preparing: SELECT * FROM (select tcbp.policy_id, tcbp.policy_code, tcbp.busi_item_id, tcbp.busi_prod_code, tcbp.busi_prd_id, tcbp.validate_date, tcm.validate_date as policy_effective_date, tcbp.maturity_date from (select product_code_sys from APP___PDS__DBUSER.t_business_product tbp where tbp.product_category1 = '20002') a left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on tcbp.busi_prod_code = a.product_code_sys left join APP___PAS__DBUSER.t_contract_master tcm on tcbp.policy_id = tcm.policy_id where 1 = 1 and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01')) AND NOT EXISTS (SELECT 'X' FROM APP___PAS__DBUSER.T_LOCK_POLICY A LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID WHERE B.SUB_ID IN ('068', '067') AND B.LOCK_SERVICE_TYPE = 1 AND A.POLICY_CODE = TCBP.POLICY_CODE) AND TCBP.POLICY_CODE = ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:52:38 957|http-bio-9090-exec-1|DEBUG|==> Parameters: 990038596832(String)|BaseJdbcLogger.java|139|
2025-2-28 10:52:38 957|http-bio-9090-exec-1|DEBUG|<==      Total: 0|BaseJdbcLogger.java|139|
2025-2-28 10:52:39 109|http-bio-9090-exec-1|DEBUG|==>  Preparing: SELECT * FROM (SELECT SUM(A.BONUS_SA) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 AND A.POLICY_ID = ? AND A.ITEM_ID = ? AND A.POLICY_CODE = ? AND A.BUSI_ITEM_ID = ? AND A.BONUS_ALLOT in ('1','2','6') AND A.ALLOCATE_DUE_DATE <= ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:52:39 109|http-bio-9090-exec-1|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 10:52:38.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:52:39 109|http-bio-9090-exec-1|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:52:39 109|http-bio-9090-exec-1|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:52:39 113|http-bio-9090-exec-1|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:52:39 113|http-bio-9090-exec-1|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 10:52:38.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:52:39 118|http-bio-9090-exec-1|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:52:39 118|http-bio-9090-exec-1|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:52:39 118|http-bio-9090-exec-1|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:52:39 366|http-bio-9090-exec-1|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 10:52:39.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:52:39 380|http-bio-9090-exec-1|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:52:39 380|http-bio-9090-exec-1|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:52:39 380|http-bio-9090-exec-1|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:52:39 384|http-bio-9090-exec-1|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 10:52:39.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:52:39 384|http-bio-9090-exec-1|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:52:39 388|http-bio-9090-exec-1|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:52:39 388|http-bio-9090-exec-1|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:53:32 129|http-bio-9090-exec-5|DEBUG|==>  Preparing: SELECT * FROM (select tcbp.policy_id, tcbp.policy_code, tcbp.busi_item_id, tcbp.busi_prod_code, tcbp.busi_prd_id, tcbp.validate_date, tcm.validate_date as policy_effective_date, tcbp.maturity_date from (select product_code_sys from APP___PDS__DBUSER.t_business_product tbp where tbp.product_category1 = '20002') a left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on tcbp.busi_prod_code = a.product_code_sys left join APP___PAS__DBUSER.t_contract_master tcm on tcbp.policy_id = tcm.policy_id where 1 = 1 and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01')) AND NOT EXISTS (SELECT 'X' FROM APP___PAS__DBUSER.T_LOCK_POLICY A LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID WHERE B.SUB_ID IN ('068', '067') AND B.LOCK_SERVICE_TYPE = 1 AND A.POLICY_CODE = TCBP.POLICY_CODE) AND TCBP.POLICY_CODE = ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:53:32 130|http-bio-9090-exec-5|DEBUG|==> Parameters: 990038596832(String)|BaseJdbcLogger.java|139|
2025-2-28 10:53:32 134|http-bio-9090-exec-5|DEBUG|<==      Total: 0|BaseJdbcLogger.java|139|
2025-2-28 10:53:32 293|http-bio-9090-exec-5|DEBUG|==>  Preparing: SELECT * FROM (SELECT SUM(A.BONUS_SA) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 AND A.POLICY_ID = ? AND A.ITEM_ID = ? AND A.POLICY_CODE = ? AND A.BUSI_ITEM_ID = ? AND A.BONUS_ALLOT in ('1','2','6') AND A.ALLOCATE_DUE_DATE <= ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 10:53:32 293|http-bio-9090-exec-5|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 10:53:32.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:53:32 293|http-bio-9090-exec-5|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:53:32 308|http-bio-9090-exec-5|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:53:32 308|http-bio-9090-exec-5|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 10:53:32 308|http-bio-9090-exec-5|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 10:53:32.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 10:53:32 308|http-bio-9090-exec-5|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 10:53:32 308|http-bio-9090-exec-5|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 10:53:32 308|http-bio-9090-exec-5|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 17:08:37 009|http-bio-9090-exec-2|DEBUG|==>  Preparing: SELECT * FROM (select tcbp.policy_id, tcbp.policy_code, tcbp.busi_item_id, tcbp.busi_prod_code, tcbp.busi_prd_id, tcbp.validate_date, tcm.validate_date as policy_effective_date, tcbp.maturity_date from (select product_code_sys from APP___PDS__DBUSER.t_business_product tbp where tbp.product_category1 = '20002') a left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on tcbp.busi_prod_code = a.product_code_sys left join APP___PAS__DBUSER.t_contract_master tcm on tcbp.policy_id = tcm.policy_id where 1 = 1 and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01')) AND NOT EXISTS (SELECT 'X' FROM APP___PAS__DBUSER.T_LOCK_POLICY A LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID WHERE B.SUB_ID IN ('068', '067') AND B.LOCK_SERVICE_TYPE = 1 AND A.POLICY_CODE = TCBP.POLICY_CODE) AND TCBP.POLICY_CODE = ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 17:08:37 009|http-bio-9090-exec-2|DEBUG|==> Parameters: 990038596832(String)|BaseJdbcLogger.java|139|
2025-2-28 17:08:37 024|http-bio-9090-exec-2|DEBUG|<==      Total: 0|BaseJdbcLogger.java|139|
2025-2-28 17:08:38 135|http-bio-9090-exec-2|DEBUG|==>  Preparing: SELECT * FROM (SELECT SUM(A.BONUS_SA) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 AND A.POLICY_ID = ? AND A.ITEM_ID = ? AND A.POLICY_CODE = ? AND A.BUSI_ITEM_ID = ? AND A.BONUS_ALLOT in ('1','2','6') AND A.ALLOCATE_DUE_DATE <= ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 17:08:38 150|http-bio-9090-exec-2|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 17:08:36.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 17:08:38 150|http-bio-9090-exec-2|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 17:08:38 150|http-bio-9090-exec-2|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 17:08:38 150|http-bio-9090-exec-2|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 17:08:38 165|http-bio-9090-exec-2|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 17:08:36.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 17:08:38 165|http-bio-9090-exec-2|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 17:08:38 165|http-bio-9090-exec-2|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 17:08:38 165|http-bio-9090-exec-2|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 17:08:40 929|http-bio-9090-exec-2|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 17:08:40.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 17:08:40 929|http-bio-9090-exec-2|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 17:08:40 929|http-bio-9090-exec-2|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 17:08:40 929|http-bio-9090-exec-2|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 17:08:40 929|http-bio-9090-exec-2|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 17:08:40.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 17:08:40 929|http-bio-9090-exec-2|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 17:08:40 929|http-bio-9090-exec-2|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 17:08:40 929|http-bio-9090-exec-2|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 18:11:48 028|http-bio-9090-exec-10|DEBUG|==>  Preparing: SELECT * FROM (select tcbp.policy_id, tcbp.policy_code, tcbp.busi_item_id, tcbp.busi_prod_code, tcbp.busi_prd_id, tcbp.validate_date, tcm.validate_date as policy_effective_date, tcbp.maturity_date from (select product_code_sys from APP___PDS__DBUSER.t_business_product tbp where tbp.product_category1 = '20002') a left join APP___PAS__DBUSER.t_contract_busi_prod tcbp on tcbp.busi_prod_code = a.product_code_sys left join APP___PAS__DBUSER.t_contract_master tcm on tcbp.policy_id = tcm.policy_id where 1 = 1 and (tcbp.liability_state = 1 or (tcbp.liability_state = 3 and tcbp.end_cause='01')) AND NOT EXISTS (SELECT 'X' FROM APP___PAS__DBUSER.T_LOCK_POLICY A LEFT JOIN APP___PAS__DBUSER.T_LOCK_SERVICE_DEF B ON A.LOCK_SERVICE_ID = B.LOCK_SERVICE_ID WHERE B.SUB_ID IN ('068', '067') AND B.LOCK_SERVICE_TYPE = 1 AND A.POLICY_CODE = TCBP.POLICY_CODE) AND TCBP.POLICY_CODE = ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 18:11:48 028|http-bio-9090-exec-10|DEBUG|==> Parameters: 990038596832(String)|BaseJdbcLogger.java|139|
2025-2-28 18:11:48 041|http-bio-9090-exec-10|DEBUG|<==      Total: 0|BaseJdbcLogger.java|139|
2025-2-28 18:11:48 299|http-bio-9090-exec-10|DEBUG|==>  Preparing: SELECT * FROM (SELECT SUM(A.BONUS_SA) as BONUS_SA FROM APP___PAS__DBUSER.T_BONUS_ALLOCATE A WHERE 1 = 1 AND A.POLICY_ID = ? AND A.ITEM_ID = ? AND A.POLICY_CODE = ? AND A.BUSI_ITEM_ID = ? AND A.BONUS_ALLOT in ('1','2','6') AND A.ALLOCATE_DUE_DATE <= ?) WHERE ROWNUM < 2000 |BaseJdbcLogger.java|139|
2025-2-28 18:11:48 299|http-bio-9090-exec-10|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006540(BigDecimal), 990038596832(String), 3600000006544(BigDecimal), 2025-02-28 18:11:48.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 18:11:48 311|http-bio-9090-exec-10|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 18:11:48 311|http-bio-9090-exec-10|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 18:11:48 311|http-bio-9090-exec-10|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
2025-2-28 18:11:48 311|http-bio-9090-exec-10|DEBUG|==> Parameters: 3600000020237(BigDecimal), 3600000006541(BigDecimal), 990038596832(String), 3600000006545(BigDecimal), 2025-02-28 18:11:48.0(Timestamp)|BaseJdbcLogger.java|139|
2025-2-28 18:11:48 311|http-bio-9090-exec-10|TRACE|<==    Columns: BONUS_SA|BaseJdbcLogger.java|145|
2025-2-28 18:11:48 311|http-bio-9090-exec-10|TRACE|<==        Row: null|BaseJdbcLogger.java|145|
2025-2-28 18:11:48 311|http-bio-9090-exec-10|DEBUG|<==      Total: 1|BaseJdbcLogger.java|139|
