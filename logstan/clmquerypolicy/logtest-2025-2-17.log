2025-2-17 15:03:48 634|http-bio-9090-exec-11|INFO|理赔抄单共计耗时：fundAmount41|QueryPolicyServiceImpl.java|609|
2025-2-17 15:03:51 956|http-bio-9090-exec-11|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-02-16 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2025-02-16 16:00:00.0 UTC</submissionDate>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2025-02-16 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000423883</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000240496</customerId>
      <insuredId>3600000317307</insuredId>
      <policyCode>990045068519</policyCode>
      <designation>00</designation>
      <listId>3600000455762</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵一娟</customerName>
      <customerBirthday>2004-05-05 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130602200405065437</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000317307</insuredId>
      <orderId>1</orderId>
      <policyCode>990045068519</policyCode>
      <listId>3600000452416</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <countWay>1</countWay>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000421154</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3024-02-17 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>11355</renewalDiscntedPremAf>
      <totalPremAf>11355</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>11355</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>11355</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2026-02-17 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000421154</itemId>
      <organCode>********</organCode>
      <policyCode>990045068519</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000437492</listId>
      <payDueDate>2026-02-17 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>11355</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000423879</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000240493</customerId>
      <jobCode>********</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503522</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2025-02-17 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503531</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000423883</addressId>
      <standLife>1</standLife>
      <customerId>3600000240496</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>01</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <insuredAge>20</insuredAge>
      <listId>3600000317307</listId>
      <policyId>*************</policyId>
      <sociSecu>1</sociSecu>
      <customerName>赵一娟</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-2-17 15:03:51 963|http-bio-9090-exec-11|INFO|理赔抄单共计耗时：6210|QueryPolicyUCCImpl.java|194|
2025-2-17 15:03:52 404|http-bio-9090-exec-11|INFO|理赔抄单共计耗时：fundAmount15|QueryPolicyServiceImpl.java|609|
2025-2-17 15:03:52 423|http-bio-9090-exec-11|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-02-16 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2025-02-16 16:00:00.0 UTC</submissionDate>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2025-02-16 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000423883</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000240496</customerId>
      <insuredId>3600000317307</insuredId>
      <policyCode>990045068519</policyCode>
      <designation>00</designation>
      <listId>3600000455762</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵一娟</customerName>
      <customerBirthday>2004-05-05 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130602200405065437</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000317307</insuredId>
      <orderId>1</orderId>
      <policyCode>990045068519</policyCode>
      <listId>3600000452416</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <countWay>1</countWay>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000421154</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3024-02-17 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>11355</renewalDiscntedPremAf>
      <totalPremAf>11355</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>11355</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>11355</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2026-02-17 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000421154</itemId>
      <organCode>********</organCode>
      <policyCode>990045068519</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000437492</listId>
      <payDueDate>2026-02-17 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>11355</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000423879</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000240493</customerId>
      <jobCode>********</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503522</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2025-02-17 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503531</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000423883</addressId>
      <standLife>1</standLife>
      <customerId>3600000240496</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>01</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <insuredAge>20</insuredAge>
      <listId>3600000317307</listId>
      <policyId>*************</policyId>
      <sociSecu>1</sociSecu>
      <customerName>赵一娟</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-2-17 15:03:52 423|http-bio-9090-exec-11|INFO|理赔抄单共计耗时：260|QueryPolicyUCCImpl.java|194|
2025-2-17 15:03:52 649|http-bio-9090-exec-11|INFO|理赔抄单共计耗时：fundAmount21|QueryPolicyServiceImpl.java|609|
2025-2-17 15:03:52 689|http-bio-9090-exec-11|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-02-16 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2025-02-16 16:00:00.0 UTC</submissionDate>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2025-02-16 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000423883</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000240496</customerId>
      <insuredId>3600000317307</insuredId>
      <policyCode>990045068519</policyCode>
      <designation>00</designation>
      <listId>3600000455762</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵一娟</customerName>
      <customerBirthday>2004-05-05 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130602200405065437</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000317307</insuredId>
      <orderId>1</orderId>
      <policyCode>990045068519</policyCode>
      <listId>3600000452416</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <countWay>1</countWay>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000421154</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3024-02-17 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>11355</renewalDiscntedPremAf>
      <totalPremAf>11355</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>11355</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>11355</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2026-02-17 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000421154</itemId>
      <organCode>********</organCode>
      <policyCode>990045068519</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000437492</listId>
      <payDueDate>2026-02-17 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>11355</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000423879</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000240493</customerId>
      <jobCode>********</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503522</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2025-02-17 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503531</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000423883</addressId>
      <standLife>1</standLife>
      <customerId>3600000240496</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>01</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <insuredAge>20</insuredAge>
      <listId>3600000317307</listId>
      <policyId>*************</policyId>
      <sociSecu>1</sociSecu>
      <customerName>赵一娟</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-2-17 15:03:52 689|http-bio-9090-exec-11|INFO|理赔抄单共计耗时：173|QueryPolicyUCCImpl.java|194|
2025-2-17 15:03:52 935|http-bio-9090-exec-11|INFO|理赔抄单共计耗时：fundAmount7|QueryPolicyServiceImpl.java|609|
2025-2-17 15:03:52 973|http-bio-9090-exec-11|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-02-16 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2025-02-16 16:00:00.0 UTC</submissionDate>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2025-02-16 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000423883</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000240496</customerId>
      <insuredId>3600000317307</insuredId>
      <policyCode>990045068519</policyCode>
      <designation>00</designation>
      <listId>3600000455762</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵一娟</customerName>
      <customerBirthday>2004-05-05 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130602200405065437</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000317307</insuredId>
      <orderId>1</orderId>
      <policyCode>990045068519</policyCode>
      <listId>3600000452416</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <countWay>1</countWay>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000421154</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3024-02-17 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>11355</renewalDiscntedPremAf>
      <totalPremAf>11355</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>11355</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>11355</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2026-02-17 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000421154</itemId>
      <organCode>********</organCode>
      <policyCode>990045068519</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000437492</listId>
      <payDueDate>2026-02-17 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>11355</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000423879</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000240493</customerId>
      <jobCode>********</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503522</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2025-02-17 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503531</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000423883</addressId>
      <standLife>1</standLife>
      <customerId>3600000240496</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>01</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <insuredAge>20</insuredAge>
      <listId>3600000317307</listId>
      <policyId>*************</policyId>
      <sociSecu>1</sociSecu>
      <customerName>赵一娟</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-2-17 15:03:52 973|http-bio-9090-exec-11|INFO|理赔抄单共计耗时：167|QueryPolicyUCCImpl.java|194|
2025-2-17 15:07:18 516|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：fundAmount11|QueryPolicyServiceImpl.java|609|
2025-2-17 15:07:18 530|http-bio-9090-exec-8|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-02-16 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2025-02-16 16:00:00.0 UTC</submissionDate>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2025-02-16 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000423883</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000240496</customerId>
      <insuredId>3600000317307</insuredId>
      <policyCode>990045068519</policyCode>
      <designation>00</designation>
      <listId>3600000455762</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵一娟</customerName>
      <customerBirthday>2004-05-05 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130602200405065437</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000317307</insuredId>
      <orderId>1</orderId>
      <policyCode>990045068519</policyCode>
      <listId>3600000452416</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <countWay>1</countWay>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000421154</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3024-02-17 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>11355</renewalDiscntedPremAf>
      <totalPremAf>11355</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>11355</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>11355</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2026-02-17 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000421154</itemId>
      <organCode>********</organCode>
      <policyCode>990045068519</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000437492</listId>
      <payDueDate>2026-02-17 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>11355</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000423879</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000240493</customerId>
      <jobCode>********</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503522</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2025-02-17 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503531</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000423883</addressId>
      <standLife>1</standLife>
      <customerId>3600000240496</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>01</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <insuredAge>20</insuredAge>
      <listId>3600000317307</listId>
      <policyId>*************</policyId>
      <sociSecu>1</sociSecu>
      <customerName>赵一娟</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-2-17 15:07:18 530|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：147|QueryPolicyUCCImpl.java|194|
2025-2-17 15:07:18 598|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：fundAmount0|QueryPolicyServiceImpl.java|609|
2025-2-17 15:07:18 603|http-bio-9090-exec-8|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-02-16 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2025-02-16 16:00:00.0 UTC</submissionDate>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2025-02-16 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000423883</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000240496</customerId>
      <insuredId>3600000317307</insuredId>
      <policyCode>990045068519</policyCode>
      <designation>00</designation>
      <listId>3600000455762</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵一娟</customerName>
      <customerBirthday>2004-05-05 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130602200405065437</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000317307</insuredId>
      <orderId>1</orderId>
      <policyCode>990045068519</policyCode>
      <listId>3600000452416</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <countWay>1</countWay>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000421154</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3024-02-17 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>11355</renewalDiscntedPremAf>
      <totalPremAf>11355</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>11355</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>11355</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2026-02-17 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000421154</itemId>
      <organCode>********</organCode>
      <policyCode>990045068519</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000437492</listId>
      <payDueDate>2026-02-17 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>11355</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000423879</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000240493</customerId>
      <jobCode>********</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503522</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2025-02-17 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503531</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000423883</addressId>
      <standLife>1</standLife>
      <customerId>3600000240496</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>01</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <insuredAge>20</insuredAge>
      <listId>3600000317307</listId>
      <policyId>*************</policyId>
      <sociSecu>1</sociSecu>
      <customerName>赵一娟</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-2-17 15:07:18 603|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：27|QueryPolicyUCCImpl.java|194|
2025-2-17 15:07:18 649|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：fundAmount6|QueryPolicyServiceImpl.java|609|
2025-2-17 15:07:18 649|http-bio-9090-exec-8|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-02-16 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2025-02-16 16:00:00.0 UTC</submissionDate>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2025-02-16 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000423883</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000240496</customerId>
      <insuredId>3600000317307</insuredId>
      <policyCode>990045068519</policyCode>
      <designation>00</designation>
      <listId>3600000455762</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵一娟</customerName>
      <customerBirthday>2004-05-05 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130602200405065437</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000317307</insuredId>
      <orderId>1</orderId>
      <policyCode>990045068519</policyCode>
      <listId>3600000452416</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <countWay>1</countWay>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000421154</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3024-02-17 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>11355</renewalDiscntedPremAf>
      <totalPremAf>11355</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>11355</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>11355</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2026-02-17 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000421154</itemId>
      <organCode>********</organCode>
      <policyCode>990045068519</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000437492</listId>
      <payDueDate>2026-02-17 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>11355</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000423879</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000240493</customerId>
      <jobCode>********</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503522</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2025-02-17 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503531</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000423883</addressId>
      <standLife>1</standLife>
      <customerId>3600000240496</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>01</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <insuredAge>20</insuredAge>
      <listId>3600000317307</listId>
      <policyId>*************</policyId>
      <sociSecu>1</sociSecu>
      <customerName>赵一娟</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-2-17 15:07:18 649|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：26|QueryPolicyUCCImpl.java|194|
2025-2-17 15:07:18 734|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：fundAmount11|QueryPolicyServiceImpl.java|609|
2025-2-17 15:07:18 738|http-bio-9090-exec-8|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-02-16 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2025-02-16 16:00:00.0 UTC</submissionDate>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2025-02-16 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2025-02-16 16:00:00.0 UTC</issueDate>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2025-02-17 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000423883</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000240496</customerId>
      <insuredId>3600000317307</insuredId>
      <policyCode>990045068519</policyCode>
      <designation>00</designation>
      <listId>3600000455762</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵一娟</customerName>
      <customerBirthday>2004-05-05 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130602200405065437</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000317307</insuredId>
      <orderId>1</orderId>
      <policyCode>990045068519</policyCode>
      <listId>3600000452416</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2030-02-17 16:00:00.0 UTC</paidupDate>
      <expiryDate>3024-02-17 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990045068519</policyCode>
      <countWay>1</countWay>
      <validateDate>2025-02-17 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2025-02-16 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000421154</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3024-02-17 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>11355</renewalDiscntedPremAf>
      <totalPremAf>11355</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>11355</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>11355</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2026-02-17 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000421154</itemId>
      <organCode>********</organCode>
      <policyCode>990045068519</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000437492</listId>
      <payDueDate>2026-02-17 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>11355</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000423879</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000240493</customerId>
      <jobCode>********</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503522</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2025-02-17 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <listId>3600000503531</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000423883</addressId>
      <standLife>1</standLife>
      <customerId>3600000240496</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>01</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990045068519</policyCode>
      <insuredAge>20</insuredAge>
      <listId>3600000317307</listId>
      <policyId>*************</policyId>
      <sociSecu>1</sociSecu>
      <customerName>赵一娟</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-2-17 15:07:18 738|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：45|QueryPolicyUCCImpl.java|194|
