2024-12-3 11:20:13 483|http-bio-9090-exec-4|INFO|理赔抄单共计耗时：fundAmount30|QueryPolicyServiceImpl.java|609|
2024-12-3 11:20:13 498|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：fundAmount45|QueryPolicyServiceImpl.java|609|
2024-12-3 11:20:13 513|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：fundAmount15|QueryPolicyServiceImpl.java|609|
2024-12-3 11:20:13 513|http-bio-9090-exec-4|INFO|理赔抄单共计耗时：fundAmount30|QueryPolicyServiceImpl.java|609|
2024-12-3 11:20:14 180|http-bio-9090-exec-4|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>1826872</policyId>
      <derivation>1</derivation>
      <inputDate>2023-07-30 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-07-31 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-07-31 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-07-30 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-07-30 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-07-30 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
      <waiver>0</waiver>
      <issueDate>2023-07-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2028-07-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-07-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181406</busiPrdId>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>00946000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>1</renew>
      <maturityDate>2024-07-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>759969</masterBusiItemId>
      <issueDate>2023-07-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2023-07-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2024-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2028-07-31 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2023-07-31 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2023-07-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>11</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1297967</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>480637</customerId>
      <insuredId>465217</insuredId>
      <policyCode>990038290596</policyCode>
      <designation>00</designation>
      <listId>586056</listId>
      <legalBene>0</legalBene>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
      <shareRate>1</shareRate>
      <customerName>张一丽</customerName>
      <customerBirthday>1983-01-23 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>230901198301241137</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1297967</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00946000</productCode>
      <customerId>480637</customerId>
      <insuredId>465217</insuredId>
      <policyCode>990038290596</policyCode>
      <designation>00</designation>
      <listId>586057</listId>
      <legalBene>0</legalBene>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
      <shareRate>1</shareRate>
      <customerName>张一丽</customerName>
      <customerBirthday>1983-01-23 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>230901198301241137</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>465217</insuredId>
      <orderId>1</orderId>
      <policyCode>990038290596</policyCode>
      <listId>588641</listId>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00946000</productCode>
      <insuredId>465217</insuredId>
      <orderId>1</orderId>
      <policyCode>990038290596</policyCode>
      <listId>588642</listId>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1826872</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2023-07-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>2024-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <productId>180920</productId>
      <productCode>946000</productCode>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>633786</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2024-07-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>759970</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>1440</renewalDiscntedPremAf>
      <totalPremAf>1440</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>1440</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>1440</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1826872</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2028-07-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>633785</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-07-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>759969</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>14880</renewalDiscntedPremAf>
      <totalPremAf>14880</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>14880</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>14880</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-07-31 16:00:00.0 UTC</extractionDueDate>
      <itemId>633785</itemId>
      <organCode>********</organCode>
      <policyCode>990038290596</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>699433</listId>
      <payDueDate>2024-07-31 16:00:00.0 UTC</payDueDate>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
      <nextPrem>14880</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>633786</itemId>
      <organCode>********</organCode>
      <policyCode>990038290596</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>699434</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
      <nextPrem>1440</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>1297966</addressId>
      <customerHeight>170</customerHeight>
      <customerId>480637</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990038290596</policyCode>
      <listId>2222745</listId>
      <policyId>1826872</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-07-31 16:00:00.0 UTC</agentStartDate>
      <agentMobile>18649346585</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990038290596</policyCode>
      <listId>1978244</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>1826872</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>1297967</addressId>
      <standLife>1</standLife>
      <customerId>480637</customerId>
      <customerHeight>170</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990038290596</policyCode>
      <insuredAge>40</insuredAge>
      <listId>465217</listId>
      <policyId>1826872</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>张一丽</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2024-12-3 11:20:14 180|http-bio-9090-exec-2|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>1826872</policyId>
      <derivation>1</derivation>
      <inputDate>2023-07-30 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-07-31 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-07-31 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-07-30 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-07-30 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-07-30 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
      <waiver>0</waiver>
      <issueDate>2023-07-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2028-07-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-07-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181406</busiPrdId>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>00946000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>1</renew>
      <maturityDate>2024-07-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>759969</masterBusiItemId>
      <issueDate>2023-07-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2023-07-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2024-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2028-07-31 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2023-07-31 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2023-07-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>11</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1297967</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>480637</customerId>
      <insuredId>465217</insuredId>
      <policyCode>990038290596</policyCode>
      <designation>00</designation>
      <listId>586056</listId>
      <legalBene>0</legalBene>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
      <shareRate>1</shareRate>
      <customerName>张一丽</customerName>
      <customerBirthday>1983-01-23 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>230901198301241137</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1297967</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00946000</productCode>
      <customerId>480637</customerId>
      <insuredId>465217</insuredId>
      <policyCode>990038290596</policyCode>
      <designation>00</designation>
      <listId>586057</listId>
      <legalBene>0</legalBene>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
      <shareRate>1</shareRate>
      <customerName>张一丽</customerName>
      <customerBirthday>1983-01-23 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>230901198301241137</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>465217</insuredId>
      <orderId>1</orderId>
      <policyCode>990038290596</policyCode>
      <listId>588641</listId>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00946000</productCode>
      <insuredId>465217</insuredId>
      <orderId>1</orderId>
      <policyCode>990038290596</policyCode>
      <listId>588642</listId>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1826872</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2023-07-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>2024-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <productId>180920</productId>
      <productCode>946000</productCode>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>633786</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2024-07-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>759970</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>1440</renewalDiscntedPremAf>
      <totalPremAf>1440</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>1440</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>1440</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1826872</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2028-07-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>633785</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-07-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>759969</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>14880</renewalDiscntedPremAf>
      <totalPremAf>14880</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>14880</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>14880</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-07-31 16:00:00.0 UTC</extractionDueDate>
      <itemId>633785</itemId>
      <organCode>********</organCode>
      <policyCode>990038290596</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>699433</listId>
      <payDueDate>2024-07-31 16:00:00.0 UTC</payDueDate>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
      <nextPrem>14880</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>633786</itemId>
      <organCode>********</organCode>
      <policyCode>990038290596</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>699434</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
      <nextPrem>1440</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>1297966</addressId>
      <customerHeight>170</customerHeight>
      <customerId>480637</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990038290596</policyCode>
      <listId>2222745</listId>
      <policyId>1826872</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-07-31 16:00:00.0 UTC</agentStartDate>
      <agentMobile>18649346585</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990038290596</policyCode>
      <listId>1978244</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>1826872</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>1297967</addressId>
      <standLife>1</standLife>
      <customerId>480637</customerId>
      <customerHeight>170</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990038290596</policyCode>
      <insuredAge>40</insuredAge>
      <listId>465217</listId>
      <policyId>1826872</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>张一丽</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2024-12-3 11:20:14 180|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：2934|QueryPolicyUCCImpl.java|194|
2024-12-3 11:20:14 180|http-bio-9090-exec-4|INFO|理赔抄单共计耗时：1884|QueryPolicyUCCImpl.java|194|
2024-12-3 11:20:14 616|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：fundAmount0|QueryPolicyServiceImpl.java|609|
2024-12-3 11:20:14 632|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：fundAmount16|QueryPolicyServiceImpl.java|609|
2024-12-3 11:20:14 692|http-bio-9090-exec-2|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>1826872</policyId>
      <derivation>1</derivation>
      <inputDate>2023-07-30 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-07-31 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-07-31 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-07-30 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-07-30 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-07-30 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
      <waiver>0</waiver>
      <issueDate>2023-07-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2028-07-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-07-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181406</busiPrdId>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>00946000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>1</renew>
      <maturityDate>2024-07-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>759969</masterBusiItemId>
      <issueDate>2023-07-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2023-07-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2024-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2028-07-31 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2023-07-31 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2023-07-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>11</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1297967</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>480637</customerId>
      <insuredId>465217</insuredId>
      <policyCode>990038290596</policyCode>
      <designation>00</designation>
      <listId>586056</listId>
      <legalBene>0</legalBene>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
      <shareRate>1</shareRate>
      <customerName>张一丽</customerName>
      <customerBirthday>1983-01-23 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>230901198301241137</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1297967</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00946000</productCode>
      <customerId>480637</customerId>
      <insuredId>465217</insuredId>
      <policyCode>990038290596</policyCode>
      <designation>00</designation>
      <listId>586057</listId>
      <legalBene>0</legalBene>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
      <shareRate>1</shareRate>
      <customerName>张一丽</customerName>
      <customerBirthday>1983-01-23 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>230901198301241137</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>465217</insuredId>
      <orderId>1</orderId>
      <policyCode>990038290596</policyCode>
      <listId>588641</listId>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00946000</productCode>
      <insuredId>465217</insuredId>
      <orderId>1</orderId>
      <policyCode>990038290596</policyCode>
      <listId>588642</listId>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1826872</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2023-07-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>2024-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <productId>180920</productId>
      <productCode>946000</productCode>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>633786</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2024-07-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>759970</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>1440</renewalDiscntedPremAf>
      <totalPremAf>1440</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>1440</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>1440</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1826872</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2028-07-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-07-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038290596</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-07-31 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-07-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>633785</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-07-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>759969</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>14880</renewalDiscntedPremAf>
      <totalPremAf>14880</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>14880</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>14880</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-07-31 16:00:00.0 UTC</extractionDueDate>
      <itemId>633785</itemId>
      <organCode>********</organCode>
      <policyCode>990038290596</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>699433</listId>
      <payDueDate>2024-07-31 16:00:00.0 UTC</payDueDate>
      <busiItemId>759969</busiItemId>
      <policyId>1826872</policyId>
      <nextPrem>14880</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>633786</itemId>
      <organCode>********</organCode>
      <policyCode>990038290596</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>699434</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>759970</busiItemId>
      <policyId>1826872</policyId>
      <nextPrem>1440</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>1297966</addressId>
      <customerHeight>170</customerHeight>
      <customerId>480637</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990038290596</policyCode>
      <listId>2222745</listId>
      <policyId>1826872</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-07-31 16:00:00.0 UTC</agentStartDate>
      <agentMobile>18649346585</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990038290596</policyCode>
      <listId>1978244</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>1826872</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>1297967</addressId>
      <standLife>1</standLife>
      <customerId>480637</customerId>
      <customerHeight>170</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990038290596</policyCode>
      <insuredAge>40</insuredAge>
      <listId>465217</listId>
      <policyId>1826872</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>张一丽</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2024-12-3 11:20:14 692|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：197|QueryPolicyUCCImpl.java|194|
2024-12-3 16:14:53 742|pool-29-thread-2|INFO|理赔抄单共计耗时：fundAmount170|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:53 752|pool-29-thread-2|INFO|理赔抄单共计耗时：fundAmount10|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:53 757|pool-29-thread-2|INFO|理赔抄单共计耗时：fundAmount5|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:53 791|pool-29-thread-2|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2023-12-30 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-12-31 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-12-31 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-12-30 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-12-31 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-12-30 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-12-30 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-12-30 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>2024-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2023-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2023-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2024-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <validateDate>2023-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-12-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>437</busiPrdId>
      <applyDate>2023-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>00507000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340187</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>*************</masterBusiItemId>
      <issueDate>2023-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2028-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <validateDate>2023-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2029-06-25 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-06-25 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2023-12-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>11</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>469</busiPrdId>
      <applyDate>2024-06-24 16:00:00.0 UTC</applyDate>
      <busiProdCode>00726000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>2025-06-25 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340188</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>*************</masterBusiItemId>
      <issueDate>2024-06-24 16:00:00.0 UTC</issueDate>
      <paidupDate>2024-06-25 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-06-25 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <validateDate>2024-06-25 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2029-06-25 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-06-25 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2024-06-25 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>12</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000243121</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245129</insuredId>
      <policyCode>990042118512</policyCode>
      <designation>00</designation>
      <listId>3600000361994</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000243121</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00507000</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245129</insuredId>
      <policyCode>990042118512</policyCode>
      <designation>00</designation>
      <listId>3600000361995</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000340187</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000243121</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00726000</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245129</insuredId>
      <policyCode>990042118512</policyCode>
      <designation>00</designation>
      <listId>3600000361996</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000340188</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000245129</insuredId>
      <orderId>1</orderId>
      <policyCode>990042118512</policyCode>
      <listId>3600000364090</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00507000</productCode>
      <insuredId>3600000245129</insuredId>
      <orderId>1</orderId>
      <policyCode>990042118512</policyCode>
      <listId>3600000364091</listId>
      <busiItemId>3600000340187</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00726000</productCode>
      <insuredId>3600000245129</insuredId>
      <orderId>1</orderId>
      <policyCode>990042118512</policyCode>
      <listId>3600000364092</listId>
      <busiItemId>3600000340188</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2023-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>2024-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-12-31 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000335240</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2024-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>17970</renewalDiscntedPremAf>
      <totalPremAf>17970</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>17970</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>17970</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2028-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-12-31 16:00:00.0 UTC</validateDate>
      <productId>244</productId>
      <productCode>507000</productCode>
      <applyDate>2023-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000335241</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340187</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>500</renewalDiscntedPremAf>
      <totalPremAf>500</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>500</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>500</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2024-06-25 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-06-25 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-06-25 16:00:00.0 UTC</validateDate>
      <productId>278</productId>
      <productCode>726000</productCode>
      <applyDate>2024-06-24 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000335242</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-06-25 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340188</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>2055</renewalDiscntedPremAf>
      <totalPremAf>2055</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>2055</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>2055</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-12-31 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335240</itemId>
      <organCode>********</organCode>
      <policyCode>990042118512</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345121</listId>
      <payDueDate>2024-12-31 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>17970</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335241</itemId>
      <organCode>********</organCode>
      <policyCode>990042118512</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345122</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000340187</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>500</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335242</itemId>
      <organCode>********</organCode>
      <policyCode>990042118512</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345123</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000340188</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>2055</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000243119</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000122005</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990042118512</policyCode>
      <listId>3600000377747</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2024-06-25 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990042118512</policyCode>
      <listId>3600000378182</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000243121</addressId>
      <standLife>1</standLife>
      <customerId>3600000122005</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990042118512</policyCode>
      <insuredAge>34</insuredAge>
      <listId>3600000245129</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>赵海柱</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2024-12-3 16:14:53 791|pool-29-thread-2|INFO|理赔抄单共计耗时：2190|QueryPolicyUCCImpl.java|194|
2024-12-3 16:14:53 937|pool-29-thread-1|INFO|理赔抄单共计耗时：fundAmount12|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:53 954|pool-29-thread-1|INFO|理赔抄单共计耗时：fundAmount15|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:53 962|pool-29-thread-1|INFO|理赔抄单共计耗时：fundAmount5|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:53 968|pool-29-thread-1|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>66068182901802</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>3600000431535</policyId>
      <derivation>1</derivation>
      <inputDate>2024-06-24 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3023-01-01 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2024-01-01 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-12-31 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-12-31 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-12-31 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340122</busiItemId>
      <policyId>3600000431535</policyId>
      <waiver>0</waiver>
      <issueDate>2023-12-31 16:00:00.0 UTC</issueDate>
      <paidupDate>2029-01-01 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3023-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2024-01-01 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>437</busiPrdId>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <busiProdCode>00507000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <renew>1</renew>
      <maturityDate>2025-01-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340123</busiItemId>
      <policyId>3600000431535</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>3600000340122</masterBusiItemId>
      <issueDate>2023-12-31 16:00:00.0 UTC</issueDate>
      <paidupDate>2024-01-01 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2029-01-01 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-01-01 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2024-01-01 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>469</busiPrdId>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <busiProdCode>00726000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <renew>0</renew>
      <maturityDate>2025-01-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340124</busiItemId>
      <policyId>3600000431535</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>3600000340122</masterBusiItemId>
      <issueDate>2023-12-31 16:00:00.0 UTC</issueDate>
      <paidupDate>2024-01-01 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2029-01-01 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-01-01 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2024-01-01 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000242911</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245083</insuredId>
      <policyCode>990042114518</policyCode>
      <designation>00</designation>
      <listId>3600000361938</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000340122</busiItemId>
      <policyId>3600000431535</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000242911</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00507000</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245083</insuredId>
      <policyCode>990042114518</policyCode>
      <designation>00</designation>
      <listId>3600000361939</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000340123</busiItemId>
      <policyId>3600000431535</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000242911</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00726000</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245083</insuredId>
      <policyCode>990042114518</policyCode>
      <designation>00</designation>
      <listId>3600000361940</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000340124</busiItemId>
      <policyId>3600000431535</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000245083</insuredId>
      <orderId>1</orderId>
      <policyCode>990042114518</policyCode>
      <listId>3600000364034</listId>
      <busiItemId>3600000340122</busiItemId>
      <policyId>3600000431535</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00507000</productCode>
      <insuredId>3600000245083</insuredId>
      <orderId>1</orderId>
      <policyCode>990042114518</policyCode>
      <listId>3600000364035</listId>
      <busiItemId>3600000340123</busiItemId>
      <policyId>3600000431535</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00726000</productCode>
      <insuredId>3600000245083</insuredId>
      <orderId>1</orderId>
      <policyCode>990042114518</policyCode>
      <listId>3600000364036</listId>
      <busiItemId>3600000340124</busiItemId>
      <policyId>3600000431535</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000431535</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2029-01-01 16:00:00.0 UTC</paidupDate>
      <expiryDate>3023-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000335176</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3023-01-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340122</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>17340</renewalDiscntedPremAf>
      <totalPremAf>17340</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>17340</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>17340</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000431535</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2024-01-01 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <productId>244</productId>
      <productCode>507000</productCode>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000335177</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-01-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340123</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>500</renewalDiscntedPremAf>
      <totalPremAf>500</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>500</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>500</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000431535</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2024-01-01 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <productId>278</productId>
      <productCode>726000</productCode>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000335178</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-01-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340124</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>2055</renewalDiscntedPremAf>
      <totalPremAf>2055</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>2055</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>2055</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2025-01-01 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335176</itemId>
      <organCode>********</organCode>
      <policyCode>990042114518</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345061</listId>
      <payDueDate>2025-01-01 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000340122</busiItemId>
      <policyId>3600000431535</policyId>
      <nextPrem>17340</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335177</itemId>
      <organCode>********</organCode>
      <policyCode>990042114518</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345062</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000340123</busiItemId>
      <policyId>3600000431535</policyId>
      <nextPrem>500</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335178</itemId>
      <organCode>********</organCode>
      <policyCode>990042114518</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345063</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000340124</busiItemId>
      <policyId>3600000431535</policyId>
      <nextPrem>2055</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000242910</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000122005</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>66068182901802</applyCode>
      <policyCode>990042114518</policyCode>
      <listId>3600000377701</listId>
      <policyId>3600000431535</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2024-01-01 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>66068182901802</applyCode>
      <policyCode>990042114518</policyCode>
      <listId>3600000378136</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>3600000431535</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000242911</addressId>
      <standLife>1</standLife>
      <customerId>3600000122005</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>66068182901802</applyCode>
      <policyCode>990042114518</policyCode>
      <insuredAge>33</insuredAge>
      <listId>3600000245083</listId>
      <policyId>3600000431535</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2024-12-3 16:14:53 969|pool-29-thread-1|INFO|理赔抄单共计耗时：2368|QueryPolicyUCCImpl.java|194|
2024-12-3 16:14:54 300|pool-30-thread-2|INFO|理赔抄单共计耗时：fundAmount12|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:54 309|pool-30-thread-2|INFO|理赔抄单共计耗时：fundAmount9|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:54 323|pool-30-thread-2|INFO|理赔抄单共计耗时：fundAmount14|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:54 330|pool-30-thread-2|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2023-12-30 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-12-31 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-12-31 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-12-30 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-12-31 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-12-30 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-12-30 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-12-30 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>2024-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2023-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2023-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2024-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <validateDate>2023-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-12-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>437</busiPrdId>
      <applyDate>2023-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>00507000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340187</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>*************</masterBusiItemId>
      <issueDate>2023-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2028-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <validateDate>2023-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2029-06-25 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-06-25 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2023-12-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>11</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>469</busiPrdId>
      <applyDate>2024-06-24 16:00:00.0 UTC</applyDate>
      <busiProdCode>00726000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>2025-06-25 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340188</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>*************</masterBusiItemId>
      <issueDate>2024-06-24 16:00:00.0 UTC</issueDate>
      <paidupDate>2024-06-25 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-06-25 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <validateDate>2024-06-25 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2029-06-25 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-06-25 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2024-06-25 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>12</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000243121</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245129</insuredId>
      <policyCode>990042118512</policyCode>
      <designation>00</designation>
      <listId>3600000361994</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000243121</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00507000</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245129</insuredId>
      <policyCode>990042118512</policyCode>
      <designation>00</designation>
      <listId>3600000361995</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000340187</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000243121</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00726000</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245129</insuredId>
      <policyCode>990042118512</policyCode>
      <designation>00</designation>
      <listId>3600000361996</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000340188</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000245129</insuredId>
      <orderId>1</orderId>
      <policyCode>990042118512</policyCode>
      <listId>3600000364090</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00507000</productCode>
      <insuredId>3600000245129</insuredId>
      <orderId>1</orderId>
      <policyCode>990042118512</policyCode>
      <listId>3600000364091</listId>
      <busiItemId>3600000340187</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00726000</productCode>
      <insuredId>3600000245129</insuredId>
      <orderId>1</orderId>
      <policyCode>990042118512</policyCode>
      <listId>3600000364092</listId>
      <busiItemId>3600000340188</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2023-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>2024-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-12-31 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000335240</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2024-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>17970</renewalDiscntedPremAf>
      <totalPremAf>17970</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>17970</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>17970</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2028-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-12-31 16:00:00.0 UTC</validateDate>
      <productId>244</productId>
      <productCode>507000</productCode>
      <applyDate>2023-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000335241</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340187</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>500</renewalDiscntedPremAf>
      <totalPremAf>500</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>500</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>500</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2024-06-25 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-06-25 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042118512</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-06-25 16:00:00.0 UTC</validateDate>
      <productId>278</productId>
      <productCode>726000</productCode>
      <applyDate>2024-06-24 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000335242</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-06-25 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340188</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>2055</renewalDiscntedPremAf>
      <totalPremAf>2055</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>2055</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>2055</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-12-31 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335240</itemId>
      <organCode>********</organCode>
      <policyCode>990042118512</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345121</listId>
      <payDueDate>2024-12-31 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>17970</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335241</itemId>
      <organCode>********</organCode>
      <policyCode>990042118512</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345122</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000340187</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>500</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335242</itemId>
      <organCode>********</organCode>
      <policyCode>990042118512</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345123</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000340188</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>2055</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000243119</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000122005</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990042118512</policyCode>
      <listId>3600000377747</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2024-06-25 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990042118512</policyCode>
      <listId>3600000378182</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000243121</addressId>
      <standLife>1</standLife>
      <customerId>3600000122005</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990042118512</policyCode>
      <insuredAge>34</insuredAge>
      <listId>3600000245129</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>赵海柱</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2024-12-3 16:14:54 330|pool-30-thread-2|INFO|理赔抄单共计耗时：119|QueryPolicyUCCImpl.java|194|
2024-12-3 16:14:56 120|pool-30-thread-1|INFO|理赔抄单共计耗时：fundAmount8|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:56 128|pool-30-thread-1|INFO|理赔抄单共计耗时：fundAmount6|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:56 134|pool-30-thread-1|INFO|理赔抄单共计耗时：fundAmount4|QueryPolicyServiceImpl.java|609|
2024-12-3 16:14:56 197|pool-30-thread-1|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>66068182901802</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>3600000431535</policyId>
      <derivation>1</derivation>
      <inputDate>2024-06-24 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3023-01-01 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2024-01-01 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-12-31 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-12-31 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-12-31 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340122</busiItemId>
      <policyId>3600000431535</policyId>
      <waiver>0</waiver>
      <issueDate>2023-12-31 16:00:00.0 UTC</issueDate>
      <paidupDate>2029-01-01 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3023-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2024-01-01 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>437</busiPrdId>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <busiProdCode>00507000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <renew>1</renew>
      <maturityDate>2025-01-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340123</busiItemId>
      <policyId>3600000431535</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>3600000340122</masterBusiItemId>
      <issueDate>2023-12-31 16:00:00.0 UTC</issueDate>
      <paidupDate>2024-01-01 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2029-01-01 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-01-01 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2024-01-01 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>469</busiPrdId>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <busiProdCode>00726000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <renew>0</renew>
      <maturityDate>2025-01-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340124</busiItemId>
      <policyId>3600000431535</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>3600000340122</masterBusiItemId>
      <issueDate>2023-12-31 16:00:00.0 UTC</issueDate>
      <paidupDate>2024-01-01 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2029-01-01 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-01-01 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2024-01-01 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000242911</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245083</insuredId>
      <policyCode>990042114518</policyCode>
      <designation>00</designation>
      <listId>3600000361938</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000340122</busiItemId>
      <policyId>3600000431535</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000242911</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00507000</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245083</insuredId>
      <policyCode>990042114518</policyCode>
      <designation>00</designation>
      <listId>3600000361939</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000340123</busiItemId>
      <policyId>3600000431535</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000242911</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00726000</productCode>
      <customerId>3600000122005</customerId>
      <insuredId>3600000245083</insuredId>
      <policyCode>990042114518</policyCode>
      <designation>00</designation>
      <listId>3600000361940</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000340124</busiItemId>
      <policyId>3600000431535</policyId>
      <shareRate>1</shareRate>
      <customerName>赵海柱</customerName>
      <customerBirthday>1990-03-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199003076931</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000245083</insuredId>
      <orderId>1</orderId>
      <policyCode>990042114518</policyCode>
      <listId>3600000364034</listId>
      <busiItemId>3600000340122</busiItemId>
      <policyId>3600000431535</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00507000</productCode>
      <insuredId>3600000245083</insuredId>
      <orderId>1</orderId>
      <policyCode>990042114518</policyCode>
      <listId>3600000364035</listId>
      <busiItemId>3600000340123</busiItemId>
      <policyId>3600000431535</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00726000</productCode>
      <insuredId>3600000245083</insuredId>
      <orderId>1</orderId>
      <policyCode>990042114518</policyCode>
      <listId>3600000364036</listId>
      <busiItemId>3600000340124</busiItemId>
      <policyId>3600000431535</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000431535</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2029-01-01 16:00:00.0 UTC</paidupDate>
      <expiryDate>3023-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000335176</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3023-01-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340122</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>17340</renewalDiscntedPremAf>
      <totalPremAf>17340</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>17340</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>17340</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000431535</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2024-01-01 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <productId>244</productId>
      <productCode>507000</productCode>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000335177</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-01-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340123</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>500</renewalDiscntedPremAf>
      <totalPremAf>500</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>500</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>500</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66068182901802</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000431535</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2024-01-01 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-01-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042114518</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-01-01 16:00:00.0 UTC</validateDate>
      <productId>278</productId>
      <productCode>726000</productCode>
      <applyDate>2023-12-31 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000335178</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-01-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000340124</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>2055</renewalDiscntedPremAf>
      <totalPremAf>2055</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>2055</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>2055</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2025-01-01 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335176</itemId>
      <organCode>********</organCode>
      <policyCode>990042114518</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345061</listId>
      <payDueDate>2025-01-01 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000340122</busiItemId>
      <policyId>3600000431535</policyId>
      <nextPrem>17340</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335177</itemId>
      <organCode>********</organCode>
      <policyCode>990042114518</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345062</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000340123</busiItemId>
      <policyId>3600000431535</policyId>
      <nextPrem>500</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000335178</itemId>
      <organCode>********</organCode>
      <policyCode>990042114518</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000345063</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000340124</busiItemId>
      <policyId>3600000431535</policyId>
      <nextPrem>2055</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000242910</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000122005</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>66068182901802</applyCode>
      <policyCode>990042114518</policyCode>
      <listId>3600000377701</listId>
      <policyId>3600000431535</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2024-01-01 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>66068182901802</applyCode>
      <policyCode>990042114518</policyCode>
      <listId>3600000378136</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>3600000431535</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000242911</addressId>
      <standLife>1</standLife>
      <customerId>3600000122005</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>66068182901802</applyCode>
      <policyCode>990042114518</policyCode>
      <insuredAge>33</insuredAge>
      <listId>3600000245083</listId>
      <policyId>3600000431535</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2024-12-3 16:14:56 197|pool-30-thread-1|INFO|理赔抄单共计耗时：1985|QueryPolicyUCCImpl.java|194|
