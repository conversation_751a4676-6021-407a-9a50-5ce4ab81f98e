2025-2-24 11:15:33 311|http-bio-9090-exec-1|INFO|理赔抄单共计耗时：fundAmount35|QueryPolicyServiceImpl.java|609|
2025-2-24 11:15:37 037|http-bio-9090-exec-1|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2024-10-10 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3023-10-11 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990043380626</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2024-10-11 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2024-10-10 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2024-10-11 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2024-10-10 16:00:00.0 UTC</submissionDate>
      <issueDate>2024-10-10 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2024-10-10 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181645</busiPrdId>
      <applyDate>2024-10-10 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2024-10-10 16:00:00.0 UTC</issueDate>
      <paidupDate>2029-10-11 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3023-10-11 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990043380626</policyCode>
      <validateDate>2024-10-11 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2024-10-11 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000318114</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000161733</customerId>
      <insuredId>3600000288715</insuredId>
      <policyCode>990043380626</policyCode>
      <designation>00</designation>
      <listId>3600000420689</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>张一丽</customerName>
      <customerBirthday>2006-01-12 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>532929200601132070</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000288715</insuredId>
      <orderId>1</orderId>
      <policyCode>990043380626</policyCode>
      <listId>3600000420260</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>2000000</amount>
      <paidupDate>2029-10-11 16:00:00.0 UTC</paidupDate>
      <expiryDate>3023-10-11 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990043380626</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-10-11 16:00:00.0 UTC</validateDate>
      <productId>181281</productId>
      <productCode>447000</productCode>
      <applyDate>2024-10-10 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000386165</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3023-10-11 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>98000</renewalDiscntedPremAf>
      <totalPremAf>98000</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>98000</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>98000</initialDiscntPremAf>
      <initialAmount>2000000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2025-10-11 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000386165</itemId>
      <organCode>********</organCode>
      <policyCode>990043380626</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000400246</listId>
      <payDueDate>2025-10-11 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>98000</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000318094</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000161724</customerId>
      <jobCode>********</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990043380626</policyCode>
      <listId>3600000445454</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2024-10-11 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990043380626</policyCode>
      <listId>3600000446150</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000318114</addressId>
      <standLife>1</standLife>
      <customerId>3600000161733</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>01</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990043380626</policyCode>
      <insuredAge>18</insuredAge>
      <listId>3600000288715</listId>
      <policyId>*************</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>张一丽</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-2-24 11:15:37 037|http-bio-9090-exec-1|INFO|理赔抄单共计耗时：9466|QueryPolicyUCCImpl.java|194|
2025-2-24 11:16:14 675|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：fundAmount26|QueryPolicyServiceImpl.java|609|
2025-2-24 11:16:14 802|http-bio-9090-exec-8|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2024-10-10 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3023-10-11 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990043380626</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2024-10-11 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2024-10-10 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2024-10-11 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2024-10-10 16:00:00.0 UTC</submissionDate>
      <issueDate>2024-10-10 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2024-10-10 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181645</busiPrdId>
      <applyDate>2024-10-10 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2024-10-10 16:00:00.0 UTC</issueDate>
      <paidupDate>2029-10-11 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3023-10-11 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990043380626</policyCode>
      <validateDate>2024-10-11 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2024-10-11 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000318114</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000161733</customerId>
      <insuredId>3600000288715</insuredId>
      <policyCode>990043380626</policyCode>
      <designation>00</designation>
      <listId>3600000420689</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>张一丽</customerName>
      <customerBirthday>2006-01-12 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>532929200601132070</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000288715</insuredId>
      <orderId>1</orderId>
      <policyCode>990043380626</policyCode>
      <listId>3600000420260</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>2000000</amount>
      <paidupDate>2029-10-11 16:00:00.0 UTC</paidupDate>
      <expiryDate>3023-10-11 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990043380626</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-10-11 16:00:00.0 UTC</validateDate>
      <productId>181281</productId>
      <productCode>447000</productCode>
      <applyDate>2024-10-10 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000386165</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3023-10-11 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>98000</renewalDiscntedPremAf>
      <totalPremAf>98000</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>98000</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>98000</initialDiscntPremAf>
      <initialAmount>2000000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2025-10-11 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000386165</itemId>
      <organCode>********</organCode>
      <policyCode>990043380626</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000400246</listId>
      <payDueDate>2025-10-11 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>98000</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000318094</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000161724</customerId>
      <jobCode>********</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990043380626</policyCode>
      <listId>3600000445454</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2024-10-11 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990043380626</policyCode>
      <listId>3600000446150</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000318114</addressId>
      <standLife>1</standLife>
      <customerId>3600000161733</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>01</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990043380626</policyCode>
      <insuredAge>18</insuredAge>
      <listId>3600000288715</listId>
      <policyId>*************</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>张一丽</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-2-24 11:16:14 803|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：329|QueryPolicyUCCImpl.java|194|
