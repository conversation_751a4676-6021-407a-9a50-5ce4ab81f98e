2025-8-29 13:47:39 454|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：fundAmount17|QueryPolicyServiceImpl.java|609|
2025-8-29 13:47:39 469|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：fundAmount11|QueryPolicyServiceImpl.java|609|
2025-8-29 13:47:39 837|http-bio-9090-exec-8|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <channelType>01</channelType>
      <policyId>3600000031003</policyId>
      <derivation>1</derivation>
      <inputDate>2023-09-14 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-09-14 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>86470000</agentOrgId>
      <initialPremDate>2023-09-14 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <busiProdCode>00555000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <waiver>0</waiver>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <paidupDate>2028-09-15 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181406</busiPrdId>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <busiProdCode>00946000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <renew>1</renew>
      <renewTimes>1</renewTimes>
      <maturityDate>2025-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>3600000016533</masterBusiItemId>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <paidupDate>2025-09-15 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <renewDecision>3</renewDecision>
      <validateDate>2024-09-15 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2028-09-15 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2023-09-15 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000008200</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00555000</productCode>
      <customerId>3600000003770</customerId>
      <insuredId>3600000011600</insuredId>
      <policyCode>990038752532</policyCode>
      <designation>00</designation>
      <listId>3600000009354</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <shareRate>1</shareRate>
      <customerName>罗一宁</customerName>
      <customerBirthday>1983-01-09 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>341204198301108798</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000008200</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00946000</productCode>
      <customerId>3600000003770</customerId>
      <insuredId>3600000011600</insuredId>
      <policyCode>990038752532</policyCode>
      <designation>00</designation>
      <listId>3600000009355</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <shareRate>1</shareRate>
      <customerName>罗一宁</customerName>
      <customerBirthday>1983-01-09 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>341204198301108798</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00555000</productCode>
      <insuredId>3600000011600</insuredId>
      <orderId>1</orderId>
      <policyCode>990038752532</policyCode>
      <listId>3600000011530</listId>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00946000</productCode>
      <insuredId>3600000011600</insuredId>
      <orderId>1</orderId>
      <policyCode>990038752532</policyCode>
      <listId>3600000011531</listId>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000031003</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2028-09-15 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000011535</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016533</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>22320</renewalDiscntedPremAf>
      <totalPremAf>44640</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>22320</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>22320</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000031003</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2025-09-15 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-09-15 16:00:00.0 UTC</validateDate>
      <productId>180920</productId>
      <productCode>946000</productCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000011536</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016534</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>1552.5</renewalDiscntedPremAf>
      <totalPremAf>1635</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>1635</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>1552.5</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>2</policyYear>
      <extractionDueDate>2025-09-15 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000011535</itemId>
      <organCode>86470000</organCode>
      <policyCode>990038752532</policyCode>
      <policyPeriod>2</policyPeriod>
      <listId>3600000012044</listId>
      <payDueDate>2025-09-15 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <nextPrem>22320</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>2</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000011536</itemId>
      <organCode>86470000</organCode>
      <renewDecisionStatus>2</renewDecisionStatus>
      <policyCode>990038752532</policyCode>
      <policyPeriod>2</policyPeriod>
      <listId>3600000012045</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <nextPrem>1552.5</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000008199</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000003770</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <listId>3600000026621</listId>
      <policyId>3600000031003</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-09-15 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <listId>3600000026820</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>3600000031003</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000008200</addressId>
      <standLife>1</standLife>
      <customerId>3600000003770</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <insuredAge>40</insuredAge>
      <listId>3600000011600</listId>
      <policyId>3600000031003</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-8-29 13:47:39 838|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：1781|QueryPolicyUCCImpl.java|194|
2025-8-29 13:47:46 028|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：fundAmount20|QueryPolicyServiceImpl.java|609|
2025-8-29 13:47:46 045|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：fundAmount10|QueryPolicyServiceImpl.java|609|
2025-8-29 13:47:46 103|http-bio-9090-exec-8|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <channelType>01</channelType>
      <policyId>3600000031003</policyId>
      <derivation>1</derivation>
      <inputDate>2023-09-14 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-09-14 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>86470000</agentOrgId>
      <initialPremDate>2023-09-14 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <busiProdCode>00555000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <waiver>0</waiver>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <paidupDate>2028-09-15 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181406</busiPrdId>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <busiProdCode>00946000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <renew>1</renew>
      <renewTimes>1</renewTimes>
      <maturityDate>2025-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>3600000016533</masterBusiItemId>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <paidupDate>2025-09-15 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <renewDecision>3</renewDecision>
      <validateDate>2024-09-15 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2028-09-15 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2023-09-15 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000008200</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00555000</productCode>
      <customerId>3600000003770</customerId>
      <insuredId>3600000011600</insuredId>
      <policyCode>990038752532</policyCode>
      <designation>00</designation>
      <listId>3600000009354</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <shareRate>1</shareRate>
      <customerName>罗一宁</customerName>
      <customerBirthday>1983-01-09 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>341204198301108798</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000008200</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00946000</productCode>
      <customerId>3600000003770</customerId>
      <insuredId>3600000011600</insuredId>
      <policyCode>990038752532</policyCode>
      <designation>00</designation>
      <listId>3600000009355</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <shareRate>1</shareRate>
      <customerName>罗一宁</customerName>
      <customerBirthday>1983-01-09 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>341204198301108798</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00555000</productCode>
      <insuredId>3600000011600</insuredId>
      <orderId>1</orderId>
      <policyCode>990038752532</policyCode>
      <listId>3600000011530</listId>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00946000</productCode>
      <insuredId>3600000011600</insuredId>
      <orderId>1</orderId>
      <policyCode>990038752532</policyCode>
      <listId>3600000011531</listId>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000031003</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2028-09-15 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000011535</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016533</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>22320</renewalDiscntedPremAf>
      <totalPremAf>44640</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>22320</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>22320</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000031003</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2025-09-15 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-09-15 16:00:00.0 UTC</validateDate>
      <productId>180920</productId>
      <productCode>946000</productCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000011536</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016534</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>1552.5</renewalDiscntedPremAf>
      <totalPremAf>1635</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>1635</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>1552.5</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>2</policyYear>
      <extractionDueDate>2025-09-15 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000011535</itemId>
      <organCode>86470000</organCode>
      <policyCode>990038752532</policyCode>
      <policyPeriod>2</policyPeriod>
      <listId>3600000012044</listId>
      <payDueDate>2025-09-15 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <nextPrem>22320</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>2</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000011536</itemId>
      <organCode>86470000</organCode>
      <renewDecisionStatus>2</renewDecisionStatus>
      <policyCode>990038752532</policyCode>
      <policyPeriod>2</policyPeriod>
      <listId>3600000012045</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <nextPrem>1552.5</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000008199</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000003770</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <listId>3600000026621</listId>
      <policyId>3600000031003</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-09-15 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <listId>3600000026820</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>3600000031003</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000008200</addressId>
      <standLife>1</standLife>
      <customerId>3600000003770</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <insuredAge>40</insuredAge>
      <listId>3600000011600</listId>
      <policyId>3600000031003</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-8-29 13:47:46 103|http-bio-9090-exec-8|INFO|理赔抄单共计耗时：450|QueryPolicyUCCImpl.java|194|
2025-8-29 15:00:21 814|http-bio-9090-exec-10|INFO|理赔抄单共计耗时：fundAmount9|QueryPolicyServiceImpl.java|609|
2025-8-29 15:00:21 823|http-bio-9090-exec-10|INFO|理赔抄单共计耗时：fundAmount6|QueryPolicyServiceImpl.java|609|
2025-8-29 15:00:21 849|http-bio-9090-exec-10|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <channelType>01</channelType>
      <policyId>3600000031003</policyId>
      <derivation>1</derivation>
      <inputDate>2023-09-14 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-09-14 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>86470000</agentOrgId>
      <initialPremDate>2023-09-14 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <busiProdCode>00555000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <waiver>0</waiver>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <paidupDate>2028-09-15 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181406</busiPrdId>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <busiProdCode>00946000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <renew>1</renew>
      <renewTimes>1</renewTimes>
      <maturityDate>2025-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>3600000016533</masterBusiItemId>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <paidupDate>2025-09-15 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <renewDecision>3</renewDecision>
      <validateDate>2024-09-15 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2028-09-15 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2023-09-15 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000008200</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00555000</productCode>
      <customerId>3600000003770</customerId>
      <insuredId>3600000011600</insuredId>
      <policyCode>990038752532</policyCode>
      <designation>00</designation>
      <listId>3600000009354</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <shareRate>1</shareRate>
      <customerName>罗一宁</customerName>
      <customerBirthday>1983-01-09 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>341204198301108798</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000008200</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00946000</productCode>
      <customerId>3600000003770</customerId>
      <insuredId>3600000011600</insuredId>
      <policyCode>990038752532</policyCode>
      <designation>00</designation>
      <listId>3600000009355</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <shareRate>1</shareRate>
      <customerName>罗一宁</customerName>
      <customerBirthday>1983-01-09 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>341204198301108798</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00555000</productCode>
      <insuredId>3600000011600</insuredId>
      <orderId>1</orderId>
      <policyCode>990038752532</policyCode>
      <listId>3600000011530</listId>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00946000</productCode>
      <insuredId>3600000011600</insuredId>
      <orderId>1</orderId>
      <policyCode>990038752532</policyCode>
      <listId>3600000011531</listId>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000031003</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2028-09-15 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000011535</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016533</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>22320</renewalDiscntedPremAf>
      <totalPremAf>44640</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>22320</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>22320</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000031003</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2025-09-15 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-09-15 16:00:00.0 UTC</validateDate>
      <productId>180920</productId>
      <productCode>946000</productCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000011536</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016534</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>1552.5</renewalDiscntedPremAf>
      <totalPremAf>1635</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>1635</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>1552.5</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>2</policyYear>
      <extractionDueDate>2025-09-15 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000011535</itemId>
      <organCode>86470000</organCode>
      <policyCode>990038752532</policyCode>
      <policyPeriod>2</policyPeriod>
      <listId>3600000012044</listId>
      <payDueDate>2025-09-15 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <nextPrem>22320</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>2</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000011536</itemId>
      <organCode>86470000</organCode>
      <renewDecisionStatus>2</renewDecisionStatus>
      <policyCode>990038752532</policyCode>
      <policyPeriod>2</policyPeriod>
      <listId>3600000012045</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <nextPrem>1552.5</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000008199</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000003770</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <listId>3600000026621</listId>
      <policyId>3600000031003</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-09-15 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <listId>3600000026820</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>3600000031003</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000008200</addressId>
      <standLife>1</standLife>
      <customerId>3600000003770</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <insuredAge>40</insuredAge>
      <listId>3600000011600</listId>
      <policyId>3600000031003</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-8-29 15:00:21 849|http-bio-9090-exec-10|INFO|理赔抄单共计耗时：169|QueryPolicyUCCImpl.java|194|
2025-8-29 15:00:22 436|http-bio-9090-exec-10|INFO|理赔抄单共计耗时：fundAmount8|QueryPolicyServiceImpl.java|609|
2025-8-29 15:00:22 447|http-bio-9090-exec-10|INFO|理赔抄单共计耗时：fundAmount8|QueryPolicyServiceImpl.java|609|
2025-8-29 15:00:22 460|http-bio-9090-exec-10|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <channelType>01</channelType>
      <policyId>3600000031003</policyId>
      <derivation>1</derivation>
      <inputDate>2023-09-14 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-09-14 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>86470000</agentOrgId>
      <initialPremDate>2023-09-14 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <busiProdCode>00555000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <waiver>0</waiver>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <paidupDate>2028-09-15 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181406</busiPrdId>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <busiProdCode>00946000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <renew>1</renew>
      <renewTimes>1</renewTimes>
      <maturityDate>2025-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>3600000016533</masterBusiItemId>
      <issueDate>2023-09-14 16:00:00.0 UTC</issueDate>
      <paidupDate>2025-09-15 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <renewDecision>3</renewDecision>
      <validateDate>2024-09-15 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2028-09-15 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2023-09-15 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2023-09-15 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000008200</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00555000</productCode>
      <customerId>3600000003770</customerId>
      <insuredId>3600000011600</insuredId>
      <policyCode>990038752532</policyCode>
      <designation>00</designation>
      <listId>3600000009354</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <shareRate>1</shareRate>
      <customerName>罗一宁</customerName>
      <customerBirthday>1983-01-09 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>341204198301108798</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000008200</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00946000</productCode>
      <customerId>3600000003770</customerId>
      <insuredId>3600000011600</insuredId>
      <policyCode>990038752532</policyCode>
      <designation>00</designation>
      <listId>3600000009355</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <shareRate>1</shareRate>
      <customerName>罗一宁</customerName>
      <customerBirthday>1983-01-09 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>341204198301108798</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00555000</productCode>
      <insuredId>3600000011600</insuredId>
      <orderId>1</orderId>
      <policyCode>990038752532</policyCode>
      <listId>3600000011530</listId>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00946000</productCode>
      <insuredId>3600000011600</insuredId>
      <orderId>1</orderId>
      <policyCode>990038752532</policyCode>
      <listId>3600000011531</listId>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000031003</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>150000</amount>
      <paidupDate>2028-09-15 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-09-15 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000011535</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016533</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>22320</renewalDiscntedPremAf>
      <totalPremAf>44640</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>22320</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>22320</initialDiscntPremAf>
      <initialAmount>150000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>66065354691509</applyCode>
      <organCode>86470000</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>3600000031003</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2025-09-15 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-09-15 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990038752532</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-09-15 16:00:00.0 UTC</validateDate>
      <productId>180920</productId>
      <productCode>946000</productCode>
      <applyDate>2023-09-14 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000011536</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-09-15 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000016534</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>1552.5</renewalDiscntedPremAf>
      <totalPremAf>1635</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>1635</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>1552.5</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>2</policyYear>
      <extractionDueDate>2025-09-15 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000011535</itemId>
      <organCode>86470000</organCode>
      <policyCode>990038752532</policyCode>
      <policyPeriod>2</policyPeriod>
      <listId>3600000012044</listId>
      <payDueDate>2025-09-15 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000016533</busiItemId>
      <policyId>3600000031003</policyId>
      <nextPrem>22320</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>2</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000011536</itemId>
      <organCode>86470000</organCode>
      <renewDecisionStatus>2</renewDecisionStatus>
      <policyCode>990038752532</policyCode>
      <policyPeriod>2</policyPeriod>
      <listId>3600000012045</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000016534</busiItemId>
      <policyId>3600000031003</policyId>
      <nextPrem>1552.5</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000008199</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000003770</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <listId>3600000026621</listId>
      <policyId>3600000031003</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-09-15 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <listId>3600000026820</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>3600000031003</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000008200</addressId>
      <standLife>1</standLife>
      <customerId>3600000003770</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>66065354691509</applyCode>
      <policyCode>990038752532</policyCode>
      <insuredAge>40</insuredAge>
      <listId>3600000011600</listId>
      <policyId>3600000031003</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-8-29 15:00:22 460|http-bio-9090-exec-10|INFO|理赔抄单共计耗时：132|QueryPolicyUCCImpl.java|194|
