2025-5-16 14:46:22 470|http-bio-9090-exec-10|INFO|理赔抄单共计耗时：fundAmount72|QueryPolicyServiceImpl.java|609|
2025-5-16 14:46:22 509|http-bio-9090-exec-10|INFO|理赔抄单共计耗时：fundAmount35|QueryPolicyServiceImpl.java|609|
2025-5-16 14:46:22 700|http-bio-9090-exec-10|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2024-08-08 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-11-01 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990042716527</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-11-01 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-10-31 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-11-01 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-10-31 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-10-31 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-10-31 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181496</busiPrdId>
      <applyDate>2023-10-31 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2023-10-31 16:00:00.0 UTC</issueDate>
      <paidupDate>2028-11-01 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-11-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042716527</policyCode>
      <validateDate>2023-11-01 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-11-01 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181406</busiPrdId>
      <applyDate>2023-10-31 16:00:00.0 UTC</applyDate>
      <busiProdCode>00946000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>2024-11-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000373920</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>*************</masterBusiItemId>
      <issueDate>2023-10-31 16:00:00.0 UTC</issueDate>
      <paidupDate>2023-11-01 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2024-11-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042716527</policyCode>
      <validateDate>2023-11-01 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2028-11-01 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2023-11-01 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2023-11-01 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>11</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000278176</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000144416</customerId>
      <insuredId>3600000275873</insuredId>
      <policyCode>990042716527</policyCode>
      <designation>00</designation>
      <listId>3600000400618</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵鄂</customerName>
      <customerBirthday>1984-10-05 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>420105198410063015</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000278176</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00946000</productCode>
      <customerId>3600000144416</customerId>
      <insuredId>3600000275873</insuredId>
      <policyCode>990042716527</policyCode>
      <designation>00</designation>
      <listId>3600000400619</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000373920</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>赵鄂</customerName>
      <customerBirthday>1984-10-05 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>420105198410063015</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000275873</insuredId>
      <orderId>1</orderId>
      <policyCode>990042716527</policyCode>
      <listId>3600000402147</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00946000</productCode>
      <insuredId>3600000275873</insuredId>
      <orderId>1</orderId>
      <policyCode>990042716527</policyCode>
      <listId>3600000402148</listId>
      <busiItemId>3600000373920</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>50000</amount>
      <paidupDate>2023-11-01 16:00:00.0 UTC</paidupDate>
      <expiryDate>2024-11-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042716527</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-11-01 16:00:00.0 UTC</validateDate>
      <productId>180920</productId>
      <productCode>946000</productCode>
      <applyDate>2023-10-31 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000368999</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2024-11-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000373920</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>1270</renewalDiscntedPremAf>
      <totalPremAf>1270</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>1270</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>1270</initialDiscntPremAf>
      <initialAmount>50000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2028-11-01 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-11-01 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990042716527</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-11-01 16:00:00.0 UTC</validateDate>
      <productId>181010</productId>
      <productCode>858000</productCode>
      <applyDate>2023-10-31 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000368998</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-11-01 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>6960</renewalDiscntedPremAf>
      <totalPremAf>6960</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>6960</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>6960</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-11-01 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000368998</itemId>
      <organCode>********</organCode>
      <policyCode>990042716527</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000382226</listId>
      <payDueDate>2024-11-01 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>6960</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000368999</itemId>
      <organCode>********</organCode>
      <policyCode>990042716527</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000382227</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000373920</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>1270</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000278171</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000144416</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990042716527</policyCode>
      <listId>3600000418239</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-11-01 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990042716527</policyCode>
      <listId>3600000418797</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000278176</addressId>
      <standLife>1</standLife>
      <customerId>3600000144416</customerId>
      <customerHeight>175</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990042716527</policyCode>
      <insuredAge>39</insuredAge>
      <listId>3600000275873</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>赵鄂</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-5-16 14:46:22 700|http-bio-9090-exec-10|INFO|理赔抄单共计耗时：10221|QueryPolicyUCCImpl.java|194|
