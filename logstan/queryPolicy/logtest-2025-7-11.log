2025-7-11 10:57:55 523|http-bio-9090-exec-3|INFO|理赔抄单共计耗时：fundAmount181|QueryPolicyServiceImpl.java|609|
2025-7-11 10:57:59 914|http-bio-9090-exec-3|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>1638630</policyId>
      <derivation>1</derivation>
      <inputDate>2023-01-04 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-01-05 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-01-04 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-01-04 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-01-04 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <waiver>0</waiver>
      <issueDate>2023-01-04 16:00:00.0 UTC</issueDate>
      <paidupDate>2033-01-05 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-01-05 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1118579</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>397749</customerId>
      <insuredId>417762</insuredId>
      <policyCode>990036048539</policyCode>
      <designation>00</designation>
      <listId>528070</listId>
      <legalBene>0</legalBene>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <shareRate>1</shareRate>
      <customerName>李一云</customerName>
      <customerBirthday>1979-12-31 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130923198001014578</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>417762</insuredId>
      <orderId>1</orderId>
      <policyCode>990036048539</policyCode>
      <listId>529645</listId>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>10</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1638630</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2033-01-05 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>567040</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-01-05 16:00:00.0 UTC</maturityDate>
      <busiItemId>561772</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>9260</renewalDiscntedPremAf>
      <totalPremAf>9260</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>9260</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>9260</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-01-05 16:00:00.0 UTC</extractionDueDate>
      <itemId>567040</itemId>
      <organCode>********</organCode>
      <policyCode>990036048539</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>623666</listId>
      <payDueDate>2024-01-05 16:00:00.0 UTC</payDueDate>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <nextPrem>9260</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>1118578</addressId>
      <customerHeight>170</customerHeight>
      <customerId>397748</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <listId>2035273</listId>
      <policyId>1638630</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-01-05 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <listId>1668932</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>1638630</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>1118579</addressId>
      <standLife>1</standLife>
      <customerId>397749</customerId>
      <customerHeight>170</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>07</relationToPh>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <insuredAge>43</insuredAge>
      <listId>417762</listId>
      <policyId>1638630</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>李一云</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-7-11 10:57:59 914|http-bio-9090-exec-3|INFO|理赔抄单共计耗时：21929|QueryPolicyUCCImpl.java|194|
2025-7-11 16:32:22 832|http-bio-9090-exec-5|INFO|理赔抄单共计耗时：fundAmount255|QueryPolicyServiceImpl.java|609|
2025-7-11 16:32:27 946|http-bio-9090-exec-5|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>1638630</policyId>
      <derivation>1</derivation>
      <inputDate>2023-01-04 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-01-05 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-01-04 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-01-04 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-01-04 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <waiver>0</waiver>
      <issueDate>2023-01-04 16:00:00.0 UTC</issueDate>
      <paidupDate>2033-01-05 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-01-05 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1118579</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>397749</customerId>
      <insuredId>417762</insuredId>
      <policyCode>990036048539</policyCode>
      <designation>00</designation>
      <listId>528070</listId>
      <legalBene>0</legalBene>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <shareRate>1</shareRate>
      <customerName>李一云</customerName>
      <customerBirthday>1979-12-31 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130923198001014578</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>417762</insuredId>
      <orderId>1</orderId>
      <policyCode>990036048539</policyCode>
      <listId>529645</listId>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>10</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1638630</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2033-01-05 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>567040</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-01-05 16:00:00.0 UTC</maturityDate>
      <busiItemId>561772</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>9260</renewalDiscntedPremAf>
      <totalPremAf>9260</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>9260</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>9260</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-01-05 16:00:00.0 UTC</extractionDueDate>
      <itemId>567040</itemId>
      <organCode>********</organCode>
      <policyCode>990036048539</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>623666</listId>
      <payDueDate>2024-01-05 16:00:00.0 UTC</payDueDate>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <nextPrem>9260</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>1118578</addressId>
      <customerHeight>170</customerHeight>
      <customerId>397748</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <listId>2035273</listId>
      <policyId>1638630</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-01-05 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <listId>1668932</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>1638630</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>1118579</addressId>
      <standLife>1</standLife>
      <customerId>397749</customerId>
      <customerHeight>170</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>07</relationToPh>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <insuredAge>43</insuredAge>
      <listId>417762</listId>
      <policyId>1638630</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>李一云</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-7-11 16:32:27 946|http-bio-9090-exec-5|INFO|理赔抄单共计耗时：24392|QueryPolicyUCCImpl.java|194|
2025-7-11 16:34:05 203|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：fundAmount0|QueryPolicyServiceImpl.java|609|
2025-7-11 16:34:05 218|http-bio-9090-exec-2|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>1638630</policyId>
      <derivation>1</derivation>
      <inputDate>2023-01-04 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-01-05 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-01-04 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-01-04 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-01-04 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <waiver>0</waiver>
      <issueDate>2023-01-04 16:00:00.0 UTC</issueDate>
      <paidupDate>2033-01-05 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-01-05 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1118579</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>397749</customerId>
      <insuredId>417762</insuredId>
      <policyCode>990036048539</policyCode>
      <designation>00</designation>
      <listId>528070</listId>
      <legalBene>0</legalBene>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <shareRate>1</shareRate>
      <customerName>李一云</customerName>
      <customerBirthday>1979-12-31 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130923198001014578</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>417762</insuredId>
      <orderId>1</orderId>
      <policyCode>990036048539</policyCode>
      <listId>529645</listId>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>10</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1638630</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2033-01-05 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>567040</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-01-05 16:00:00.0 UTC</maturityDate>
      <busiItemId>561772</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>9260</renewalDiscntedPremAf>
      <totalPremAf>9260</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>9260</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>9260</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-01-05 16:00:00.0 UTC</extractionDueDate>
      <itemId>567040</itemId>
      <organCode>********</organCode>
      <policyCode>990036048539</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>623666</listId>
      <payDueDate>2024-01-05 16:00:00.0 UTC</payDueDate>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <nextPrem>9260</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>1118578</addressId>
      <customerHeight>170</customerHeight>
      <customerId>397748</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <listId>2035273</listId>
      <policyId>1638630</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-01-05 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <listId>1668932</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>1638630</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>1118579</addressId>
      <standLife>1</standLife>
      <customerId>397749</customerId>
      <customerHeight>170</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>07</relationToPh>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <insuredAge>43</insuredAge>
      <listId>417762</listId>
      <policyId>1638630</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>李一云</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-7-11 16:34:05 218|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：82|QueryPolicyUCCImpl.java|194|
2025-7-11 16:36:05 306|http-bio-9090-exec-6|INFO|理赔抄单共计耗时：fundAmount15|QueryPolicyServiceImpl.java|609|
2025-7-11 16:36:05 321|http-bio-9090-exec-6|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>1638630</policyId>
      <derivation>1</derivation>
      <inputDate>2023-01-04 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-01-05 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-01-04 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-01-04 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-01-04 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <waiver>0</waiver>
      <issueDate>2023-01-04 16:00:00.0 UTC</issueDate>
      <paidupDate>2033-01-05 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-01-05 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1118579</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>397749</customerId>
      <insuredId>417762</insuredId>
      <policyCode>990036048539</policyCode>
      <designation>00</designation>
      <listId>528070</listId>
      <legalBene>0</legalBene>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <shareRate>1</shareRate>
      <customerName>李一云</customerName>
      <customerBirthday>1979-12-31 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130923198001014578</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>417762</insuredId>
      <orderId>1</orderId>
      <policyCode>990036048539</policyCode>
      <listId>529645</listId>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>10</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1638630</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2033-01-05 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>567040</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-01-05 16:00:00.0 UTC</maturityDate>
      <busiItemId>561772</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>9260</renewalDiscntedPremAf>
      <totalPremAf>9260</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>9260</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>9260</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-01-05 16:00:00.0 UTC</extractionDueDate>
      <itemId>567040</itemId>
      <organCode>********</organCode>
      <policyCode>990036048539</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>623666</listId>
      <payDueDate>2024-01-05 16:00:00.0 UTC</payDueDate>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <nextPrem>9260</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>1118578</addressId>
      <customerHeight>170</customerHeight>
      <customerId>397748</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <listId>2035273</listId>
      <policyId>1638630</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-01-05 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <listId>1668932</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>1638630</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>1118579</addressId>
      <standLife>1</standLife>
      <customerId>397749</customerId>
      <customerHeight>170</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>07</relationToPh>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <insuredAge>43</insuredAge>
      <listId>417762</listId>
      <policyId>1638630</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>李一云</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-7-11 16:36:05 321|http-bio-9090-exec-6|INFO|理赔抄单共计耗时：132|QueryPolicyUCCImpl.java|194|
2025-7-11 18:38:12 402|http-bio-9090-exec-1|INFO|理赔抄单共计耗时：fundAmount15|QueryPolicyServiceImpl.java|609|
2025-7-11 18:38:12 417|http-bio-9090-exec-1|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>1638630</policyId>
      <derivation>1</derivation>
      <inputDate>2023-01-04 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2023-01-05 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2023-01-04 16:00:00.0 UTC</submissionDate>
      <issueDate>2023-01-04 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2023-01-04 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>1542</busiPrdId>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <waiver>0</waiver>
      <issueDate>2023-01-04 16:00:00.0 UTC</issueDate>
      <paidupDate>2033-01-05 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2023-01-05 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>1118579</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>397749</customerId>
      <insuredId>417762</insuredId>
      <policyCode>990036048539</policyCode>
      <designation>00</designation>
      <listId>528070</listId>
      <legalBene>0</legalBene>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <shareRate>1</shareRate>
      <customerName>李一云</customerName>
      <customerBirthday>1979-12-31 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>130923198001014578</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>417762</insuredId>
      <orderId>1</orderId>
      <policyCode>990036048539</policyCode>
      <listId>529645</listId>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>10</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>1638630</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2033-01-05 16:00:00.0 UTC</paidupDate>
      <expiryDate>3022-01-05 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990036048539</policyCode>
      <countWay>1</countWay>
      <validateDate>2023-01-05 16:00:00.0 UTC</validateDate>
      <productId>1307</productId>
      <productCode>555000</productCode>
      <applyDate>2023-01-04 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>567040</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3022-01-05 16:00:00.0 UTC</maturityDate>
      <busiItemId>561772</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>9260</renewalDiscntedPremAf>
      <totalPremAf>9260</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>9260</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>9260</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2024-01-05 16:00:00.0 UTC</extractionDueDate>
      <itemId>567040</itemId>
      <organCode>********</organCode>
      <policyCode>990036048539</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>623666</listId>
      <payDueDate>2024-01-05 16:00:00.0 UTC</payDueDate>
      <busiItemId>561772</busiItemId>
      <policyId>1638630</policyId>
      <nextPrem>9260</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>1118578</addressId>
      <customerHeight>170</customerHeight>
      <customerId>397748</customerId>
      <jobCode>W001001</jobCode>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <listId>2035273</listId>
      <policyId>1638630</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2023-01-05 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <listId>1668932</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>1638630</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>1118579</addressId>
      <standLife>1</standLife>
      <customerId>397749</customerId>
      <customerHeight>170</customerHeight>
      <jobCode>W001001</jobCode>
      <relationToPh>07</relationToPh>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990036048539</policyCode>
      <insuredAge>43</insuredAge>
      <listId>417762</listId>
      <policyId>1638630</policyId>
      <sociSecu>1</sociSecu>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <customerName>李一云</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-7-11 18:38:12 417|http-bio-9090-exec-1|INFO|理赔抄单共计耗时：132|QueryPolicyUCCImpl.java|194|
