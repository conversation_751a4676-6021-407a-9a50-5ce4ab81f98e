2025-8-26 09:53:30 945|pool-27-thread-2|INFO|理赔抄单共计耗时：fundAmount17|QueryPolicyServiceImpl.java|609|
2025-8-26 09:53:30 963|pool-27-thread-2|INFO|理赔抄单共计耗时：fundAmount18|QueryPolicyServiceImpl.java|609|
2025-8-26 09:53:30 993|pool-27-thread-2|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>0</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-08-11 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990046906656</policyCode>
      <branchCode>8624</branchCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2024-12-30 16:00:00.0 UTC</submissionDate>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2025-08-11 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181504</busiPrdId>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>1</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2029-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046906656</policyCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181438</busiPrdId>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>00831000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>1</renew>
      <maturityDate>2025-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000494248</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>*************</masterBusiItemId>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2024-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046906656</policyCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2027-12-31 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-12-31 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>11</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000540534</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000287853</customerId>
      <insuredId>3600000344305</insuredId>
      <policyCode>990046906656</policyCode>
      <designation>00</designation>
      <listId>3600000493952</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>穆桐雨</customerName>
      <customerBirthday>2016-12-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>610481201612073011</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000540534</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00831000</productCode>
      <customerId>3600000287853</customerId>
      <insuredId>3600000344305</insuredId>
      <policyCode>990046906656</policyCode>
      <designation>00</designation>
      <listId>3600000493953</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000494248</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>穆桐雨</customerName>
      <customerBirthday>2016-12-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>610481201612073011</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000344305</insuredId>
      <orderId>1</orderId>
      <policyCode>990046906656</policyCode>
      <listId>3600000487747</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00831000</productCode>
      <insuredId>3600000344305</insuredId>
      <orderId>1</orderId>
      <policyCode>990046906656</policyCode>
      <listId>3600000487748</listId>
      <busiItemId>3600000494248</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>6000</amount>
      <paidupDate>2024-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046906656</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <productId>180952</productId>
      <productCode>831000</productCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000457504</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000494248</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>641</renewalDiscntedPremAf>
      <totalPremAf>641</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>641</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>641</initialDiscntPremAf>
      <initialAmount>6000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>1</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2029-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046906656</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <productId>181018</productId>
      <productCode>857000</productCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000457503</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3023-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>6780</renewalDiscntedPremAf>
      <totalPremAf>6780</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>6780</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>6780</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2025-12-31 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000457503</itemId>
      <organCode>********</organCode>
      <policyCode>990046906656</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000474794</listId>
      <payDueDate>2025-12-31 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>6780</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000457504</itemId>
      <organCode>********</organCode>
      <policyCode>990046906656</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000474795</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000494248</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>641</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000540516</addressId>
      <customerHeight>177</customerHeight>
      <customerId>3600000287842</customerId>
      <jobCode>A2601001</jobCode>
      <customerWeight>77</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990046906656</policyCode>
      <listId>3600000576425</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>岳窝屿</agentName>
      <agentStartDate>2024-12-31 16:00:00.0 UTC</agentStartDate>
      <agentMobile>15156614603</agentMobile>
      <agentType>1</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990046906656</policyCode>
      <listId>3600000576690</listId>
      <agentOrganCode>360000151222</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>08210413</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000540534</addressId>
      <standLife>1</standLife>
      <customerId>3600000287853</customerId>
      <customerHeight>122</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>03</relationToPh>
      <customerWeight>45</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990046906656</policyCode>
      <insuredAge>8</insuredAge>
      <listId>3600000344305</listId>
      <policyId>*************</policyId>
      <customerName>穆桐雨</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductOtherVO>
      <productId>180952</productId>
      <busiPrdId>181438</busiPrdId>
      <itemId>3600000457504</itemId>
      <field20>8</field20>
      <applyCode>**************</applyCode>
      <busiItemId>3600000494248</busiItemId>
      <policyId>*************</policyId>
      <policyCode>990046906656</policyCode>
      <field1>1</field1>
    </com.nci.tunan.pa.interfaces.vo.ContractProductOtherVO>
  </contractProductOtherVOList>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-8-26 09:53:30 993|pool-27-thread-2|INFO|理赔抄单共计耗时：2104|QueryPolicyUCCImpl.java|194|
2025-8-26 09:53:30 999|pool-27-thread-1|INFO|理赔抄单共计耗时：fundAmount71|QueryPolicyServiceImpl.java|609|
2025-8-26 09:53:31 010|pool-27-thread-1|INFO|理赔抄单共计耗时：fundAmount11|QueryPolicyServiceImpl.java|609|
2025-8-26 09:53:31 017|pool-27-thread-1|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-08-07 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990046868562</policyCode>
      <branchCode>8624</branchCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2024-12-30 16:00:00.0 UTC</submissionDate>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2024-12-30 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181575</busiPrdId>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>1</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2033-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046868562</policyCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181484</busiPrdId>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>00847000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>1</renew>
      <maturityDate>2025-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000493790</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>*************</masterBusiItemId>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2024-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046868562</policyCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2034-12-31 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-12-31 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>11</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000538400</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000287853</customerId>
      <insuredId>3600000343942</insuredId>
      <policyCode>990046868562</policyCode>
      <designation>00</designation>
      <listId>3600000493496</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>穆桐雨</customerName>
      <customerBirthday>2016-12-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>610481201612073011</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000538400</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00847000</productCode>
      <customerId>3600000287853</customerId>
      <insuredId>3600000343942</insuredId>
      <policyCode>990046868562</policyCode>
      <designation>00</designation>
      <listId>3600000493497</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000493790</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>穆桐雨</customerName>
      <customerBirthday>2016-12-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>610481201612073011</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000343942</insuredId>
      <orderId>1</orderId>
      <policyCode>990046868562</policyCode>
      <listId>3600000487328</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00847000</productCode>
      <insuredId>3600000343942</insuredId>
      <orderId>1</orderId>
      <policyCode>990046868562</policyCode>
      <listId>3600000487329</listId>
      <busiItemId>3600000493790</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>10000</amount>
      <paidupDate>2024-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046868562</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <productId>180998</productId>
      <productCode>847000</productCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000457040</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000493790</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>382</renewalDiscntedPremAf>
      <totalPremAf>382</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>382</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>382</initialDiscntPremAf>
      <initialAmount>10000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>1</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>9</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>200000</amount>
      <paidupDate>2033-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046868562</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <productId>181197</productId>
      <productCode>825000</productCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000457039</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3023-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>8540</renewalDiscntedPremAf>
      <totalPremAf>8540</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>8540</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>8540</initialDiscntPremAf>
      <initialAmount>200000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2025-12-31 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000457039</itemId>
      <organCode>********</organCode>
      <policyCode>990046868562</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000474347</listId>
      <payDueDate>2025-12-31 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>8540</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000457040</itemId>
      <organCode>********</organCode>
      <policyCode>990046868562</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000474348</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000493790</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>382</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000538376</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000287842</customerId>
      <jobCode>A2601001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990046868562</policyCode>
      <listId>3600000575018</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>岳窝屿</agentName>
      <agentStartDate>2024-12-31 16:00:00.0 UTC</agentStartDate>
      <agentMobile>15156614603</agentMobile>
      <agentType>1</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990046868562</policyCode>
      <listId>3600000575267</listId>
      <agentOrganCode>360000151222</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>08210413</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000538400</addressId>
      <standLife>1</standLife>
      <customerId>3600000287853</customerId>
      <customerHeight>166</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>03</relationToPh>
      <customerWeight>55</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990046868562</policyCode>
      <insuredAge>8</insuredAge>
      <listId>3600000343942</listId>
      <policyId>*************</policyId>
      <agentRelation>, </agentRelation>
      <ruralPopulationFlag>0</ruralPopulationFlag>
      <disabilityFlag>0</disabilityFlag>
      <customerName>穆桐雨</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-8-26 09:53:31 017|pool-27-thread-1|INFO|理赔抄单共计耗时：2128|QueryPolicyUCCImpl.java|194|
2025-8-26 09:55:49 345|pool-28-thread-1|INFO|理赔抄单共计耗时：fundAmount34|QueryPolicyServiceImpl.java|609|
2025-8-26 09:55:49 385|pool-28-thread-1|INFO|理赔抄单共计耗时：fundAmount40|QueryPolicyServiceImpl.java|609|
2025-8-26 09:55:49 398|pool-28-thread-1|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-08-07 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990046868562</policyCode>
      <branchCode>8624</branchCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2024-12-30 16:00:00.0 UTC</submissionDate>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2024-12-30 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>1</policyFlag>
      <callTimeList>, , , </callTimeList>
      <multiMainriskFlag>0</multiMainriskFlag>
      <banknrtFalg>0</banknrtFalg>
      <isChannelSelfInsured>0</isChannelSelfInsured>
      <isChannelMutualInsured>0</isChannelMutualInsured>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181575</busiPrdId>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>1</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2033-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046868562</policyCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>1</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181484</busiPrdId>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>00847000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>1</renew>
      <maturityDate>2025-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000493790</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>*************</masterBusiItemId>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2024-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046868562</policyCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2034-12-31 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-12-31 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
      <orderId>11</orderId>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000538400</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000287853</customerId>
      <insuredId>3600000343942</insuredId>
      <policyCode>990046868562</policyCode>
      <designation>00</designation>
      <listId>3600000493496</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>穆桐雨</customerName>
      <customerBirthday>2016-12-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>610481201612073011</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000538400</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00847000</productCode>
      <customerId>3600000287853</customerId>
      <insuredId>3600000343942</insuredId>
      <policyCode>990046868562</policyCode>
      <designation>00</designation>
      <listId>3600000493497</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000493790</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>穆桐雨</customerName>
      <customerBirthday>2016-12-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>610481201612073011</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000343942</insuredId>
      <orderId>1</orderId>
      <policyCode>990046868562</policyCode>
      <listId>3600000487328</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00847000</productCode>
      <insuredId>3600000343942</insuredId>
      <orderId>1</orderId>
      <policyCode>990046868562</policyCode>
      <listId>3600000487329</listId>
      <busiItemId>3600000493790</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>10000</amount>
      <paidupDate>2024-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046868562</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <productId>180998</productId>
      <productCode>847000</productCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000457040</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000493790</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>382</renewalDiscntedPremAf>
      <totalPremAf>382</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>382</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>382</initialDiscntPremAf>
      <initialAmount>10000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>1</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>9</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>200000</amount>
      <paidupDate>2033-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046868562</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <productId>181197</productId>
      <productCode>825000</productCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000457039</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3023-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>8540</renewalDiscntedPremAf>
      <totalPremAf>8540</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>8540</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>8540</initialDiscntPremAf>
      <initialAmount>200000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2025-12-31 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000457039</itemId>
      <organCode>********</organCode>
      <policyCode>990046868562</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000474347</listId>
      <payDueDate>2025-12-31 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>8540</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000457040</itemId>
      <organCode>********</organCode>
      <policyCode>990046868562</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000474348</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000493790</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>382</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000538376</addressId>
      <customerHeight>175</customerHeight>
      <customerId>3600000287842</customerId>
      <jobCode>A2601001</jobCode>
      <customerWeight>65</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990046868562</policyCode>
      <listId>3600000575018</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>岳窝屿</agentName>
      <agentStartDate>2024-12-31 16:00:00.0 UTC</agentStartDate>
      <agentMobile>15156614603</agentMobile>
      <agentType>1</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990046868562</policyCode>
      <listId>3600000575267</listId>
      <agentOrganCode>360000151222</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>08210413</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000538400</addressId>
      <standLife>1</standLife>
      <customerId>3600000287853</customerId>
      <customerHeight>166</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>03</relationToPh>
      <customerWeight>55</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990046868562</policyCode>
      <insuredAge>8</insuredAge>
      <listId>3600000343942</listId>
      <policyId>*************</policyId>
      <agentRelation>, </agentRelation>
      <ruralPopulationFlag>0</ruralPopulationFlag>
      <disabilityFlag>0</disabilityFlag>
      <customerName>穆桐雨</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-8-26 09:55:49 399|pool-28-thread-1|INFO|理赔抄单共计耗时：293|QueryPolicyUCCImpl.java|194|
2025-8-26 09:55:49 959|pool-28-thread-2|INFO|理赔抄单共计耗时：fundAmount18|QueryPolicyServiceImpl.java|609|
2025-8-26 09:55:49 976|pool-28-thread-2|INFO|理赔抄单共计耗时：fundAmount14|QueryPolicyServiceImpl.java|609|
2025-8-26 09:55:49 994|pool-28-thread-2|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>0</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>*************</policyId>
      <derivation>1</derivation>
      <inputDate>2025-08-11 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>990046906656</policyCode>
      <branchCode>8624</branchCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2024-12-30 16:00:00.0 UTC</submissionDate>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2025-08-11 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181504</busiPrdId>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>1</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>9999-12-30 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2029-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046906656</policyCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181438</busiPrdId>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <busiProdCode>00831000</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>1</renew>
      <maturityDate>2025-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000494248</busiItemId>
      <policyId>*************</policyId>
      <waiver>0</waiver>
      <masterBusiItemId>*************</masterBusiItemId>
      <issueDate>2024-12-30 16:00:00.0 UTC</issueDate>
      <paidupDate>2024-12-31 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2025-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046906656</policyCode>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <gurntRenewEnd>2027-12-31 16:00:00.0 UTC</gurntRenewEnd>
      <gurntRenewStart>2024-12-31 16:00:00.0 UTC</gurntRenewStart>
      <initialValidateDate>2024-12-31 16:00:00.0 UTC</initialValidateDate>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000540534</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>3600000287853</customerId>
      <insuredId>3600000344305</insuredId>
      <policyCode>990046906656</policyCode>
      <designation>00</designation>
      <listId>3600000493952</listId>
      <legalBene>0</legalBene>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>穆桐雨</customerName>
      <customerBirthday>2016-12-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>610481201612073011</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>3600000540534</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>00831000</productCode>
      <customerId>3600000287853</customerId>
      <insuredId>3600000344305</insuredId>
      <policyCode>990046906656</policyCode>
      <designation>00</designation>
      <listId>3600000493953</listId>
      <legalBene>0</legalBene>
      <busiItemId>3600000494248</busiItemId>
      <policyId>*************</policyId>
      <shareRate>1</shareRate>
      <customerName>穆桐雨</customerName>
      <customerBirthday>2016-12-06 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>610481201612073011</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>3600000344305</insuredId>
      <orderId>1</orderId>
      <policyCode>990046906656</policyCode>
      <listId>3600000487747</listId>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>00831000</productCode>
      <insuredId>3600000344305</insuredId>
      <orderId>1</orderId>
      <policyCode>990046906656</policyCode>
      <listId>3600000487748</listId>
      <busiItemId>3600000494248</busiItemId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>1</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>5</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>100000</amount>
      <paidupDate>2029-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>3023-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046906656</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <productId>181018</productId>
      <productCode>857000</productCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>W</coveragePeriod>
      <itemId>3600000457503</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>3023-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>*************</busiItemId>
      <unit>0</unit>
      <coverageYear>999</coverageYear>
      <renewalDiscntedPremAf>6780</renewalDiscntedPremAf>
      <totalPremAf>6780</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>6780</stdPremAf>
      <chargePeriod>2</chargePeriod>
      <premFreq>5</premFreq>
      <initialDiscntPremAf>6780</initialDiscntPremAf>
      <initialAmount>100000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>*************</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>6000</amount>
      <paidupDate>2024-12-31 16:00:00.0 UTC</paidupDate>
      <expiryDate>2025-12-31 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>990046906656</policyCode>
      <countWay>1</countWay>
      <validateDate>2024-12-31 16:00:00.0 UTC</validateDate>
      <productId>180952</productId>
      <productCode>831000</productCode>
      <applyDate>2024-12-30 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>3600000457504</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2025-12-31 16:00:00.0 UTC</maturityDate>
      <busiItemId>3600000494248</busiItemId>
      <unit>0</unit>
      <coverageYear>1</coverageYear>
      <renewalDiscntedPremAf>641</renewalDiscntedPremAf>
      <totalPremAf>641</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>641</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>641</initialDiscntPremAf>
      <initialAmount>6000</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>2025-12-31 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000457503</itemId>
      <organCode>********</organCode>
      <policyCode>990046906656</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000474794</listId>
      <payDueDate>2025-12-31 16:00:00.0 UTC</payDueDate>
      <busiItemId>*************</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>6780</nextPrem>
      <premStatus>1</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>3600000457504</itemId>
      <organCode>********</organCode>
      <policyCode>990046906656</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>3600000474795</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>3600000494248</busiItemId>
      <policyId>*************</policyId>
      <nextPrem>641</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>3600000540516</addressId>
      <customerHeight>177</customerHeight>
      <customerId>3600000287842</customerId>
      <jobCode>A2601001</jobCode>
      <customerWeight>77</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990046906656</policyCode>
      <listId>3600000576425</listId>
      <policyId>*************</policyId>
      <annualIncomeCeil>999</annualIncomeCeil>
      <incomeSource>1</incomeSource>
      <residentType>1</residentType>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>岳窝屿</agentName>
      <agentStartDate>2024-12-31 16:00:00.0 UTC</agentStartDate>
      <agentMobile>15156614603</agentMobile>
      <agentType>1</agentType>
      <applyCode>**************</applyCode>
      <policyCode>990046906656</policyCode>
      <listId>3600000576690</listId>
      <agentOrganCode>360000151222</agentOrganCode>
      <policyId>*************</policyId>
      <agentCode>08210413</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>3600000540534</addressId>
      <standLife>1</standLife>
      <customerId>3600000287853</customerId>
      <customerHeight>122</customerHeight>
      <jobCode>********</jobCode>
      <relationToPh>03</relationToPh>
      <customerWeight>45</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>990046906656</policyCode>
      <insuredAge>8</insuredAge>
      <listId>3600000344305</listId>
      <policyId>*************</policyId>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductOtherVO>
      <productId>180952</productId>
      <busiPrdId>181438</busiPrdId>
      <itemId>3600000457504</itemId>
      <field20>8</field20>
      <applyCode>**************</applyCode>
      <busiItemId>3600000494248</busiItemId>
      <policyId>*************</policyId>
      <policyCode>990046906656</policyCode>
      <field1>1</field1>
    </com.nci.tunan.pa.interfaces.vo.ContractProductOtherVO>
  </contractProductOtherVOList>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-8-26 09:55:49 994|pool-28-thread-2|INFO|理赔抄单共计耗时：889|QueryPolicyUCCImpl.java|194|
