2025-3-27 15:15:40 040|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：fundAmount49|QueryPolicyServiceImpl.java|609|
2025-3-27 15:15:41 422|http-bio-9090-exec-2|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>520865</policyId>
      <derivation>1</derivation>
      <inputDate>2021-05-19 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>2031-05-20 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>************</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2021-05-20 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2021-05-19 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2021-05-20 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2021-05-19 16:00:00.0 UTC</submissionDate>
      <issueDate>2021-05-19 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2021-05-19 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>6</policyFlag>
      <callTimeList>, , , </callTimeList>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181465</busiPrdId>
      <applyDate>2021-05-19 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>2031-05-20 16:00:00.0 UTC</maturityDate>
      <busiItemId>211180</busiItemId>
      <policyId>520865</policyId>
      <waiver>0</waiver>
      <issueDate>2021-05-19 16:00:00.0 UTC</issueDate>
      <paidupDate>2021-05-20 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2031-05-20 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>************</policyCode>
      <validateDate>2021-05-20 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2021-05-20 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>618934</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>166228</customerId>
      <insuredId>173222</insuredId>
      <policyCode>************</policyCode>
      <designation>00</designation>
      <listId>202079</listId>
      <legalBene>0</legalBene>
      <busiItemId>211180</busiItemId>
      <policyId>520865</policyId>
      <shareRate>1</shareRate>
      <customerName>李哈</customerName>
      <customerBirthday>1990-03-31 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199004010010</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>173222</insuredId>
      <orderId>1</orderId>
      <policyCode>************</policyCode>
      <listId>200694</listId>
      <busiItemId>211180</busiItemId>
      <policyId>520865</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>520865</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>1565</amount>
      <paidupDate>2021-05-20 16:00:00.0 UTC</paidupDate>
      <expiryDate>2031-05-20 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>************</policyCode>
      <countWay>2</countWay>
      <validateDate>2021-05-20 16:00:00.0 UTC</validateDate>
      <productId>180979</productId>
      <productCode>442000</productCode>
      <applyDate>2021-05-19 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>210211</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2031-05-20 16:00:00.0 UTC</maturityDate>
      <busiItemId>211180</busiItemId>
      <unit>0</unit>
      <coverageYear>10</coverageYear>
      <renewalDiscntedPremAf>10000</renewalDiscntedPremAf>
      <totalPremAf>10000</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>10000</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>10000</initialDiscntPremAf>
      <initialAmount>1565</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>210211</itemId>
      <organCode>********</organCode>
      <policyCode>************</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>206301</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>211180</busiItemId>
      <policyId>520865</policyId>
      <nextPrem>10000</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>653979</addressId>
      <customerHeight>170</customerHeight>
      <customerId>166228</customerId>
      <jobCode>Z006002</jobCode>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>************</policyCode>
      <listId>924540</listId>
      <policyId>520865</policyId>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2021-05-20 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>************</policyCode>
      <listId>538285</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>520865</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>653979</addressId>
      <standLife>1</standLife>
      <customerId>166228</customerId>
      <customerHeight>170</customerHeight>
      <jobCode>Z006002</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>************</policyCode>
      <insuredAge>31</insuredAge>
      <listId>173222</listId>
      <policyId>520865</policyId>
      <sociSecu>0</sociSecu>
      <customerName>李哈</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-3-27 15:15:41 423|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：2674|QueryPolicyUCCImpl.java|194|
2025-3-27 15:15:41 628|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：fundAmount12|QueryPolicyServiceImpl.java|609|
2025-3-27 15:15:41 644|http-bio-9090-exec-2|INFO|理赔抄单返回结果：<com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>
  <contractMasterVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
      <mediaType>1</mediaType>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <channelType>01</channelType>
      <policyId>520867</policyId>
      <derivation>1</derivation>
      <inputDate>2021-05-19 16:00:00.0 UTC</inputDate>
      <policyType>1</policyType>
      <expiryDate>2031-05-20 16:00:00.0 UTC</expiryDate>
      <submitChannel>4</submitChannel>
      <liabilityState>1</liabilityState>
      <policyCode>************</policyCode>
      <branchCode>8647</branchCode>
      <validateDate>2021-05-20 16:00:00.0 UTC</validateDate>
      <moneyCode>CNY</moneyCode>
      <applyDate>2021-05-19 16:00:00.0 UTC</applyDate>
      <initialValidateDate>2021-05-20 16:00:00.0 UTC</initialValidateDate>
      <submissionDate>2021-05-19 16:00:00.0 UTC</submissionDate>
      <issueDate>2021-05-19 16:00:00.0 UTC</issueDate>
      <decisionCode>10</decisionCode>
      <agentOrgId>********</agentOrgId>
      <initialPremDate>2021-05-19 16:00:00.0 UTC</initialPremDate>
      <langCode>211</langCode>
      <bankAgencyFlag>0</bankAgencyFlag>
      <policyFlag>6</policyFlag>
      <callTimeList>, , , </callTimeList>
      <isSelfInsured>0</isSelfInsured>
    </com.nci.tunan.pa.interfaces.vo.ContractMasterVO>
  </contractMasterVOList>
  <contractBusiProdVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
      <aplPermit>0</aplPermit>
      <busiPrdId>181465</busiPrdId>
      <applyDate>2021-05-19 16:00:00.0 UTC</applyDate>
      <busiProdCode>********</busiProdCode>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <renew>0</renew>
      <maturityDate>2031-05-20 16:00:00.0 UTC</maturityDate>
      <busiItemId>211183</busiItemId>
      <policyId>520867</policyId>
      <waiver>0</waiver>
      <issueDate>2021-05-19 16:00:00.0 UTC</issueDate>
      <paidupDate>2021-05-20 16:00:00.0 UTC</paidupDate>
      <decisionCode>10</decisionCode>
      <expiryDate>2031-05-20 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>************</policyCode>
      <validateDate>2021-05-20 16:00:00.0 UTC</validateDate>
      <renewalState>0</renewalState>
      <initialValidateDate>2021-05-20 16:00:00.0 UTC</initialValidateDate>
      <hesitationPeriodDay>15</hesitationPeriodDay>
    </com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO>
  </contractBusiProdVOList>
  <contractBeneVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
      <addressId>618941</addressId>
      <shareOrder>1</shareOrder>
      <beneType>0</beneType>
      <productCode>********</productCode>
      <customerId>166228</customerId>
      <insuredId>173224</insuredId>
      <policyCode>************</policyCode>
      <designation>00</designation>
      <listId>202082</listId>
      <legalBene>0</legalBene>
      <busiItemId>211183</busiItemId>
      <policyId>520867</policyId>
      <shareRate>1</shareRate>
      <customerName>李哈</customerName>
      <customerBirthday>1990-03-31 16:00:00.0 UTC</customerBirthday>
      <customerGender>1</customerGender>
      <customerCertType>0</customerCertType>
      <customerCertiCode>110101199004010010</customerCertiCode>
    </com.nci.tunan.pa.interfaces.vo.ContractBeneVO>
  </contractBeneVOList>
  <benefitInsuredVOList>
    <com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
      <relationToInsured1>00</relationToInsured1>
      <productCode>********</productCode>
      <insuredId>173224</insuredId>
      <orderId>1</orderId>
      <policyCode>************</policyCode>
      <listId>200697</listId>
      <busiItemId>211183</busiItemId>
      <policyId>520867</policyId>
    </com.nci.tunan.pa.interfaces.vo.BenefitInsuredVO>
  </benefitInsuredVOList>
  <contractProductVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractProductVO>
      <isWaived>0</isWaived>
      <applyCode>**************</applyCode>
      <organCode>********</organCode>
      <chargeYear>1</chargeYear>
      <extraPremAf>0</extraPremAf>
      <policyId>520867</policyId>
      <initialExtraPremAf>0</initialExtraPremAf>
      <amount>1565</amount>
      <paidupDate>2021-05-20 16:00:00.0 UTC</paidupDate>
      <expiryDate>2031-05-20 16:00:00.0 UTC</expiryDate>
      <liabilityState>1</liabilityState>
      <policyCode>************</policyCode>
      <countWay>2</countWay>
      <validateDate>2021-05-20 16:00:00.0 UTC</validateDate>
      <productId>180979</productId>
      <productCode>442000</productCode>
      <applyDate>2021-05-19 16:00:00.0 UTC</applyDate>
      <coveragePeriod>Y</coveragePeriod>
      <itemId>210214</itemId>
      <renewalExtraPremAf>0</renewalExtraPremAf>
      <maturityDate>2031-05-20 16:00:00.0 UTC</maturityDate>
      <busiItemId>211183</busiItemId>
      <unit>0</unit>
      <coverageYear>10</coverageYear>
      <renewalDiscntedPremAf>10000</renewalDiscntedPremAf>
      <totalPremAf>10000</totalPremAf>
      <decisionCode>10</decisionCode>
      <stdPremAf>10000</stdPremAf>
      <chargePeriod>1</chargePeriod>
      <premFreq>1</premFreq>
      <initialDiscntPremAf>10000</initialDiscntPremAf>
      <initialAmount>1565</initialAmount>
      <isMasterItem>1</isMasterItem>
    </com.nci.tunan.pa.interfaces.vo.ContractProductVO>
  </contractProductVOList>
  <contractExtendVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
      <policyYear>1</policyYear>
      <extractionDueDate>9999-12-30 16:00:00.0 UTC</extractionDueDate>
      <itemId>210214</itemId>
      <organCode>********</organCode>
      <policyCode>************</policyCode>
      <policyPeriod>1</policyPeriod>
      <listId>206304</listId>
      <payDueDate>9999-12-30 16:00:00.0 UTC</payDueDate>
      <busiItemId>211183</busiItemId>
      <policyId>520867</policyId>
      <nextPrem>10000</nextPrem>
      <premStatus>2</premStatus>
    </com.nci.tunan.pa.interfaces.vo.ContractExtendVO>
  </contractExtendVOList>
  <policyHolderVOList>
    <com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
      <addressId>618940</addressId>
      <customerHeight>170</customerHeight>
      <customerId>166228</customerId>
      <jobCode>Z006002</jobCode>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>************</policyCode>
      <listId>924542</listId>
      <policyId>520867</policyId>
    </com.nci.tunan.pa.interfaces.vo.PolicyHolderVO>
  </policyHolderVOList>
  <contractAgentVOList>
    <com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
      <agentName>胡廿想</agentName>
      <agentStartDate>2021-05-20 16:00:00.0 UTC</agentStartDate>
      <agentMobile>13361227615</agentMobile>
      <agentType>2</agentType>
      <applyCode>**************</applyCode>
      <policyCode>************</policyCode>
      <listId>538287</listId>
      <agentOrganCode>360000148465</agentOrganCode>
      <policyId>520867</policyId>
      <agentCode>18200208</agentCode>
      <isNbAgent>1</isNbAgent>
      <isCurrentAgent>1</isCurrentAgent>
    </com.nci.tunan.pa.interfaces.vo.ContractAgentVO>
  </contractAgentVOList>
  <insuredListVOList>
    <com.nci.tunan.pa.interfaces.vo.InsuredListVO>
      <addressId>618941</addressId>
      <standLife>1</standLife>
      <customerId>166228</customerId>
      <customerHeight>170</customerHeight>
      <jobCode>Z006002</jobCode>
      <relationToPh>00</relationToPh>
      <customerWeight>60</customerWeight>
      <applyCode>**************</applyCode>
      <policyCode>************</policyCode>
      <insuredAge>31</insuredAge>
      <listId>173224</listId>
      <policyId>520867</policyId>
      <sociSecu>0</sociSecu>
      <customerName>李哈</customerName>
    </com.nci.tunan.pa.interfaces.vo.InsuredListVO>
  </insuredListVOList>
  <contractProductOtherVOList/>
</com.nci.tunan.pa.interfaces.serviceData.querypolicy.QueryPolicyResData>|QueryPolicyUCCImpl.java|192|
2025-3-27 15:15:41 644|http-bio-9090-exec-2|INFO|理赔抄单共计耗时：146|QueryPolicyUCCImpl.java|194|
