2024-10-28 13:48:59|main|DEBUG||||| 消息id：253159a9-4bbf-40aa-9f0c-dc08b4365306-技术报文头：{"msgId":"253159a9-4bbf-40aa-9f0c-dc08b4365306","msgDate":"2024-10-28","msgTime":"13:48:38","servCd":null,"sysCd":null,"bizId":null,"bizType":null,"orgCd":null,"resCd":null,"resText":null,"bizResCd":null,"bizResText":null,"ver":null}|Test.java|70|
2024-10-28 13:49:01|main|DEBUG||||| 消息id：253159a9-4bbf-40aa-9f0c-dc08b4365306-技术报文体：{"bizHeader":{"userCode":null,"roleId":null,"manageCode":null,"sysCode":null,"mouldCode":null,"buttonCode":null,"tranCode":null,"transSerialno":null,"userName":null,"manageCom":null,"recordLog":null,"logDetail":null,"ipStr":null,"pageId":null,"pageId4Help":null,"inputId":null,"keyCode":null},"bizBody":{"inputData":{"CustomerId":["java.util.ArrayList",[0]],"customerId":["java.util.ArrayList",[0]]}}}|Test.java|72|
2024-10-28 13:49:13|main|DEBUG|||||hessian调用业务接口异常|Test.java|112|
2024-10-28 13:52:25|main|DEBUG||||| 消息id：da30cfc7-973a-4d4d-8067-0814fa33f62a-技术报文头：{"msgId":"da30cfc7-973a-4d4d-8067-0814fa33f62a","msgDate":"2024-10-28","msgTime":"13:52:11","servCd":null,"sysCd":null,"bizId":null,"bizType":null,"orgCd":null,"resCd":null,"resText":null,"bizResCd":null,"bizResText":null,"ver":null}|Test.java|72|
2024-10-28 13:52:28|main|DEBUG||||| 消息id：da30cfc7-973a-4d4d-8067-0814fa33f62a-技术报文体：{"bizHeader":{"userCode":null,"roleId":null,"manageCode":null,"sysCode":null,"mouldCode":null,"buttonCode":null,"tranCode":null,"transSerialno":null,"userName":null,"manageCom":null,"recordLog":null,"logDetail":null,"ipStr":null,"pageId":null,"pageId4Help":null,"inputId":null,"keyCode":null},"bizBody":{"inputData":{"CustomerId":["java.util.ArrayList",[0]],"customerId":["java.util.ArrayList",[0]]}}}|Test.java|74|
2024-10-28 13:54:17|main|DEBUG||||| hessian返回结果的消息id：da30cfc7-973a-4d4d-8067-0814fa33f62a-技术报文头：{"msgId":"da30cfc7-973a-4d4d-8067-0814fa33f62a","msgDate":"2024-10-28","msgTime":"13:52:11","servCd":null,"sysCd":null,"bizId":null,"bizType":null,"orgCd":null,"resCd":null,"resText":null,"bizResCd":"0","bizResText":null,"ver":null}|Test.java|105|
2024-10-28 13:54:19|main|DEBUG||||| hessian返回结果的消息id：da30cfc7-973a-4d4d-8067-0814fa33f62a-技术报文体：{"bizHeader":{"userCode":null,"roleId":null,"manageCode":null,"sysCode":null,"mouldCode":null,"buttonCode":null,"tranCode":null,"transSerialno":null,"userName":null,"manageCom":null,"recordLog":null,"logDetail":null,"ipStr":null,"pageId":null,"pageId4Help":null,"inputId":null,"keyCode":null},"bizBody":{"outputData":{"CustomerList":["java.util.ArrayList",[{"CustomerId":0,"HasDeathClaim":"1","customerId":0,"hasDeathClaim":"1"}]],"customerList":["java.util.ArrayList",[{"CustomerId":0,"HasDeathClaim":"1","customerId":0,"hasDeathClaim":"1"}]]}}}|Test.java|108|
2024-10-28 13:54:21|main|DEBUG|||||hessian调用接口成功|Test.java|111|
2024-10-28 15:08:38|main|DEBUG||||| 消息id：f280c471-353a-4c6e-ac4d-ce6efdbf7cd0-技术报文头：{"msgId":"f280c471-353a-4c6e-ac4d-ce6efdbf7cd0","msgDate":"2024-10-28","msgTime":"15:08:38","servCd":null,"sysCd":null,"bizId":null,"bizType":null,"orgCd":null,"resCd":null,"resText":null,"bizResCd":null,"bizResText":null,"ver":null}|Test.java|72|
2024-10-28 15:08:38|main|DEBUG||||| 消息id：f280c471-353a-4c6e-ac4d-ce6efdbf7cd0-技术报文体：{"bizHeader":{"userCode":null,"roleId":null,"manageCode":null,"sysCode":null,"mouldCode":null,"buttonCode":null,"tranCode":null,"transSerialno":null,"userName":null,"manageCom":null,"recordLog":null,"logDetail":null,"ipStr":null,"pageId":null,"pageId4Help":null,"inputId":null,"keyCode":null},"bizBody":{"inputData":{"CustomerId":["java.util.ArrayList",[3600000167909]],"customerId":["java.util.ArrayList",[3600000167909]]}}}|Test.java|74|
2024-10-28 15:10:37|main|DEBUG||||| hessian返回结果的消息id：f280c471-353a-4c6e-ac4d-ce6efdbf7cd0-技术报文头：{"msgId":"f280c471-353a-4c6e-ac4d-ce6efdbf7cd0","msgDate":"2024-10-28","msgTime":"15:08:38","servCd":null,"sysCd":null,"bizId":null,"bizType":null,"orgCd":null,"resCd":null,"resText":null,"bizResCd":"0","bizResText":null,"ver":null}|Test.java|105|
2024-10-28 15:10:37|main|DEBUG||||| hessian返回结果的消息id：f280c471-353a-4c6e-ac4d-ce6efdbf7cd0-技术报文体：{"bizHeader":{"userCode":null,"roleId":null,"manageCode":null,"sysCode":null,"mouldCode":null,"buttonCode":null,"tranCode":null,"transSerialno":null,"userName":null,"manageCom":null,"recordLog":null,"logDetail":null,"ipStr":null,"pageId":null,"pageId4Help":null,"inputId":null,"keyCode":null},"bizBody":{"outputData":{"CustomerList":["java.util.ArrayList",[{"CustomerId":3600000167909,"HasDeathClaim":"1","hasDeathClaim":"1","customerId":3600000167909}]],"customerList":["java.util.ArrayList",[{"CustomerId":3600000167909,"HasDeathClaim":"1","hasDeathClaim":"1","customerId":3600000167909}]]}}}|Test.java|108|
2024-10-28 15:10:37|main|DEBUG|||||hessian调用接口成功|Test.java|111|
2024-10-28 15:12:38|main|DEBUG||||| 消息id：3e03cb27-74b0-4fb3-a997-6fd90e9fbcd7-技术报文头：{"msgId":"3e03cb27-74b0-4fb3-a997-6fd90e9fbcd7","msgDate":"2024-10-28","msgTime":"15:12:38","servCd":null,"sysCd":null,"bizId":null,"bizType":null,"orgCd":null,"resCd":null,"resText":null,"bizResCd":null,"bizResText":null,"ver":null}|Test.java|73|
2024-10-28 15:12:38|main|DEBUG||||| 消息id：3e03cb27-74b0-4fb3-a997-6fd90e9fbcd7-技术报文体：{"bizHeader":{"userCode":null,"roleId":null,"manageCode":null,"sysCode":null,"mouldCode":null,"buttonCode":null,"tranCode":null,"transSerialno":null,"userName":null,"manageCom":null,"recordLog":null,"logDetail":null,"ipStr":null,"pageId":null,"pageId4Help":null,"inputId":null,"keyCode":null},"bizBody":{"inputData":{"CustomerId":["java.util.ArrayList",[3600000167909,6000000297787]],"customerId":["java.util.ArrayList",[3600000167909,6000000297787]]}}}|Test.java|75|
2024-10-28 15:13:16|main|DEBUG||||| hessian返回结果的消息id：3e03cb27-74b0-4fb3-a997-6fd90e9fbcd7-技术报文头：{"msgId":"3e03cb27-74b0-4fb3-a997-6fd90e9fbcd7","msgDate":"2024-10-28","msgTime":"15:12:38","servCd":null,"sysCd":null,"bizId":null,"bizType":null,"orgCd":null,"resCd":null,"resText":null,"bizResCd":"0","bizResText":null,"ver":null}|Test.java|106|
2024-10-28 15:13:16|main|DEBUG||||| hessian返回结果的消息id：3e03cb27-74b0-4fb3-a997-6fd90e9fbcd7-技术报文体：{"bizHeader":{"userCode":null,"roleId":null,"manageCode":null,"sysCode":null,"mouldCode":null,"buttonCode":null,"tranCode":null,"transSerialno":null,"userName":null,"manageCom":null,"recordLog":null,"logDetail":null,"ipStr":null,"pageId":null,"pageId4Help":null,"inputId":null,"keyCode":null},"bizBody":{"outputData":{"CustomerList":["java.util.ArrayList",[{"CustomerId":3600000167909,"HasDeathClaim":"1","customerId":3600000167909,"hasDeathClaim":"1"},{"CustomerId":6000000297787,"HasDeathClaim":"0","customerId":6000000297787,"hasDeathClaim":"0"}]],"customerList":["java.util.ArrayList",[{"CustomerId":3600000167909,"HasDeathClaim":"1","customerId":3600000167909,"hasDeathClaim":"1"},{"CustomerId":6000000297787,"HasDeathClaim":"0","customerId":6000000297787,"hasDeathClaim":"0"}]]}}}|Test.java|109|
2024-10-28 15:13:16|main|DEBUG|||||hessian调用接口成功|Test.java|112|
2024-10-28 16:36:31|main|DEBUG||||| 消息id：6f059340-44cf-4ac5-b437-90dd0fb40d3c-技术报文头：{"msgId":"6f059340-44cf-4ac5-b437-90dd0fb40d3c","msgDate":"2024-10-28","msgTime":"16:36:03","servCd":null,"sysCd":null,"bizId":null,"bizType":null,"orgCd":null,"resCd":null,"resText":null,"bizResCd":null,"bizResText":null,"ver":null}|Test.java|73|
2024-10-28 16:36:31|main|DEBUG||||| 消息id：6f059340-44cf-4ac5-b437-90dd0fb40d3c-技术报文体：{"bizHeader":{"userCode":null,"roleId":null,"manageCode":null,"sysCode":null,"mouldCode":null,"buttonCode":null,"tranCode":null,"transSerialno":null,"userName":null,"manageCom":null,"recordLog":null,"logDetail":null,"ipStr":null,"pageId":null,"pageId4Help":null,"inputId":null,"keyCode":null},"bizBody":{"inputData":{"CustomerId":["java.util.ArrayList",[3600000167909,3600000140938]],"customerId":["java.util.ArrayList",[3600000167909,3600000140938]]}}}|Test.java|75|
2024-10-28 16:46:43|main|DEBUG||||| hessian返回结果的消息id：6f059340-44cf-4ac5-b437-90dd0fb40d3c-技术报文头：{"msgId":"6f059340-44cf-4ac5-b437-90dd0fb40d3c","msgDate":"2024-10-28","msgTime":"16:36:03","servCd":null,"sysCd":null,"bizId":null,"bizType":null,"orgCd":null,"resCd":null,"resText":null,"bizResCd":"0","bizResText":null,"ver":null}|Test.java|106|
2024-10-28 16:46:43|main|DEBUG||||| hessian返回结果的消息id：6f059340-44cf-4ac5-b437-90dd0fb40d3c-技术报文体：{"bizHeader":{"userCode":null,"roleId":null,"manageCode":null,"sysCode":null,"mouldCode":null,"buttonCode":null,"tranCode":null,"transSerialno":null,"userName":null,"manageCom":null,"recordLog":null,"logDetail":null,"ipStr":null,"pageId":null,"pageId4Help":null,"inputId":null,"keyCode":null},"bizBody":{"outputData":{"CustomerList":["java.util.ArrayList",[{"CustomerId":3600000167909,"HasDeathClaim":"1","hasDeathClaim":"1","customerId":3600000167909},{"CustomerId":3600000140938,"HasDeathClaim":"0","hasDeathClaim":"0","customerId":3600000140938}]],"customerList":["java.util.ArrayList",[{"CustomerId":3600000167909,"HasDeathClaim":"1","hasDeathClaim":"1","customerId":3600000167909},{"CustomerId":3600000140938,"HasDeathClaim":"0","hasDeathClaim":"0","customerId":3600000140938}]]}}}|Test.java|109|
2024-10-28 16:46:43|main|DEBUG|||||hessian调用接口成功|Test.java|112|
