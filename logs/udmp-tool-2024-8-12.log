2024-8-12 13:44:35|main|ERROR|||||DataBase init error, error info {}|MyBatisUtil.java|82|
java.sql.SQLRecoverableException: ORA-01033: ORACLE initialization or shutdown in progress

	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:440) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:389) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:382) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIoauthenticate.processError(T4CTTIoauthenticate.java:427) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:445) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:191) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIoauthenticate.doOSESSKEY(T4CTTIoauthenticate.java:390) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:363) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.PhysicalConnection.<init>(PhysicalConnection.java:536) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CConnection.<init>(T4CConnection.java:228) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:32) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:521) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at java.sql.DriverManager.getConnection(DriverManager.java:664) ~[na:1.8.0-262]
	at java.sql.DriverManager.getConnection(DriverManager.java:208) ~[na:1.8.0-262]
	at com.nci.udmp.tools.generate.mapper.MyBatisUtil.init(MyBatisUtil.java:79) [udmp-tools-0.8.0.jar:na]
	at com.nci.udmp.tools.generate.mapper.MyBatisUtil.initDbMetaData(MyBatisUtil.java:96) [udmp-tools-0.8.0.jar:na]
	at com.nci.udmp.tools.generate.mapper.MyBatisUtil.getColumnAndType(MyBatisUtil.java:677) [udmp-tools-0.8.0.jar:na]
	at com.nci.udmp.tools.generate.mapper.GenerateMapperBean.init(GenerateMapperBean.java:54) [udmp-tools-0.8.0.jar:na]
	at com.nci.udmp.tools.generate.mapper.AbstractGenerateBean.<init>(AbstractGenerateBean.java:44) [udmp-tools-0.8.0.jar:na]
	at com.nci.udmp.tools.generate.mapper.GenerateMapperBean.<init>(GenerateMapperBean.java:27) [udmp-tools-0.8.0.jar:na]
	at GenerateUtilTest.testCreateMapperFile(GenerateUtilTest.java:228) [test-classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0-262]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0-262]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0-262]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0-262]
	at junit.framework.TestCase.runTest(TestCase.java:168) [junit-4.10.jar:na]
	at junit.framework.TestCase.runBare(TestCase.java:134) [junit-4.10.jar:na]
	at junit.framework.TestResult$1.protect(TestResult.java:110) [junit-4.10.jar:na]
	at junit.framework.TestResult.runProtected(TestResult.java:128) [junit-4.10.jar:na]
	at junit.framework.TestResult.run(TestResult.java:113) [junit-4.10.jar:na]
	at junit.framework.TestCase.run(TestCase.java:124) [junit-4.10.jar:na]
	at junit.framework.TestSuite.runTest(TestSuite.java:243) [junit-4.10.jar:na]
	at junit.framework.TestSuite.run(TestSuite.java:238) [junit-4.10.jar:na]
	at org.junit.internal.runners.JUnit38ClassRunner.run(JUnit38ClassRunner.java:83) [junit-4.10.jar:na]
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:236) [surefire-junit4-2.12.jar:2.12]
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:134) [surefire-junit4-2.12.jar:2.12]
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:113) [surefire-junit4-2.12.jar:2.12]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0-262]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0-262]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0-262]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0-262]
	at org.apache.maven.surefire.util.ReflectionUtils.invokeMethodWithArray(ReflectionUtils.java:189) [surefire-api-2.12.jar:2.12]
	at org.apache.maven.surefire.booter.ProviderFactory$ProviderProxy.invoke(ProviderFactory.java:165) [surefire-booter-2.12.jar:2.12]
	at org.apache.maven.surefire.booter.ProviderFactory.invokeProvider(ProviderFactory.java:85) [surefire-booter-2.12.jar:2.12]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:103) [surefire-booter-2.12.jar:2.12]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:74) [surefire-booter-2.12.jar:2.12]
2024-8-12 13:44:35|main|ERROR|||||DataBase init error, error info {}|MyBatisUtil.java|82|
java.sql.SQLRecoverableException: ORA-01033: ORACLE initialization or shutdown in progress

	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:440) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:389) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIoer.processError(T4CTTIoer.java:382) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIoauthenticate.processError(T4CTTIoauthenticate.java:427) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:445) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:191) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CTTIoauthenticate.doOSESSKEY(T4CTTIoauthenticate.java:390) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:363) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.PhysicalConnection.<init>(PhysicalConnection.java:536) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CConnection.<init>(T4CConnection.java:228) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:32) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:521) ~[ojdbc6-11.2.0.2.0.jar:11.2.0.2.0]
	at java.sql.DriverManager.getConnection(DriverManager.java:664) ~[na:1.8.0-262]
	at java.sql.DriverManager.getConnection(DriverManager.java:208) ~[na:1.8.0-262]
	at com.nci.udmp.tools.generate.mapper.MyBatisUtil.init(MyBatisUtil.java:79) [udmp-tools-0.8.0.jar:na]
	at com.nci.udmp.tools.generate.mapper.MyBatisUtil.initDbMetaData(MyBatisUtil.java:96) [udmp-tools-0.8.0.jar:na]
	at com.nci.udmp.tools.generate.mapper.MyBatisUtil.getColumnAndType(MyBatisUtil.java:677) [udmp-tools-0.8.0.jar:na]
	at com.nci.udmp.tools.generate.mapper.GenerateMapperBean.init(GenerateMapperBean.java:54) [udmp-tools-0.8.0.jar:na]
	at com.nci.udmp.tools.generate.mapper.AbstractGenerateBean.<init>(AbstractGenerateBean.java:44) [udmp-tools-0.8.0.jar:na]
	at com.nci.udmp.tools.generate.mapper.GenerateMapperBean.<init>(GenerateMapperBean.java:27) [udmp-tools-0.8.0.jar:na]
	at GenerateUtilTest.createMapperFile(GenerateUtilTest.java:213) [test-classes/:na]
	at GenerateUtilTest.testCreateFile(GenerateUtilTest.java:119) [test-classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0-262]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0-262]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0-262]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0-262]
	at junit.framework.TestCase.runTest(TestCase.java:168) [junit-4.10.jar:na]
	at junit.framework.TestCase.runBare(TestCase.java:134) [junit-4.10.jar:na]
	at junit.framework.TestResult$1.protect(TestResult.java:110) [junit-4.10.jar:na]
	at junit.framework.TestResult.runProtected(TestResult.java:128) [junit-4.10.jar:na]
	at junit.framework.TestResult.run(TestResult.java:113) [junit-4.10.jar:na]
	at junit.framework.TestCase.run(TestCase.java:124) [junit-4.10.jar:na]
	at junit.framework.TestSuite.runTest(TestSuite.java:243) [junit-4.10.jar:na]
	at junit.framework.TestSuite.run(TestSuite.java:238) [junit-4.10.jar:na]
	at org.junit.internal.runners.JUnit38ClassRunner.run(JUnit38ClassRunner.java:83) [junit-4.10.jar:na]
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:236) [surefire-junit4-2.12.jar:2.12]
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:134) [surefire-junit4-2.12.jar:2.12]
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:113) [surefire-junit4-2.12.jar:2.12]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0-262]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0-262]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0-262]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0-262]
	at org.apache.maven.surefire.util.ReflectionUtils.invokeMethodWithArray(ReflectionUtils.java:189) [surefire-api-2.12.jar:2.12]
	at org.apache.maven.surefire.booter.ProviderFactory$ProviderProxy.invoke(ProviderFactory.java:165) [surefire-booter-2.12.jar:2.12]
	at org.apache.maven.surefire.booter.ProviderFactory.invokeProvider(ProviderFactory.java:85) [surefire-booter-2.12.jar:2.12]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:103) [surefire-booter-2.12.jar:2.12]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:74) [surefire-booter-2.12.jar:2.12]
