package com.nci.tunan.cap.interfaces.model.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @.belongToModule 收付费—POS收费
 * @date 2016-12-20 上午9:52:40
 * @description 智能POS应收查询服务接收请求接口
 */
public class PosArapQueryInputVO implements Serializable {

    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;

    /**
     * 服务商代码
     */
    private String servicerCode;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户代码
     */
    private String userCode;

    /**
     * 收费类型
     */
    private String chargeType;

    /**
     * 投保单号
     */
    private String proposalFormNo;

    /**
     * 单证号码
     */
    private String certifyNo;

    public String getServicerCode() {
        return servicerCode;
    }

    public void setServicerCode(String servicerCode) {
        this.servicerCode = servicerCode;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    public String getProposalFormNo() {
        return proposalFormNo;
    }

    public void setProposalFormNo(String proposalFormNo) {
        this.proposalFormNo = proposalFormNo;
    }

    public String getCertifyNo() {
        return certifyNo;
    }

    public void setCertifyNo(String certifyNo) {
        this.certifyNo = certifyNo;
    }

    @Override
    public String toString() {
        return "PosArapQueryInputVO [servicerCode=" + servicerCode
                + ", userType=" + userType + ", userCode=" + userCode
                + ", chargeType=" + chargeType + ", proposalFormNo="
                + proposalFormNo + ", certifyNo=" + certifyNo + "]";
    }

}
