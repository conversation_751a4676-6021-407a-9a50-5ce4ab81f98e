package com.nci.tunan.cap.interfaces.model.vo;

/**
 * @description 针对报文中循环体中字段，专设对象，以List形式放入InvoiceConfigInfoVO以供使用
 * @<NAME_EMAIL>
 * @date 2015-11-19 下午2:16:55
 * @.belongToModule 收付费-发票打印
 */
public class InvoiceConfigInfoDetailVO {

    /**
     * 主附险标示符"0"为主险，"1"为附加险
     */
    private String productCategory;

    /**
     * 险种保费
     */
    private String premium;

    /**
     * 险种代码
     */
    private String productNo;

    /**
     * 险种名称
     */
    private String productName;

    public String getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory;
    }

    public String getPremium() {
        return premium;
    }

    public void setPremium(String premium) {
        this.premium = premium;
    }

    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

}
