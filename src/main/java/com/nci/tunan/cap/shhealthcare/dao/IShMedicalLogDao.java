package com.nci.tunan.cap.shhealthcare.dao;

import com.nci.tunan.cap.shhealthcare.interfaces.model.po.ShMedicalLogPO;
import com.nci.udmp.framework.exception.app.BizException;

/**
 * 
 * @description 上海医保-日志记录Dao接口
 * @<NAME_EMAIL> 
 * @date 2021年7月16日 下午3:28:05 
 * @.belongToModule 收付费子系统-上海医保
 */
public interface IShMedicalLogDao {
	/**
	 * 
	 * @description 添加日志信息
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param shMedicalLogPO 日志PO对象
	 * @return
	 * @throws BizException
	 */
	public ShMedicalLogPO addShMedicalLog(ShMedicalLogPO shMedicalLogPO) throws BizException;
	/**
	 * 
	 * @description 根据医保流水、保单编码查询流水号等查询投保单号、流水号信息
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param shMedicalLogPO 日志PO对象
	 * @return
	 * @throws BizException
	 */
	public ShMedicalLogPO queryAppCodeOrPolForNo(ShMedicalLogPO shMedicalLogPO) throws BizException;
}
