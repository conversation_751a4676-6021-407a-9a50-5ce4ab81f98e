package com.nci.tunan.clm.impl.report.ucc.impl;

import org.slf4j.Logger;

import com.nci.tunan.clm.impl.report.service.IClaimCommonService;
import com.nci.tunan.clm.impl.report.ucc.IHospitalInfoQueryUcc;
import com.nci.tunan.clm.interfaces.model.bo.ClaimDrugDetailBO;
import com.nci.tunan.clm.interfaces.model.bo.HospitalBO;
import com.nci.tunan.clm.interfaces.model.bo.OperationBO;
import com.nci.tunan.clm.interfaces.model.bo.OperationLevelBO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimDrugDetailVO;
import com.nci.tunan.clm.interfaces.model.vo.HospitalVO;
import com.nci.tunan.clm.interfaces.model.vo.OperationLevelVO;
import com.nci.tunan.clm.interfaces.model.vo.OperationVO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.signature.IUCC;
import com.nci.udmp.util.bean.BeanUtils;

/**
 * 医院信息查询功能，UCC层的实现
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统 -- 医院信息
 * @date 2015年5月15日 下午2:30:31
 */
public class HospitalInfoQueryUccImpl implements IHospitalInfoQueryUcc, IUCC {
	
	/**
	 * 定义SERVICE查询医院信息
	 */
	private IClaimCommonService claimCommonService;	
//@invalid 	//@invalid 接收调用SERVICE组件返回来的值
//@invalid 	private List<HospitalBO> hospitalBOList=new ArrayList<HospitalBO>();
//@invalid 	//@invalid 接收数据转换回来的值，返回给ACTION
//@invalid 	private List<HospitalVO> hospitalVOList=new ArrayList<HospitalVO>();
//@invalid 	//@invalid 接收从SVO数据转换回来的值
//@invalid 	private HospitalBO hospitalBO=new HospitalBO();
//@invalid 	
//@invalid 	private OperationBO operationBO;
//@invalid 	//@invalid 循环体内的中间变量，作为媒介，仅供中间数据转换使用
//@invalid 	private HospitalVO hospitalVO;

	/**
     * 分页查询医院信息
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param hospitalBO 医院信息
     * @param currentPage 分页
     * @return CurrentPage<HospitalBO>  分页集合
     */
	@Override
	public CurrentPage<HospitalVO> showHospital(HospitalVO hospitalSVO, CurrentPage<HospitalVO> currentPage)
            throws BizException {
	    //分页查询医院信息
		HospitalBO hospitalBO = BeanUtils.copyProperties(HospitalBO.class,hospitalSVO);
		return BeanUtils.copyCurrentPage(HospitalVO.class, claimCommonService.showHospital(hospitalBO, BeanUtils.copyCurrentPage(HospitalBO.class, currentPage)));
	}
	
	/**
     * 查询分页信息、手术代码
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param operationBO 手术代码
    * @param currentPage 分页
    * @return CurrentPage<OperationBO> 分页集合
     */
	@Override
	public CurrentPage<OperationVO> queryOperation(OperationVO operationVO, CurrentPage<OperationVO> currentPage)
            throws BizException {
	    //查询分页信息、手术代码
		OperationBO operationBO = BeanUtils.copyProperties(OperationBO.class,operationVO);
		return BeanUtils.copyCurrentPage(OperationVO.class, claimCommonService.queryOperation(operationBO, BeanUtils.copyCurrentPage(OperationBO.class, currentPage)));
	}
	/**
     * 分页手术分级项目信息
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param operationLevelBO 手术项目对象
      * @param currentPage 分页
      * @return CurrentPage<OperationLevelBO> 分页集
     */
	@Override
	public CurrentPage<OperationLevelVO> queryOpsLevelItem(OperationLevelVO operationLevelVO, CurrentPage<OperationLevelVO> currentPage)
            throws BizException {
	    //分页手术分级项目信息
		OperationLevelBO operationLevelBO = BeanUtils.copyProperties(OperationLevelBO.class,operationLevelVO);
		return BeanUtils.copyCurrentPage(OperationLevelVO.class, claimCommonService.queryOpsLevelItem(operationLevelBO, BeanUtils.copyCurrentPage(OperationLevelBO.class, currentPage)));
	}
//@invalid 	public OperationBO getOperationBO() {
//@invalid 		return operationBO;
//@invalid 	}
//@invalid 
//@invalid 
//@invalid 
//@invalid 
//@invalid 
//@invalid 	public void setOperationBO(OperationBO operationBO) {
//@invalid 		this.operationBO = operationBO;
//@invalid 	}
//@invalid 
//@invalid 
//@invalid 
//@invalid 
//@invalid 
//@invalid 	public List<HospitalBO> getHospitalBOList() {
//@invalid         return hospitalBOList;
//@invalid     }
//@invalid     public void setHospitalBOList(List<HospitalBO> hospitalBOList) {
//@invalid         this.hospitalBOList = hospitalBOList;
//@invalid     }
//@invalid     public List<HospitalVO> getHospitalVOList() {
//@invalid         return hospitalVOList;
//@invalid     }
//@invalid     public void setHospitalVOList(List<HospitalVO> hospitalVOList) {
//@invalid         this.hospitalVOList = hospitalVOList;
//@invalid     }
//@invalid     public HospitalBO getHospitalBO() {
//@invalid         return hospitalBO;
//@invalid     }
//@invalid     public void setHospitalBO(HospitalBO hospitalBO) {
//@invalid         this.hospitalBO = hospitalBO;
//@invalid     }
//@invalid     public HospitalVO getHospitalVO() {
//@invalid         return hospitalVO;
//@invalid     }
//@invalid     public void setHospitalVO(HospitalVO hospitalVO) {
//@invalid         this.hospitalVO = hospitalVO;
//@invalid     }
	/**
	 * 日志信息
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @return  Logger 日志信息
	 */
    public static Logger getLogger() {
		return LOGGER;
	}
	public IClaimCommonService getClaimCommonService() {
		return claimCommonService;
	}
	public void setClaimCommonService(IClaimCommonService claimCommonService) {
		this.claimCommonService = claimCommonService;
	}

	
	/**
	 * @description 查询分页信息、药品明细
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimDrugDetailVO 药品信息
	 * @param currentPage 分页信息
	 * @return 分页数据
	 * @throws BizException 
	*/
	@Override
	public CurrentPage<ClaimDrugDetailVO> queryDrug(
			ClaimDrugDetailVO claimDrugDetailVO,
			CurrentPage<ClaimDrugDetailVO> currentPage) throws BizException {
		//查询分页信息、药品明细
		ClaimDrugDetailBO claimDrugDetailBO = BeanUtils.copyProperties(ClaimDrugDetailBO.class,claimDrugDetailVO);
		return BeanUtils.copyCurrentPage(ClaimDrugDetailVO.class, claimCommonService.queryDrug(claimDrugDetailBO, BeanUtils.copyCurrentPage(ClaimDrugDetailBO.class, currentPage)));
	}
	
}
