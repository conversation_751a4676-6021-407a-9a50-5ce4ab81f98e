package com.nci.tunan.clm.impl.peripheral.service.r00101001062.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.nci.core.common.impl.util.CodeMapperUtils;
import com.nci.tunan.clm.dao.IClaimForMobileDao;
import com.nci.tunan.clm.impl.peripheral.service.r00101001062.IQueryClaimHospitalInfoService;
import com.nci.tunan.clm.interfaces.model.po.HospitalPO;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001062.vo.InputData;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001062.vo.OutputData;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001062.vo.Result;
import com.nci.udmp.util.lang.StringUtilsEx;

/**
 * 
 * @description 理赔治疗医院信息查询
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/医院信息
 * @date  2015年5月15日 上午10:08:31
 */
public class QueryClaimHospitalInfoServiceImpl  implements IQueryClaimHospitalInfoService{
	/**
	 * 理赔Dao
	 */
	private IClaimForMobileDao iClaimForMobileDao;
	
	/**
	 * 
	 * @description 查询理赔治疗医院信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.peripheral.service.r00101001062.IQueryClaimHospitalInfoService#queryClaimHospitalInfo(com.nci.tunan.clm.interfaces.peripheral.exports.r00101001062.vo.InputData)
	 * @param inputVO 查询入参
	 * @return OutputData 返回医院信息出参
	 * @throws Exception
	 */
	public OutputData queryClaimHospitalInfo(InputData inputVO) throws Exception{
		//1.根据入参输入条件查询治疗医院的相关信息。
		OutputData outputData = new OutputData();
//	@invalid 	SysHeader sysHeader = CommonHeaderDeal.getSYSHEADERTHREAD();
		HospitalPO hospitalPO = new HospitalPO();
		//@invalid 医院代码
		hospitalPO.setHospitalCode(inputVO.getHospitalCode());
		//@invalid 医院名称
		hospitalPO.setHospitalName(inputVO.getHospitalName());
		//@invalid 医院等级
		hospitalPO.setHospitalLevel(CodeMapperUtils.getNewCodeByOldCode("HOSPITAL_LEVEL", inputVO.getHospitalGrade(), "CLM"));
		//@invalid 管理机构
		hospitalPO.setOrganCode(inputVO.getManagecomCode());
		//@invalid 根据条件查询结果
		List<Map<String, Object>> maplst = iClaimForMobileDao.findAllMapClaimHospitalInfo(hospitalPO);
//@invalid 		if(null == maplst || maplst.size() == 0 ){
//	@invalid 		sysHeader.setBizResCd("1");
//@invalid 			sysHeader.setBizResText("失败");
//@invalid 			return outputData;
//@invalid 		}
		List<Result> results = new ArrayList<Result>();
		Result result = null;
		//2.遍历查询结果并返回
		for(Map<String, Object> map : maplst){
			result = new Result();
			result.setHospitalCode(StringUtilsEx.nullToString(map.get("HOSPITAL_CODE")));
			result.setHospitalName(StringUtilsEx.nullToString(map.get("HOSPITAL_NAME")));
			result.setHospitalGrade(StringUtilsEx.nullToString(map.get("HOSPITAL_LEVEL")));
			if(StringUtilsEx.nullToString(map.get("IS_DESIGNATED")).equals("0")){
				result.setFlag("定点");
			}else if(StringUtilsEx.nullToString(map.get("IS_DESIGNATED")).equals("1")){
				result.setFlag("非定点");
			}
			if(StringUtilsEx.nullToString(map.get("IS_DEFORMITY")).equals("0")){
				result.setIdentifyFlag("有");
			}else if(StringUtilsEx.nullToString(map.get("IS_DEFORMITY")).equals("1")){
				result.setIdentifyFlag("无");
			}
			result.setHospitalState(StringUtilsEx.nullToString(map.get("HOSPITAL_STATUS")));
			result.setManagecomCode(StringUtilsEx.nullToString(map.get("ORGAN_CODE")));
			results.add(result);
		}
		outputData.setResult(results);
		return outputData;
		
	}
	public IClaimForMobileDao getiClaimForMobileDao() {
		return iClaimForMobileDao;
	}
	public void setiClaimForMobileDao(IClaimForMobileDao iClaimForMobileDao) {
		this.iClaimForMobileDao = iClaimForMobileDao;
	}
}
