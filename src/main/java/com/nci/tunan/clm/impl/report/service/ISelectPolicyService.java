package com.nci.tunan.clm.impl.report.service;


import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.udmp.framework.exception.app.BizException;

/**
 * 查询保单信息和赔案信息接口
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统 -- 查询保单信息和赔案信息
 * @date 2015年5月15日 下午2:30:31
 */
public interface ISelectPolicyService {

    /**
     * 根据caseId查询t_claim_case表信息
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param casebo 赔案信息
     * @return ClaimCaseBO 赔案信息
     * @throws BizException 异常
     */
	public ClaimCaseBO findByCaseId(ClaimCaseBO casebo)throws BizException;
	
	
	
	/**
	 * 根据caseId查询ClmContractMaster表查询是否有抄单数据
	 */
//	public ClmContractMasterPO findContractMasterByCaseId(ClmContractMasterPO po)throws BizException;
	
	/**
	 * 删除所有抄单数据
	 */
//	public void deleteAllCopyPolicy(ClmContractMasterPO tClmContractMasterPO);
	
	
	

}
