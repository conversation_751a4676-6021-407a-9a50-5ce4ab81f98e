package com.nci.tunan.clm.impl.register.service;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.bo.ClaimBillBO;
import com.nci.udmp.framework.model.CurrentPage;


 /** 
 * @description  账单信息
 * <AUTHOR> <EMAIL>
 * @date 2015年8月29日 下午5:50:14
 * @.belongToModule CLM-理赔系统 /账单信息
*/
public interface IClaimBillService {
 	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBO 对象
     * @return ClaimBillBO 添加结果
     */
 	public ClaimBillBO addClaimBill(ClaimBillBO claimBillBO);
 	
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBO 对象
     * @return ClaimBillBO 修改结果
     */
 	public ClaimBillBO updateClaimBill(ClaimBillBO claimBillBO);
 	
 	 /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBO 对象
     * @return boolean 删除是否成功
     */
 	public boolean deleteClaimBill(ClaimBillBO claimBillBO);
 	
 	 /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBO 对象
     * @return ClaimBillBO 查询结果对象
     */
 	public ClaimBillBO findClaimBill(ClaimBillBO claimBillBO);
 	
 	/**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBO 对象
     * @return List<ClaimBillBO> 查询结果List
     */
 	public List<ClaimBillBO> findAllClaimBill(ClaimBillBO claimBillBO);
 	
 	 /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBO 对象
     * @return int 查询结果条数
     */
 	public int findClaimBillTotal(ClaimBillBO claimBillBO);
 	
 	 /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBO 对象
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBillBO> 查询结果的当前页对象
     */
 	public CurrentPage<ClaimBillBO> queryClaimBillForPage(ClaimBillBO claimBillBO, CurrentPage<ClaimBillBO> currentPage);
 	
 	 /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBOList 对象列表
     * @return boolean 批量添加是否成功
     */
 	public boolean batchSaveClaimBill(List<ClaimBillBO> claimBillBOList);
 	
 	/**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBOList 对象列表
     * @return boolean 批量修改是否成功
     */
 	public boolean batchUpdateClaimBill(List<ClaimBillBO> claimBillBOList);
 	
 	/**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBOList 对象列表
     * @return boolean 批量删除是否成功
     */
 	public boolean batchDeleteClaimBill(List<ClaimBillBO> claimBillBOList);
 	
 	/**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillBO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
 	public List<Map<String, Object>> findAllMapClaimBill(ClaimBillBO claimBillBO);
 	
 }
  