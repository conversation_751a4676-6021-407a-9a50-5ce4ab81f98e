package com.nci.tunan.clm.impl.report.service;

import com.nci.bpm.service.process.clm.clmmain.create.bd.CreateCLMMainRequestVO;
import com.nci.bpm.service.process.clm.clmmain.submit.bd.SubmitCLMMainRequestVO;
import com.nci.udmp.component.serviceinvoke.message.ServiceParameter;
import com.nci.udmp.component.serviceinvoke.message.body.bd.SrvReqBizBody;
import com.nci.udmp.component.serviceinvoke.message.body.bd.SrvReqBody;
import com.nci.udmp.component.serviceinvoke.message.body.hd.SRVReqHead;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;

/**
 * 组织测试数据参数
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统 -- 组织测试数据参数
 * @date 2015年5月15日 下午2:30:31
 */
public final class ParaData {
	
	private ParaData() {
	}
	/**
	 * 当前方法使用创建参数的过程都是使用Service-Client中的公共部分类型进行创建
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL> 
	 * @param inputData 赋值耦合使用服务端stub对象
	 * @return ServiceParameter 赋值完毕的一个参数对象
	 */
	public static ServiceParameter creatParameterData(SubmitCLMMainRequestVO inputData) {
//1.  设置报文头
		ServiceParameter para = new ServiceParameter();
		SysHeader inSysHeader = new SysHeader();
		{
			inSysHeader.setBizId("");
			inSysHeader.setBizResCd("");
			inSysHeader.setBizResText("");
			inSysHeader.setBizType("");
			inSysHeader.setMsgDate("");
			inSysHeader.setMsgId("");
			inSysHeader.setMsgTime("");
			inSysHeader.setOrgCd("");
			inSysHeader.setResCd("");
			inSysHeader.setResText("");
			inSysHeader.setServCd("");
			inSysHeader.setSysCd("");
			inSysHeader.setVer("");
		}
		para.setSysHeader(inSysHeader);
		//@invalid @invalidcom.nci.udmp.component.serviceinvoke.message.body.bd.SrvReqBody 
		SrvReqBody srvReqBody = new SrvReqBody();
		para.setRequest(srvReqBody);
		//@invalid @invalidcom.nci.udmp.component.serviceinvoke.message.body.hd.
		SRVReqHead bizHeader = new SRVReqHead();
		{
			//@invalid @invalid业务报文头赋值
			bizHeader.setButtonCode("buttonCode");
			bizHeader.setInputId("inputId");
			bizHeader.setIpStr("ipStr");
			bizHeader.setKeyCode("keyCode");
			bizHeader.setLogDetail("logDetail");
			bizHeader.setManageCode("manageCode");
			bizHeader.setManageCom("manageCom");
			bizHeader.setMouldCode("mouldCode");
			bizHeader.setPageId("pageId");
			bizHeader.setPageId4Help("pageId4Help");
			bizHeader.setRecordLog("recordLog");
			bizHeader.setRoleId("roleId");
			bizHeader.setSysCode("sysCode");
			bizHeader.setTranCode("tranCode");
			bizHeader.setTransSerialno("transSerialno");
			bizHeader.setUserCode("userCode");
			bizHeader.setUserName("userName");
		}
		//@invalid @invalidcom.nci.udmp.component.serviceinvoke.message.body.bd.
		SrvReqBizBody bizBody = new SrvReqBizBody();
		
		bizBody.setInputData(inputData);
		//@invalid @invalid end  允许轻度耦合，调用时使用服务端的inputdata的bean类型
		srvReqBody.setBizBody(bizBody);
		srvReqBody.setBizHeader(bizHeader);
		return para;
	}
	
	/**
     * 当前方法使用创建参数的过程都是使用Service-Client中的公共部分类型进行创建
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL> 
     * @param inputData 赋值耦合使用服务端stub对象
     * @return 赋值完毕的一个参数对象
     */
	public static ServiceParameter creatParameterData(CreateCLMMainRequestVO inputData) {
	    //1. 设置业务报文头
		ServiceParameter para = new ServiceParameter();
		SysHeader inSysHeader = new SysHeader();
		{
			inSysHeader.setBizId("");
			inSysHeader.setBizResCd("");
			inSysHeader.setBizResText("");
			inSysHeader.setBizType("");
			inSysHeader.setMsgDate("");
			inSysHeader.setMsgId("");
			inSysHeader.setMsgTime("");
			inSysHeader.setOrgCd("");
			inSysHeader.setResCd("");
			inSysHeader.setResText("");
			inSysHeader.setServCd("");
			inSysHeader.setSysCd("");
			inSysHeader.setVer("");
		}
		para.setSysHeader(inSysHeader);
		//@invalid @invalidcom.nci.udmp.component.serviceinvoke.message.body.bd.SrvReqBody 
		SrvReqBody srvReqBody = new SrvReqBody();
		para.setRequest(srvReqBody);
		//@invalid @invalidcom.nci.udmp.component.serviceinvoke.message.body.hd.
		SRVReqHead bizHeader = new SRVReqHead();
		{
			//@invalid @invalid业务报文头赋值
			bizHeader.setButtonCode("buttonCode");
			bizHeader.setInputId("inputId");
			bizHeader.setIpStr("ipStr");
			bizHeader.setKeyCode("keyCode");
			bizHeader.setLogDetail("logDetail");
			bizHeader.setManageCode("manageCode");
			bizHeader.setManageCom("manageCom");
			bizHeader.setMouldCode("mouldCode");
			bizHeader.setPageId("pageId");
			bizHeader.setPageId4Help("pageId4Help");
			bizHeader.setRecordLog("recordLog");
			bizHeader.setRoleId("roleId");
			bizHeader.setSysCode("sysCode");
			bizHeader.setTranCode("tranCode");
			bizHeader.setTransSerialno("transSerialno");
			bizHeader.setUserCode("userCode");
			bizHeader.setUserName("userName");
		}
		//@invalid @invalidcom.nci.udmp.component.serviceinvoke.message.body.bd. 
		SrvReqBizBody bizBody = new SrvReqBizBody();
		
		bizBody.setInputData(inputData);
		//@invalid @invalid end  允许轻度耦合，调用时使用服务端的inputdata的bean类型
		srvReqBody.setBizBody(bizBody);
		srvReqBody.setBizHeader(bizHeader);
		return para;
	}
}
