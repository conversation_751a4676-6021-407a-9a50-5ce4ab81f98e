package com.nci.tunan.clm.impl.peripheral.service.r00101001051;

import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;

/**
 * 
 * @description 赔案号查询明细信息
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/明细信息
 * @date  2015年5月15日 上午10:08:31
 */
public interface ILLDetailQueryService {

	/**
	 * 
	 * @description 通过个人赔案号查询报案人基本信息、报案基本信息等信息。
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCasePo 主表PO入参
	 * @return ClaimCasePO 返回明细结果出参
	 */
	public ClaimCasePO queryDetail(ClaimCasePO claimCasePo);
	
}
