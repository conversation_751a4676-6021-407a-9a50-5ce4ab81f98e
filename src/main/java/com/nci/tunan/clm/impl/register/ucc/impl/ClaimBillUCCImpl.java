package com.nci.tunan.clm.impl.register.ucc.impl;


import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.clm.impl.register.service.IClaimBillService;
import com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC;
import com.nci.tunan.clm.interfaces.model.bo.ClaimBillBO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimBillVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.logging.LoggerFactory;

/** 
 * @description 医疗费用
 * <AUTHOR> <EMAIL>
 * @date 2015-05-15
 * @.belongToModule CLM-理赔系统 /立案医疗费用
*/
public class ClaimBillUCCImpl   implements IClaimBillUCC  {
    /** 
     * @Fields claimBillService : 医疗费用service 
     */
	private IClaimBillService claimBillService;
	
	/** 
	 * @Fields logger :  日志工具
 	 */
	private static Logger logger = LoggerFactory.getLogger();
	
	/**
     * @description SERVICE-getter方法
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @return IClaimBillService service接口对象
     */
	public IClaimBillService getClaimBillService() {
		return claimBillService;
	}
	
	/**
     * @description SERVICE-setter方法
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimBillService service对象
     */
	public void setClaimBillService(IClaimBillService claimBillService) {
		this.claimBillService = claimBillService;
	}
	
	/**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#addClaimBill(com.nci.tunan.clm.interfaces.model.vo.ClaimBillVO)
     * @param claimBillVO 对象
     * @return ClaimBillVO 添加结果
     */
	public ClaimBillVO addClaimBill(ClaimBillVO claimBillVO) {
		//增加医疗费用数据
		logger.debug("<======ClaimBillUCC--addClaimBill======>");
		ClaimBillBO claimBillBO = claimBillService.addClaimBill(BeanUtils.copyProperties(ClaimBillBO.class, claimBillVO));
		return BeanUtils.copyProperties(ClaimBillVO.class, claimBillBO);
	}
	
	/**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#updateClaimBill(com.nci.tunan.clm.interfaces.model.vo.ClaimBillVO)
     * @param claimBillVO 对象
     * @return ClaimBillVO 修改结果
     */
	public ClaimBillVO updateClaimBill(ClaimBillVO claimBillVO)  {
		//修改医疗费用数据
		logger.debug("<======ClaimBillUCC--updateClaimBill======>");
		ClaimBillBO claimBillBO = claimBillService.updateClaimBill(BeanUtils.copyProperties(ClaimBillBO.class, claimBillVO));
		return BeanUtils.copyProperties(ClaimBillVO.class, claimBillBO);
	}

	 /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#deleteClaimBill(com.nci.tunan.clm.interfaces.model.vo.ClaimBillVO)
     * @param claimBillVO 对象
     * @return boolean 删除是否成功
     */
	public boolean deleteClaimBill(ClaimBillVO claimBillVO)  {
		//删除医疗费用数据
		logger.debug("<======ClaimBillUCC--deleteClaimBill======>");
		return claimBillService.deleteClaimBill(BeanUtils.copyProperties(ClaimBillBO.class, claimBillVO));
	}
	
	/**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#findClaimBill(com.nci.tunan.clm.interfaces.model.vo.ClaimBillVO)
     * @param claimBillVO 对象
     * @return ClaimBillVO 查询结果对象
     */
	public ClaimBillVO findClaimBill(ClaimBillVO claimBillVO) {
		//查询单条医疗费用数据
		logger.debug("<======ClaimBillUCC--findClaimBill======>");
		ClaimBillBO claimBillBackBO = claimBillService.findClaimBill(BeanUtils.copyProperties(ClaimBillBO.class, claimBillVO));
		ClaimBillVO claimBillBackVO = BeanUtils.copyProperties(ClaimBillVO.class, claimBillBackBO);
		return claimBillBackVO;
	}  
	
	/**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#findAllClaimBill(com.nci.tunan.clm.interfaces.model.vo.ClaimBillVO)
     * @param claimBillVO 对象
     * @return List<ClaimBillVO> 查询结果List
     */
	public List<ClaimBillVO> findAllClaimBill(ClaimBillVO claimBillVO) {
		//查询所有医疗费用数据
		logger.debug("<======ClaimBillUCC--findAllClaimBill======>");
		ClaimBillBO claimBillBO = BeanUtils.copyProperties(ClaimBillBO.class, claimBillVO);
		return BeanUtils.copyList(ClaimBillVO.class, claimBillService.findAllClaimBill(claimBillBO));
	} 
	
	 /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#findClaimBillTotal(com.nci.tunan.clm.interfaces.model.vo.ClaimBillVO)
     * @param claimBillVO 对象
     * @return int 查询结果条数
     */
	public int findClaimBillTotal(ClaimBillVO claimBillVO) {
		//查询医疗费用数据条数
		logger.debug("<======ClaimBillUCC--findClaimBillTotal======>");
		ClaimBillBO claimBillBO = BeanUtils.copyProperties(ClaimBillBO.class, claimBillVO);
		return claimBillService.findClaimBillTotal(claimBillBO);
	}	
	
	 /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#queryClaimBillForPage(com.nci.tunan.clm.interfaces.model.vo.ClaimBillVO, com.nci.udmp.framework.model.CurrentPage)
     * @param claimBillVO 对象
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimBillVO> 查询结果的当前页对象
     */
	public CurrentPage<ClaimBillVO> queryClaimBillForPage(ClaimBillVO claimBillVO, CurrentPage<ClaimBillVO> currentPage) {
		logger.debug("<======ClaimBillUCC--queryClaimBillForPage======>");
		//分页查询医疗费用数据
		ClaimBillBO claimBillBO = BeanUtils.copyProperties(ClaimBillBO.class, claimBillVO);
		return BeanUtils.copyCurrentPage(ClaimBillVO.class, claimBillService.queryClaimBillForPage(claimBillBO, BeanUtils.copyCurrentPage(ClaimBillBO.class, currentPage)));
	}
	
	/**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#batchSaveClaimBill(java.util.List)
     * @param claimBillVOList 对象列表
     * @return boolean 批量添加是否成功
     */
	public boolean batchSaveClaimBill(List<ClaimBillVO> claimBillVOList) {
		//批量增加医疗费用数据
		logger.debug("<======ClaimBillUCC--batchSaveClaimBill======>");
		return claimBillService.batchSaveClaimBill(BeanUtils.copyList(ClaimBillBO.class, claimBillVOList));
	}
	
	/**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#batchUpdateClaimBill(java.util.List)
     * @param claimBillVOList 对象列表
     * @return boolean 批量修改是否成功
     */
	public boolean batchUpdateClaimBill(List<ClaimBillVO> claimBillVOList) {
		//批量修改医疗费用数据
		logger.debug("<======ClaimBillUCC--batchUpdateClaimBill======>");
		return claimBillService.batchUpdateClaimBill(BeanUtils.copyList(ClaimBillBO.class, claimBillVOList));
	}
	
	/**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#batchDeleteClaimBill(java.util.List)
     * @param claimBillVOList 对象列表
     * @return boolean 批量删除是否成功
     */
	public boolean batchDeleteClaimBill(List<ClaimBillVO> claimBillVOList) {
		//批量删除医疗费用数据
		logger.debug("<======ClaimBillUCC--batchDeleteClaimBill======>");
		return claimBillService.batchDeleteClaimBill(BeanUtils.copyList(ClaimBillBO.class, claimBillVOList));
	}
	
	/**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.ucc.IClaimBillUCC#findAllMapClaimBill(com.nci.tunan.clm.interfaces.model.vo.ClaimBillVO)
     * @param claimBillVO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	public List<Map<String, Object>> findAllMapClaimBill(ClaimBillVO claimBillVO) {
		//查询所有医疗费用数据 ，重新组装为map
		logger.debug("<======ClaimBillUCC--findAllMapClaimBill======>");
		ClaimBillBO claimBillBO = BeanUtils.copyProperties(ClaimBillBO.class, claimBillVO);
		return claimBillService.findAllMapClaimBill(claimBillBO);
	}
	
}
