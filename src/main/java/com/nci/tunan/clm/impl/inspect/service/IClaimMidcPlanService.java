package com.nci.tunan.clm.impl.inspect.service;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.bo.ClaimMidcPlanBO;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * 理赔事中质检计划service接口
 * @description 
 * <AUTHOR> <EMAIL>
 * @.belongToModule CLM-理赔系统 质检
 * @date 2015-05-15 下午3:53:34
 */
 public interface IClaimMidcPlanService {

	 /**
	  * 增加数据
	  * @description
	  * @version V1.0.0
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param claimMidcPlanBO 理赔事中质检计划bo
	  * @return ClaimMidcPlanBO 理赔事中质检计划bo
	  */
 	public ClaimMidcPlanBO addClaimMidcPlan(ClaimMidcPlanBO claimMidcPlanBO);
 	
 	/**
 	 * 修改数据
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimMidcPlanBO 理赔事中质检计划bo
 	 * @return ClaimMidcPlanBO 理赔事中质检计划bo
 	 */
 	public ClaimMidcPlanBO updateClaimMidcPlan(ClaimMidcPlanBO claimMidcPlanBO);
 	
 	/**
 	 * 删除数据
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimMidcPlanBO 理赔事中质检计划bo
 	 * @return boolean
 	 */
 	public boolean deleteClaimMidcPlan(ClaimMidcPlanBO claimMidcPlanBO);
 	
 	/**
 	 * 查询单条数据
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimMidcPlanBO 理赔事中质检计划bo
 	 * @return ClaimMidcPlanBO 理赔事中质检计划bo
 	 */
 	public ClaimMidcPlanBO findClaimMidcPlan(ClaimMidcPlanBO claimMidcPlanBO);
 	
 	/**
 	 * 查询所有数据
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimMidcPlanBO 理赔事中质检计划bo
 	 * @return List<ClaimMidcPlanBO> 理赔事中质检计划集合
 	 */
 	public List<ClaimMidcPlanBO> findAllClaimMidcPlan(ClaimMidcPlanBO claimMidcPlanBO);
 	
 	/**
 	 * 查询数据条数
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimMidcPlanBO 理赔事中质检计划bo
 	 * @return int
 	 */
 	public int findClaimMidcPlanTotal(ClaimMidcPlanBO claimMidcPlanBO);
 	
 	/**
 	 * 分页查询数据
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimMidcPlanBO 理赔事中质检计划bo
 	 * @param currentPage 理赔事中质检计划分页条件
 	 * @return CurrentPage<ClaimMidcPlanBO> 理赔事中质检计划分页结果
 	 */
 	public CurrentPage<ClaimMidcPlanBO> queryClaimMidcPlanForPage(ClaimMidcPlanBO claimMidcPlanBO, CurrentPage<ClaimMidcPlanBO> currentPage);
 	
 	/**
 	 * 批量增加数据
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimMidcPlanBOList 理赔事中质检计划集合
 	 * @return boolean
 	 */
 	public boolean batchSaveClaimMidcPlan(List<ClaimMidcPlanBO> claimMidcPlanBOList);
 	
 	/**
 	 * 批量修改数据
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimMidcPlanBOList 理赔事中质检计划集合
 	 * @return boolean
 	 */
 	public boolean batchUpdateClaimMidcPlan(List<ClaimMidcPlanBO> claimMidcPlanBOList);
 	
 	/**
 	 * 批量删除数据
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimMidcPlanBOList 理赔事中质检计划集合
 	 * @return boolean
 	 */
 	public boolean batchDeleteClaimMidcPlan(List<ClaimMidcPlanBO> claimMidcPlanBOList);
 	
 	/**
 	 * 查询所有数据 ，重新组装为map
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimMidcPlanBO 理赔事中质检计划bo
 	 * @return List<Map<String, Object>> 理赔事中质检计划重新组装为map
 	 */
 	public List<Map<String, Object>> findAllMapClaimMidcPlan(ClaimMidcPlanBO claimMidcPlanBO);
 	
 }
  