package com.nci.tunan.clm.impl.register.ucc;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.vo.CommonMapVO;
import com.nci.tunan.clm.interfaces.model.vo.OutsourceAgingDeductVO;
import com.nci.tunan.clm.interfaces.model.vo.OutsourceDeductVO;
import com.nci.tunan.clm.interfaces.model.vo.OutsourceErrorDeductVO;

/** 
 * @description 外包扣费标准录入UCC接口
 * <AUTHOR> <EMAIL>
 * @date 2015-05-15
 * @.belongToModule CLM-理赔系统 /外包扣费标准
*/
public interface IEpilolyDeductEnterUCC {
	/**
	 * 
	 * @description 外包扣费
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param outsourceDeductVO 外包扣费对象
	 * @return 外包扣费对象
	 */
	public OutsourceDeductVO queryOutsourceDeduct(
			OutsourceDeductVO outsourceDeductVO);

	/**
	 * 
	 * @description 差错扣费
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param outsourceErrorDeductVO 差错扣费对象
	 * @return 差错扣费对象集合
	 */
	public List<OutsourceErrorDeductVO> queryOutsourceErrorDeduct(
			OutsourceErrorDeductVO outsourceErrorDeductVO);

	/**
	 * 
	 * @description 超时效扣费
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param outsourceAgingDeductVO 超时效扣费
	 * @return 超时效扣费集合
	 */
	public List<OutsourceAgingDeductVO> queryOutsourceAgingDeduct(
			OutsourceAgingDeductVO outsourceAgingDeductVO);

	/**
	 * 
	 * @description 保存
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param outsourceDeductVO 外包扣费标准
	 * @param outsourceAgingDeductVO 超时效扣费标准
	 * @param outsourceErrorDeductVO 差错扣费标准
	 */
	public boolean saveAllEpilolyDeduct(OutsourceDeductVO outsourceDeductVO,
			OutsourceAgingDeductVO outsourceAgingDeductVO,
			OutsourceErrorDeductVO outsourceErrorDeductVO);
	
	/**
	 * @description 删除
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param deleteErrorDeduct  差错扣费标准
	 * @param deleteAgingDeduct  超时效扣费标准
	*/
	public void deleteDeduct(String deleteErrorDeduct,
			String deleteAgingDeduct);
	
	
	/**
	 * @description 查询外包扣费标准
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param commonMapVO 外包map
	 * @return 返回map
	*/
	public CommonMapVO queryEpilolyDeductInfo(CommonMapVO commonMapVO);
	
	/**
	 * @description 删除外包扣费标准
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param commonMapVO 超时效扣费标准，超时效扣费标准
	 * @return 返回map
	*/
	public CommonMapVO deleteDeduct(CommonMapVO commonMapVO);
}
