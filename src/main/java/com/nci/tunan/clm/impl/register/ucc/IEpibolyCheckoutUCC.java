package com.nci.tunan.clm.impl.register.ucc;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.vo.OutsourceDataCheckDefineVO;

/** 
 * @description 外包数据校验定义UCC
 * <AUTHOR> <EMAIL>
 * @date 2015-05-15
 * @.belongToModule CLM-理赔系统/外包数据校验
*/
public interface IEpibolyCheckoutUCC {

     /**
      * 批量修改数据
      * @description
      * @version
      * @title
      * @<NAME_EMAIL>
      * @param outsourceDataCheckDefineBOList 外包数据校验
      * @return 修改是否成功
      */
     public boolean batchUpdateOutsourceDataCheckDefine(OutsourceDataCheckDefineVO outsourceDataCheckDefineVO);

     /**
     * @description 根据必填标识查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param outsourceDataCheckDefineVO 查询条件
     * @return 外包数据校验集合
    */
    public List<OutsourceDataCheckDefineVO> findAllByFlag(OutsourceDataCheckDefineVO outsourceDataCheckDefineVO);

}
