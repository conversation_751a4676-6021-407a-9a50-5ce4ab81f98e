package com.nci.tunan.clm.impl.report.service;

import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.udmp.framework.exception.app.BizException;

/**
 * 调查/慰问接口
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统 --调查/慰问
 * @date 2015年5月15日 上午9:54:45
 */
public interface IClaimBRMService {
    /**
     * 校验是否自动发起调查
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案信息
     * @return String 是否发起调查 
     * @throws BizException 抛出异常
     */
	public String checkAutoStartSurvey(ClaimCaseBO claimCaseBO) throws BizException;
	/**
     * 校验是否自动发起慰问
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案信息
     * @return String 是否发起调查 
     * @throws BizException 抛出异常
     */
	public String checkAutoStartComfort(ClaimCaseBO claimCaseBO) throws BizException;
	/**
	 * 校验是否自动发起“重疾慰问先赔”调查
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param copyProperties
	 * @return
	 * @throws BizException
	 */
	public String checkAutoStartSurveyDisease(ClaimCaseBO copyProperties) throws BizException;
}
