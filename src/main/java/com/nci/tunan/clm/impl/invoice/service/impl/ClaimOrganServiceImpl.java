package com.nci.tunan.clm.impl.invoice.service.impl;

import java.util.List;

import com.nci.tunan.clm.dao.IOrgDao;
import com.nci.tunan.clm.impl.invoice.service.IClaimOrganService;
import com.nci.tunan.clm.interfaces.model.bo.OrgBO;
import com.nci.tunan.clm.interfaces.model.po.OrgPO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;

/** 
 * 管理机构Service
 * @description 
 * <AUTHOR> <EMAIL>
 * @date 2015-05-15
 * @.belongToModule CLM-理赔系统  /理赔打印
*/
public class ClaimOrganServiceImpl implements IClaimOrganService{

    /** 
    * @Fields orgDao : 机构信息
    */ 
    private IOrgDao orgDao;

    /**
     * @description 分页查询管理机构
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.invoice.service.IClaimOrganService#queryPageService(com.nci.tunan.clm.interfaces.model.bo.OrgBO, com.nci.udmp.framework.model.CurrentPage)
     * @param orgBO 管理机构
     * @param currentPage 分页查询
     * @return 管理机构
     * @throws BizException 
    */
    @Override
    public CurrentPage<OrgPO> queryPageService(OrgBO orgBO, CurrentPage<OrgPO> currentPage) throws BizException {
    	//分页打印管理机构
        OrgPO orgPO = new OrgPO();
        BeanUtils.copyProperties(orgPO, orgBO);
        return orgDao.queryOrgForPage(orgPO, currentPage);
    }
    
    public IOrgDao getOrgDao() {
        return orgDao;
    }
    public void setOrgDao(IOrgDao orgDao) {
        this.orgDao = orgDao;
    }

	/**
	 * @description 查询机构信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.invoice.service.IClaimOrganService#queryOrg(com.nci.tunan.clm.interfaces.model.bo.OrgBO)
	 * @param orgBO 查询机构
	 * @return 管理机构
	*/
	@Override
	public OrgBO queryOrg(OrgBO orgBO) {
		//查询机构信息
		OrgPO orgPO = BeanUtils.copyProperties(OrgPO.class, orgBO);
		orgPO = orgDao.findOrg(orgPO);
		return BeanUtils.copyProperties(OrgBO.class, orgPO);
	}
	/**
	 * @description 按条件查询机构集合
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.invoice.service.IClaimOrganService#queryOrgList(com.nci.tunan.clm.interfaces.model.bo.OrgBO)
	 * @param orgBO 机构
	 * @return 机构集合
	 * @throws BizException 
	*/
	@Override
	public List<OrgBO> queryOrgList(OrgBO orgBO) throws BizException {
		//按条件查询机构集合
		OrgPO orgPO = BeanUtils.copyProperties(OrgPO.class, orgBO);
		return BeanUtils.copyList(OrgBO.class, orgDao.findAllOrg(orgPO));
	}
	/**
	 * @description 查询机构（二级机构）
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.invoice.service.IClaimOrganService#queryOrgWhereLevelList(com.nci.tunan.clm.interfaces.model.bo.OrgBO)
	 * @param orgBO 机构
	 * @return 机构（二级机构）集合
	 * @throws BizException 
	*/
	@Override
	public List<OrgBO> queryOrgWhereLevelList(OrgBO orgBO) throws BizException {
		//查询机构（二级机构）
		OrgPO orgPO = BeanUtils.copyProperties(OrgPO.class, orgBO);
		return BeanUtils.copyList(OrgBO.class, orgDao.queryOrgWhereLevelList(orgPO));
	}
}
