package com.nci.tunan.clm.impl.claimHiscaseRetrieval.service;

import com.nci.tunan.clm.interfaces.model.bo.ClaimHiscaseRetrievalBO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;

public interface IClaimHiscaseRetrievalService {

	/**
	 * 导入Excel
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 */
	public String uploadRetrievalHiscases(ClaimHiscaseRetrievalBO claimHiscaseRetrievalBO) throws BizException;

	/**
	 * 查询历史赔案调取数据
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 */
	public CurrentPage<ClaimHiscaseRetrievalBO> queryHiscasesRetrievalResult(ClaimHiscaseRetrievalBO claimHiscaseRetrievalBO, CurrentPage<ClaimHiscaseRetrievalBO> copyCurrentPage);

}
