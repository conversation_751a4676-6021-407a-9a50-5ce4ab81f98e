package com.nci.tunan.clm.impl.register.ucc;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.vo.CommonMapVO;
import com.nci.tunan.clm.interfaces.model.vo.OrgVO;
import com.nci.tunan.clm.interfaces.model.vo.OutsourceIfnoVO;
import com.nci.tunan.clm.interfaces.model.vo.OutsourceOrganMappingVO;
import com.nci.udmp.framework.exception.app.BizException;

/** 
 * @description 外包商维护
 * <AUTHOR> <EMAIL>
 * @date 2015-05-15
 * @.belongToModule CLM-理赔系统/外包商维护
*/
public interface IEpibolyManageUCC {
	
	/**
	 * @description 外包商的添加
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param outsourceIfnoVO 外包商信息
	 * @param outsourceOrganMappingVO 外包商与管理机构映射
	 * @return 添加是否成功
	 * @throws BizException 
	*/
	public boolean addOutsourceIfno (OutsourceIfnoVO outsourceIfnoVO, OutsourceOrganMappingVO outsourceOrganMappingVO) throws BizException;
	
	/**
	 * @description 查询出所有的机构
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param orgVO 机构信息
	 * @return 机构信息集合
	 * @throws BizException 
	*/
	public List<OrgVO> queryOrgVO(OrgVO orgVO) throws BizException;
	
	/**
	 * @description 外包商查询
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param outsourceIfnoVO 外包商信息
	 * @param outsourceOrganMappingVO 外包商与机构映射
	 * @return 外包商信息集合
	 * @throws BizException 
	*/
	public List<OutsourceIfnoVO> queryOutsourceIfno (OutsourceIfnoVO outsourceIfnoVO, OutsourceOrganMappingVO outsourceOrganMappingVO) throws BizException;
    
	
	
	/**
	 * @description 查询外包商信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param outsourceIfnoVO 外包商信息
	 * @return 外包商信息集合
	 * @throws BizException 
	*/
	public List<OutsourceIfnoVO> queryAllOutsourceIfno (OutsourceIfnoVO outsourceIfnoVO) throws BizException;
	
	/**
	 * @description 校验外包商信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param outsourceIfnoVO 外包商信息
	 * @param outsourceOrganMappingVO 外包商与机构映射
	 * @return 校验是否成功
	*/
	public String checkSimpleOutsource(OutsourceIfnoVO outsourceIfnoVO,
			OutsourceOrganMappingVO outsourceOrganMappingVO);
	
	/**
	 * @description 外包商数据重复校验
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param outsourceIfnoVO 外包商信息
	 * @param outsourceOrganMappingVO 外包商信息与机构映射
	 * @return 重复数据校验返回
	*/
	public boolean checkOutsourceIfno(OutsourceIfnoVO outsourceIfnoVO,
			OutsourceOrganMappingVO outsourceOrganMappingVO);

	/**
	 * @description 外包商的查询
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param commonMapVO 查询条件
	 * @return 外包商信息
	*/
	public CommonMapVO queryOutsourceIfno(CommonMapVO commonMapVO);

	/**
	 * @description 外包商管理的初始化页面
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param commonMapVO 外包商信息
	 * @return  初始化信息
	*/
	public CommonMapVO epibolyManageInit(CommonMapVO commonMapVO);

}
