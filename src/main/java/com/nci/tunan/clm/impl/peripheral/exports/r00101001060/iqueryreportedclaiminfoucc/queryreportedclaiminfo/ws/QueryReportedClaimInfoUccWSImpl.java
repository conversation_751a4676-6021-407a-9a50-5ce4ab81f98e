package com.nci.tunan.clm.impl.peripheral.exports.r00101001060.iqueryreportedclaiminfoucc.queryreportedclaiminfo.ws;

import com.nci.udmp.component.aop.TablePrevThreadLocal;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;

import javax.jws.WebService;
import javax.xml.ws.Holder;

import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001060.iqueryreportedclaiminfoucc.queryreportedclaiminfo.SrvReqBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001060.iqueryreportedclaiminfoucc.queryreportedclaiminfo.SrvResBizBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001060.iqueryreportedclaiminfoucc.queryreportedclaiminfo.SrvResBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001060.iqueryreportedclaiminfoucc.queryreportedclaiminfo.ws.IQueryReportedClaimInfoUccWS;
import com.nci.tunan.clm.impl.peripheral.ucc.r00101001060.IQueryReportedClaimInfoUcc;
/**
 * 
 * @description 出险人报案信息查询Ucc
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/理赔查询
 * @date  2015年5月15日 上午10:08:31
 */
@WebService(endpointInterface = "com.nci.tunan.clm.interfaces.peripheral.exports.r00101001060.iqueryreportedclaiminfoucc.queryreportedclaiminfo.ws.IQueryReportedClaimInfoUccWS"
, serviceName = "QueryReportedClaimInfoUccWSImplqueryReportedClaimInfo")
public class QueryReportedClaimInfoUccWSImpl implements IQueryReportedClaimInfoUccWS {
	/**
	 * 出险人报案信息查询
	 */
	private IQueryReportedClaimInfoUcc ucc = null;
	
	public IQueryReportedClaimInfoUcc getUcc() {
        return ucc;
    }
    public void setUcc(IQueryReportedClaimInfoUcc ucc) {
        this.ucc = ucc;
    }
    
    /**
     * 
     * @description 查询出险人报案信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.interfaces.peripheral.exports.r00101001060.iqueryreportedclaiminfoucc.queryreportedclaiminfo.ws.IQueryReportedClaimInfoUccWS#queryReportedClaimInfo(com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader, com.nci.tunan.clm.interfaces.peripheral.exports.r00101001060.iqueryreportedclaiminfoucc.queryreportedclaiminfo.SrvReqBody, javax.xml.ws.Holder, javax.xml.ws.Holder)
     * @param parametersReqHeader 入参报文头
     * @param parametersReqBody 入参报文体
     * @param parametersResHeader 出参报文头
     * @param parametersResBody 出参报文体
     */
    @Override
	public void queryReportedClaimInfo(SysHeader parametersReqHeader, SrvReqBody parametersReqBody, 
			Holder<SysHeader> parametersResHeader, Holder<SrvResBody> parametersResBody) {//查询出险人报案信息
    	
    	TablePrevThreadLocal.setTABLEPREV("APP___CLM__DBUSER.");
    	
		CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
				CommonHeaderDeal.setBIZHEADERTHREAD_EXB(parametersReqBody.getBizHeader());
		        com.nci.tunan.clm.interfaces.peripheral.exports.r00101001060.vo.InputData inputVO = parametersReqBody.getBizBody().getInputData();
        com.nci.tunan.clm.interfaces.peripheral.exports.r00101001060.vo.OutputData output = ucc.queryReportedClaimInfo(inputVO);
        SysHeader sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
         BizHeaderExB  bizHeader = CommonHeaderDeal.dealBizHeader(parametersReqBody.getBizHeader());
         if(output == null || output.getResult()==null || output.getResult().isEmpty()){
        	 bizHeader.setTotalRowNum("0");
         }else{
        	 bizHeader.setTotalRowNum(output.getResult().size()+"");
         }
        SrvResBizBody bizResBody = new SrvResBizBody();
        bizResBody.setOutputData(output);
        SrvResBody srvResBody = new SrvResBody();
        srvResBody.setBizBody(bizResBody);
        srvResBody.setBizHeader(bizHeader);
        parametersResHeader.value = sysHeader;
        parametersResBody.value = srvResBody;
	}
}
 

