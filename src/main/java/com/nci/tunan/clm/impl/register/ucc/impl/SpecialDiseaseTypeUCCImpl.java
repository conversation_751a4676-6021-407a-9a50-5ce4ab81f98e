package com.nci.tunan.clm.impl.register.ucc.impl;


import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.clm.impl.register.service.ISpecialDiseaseTypeService;
import com.nci.tunan.clm.impl.register.ucc.ISpecialDiseaseTypeUCC;
import com.nci.tunan.clm.interfaces.model.bo.SpecialDiseaseTypeBO;
import com.nci.tunan.clm.interfaces.model.vo.SpecialDiseaseTypeVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.logging.LoggerFactory;

/** 
 * @description 特定费用类型代码UCC实现类
 * <AUTHOR> <EMAIL>
 * @date 2015-05-15
 * @.belongToModule CLM-理赔系统/特定费用类型代码
*/
public class SpecialDiseaseTypeUCCImpl   implements ISpecialDiseaseTypeUCC  {
    /** 
     * @Fields specialDiseaseTypeService : 特定费用类型代码service 
     */
	private ISpecialDiseaseTypeService specialDiseaseTypeService;
	
	/** 
	 * @Fields logger :  日志工具
 	 */
	private static Logger logger = LoggerFactory.getLogger();
	
	/**
     * @description SERVICE-getter方法
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @return ISpecialDiseaseTypeService service接口对象
     */
	public ISpecialDiseaseTypeService getSpecialDiseaseTypeService() {
		return specialDiseaseTypeService;
	}
	
	/**
     * @description SERVICE-setter方法
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeService service对象
     */
	public void setSpecialDiseaseTypeService(ISpecialDiseaseTypeService specialDiseaseTypeService) {
		this.specialDiseaseTypeService = specialDiseaseTypeService;
	}
	
	/**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVO 对象
     * @return SpecialDiseaseTypeVO 添加结果
     */
	public SpecialDiseaseTypeVO addSpecialDiseaseType(SpecialDiseaseTypeVO specialDiseaseTypeVO) {
		logger.debug("<======SpecialDiseaseTypeUCC--addSpecialDiseaseType======>");
		//1.增加特定病费用数据
		SpecialDiseaseTypeBO specialDiseaseTypeBO = specialDiseaseTypeService.addSpecialDiseaseType(BeanUtils.copyProperties(SpecialDiseaseTypeBO.class, specialDiseaseTypeVO));
		return BeanUtils.copyProperties(SpecialDiseaseTypeVO.class, specialDiseaseTypeBO);
	}
	
	/**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVO 对象
     * @return SpecialDiseaseTypeVO 修改结果
     */
	public SpecialDiseaseTypeVO updateSpecialDiseaseType(SpecialDiseaseTypeVO specialDiseaseTypeVO)  {
		logger.debug("<======SpecialDiseaseTypeUCC--updateSpecialDiseaseType======>");
		//1.修改特定病费用数据
		SpecialDiseaseTypeBO specialDiseaseTypeBO = specialDiseaseTypeService.updateSpecialDiseaseType(BeanUtils.copyProperties(SpecialDiseaseTypeBO.class, specialDiseaseTypeVO));
		return BeanUtils.copyProperties(SpecialDiseaseTypeVO.class, specialDiseaseTypeBO);
	}

	 /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVO 对象
     * @return boolean 删除是否成功
     */
	public boolean deleteSpecialDiseaseType(SpecialDiseaseTypeVO specialDiseaseTypeVO)  {
		logger.debug("<======SpecialDiseaseTypeUCC--deleteSpecialDiseaseType======>");
		//1.删除特定病费用数据
		return specialDiseaseTypeService.deleteSpecialDiseaseType(BeanUtils.copyProperties(SpecialDiseaseTypeBO.class, specialDiseaseTypeVO));
	}
	
	/**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVO 对象
     * @return SpecialDiseaseTypeVO 查询结果对象
     */
	public SpecialDiseaseTypeVO findSpecialDiseaseType(SpecialDiseaseTypeVO specialDiseaseTypeVO) {
		logger.debug("<======SpecialDiseaseTypeUCC--findSpecialDiseaseType======>");
		//1.查询单条特定病费用数据
		SpecialDiseaseTypeBO specialDiseaseTypeBackBO = specialDiseaseTypeService.findSpecialDiseaseType(BeanUtils.copyProperties(SpecialDiseaseTypeBO.class, specialDiseaseTypeVO));
		SpecialDiseaseTypeVO specialDiseaseTypeBackVO = BeanUtils.copyProperties(SpecialDiseaseTypeVO.class, specialDiseaseTypeBackBO);
		return specialDiseaseTypeBackVO;
	}  
	
	/**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVO 对象
     * @return List<SpecialDiseaseTypeVO> 查询结果List
     */
	public List<SpecialDiseaseTypeVO> findAllSpecialDiseaseType(SpecialDiseaseTypeVO specialDiseaseTypeVO) {
		logger.debug("<======SpecialDiseaseTypeUCC--findAllSpecialDiseaseType======>");
		//1.查询所有特定病费用数据
		SpecialDiseaseTypeBO specialDiseaseTypeBO = BeanUtils.copyProperties(SpecialDiseaseTypeBO.class, specialDiseaseTypeVO);
		return BeanUtils.copyList(SpecialDiseaseTypeVO.class, specialDiseaseTypeService.findAllSpecialDiseaseType(specialDiseaseTypeBO));
	} 
	
	 /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVO 对象
     * @return int 查询结果条数
     */
	public int findSpecialDiseaseTypeTotal(SpecialDiseaseTypeVO specialDiseaseTypeVO) {
		logger.debug("<======SpecialDiseaseTypeUCC--findSpecialDiseaseTypeTotal======>");
		//1.查询特定病费用数据条数
		SpecialDiseaseTypeBO specialDiseaseTypeBO = BeanUtils.copyProperties(SpecialDiseaseTypeBO.class, specialDiseaseTypeVO);
		return specialDiseaseTypeService.findSpecialDiseaseTypeTotal(specialDiseaseTypeBO);
	}	
	
	 /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVO 对象
     * @param currentPage 当前页对象
     * @return CurrentPage<SpecialDiseaseTypeVO> 查询结果的当前页对象
     */
	public CurrentPage<SpecialDiseaseTypeVO> querySpecialDiseaseTypeForPage(SpecialDiseaseTypeVO specialDiseaseTypeVO, CurrentPage<SpecialDiseaseTypeVO> currentPage) {
		//1.分页查询特定病费用数据
		logger.debug("<======SpecialDiseaseTypeUCC--querySpecialDiseaseTypeForPage======>");
		SpecialDiseaseTypeBO specialDiseaseTypeBO = BeanUtils.copyProperties(SpecialDiseaseTypeBO.class, specialDiseaseTypeVO);
		return BeanUtils.copyCurrentPage(SpecialDiseaseTypeVO.class, specialDiseaseTypeService.querySpecialDiseaseTypeForPage
		        (specialDiseaseTypeBO, BeanUtils.copyCurrentPage(SpecialDiseaseTypeBO.class, currentPage)));
	}
	
	/**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVOList 对象列表
     * @return boolean 批量添加是否成功
     */
	public boolean batchSaveSpecialDiseaseType(List<SpecialDiseaseTypeVO> specialDiseaseTypeVOList) {
		logger.debug("<======SpecialDiseaseTypeUCC--batchSaveSpecialDiseaseType======>");
		//1.分页查询特定病费用数据
		return specialDiseaseTypeService.batchSaveSpecialDiseaseType(BeanUtils.copyList(SpecialDiseaseTypeBO.class, specialDiseaseTypeVOList));
	}
	
	/**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVOList 对象列表
     * @return boolean 批量修改是否成功
     */
	public boolean batchUpdateSpecialDiseaseType(List<SpecialDiseaseTypeVO> specialDiseaseTypeVOList) {
		logger.debug("<======SpecialDiseaseTypeUCC--batchUpdateSpecialDiseaseType======>");
		//1.批量修改特定病费用数据
		return specialDiseaseTypeService.batchUpdateSpecialDiseaseType(BeanUtils.copyList(SpecialDiseaseTypeBO.class, specialDiseaseTypeVOList));
	}
	
	/**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVOList 对象列表
     * @return boolean 批量删除是否成功
     */
	public boolean batchDeleteSpecialDiseaseType(List<SpecialDiseaseTypeVO> specialDiseaseTypeVOList) {
		logger.debug("<======SpecialDiseaseTypeUCC--batchDeleteSpecialDiseaseType======>");
		//1.批量删除特定病费用数据
		return specialDiseaseTypeService.batchDeleteSpecialDiseaseType(BeanUtils.copyList(SpecialDiseaseTypeBO.class, specialDiseaseTypeVOList));
	}
	
	/**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param specialDiseaseTypeVO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	public List<Map<String, Object>> findAllMapSpecialDiseaseType(SpecialDiseaseTypeVO specialDiseaseTypeVO) {
		logger.debug("<======SpecialDiseaseTypeUCC--findAllMapSpecialDiseaseType======>");
		//查询所有特定病费用数据 ，重新组装为map
		SpecialDiseaseTypeBO specialDiseaseTypeBO = BeanUtils.copyProperties(SpecialDiseaseTypeBO.class, specialDiseaseTypeVO);
		return specialDiseaseTypeService.findAllMapSpecialDiseaseType(specialDiseaseTypeBO);
	}
	
}
