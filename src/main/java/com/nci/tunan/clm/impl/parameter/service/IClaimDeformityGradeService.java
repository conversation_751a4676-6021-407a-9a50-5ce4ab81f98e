package com.nci.tunan.clm.impl.parameter.service;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.bo.ClaimDeformityGradeBO;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * 伤残等级维护service
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2016-03-04 11:45:14  
 */
 public interface IClaimDeformityGradeService {
 	/**
     * @description 增加数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBO 伤残等级维护对象
     * @return ClaimDeformityGradeBO 添加结果
     */
 	public ClaimDeformityGradeBO addClaimDeformityGrade(ClaimDeformityGradeBO claimDeformityGradeBO);
 	
	 /**
     * @description 修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBO 伤残等级维护对象
     * @return ClaimDeformityGradeBO 修改结果
     */
 	public ClaimDeformityGradeBO updateClaimDeformityGrade(ClaimDeformityGradeBO claimDeformityGradeBO);
 	
	 /**
     * @description 删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBO 伤残等级维护对象
     * @return boolean 删除是否成功
     */
 	public boolean deleteClaimDeformityGrade(ClaimDeformityGradeBO claimDeformityGradeBO);
 	
	 /**
     * @description 查询单条数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBO 伤残等级维护对象
     * @return ClaimDeformityGradeBO 查询结果对象
     */
 	public ClaimDeformityGradeBO findClaimDeformityGrade(ClaimDeformityGradeBO claimDeformityGradeBO);
 	
	/**
     * @description 查询所有数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBO 伤残等级维护对象
     * @return List<ClaimDeformityGradeBO> 查询结果List
     */
 	public List<ClaimDeformityGradeBO> findAllClaimDeformityGrade(ClaimDeformityGradeBO claimDeformityGradeBO);
 	
	 /**
     * @description 查询数据条数
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBO 伤残等级维护对象
     * @return int 查询结果条数
     */
 	public int findClaimDeformityGradeTotal(ClaimDeformityGradeBO claimDeformityGradeBO);
 	
	 /**
     * @description 分页查询数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBO 伤残等级维护对象
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimDeformityGradeBO> 查询结果的当前页对象
     */
 	public CurrentPage<ClaimDeformityGradeBO> queryClaimDeformityGradeForPage(ClaimDeformityGradeBO claimDeformityGradeBO, CurrentPage<ClaimDeformityGradeBO> currentPage);
 	
	 /**
     * @description 批量增加数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBOList 伤残等级维护对象列表
     * @return boolean 批量添加是否成功
     */
 	public boolean batchSaveClaimDeformityGrade(List<ClaimDeformityGradeBO> claimDeformityGradeBOList);
 	
	/**
     * @description 批量修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBOList 伤残等级维护对象列表
     * @return boolean 批量修改是否成功
     */
 	public boolean batchUpdateClaimDeformityGrade(List<ClaimDeformityGradeBO> claimDeformityGradeBOList);
 	
	/**
     * @description 批量删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBOList 伤残等级维护对象列表
     * @return boolean 批量删除是否成功
     */
 	public boolean batchDeleteClaimDeformityGrade(List<ClaimDeformityGradeBO> claimDeformityGradeBOList);
 	
	/**
     * @description 查询所有数据 ，重新组装为map
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeBO 伤残等级维护对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
 	public List<Map<String, Object>> findAllMapClaimDeformityGrade(ClaimDeformityGradeBO claimDeformityGradeBO);
 	
	/**
 	 * @description 通过对象查询伤残等级
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimDeformityGradeVO 伤残等级维护对象
     * @return List<ClaimDeformityGradeVO> 查询结果存放到list中 
 	 */
 	public List<ClaimDeformityGradeBO> findDeformityGradeByTypeId(ClaimDeformityGradeBO claimDeformityGradeBO);
 	
 }
  