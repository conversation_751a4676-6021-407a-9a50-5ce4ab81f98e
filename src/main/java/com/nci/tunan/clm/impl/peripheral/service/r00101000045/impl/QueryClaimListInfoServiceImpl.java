package com.nci.tunan.clm.impl.peripheral.service.r00101000045.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.nci.core.common.impl.util.CodeMapperUtils;
import com.nci.tunan.clm.dao.IClaimForMobileDao;
import com.nci.tunan.clm.impl.peripheral.service.r00101000045.IQueryClaimListInfoService;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000045.vo.InputData;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000045.vo.OutputData;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000045.vo.Result;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;

/**
 * 
 * @description 理赔列表信息查询(该接口不支持团体保单）
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/理赔列表信息
 * @date  2015年5月15日 上午10:08:31
 */
public class QueryClaimListInfoServiceImpl implements IQueryClaimListInfoService{
	
	/** 
	* @Fields iClaimForMobileDao : 理赔移动Dao
	*/ 
	private IClaimForMobileDao iClaimForMobileDao;

	/**
	 * 
	 * @description 查询理赔列表信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.peripheral.service.r00101000045.IQueryClaimListInfoService#queryClaimListInfo(com.nci.tunan.clm.interfaces.peripheral.exports.r00101000045.vo.InputData)
	 * @param inputVO 查询入参
	 * @return OutputData 出参
	 * @throws Exception
	 */
	@Override
	public OutputData queryClaimListInfo(InputData inputVO) throws Exception{
		//1.根据输入条件查询理赔列表信息
		String caseNo = inputVO.getClmNo();//@invalid 个人立案号
		String policyCode = inputVO.getContNo();//@invalid 保单号
		//2.拼接sql,实现排序	
		BizHeaderExB bizheaderthread_EXB = CommonHeaderDeal.getBIZHEADERTHREAD_EXB();
		String flag=bizheaderthread_EXB.getOrderFlag();
		String field=bizheaderthread_EXB.getOrderField();
		String orderContext="";
		//@invalid 如果field值为ClmNo，则field设置为CASE_NO，否则设置为CLAIM_DATE。
		if(field.equals("ClmNo")){
			field="CASE_NO";
		}else if(field.equals("Accdate")){
			field="CLAIM_DATE";
		}
		//3.如果赔案号和保单号不为空，继续判断field值是否为CASE_NO或者CLAIM_DATE，再判断flag是否为1，是则orderContext赋指定值。
		if(!StringUtilsEx.isNullOrEmpty(caseNo) || !StringUtilsEx.isNullOrEmpty(policyCode)){			
			if(field.equals("CASE_NO")||field.equals("CLAIM_DATE")){
				if(flag.equals("1")){
					orderContext=" ORDER BY "+field;
				}else if(flag.equals("2")){
					orderContext=" ORDER BY "+field+" DESC";
				}
			}
		}
		//@invalid 如果入参的GrpContNo的值不为空，则orderContext设置为指定值。
		else if(!StringUtilsEx.isNullOrEmpty(inputVO.getGrpContNo()) 
				|| !StringUtilsEx.isNullOrEmpty(inputVO.getGrpClmNo())){
			orderContext=" ORDER BY CASE_NO DESC";
		}

		//@invalid 个人立案号和保单号 为空时返回输出。
		if(StringUtils.isBlank(caseNo) && StringUtils.isBlank(policyCode)){
			return new OutputData();
		}
		
		//4.个人立案号或保单号不为空时查询理赔列表信息
		if(StringUtils.isNotBlank(caseNo)||StringUtils.isNotBlank(policyCode)){
			//@invalid /**理赔保单理算表 和赔案主表 中的管理机构不一致，这里只取理赔保单理算表中的管理机构作为约束条件**/
			//@invalid 理赔主表中的管理机构
			inputVO.setMngCom(inputVO.getMngCom());
			ClaimCasePO claimCasePO = setClaimCasePO(inputVO,orderContext);
			List<Map<String, Object>> allMaplst = iClaimForMobileDao.findAllMapClaimListInfo(claimCasePO);
			return getClaimlstInfo(allMaplst,inputVO.getStateCode());
		}

		return new OutputData();
	}
	
	/**
	 * 
	 * @description 设置传入参数
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param inputVO 入参
	 * @param orderContext 传入参数
	 * @return ClaimCasePO 赔案主表信息返回
	 */
	private ClaimCasePO setClaimCasePO(InputData inputVO,String orderContext) {
		//1.设置传入参数
		ClaimCasePO claimCasePO = new ClaimCasePO();
		claimCasePO.getData().put("policy_code", inputVO.getContNo());//@invalid 保单号
		claimCasePO.getData().put("accident_no", inputVO.getAccNo());//@invalid 事故号
		claimCasePO.getData().put("case_no", inputVO.getClmNo());//@invalid 个人立案号
		claimCasePO.getData().put("customer_name", inputVO.getCustomerName());//@invalid 客户姓名
		claimCasePO.getData().put("rptr_name", inputVO.getRptorName());//@invalid 报案人姓名
		claimCasePO.getData().put("organ_code", inputVO.getMngCom());//@invalid 管理机构
		try {
			claimCasePO.getData().put("claim_start_date", DateUtilsEx.formatToDate(inputVO.getAccStartDate(),"yyyy-MM-dd"));//@invalid 出险开始时间
			claimCasePO.getData().put("claim_end_date", DateUtilsEx.formatToDate(inputVO.getAccEndDate(),"yyyy-MM-dd"));//@invalid 出险开始时间
		} catch (Exception e) {
			e.printStackTrace();
		}
//	@invalid 	claimCasePO.getData().put("case_status", CodeMapperUtils.getNewCodeByOldCode("CASE_STATUS",inputVO.getStateCode(),"CLM"));//赔案状态
		claimCasePO.getData().put("id_no", inputVO.getiDNo());//@invalid 证件号码
		
		claimCasePO.getData().put("order_context", orderContext);//2.拼接排序sql
		return claimCasePO;
	}
	
	
	/**
	 * 
	 * @description 获取列表信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param maplst map集合
	 * @param clmState 理赔状态
	 * @return OutputData 出参结果
	 */
	private OutputData getClaimlstInfo(List<Map<String, Object>> maplst,String clmState) {
		//@invalid 获取列表信息
		//1.查询表赔案主表T_CLAIM_CASE、理赔子案件表T_CLAIM_SUB_CASE、理赔事件表T_CLAIM_ACCIDENT
		if(null == maplst || maplst.size() == 0){
			return new OutputData();
		}
		OutputData outputData = new OutputData();
		List<Result> resultlst = new ArrayList<Result>();
		outputData.setResult(resultlst);
		Result result = null;
		
		//2.遍历map
		for(Map<String, Object> map : maplst){
			result = new Result();
			String newCaseStatus=StringUtilsEx.nullToString(map.get("CASE_STATUS"));
			String newStatusName=StringUtilsEx.nullToString(map.get("CASE_STATUS_NAME"));
			String oldCaseStatus=CodeMapperUtils.getOldCodeByNewCode("CASE_STATUS", newCaseStatus, "CLM");
			String oldStatusName=CodeMapperUtils.getOldNameByNewCode("CASE_STATUS", newCaseStatus, "CLM");
			//@invalid 新核心 10 20 都返回老核心 10 报案
			if("15".equals(oldCaseStatus)){
				oldCaseStatus="10";
				oldStatusName="报案";
			}
			//@invalid 如果入参中状态为空
			if(StringUtilsEx.isNullOrEmpty(clmState)){
				if(newCaseStatus.equals("80")){
					oldCaseStatus="60";
					oldStatusName="完成";
				}
			}else{
				if(newCaseStatus.equals("80")){
					oldCaseStatus=clmState;
					if(oldCaseStatus.equals("50")){
						oldStatusName="结案";
					}else if(oldCaseStatus.equals("60")){
						oldStatusName="完成";
					}
				}
				if(!clmState.equals(oldCaseStatus)){
					continue;
				}
			}
			
			//@invalid 个人立案号
			result.setClmNo(StringUtilsEx.nullToString(map.get("CASE_NO")));
			//@invalid 客户姓名
			result.setCustomerName(StringUtilsEx.nullToString(map.get("CUSTOMER_NAME")));
			//@invalid 状态编码	ps:设计文档中该节点域名称为 出险开始时间
			result.setStateCode(oldCaseStatus);
//@invalid 			result.setStateCode(CodeMapperUtils.getOldCodeByNewCode("CASE_STATUS", caseStatus));
			//@invalid 客户号
			result.setCustomerNo(StringUtilsEx.nullToString(map.get("INSURED_ID")));
			//@invalid 出险日期
			result.setAccdate(DateUtilsEx.formatToString((Date)map.get("CLAIM_DATE"),"yyyy-MM-dd"));
			//@invalid 赔案状态
			result.setState(oldStatusName);
//	@invalid 		result.setState(CodeMapperUtils.getOldNameByNewCode("CASE_STATUS",caseStatus));
			//@invalid 管理机构,ESB报文比对，修改为与老核心一致取REGISTE_ORGAN字段值,若为空则取ORGAN_CODE。yanhzit。
			result.setMngCom(StringUtilsEx.nullToString(map.get("REGISTE_ORGAN")));
			if(result.getMngCom().equals("")) {
				result.setMngCom(StringUtilsEx.nullToString(map.get("ORGAN_CODE")));
		    }
			//@invalid 接收人（这里取的是受理人的姓名）老核心 报案节点和立案节点 取得你一样
			 if("报案".equals(result.getState())){ 
				 result.setAcceptedName(StringUtilsEx.nullToString(map.get("REAL_NAME_RECODE")));
			     //@invalid 查询不出来在按之前的逻辑
				 if("".equals(result.getAcceptedName())){
					 result.setAcceptedName(StringUtilsEx.nullToString(map.get("REAL_NAME"))); 
				 }
			 }else{
				 result.setAcceptedName(StringUtilsEx.nullToString(map.get("REAL_NAME")));
			 }
			//@invalid 状态标志 
//@invalid 			/* 2017-8-2 15:18:23
//	@invalid 		 * 经测试人员杨晶薇与新电话中心消费方张恒联系，只有在赔案状态为10-报案（新核心为10-报案中），才能修改赔案信息
//	@invalid 		 * 目前老核心代码查询SQL是写死的，因此始终返回"是"
//	@invalid 		 */
			 //@invalid 新核心20(代签收)也转为老核心10报案
			 if(ClaimConstant.REPORTSTATUSE.equals(newCaseStatus)||ClaimConstant.SIGNSTATUSE.equals(newCaseStatus)){
			        result.setIsInPubCache("是");
			    }else{
			        result.setIsInPubCache("否");
			    }
			
			//@invalid 申请类型
//	@invalid 		result.setRgtClass(StringUtilsEx.nullToString(map.get("CASE_APPLY_TYPE")));
			result.setRgtClass("1");//@invalid 新核心只有1-个人
			
			//@invalid 团体保单号	新核心没有团体保单/赔案号，暂当做个人赔案号返回
			result.setGrpClmNo(result.getClmNo());
			
			resultlst.add(result);
		}
		return outputData;
	}

	public IClaimForMobileDao getiClaimForMobileDao() {
		return iClaimForMobileDao;
	}

	public void setiClaimForMobileDao(IClaimForMobileDao iClaimForMobileDao) {
		this.iClaimForMobileDao = iClaimForMobileDao;
	}

}
