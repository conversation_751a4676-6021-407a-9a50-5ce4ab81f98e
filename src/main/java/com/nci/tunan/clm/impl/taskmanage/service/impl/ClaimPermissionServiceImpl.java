package com.nci.tunan.clm.impl.taskmanage.service.impl;

import com.nci.tunan.clm.dao.IClaimCaseDao;
import com.nci.tunan.clm.dao.IClaimPrePermissionConfigDao;
import com.nci.tunan.clm.dao.IClaimTaskStaffDao;
import com.nci.tunan.clm.dao.IUserDao;
import com.nci.tunan.clm.impl.taskmanage.service.IClaimPermissionService;
import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimPrePermissionConfigBO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimPrePermissionConfigPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimTaskStaffPO;
import com.nci.tunan.clm.interfaces.model.po.UserPO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/** 
 * @description  岗前核赔用户权限配置Service
 * <AUTHOR> <EMAIL> 
 * @date 2017年8月28日 下午1:49:14
 * @.belongToModule CLM-理赔系统/岗前核赔用户权限配置
*/
public class ClaimPermissionServiceImpl implements IClaimPermissionService {
	private static final String String = null;

	/** 
	* @Fields iClaimPermissionDao : 岗前核赔Dao
	*/ 
	private IClaimPrePermissionConfigDao iClaimPrePermissionConfigDao;
	
	private IUserDao claimUserDao;
	
	public IClaimTaskStaffDao getiClaimTaskStaffDao() {
		return iClaimTaskStaffDao;
	}

	public void setiClaimTaskStaffDao(IClaimTaskStaffDao iClaimTaskStaffDao) {
		this.iClaimTaskStaffDao = iClaimTaskStaffDao;
	}

	private IClaimCaseDao iClaimCaseDao;
	
	private IClaimTaskStaffDao iClaimTaskStaffDao;
	/**
	 * 
	 * @description 查询所有岗前核赔用户权限配置
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.taskmanage.service.IClaimAreaService#findAll(com.nci.tunan.clm.interfaces.model.bo.ClaimAreaBO)
	 * @param claimAreaBO 
	 * @return List<ClaimAreaBO>
	 */

	@Override
	public CurrentPage<ClaimPrePermissionConfigBO> getClaimPermission(
			ClaimPrePermissionConfigBO claimPrePermissionConfigBO,
			CurrentPage<ClaimPrePermissionConfigBO> currentPage) {
		// TODO Auto-generated method stub
		ClaimPrePermissionConfigPO claimPrePermissionConfigPO = BeanUtils.copyProperties(ClaimPrePermissionConfigPO.class, claimPrePermissionConfigBO);
		
		if(StringUtils.isNotBlank(claimPrePermissionConfigBO.getPreAuditCode())){
			UserPO userPo=new UserPO();
			userPo.setUserName(claimPrePermissionConfigBO.getPreAuditCode());
			UserPO findClaimUserVOByUserName = claimUserDao.findClaimUserVOByUserName(userPo);  
			if(findClaimUserVOByUserName.getUserId()==null){
				CurrentPage<ClaimPrePermissionConfigBO> claimPrePermissionConfigList=new CurrentPage<ClaimPrePermissionConfigBO>();
				return claimPrePermissionConfigList;
			}
			claimPrePermissionConfigPO.setPreAuditId(findClaimUserVOByUserName.getUserId());
		}
		if(StringUtils.isNotBlank(claimPrePermissionConfigBO.getPreAuditName())){
			UserPO userPoTwo=new UserPO();
			userPoTwo.setRealName(claimPrePermissionConfigBO.getPreAuditName());
			List<UserPO> findClaimUserVOByUserNames = claimUserDao.findAllUser(userPoTwo);  
			if(findClaimUserVOByUserNames.get(0).getUserId()==null){
				CurrentPage<ClaimPrePermissionConfigBO> claimPrePermissionConfigList=new CurrentPage<ClaimPrePermissionConfigBO>();
				return claimPrePermissionConfigList;
			}
			claimPrePermissionConfigPO.setPreAuditId(findClaimUserVOByUserNames.get(0).getUserId());
			
		}
		CurrentPage<ClaimPrePermissionConfigBO> claimPrePermissionConfigBOs= BeanUtils.copyCurrentPage(ClaimPrePermissionConfigBO.class, 
		iClaimPrePermissionConfigDao.queryClaimPrePermissionConfigForPage(claimPrePermissionConfigPO, BeanUtils.copyCurrentPage(ClaimPrePermissionConfigPO.class, currentPage)));
		List<ClaimPrePermissionConfigBO> list= claimPrePermissionConfigBOs.getPageItems();
		
		
		  for (int i = 0; i < list.size(); i++) {
			  BigDecimal preAuditId=  list.get(i).getPreAuditId();
			  UserPO userPo=new UserPO();
			  userPo.setUserId(preAuditId);
			  userPo= claimUserDao.findUserByUserId(userPo);
			  list.get(i).setPreAuditCode(userPo.getUserName());
			  list.get(i).setPreAuditName(userPo.getRealName());
			  
			  
          } 

		  for (int i = 0; i < list.size(); i++) {
		 			  BigDecimal easyAudit=list.get(i).getEasyAudit();
		 		  if(easyAudit.compareTo(new BigDecimal(1))==0){
		 			  list.get(i).setEasyAuditStr("有");
		 			  
		 		  }else{
		 			  list.get(i).setEasyAuditStr("无");
		 		  }
		 		  }
		  for (int i = 0; i < list.size(); i++) {
			  BigDecimal auditId=  list.get(i).getAuditId();
			  UserPO userPo=new UserPO();
			  userPo.setUserId(auditId);
			  userPo= claimUserDao.findUserByUserId(userPo);
			  list.get(i).setAuditCode(userPo.getUserName());
			  list.get(i).setAuditName(userPo.getRealName());
			  
          } 

		return claimPrePermissionConfigBOs;
		
	}

	@Override
	public ClaimPrePermissionConfigBO saveClaimPermissionConfiguration(
			ClaimPrePermissionConfigBO claimPrePermissionConfigBO) {
		// TODO Auto-generated method stub
		ClaimPrePermissionConfigPO claimPrePermissionConfigPO = BeanUtils.copyProperties(ClaimPrePermissionConfigPO.class, claimPrePermissionConfigBO);
		
		UserPO userPo=new UserPO();
		userPo.setUserName(claimPrePermissionConfigBO.getPreAuditCode());
		UserPO findClaimUserVOByUserName = claimUserDao.findClaimUserVOByUserName(userPo);  
		if(findClaimUserVOByUserName==null){
			throw new BizException("findClaimUserVOByUserName为null");
		}
		claimPrePermissionConfigPO.setPreAuditId(findClaimUserVOByUserName.getUserId());
		
		UserPO userPoTwo=new UserPO();
		userPoTwo.setUserName(claimPrePermissionConfigBO.getAuditCode());
		UserPO findClaimUserVOByUserNameTwo = claimUserDao.findClaimUserVOByUserName(userPoTwo);  
		claimPrePermissionConfigPO.setAuditId(findClaimUserVOByUserNameTwo.getUserId());
		
		return BeanUtils.copyProperties(ClaimPrePermissionConfigBO.class, 
				iClaimPrePermissionConfigDao.addClaimPrePermissionConfig(claimPrePermissionConfigPO));
	}

	@Override
	public boolean deletePermissionConfig(
			ClaimPrePermissionConfigBO claimPrePermissionConfigBO) {
		// TODO Auto-generated method stub
		
		return iClaimPrePermissionConfigDao.deleteClaimPrePermissionConfig(BeanUtils.copyProperties(ClaimPrePermissionConfigPO.class, claimPrePermissionConfigBO));

	}

	/**
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.taskmanage.service.IClaimPermissionService#queryPermissionForPage(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO, com.nci.udmp.framework.model.CurrentPage)
	 * @param claimCaseBO
	 * @param copyCurrentPage
	 * @return 
	*/
	@Override
	public CurrentPage<ClaimCaseBO> queryPermissionForPage(ClaimCaseBO claimCaseBO,
			CurrentPage<ClaimCaseBO> copyCurrentPage) {
		// TODO Auto-generated method stub
		//ClaimCasePO claimCasePO = BeanUtils.copyProperties(NoPayPreparePO.class, noPayPrepareBO);
		ClaimCasePO claimCasePO=new ClaimCasePO();
		claimCasePO.set("start_Time",claimCaseBO.getMemoContent());
        claimCasePO.set("end_Time",claimCaseBO.getMemoOption());
        
     //   CurrentPage<ClaimCasePO>claimCasePOs= iClaimCaseDao.queryPermissionListForPage(claimCasePO, BeanUtils.copyCurrentPage(ClaimCasePO.class, copyCurrentPage));
        
        
        
		CurrentPage<ClaimCaseBO>claimCaseBOs= BeanUtils.copyCurrentPage(ClaimCaseBO.class, 
				iClaimCaseDao.queryPermissionListForPage(claimCasePO, BeanUtils.copyCurrentPage(ClaimCasePO.class, copyCurrentPage))); 
	
		List<ClaimCaseBO> list= claimCaseBOs.getPageItems();
		
		
		  for (int i = 0; i < list.size(); i++) {
			  BigDecimal auditorId=  list.get(i).getAuditorId();
			  UserPO userPo=new UserPO();
			  userPo.setUserId(auditorId);
			  userPo= claimUserDao.findUserByUserId(userPo);
			  list.get(i).setUserName(userPo.getUserName());
			  list.get(i).setAuditorName(userPo.getRealName());
			  BigDecimal preAuditId=  list.get(i).getPreAuditId();
			  UserPO userPoTwo=new UserPO();
			  userPoTwo.setUserId(preAuditId);
			  userPoTwo= claimUserDao.findUserByUserId(userPoTwo);
			  list.get(i).setPreAuditCode(userPoTwo.getUserName());
			  list.get(i).setPreAuditName(userPoTwo.getRealName());
			  
        } 
		  for (int i = 0; i < list.size(); i++) {
		  if(list.get(i).getReAuditDecision()!=null){
			  if(list.get(i).getReAuditDecision().compareTo(new BigDecimal(1))==0){
				  list.get(i).setReAuditDecisionStr("通过");
			  }else{
				  list.get(i).setReAuditDecisionStr("不通过");
			  }
		  }
			  
        } 
		
		
		return claimCaseBOs;
	}

	public IClaimPrePermissionConfigDao getiClaimPrePermissionConfigDao() {
		return iClaimPrePermissionConfigDao;
	}

	public void setiClaimPrePermissionConfigDao(
			IClaimPrePermissionConfigDao iClaimPrePermissionConfigDao) {
		this.iClaimPrePermissionConfigDao = iClaimPrePermissionConfigDao;
	}

	public IUserDao getClaimUserDao() {
		return claimUserDao;
	}

	public void setClaimUserDao(IUserDao claimUserDao) {
		this.claimUserDao = claimUserDao;
	}

	public IClaimCaseDao getiClaimCaseDao() {
		return iClaimCaseDao;
	}

	public void setiClaimCaseDao(IClaimCaseDao iClaimCaseDao) {
		this.iClaimCaseDao = iClaimCaseDao;
	}

	@Override
	public int findAllClaimPrePermissionConfig(
			ClaimPrePermissionConfigBO claimPrePermissionConfigBO)
			throws BizException {
		// TODO Auto-generated method stub
		ClaimPrePermissionConfigPO claimPrePermissionConfigPO = new ClaimPrePermissionConfigPO();
		ClaimPrePermissionConfigPO claimPrePermissionConfigPOt = BeanUtils.copyProperties(ClaimPrePermissionConfigPO.class, claimPrePermissionConfigBO);
		
	        return iClaimPrePermissionConfigDao.findClaimPrePermissionConfigTotal(claimPrePermissionConfigPOt);
		
	
	}

	@Override
	public Map<String, String> checkMthod(ClaimPrePermissionConfigBO claimPrePermissionConfigBO) {
		// TODO Auto-generated method stub	
		Map<String,String> messageMap = new HashMap<String, String>();
		
		UserPO userPoThree=new UserPO();
		userPoThree.setUserName(claimPrePermissionConfigBO.getPreAuditCode());
		UserPO findClaimUserVOByUserName = claimUserDao.findClaimUserVOByUserName(userPoThree);  
		if(findClaimUserVOByUserName.getUserId()!=null){
			if(StringUtils.isNotBlank(findClaimUserVOByUserName.getRealName())){
				if(!findClaimUserVOByUserName.getRealName().equals(claimPrePermissionConfigBO.getPreAuditName()
)){
					messageMap.put("1", "您录入的【核赔用户姓名】不符合要求，请重新录入。");	
					 return messageMap;
				}
				
			}
			
			
		}else{
			 messageMap.put("1", "您录入的【核赔用户编码】不符合要求，请重新录入。");
			 return messageMap;
		}
		UserPO userPoTwo=new UserPO();
		userPoTwo.setUserName(claimPrePermissionConfigBO.getAuditCode());
		UserPO findClaimUserVOByUserNameTwo = claimUserDao.findClaimUserVOByUserName(userPoTwo);  
		if(findClaimUserVOByUserNameTwo.getUserId()==null){
			 messageMap.put("1", "您录入的【复核用户】不符合要求，请重新录入。");
			 return messageMap;
		}
		if(findClaimUserVOByUserNameTwo.getRealName()!=null){
			if(!findClaimUserVOByUserNameTwo.getRealName().equals(claimPrePermissionConfigBO.getAuditName()
)){
				messageMap.put("1", "您录入的【复核用户姓名】不符合要求，请重新录入。");	
				 return messageMap;
				
			}
			
		}
		ClaimTaskStaffPO claimTaskStaffPO = new ClaimTaskStaffPO();
		claimTaskStaffPO.setUserId(findClaimUserVOByUserNameTwo.getUserId());
		List<ClaimTaskStaffPO>claimTaskStaffPOs= iClaimTaskStaffDao.findAllClaimTaskStaff(claimTaskStaffPO);
		if(claimTaskStaffPOs!=null && claimTaskStaffPOs.size()>0){
			ClaimTaskStaffPO claimTaskStaff=claimTaskStaffPOs.get(0);
		String	auditFrom=claimTaskStaff.getAuditFrom();
		String	auditTo=claimTaskStaff.getAuditTo();
		if(StringUtils.isNotBlank(auditFrom)&&StringUtils.isNotBlank(auditTo) ){
			
		int i=auditFrom.compareTo(claimPrePermissionConfigBO.getAuditFrom());
		int c=auditTo.compareTo(claimPrePermissionConfigBO.getAuditTo());
		if(i>0 || c<0){
			messageMap.put("1", "您录入的审核权限高于复核用户审核权限，请重新录入。");	
			 return messageMap;
		}
		
		}else{
			messageMap.put("1", "您录入的【复核用户】不符合要求，请重新录入。");	
			 return messageMap;
		}
	
			
		}else{
			messageMap.put("1", "您录入的【复核用户】不符合要求，请重新录入。");	
			 return messageMap;
		}
		messageMap.put("0", "");
		return messageMap;
	}

	
	
	

}
