package com.nci.tunan.clm.impl.parameter.service;

import com.nci.tunan.clm.interfaces.model.bo.ClaimBlackNameBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimBlackNameFailBO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 理赔黑名单Service层
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2017年12月8日 下午4:57:12
 */
public interface IClaimBlackNameService {

    /**
     * 理赔黑名单查询
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.parameter.service.IClaimBlackNameService#queryClaimBlackNameInfo(com.nci.tunan.clm.interfaces.model.bo.ClaimBlackNameBO, com.nci.udmp.framework.model.CurrentPage)
     * @param claimBlackNameBO 理赔黑名单对象
     * @param claimBlackNameBOPage 理赔黑名单集合
     * @return
     */
    public CurrentPage<ClaimBlackNameBO> queryClaimBlackNameInfo(ClaimBlackNameBO claimBlackNameBO,CurrentPage<ClaimBlackNameBO> claimBlackNameBOPage);
    
    /**
     * 删除理赔黑名单信息
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.parameter.service.IClaimBlackNameService#deletedClaimBlackNameInfo(com.nci.tunan.clm.interfaces.model.bo.ClaimBlackNameBO)
     * @param claimBlackNameBO 理赔黑名单对象
     */
    public void deletedClaimBlackNameInfo(ClaimBlackNameBO claimBlackNameBO);
    
    /**
     * 保存或者修改理赔黑名单信息
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.parameter.service.IClaimBlackNameService#saveClaimBlackNameInfo(com.nci.tunan.clm.interfaces.model.bo.ClaimBlackNameBO)
     * @param claimBlackNameBO 理赔黑名单对象
     */
    public ClaimBlackNameBO saveClaimBlackNameInfo(ClaimBlackNameBO claimBlackNameBO);
    /**
     * 上载黑名单客户文件方法
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ClaimBlackNameBO  对象
     * @return  String 提示信息
     * @throws BizException 异常
     */
	public String uploadClaimBlackName(ClaimBlackNameFailBO claimBlackNameFailBO) throws BizException;
	/**
     * 初始化上传错误信息列表展示
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ClaimBlackNameFailBO  对象
     * @return  ClaimBlackNameFailBO  对象
     */
    public CurrentPage<ClaimBlackNameFailBO> queryBlackNameFailInit(ClaimBlackNameFailBO claimBlackNameFailBO,CurrentPage<ClaimBlackNameFailBO> currentPage);

}
