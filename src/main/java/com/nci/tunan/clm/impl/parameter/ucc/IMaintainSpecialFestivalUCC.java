package com.nci.tunan.clm.impl.parameter.ucc;

import com.nci.tunan.clm.interfaces.model.vo.ClaimHolidayVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimHospitalServiceVO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 特定节假日信息实现类
 * @description
 * <AUTHOR>
 * @.belongToModule CLM-理赔系统
 * @date 2015-07-28 17:24:53
 */
public interface IMaintainSpecialFestivalUCC {
    
    /**
     * @description 根据条件分页查询已设置的特定节假日
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimHolidayVO 理赔特定节假日参数
     * @return List<claimHolidayVO> 理赔特定节假日list集合
     * @throws BizException
     */
    public CurrentPage<ClaimHolidayVO> findClaimHolidayByPages(ClaimHolidayVO claimHolidayVO,CurrentPage<ClaimHospitalServiceVO> currentPage) throws BizException;

    /**
     * @description 保存校验特定节假日是否重复
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimHolidayVO 理赔特定节假日对象
     * @return claimHolidayVO 理赔特定节假日对象
     * @throws BizException
     */
    public ClaimHolidayVO saveSpeFestivalCheck(ClaimHolidayVO claimHolidayVO) throws BizException;
    
    /**
     * @description 保存校验特定节假日是否重复
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimHolidayVO 理赔特定节假日对象
     * @throws BizException
     */
    public void updateSpeFestival(ClaimHolidayVO claimHolidayVO) throws BizException;
    
    /**
     * @description 保存校验特定节假日是否重复
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimHolidayVO 理赔特定节假日对象
     * @throws BizException
     */
    public void saveSpeFestival(ClaimHolidayVO claimHolidayVO) throws BizException;
    
    /**
     * @description 保存新增节假日信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.parameter.service.IMaintainSpecialFestivalService#saveSpeFestival(com.nci.tunan.clm.interfaces.model.bo.ClaimHolidayBO)
     * @param claimHolidayVO 理赔特定节假日对象
     * @throws BizException
     */
    public boolean deleteSpeFestival(ClaimHolidayVO claimHolidayVO) throws BizException;
    
}
