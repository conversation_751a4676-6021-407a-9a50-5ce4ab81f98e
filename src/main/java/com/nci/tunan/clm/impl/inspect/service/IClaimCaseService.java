package com.nci.tunan.clm.impl.inspect.service;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.bo.ClaimBeneBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimLiabBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimNoticePremBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimPayeeBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimPolicyHolderBO;
import com.nci.tunan.clm.interfaces.model.bo.OrgRelBO;
import com.nci.tunan.clm.interfaces.model.bo.ReportHangUpBO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimPayChangeVO;
import com.nci.tunan.clm.interfaces.model.vo.CommonMapVO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * 赔案主表接口
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-10-20 15:35:50  
 */
 public interface IClaimCaseService {
	/**
     * @description 增加数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案主表对象
     * @return ClaimCaseBO 赔案主表添加结果
     */
 	public ClaimCaseBO addClaimCase(ClaimCaseBO claimCaseBO);
 	/**
	 * 
	 * @description 查询保单挂起记录表信息
	 * @version V1.0.0 V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param reportHangUpBO 保单挂起记录
	 * @return List<ReportHangUpBO> 保单挂起记录
	 */
 	public List<ReportHangUpBO> findAllReportHangUp(ReportHangUpBO reportHangUpBO);
 	
 	 /**
     * @description 修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案主表对象
     * @return ClaimCaseBO 赔案主表修改结果
     */
 	public ClaimCaseBO updateClaimCase(ClaimCaseBO claimCaseBO);
 	
 	 /**
     * @description 删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案主表对象
     * @return boolean 删除是否成功
     */
 	public boolean deleteClaimCase(ClaimCaseBO claimCaseBO);
 	
 	/**
     * @description 查询单条数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案主表对象
     * @return ClaimCaseBO 查询赔案主表结果对象
     */
 	public ClaimCaseBO findClaimCase(ClaimCaseBO claimCaseBO);
 	
 	/**
     * @description 查询所有数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案主表对象
     * @return List<ClaimCaseBO> 查询赔案主表结果List
     */
 	public List<ClaimCaseBO> findAllClaimCase(ClaimCaseBO claimCaseBO);
 	
 	/**
     * @description 作业监控查询结案赔案
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案主表对象
     * @return List<ClaimCaseBO> 查询赔案主表结果List
     */
	public int findEndClaimCase(ClaimCaseBO  claimCaseBO);
 	
	/**
     * @description 查询数据条数
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案主表对象
     * @return int 查询结果条数
     */
 	public int findClaimCaseTotal(ClaimCaseBO claimCaseBO);
 	
 	/**
     * @description 分页查询数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案主表对象
     * @param currentPage 赔案主表当前页对象
     * @return CurrentPage<ClaimCaseBO> 查询赔案主表结果的当前页对象
     */
 	public CurrentPage<ClaimCaseBO> queryClaimCaseForPage(ClaimCaseBO claimCaseBO, CurrentPage<ClaimCaseBO> currentPage);
 	
 	 /**
     * @description 批量增加数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBOList 赔案主表对象列表
     * @return boolean 批量添加是否成功
     */
 	public boolean batchSaveClaimCase(List<ClaimCaseBO> claimCaseBOList);
 	
 	/**
     * @description 批量修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBOList 赔案主表对象列表
     * @return boolean 批量修改是否成功
     */
 	public boolean batchUpdateClaimCase(List<ClaimCaseBO> claimCaseBOList);
 	
 	/**
     * @description 批量删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBOList 赔案主表对象列表
     * @return boolean 批量删除是否成功
     */
 	public boolean batchDeleteClaimCase(List<ClaimCaseBO> claimCaseBOList);
 	
 	/**
     * @description 查询所有数据 ，重新组装为map
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseBO 赔案主表对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
 	public List<Map<String, Object>> findAllMapClaimCase(ClaimCaseBO claimCaseBO);
 	/**
	 * 查询赔案信息
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCaseBO 赔案主表对象
	 * @return ClaimCaseBO 赔案主表对象
	 */
	public ClaimCaseBO findClaimCaseByCaseNo(ClaimCaseBO claimCaseBO);
	
	/**
	 * 修改审核权限
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCaseBO 赔案主表对象
	 * @return ClaimCaseBO 赔案主表对象
	 */
	public ClaimCaseBO updateAuditPermissionByCaseId(ClaimCaseBO claimCaseBO);
	
	/**
	 * 增加审核权限
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCaseBO 赔案主表对象
	 */
	public ClaimCaseBO addAuditLevel(ClaimCaseBO claimCaseBO);
	
	/**
	 * 增加审批权限
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimCaseBO 赔案主表对象
	 */
	public void addApproveLevel(ClaimCaseBO claimCaseBO);
	
	/**
     * @description 查询所有数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimNoticePremBO 消息提醒人员配置对象
     * @return List<ClaimNoticePremPO> 查询消息提醒人员配置结果List
     */
	 public List<ClaimNoticePremBO> findAllClaimNoticePrem(ClaimNoticePremBO claimNoticePremBO); 
	 
	 /**
	 * 根据机构号查询机构信息
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param orgRelBO 机构相关信息
	 * @return OrgRelBO 机构相关信息
	 */
	public OrgRelBO findOrgRelByOrganCode(OrgRelBO orgRelBO);
	
	/**
     * @description 查询数据通过caseId
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param claimLiabBO 理赔给付责任理算对象
     * @return ClaimLiabBO 查询理赔给付责任理算结果对象
     */
    public List<ClaimLiabBO> findClaimLiaByCaseId(ClaimLiabBO claimLiabBO);
    
	public void updatePreConfirmAuditConclusion(CommonMapVO commonMapVO);
	
    /**
     * 
     * @description 查询当前赔案下所有收益人
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.audit.service.IClaimAuditConclusionService#findAllBeneByCaseId(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 主表BO
     * @return 返回值
     * @throws BizException
     */
    public List<ClaimBeneBO> findAllBeneByCaseIdNew(ClaimCaseBO claimCaseBO);
    /**
     * 
     * @description 通过赔案查询涉案保单及投保人信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.audit.service.IClaimAuditConclusionService#findAllBeneByCaseId(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 主表BO
     * @return 返回值
     * @throws BizException
     */
    public List<ClaimPolicyHolderBO> findPolicyListByCaseNo(ClaimPolicyHolderBO claimPolicyHolderBO);
    /**
     * 
     * @description 查询被保人数据条数
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.audit.service.IClaimAuditConclusionService#findAllBeneByCaseId(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 主表BO
     * @return 返回值
     * @throws BizException
     */
    public int findInsuredCountById(ClaimPolicyHolderBO claimPolicyHolderBO);
    /**
     * 
     * @description 查询受益人数据条数
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.audit.service.IClaimAuditConclusionService#findAllBeneByCaseId(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 主表BO
     * @return 返回值
     * @throws BizException
     */
    public int findBeneCountById(ClaimPolicyHolderBO claimPolicyHolderBO);
    /**
     * 
     * @description 查询领款人数据条数
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.audit.service.IClaimAuditConclusionService#findAllBeneByCaseId(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
     * @param claimCaseBO 主表BO
     * @return 返回值
     * @throws BizException
     */
    public int findPayeeCountById(ClaimPolicyHolderBO claimPolicyHolderBO);
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
    public String getBeneRole(ClaimBeneBO claimBeneBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole);
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
	public String getPayeeRole(ClaimPayeeBO claimPayeeBO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole);
	/**
     * 
     * @description 获取指定客户在保单中的客户角色
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimCaseVO 入参
     * @return 返回值
     */
	public String getPayChangeRole(ClaimPayChangeVO claimPayChangeVO,ClaimPolicyHolderBO claimPolicyHolderBO,String customerRole);
    
 }
  