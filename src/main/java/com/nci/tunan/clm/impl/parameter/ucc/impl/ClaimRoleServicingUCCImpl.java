package com.nci.tunan.clm.impl.parameter.ucc.impl;


import java.util.List;

import org.slf4j.Logger;

import com.nci.tunan.clm.impl.parameter.service.IClaimRoleServicingService;
import com.nci.tunan.clm.impl.parameter.ucc.IClaimRoleServicingUCC;
import com.nci.tunan.clm.interfaces.model.bo.ClaimRoleServicingBO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimRoleServicingVO;
import com.nci.tunan.clm.interfaces.model.vo.CommonMapVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * 理赔维护角色配置信息实现方法
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2018-06-19 10:06:06 
 */
public class ClaimRoleServicingUCCImpl   implements IClaimRoleServicingUCC  {
    /** 
     * @Fields claimRoleServicingService : 理赔维护角色配置信息注入service 
     */
	private IClaimRoleServicingService claimRoleServicingService;
	
	/** 
	 * @Fields logger :  日志工具
 	 */
	private static Logger logger = LoggerFactory.getLogger();
	
	/**
     * @description SERVICE-getter方法
     * @version
     * @title
     * <AUTHOR>
     * @return IClaimRoleServicingService service接口对象
     */
	public IClaimRoleServicingService getClaimRoleServicingService() {
		return claimRoleServicingService;
	}
	
	/**
     * @description SERVICE-setter方法
     * @version
     * @title
     * <AUTHOR>
     * @param claimRoleServicingService service对象
     */
	public void setClaimRoleServicingService(IClaimRoleServicingService claimRoleServicingService) {
		this.claimRoleServicingService = claimRoleServicingService;
	}
	
 	/**
     * @description 增加数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRoleServicingBO 理赔维护角色配置信息对象
     * @return ClaimRoleServicingBO 添加结果
     */
	public ClaimRoleServicingVO addClaimRoleServicing(ClaimRoleServicingVO claimRoleServicingVO) {
		//1.增加数据
		logger.debug("<======ClaimRoleServicingUCC--addClaimRoleServicing======>");
		ClaimRoleServicingBO claimRoleServicingBO = claimRoleServicingService.addClaimRoleServicing(BeanUtils.copyProperties(ClaimRoleServicingBO.class, claimRoleServicingVO));
		return BeanUtils.copyProperties(ClaimRoleServicingVO.class, claimRoleServicingBO);
	}
	
	 /**
     * @description 修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRoleServicingBO 理赔维护角色配置信息对象
     * @return ClaimRoleServicingBO 修改结果
     */
	public ClaimRoleServicingVO updateClaimRoleServicing(ClaimRoleServicingVO claimRoleServicingVO)  {
		//1.修改数据
		logger.debug("<======ClaimRoleServicingUCC--updateClaimRoleServicing======>");
		ClaimRoleServicingBO claimRoleServicingBO = claimRoleServicingService.updateClaimRoleServicing(BeanUtils.copyProperties(ClaimRoleServicingBO.class, claimRoleServicingVO));
		return BeanUtils.copyProperties(ClaimRoleServicingVO.class, claimRoleServicingBO);
	}

	 /**
     * @description 删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRoleServicingBO 理赔维护角色配置信息对象
     * @return boolean 删除是否成功
     */
	public boolean deleteClaimRoleServicing(ClaimRoleServicingVO claimRoleServicingVO)  {
		//1.删除数据
		logger.debug("<======ClaimRoleServicingUCC--deleteClaimRoleServicing======>");
		return claimRoleServicingService.deleteClaimRoleServicing(BeanUtils.copyProperties(ClaimRoleServicingBO.class, claimRoleServicingVO));
	}
	
	 /**
     * @description 查询单条数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRoleServicingBO 理赔维护角色配置信息对象
     * @return ClaimRoleServicingBO 查询结果对象
     */
	public ClaimRoleServicingVO findClaimRoleServicing(ClaimRoleServicingVO claimRoleServicingVO) {
		//1.查询单条数据
		logger.debug("<======ClaimRoleServicingUCC--findClaimRoleServicing======>");
		ClaimRoleServicingBO claimRoleServicingBackBO = claimRoleServicingService.findClaimRoleServicing(BeanUtils.copyProperties(ClaimRoleServicingBO.class, claimRoleServicingVO));
		ClaimRoleServicingVO claimRoleServicingBackVO = BeanUtils.copyProperties(ClaimRoleServicingVO.class, claimRoleServicingBackBO);
		return claimRoleServicingBackVO;
	}  
	
	/**
     * @description 查询所有数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRoleServicingBO 理赔维护角色配置信息对象
     * @return List<ClaimRoleServicingBO> 查询结果List
     */
	public List<ClaimRoleServicingVO> findAllClaimRoleServicing(ClaimRoleServicingVO claimRoleServicingVO) {
		//1.查询所有数据
		logger.debug("<======ClaimRoleServicingUCC--findAllClaimRoleServicing======>");
		ClaimRoleServicingBO claimRoleServicingBO = BeanUtils.copyProperties(ClaimRoleServicingBO.class, claimRoleServicingVO);
		return BeanUtils.copyList(ClaimRoleServicingVO.class, claimRoleServicingService.findAllClaimRoleServicing(claimRoleServicingBO));
	} 
	
	 /**
     * @description 分页查询数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRoleServicingBO 理赔维护角色配置信息对象
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimRoleServicingBO> 查询结果的当前页对象
     */
	public CurrentPage<ClaimRoleServicingVO> queryClaimRoleServicingForPage(ClaimRoleServicingVO claimRoleServicingVO, CurrentPage<ClaimRoleServicingVO> currentPage) {
		//1.分页查询数据
		logger.debug("<======ClaimRoleServicingUCC--queryClaimRoleServicingForPage======>");
		ClaimRoleServicingBO claimRoleServicingBO = BeanUtils.copyProperties(ClaimRoleServicingBO.class, claimRoleServicingVO);
		return BeanUtils.copyCurrentPage(ClaimRoleServicingVO.class, claimRoleServicingService.queryClaimRoleServicingForPage(claimRoleServicingBO, BeanUtils.copyCurrentPage(ClaimRoleServicingBO.class, currentPage)));
	}
	
	/**
     * @description 角色维护界面查询按钮
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimRoleServicingVO 角色维护对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	public CommonMapVO queryClaimRole(CommonMapVO commonMapVO){
		ClaimRoleServicingVO claimRoleServicingVO = (ClaimRoleServicingVO)commonMapVO.get("claimRoleServicingVO");
		CurrentPage<ClaimRoleServicingVO> currentPage = (CurrentPage<ClaimRoleServicingVO>)commonMapVO.get("currentPage");
		//@invalid 查询结果
		ClaimRoleServicingBO claimRoleServicingBO = BeanUtils.copyProperties(ClaimRoleServicingBO.class, claimRoleServicingVO);
		CurrentPage<ClaimRoleServicingBO> currentPageBO = claimRoleServicingService.queryClaimRoleServicingForPage(
				claimRoleServicingBO, BeanUtils.copyCurrentPage(ClaimRoleServicingBO.class, currentPage));
		//1. 查询所有角色列表
		ClaimRoleServicingBO claimRoleBO = new ClaimRoleServicingBO();
		List<ClaimRoleServicingBO> claimRoleServicingBOList = claimRoleServicingService.findAllClaimRoleServicing(claimRoleBO);
		//@invalid 返回赋值
		CommonMapVO commonMapVORes = new CommonMapVO();
		commonMapVORes.put("currentPage", BeanUtils.copyCurrentPage(ClaimRoleServicingVO.class, currentPageBO));
		commonMapVORes.put("claimRoleServicingVOList", BeanUtils.copyList(ClaimRoleServicingVO.class, claimRoleServicingBOList));
		commonMapVORes.put("claimRoleServicingVO", claimRoleServicingVO);
		return commonMapVORes;
	}
	/**
     * @description 角色维护界面保存按钮
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param claimRoleServicingVO 角色维护对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	public CommonMapVO saveOrUpdateClaimRole(CommonMapVO commonMapVO){
		ClaimRoleServicingVO claimRoleServicingVO = (ClaimRoleServicingVO)commonMapVO.get("claimRoleServicingVO");
		//角色维护界面保存操作
		//@invalid 更新操作
		if(claimRoleServicingVO.getClaimRoleId() != null){
			ClaimRoleServicingBO claimRoleServicingBO = new ClaimRoleServicingBO();
			claimRoleServicingBO.setClaimRoleId(claimRoleServicingVO.getClaimRoleId());
			List<ClaimRoleServicingBO> claimRoleServicingBOList =  claimRoleServicingService.findAllClaimRoleServicing(
					claimRoleServicingBO);
			if(CollectionUtilEx.isNotEmpty(claimRoleServicingBOList)){
				claimRoleServicingBO = claimRoleServicingBOList.get(0);
				claimRoleServicingBO.setApproveDifficultFrom(claimRoleServicingVO.getApproveDifficultFrom());
				claimRoleServicingBO.setApproveDifficultTo(claimRoleServicingVO.getApproveDifficultTo());
				if(claimRoleServicingVO.getApproveDifficultFrom() != null && "CLM-MAX".equals(claimRoleServicingVO.getApproveDifficultFrom())){
					claimRoleServicingBO.setApproveDifficultTo(claimRoleServicingVO.getApproveDifficultFrom());
				}
				claimRoleServicingBO.setApproveFrom(claimRoleServicingVO.getApproveFrom());
				claimRoleServicingBO.setApproveTo(claimRoleServicingVO.getApproveTo());
				if(claimRoleServicingVO.getApproveFrom() != null && "CLM-MAX".equals(claimRoleServicingVO.getApproveFrom())){
					claimRoleServicingBO.setApproveTo(claimRoleServicingVO.getApproveFrom());
				}
				claimRoleServicingBO.setAuditFrom(claimRoleServicingVO.getAuditFrom());
				claimRoleServicingBO.setAuditTo(claimRoleServicingVO.getAuditTo());
				if(claimRoleServicingVO.getAuditFrom() != null && "CLM-MAX".equals(claimRoleServicingVO.getAuditFrom())){
					claimRoleServicingBO.setAuditTo(claimRoleServicingVO.getAuditFrom());
				}
				claimRoleServicingBO.setClaimTypeList(claimRoleServicingVO.getClaimTypeList());
				claimRoleServicingBO.setEasyAuditPermission(claimRoleServicingVO.getEasyAuditPermission());
				claimRoleServicingBO.setIsWorking(claimRoleServicingVO.getIsWorking());
				claimRoleServicingService.updateClaimRoleServicing(claimRoleServicingBO);
			}
		}else{
			ClaimRoleServicingBO claimRoleServicingBO = new ClaimRoleServicingBO();
			claimRoleServicingBO.setClaimRoleCode(claimRoleServicingVO.getClaimRoleCode());
			List<ClaimRoleServicingBO> list = claimRoleServicingService.findAllClaimRoleServicing(claimRoleServicingBO);
			if(CollectionUtilEx.isNotEmpty(list)){
				commonMapVO.put("repeatFlag", true);
			}else{
				claimRoleServicingService.addClaimRoleServicing(
						BeanUtils.copyProperties(ClaimRoleServicingBO.class, claimRoleServicingVO));
			}
		}
		return commonMapVO;
	}
	
}
