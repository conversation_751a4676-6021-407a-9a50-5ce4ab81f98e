package com.nci.tunan.clm.impl.peripheral.service.r00101000062.impl;

import java.util.List;

import com.nci.tunan.clm.dao.IClaimChecklistDao;
import com.nci.tunan.clm.impl.peripheral.service.r00101000062.IClaimBillQueryService;
import com.nci.tunan.clm.interfaces.model.po.ClaimChecklistPO;
/**
 * 
 * @description 理赔单证信息查询
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/理赔单证信息
 * @date  2015年5月15日 上午10:08:31
 */
public class ClaimBillQueryServiceImpl implements IClaimBillQueryService{
	/**
	 * 理赔单证信息查询Dao
	 */
	private IClaimChecklistDao claimChecklistDao;
	/**
	 * 
	 * @description 查询单证信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.peripheral.service.r00101000062.IClaimBillQueryService#queryBillClaim(com.nci.tunan.clm.interfaces.model.po.ClaimChecklistPO)
	 * @param claimChecklistPO 单证信息入参
	 * @return List<ClaimChecklistPO> 结果集出参
	 */
	@Override
	public List<ClaimChecklistPO> queryBillClaim(ClaimChecklistPO claimChecklistPO) {
		//1.判断入参是否为空
		//2.通过报案号和客户号查询单证信息
		if(claimChecklistPO == null || claimChecklistPO.getData().get("caseNo") == null){
		    return null;
		}
//@invalid 		if("1".equals(claimChecklistPO.getData().get("isMigrate"))){
//@invalid 			return  claimChecklistDao.queryAllClaimChecklist2(claimChecklistPO);
//	@invalid 	}else{
			return  claimChecklistDao.queryAllClaimChecklist(claimChecklistPO);
//	@invalid 	}
	}
	
	public IClaimChecklistDao getClaimChecklistDao() {
		return claimChecklistDao;
	}

	public void setClaimChecklistDao(IClaimChecklistDao claimChecklistDao) {
		this.claimChecklistDao = claimChecklistDao;
	} 

}
