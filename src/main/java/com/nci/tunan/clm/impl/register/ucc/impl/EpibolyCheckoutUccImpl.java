package com.nci.tunan.clm.impl.register.ucc.impl;

import java.util.List;

import com.nci.tunan.clm.impl.register.service.IEpibolyCheckoutService;
import com.nci.tunan.clm.impl.register.ucc.IEpibolyCheckoutUCC;
import com.nci.tunan.clm.interfaces.model.bo.OutsourceDataCheckDefineBO;
import com.nci.tunan.clm.interfaces.model.vo.OutsourceDataCheckDefineVO;
import com.nci.udmp.util.bean.BeanUtils;

/** 
 * @description 外包数据校验定义UccImpl
 * <AUTHOR> <EMAIL>
 * @date 2015-05-15
 * @.belongToModule CLM-理赔系统 /外包数据校验定义
*/
public class EpibolyCheckoutUccImpl implements IEpibolyCheckoutUCC {

    /** 
    * @Fields epibolyCheckoutService : 外包数据校验定义Service
    */ 
    private IEpibolyCheckoutService epibolyCheckoutService;

    public IEpibolyCheckoutService getEpibolyCheckoutService() {
        return epibolyCheckoutService;
    }

    public void setEpibolyCheckoutService(IEpibolyCheckoutService epibolyCheckoutService) {
        this.epibolyCheckoutService = epibolyCheckoutService;
    }

    /**
     * @description 修改外包数据校验定义
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IEpibolyCheckoutUCC#batchUpdateOutsourceDataCheckDefine(com.nci.tunan.clm.interfaces.model.vo.OutsourceDataCheckDefineVO)
     * @param outsourceDataCheckDefineVO 外包数据
     * @return  修改成功标识
    */
    @Override
    public boolean batchUpdateOutsourceDataCheckDefine(OutsourceDataCheckDefineVO outsourceDataCheckDefineVO) {
        //修改外包数据校验定义 
    	return epibolyCheckoutService.batchUpdateOutsourceDataCheckDefine(BeanUtils.copyProperties(OutsourceDataCheckDefineBO.class, outsourceDataCheckDefineVO));
    }

    /**
     * @description 根据必填标识查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.ucc.IEpibolyCheckoutUCC#findAllByFlag(com.nci.tunan.clm.interfaces.model.vo.OutsourceDataCheckDefineVO)
     * @param outsourceDataCheckDefineVO 外包数据
     * @return  必填标识查询数据
    */
    @Override
    public List<OutsourceDataCheckDefineVO> findAllByFlag(OutsourceDataCheckDefineVO outsourceDataCheckDefineVO) {
    	//根据必填标识查询数据
    	List<OutsourceDataCheckDefineBO> bolist = epibolyCheckoutService.findAllByFlag(BeanUtils.copyProperties(OutsourceDataCheckDefineBO.class, outsourceDataCheckDefineVO));
        return BeanUtils.copyList(OutsourceDataCheckDefineVO.class, bolist);
    }
    
}
