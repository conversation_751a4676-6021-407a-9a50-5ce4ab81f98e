package com.nci.tunan.clm.impl.report.ucc.impl;


import org.slf4j.Logger;

import com.nci.tunan.clm.impl.report.service.IClaimReportHourEfficiencyService;
import com.nci.tunan.clm.impl.report.ucc.IClaimReportHourEfficiencyUCC;
import com.nci.tunan.clm.interfaces.model.bo.ClaimReportHourEfficiencyBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimReportHourEfficiencyDiscussBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimReportHourEfficiencyProblemMemoBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimReportHourEfficiencySecondaryUWBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimReportHourEfficiencySupDocumentsBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimReportHourEfficiencySurveyBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimReportHourEfficiencyTreatyTalkBO;
import com.nci.tunan.clm.interfaces.model.bo.LreportBO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimReportHourEfficiencyDiscussVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimReportHourEfficiencyProblemMemoVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimReportHourEfficiencySecondaryUWVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimReportHourEfficiencySupDocumentsVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimReportHourEfficiencySurveyVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimReportHourEfficiencyTreatyTalkVO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimReportHourEfficiencyVO;
import com.nci.tunan.clm.interfaces.model.vo.LreportVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * 时效清单查询
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统 --时效清单查询
 * @date 2015年5月15日 下午2:30:31
 */
public class ClaimReportHourEfficiencyUCCImpl implements IClaimReportHourEfficiencyUCC {
	
	/** 
	 * @Fields logger :  日志工具
 	 */
	private static Logger logger = LoggerFactory.getLogger();
	
    /** 
     * @Fields claimReportHourEfficiencyService : 注入service 
     */
	private IClaimReportHourEfficiencyService claimReportHourEfficiencyService;
	
	/**
     * 时效清单分页查询数据
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimReportHourEfficiencyBO 时效清单对象
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimReportHourEfficiencyBO>  查询结果的当前页对象
     */
	public CurrentPage<ClaimReportHourEfficiencyVO> queryHourEfficiencyData(ClaimReportHourEfficiencyVO claimReportHourEfficiencyVO, CurrentPage<ClaimReportHourEfficiencyVO> currentPage) {
	    //时效清单分页查询数据
		logger.debug("<======ClaimReportHourEfficiencyUCCImpl--queryHourEfficiencyData======>");
		ClaimReportHourEfficiencyBO claimReportHourEfficiencyBO = BeanUtils.copyProperties(ClaimReportHourEfficiencyBO.class, claimReportHourEfficiencyVO);
		return BeanUtils.copyCurrentPage(ClaimReportHourEfficiencyVO.class, 
		        claimReportHourEfficiencyService.queryHourEfficiencyData(claimReportHourEfficiencyBO, BeanUtils.copyCurrentPage(ClaimReportHourEfficiencyBO.class, currentPage)));
	}
	
	/**
     * 时效清单调查分页查询页面
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimReportHourEfficiencySurveyBO 时效清单调查对象
     * @param currentPage 分页
     * @return CurrentPage<ClaimReportHourEfficiencySurveyBO> 查询结果的当前页对象
     */
	public CurrentPage<ClaimReportHourEfficiencySurveyVO> queryHourEfficiencySurveyData(ClaimReportHourEfficiencySurveyVO claimReportHourEfficiencySurveyVO, 
	        CurrentPage<ClaimReportHourEfficiencySurveyVO> currentPage) {
	    //时效清单调查分页查询页面
		logger.debug("<======ClaimReportHourEfficiencyUCCImpl--queryHourEfficiencySurveyData======>");
		ClaimReportHourEfficiencySurveyBO claimReportHourEfficiencySurveyBO = BeanUtils.copyProperties(ClaimReportHourEfficiencySurveyBO.class, claimReportHourEfficiencySurveyVO);
		return BeanUtils.copyCurrentPage(ClaimReportHourEfficiencySurveyVO.class, 
		        claimReportHourEfficiencyService.queryHourEfficiencySurveyData(claimReportHourEfficiencySurveyBO, BeanUtils.copyCurrentPage(ClaimReportHourEfficiencySurveyBO.class, currentPage)));
	}
	
	/**
     * 时效二核清单查询
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimReportHourEfficiencySecondaryUWBO 时效二核清单查询对象
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimReportHourEfficiencySecondaryUWBO>  查询结果的当前页对象
     */
	public CurrentPage<ClaimReportHourEfficiencySecondaryUWVO> queryHourEfficiencySecondaryUWData(ClaimReportHourEfficiencySecondaryUWVO claimReportHourEfficiencySecondaryUWVO, 
	        CurrentPage<ClaimReportHourEfficiencySecondaryUWVO> currentPage) {
	    //时效二核清单查询
		logger.debug("<======ClaimReportHourEfficiencyUCCImpl--queryHourEfficiencySecondaryUWData======>");
		ClaimReportHourEfficiencySecondaryUWBO claimReportHourEfficiencySecondaryUWBO = BeanUtils.copyProperties(ClaimReportHourEfficiencySecondaryUWBO.class, claimReportHourEfficiencySecondaryUWVO);
		return BeanUtils.copyCurrentPage(ClaimReportHourEfficiencySecondaryUWVO.class, claimReportHourEfficiencyService.queryHourEfficiencySecondaryUWData
		        (claimReportHourEfficiencySecondaryUWBO, BeanUtils.copyCurrentPage(ClaimReportHourEfficiencySecondaryUWBO.class, currentPage)));
	}
	
	/**
     * 时效补充单证清单分页查询页面
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimReportHourEfficiencySupDocumentsBO 时效补充单证清单对象
     * @param currentPage 当前页对象
     * @return  CurrentPage<ClaimReportHourEfficiencySupDocumentsBO> 查询结果的当前页对象
     */
	public CurrentPage<ClaimReportHourEfficiencySupDocumentsVO> queryHourEfficiencySupDocumentsData(ClaimReportHourEfficiencySupDocumentsVO claimReportHourEfficiencySupDocumentsVO, 
	        CurrentPage<ClaimReportHourEfficiencySupDocumentsVO> currentPage) {
	    //时效补充单证清单分页查询页面
		logger.debug("<======ClaimReportHourEfficiencyUCCImpl--queryHourEfficiencySupDocumentsData======>");
		ClaimReportHourEfficiencySupDocumentsBO claimReportHourEfficiencySupDocumentsBO = BeanUtils.copyProperties
		        (ClaimReportHourEfficiencySupDocumentsBO.class, claimReportHourEfficiencySupDocumentsVO);
		return BeanUtils.copyCurrentPage(ClaimReportHourEfficiencySupDocumentsVO.class, claimReportHourEfficiencyService.queryHourEfficiencySupDocumentsData
		        (claimReportHourEfficiencySupDocumentsBO, BeanUtils.copyCurrentPage(ClaimReportHourEfficiencySupDocumentsBO.class, currentPage)));
	}
	
	/**
     * 时效合议清单分页查询
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimReportHourEfficiencyDiscussBO 时效合议清单对象
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimReportHourEfficiencyDiscussBO> 查询结果的当前页对象
     */
	public CurrentPage<ClaimReportHourEfficiencyDiscussVO> queryHourEfficiencyDiscussData(ClaimReportHourEfficiencyDiscussVO claimReportHourEfficiencyDiscussVO, 
	        CurrentPage<ClaimReportHourEfficiencyDiscussVO> currentPage) {
	    //时效合议清单分页查询
		logger.debug("<======ClaimReportHourEfficiencyUCCImpl--queryHourEfficiencyDiscussData======>");
		ClaimReportHourEfficiencyDiscussBO claimReportHourEfficiencyDiscussBO = BeanUtils.copyProperties(ClaimReportHourEfficiencyDiscussBO.class, claimReportHourEfficiencyDiscussVO);
		return BeanUtils.copyCurrentPage(ClaimReportHourEfficiencyDiscussVO.class, 
		        claimReportHourEfficiencyService.queryHourEfficiencyDiscussData(claimReportHourEfficiencyDiscussBO, BeanUtils.copyCurrentPage(ClaimReportHourEfficiencyDiscussBO.class, currentPage)));
	}
	
	/**
     * 时效问题件清单分页查询
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimReportHourEfficiencyProblemMemoBO 时效问题件清单对象
     * @param currentPage   当前页对象
     * @return CurrentPage<ClaimReportHourEfficiencyProblemMemoBO> 查询结果的当前页对象
     */
	public CurrentPage<ClaimReportHourEfficiencyProblemMemoVO> queryHourEfficiencyProblemMemoData(ClaimReportHourEfficiencyProblemMemoVO claimReportHourEfficiencyProblemMemoVO, 
	        CurrentPage<ClaimReportHourEfficiencyProblemMemoVO> currentPage) {
	    //时效问题件清单分页查询
		logger.debug("<======ClaimReportHourEfficiencyUCCImpl--queryHourEfficiencyProblemMemoData======>");
		ClaimReportHourEfficiencyProblemMemoBO claimReportHourEfficiencyProblemMemoBO = BeanUtils.copyProperties(ClaimReportHourEfficiencyProblemMemoBO.class, claimReportHourEfficiencyProblemMemoVO);
		return BeanUtils.copyCurrentPage(ClaimReportHourEfficiencyProblemMemoVO.class,claimReportHourEfficiencyService.queryHourEfficiencyProblemMemoData
		        (claimReportHourEfficiencyProblemMemoBO, BeanUtils.copyCurrentPage(ClaimReportHourEfficiencyProblemMemoBO.class, currentPage)));
	}
	
	/**
     * 时效协谈清单分页查询
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimReportHourEfficiencyTreatyTalkBO 时效协谈清单对象
     * @param currentPage  当前页对象
     * @return CurrentPage<ClaimReportHourEfficiencyTreatyTalkBO> 查询结果的当前页对象
     */
	public CurrentPage<ClaimReportHourEfficiencyTreatyTalkVO> queryHourEfficiencyTreatyTalkData(ClaimReportHourEfficiencyTreatyTalkVO claimReportHourEfficiencyTreatyTalkVO, 
	        CurrentPage<ClaimReportHourEfficiencyTreatyTalkVO> currentPage) {
	    //时效协谈清单分页查询
		logger.debug("<======ClaimReportHourEfficiencyUCCImpl--queryHourEfficiencyTreatyTalkData======>");
		ClaimReportHourEfficiencyTreatyTalkBO claimReportHourEfficiencyTreatyTalkBO = BeanUtils.copyProperties(ClaimReportHourEfficiencyTreatyTalkBO.class, claimReportHourEfficiencyTreatyTalkVO);
		return BeanUtils.copyCurrentPage(ClaimReportHourEfficiencyTreatyTalkVO.class, claimReportHourEfficiencyService.queryHourEfficiencyTreatyTalkData
		        (claimReportHourEfficiencyTreatyTalkBO, BeanUtils.copyCurrentPage(ClaimReportHourEfficiencyTreatyTalkBO.class, currentPage)));
	}
	

	public IClaimReportHourEfficiencyService getClaimReportHourEfficiencyService() {
		return claimReportHourEfficiencyService;
	}

	public void setClaimReportHourEfficiencyService(
			IClaimReportHourEfficiencyService claimReportHourEfficiencyService) {
		this.claimReportHourEfficiencyService = claimReportHourEfficiencyService;
	}

	 /**
     * 历史报案信息查询
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param historyReportInforMationBO 历史报案对象
     * @param currentPage  当前页对象
     * @return CurrentPage<LreportBO>  查询结果的当前页对象
     */
	@Override
	public CurrentPage<LreportVO> findHistoryreportinformation(
			LreportVO lreport,
			CurrentPage<LreportVO> currentPage) {
		//历史报案信息查询 
		CurrentPage<LreportBO> currentPageres = claimReportHourEfficiencyService.findHistoryreportinformation(BeanUtils.copyProperties(LreportBO.class, lreport), 
				BeanUtils.copyCurrentPage(LreportBO.class, currentPage));
		 return BeanUtils.copyCurrentPage(LreportVO.class, currentPageres);
	}

}
