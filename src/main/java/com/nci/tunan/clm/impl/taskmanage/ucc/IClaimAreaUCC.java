package com.nci.tunan.clm.impl.taskmanage.ucc;

import java.util.List;

import com.nci.tunan.clm.interfaces.model.vo.ClaimAreaVO;
import com.nci.tunan.clm.interfaces.model.vo.CommonMapVO;

/**
 * 
 * @description UCC层片区管理接口
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/片区管理
 * @date 2017年8月28日 上午11:41:55
 */
public interface IClaimAreaUCC {
    /**
     * 
     * @description 查询所有片区信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAreaVO 片区VO
     * @return List<ClaimAreaVO> 符合条件的片区集合
     */
	public List<ClaimAreaVO> findAll(ClaimAreaVO claimAreaVO);
	/**
     * 
     * @description 更新片区信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAreaVO 片区VO
     * @return ClaimAreaVO 片区VO
     */
	public ClaimAreaVO updateClaimArea(ClaimAreaVO claimAreaVO);
	/**
     * 
     * @description 新增片区
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAreaVO 片区VO
     * @return ClaimAreaVO 片区VO
     */
	public ClaimAreaVO addClaimArea(ClaimAreaVO claimAreaVO);
	/**
     * 
     * @description 批量删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimAreaVOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteClaimArea(List<ClaimAreaVO> claimAreaVOList);
	 /**
     * 
     * @description 编辑片区信息，包括添加和修改
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param parameterVO 需要操作的参数放入Map集合中
     * @return CommonMapVO Map集合
     */
	 public CommonMapVO updateEditArea(CommonMapVO parameterVO);
	 /**
     * 
     * @description 片区机构页面初始化
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param parameterVO 需要操作的参数放入Map集合中
     * @return CommonMapVO Map集合
     */
	public CommonMapVO updateAreaOrganInit(CommonMapVO parameterVO);
	
	/**
     * 
     * @description 判断用户所属的机构是否有片区
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param organCode 机构号
     * @return String 如果有返回"1"，无返回"0"
     */
	public String judgeAreaOrgan(String organCode);
}
  