package com.nci.tunan.clm.impl.peripheral.service.r00101000104;

import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000104.vo.InputData;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000104.vo.OutputData;
/**
 * 
 * @description 理赔信息查询接口
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/理赔信息查询
 * @date  2015年5月15日 上午10:08:31
 */
public interface IQueryClaimCaseInfoService {
	/**
	 * 
	 * @description 查询理赔信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param inputData 查询入参
	 * @return OutputData 理赔信息返回出参
	 */
	public OutputData queryClaimCaseInfo(InputData inputData);
}
