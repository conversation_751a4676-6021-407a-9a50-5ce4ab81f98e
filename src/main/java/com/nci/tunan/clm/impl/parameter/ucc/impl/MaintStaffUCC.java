package com.nci.tunan.clm.impl.parameter.ucc.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;

import com.nci.core.common.factory.BOServiceFactory;
import com.nci.core.common.interfaces.vo.UserTelCheckVO;
import com.nci.tunan.clm.impl.parameter.service.IMaintStaffService;
import com.nci.tunan.clm.impl.parameter.ucc.IMaintStaffUCC;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.impl.util.CodeUtils;
import com.nci.tunan.clm.interfaces.model.bo.ClaimCareServerBO;
import com.nci.tunan.clm.interfaces.model.bo.OrgBO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimCareServerVO;
import com.nci.tunan.clm.interfaces.model.vo.CommonMapVO;
import com.nci.tunan.clm.interfaces.model.vo.OrgVO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

import jxl.Workbook;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.UnderlineStyle;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

/**
 * 
 * @description 理赔医院信息参数实现方法
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统-医院信息
 * @date  2015年5月15日 上午10:08:31
 */
public class MaintStaffUCC implements IMaintStaffUCC {

    /**
     * @Fields logger : 日志工具
     */
    private static Logger logger = LoggerFactory.getLogger();
    /**
     * 理赔医院信息参数实现类注入
     */
	 private IMaintStaffService maintStaffService;
	
    public IMaintStaffService getMaintStaffService() {
		return maintStaffService;
	}

	public void setMaintStaffService(IMaintStaffService maintStaffService) {
		this.maintStaffService = maintStaffService;
	}


    /**
     * @description 分页查询数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param careServerAVO 理赔医院信息参数对象
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimPhoneServiceBO> 查询结果的当前页对象
     */
    public CurrentPage<ClaimCareServerVO> queryMaintStaffForPage(ClaimCareServerVO careServerVO,
            CurrentPage<ClaimCareServerVO> currentPage) {
//        分页查询数据
        logger.debug("<======MaintStaffUCC--queryMaintStaffForPage======>");
        ClaimCareServerBO careServerBO = BeanUtils.copyProperties(ClaimCareServerBO.class,
        		careServerVO);
        return  BeanUtils.copyCurrentPage(
                ClaimCareServerVO.class,
                maintStaffService.queryMaintStaffForPage(careServerBO,
                        BeanUtils.copyCurrentPage(ClaimCareServerBO.class, currentPage)));
    }
 	/**
     * @description 增加数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param careServerAVO 理赔医院信息参数对象
     * @return careServerAVO 添加结果
     */
    public int addStaff(ClaimCareServerVO careServerAVO) {
    	//增加数据
        ClaimCareServerBO careServerABO = BeanUtils.copyProperties(ClaimCareServerBO.class,
    			careServerAVO);
    	maintStaffService.addClaimStaffService(careServerABO);
    	return 1;
    }
    
	 /**
     * @description 修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ClaimCareServerVO 理赔医院信息参数对象
     * @return ClaimCareServerVO 修改结果
     */
    public boolean updateStaff(ClaimCareServerVO careServerVO) {
//        修改数据
        
    	maintStaffService.updateClaimStaffService(BeanUtils.copyProperties(ClaimCareServerBO.class,
    			careServerVO));

        return true;
    }
	 /**
     * @description 查询单条数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param ClaimCareServerVO 理赔医院信息参数对象
     * @return ClaimCareServerVO 查询结果对象
     */
    public ClaimCareServerVO findStaffAllInfoByKey(ClaimCareServerVO careServerVO) {
//        查询单条数据
        ClaimCareServerBO careServerBackBO = maintStaffService.findStaffAllInfoByKey(BeanUtils.copyProperties(ClaimCareServerBO.class,
                careServerVO));
    	ClaimCareServerVO careServerBackVO = BeanUtils.copyProperties(ClaimCareServerVO.class,careServerBackBO);
        return careServerBackVO;
    }
	/**
	 * 维护服务人员信息查询
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.parameter.service.IMaintStaffService#getList(com.nci.tunan.clm.interfaces.model.bo.ClaimCareServerBO)
	 * @param careServerVO 维护服务人员信息参数
	 * @return List<careServerVO> 维护服务人员信息list集合
	 * @throws BizException
	 */
	public List<ClaimCareServerVO> getList(ClaimCareServerVO careServerVO) throws BizException {
//	    维护服务人员信息查询
	    ClaimCareServerBO careServerBO = new ClaimCareServerBO();
		careServerBO = BeanUtils.copyProperties(ClaimCareServerBO.class, careServerVO);
		List<ClaimCareServerBO> careServerBOList = maintStaffService.getList(careServerBO);
		List<ClaimCareServerVO> careServerVOList = BeanUtils.copyList(ClaimCareServerVO.class, careServerBOList);
		return careServerVOList;
	}
	/**
	 * 机构信息查询
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.parameter.service.IMaintStaffService#findOrgNameByOrganCode(com.nci.tunan.clm.interfaces.model.bo.OrgBO)
	 * @param OrgVO 机构信息参数		
	 * @return OrgVO 机构信息
	 * @throws BizException
	 */
    public OrgVO findOrgNameByOrganCode(OrgVO orgVO) throws BizException {
//        机构信息查询
        OrgBO orgBO = maintStaffService.findOrgNameByOrganCode(BeanUtils.copyProperties(OrgBO.class, orgVO));
        return BeanUtils.copyProperties(OrgVO.class, orgBO);
    }
	 /**
     * @description 修改单条数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param commonMapVO 理赔医院信息参数对象
     * @return CommonMapVO 修改结果对象
     */
	@Override
	public CommonMapVO updateStaffService(CommonMapVO commonMapVO) {
//	    修改单条数据
		String flag=(String) commonMapVO.get("flag");
		ClaimCareServerVO careServerVO=(ClaimCareServerVO) commonMapVO.get("claimCareServerVO");
		
		ClaimCareServerVO claimCareServerVO=null;
		if("update".equals(flag)){
			claimCareServerVO=findStaffAllInfoByKey(careServerVO);
		}else{
			updateStaff(careServerVO);
		}
		
		CommonMapVO CommonMapVO=new CommonMapVO();
        CommonMapVO.put("flag", flag);
        CommonMapVO.put("claimCareServerVO", claimCareServerVO);
		return CommonMapVO;
	}
	 /**
     * @description 修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param commonMapVO 理赔医院信息参数对象
     */
	@Override
	public void updateStaffToService(CommonMapVO commonMapVO) {
	    String addFlag=(String) commonMapVO.get("addFlag");
        String editFlag=(String) commonMapVO.get("editFlag");
        ClaimCareServerVO careServerAVO=(ClaimCareServerVO) commonMapVO.get("careServerAVO");
	    //1 调业务公共接口校验服务人员的电话和手机
        boolean checkuser = true;
        if (careServerAVO.getMobile() != null && !careServerAVO.getMobile().equals("")) {
          //1.1@invalid 接口入参赋值
            UserTelCheckVO userTelCheckVO = new UserTelCheckVO();
            userTelCheckVO.setTelNo(careServerAVO.getMobile().trim().replace(" ", "")); //1.2 手机号
            userTelCheckVO.setTelType("2"); //1.3 手机号2 固定电话1
            //1.4@invalid 调业务公共接口
            checkuser = BOServiceFactory.userTelCheck(userTelCheckVO);
            if (!checkuser) {
                throw new BizException("服务人员手机号验证错误，请检查！");
            }  
        }
        if (careServerAVO.getPhone() != null && !careServerAVO.getPhone().equals("")) {
            UserTelCheckVO userTelCheckVO = new UserTelCheckVO();
            userTelCheckVO.setTelNo(careServerAVO.getPhone().trim().replace(" ", "")); //@invalid 固定电话
            userTelCheckVO.setTelType("1"); //@invalid 手机号2 固定电话1
            //@invalid 调业务公共接口
            checkuser = BOServiceFactory.userTelCheck(userTelCheckVO);
            if (!checkuser) {
                throw new BizException("服务人员电话验证错误，请检查！");
            }
        }
		//2 修改
		if("1".equals(editFlag)){
			updateStaff(careServerAVO);
		}
		//3 新增
		if("1".equals(addFlag)){
			addStaff(careServerAVO);
		}
	}
	/**
	 * 导出服务人员信息清单模板
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.parameter.ucc.IMaintStaffUCC#outputStaffToService(com.nci.tunan.clm.interfaces.model.vo.CommonMapVO)
	 * @param commonMapVO 页面参数VO
	 */
	@Override
	public void outputStaffToService(CommonMapVO commonMapVO) {

		ClaimCareServerVO careServerAVO=(ClaimCareServerVO) commonMapVO.get("claimCareServerVO");
		HttpServletResponse response=(HttpServletResponse) commonMapVO.get("response");
		List<ClaimCareServerVO> careServerVOList = getList(careServerAVO);
		
		
		String sheetName = "服务人员信息清单";
        //1  写title
		String[] titleArray = {"序号",
                		       "服务人员",
                		       "用户名/业务员代码",
                		       "姓名","机构",
                		       "手机号码",
                		       "有效标志"};
        //2 生成excle表格
    	try {
    	logger.debug("*******************生成excel表格开始*******************");
       	 Map<String,Object> map =  createDownExcel(response,sheetName,"","",titleArray.length);
       	 WritableWorkbook workbook = (WritableWorkbook) map.get("workbook");
       	 ServletOutputStream fos = (ServletOutputStream)map.get("fos");
            WritableSheet wsheet = (WritableSheet) map.get("wsheet");
            WritableCellFormat wCF = (WritableCellFormat) map.get("wCF");
            wCF.setBorder(Border.ALL,BorderLineStyle.getStyle(1), Colour.BLACK);//2.1 设置字段边框
            //@invalid2.2 标题
            for (int i = 0; i < titleArray.length; i++) {
                wsheet.addCell(new Label(i,2,titleArray[i], wCF));
            }
            //@invalid2.3 组装内容
            if(careServerVOList!=null&&careServerVOList.size()>0){
           	 for (int j=0;j<careServerVOList.size();j++) {
           	     ClaimCareServerVO careServerVO = careServerVOList.get(j);
           	     String serverTypeName = "";
           	     String validFlagName = "";
           	     String organName = "";
           	     if (careServerVO.getOrganCode() != null) {
                    OrgVO orgVO = new OrgVO();
                    orgVO.setOrganCode(careServerVO.getOrganCode());
                    orgVO = findOrgNameByOrganCode(orgVO);
                    if (orgVO != null) {
                        String orgName = orgVO.getOrganName();
                        if (orgName != null ) {
                            organName = orgName;
                        }
                    }
                 }
           	     if (careServerVO.getServerType() != null) {
           	        serverTypeName = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_SERVER_TYPE", careServerVO.getServerType().toString());
           	     }
           	     if (careServerVO.getValidFlag() != null) {
           	        validFlagName = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_YES_NO", careServerVO.getValidFlag().toString());
           	     }
           		 wsheet.addCell(new Label(0,ClaimConstant.THREE+j, (j+1)+"", wCF));
           		 wsheet.addCell(new Label(1,ClaimConstant.THREE+j, careServerVO.getServerType() == null ? "":serverTypeName , wCF));
           		 wsheet.addCell(new Label(ClaimConstant.TWO,ClaimConstant.THREE+j, careServerVO.getServerCode() == null ? "":careServerVO.getServerCode().toString() , wCF));
           		 wsheet.addCell(new Label(ClaimConstant.THREE,ClaimConstant.THREE+j, careServerVO.getName() == null ? "":careServerVO.getName() , wCF));
           		 wsheet.addCell(new Label(ClaimConstant.FOUR,ClaimConstant.THREE+j, careServerVO.getOrganCode() == null ? "":organName , wCF));
           		 wsheet.addCell(new Label(ClaimConstant.FIVE,ClaimConstant.THREE+j, careServerVO.getMobile() == null ? "":careServerVO.getMobile() , wCF));
           		 wsheet.addCell(new Label(ClaimConstant.SIX,ClaimConstant.THREE+j, careServerVO.getValidFlag() == null ? "":validFlagName , wCF));
           		 }
            }
            //@invalid 主体内容生成结束
            workbook.write(); //3 写入文件
            workbook.close();
            fos.close(); //4 关闭流
            logger.debug("*******************生成excel表格成功*******************");
       } catch (Exception e) {
           e.printStackTrace();
           throw new BizException("系统异常");
       }
		
	}
	/**
	 * 方法说明: 导出加下载模型Excel 格式
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param response 前台入值
	 * @param sheetName 名称
	 * @param startDate 开始时间
	 * @param endDate 结束时间
	 * @param titleLenth 表头长度
	 * @return Map 集合信息
	 * @throws Exception
	 */
	private Map<String,Object> createDownExcel(HttpServletResponse response,String sheetName,String startDate,String endDate,int titleLenth) throws Exception{
		 Map<String,Object> map = new HashMap<String,Object>();
         response.setContentType("text/x-msdownload");
         response.addHeader("Content-Disposition", 
                            "attachment; filename=\"" +
                            new String((sheetName+".xls").getBytes("gb2312"),"ISO8859-1") + "\"");
         ServletOutputStream fos = response.getOutputStream();
         WritableWorkbook workbook = Workbook.createWorkbook(fos);//1 建立Excel文档和格式
         WritableSheet wsheet = workbook.createSheet(sheetName, 1);//2@invalid 工作表名称
         for(int i=0;i<titleLenth;i++){
        	 wsheet.setColumnView(i, ClaimConstant.TWENTY);//3@invalid 设置列的宽度
         }
         wsheet.setRowView(0, ClaimConstant.FIVE_HUNDRED); //4@invalid 设置行的高度
         wsheet.mergeCells(0, 0, titleLenth-1, 0);//5@invalid 设置合并单元格
         //6@invalid 格式
         WritableFont wfont;
         WritableCellFormat wCF;
         //7@invalid 设置表头格式
         wfont = new jxl.write.WritableFont(WritableFont.ARIAL, ClaimConstant.TEN, WritableFont.BOLD, false,
                 UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
         //8@invalid 设置表头格式
         WritableFont wfontHead = new jxl.write.WritableFont(WritableFont.ARIAL, ClaimConstant.FIFTEEN, WritableFont.BOLD, false,
        		 UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
         wCF = new WritableCellFormat(wfont);
         wCF.setAlignment(jxl.format.Alignment.CENTRE);//9@invalid 设置对齐方式
         WritableCellFormat wCFHead = new WritableCellFormat(wfontHead);
         wCFHead.setAlignment(jxl.format.Alignment.CENTRE);//10@invalid 设置对齐方式
         //11@invalid 设置表头
         wsheet.addCell(new Label(0, 0, sheetName, wCFHead));
         wsheet.addCell(new Label(0, 1, "统计期间", wCF));
         wsheet.addCell(new Label(1, 1, startDate, wCF));
         wsheet.addCell(new Label(2, 1, endDate, wCF));
         wsheet.addCell(new Label(0, 2, "报表日期", wCF));
         wsheet.addCell(new Label(1, 2, DateUtilsEx.date2String(WorkDateUtil.getWorkDate(), "yyyy-MM-dd HH:mm:ss"), wCF));
         //12@invalid 设置表内容格式
         wCF = new WritableCellFormat();
         wCF.setAlignment(jxl.format.Alignment.CENTRE);//13@invalid 设置对齐方式
         map.put("wsheet", wsheet);
         map.put("workbook", workbook);
         map.put("wCF", wCF);
         map.put("fos", fos);
         return map;
	}
}
