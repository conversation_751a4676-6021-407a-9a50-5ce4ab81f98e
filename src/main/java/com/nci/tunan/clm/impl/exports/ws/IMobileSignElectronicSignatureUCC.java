package com.nci.tunan.clm.impl.exports.ws;

import com.nci.tunan.clm.interfaces.vo.mobileSignElectronicSignature.MobileSignElectronicSignatureReqVO;
import com.nci.tunan.clm.interfaces.vo.mobileSignElectronicSignature.MobileSignElectronicSignatureResVO;

/** 
 * @description 移动签收电子签名分享轨迹回传接口
 * <AUTHOR> <EMAIL>
 * @date 2020年7月21日 下午2:44:32
 * @.belongToModule CLM-理赔系统 
*/
public interface IMobileSignElectronicSignatureUCC {
	
	/**
	 * @description 移动签收电子签名分享轨迹回传接口
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param inputVO
	 * @return 返回参数
	*/
	public MobileSignElectronicSignatureResVO mobileSignElectronicSignature(MobileSignElectronicSignatureReqVO inputVO);
	
	
	
}
