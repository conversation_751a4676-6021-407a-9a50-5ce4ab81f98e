package com.nci.tunan.clm.impl.register.ucc.impl;


import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.clm.impl.register.service.IClaimSpecialCodeService;
import com.nci.tunan.clm.impl.register.ucc.IClaimSpecialCodeUCC;
import com.nci.tunan.clm.interfaces.model.bo.ClaimSpecialCodeBO;
import com.nci.tunan.clm.interfaces.model.vo.ClaimSpecialCodeVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.logging.LoggerFactory;

/** 
 * @description  特种费用代码UCC实现类
 * <AUTHOR> <EMAIL>
 * @date 2015-05-15
 * @.belongToModule CLM-理赔系统 /特种费用代码UCC实现类
*/
public class ClaimSpecialCodeUCCImpl   implements IClaimSpecialCodeUCC  {
    /** 
     * @Fields claimSpecialCodeService : 特种费用代码service
     */
	private IClaimSpecialCodeService claimSpecialCodeService;
	
	/** 
	 * @Fields logger :  日志工具
 	 */
	private static Logger logger = LoggerFactory.getLogger();
	
	/**
     * @description SERVICE-getter方法
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @return IClaimSpecialCodeService service接口对象
     */
	public IClaimSpecialCodeService getClaimSpecialCodeService() {
		return claimSpecialCodeService;
	}
	
	/**
     * @description SERVICE-setter方法
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeService service对象
     */
	public void setClaimSpecialCodeService(IClaimSpecialCodeService claimSpecialCodeService) {
		this.claimSpecialCodeService = claimSpecialCodeService;
	}
	
	/**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVO 对象
     * @return ClaimSpecialCodeVO 添加结果
     */
	public ClaimSpecialCodeVO addClaimSpecialCode(ClaimSpecialCodeVO claimSpecialCodeVO) {
		logger.debug("<======ClaimSpecialCodeUCC--addClaimSpecialCode======>");
		//增加特种费用数据
		ClaimSpecialCodeBO claimSpecialCodeBO = claimSpecialCodeService.addClaimSpecialCode(BeanUtils.copyProperties(ClaimSpecialCodeBO.class, claimSpecialCodeVO));
		return BeanUtils.copyProperties(ClaimSpecialCodeVO.class, claimSpecialCodeBO);
	}
	
	/**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVO 对象
     * @return ClaimSpecialCodeVO 修改结果
     */
	public ClaimSpecialCodeVO updateClaimSpecialCode(ClaimSpecialCodeVO claimSpecialCodeVO)  {
		logger.debug("<======ClaimSpecialCodeUCC--updateClaimSpecialCode======>");
		//修改特种费用数据
		ClaimSpecialCodeBO claimSpecialCodeBO = claimSpecialCodeService.updateClaimSpecialCode(BeanUtils.copyProperties(ClaimSpecialCodeBO.class, claimSpecialCodeVO));
		return BeanUtils.copyProperties(ClaimSpecialCodeVO.class, claimSpecialCodeBO);
	}

	 /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVO 对象
     * @return boolean 删除是否成功
     */
	public boolean deleteClaimSpecialCode(ClaimSpecialCodeVO claimSpecialCodeVO)  {
		logger.debug("<======ClaimSpecialCodeUCC--deleteClaimSpecialCode======>");
		//删除特种费用数据
		return claimSpecialCodeService.deleteClaimSpecialCode(BeanUtils.copyProperties(ClaimSpecialCodeBO.class, claimSpecialCodeVO));
	}
	
	/**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVO 对象
     * @return ClaimSpecialCodeVO 查询结果对象
     */
	public ClaimSpecialCodeVO findClaimSpecialCode(ClaimSpecialCodeVO claimSpecialCodeVO) {
		logger.debug("<======ClaimSpecialCodeUCC--findClaimSpecialCode======>");
		//查询单条特种费用数据
		ClaimSpecialCodeBO claimSpecialCodeBackBO = claimSpecialCodeService.findClaimSpecialCode(BeanUtils.copyProperties(ClaimSpecialCodeBO.class, claimSpecialCodeVO));
		ClaimSpecialCodeVO claimSpecialCodeBackVO = BeanUtils.copyProperties(ClaimSpecialCodeVO.class, claimSpecialCodeBackBO);
		return claimSpecialCodeBackVO;
	}  
	
	/**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVO 对象
     * @return List<ClaimSpecialCodeVO> 查询结果List
     */
	public List<ClaimSpecialCodeVO> findAllClaimSpecialCode(ClaimSpecialCodeVO claimSpecialCodeVO) {
		logger.debug("<======ClaimSpecialCodeUCC--findAllClaimSpecialCode======>");
		//查询所有特种费用数据
		ClaimSpecialCodeBO claimSpecialCodeBO = BeanUtils.copyProperties(ClaimSpecialCodeBO.class, claimSpecialCodeVO);
		return BeanUtils.copyList(ClaimSpecialCodeVO.class, claimSpecialCodeService.findAllClaimSpecialCode(claimSpecialCodeBO));
	} 
	
	 /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVO 对象
     * @return int 查询结果条数
     */
	public int findClaimSpecialCodeTotal(ClaimSpecialCodeVO claimSpecialCodeVO) {
		logger.debug("<======ClaimSpecialCodeUCC--findClaimSpecialCodeTotal======>");
		//查询特种费用数据条数
		ClaimSpecialCodeBO claimSpecialCodeBO = BeanUtils.copyProperties(ClaimSpecialCodeBO.class, claimSpecialCodeVO);
		return claimSpecialCodeService.findClaimSpecialCodeTotal(claimSpecialCodeBO);
	}	
	
	 /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVO 对象
     * @param currentPage 当前页对象
     * @return CurrentPage<ClaimSpecialCodeVO> 查询结果的当前页对象
     */
	public CurrentPage<ClaimSpecialCodeVO> queryClaimSpecialCodeForPage(ClaimSpecialCodeVO claimSpecialCodeVO, CurrentPage<ClaimSpecialCodeVO> currentPage) {
		logger.debug("<======ClaimSpecialCodeUCC--queryClaimSpecialCodeForPage======>");
		//分页查询特种费用数据
		ClaimSpecialCodeBO claimSpecialCodeBO = BeanUtils.copyProperties(ClaimSpecialCodeBO.class, claimSpecialCodeVO);
		return BeanUtils.copyCurrentPage(ClaimSpecialCodeVO.class, 
		        claimSpecialCodeService.queryClaimSpecialCodeForPage(claimSpecialCodeBO, BeanUtils.copyCurrentPage(ClaimSpecialCodeBO.class, currentPage)));
	}
	
	/**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVOList 对象列表
     * @return boolean 批量添加是否成功
     */
	public boolean batchSaveClaimSpecialCode(List<ClaimSpecialCodeVO> claimSpecialCodeVOList) {
		logger.debug("<======ClaimSpecialCodeUCC--batchSaveClaimSpecialCode======>");
		//批量增加特种费用数据
		return claimSpecialCodeService.batchSaveClaimSpecialCode(BeanUtils.copyList(ClaimSpecialCodeBO.class, claimSpecialCodeVOList));
	}
	
	/**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVOList 对象列表
     * @return boolean 批量修改是否成功
     */
	public boolean batchUpdateClaimSpecialCode(List<ClaimSpecialCodeVO> claimSpecialCodeVOList) {
		logger.debug("<======ClaimSpecialCodeUCC--batchUpdateClaimSpecialCode======>");
		//批量修改特种费用数据
		return claimSpecialCodeService.batchUpdateClaimSpecialCode(BeanUtils.copyList(ClaimSpecialCodeBO.class, claimSpecialCodeVOList));
	}
	
	/**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVOList 对象列表
     * @return boolean 批量删除是否成功
     */
	public boolean batchDeleteClaimSpecialCode(List<ClaimSpecialCodeVO> claimSpecialCodeVOList) {
		logger.debug("<======ClaimSpecialCodeUCC--batchDeleteClaimSpecialCode======>");
		//批量删除特种费用数据
		return claimSpecialCodeService.batchDeleteClaimSpecialCode(BeanUtils.copyList(ClaimSpecialCodeBO.class, claimSpecialCodeVOList));
	}
	
	/**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSpecialCodeVO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	public List<Map<String, Object>> findAllMapClaimSpecialCode(ClaimSpecialCodeVO claimSpecialCodeVO) {
		logger.debug("<======ClaimSpecialCodeUCC--findAllMapClaimSpecialCode======>");
		//查询所有特种费用数据 ，重新组装为map
		ClaimSpecialCodeBO claimSpecialCodeBO = BeanUtils.copyProperties(ClaimSpecialCodeBO.class, claimSpecialCodeVO);
		return claimSpecialCodeService.findAllMapClaimSpecialCode(claimSpecialCodeBO);
	}
	
}
