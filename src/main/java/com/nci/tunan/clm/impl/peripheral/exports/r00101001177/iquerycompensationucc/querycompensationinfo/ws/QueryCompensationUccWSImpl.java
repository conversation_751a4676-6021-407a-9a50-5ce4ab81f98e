package com.nci.tunan.clm.impl.peripheral.exports.r00101001177.iquerycompensationucc.querycompensationinfo.ws;

import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.json.DataSerialJSon;
import javax.jws.WebService;
import javax.xml.ws.Holder;
import org.slf4j.Logger;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001177.iquerycompensationucc.querycompensationinfo.SrvReqBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001177.iquerycompensationucc.querycompensationinfo.SrvResBizBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001177.iquerycompensationucc.querycompensationinfo.SrvResBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101001177.iquerycompensationucc.querycompensationinfo.ws.IQueryCompensationUccWS;
import com.nci.tunan.clm.impl.peripheral.ucc.r00101001177.IQueryCompensationUcc;
/**
 * 
 * @description 赔付信息查询
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
@WebService(endpointInterface = "com.nci.tunan.clm.interfaces.peripheral.exports.r00101001177.iquerycompensationucc.querycompensationinfo.ws.IQueryCompensationUccWS"
, serviceName = "QueryCompensationUccWSImplqueryCompensationInfo")
public class QueryCompensationUccWSImpl implements IQueryCompensationUccWS {

    /** 
     * @Fields logger : 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();
    /**
     * 赔付信息查询
     */
    private IQueryCompensationUcc ucc = null;
    
    public IQueryCompensationUcc getUcc() {
        return ucc;
    }
    public void setUcc(IQueryCompensationUcc ucc) {
        this.ucc = ucc;
    }
    
    /**
     * 
     * @description 查询赔付信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.interfaces.peripheral.exports.r00101001177.iquerycompensationucc.querycompensationinfo.ws.IQueryCompensationUccWS#queryCompensationInfo(com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader, com.nci.tunan.clm.interfaces.peripheral.exports.r00101001177.iquerycompensationucc.querycompensationinfo.SrvReqBody, javax.xml.ws.Holder, javax.xml.ws.Holder)
     * @param parametersReqHeader 入参报文头
     * @param parametersReqBody 入参报文体
     * @param parametersResHeader 出参报文头
     * @param parametersResBody 出参报文体
     */
    @Override
    public void queryCompensationInfo(SysHeader parametersReqHeader, SrvReqBody parametersReqBody, 
            Holder<SysHeader> parametersResHeader, Holder<SrvResBody> parametersResBody) {//查询赔付信息
        
        CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
        CommonHeaderDeal.setBIZHEADERTHREAD_EXB(parametersReqBody.getBizHeader());
        com.nci.tunan.clm.interfaces.peripheral.exports.r00101001177.vo.InputData inputVO = parametersReqBody.getBizBody().getInputData();
        try {
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(parametersReqHeader));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersReqBody.getBizHeader()));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersReqBody.getBizBody()));
        } catch (Exception e1) {
            e1.printStackTrace();
            logger.debug("解析调用前报文的JSON字符串发生异常");
        }
        SrvResBody srvResBody = new SrvResBody();
        SysHeader sysHeader = new SysHeader();
        try {
            com.nci.tunan.clm.interfaces.peripheral.exports.r00101001177.vo.OutputData output = ucc.queryCompensationInfo(inputVO);
            sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
             BizHeaderExB  bizHeader = CommonHeaderDeal.dealBizHeader(parametersReqBody.getBizHeader());
            SrvResBizBody bizResBody = new SrvResBizBody();
            bizResBody.setOutputData(output);
            //报文比对
            bizHeader.setSysRouteFlag(null);
            bizHeader.setManageCom(null);
            bizHeader.setRiskCode(null);
            bizHeader.setContNo(null);
            srvResBody.setBizBody(bizResBody);
            srvResBody.setBizHeader(bizHeader);
            parametersResHeader.value = sysHeader;
            parametersResBody.value = srvResBody;
        } catch (Exception e2) {
        	logger.error("调用接口过程中产生异常!",e2);
        }
        try {
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(parametersResHeader.value));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersResBody.value.getBizHeader()));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersResBody.value.getBizBody()));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析调用后报文的JSON字符串发生异常");
        } 
    }
}
 

