package com.nci.tunan.clm.impl.peripheral.exports.r00101000097.iqueryclaimucc.queryclaimbycasenoandinsuredid.ws;

import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;

import javax.jws.WebService;
import javax.xml.ws.Holder;

import org.slf4j.Logger;

import java.util.Map;

import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000097.iqueryclaimucc.queryclaimbycasenoandinsuredid.SrvReqBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000097.iqueryclaimucc.queryclaimbycasenoandinsuredid.SrvResBizBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000097.iqueryclaimucc.queryclaimbycasenoandinsuredid.SrvResBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000097.iqueryclaimucc.queryclaimbycasenoandinsuredid.ws.IQueryClaimUccWS;
import com.nci.tunan.clm.impl.peripheral.ucc.r00101000097.IQueryClaimUcc;
import com.nci.tunan.clm.impl.util.ClaimConstant;
/**
 * 
 * @description 查询理赔信息
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/理赔查询
 * @date  2015年5月15日 上午10:08:31
 */
@WebService(endpointInterface = "com.nci.tunan.clm.interfaces.peripheral.exports.r00101000097.iqueryclaimucc.queryclaimbycasenoandinsuredid.ws.IQueryClaimUccWS"
, serviceName = "QueryClaimUccWSImplqueryClaimByCasenoAndInsuredid")
public class QueryClaimUccWSImpl implements IQueryClaimUccWS {

    /** 
     * @Fields logger : 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();
    /**
     * 查询理赔信息
     */
    private IQueryClaimUcc ucc = null;
    
    public IQueryClaimUcc getUcc() {
        return ucc;
    }
    public void setUcc(IQueryClaimUcc ucc) {
        this.ucc = ucc;
    }
    
    /**
     * 
     * @description 查询理赔信息
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.interfaces.peripheral.exports.r00101000097.iqueryclaimucc.queryclaimbycasenoandinsuredid.ws.IQueryClaimUccWS#queryClaimByCasenoAndInsuredid(com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader, com.nci.tunan.clm.interfaces.peripheral.exports.r00101000097.iqueryclaimucc.queryclaimbycasenoandinsuredid.SrvReqBody, javax.xml.ws.Holder, javax.xml.ws.Holder)
     * @param parametersReqHeader 入参报文头
     * @param parametersReqBody 入参报文体
     * @param parametersResHeader 出参报文头
     * @param parametersResBody 出参报文体
     */
    @Override
    public void queryClaimByCasenoAndInsuredid(SysHeader parametersReqHeader, SrvReqBody parametersReqBody, 
            Holder<SysHeader> parametersResHeader, Holder<SrvResBody> parametersResBody) {//查询理赔信息
        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        String dealTime = DateUtilsEx.getTodayTime();
        String systemName = StringUtilsEx.getStr("com.nci.tunan.clm.impl.peripheral.exports.r00101000097.iqueryclaimucc.queryclaimbycasenoandinsuredid.ws",  ClaimConstant.FOUR, ".");
        String dealNo = systemName + "_" + "IQueryClaimUcc" + "_" + "queryClaimByCasenoAndInsuredid";
        /* if (dealSwitch) {
            logger.debug("开始记录交易请求日志");
            CommonDealManagement.beforeCommonDealManagement(parametersReqHeader, parametersReqBody.getBizHeader(),
                parametersReqBody.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime); 
        }*/
        CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
                CommonHeaderDeal.setBIZHEADERTHREAD_EXB(parametersReqBody.getBizHeader());
                com.nci.tunan.clm.interfaces.peripheral.exports.r00101000097.vo.InputData inputVO = parametersReqBody.getBizBody().getInputData();
        try {
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(parametersReqHeader));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersReqBody.getBizHeader()));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersReqBody.getBizBody()));
        } catch (Exception e1) {
            e1.printStackTrace();
            logger.debug("解析调用前报文的JSON字符串发生异常");
        }
        SrvResBody srvResBody = new SrvResBody();
        SysHeader sysHeader = new SysHeader();
        String dealStatus = "2";
        try {
            com.nci.tunan.clm.interfaces.peripheral.exports.r00101000097.vo.OutputData output = ucc.queryClaimByCasenoAndInsuredid(inputVO);
            sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
             BizHeaderExB  bizHeader = CommonHeaderDeal.dealBizHeader(parametersReqBody.getBizHeader());
            SrvResBizBody bizResBody = new SrvResBizBody();
            bizResBody.setOutputData(output);
            srvResBody.setBizBody(bizResBody);
            srvResBody.setBizHeader(bizHeader);
            parametersResHeader.value = sysHeader;
            parametersResBody.value = srvResBody;
        } catch (Exception e2) {
            dealStatus = "3";
        	logger.error("调用接口过程中产生异常!",e2);
        } /*finally{
            if (dealSwitch) {
            	logger.debug("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysHeader,
                        srvResBody.getBizHeader(), srvResBody.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime,dealStatus); 
            }    
        }*/
        try {
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(parametersResHeader.value));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersResBody.value.getBizHeader()));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersResBody.value.getBizBody()));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析调用后报文的JSON字符串发生异常");
        } 
    }
}
 

