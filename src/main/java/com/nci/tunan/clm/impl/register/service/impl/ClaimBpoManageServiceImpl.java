package com.nci.tunan.clm.impl.register.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;

import com.nci.tunan.bpm.vo.task.assign.AssignTaskRequestVO;
import com.nci.tunan.bpm.vo.task.assign.AssignTaskVO;
import com.nci.tunan.clm.dao.IClaimMemoDao;
import com.nci.tunan.clm.dao.IOutSourceStateDao;
import com.nci.tunan.clm.dao.IOutSourceTypeDao;
import com.nci.tunan.clm.dao.IOutsourceCaseDao;
import com.nci.tunan.clm.dao.IOutsourceIfnoDao;
import com.nci.tunan.clm.dao.IUserDao;
import com.nci.tunan.clm.impl.register.service.IClaimBpoManageService;
import com.nci.tunan.clm.impl.register.service.IClaimBusiProdService;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.imports.ICLMServiceUtil;
import com.nci.tunan.clm.interfaces.model.bo.ClaimBpmRequestBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.OutSourceStateBO;
import com.nci.tunan.clm.interfaces.model.bo.OutSourceTypeBO;
import com.nci.tunan.clm.interfaces.model.bo.OutsourceCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.OutsourceIfnoBO;
import com.nci.tunan.clm.interfaces.model.bo.TaskListBO;
import com.nci.tunan.clm.interfaces.model.bo.UserBO;
import com.nci.tunan.clm.interfaces.model.po.ClaimMemoPO;
import com.nci.tunan.clm.interfaces.model.po.OutSourceStatePO;
import com.nci.tunan.clm.interfaces.model.po.OutSourceTypePO;
import com.nci.tunan.clm.interfaces.model.po.OutsourceCasePO;
import com.nci.tunan.clm.interfaces.model.po.OutsourceIfnoPO;
import com.nci.tunan.clm.interfaces.model.po.UserPO;
import com.nci.udmp.component.datapermission.DataPermissionUtil;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.presentation.vo.UserVO;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

/** 
 * @description  理赔外包ucc实现
 * @<NAME_EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2017-3-29 下午3:57:52   
*/
public class ClaimBpoManageServiceImpl implements IClaimBpoManageService {

    /**
     * @Fields logger : 日志工具
     */
    private static Logger logger = LoggerFactory.getLogger();

    /** 
     * @Fields outsourceCaseDao : 外包案件信息表Dao接口
    */ 
    private IOutsourceCaseDao outsourceCaseDao;
    
    /** 
     * @Fields outSourceStateDao : 外包状态Dao接口
    */ 
    private IOutSourceStateDao outSourceStateDao;
    
    /** 
     * @Fields outsourceInfoDao : 外包商信息表Dao接口
    */ 
    private IOutsourceIfnoDao outsourceInfoDao;
    
    /** 
     * @Fields outsourceTypeDao :外包类型Dao接口
    */ 
    private IOutSourceTypeDao outsourceTypeDao;
    
    /** 
     * @Fields claimBusiProdService : 理算险种service
    */ 
    private IClaimBusiProdService claimBusiProdService;

    /** 
     * @Fields claimUserDao : UserDaoImpl用户接口
    */ 
    private IUserDao claimUserDao;
    
    /** 
     * @Fields clmServiceUtil : 服务总接口
    */ 
    private ICLMServiceUtil clmServiceUtil;
    /** 
    * @Fields claimMemoDao : 问题件 
    */ 
    private IClaimMemoDao claimMemoDao;
   

    /**
     * @description 外包案件池查询
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @see com.nci.tunan.clm.impl.register.service.IClaimBpoManageService#queryOutSourceCasePool(com.nci.tunan.clm.interfaces.model.bo.OutsourceCaseBO, com.nci.udmp.framework.model.CurrentPage)
     * @param outsourceCaseBO 外包案件信息表
     * @param currentPage 外包案件信息表
     * @return CurrentPage<OutsourceCaseBO> 外包案件信息表
    */
    @Override
    public CurrentPage<OutsourceCaseBO> queryOutSourceCasePool(OutsourceCaseBO outsourceCaseBO,
            CurrentPage<OutsourceCaseBO> currentPage) {
        logger.debug("<======ClaimBpoManageServiceImpl--queryOutSourceCasePool======>");
        OutsourceCasePO outsourceCasePO = BeanUtils.copyProperties(OutsourceCasePO.class, outsourceCaseBO);
        outsourceCasePO.set("case_no", outsourceCaseBO.getCaseNo());
//@invalid         outsourceCasePO.set("case_status", outsourceCaseBO.getCaseStatus());
        outsourceCasePO.set("outsource_id", outsourceCaseBO.getOutsourceId());
        outsourceCasePO.set("outsource_name", outsourceCaseBO.getOutsourceName());
        outsourceCasePO.set("outsource_type_code", outsourceCaseBO.getOutsourceTypeCode());
        outsourceCasePO.set("outsource_status_code", outsourceCaseBO.getOutsourceStatusCode());
        outsourceCasePO.set("send_package_start_time", outsourceCaseBO.getSendPackageStartTime());
        outsourceCasePO.set("send_package_end_time", outsourceCaseBO.getSendPackageEndTime());
        outsourceCasePO.set("organ_code", outsourceCaseBO.getOrganCode());
        outsourceCasePO.set("outsource_way", outsourceCaseBO.getOutsourceWay());
// @invalid        outsourceCasePO.set("area_code", outsourceCaseBO.getAreaCode());
        //1.查询外包案件池数据数据T_outsource_Case
        CurrentPage<OutsourceCasePO> currentPO = outsourceCaseDao.queryOutsourceCasePoolForPage(outsourceCasePO,
                BeanUtils.copyCurrentPage(OutsourceCasePO.class, currentPage));
//  @invalid       currentBO = BeanUtils.copyCurrentPage(OutsourceCaseBO.class, currentPO)
        List<OutsourceCasePO> outsourceCasePOList = currentPO.getPageItems();
        List<OutsourceCaseBO> outsourceCaseBOList = new ArrayList<OutsourceCaseBO>();
        for (OutsourceCasePO outsourceCasePO1 : outsourceCasePOList) {
            outsourceCaseBO = BeanUtils.copyProperties(OutsourceCaseBO.class, outsourceCasePO1);
            ClaimCaseBO claimCaseBO = new ClaimCaseBO();
            claimCaseBO.setCaseId(outsourceCaseBO.getCaseId());
            //2.查询赔案的挂起标识
            boolean flag = claimBusiProdService.queryLockRegister(claimCaseBO);
            if(flag){ //@invalid 说明被挂起
                outsourceCaseBO.setPolicyFlag(ClaimConstant.SHAREQUERYTASKTYPE); //1为是
            }
            outsourceCaseBO.setCaseStatus((String) outsourceCasePO1.get("case_status"));
            outsourceCaseBO.setGreenFlag((BigDecimal) outsourceCasePO1.get("green_flag"));
            outsourceCaseBO.setIsBpo((BigDecimal) outsourceCasePO1.get("is_bpo"));
            
            if ((Date) outsourceCasePO1.get("sign_time") != null){
                outsourceCaseBO.setSignTime((Date) outsourceCasePO1.get("sign_time"));
                outsourceCaseBO.setRealName((String) outsourceCasePO1.get("real_name"));
                outsourceCaseBO.setCustomerName((String) outsourceCasePO1.get("customer_name"));
                outsourceCaseBO.setOutsourceName((String) outsourceCasePO1.get("outsource_name"));
                outsourceCaseBO.setOutsourceWay((BigDecimal) outsourceCasePO1.get("outsource_way"));
                outsourceCaseBO.setOutsourceOrganCode((String) outsourceCasePO1.get("organ_code"));
                outsourceCaseBO.setAreaCode((String) outsourceCasePO1.get("area_code"));
                outsourceCaseBO.setReceivedPackageTime((Date)outsourceCasePO1.get("received_package_time"));
                outsourceCaseBO.setSendPackageTime((Date)outsourceCasePO1.get("send_package_time"));
            }
            /*//@invalid  查询回传时效
            if (outsourceCaseBO.getReceiveTime() != null) {
                double day = DateUtilsEx.getDayAmount(outsourceCaseBO.getSendTime(),
                        DateUtil.stringToUtilDate(outsourceCaseBO.getReceiveTime()));
                double dayTime = day * 24;
                //@invalid  设置回传时效
                outsourceCaseBO.setBackTime(dayTime);
            }*/
            if(outsourceCaseBO.getReceivedPackageTime() != null){
//@invalid             	double dayTime = 0.0;
            	SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH");
            	String sendPackage = sf.format(outsourceCaseBO.getSendPackageTime());
            	String receivedPackageTime = sf.format(outsourceCaseBO.getReceivedPackageTime());
            	
            	Date sendPackageDate = DateUtilsEx.formatToDate(sendPackage, "yyyy-MM-dd HH");
            	Date receivedPackageTimeDate = DateUtilsEx.formatToDate(receivedPackageTime, "yyyy-MM-dd HH");
            	double day = DateUtilsEx.getDayAmount(sendPackageDate,receivedPackageTimeDate);
//@invalid             	if(day == 0.0) { //@invalid 计算小时
//@invalid             		int sendPackage = DateUtilsEx.getHour(outsourceCaseBO.getSendPackageTime());
//@invalid             		int receivedPackage = DateUtilsEx.getHour(outsourceCaseBO.getReceivedPackageTime());
//@invalid             		dayTime = receivedPackage - sendPackage;
//@invalid             	} else {
            	double dayTime = day*ClaimConstant.TWENTY_FOUR;
//@invalid             	}
            	outsourceCaseBO.setBackTime(dayTime);
            }
            //判断是否存在自动立案问题件
			ClaimMemoPO claimMemoPO = new ClaimMemoPO();
			claimMemoPO.setCaseId(outsourceCasePO1.getCaseId());
			claimMemoPO.setMemoType(ClaimConstant.MEMO_TYPE_TWENTYONE);
			int memoCount = claimMemoDao.findClaimMemoTotal(claimMemoPO);
			if(memoCount>0){
				outsourceCaseBO.setIsRedFlag(ClaimConstant.YES);
			}
            outsourceCaseBOList.add(outsourceCaseBO);
        }

        CurrentPage<OutsourceCaseBO> currentBO = new CurrentPage<OutsourceCaseBO>();
        currentBO.setPageItems(outsourceCaseBOList);
        currentBO.setPageNo(currentPO.getPageNo());
        currentBO.setPageSize(currentPO.getPageSize());
        currentBO.setPageTotal(currentPO.getPageTotal());
        currentBO.setTotal(currentPO.getTotal());
        return currentBO;
    }

	/**
	 * @description 查询外包状态所有数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.service.IClaimBpoManageService#findAllOutsourceState(com.nci.tunan.clm.interfaces.model.bo.OutSourceStateBO)
	 * @param outsourceStateBO 外包状态表
	 * @return  List<OutSourceStateBO> 外包状态表
	*/
	public List<OutSourceStateBO> findAllOutsourceState(OutSourceStateBO  outsourceStateBO) {
		//查询外包状态所有数据
		logger.debug("<======OutsourceStateServiceImpl--findAllOutsourceState======>");
		OutSourceStatePO outsourceStatePO = BeanUtils.copyProperties(OutSourceStatePO.class, outsourceStateBO);
		List<OutSourceStatePO> list=outSourceStateDao.findAllOutsourceState(outsourceStatePO);
		return BeanUtils.copyList(OutSourceStateBO.class, list);
	}
	
	/**
	 * @description 查询所有外包类型数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.service.IClaimBpoManageService#findAllOutsourceType(com.nci.tunan.clm.interfaces.model.bo.OutSourceTypeBO)
	 * @param outsourceTypeBO 外包类型表
	 * @return  List<OutSourceTypeBO> 外包类型表
	*/
	public List<OutSourceTypeBO> findAllOutsourceType(OutSourceTypeBO  outsourceTypeBO) {
		//查询所有外包类型数据
		OutSourceTypePO outsourceTypePO = BeanUtils.copyProperties(OutSourceTypePO.class, outsourceTypeBO);
		List<OutSourceTypePO> list= outsourceTypeDao.findAllOutsourceType(outsourceTypePO);
		return BeanUtils.copyList(OutSourceTypeBO.class,list);
	}
	/**
	 * @description 查询外包商信息     
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.service.IClaimBpoManageService#findAllOutsourceIfno(com.nci.tunan.clm.interfaces.model.bo.OutsourceIfnoBO)
	 * @param outsourceIfnoBO 外包商信息表
	 * @return List<OutsourceIfnoBO>  外包商信息表
	*/
	public List<OutsourceIfnoBO> findAllOutsourceIfno(OutsourceIfnoBO outsourceIfnoBO) {
		//查询外包商信息     
		OutsourceIfnoPO outsourceIfnoPO = BeanUtils.copyProperties(OutsourceIfnoPO.class, outsourceIfnoBO);
		List<OutsourceIfnoPO> list= outsourceInfoDao.findAllOutsourceIfno(outsourceIfnoPO);
		return BeanUtils.copyList(OutsourceIfnoBO.class,list);
	}
    /**
     * @description 查询操作人员
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.service.IClaimBpoManageService#queryMultiOperatorOutsource(com.nci.tunan.clm.interfaces.model.bo.UserBO, com.nci.udmp.framework.model.CurrentPage)
     * @param userBO 用户
     * @param currentPage 用户
     * @return  CurrentPage<UserBO> 用户
    */
    @Override
    public CurrentPage<UserBO> queryMultiOperatorOutsource(UserBO userBO,
            CurrentPage<UserBO> currentPage) {
    	//查询操作人员
        UserPO userPO = BeanUtils.copyProperties(UserPO.class, userBO);
        CurrentPage<UserPO> currentPO = claimUserDao.queryUserForPage(userPO,
                BeanUtils.copyCurrentPage(UserPO.class, currentPage));
        return BeanUtils.copyCurrentPage(UserBO.class, currentPO);
    }
	
    /**
     * @description 分配任务
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.register.service.IClaimBpoManageService#allotTaskOutsource(com.nci.tunan.clm.interfaces.model.bo.TaskListBO)
     * @param taskListBO 主管作业管理 查询任务列表展示
    */
    @Override
    public void allotTaskOutsource(TaskListBO taskListBO) {
         
        //1.判断分配的用户是否有立案权限。
        if(!StringUtils.isEmpty(taskListBO.getUserId())){
            boolean flag = true;
            List<UserVO> userVOList = DataPermissionUtil.getInstance().getUserByPermissionType("立案登记");
            for(UserVO userVO : userVOList){
                if(userVO.getUserId().compareTo(new BigDecimal(taskListBO.getUserId())) == 0){ 
                    //2.此用户有立案登记权限。
                    flag = false;
                    break;
                }
            }
            if(flag){
                throw new BizException("用户无立案登记权限,请重新选择符合权限的作业人员。"); 
            }
        } else {
            throw new BizException("获取用户ID为空，无法判断是否有立案登记权限。"); 
        }
        List<String> taskIdList = new ArrayList<String>();
        String[] caseNos = taskListBO.getCaseNo().split(",");
        for(int i = 0; i < caseNos.length; i++){
        	if(caseNos[i].trim().isEmpty()) {
                continue;
            }
        
            //3.根据赔案号查询赔案对应的taskId
            ClaimBpmRequestBO claimBpmRequestBO = new ClaimBpmRequestBO();
            claimBpmRequestBO.setCaseNo(caseNos[i].trim());
            claimBpmRequestBO.setProcessFlag("CLMMain");
            claimBpmRequestBO.setTaskCode("CLM010220");
            claimBpmRequestBO.setQueryTaskType("1"); //@invalid 查询共享池
            
            String taskId = claimBusiProdService.queryTaskId(claimBpmRequestBO);
            if(StringUtils.isEmpty(taskId)){
                claimBpmRequestBO.setQueryTaskType("2"); //@invalid 查询个人池
                String taskIdSelf = claimBusiProdService.queryTaskId(claimBpmRequestBO);
                if(StringUtils.isEmpty(taskIdSelf)){
                    claimBpmRequestBO.setQueryTaskType("2"); //@invalid 查询个人池
                    claimBpmRequestBO.setTaskCode("CLM010203");
                    String taskIdSelfZ = claimBusiProdService.queryTaskId(claimBpmRequestBO);
                    if(StringUtils.isEmpty(taskIdSelfZ)){
                        claimBpmRequestBO.setQueryTaskType("1"); //@invalid 查询共享池
                        claimBpmRequestBO.setTaskCode("CLM010203");
                        String taskIdZ = claimBusiProdService.queryTaskId(claimBpmRequestBO);
                        if(StringUtils.isEmpty(taskIdZ)){
                            throw new BizException(caseNos[i]+"此赔案查询不到工作流的任务。"); 
                        } else {
                            taskIdList.add(taskIdZ);
                        }
                    } else {
                        taskIdList.add(taskIdSelfZ);
                    }
                } else {
                    taskIdList.add(taskIdSelf);
                }
            } else {
                taskIdList.add(taskId);
            }
            //4.将所有任务分配给页面上指定的操作员
            AssignTaskVO assignTaskVO = new AssignTaskVO();
            assignTaskVO.setTaskIdList(taskIdList);
            String caseNo = "CLM010220"+caseNos[i].trim(); 
            assignTaskVO.setRessignUser(taskListBO.getUserName());
            Map<String, String> updateMap = new HashMap<String, String>();
            updateMap.put("assignFlag", ClaimConstant.ASSIGNFLAG_HAND); //@invalid  人工分配标识
            updateMap.put("assignCode", AppUserContext.getCurrentUser().getUserName());
            updateMap.put("assignName", AppUserContext.getCurrentUser().getRealName());
            updateMap.put("assignTime",
                    DateUtilsEx.date2String(WorkDateUtil.getWorkDate(), "yyyy-MM-dd HH:mm:ss"));
            assignTaskVO.setUpdateMap(updateMap);
            List<AssignTaskVO> assignTaskVOList = new ArrayList<AssignTaskVO>();
            assignTaskVOList.add(assignTaskVO);
            AssignTaskRequestVO inputData = new AssignTaskRequestVO();
            inputData.setAssignTaskVOList(assignTaskVOList);
            //@invalid processFlag和TaskCode 由于是必填字段，根据工作流要求写死，实际更新跟这个无关
            inputData.setProcessFlag(ClaimConstant.BATCHFLAG);
            inputData.setTaskCode("nothing");
            //@invalid 调用工作流分配任务接口
            clmServiceUtil.bpmitaskuccassignTask(inputData,caseNo);
            String bizResCd = CommonHeaderDeal.getSYSHEADERTHREAD().getBizResCd();
            if(!ClaimConstant.WORKFLOW_SUCCESS.equals(bizResCd)){
                throw new BizException("任务分配失败。"); 
            } else {
                //@invalid 存储分配标识
                OutsourceCasePO outsourceCasePO = new OutsourceCasePO();
                outsourceCasePO.setCaseNo(caseNos[i].trim());
                outsourceCasePO = outsourceCaseDao.findOutSourceCaseByCaseNo(outsourceCasePO);
                outsourceCasePO.setCounterEmployeeFlag(new BigDecimal(ClaimConstant.ONE));
                outsourceCaseDao.updateOutsourceCase(outsourceCasePO);
            }
        
        }
    }
    /**
	 * @description 外包案件池初始化下拉框显示 
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.register.service.IClaimBpoManageService#findAllOutsourceIfnoPoolIni(com.nci.tunan.clm.interfaces.model.bo.OutsourceIfnoBO)
	 * @param outsourceIfnoBO 外包商信息表
	 * @return List<OutsourceIfnoBO>  外包商信息表
	*/
	public List<OutsourceIfnoBO> findAllOutsourceIfnoPoolIni(OutsourceIfnoBO outsourceIfnoBO) {
		//查询外包商信息     
		OutsourceIfnoPO outsourceIfnoPO = BeanUtils.copyProperties(OutsourceIfnoPO.class, outsourceIfnoBO);
		List<OutsourceIfnoPO> list= outsourceInfoDao.findAllOutsourceIfnoPoolIni(outsourceIfnoPO);
		return BeanUtils.copyList(OutsourceIfnoBO.class,list);
	}
    public IOutsourceIfnoDao getOutsourceInfoDao() {
		return outsourceInfoDao;
	}

	public void setOutsourceInfoDao(IOutsourceIfnoDao outsourceInfoDao) {
		this.outsourceInfoDao = outsourceInfoDao;
	}

	public IOutSourceTypeDao getOutsourceTypeDao() {
		return outsourceTypeDao;
	}

	public void setOutsourceTypeDao(IOutSourceTypeDao outsourceTypeDao) {
		this.outsourceTypeDao = outsourceTypeDao;
	}

	public IOutsourceCaseDao getOutsourceCaseDao() {
        return outsourceCaseDao;
    }

    public void setOutsourceCaseDao(IOutsourceCaseDao outsourceCaseDao) {
        this.outsourceCaseDao = outsourceCaseDao;
    }

	public IOutSourceStateDao getOutSourceStateDao() {
		return outSourceStateDao;
	}

	public void setOutSourceStateDao(IOutSourceStateDao outSourceStateDao) {
		this.outSourceStateDao = outSourceStateDao;
	}

    public IClaimBusiProdService getClaimBusiProdService() {
        return claimBusiProdService;
    }

    public void setClaimBusiProdService(IClaimBusiProdService claimBusiProdService) {
        this.claimBusiProdService = claimBusiProdService;
    }

    public IUserDao getClaimUserDao() {
        return claimUserDao;
    }

    public void setClaimUserDao(IUserDao claimUserDao) {
        this.claimUserDao = claimUserDao;
    }

    public ICLMServiceUtil getClmServiceUtil() {
        return clmServiceUtil;
    }

    public void setClmServiceUtil(ICLMServiceUtil clmServiceUtil) {
        this.clmServiceUtil = clmServiceUtil;
    }

	public IClaimMemoDao getClaimMemoDao() {
		return claimMemoDao;
	}

	public void setClaimMemoDao(IClaimMemoDao claimMemoDao) {
		this.claimMemoDao = claimMemoDao;
	}
}
