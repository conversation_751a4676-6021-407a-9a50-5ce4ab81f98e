package com.nci.tunan.clm.impl.claimTaskStatus.service.impl;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.nci.udmp.component.datapermission.DataPermissionUtil;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.CsApplyInfoS;
import com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.CsApplyList;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.git.ecss.dm.fp.PluginRegisterInfo;
import com.git.ecss.easyclient.fp.IPageRegisterServiceProcess;
import com.git.ecss.easyclient.fp.impl.PageRegisterProcessImpl;
import com.git.ecss.sysobject.system.BaseBusiObject;
import com.git.ecss.sysobject.system.BaseDocObject;
import com.nci.tunan.clm.interfaces.model.bo.CustomerBO;
import com.nci.tunan.clm.dao.IClaimHiTaskDao;
import com.nci.tunan.clm.dao.ICustomerDao;
import com.nci.tunan.clm.dao.ISurveyApplyDao;
import com.nci.tunan.clm.dao.impl.SurveyApplyDaoImpl;
import com.nci.tunan.clm.impl.claimTaskStatus.service.IBfSurveyHITaskManageService;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.impl.util.CodeUtils;
import com.nci.tunan.clm.impl.util.DateUtil;
import com.nci.tunan.clm.interfaces.model.bo.ClaimApplySurveyBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimHiTaskBO;
import com.nci.tunan.clm.interfaces.model.bo.SurveyApplyBO;
import com.nci.tunan.clm.interfaces.model.bo.SurveyConclusionBO;
import com.nci.tunan.clm.interfaces.model.po.ClaimHiTaskPO;
import com.nci.tunan.clm.interfaces.model.po.CustomerPO;
import com.nci.tunan.clm.interfaces.model.po.SurveyApplyPO;
import com.nci.tunan.clm.interfaces.model.po.SurveyConclusionPO;
import com.nci.tunan.clm.interfaces.model.report.vo.ClaimHiTaskVO;
import com.nci.tunan.clm.interfaces.model.vo.CustomerVO;
import com.nci.tunan.clm.interfaces.model.vo.SurveyApplyVO;
import com.nci.tunan.cs.impl.commonFlow.service.ICusApplicationService;
import com.nci.tunan.cs.impl.peripheral.service.r00102001139.IcancelApplyService;
import com.nci.tunan.cs.impl.peripheral.service.r00102001139.impl.CancelApplyServiceImpl;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102001139.vo.OutputData;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.presentation.vo.UserVO;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.xml.transform.XmlHelper;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102001139.vo.InputData;
import com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.SaveAndCheckInputData;
import com.nci.tunan.clm.imports.CLMServiceUtil;
import com.nci.tunan.clm.imports.ICLMServiceUtil;
import com.nci.tunan.pa.interfaces.model.po.CustemPO;

public class BfSurveyHITaskManageServiceImpl implements IBfSurveyHITaskManageService {

	/**
	 * 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	private ISurveyApplyDao surveyApplyDao;

	private SurveyApplyDaoImpl surveyApplyDaoImpl;

	private IClaimHiTaskDao iClaimHiTaskDao;

	private ClaimHiTaskPO ClaimHiTaskPO;

	private ClaimHiTaskPO ClaimHiTaskBO;

	private SurveyApplyPO surveyApplyPO;

	private IcancelApplyService cancelApplyService;

	private InputData inputData;

	private ICLMServiceUtil clmServiceUtil;

	private SurveyConclusionPO curveyConclusionPO;

	private ICustomerDao customerDao;

	@Autowired
	@Qualifier("PA_cusApplicationService")
	private ICusApplicationService cusApplicationService;

	/**
	 * @description 配置分页查询数据
	 * @version V1.0.0
	 * @title
	 * @param currentPage 当前页对象
	 * @return CurrentPage<ClaimPhoneServiceBO> 查询结果的当前页对象
	 */
	@Override
	public CurrentPage<ClaimHiTaskBO> queryProcessHITask(ClaimHiTaskBO claimHiTaskBO,
			CurrentPage<ClaimHiTaskBO> copyCurrentPage) {
		// 1.分页查询数据
		ClaimHiTaskPO claimHiTaskPO = BeanUtils.copyProperties(ClaimHiTaskPO.class, claimHiTaskBO);
		CurrentPage<ClaimHiTaskPO> page = iClaimHiTaskDao.queryClaimHiTaskByStatus(claimHiTaskPO,
				BeanUtils.copyCurrentPage(ClaimHiTaskPO.class, copyCurrentPage));
		CurrentPage<ClaimHiTaskBO> claimHiTaskBOss = BeanUtils.copyCurrentPage(ClaimHiTaskBO.class, page);
		List<ClaimHiTaskBO> claimHiTaskBOs = claimHiTaskBOss.getPageItems();
		if (claimHiTaskBOs.size() > 0) {
			for (int i =0; i<claimHiTaskBOs.size() ;i++) {
				ClaimHiTaskBO bo = claimHiTaskBOs.get(i);
				// 查询保单号
				SurveyApplyPO surveyApplyPOCs = new SurveyApplyPO();
				surveyApplyPOCs.setCsApplyCode(bo.getApplyCode());
				surveyApplyPOCs.setCsAcceptCode(bo.getAcceptCode());
				surveyApplyPOCs.setApplyId(bo.getApplyId());
				List<SurveyApplyPO> surveyApplyPOCses = surveyApplyDao.findAllSurveyApply(surveyApplyPOCs);
				if (surveyApplyPOCses.size() > 0) {
					claimHiTaskBOs.get(i).setPolicyCode(surveyApplyPOCses.get(0).getPolicyCode());
					claimHiTaskBOs.get(i).setApplyCode(surveyApplyPOCses.get(0).getCsApplyCode());
				}
				if(bo.getApplyConfirmTime()!=null){
					claimHiTaskBOs.get(i).setApplyConfirmTimeStr(DateUtil.utilDateToString(bo.getApplyConfirmTime()));
				}
				if(bo.getAcceptorName()!=null){
					claimHiTaskBOs.get(i).setAcceptorName(bo.getAcceptorName().toString());
				}
				claimHiTaskBOs.get(i).setBackReason(bo.getBackReason());
				if(bo.getTaskStatus()!=null){
					if("1".equals(bo.getTaskStatus().toString())){
						claimHiTaskBOs.get(i).setTaskStatusStr("待处理");
					}
					if("3".equals(bo.getTaskStatus().toString())){
						claimHiTaskBOs.get(i).setTaskStatusStr("已退回");
					}
				}
			}
		}
		return claimHiTaskBOss;
	}

	/**
	 * @description 关闭
	 * @version V1.0.0
	 * @title
	 * @param currentPage 当前页对象
	 * @return OutputData 关闭
	 */
	@Override
	public OutputData closeHITask(String listIdList){
		OutputData outputData = new OutputData();
		String[] listIds = listIdList.split(",");
		String acceptCode;
		//查询受理号
		for(String listId : listIds) {
			ClaimHiTaskPO claimHiTaskPO = new ClaimHiTaskPO();
			claimHiTaskPO.setListId(new BigDecimal(listId));
			claimHiTaskPO = iClaimHiTaskDao.findClaimHiTaskByListId(claimHiTaskPO);
			acceptCode = claimHiTaskPO.getAcceptCode();
//			acceptCode = "z612024030597242301";
			//没有保全受理号的不进行保全解挂操作
			if(acceptCode==null) {
				if (listId != null) {
					// 更新任务状态为“已关闭”
					claimHiTaskPO.setListId(new BigDecimal(listId));
					claimHiTaskPO.setTaskStatus(ClaimConstant.AUDIT_DECISION_FOUR);
					claimHiTaskPO = iClaimHiTaskDao.updateClaimHiTaskByListId(claimHiTaskPO);
					}
			}else {
				com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.InputData inputData = 
						new com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.InputData();
				CsApplyList csApplyList = new CsApplyList();
				List<CsApplyInfoS> csApplyInfoS = new ArrayList<>();
				CsApplyInfoS applyInfoS = new CsApplyInfoS();
				applyInfoS.setAcceptCode(acceptCode);
				applyInfoS.setApplyCode(claimHiTaskPO.getApplyCode());
				applyInfoS.setClmSubmitStatus("04");
					csApplyInfoS.add(applyInfoS);
				csApplyList.setCsApplyInfoS(csApplyInfoS);
				inputData.setCsApplyList(csApplyList);
				cusApplicationService.updateAppInfoCLM(inputData);
//				clmServiceUtil.cancelApply(inputData);
				//更新为关闭
				if (listId != null) {
					// 更新任务状态为“已关闭”
					claimHiTaskPO.setListId(new BigDecimal(listId));
					claimHiTaskPO.setTaskStatus(ClaimConstant.AUDIT_DECISION_FOUR);
					claimHiTaskPO = iClaimHiTaskDao.updateClaimHiTaskByListId(claimHiTaskPO);
					}
			}

			}
		return outputData;
	}

	/**
	 * @description 增补告知初始化
	 * @version V1.0.0
	 * @title
	 * @param currentPage 当前页对象
	 * @return OutputData 关闭
	 */
	@Override
	public CurrentPage<ClaimHiTaskBO> findinforms(SurveyApplyBO surveyApplyBO,CurrentPage<ClaimHiTaskBO> currentPage) {
		// 增补告知任务表
		ClaimHiTaskPO claimHiTaskPO = BeanUtils.copyProperties(ClaimHiTaskPO.class, surveyApplyBO);
		claimHiTaskPO.setApplyId(surveyApplyBO.getApplyId());
		CurrentPage<ClaimHiTaskPO> pos = iClaimHiTaskDao.queryClaimHiTask(claimHiTaskPO,
				BeanUtils.copyCurrentPage(ClaimHiTaskPO.class, currentPage));
		CurrentPage<ClaimHiTaskBO> claimHiTaskBOs = BeanUtils.copyCurrentPage(ClaimHiTaskBO.class, pos);
		List<ClaimHiTaskBO> taskBOs = claimHiTaskBOs.getPageItems();
		for(ClaimHiTaskBO hiTaskBO : taskBOs) {
			if(hiTaskBO.getApplyConfirmTime()!=null) {
			hiTaskBO.setApplyConfirmTimeStr(DateUtil.utilDateToString(hiTaskBO.getApplyConfirmTime()));
			}
		}
		claimHiTaskBOs.setPageItems(taskBOs);
		return claimHiTaskBOs;
	}

	@Override
	public CurrentPage<SurveyApplyBO> findTaskDisposal(SurveyApplyBO surveyApplyBO,
			CurrentPage<SurveyApplyBO> currentPage) {
		SurveyApplyPO surveyApplyPO = BeanUtils.copyProperties(SurveyApplyPO.class, surveyApplyBO);
		if(null!=surveyApplyPO.getSurveyStatus()){
		if (new BigDecimal(0).compareTo(surveyApplyPO.getSurveyStatus()) == 0) {
			surveyApplyPO.setSurveyStatus(null);
		}
		}
		surveyApplyPO.getData().put("organ_code", surveyApplyBO.getOrganCode());
		surveyApplyPO.getData().put("positive_flag", surveyApplyBO.getPositiveFlag());
		if(null!=surveyApplyPO.getSurveyStatus()){
		if(surveyApplyBO.getSurveyStatus().compareTo(new BigDecimal(0))!=0){
			surveyApplyPO.getData().put("survey_status", surveyApplyBO.getSurveyStatus());
		}
		}else{
			surveyApplyPO.getData().put("survey_status", null);
		}
		surveyApplyPO.getData().put("start_date", surveyApplyBO.getStartDate());
		surveyApplyPO.getData().put("end_date", surveyApplyBO.getEndDate());
		// 1.分页查询数据
		CurrentPage<SurveyApplyPO> page = surveyApplyDao.findSurveyApplyByList(surveyApplyPO, BeanUtils.copyCurrentPage(SurveyApplyPO.class, currentPage));
		CurrentPage<SurveyApplyBO> surveyApplyBOs = BeanUtils.copyCurrentPage(SurveyApplyBO.class, page);
		List<SurveyApplyBO> applyBOs = surveyApplyBOs.getPageItems();
		List<SurveyApplyPO> surveyApplyPOs = page.getPageItems();
		for (int i = 0; i < applyBOs.size(); i++) {
			applyBOs.get(i).setOrganCode(surveyApplyPOs.get(i).getData().get("survey_org") == null ? null
					: surveyApplyPOs.get(i).getData().get("survey_org").toString());
			applyBOs.get(i).setPolicyCode(surveyApplyPOs.get(i).getData().get("policy_code") == null ? null
					: surveyApplyPOs.get(i).getData().get("policy_code").toString());
			if (surveyApplyPOs.get(i).getData().get("positive_flag") != null) {
				applyBOs.get(i).setPositiveFlag(
						new BigDecimal(surveyApplyPOs.get(i).getData().get("positive_flag").toString()));
				if (applyBOs.get(i).getPositiveFlag().compareTo(BigDecimal.ZERO) == 0) {
					applyBOs.get(i).setPositiveFlagStr("0");
				} else if (applyBOs.get(i).getPositiveFlag().compareTo(BigDecimal.ONE) == 0) {
					applyBOs.get(i).setPositiveFlagStr("1");
				}
			}
			applyBOs.get(i).setApplyDateStr(DateUtil.utilDateToString(surveyApplyPOs.get(i).getApplyDate()));
			if (applyBOs.get(i).getSurveyStatus() != null) {
				String surveyStatus = applyBOs.get(i).getSurveyStatus().toString();
				String str = CodeUtils.getValueByCode("APP___CLM__DBUSER.T_SURVEY_STATUS", surveyStatus);
				applyBOs.get(i).setSurveyStatusStr(str);
			}
			if(surveyApplyBO.getSurveyStatus()!=null){
			applyBOs.get(i).setSurveyStatus(surveyApplyBO.getSurveyStatus());
			}
		}
		return surveyApplyBOs;
	}

	/**
	 * @description 增补告知初始化--待处理
	 * @version V1.0.0
	 * @title
	 * @param currentPage 当前页对象
	 * @return OutputData 关闭
	 */
	@Override
	public List<SurveyApplyBO> findClaimHITask(SurveyApplyBO surveyApplyBO) {
		// 增补告知任务表
		ClaimHiTaskPO claimHiTaskPO = BeanUtils.copyProperties(ClaimHiTaskPO.class, surveyApplyBO);
		ClaimHiTaskPO po = iClaimHiTaskDao.findClaimHiTasks(claimHiTaskPO);
		List<SurveyApplyBO> surveyApplyBOs = new ArrayList<>();
		SurveyApplyBO applyBO = new SurveyApplyBO();
		return surveyApplyBOs;
	}

	/**
	 * @description 发起增补告知
	 * @version V1.0.0
	 * @title
	 * @param currentPage 当前页对象
	 * @return OutputData 关闭
	 */
	@Override
	public SurveyApplyBO startHITask(SurveyApplyBO surveyApplyBO) {
		SaveAndCheckInputData saveAndCheckInputData = new SaveAndCheckInputData();
		// clmServiceUtil.saveAndCheckAppInfoCLM(saveAndCheckInputData);
		return BeanUtils.copyProperties(SurveyApplyBO.class, surveyApplyPO);
	}

	/**
	 * @description 资料上传
	 * @version V1.0.0
	 * @title
	 * @param currentPage 当前页对象
	 * @return OutputData 关闭
	 */
	@Override
	public String uploadVideoImage(SurveyApplyBO surveyApplyBO) {
		// 1.设置影像接口参数
		String[] url = Constants.SERVICEENVPARAMAP.get("ECS").split("/ecs");
		logger.debug("UDMP关于影像地址：" + url[0]);
		IPageRegisterServiceProcess process = PageRegisterProcessImpl.getInstance(url[0]);
		List<String> roles = Arrays.asList("8a889cf64784fab9014785021954003e"); // @invalid
																				// 设置用户角色集合
		List<BaseBusiObject<BaseDocObject>> businessObjects = new ArrayList<BaseBusiObject<BaseDocObject>>();// @invalid
																												// 设置业务对象初始化集合
		BaseBusiObject<BaseDocObject> baseBusiObject = new BaseBusiObject<BaseDocObject>();
		if (surveyApplyBO.getCsAcceptCode() != null && !surveyApplyBO.getCsAcceptCode().equals("")) { // @invalid
																										// 设置赔案号，供新增单证菜单使用
			baseBusiObject.setEcmBusiCode(surveyApplyBO.getCsAcceptCode());
		}
		baseBusiObject.setEcmBusiType("bq");
		businessObjects.add(baseBusiObject);
		String orgCode = surveyApplyBO.getOrganCode();// @invalid 设置用户机构号
		String scanner = AppUserContext.getCurrentUser().getUserName(); // @invalid
																		// 设置扫描人
		PluginRegisterInfo registerInfo;
		String scanPageUrl = null;
		Map<String, String> inputMap = new HashMap<String, String>();
		int a = Constants.SERVICEENVPARAMAP.get("CLM").substring(0, Constants.SERVICEENVPARAMAP.get("CLM").length() - 1)
				.lastIndexOf("/");
		String clmUrl = Constants.SERVICEENVPARAMAP.get("CLM").substring(0, a);
		inputMap.put("resultServiceIp", clmUrl);
		// @invalid 据赔案状态等设置lpStatusFlag标识值(t_claim_case)
		// if (claimChecklistBO.getCaseNo() != null &&
		// !claimChecklistBO.getCaseNo().equals("")) {
		// ClaimCasePO casePO = new ClaimCasePO();
		// casePO.setCaseNo(claimChecklistBO.getCaseNo());
		// casePO = claimCaseDao.findCaseIdByCaseNo(casePO);
		// if (casePO.getCaseStatus().equals("70") ||
		// casePO.getCaseStatus().equals("71") ||
		// casePO.getCaseStatus().equals("80")) { //@invalid 审批(70待审批
		// 71审批中)以及结案完成的赔案，不能进行删除和替换，可以新增其他的单证类型
		// inputMap.put("lpStatusFlag", ClaimConstant.ONE+"");
		// } else {
		// inputMap.put("lpStatusFlag", ClaimConstant.ZERO+"");
		// }
		// } else {
		// inputMap.put("lpStatusFlag", ClaimConstant.ZERO+"");
		// }
		inputMap.put("lpStatusFlag", ClaimConstant.ZERO + "");// @invalid
																// 因目前已不用lpStatusFlag校验，直接赋0。
		// @invalid 设置是否拒付通知书类单证标识。
		// inputMap.put("isRejectPayDoc",claimChecklistBO.getIsRejectPayDoc());
		// @invalid 设置是否实名制查验类单证标识。
		// inputMap.put("isRealNameDoc",claimChecklistBO.getIsRealNameDoc());
		// @invalid 获取影像返回地址信息
		// @invalid 获取影像返回地址信息
		try {
			logger.debug("process日志：" + process);
			logger.debug("接口入参日志：roles:" + roles + " ,orgCode:" + orgCode + ",scanner:" + scanner + ",businessObjects:"
					+ businessObjects + ",inputMap:" + inputMap.toString());
			// 2.调用接口获取url返回
			registerInfo = process.imageScanRegister(roles, orgCode, scanner, businessObjects, inputMap);
			scanPageUrl = registerInfo.getAppUrl();
			logger.debug("影像返回地址：" + scanPageUrl + " resultServiceIp:" + inputMap.toString());
		} catch (Exception e) {
			e.printStackTrace();
			throw new BizException(e.getMessage());
		}
		return scanPageUrl;
	}

	/**
	 * @description 增补告知
	 * @version V1.0.0
	 * @title
	 * @param currentPage 当前页对象
	 * @return OutputData 关闭
	 */
	@Override
	public ClaimHiTaskBO findHiTaskManage(SurveyApplyBO surveyApplyBO) {
		// 查询被保人信息
		ClaimHiTaskBO claimHiTaskBO = new ClaimHiTaskBO();
		ClaimHiTaskPO claimHiTaskPO = new ClaimHiTaskPO();
		CustomerVO custemerVO = new CustomerVO();
		List<CustomerVO> customerVOs = new ArrayList<>();
		List<ClaimHiTaskPO> insuredLists = new ArrayList<>();
		List<ClaimHiTaskPO> userNames = new ArrayList<>();
		// 查询受理人
		claimHiTaskPO.getData().put("organ_code", surveyApplyBO.getOrganCode());
		userNames = iClaimHiTaskDao.finduserNames(claimHiTaskPO);

		if (surveyApplyBO.getApplyId() != null) {
			claimHiTaskPO.setApplyId(surveyApplyBO.getApplyId());
			claimHiTaskPO.getData().put("policy_code", surveyApplyBO.getPolicyCode());
			insuredLists = iClaimHiTaskDao.findInsuredLists(claimHiTaskPO);
		}
		if (insuredLists.size() > 0) {
			for (int i = 0; i < insuredLists.size(); i++) {
				String customerName = "";
				String customerId = "";
				customerName = (String) insuredLists.get(i).getData().get("customer_name");
				customerId = insuredLists.get(i).getData().get("customer_id").toString();
				if (customerName != null) {
					custemerVO.setCustomerName(customerName);
					custemerVO.setCustomerIdStr(customerId);
					customerVOs.add(custemerVO);
				}
			}
		}
		List<UserVO> userVos = new ArrayList<>();
		if (userNames.size() > 0) {
			for (int i = 0; i < userNames.size(); i++) {
				String userName = "";
				String userId = "";
				String user = "";
				userName = (String) userNames.get(i).getData().get("real_name");
				userId = userNames.get(i).getData().get("user_id").toString();
				user = userId + "-" + userName;
				if (userNames != null) {
					UserVO userVO = new UserVO();
					userVO.setUserName(user);
					userVos.add(userVO);
				}
			}
		}

//		List<UserVO> userVo = DataPermissionUtil.getInstance().findUserBySameAndSubOrgCodeAndPermName(surveyApplyBO.getOrganCode(),"IN01");
		claimHiTaskBO.setCustomerVOs(customerVOs);
		claimHiTaskBO.setUserVOs(userVos);
		claimHiTaskBO.setApplyConfirmTime(WorkDateUtil.getWorkDate());
		claimHiTaskBO.setApplyConfirmTimeStr(DateUtil.utilDateToString(WorkDateUtil.getWorkDate()));
		return claimHiTaskBO;
	}

	@Override
	public ClaimHiTaskBO findInsureLists(CustomerBO customerBO, String organCode, String acceptorName) {
		ClaimHiTaskBO claimHiTaskBO = new ClaimHiTaskBO();
		ClaimHiTaskPO claimHiTaskPO = new ClaimHiTaskPO();
		CustomerPO customerPO = new CustomerPO();
		customerPO.setCustomerId(customerBO.getCustomerId());
		customerPO = customerDao.findCustomer(customerPO);
		if (customerPO.getCustomerName() == null) {
			customerPO.setCustomerId(customerBO.getCustomerId());
			customerPO = customerDao.findPASCustomer(customerPO);
		}
		customerBO = BeanUtils.copyProperties(CustomerBO.class, customerPO);
		customerBO.setCustomerCertType(
				CodeUtils.getValueByCode("APP___CLM__DBUSER.T_CERTI_TYPE", customerBO.getCustomerCertType()));
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		customerBO.setCustomerGenderStr(
				CodeUtils.getValueByCode("APP___CLM__DBUSER.T_GENDER", customerBO.getCustomerGender().toString()));
		String birthday = dateFormat.format(customerBO.getCustomerBirthday());

		// 受理人
		claimHiTaskBO = BeanUtils.copyProperties(ClaimHiTaskBO.class, customerBO);
		claimHiTaskBO.setCustomerCertType(customerBO.getCustomerCertType());
		claimHiTaskBO.setCustomerCertiCode(customerBO.getCustomerCertiCode());
		claimHiTaskBO.setCustomerGenderStr(customerBO.getCustomerGenderStr());
//		claimHiTaskBO.setApplyConfirmTimeStr(DateUtil.utilDateToString(WorkDateUtil.getWorkDate()));
		claimHiTaskBO.setCustomerBirthdayStr(DateUtil.utilDateToString(customerBO.getCustomerBirthday()));
		List<CustomerVO> customerVOs = new ArrayList<>();
		customerVOs.add(BeanUtils.copyProperties(CustomerVO.class, customerBO));
		claimHiTaskBO.setCustomerVOs(customerVOs);
		if (!"0".equals(acceptorName)) {
//			String[] s1=acceptorName.split("-");
//			if(s1.length>0) {
//				claimHiTaskBO.setAcceptorId(new BigDecimal(s1[0]));
//				claimHiTaskBO.setAcceptorName(s1[1]);
//			}
			claimHiTaskBO.setAcceptorName(acceptorName);
		} else {
			List<ClaimHiTaskPO> userNames = new ArrayList<>();
			// 查询受理人
			claimHiTaskPO.getData().put("organ_code", organCode);
			userNames = iClaimHiTaskDao.finduserNames(claimHiTaskPO);
			List<UserVO> userVos = new ArrayList<>();
			if (userNames.size() > 0) {
				for (int i = 0; i < userNames.size(); i++) {
					String userName = "";
					String userId = "";
					String user = "";
					userName = (String) userNames.get(i).getData().get("real_name");
					userId = userNames.get(i).getData().get("user_id").toString();
					user = userId + "-" + userName;
					if (userNames != null) {
						UserVO userVO = new UserVO();
						userVO.setUserName(user);
						userVos.add(userVO);
					}
				}
			}
			claimHiTaskBO.setUserVOs(userVos);
		}
		if (claimHiTaskBO.getCustomerName() != null) {
			claimHiTaskBO.setCustomerName(customerBO.getCustomerName());
		}

		return claimHiTaskBO;
	}

	/**
	 * 发起增补告知提交保全
	 * 
	 * @description
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @return 返回查询界面
	 */
	@Override
	public com.nci.tunan.clm.interfaces.model.bo.ClaimHiTaskBO updateAppInfoCLM(
			com.nci.tunan.clm.interfaces.model.bo.ClaimHiTaskBO claimHiTaskBO) {
		com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.InputData inputData = new com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.InputData();
		ClaimHiTaskPO claimHiTaskPO = new ClaimHiTaskPO();
		claimHiTaskPO = BeanUtils.copyProperties(ClaimHiTaskPO.class, claimHiTaskBO);
		List<ClaimHiTaskPO> ClaimHiTaskPOs = iClaimHiTaskDao.findAllClaimHiTaskBycode(claimHiTaskPO);
		CsApplyList csApplyList = new CsApplyList();
		List<CsApplyInfoS> csApplyInfoS = new ArrayList<>();
		CsApplyInfoS applyInfoS = new CsApplyInfoS();
		applyInfoS.setAcceptCode(claimHiTaskBO.getAcceptCode());
		applyInfoS.setApplyCode(claimHiTaskBO.getApplyCode());
		if (ClaimHiTaskPOs.size() > 0) {
			BigDecimal taskStatus =  ClaimHiTaskPOs.get(0).getTaskStatus();
			if (ClaimConstant.BIGDECIMAL_ONE.compareTo(taskStatus) == 0) {
				applyInfoS.setClmSubmitStatus(ClaimConstant.STRING_SORT_ONE);
			} else if (ClaimConstant.BIGDECIMAL_THREE.compareTo(taskStatus) == 0) {
				applyInfoS.setClmSubmitStatus(ClaimConstant.STRING_SORT_THREE);
			} else {
				applyInfoS.setClmSubmitStatus(null);
			}
		}
		csApplyInfoS.add(applyInfoS);
		csApplyList.setCsApplyInfoS(csApplyInfoS);
		inputData.setCsApplyList(csApplyList);
		com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.SaveAndCheckOutputData outPutData = cusApplicationService
				.updateAppInfoCLM(inputData);
		claimHiTaskBO.setResultMsg(outPutData.getResultMsg());
		//更新状态为已发起
		if (claimHiTaskPO != null) {
			// 成功 更新保全申请号和受理号且任务状态为“已发起”
			claimHiTaskPO.setTaskStatus(new BigDecimal("2"));
			// 保全申请号
			claimHiTaskPO.setApplyCode(claimHiTaskBO.getApplyCode());
			// 保全受理号
			claimHiTaskPO.setAcceptCode(claimHiTaskBO.getAcceptCode());
			iClaimHiTaskDao.updateTaskStatusByapplyCode(claimHiTaskPO);
		}
		return claimHiTaskBO;
	}

	/**
	 * 发起增补告知（保全）
	 * 
	 * @description
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @return 返回查询界面
	 */
	@Override
	public com.nci.tunan.clm.interfaces.model.bo.ClaimHiTaskBO SaveAndCheckOutputData(
			com.nci.tunan.clm.interfaces.model.bo.ClaimHiTaskBO claimHiTaskBO) {
		if ("0".equals(claimHiTaskBO.getAcceptorName())) {
			claimHiTaskBO.setAcceptorId(null);
			claimHiTaskBO.setAcceptorName(null);
		} else {
			String[] temp = claimHiTaskBO.getAcceptorName().split("-");
			claimHiTaskBO.setAcceptorId(temp[0] == null ? null:new BigDecimal(temp[0]));
			claimHiTaskBO.setAcceptorName(temp[1]);
		}
		CustomerPO customerPO = new CustomerPO();
		customerPO.setCustomerId(new BigDecimal(claimHiTaskBO.getCustomerId()));
		customerPO = customerDao.findPASCustomer(customerPO);
		com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.SaveAndCheckInputData saveAndCheckInputData = new com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.SaveAndCheckInputData();
		saveAndCheckInputData.setCustomerId(claimHiTaskBO.getCustomerId());
		saveAndCheckInputData.setApplyName(customerPO.getCustomerName());
		saveAndCheckInputData.setAppType("7");
		saveAndCheckInputData.setApplyTime(DateUtil.utilDateToString(WorkDateUtil.getWorkDate()));
		saveAndCheckInputData.setServiceCode("HI");
		saveAndCheckInputData.setPolicyCode(claimHiTaskBO.getPolicyCode());
		saveAndCheckInputData.setAcceptOperatirId(claimHiTaskBO.getAcceptorId() == null ? null : claimHiTaskBO.getAcceptorId().toString());
		logger.info("调用保全增补告知入参:"+XmlHelper.classToXml(saveAndCheckInputData));
		com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.SaveAndCheckOutputData outPutData = cusApplicationService
				.saveAndCheckAppInfoCLM(saveAndCheckInputData);
		logger.info("调用保全增补告知出参:"+XmlHelper.classToXml(outPutData));
		// 增补告知/待处理 listid查询有记录 -- 更新失败原因
		ClaimHiTaskPO claimHiTaskPO = new ClaimHiTaskPO();
		claimHiTaskPO.setListId(claimHiTaskBO.getListId());
	/*	com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.SaveAndCheckOutputData outPutData =
				new com.nci.tunan.cs.interfaces.serviceData.clmquerycashvalue.SaveAndCheckOutputData();
		outPutData.setResultCode("0");
		outPutData.setApplyCode("z612022010472842225");
		outPutData.setAcceptCode("z612022010472842225");*/
//				outPutData.setResultMsg("失败123");
		SurveyApplyPO surveyApplyPOCs = new SurveyApplyPO();
		surveyApplyPOCs.setPolicyCode(claimHiTaskBO.getPolicyCode());
		List<SurveyApplyPO> surveyApplyPOCses = surveyApplyDao.findAllSurveyApply(surveyApplyPOCs);
		
		if (outPutData.getResultCode() != null && outPutData.getResultCode() == "0") {
			if (claimHiTaskPO.getListId() != null) {
				claimHiTaskPO = iClaimHiTaskDao.findClaimHiTaskByListId(claimHiTaskPO);
				if (claimHiTaskPO != null) {
					surveyApplyPOCs.setApplyId(surveyApplyPOCses.get(0).getApplyId());
					surveyApplyPOCs.setCsApplyCode(outPutData.getApplyCode());
					surveyApplyPOCs.setCsAcceptCode(outPutData.getAcceptCode());
					surveyApplyPOCs.setBizType(surveyApplyPOCses.get(0).getBizType());
					surveyApplyDao.updateSurveyApply(surveyApplyPOCs);
					// 成功 更新保全申请号和受理号且任务状态为“待处理”
					claimHiTaskPO.setTaskStatus(new BigDecimal("1"));
					// 保全申请号
					claimHiTaskPO.setApplyCode(outPutData.getApplyCode());
					// 保全受理号
					claimHiTaskPO.setAcceptCode(outPutData.getAcceptCode());
					claimHiTaskPO.setApplyId(surveyApplyPOCses.get(0).getApplyId());
					claimHiTaskPO.setApplyConfirmTime(DateUtil.stringToUtilDate(claimHiTaskBO.getApplyConfirmTimeStr()));
					claimHiTaskPO.setInsuredName(customerPO.getCustomerName());
					claimHiTaskPO.setInsuredCertType(customerPO.getCustomerCertType());
					claimHiTaskPO.setInsuredCertCode(customerPO.getCustomerCertiCode());
					claimHiTaskPO.setInsuredBirthday(customerPO.getCustomerBirthday());
					claimHiTaskPO.setInsuredGender(customerPO.getCustomerGender());
					iClaimHiTaskDao.updateClaimHiTask(claimHiTaskPO);
				}
			} else {
				surveyApplyPOCs.setApplyId(surveyApplyPOCses.get(0).getApplyId());
				surveyApplyPOCs.setCsApplyCode(outPutData.getApplyCode());
				surveyApplyPOCs.setCsAcceptCode(outPutData.getAcceptCode());
				surveyApplyPOCs.setBizType(surveyApplyPOCses.get(0).getBizType());
				surveyApplyDao.updateSurveyApplyClmById(surveyApplyPOCs);
				// 无记录 保存页面元素 且任务状态为待处理 TASK_STATUS=1
				ClaimHiTaskPO cHiTaskPO = new ClaimHiTaskPO();
				cHiTaskPO = BeanUtils.copyProperties(ClaimHiTaskPO.class, claimHiTaskBO);
				cHiTaskPO.setApplyId(surveyApplyPOCses.get(0).getApplyId());
				cHiTaskPO.setServiceCode(claimHiTaskBO.getServiceCode());
				cHiTaskPO.setAcceptorId(claimHiTaskBO.getAcceptorId() == null ? null : new BigDecimal(claimHiTaskBO.getAcceptorId().toString()));
				Charset charset = Charset.defaultCharset();			 
				try {
					String	acceptorName = URLDecoder.decode(claimHiTaskBO.getAcceptorName(), "UTF-8");
					cHiTaskPO.setAcceptorName(acceptorName);
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
					throw new BizException(e.getMessage());
				}
				cHiTaskPO.setApplyBy(claimHiTaskBO.getApplyBy());
				cHiTaskPO.setApplyConfirmTime(DateUtil.stringToUtilDate(claimHiTaskBO.getApplyConfirmTimeStr()));
				cHiTaskPO.setAcceptCode(outPutData.getAcceptCode());
				cHiTaskPO.setApplyCode(outPutData.getApplyCode());
				cHiTaskPO.setTaskStatus(new BigDecimal("1"));
				cHiTaskPO.setInsuredName(customerPO.getCustomerName());
				cHiTaskPO.setInsuredCertType(customerPO.getCustomerCertType());
				cHiTaskPO.setInsuredCertCode(customerPO.getCustomerCertiCode());
				cHiTaskPO.setInsuredBirthday(customerPO.getCustomerBirthday());
				cHiTaskPO.setInsuredGender(customerPO.getCustomerGender());
				cHiTaskPO.setBackReason(outPutData.getResultMsg());
				cHiTaskPO = iClaimHiTaskDao.addClaimHiTask(cHiTaskPO);
			}
			claimHiTaskBO.setAcceptCode(outPutData.getAcceptCode());
			claimHiTaskBO.setApplyCode(outPutData.getApplyCode());
			claimHiTaskBO.setCustomerCertiCode(customerPO.getCustomerCertiCode());
			claimHiTaskBO.setCustomerBirthdayStr(customerPO.getCustomerBirthday()==null?null:DateUtil.utilDateToString(customerPO.getCustomerBirthday()));
			claimHiTaskBO.setCustomerCertType(CodeUtils.getValueByCode("APP___CLM__DBUSER.T_CERTI_TYPE", customerPO.getCustomerCertType()));
			if(customerPO.getCustomerGender()!=null) {
				claimHiTaskBO.setCustomerGenderStr(CodeUtils.getValueByCode("APP___CLM__DBUSER.T_GENDER", customerPO.getCustomerGender().toString()));
			}
			return claimHiTaskBO;
		}
		// 0成功1失败
		if (outPutData.getResultCode() != null && outPutData.getResultCode() == "1") {
			if (claimHiTaskPO.getListId() != null) {
				claimHiTaskPO = iClaimHiTaskDao.findClaimHiTaskByListId(claimHiTaskPO);
				if (claimHiTaskPO != null) {
					// 失败 更新失败原因 任务状态为“待处理”；
					claimHiTaskPO.setTaskStatus(new BigDecimal("1"));
					claimHiTaskPO.setApplyId(surveyApplyPOCses.get(0).getApplyId());
					claimHiTaskPO = iClaimHiTaskDao.updateClaimHiTask(claimHiTaskPO);
					claimHiTaskBO = BeanUtils.copyProperties(ClaimHiTaskBO.class, claimHiTaskPO);
				}
			} else {
				// 无记录 保存页面元素 且任务状态为待处理 TASK_STATUS=1
				ClaimHiTaskPO cHiTaskPO = new ClaimHiTaskPO();
				cHiTaskPO = BeanUtils.copyProperties(ClaimHiTaskPO.class, claimHiTaskBO);
				cHiTaskPO.setApplyId(surveyApplyPOCses.get(0).getApplyId());
				cHiTaskPO.setServiceCode(claimHiTaskBO.getServiceCode());
				cHiTaskPO.setAcceptorId(claimHiTaskBO.getAcceptorId() == null ? null : new BigDecimal(claimHiTaskBO.getAcceptorId().toString()));
				try {
					String	acceptorName = URLDecoder.decode(claimHiTaskBO.getAcceptorName(), "UTF-8");
					cHiTaskPO.setAcceptorName(acceptorName);
				} catch (UnsupportedEncodingException e) {
					e.printStackTrace();
					throw new BizException(e.getMessage());
				}
				cHiTaskPO.setApplyBy(claimHiTaskBO.getApplyBy());
				cHiTaskPO.setApplyConfirmTime(DateUtil.stringToUtilDate(claimHiTaskBO.getApplyConfirmTimeStr()));
				cHiTaskPO.setAcceptCode(outPutData.getAcceptCode());
				cHiTaskPO.setApplyCode(outPutData.getApplyCode());
				cHiTaskPO.setTaskStatus(new BigDecimal("1"));
				cHiTaskPO.setInsuredName(customerPO.getCustomerName());
				cHiTaskPO.setInsuredCertType(customerPO.getCustomerCertType());
				cHiTaskPO.setInsuredCertCode(customerPO.getCustomerCertiCode());
				cHiTaskPO.setInsuredBirthday(customerPO.getCustomerBirthday());
				cHiTaskPO.setInsuredGender(customerPO.getCustomerGender());
				cHiTaskPO.setBackReason(outPutData.getResultMsg());
				cHiTaskPO = iClaimHiTaskDao.addClaimHiTask(cHiTaskPO);
				claimHiTaskBO = BeanUtils.copyProperties(ClaimHiTaskBO.class, cHiTaskPO);
			}
			claimHiTaskBO.setAcceptCode(outPutData.getAcceptCode());
			claimHiTaskBO.setApplyCode(outPutData.getApplyCode());
			claimHiTaskBO.setCustomerCertiCode(customerPO.getCustomerCertiCode());
			if(customerPO.getCustomerBirthday()!=null) {
				claimHiTaskBO.setCustomerBirthdayStr(DateUtil.utilDateToString(customerPO.getCustomerBirthday()));
			}
			claimHiTaskBO.setCustomerCertType(CodeUtils.getValueByCode("APP___CLM__DBUSER.T_CERTI_TYPE", customerPO.getCustomerCertType()));
			claimHiTaskBO.setCustomerGenderStr(CodeUtils.getValueByCode("APP___CLM__DBUSER.T_GENDER", customerPO.getCustomerGender().toString()));
			// 返回失败原因
			claimHiTaskBO.setResultMsg(outPutData.getResultMsg());
			claimHiTaskBO.setCustomerName(customerPO.getCustomerName());
			return claimHiTaskBO;
		}
		return claimHiTaskBO;
	}
	
	/**
	 * 待处理返回增补告知
	 * 
	 * @description
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @return 返回查询界面
	 */
	@Override
	public com.nci.tunan.clm.interfaces.model.bo.ClaimHiTaskBO findTaskManage(
			com.nci.tunan.clm.interfaces.model.bo.ClaimHiTaskBO claimHiTaskBO) {
		ClaimHiTaskPO claimHiTaskPO = BeanUtils.copyProperties(ClaimHiTaskPO.class, claimHiTaskBO);
		claimHiTaskPO = iClaimHiTaskDao.findClaimHiTaskByListId(claimHiTaskPO);
		ClaimHiTaskBO hiTaskBO = BeanUtils.copyProperties(ClaimHiTaskBO.class, claimHiTaskPO);
		if(hiTaskBO.getApplyConfirmTime()!=null){
		hiTaskBO.setApplyConfirmTimeStr(DateUtil.utilDateToString(hiTaskBO.getApplyConfirmTime()));
		}
//		hiTaskBO.setTaskStatusStr(CodeUtils.getValueByCode(
//				"APP___CLM__DBUSER.T_CLAIM_TASK_STATUS",
//				hiTaskBO.getTaskStatus().toString()));
		// 查询被保人信息
		CustomerVO custemerVO = new CustomerVO();
		List<CustomerVO> customerVOs = new ArrayList<>();
		List<ClaimHiTaskPO> insuredLists = new ArrayList<>();
		List<ClaimHiTaskPO> userNames = new ArrayList<>();
		String customerCertiCodeCopy = "";
		String customerIdCopy = "";
		// 查询受理人
//		claimHiTaskPO.getData().put("organ_code", claimHiTaskBO.getOrganCode());
//		userNames = iClaimHiTaskDao.finduserNames(claimHiTaskPO);
		if(claimHiTaskBO.getPolicyCode()!=null) {
			claimHiTaskPO.getData().put("policy_code", claimHiTaskBO.getPolicyCode());
			insuredLists = iClaimHiTaskDao.findInsuredLists(claimHiTaskPO);
			if (insuredLists.size() > 0) {
				for (int i = 0; i < insuredLists.size(); i++) {
						String customerName = "";
						String customerId = "";
						if(insuredLists.get(i).getData().get("customer_name")!=null){
							customerName = (String) insuredLists.get(i).getData().get("customer_name");
						}
						if(insuredLists.get(i).getData().get("customer_id")!=null){
							customerId = insuredLists.get(i).getData().get("customer_id").toString();
						}
						if (customerName != null) {
							if(customerName.equals(hiTaskBO.getInsuredName())) {
								customerIdCopy = customerId;
								if(insuredLists.get(i).getData().get("customer_certi_code")!=null) {
								customerCertiCodeCopy = insuredLists.get(i).getData().get("customer_certi_code").toString();
								}
							}
							custemerVO.setCustomerName(customerName);
							custemerVO.setCustomerIdStr(customerId);
							customerVOs.add(custemerVO);
						}
					}
				}
		}
//				List<UserVO> userVos = new ArrayList<>();
//				if (userNames.size() > 0) {
//					for (int i = 0; i < userNames.size(); i++) {
//						String userName = "";
//						String userId = "";
//						String user = "";
//						userName = (String) userNames.get(i).getData().get("real_name");
//						userId = userNames.get(i).getData().get("user_id").toString();
//						user = userId + "-" + userName;
//						if (userNames != null) {
//							UserVO userVO = new UserVO();
//							userVO.setUserName(user);
//							userVos.add(userVO);
//						}
//					}
//				}

//				List<UserVO> userVo = DataPermissionUtil.getInstance().findUserBySameAndSubOrgCodeAndPermName(surveyApplyBO.getOrganCode(),"IN01");
		String names = hiTaskBO.getAcceptorId()+"-"+hiTaskBO.getAcceptorName();
		hiTaskBO.setAcceptorName(names);
		hiTaskBO.setCustomerVOs(customerVOs);
//				claimHiTaskBO.setUserVOs(userVos);
		hiTaskBO.setApplyConfirmTime(WorkDateUtil.getWorkDate());
		hiTaskBO.setApplyConfirmTimeStr(DateUtil.utilDateToString(WorkDateUtil.getWorkDate()));
		//被保人信息
		hiTaskBO.setCustomerName(hiTaskBO.getInsuredName());
		hiTaskBO.setCustomerId(customerIdCopy);
		hiTaskBO.setCustomerCertType(CodeUtils.getValueByCode("APP___CLM__DBUSER.T_CERTI_TYPE", hiTaskBO.getInsuredCertType()));
		hiTaskBO.setCustomerCertiCode(customerCertiCodeCopy);
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		hiTaskBO.setCustomerBirthdayStr(hiTaskBO.getInsuredBirthday()==null?null:dateFormat.format(hiTaskBO.getInsuredBirthday()));
		hiTaskBO.setCustomerGenderStr(hiTaskBO.getInsuredGender()==null?null:CodeUtils.getValueByCode("APP___CLM__DBUSER.T_GENDER", hiTaskBO.getInsuredGender().toString()));
		return hiTaskBO;
	}

	public ICusApplicationService getCusApplicationService() {
		return cusApplicationService;
	}

	public void setCusApplicationService(ICusApplicationService cusApplicationService) {
		this.cusApplicationService = cusApplicationService;
	}

	public ISurveyApplyDao getSurveyApplyDao() {
		return surveyApplyDao;
	}

	public void setSurveyApplyDao(ISurveyApplyDao surveyApplyDao) {
		this.surveyApplyDao = surveyApplyDao;
	}

	public SurveyApplyDaoImpl getSurveyApplyDaoImpl() {
		return surveyApplyDaoImpl;
	}

	public void setSurveyApplyDaoImpl(SurveyApplyDaoImpl surveyApplyDaoImpl) {
		this.surveyApplyDaoImpl = surveyApplyDaoImpl;
	}

	public IClaimHiTaskDao getiClaimHiTaskDao() {
		return iClaimHiTaskDao;
	}

	public void setiClaimHiTaskDao(IClaimHiTaskDao iClaimHiTaskDao) {
		this.iClaimHiTaskDao = iClaimHiTaskDao;
	}

	public ClaimHiTaskPO getClaimHiTaskPO() {
		return ClaimHiTaskPO;
	}

	public void setClaimHiTaskPO(ClaimHiTaskPO claimHiTaskPO) {
		ClaimHiTaskPO = claimHiTaskPO;
	}

	public SurveyApplyPO getSurveyApplyPO() {
		return surveyApplyPO;
	}

	public IcancelApplyService getCancelApplyService() {
		return cancelApplyService;
	}

	public void setCancelApplyService(IcancelApplyService cancelApplyService) {
		this.cancelApplyService = cancelApplyService;
	}

	public void setSurveyApplyPO(SurveyApplyPO surveyApplyPO) {
		this.surveyApplyPO = surveyApplyPO;
	}

	public InputData getInputData() {
		return inputData;
	}

	public void setInputData(InputData inputData) {
		this.inputData = inputData;
	}

	public ICLMServiceUtil getClmServiceUtil() {
		return clmServiceUtil;
	}

	public void setClmServiceUtil(ICLMServiceUtil clmServiceUtil) {
		this.clmServiceUtil = clmServiceUtil;
	}

	public SurveyConclusionPO getCurveyConclusionPO() {
		return curveyConclusionPO;
	}

	public void setCurveyConclusionPO(SurveyConclusionPO curveyConclusionPO) {
		this.curveyConclusionPO = curveyConclusionPO;
	}

	public ClaimHiTaskPO getClaimHiTaskBO() {
		return ClaimHiTaskBO;
	}

	public void setClaimHiTaskBO(ClaimHiTaskPO claimHiTaskBO) {
		ClaimHiTaskBO = claimHiTaskBO;
	}

	public ICustomerDao getCustomerDao() {
		return customerDao;
	}

	public void setCustomerDao(ICustomerDao customerDao) {
		this.customerDao = customerDao;
	}


}
