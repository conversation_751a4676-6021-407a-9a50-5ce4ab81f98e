package com.nci.tunan.clm.impl.peripheral.exports.r00101000145.iclaimquerychecklistucc.queryclaimexsistedchecklist.ws;

import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.json.DataSerialJSon;
import javax.jws.WebService;
import javax.xml.ws.Holder;
import org.slf4j.Logger;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000145.iclaimquerychecklistucc.queryclaimexsistedchecklist.SrvReqBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000145.iclaimquerychecklistucc.queryclaimexsistedchecklist.SrvResBizBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000145.iclaimquerychecklistucc.queryclaimexsistedchecklist.SrvResBody;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000145.iclaimquerychecklistucc.queryclaimexsistedchecklist.ws.IClaimQueryCheckListUCCWS;
import com.nci.tunan.clm.impl.peripheral.ucc.r00101000145.IClaimQueryCheckListUCC;
/**
 * 
 * @description 理赔单证信息查询
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/理赔查询
 * @date  2015年5月15日 上午10:08:31
 */
@WebService(endpointInterface = "com.nci.tunan.clm.interfaces.peripheral.exports.r00101000145.iclaimquerychecklistucc.queryclaimexsistedchecklist.ws.IClaimQueryCheckListUCCWS"
, serviceName = "ClaimQueryCheckListUCCWSImplqueryClaimExsistedCheckList")
public class ClaimQueryCheckListUCCWSImpl implements IClaimQueryCheckListUCCWS {

    /** 
     * @Fields logger : 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();
    /**
     * 理赔单证信息查询
     */
    private IClaimQueryCheckListUCC ucc = null;
    
    public IClaimQueryCheckListUCC getUcc() {
        return ucc;
    }
    public void setUcc(IClaimQueryCheckListUCC ucc) {
        this.ucc = ucc;
    }
    
    /**
     * 
     * @description 理赔单证信息查询
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.interfaces.peripheral.exports.r00101000145.iclaimquerychecklistucc.queryclaimexsistedchecklist.ws.IClaimQueryCheckListUCCWS#queryClaimExsistedCheckList(com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader, com.nci.tunan.clm.interfaces.peripheral.exports.r00101000145.iclaimquerychecklistucc.queryclaimexsistedchecklist.SrvReqBody, javax.xml.ws.Holder, javax.xml.ws.Holder)
     * @param parametersReqHeader 入参报文头
     * @param parametersReqBody 入参报文体
     * @param parametersResHeader 出参报文头
     * @param parametersResBody 出参报文体 
     */
    @Override
    public void queryClaimExsistedCheckList(SysHeader parametersReqHeader, SrvReqBody parametersReqBody, 
            Holder<SysHeader> parametersResHeader, Holder<SrvResBody> parametersResBody) {//理赔单证信息查询
        
        CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
        CommonHeaderDeal.setBIZHEADERTHREAD_EXB(parametersReqBody.getBizHeader());
        com.nci.tunan.clm.interfaces.peripheral.exports.r00101000145.vo.InputData inputVO = parametersReqBody.getBizBody().getInputData();
        try {
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(parametersReqHeader));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersReqBody.getBizHeader()));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersReqBody.getBizBody()));
        } catch (Exception e1) {
            e1.printStackTrace();
            logger.debug("解析调用前报文的JSON字符串发生异常");
        }
        SrvResBody srvResBody = new SrvResBody();
        SysHeader sysHeader = new SysHeader();
        try {
            com.nci.tunan.clm.interfaces.peripheral.exports.r00101000145.vo.OutputData output = ucc.queryClaimExsistedCheckList(inputVO);
            sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
             BizHeaderExB  bizHeader = CommonHeaderDeal.dealBizHeader(parametersReqBody.getBizHeader());
            SrvResBizBody bizResBody = new SrvResBizBody();
            bizResBody.setOutputData(output);
            srvResBody.setBizBody(bizResBody);
            srvResBody.setBizHeader(bizHeader);
            parametersResHeader.value = sysHeader;
            parametersResBody.value = srvResBody;
        } catch (Exception e2) {
        	logger.error("调用接口过程中产生异常!",e2);
        }
        try {
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(parametersResHeader.value));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersResBody.value.getBizHeader()));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersResBody.value.getBizBody()));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析调用后报文的JSON字符串发生异常");
        } 
    }
}
 

