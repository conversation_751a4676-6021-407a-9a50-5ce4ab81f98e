package com.nci.tunan.clm.impl.peripheral.service.r00101000106.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.nci.core.common.impl.util.CodeMapperUtils;
import com.nci.tunan.clm.dao.IClaimBillDao;
import com.nci.tunan.clm.dao.IClaimBillItemDao;
import com.nci.tunan.clm.dao.IClaimBillPaidDao;
import com.nci.tunan.clm.dao.IClaimCaseDao;
import com.nci.tunan.clm.dao.IClaimLiabBillRelationDao;
import com.nci.tunan.clm.dao.IClaimLiabDao;
import com.nci.tunan.clm.dao.IClobDao;
import com.nci.tunan.clm.dao.ICustomerDao;
import com.nci.tunan.clm.dao.IUserDao;
import com.nci.tunan.clm.impl.peripheral.service.r00101000106.IClaimMedBillQueryService;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.interfaces.model.po.ClaimBillItemPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBillPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBillPaidPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimLiabBillRelationPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimLiabPO;
import com.nci.tunan.clm.interfaces.model.po.CustomerPO;
import com.nci.tunan.clm.interfaces.model.po.UserPO;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000106.vo.InputData;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000106.vo.OutputData;
import com.nci.tunan.clm.interfaces.peripheral.exports.r00101000106.vo.Result;
import com.nci.udmp.util.lang.DateUtilsEx;

/**
 * 
 * @description 医疗理赔清单报告书查询接口
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/医疗理赔
 * @date  2015年5月15日 上午10:08:31
 */
public class ClaimMedBillQueryServiceImpl implements IClaimMedBillQueryService {
    /**
     * 理赔赔案主表Dao
     */
	private IClaimCaseDao claimCaseDao;
	/**
     * 客户Dao
     */
	private ICustomerDao customerDao;
	/**
     * 给付责任与医疗费用的关系Dao
     */
	private IClaimLiabBillRelationDao claimLiabBillRelationDao;
	/**
     * 医疗费用主表Dao
     */
	private IClaimBillDao claimBillDao;
	/**
     * 理赔给付责任理算Dao
     */
	private IClaimLiabDao claimLiabDao;
	/**
     * 社保/第三方支付信息表Dao
     */
	private IClaimBillPaidDao claimBillPaidDao;
	/**
     * 新契约大数据表Dao
     */
	private IClobDao clobDao;
	/**
     * 用户Dao
     */
	private IUserDao userDao; 
	/**
     * 医疗费用明细表Dao
     */
	private IClaimBillItemDao claimbillItemDao;

	/**
	 * 
	 * @description 查询理赔医疗账单
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.peripheral.service.r00101000106.IClaimMedBillQueryService#queryClaimMedBill(com.nci.tunan.clm.interfaces.peripheral.exports.r00101000106.vo.InputData)
	 * @param inputData 入参
	 * @return OutputData 结果出参
	 */
	@Override
    public OutputData queryClaimMedBill(InputData inputData) {
        
		OutputData outputData = new OutputData();
		boolean flag = false;
		int resultCount = 0;
		List<Result> resultList = new ArrayList<Result>();
		List<ClaimCasePO> claimList = new ArrayList<ClaimCasePO>();
		ClaimCasePO claimCasePO = new ClaimCasePO();
		
		//1.获取入参数据并组装查询条件
		claimCasePO.setCaseNo(inputData.getClmNo());//@invalid 赔案号
		//2.根据赔案号查询赔案信息T_CLAIM_CASE
		claimList = claimCaseDao.findAllClaimCase(claimCasePO);
		if(claimList!=null && claimList.size()>0){
			claimCasePO = claimList.get(0);
		}else{
		    return outputData;
		}
		BigDecimal caseId = claimCasePO.getCaseId();
		BigDecimal insuredId = claimCasePO.getInsuredId();
		CustomerPO customerPO = new CustomerPO();
		customerPO.setCustomerId(insuredId);
		//3.根据赔案的出险人号查询客户信息T_CUSTOMER
		customerPO = customerDao.findCustomerByCustomerId(customerPO);
		BigDecimal thirdFee = new BigDecimal(0);
		BigDecimal socialInsuranceFee = new BigDecimal(0); 
		ClaimBillPaidPO claimBillPaidPO = new ClaimBillPaidPO();
		claimBillPaidPO.setCaseId(caseId);
		//4.根据赔案ID查询社保第三方T_CLAIM_BILL_PAID
		List<ClaimBillPaidPO> claimBillPaidPOList = claimBillPaidDao.findAllClaimBillPaid(claimBillPaidPO);
		for(int i = 0; i < claimBillPaidPOList.size(); i++){
			// @invalid 1 社保 ； 2 第三方
			if(claimBillPaidPOList.get(i).getOtherType().equals(ClaimConstant.CLAIM_BILL_PAID_OTHER_TYPE_2)){
				thirdFee = thirdFee.add(null==claimBillPaidPOList.get(i).getPaidAmount()?BigDecimal.valueOf(0):claimBillPaidPOList.get(i).getPaidAmount());
			}		
			if(claimBillPaidPOList.get(i).getOtherType().equals(ClaimConstant.CLAIM_BILL_PAID_OTHER_TYPE_1)){
				socialInsuranceFee = socialInsuranceFee.add(null==claimBillPaidPOList.get(i).getPaidAmount()?BigDecimal.valueOf(0):claimBillPaidPOList.get(i).getPaidAmount());
			}
		}
		
		ClaimLiabBillRelationPO claimLiabBillRelationPO = new ClaimLiabBillRelationPO();
		claimLiabBillRelationPO.setCaseId(caseId);
		//@invalid 查询结果list集合
		//@invalid List<ClaimLiabBillRelationPO> relationPOList = claimLiabBillRelationDao.findClaimLiabBillRelationDistinct(claimLiabBillRelationPO);
		
		
		//@invalid  以下有关的总费用的  是保单层级的，还是责任层级的？
		
		//@invalid 原始医疗费用 =每笔的账单金额的总和？ T_CLAIM_BILL_ITEM.fee_amount
		//@invalid BigDecimal sumBillFee  =  BigDecimal.ZERO; 
		//@invalid 责任内总金额 = 每笔账单的理算金额  T_CLAIM_BILL_ITEM.calc_amount
		//@invalid BigDecimal sumInDutyFee = BigDecimal.ZERO;
		//@invalid 责任外总金额
		//@invalid BigDecimal sumOutDutyFee = BigDecimal.ZERO;
		//@invalid 给付金额 不是理算金额 现在取的是整个赔案下的。不知道是否是应该去责任的。
		String payMoney = "";
		BigDecimal payMoneyNum =  BigDecimal.ZERO;
		//5.根据赔案ID查询给付责任表数据T_CLAIM_LIAB
		ClaimLiabPO claimLiabPO = new ClaimLiabPO();
		claimLiabPO.setCaseId(claimCasePO.getCaseId());
		List<ClaimLiabPO> claimLiabPOs = claimLiabDao.findClaimLiaByCaseId(claimLiabPO);
		if(claimLiabPOs != null && !claimLiabPOs.isEmpty()){
			for(ClaimLiabPO CLpo : claimLiabPOs){
				if(CLpo.getActualPay()!=null){
					payMoneyNum = payMoneyNum.add(CLpo.getActualPay());
				}
			}
			payMoney = payMoneyNum.toString();
		}
		
		//6.根据赔案ID查询费用明细T_CLAIM_BILL_ITEM
		ClaimBillItemPO claimBillItemPO2 = new ClaimBillItemPO();
		claimBillItemPO2.setCaseId(caseId);
	    List<ClaimBillItemPO> claimBillItem = claimbillItemDao.findAllClaimBillItem(claimBillItemPO2);
		 
		//7.根据查询结果组装接口返回参数
		if(claimBillItem != null &&  claimBillItem.size() > 0){
			Result result = null;
            for(ClaimLiabPO bpo:claimLiabPOs){//@invalid 遍历理算责任可能出现一个险种对应多个理算责任
            	if(flag){
            		break;
            	}
            	//@invalid 原始医疗费用 
        		BigDecimal sumBillFee  =  BigDecimal.ZERO; 
        		//@invalid 责任内总金额 每笔账单的理算金额  T_CLAIM_BILL_ITEM.calc_amount
        		BigDecimal sumInDutyFee = BigDecimal.ZERO;
        		//@invalid 责任外总金额
        		BigDecimal sumOutDutyFee = BigDecimal.ZERO;
            	for (ClaimBillItemPO po : claimBillItem) {//@invalid 医疗费用明细表
            		if(resultCount==claimBillItem.size()){
            		   flag = true;	
            		   break;
            		}else{
            		result = new Result();
                	//@invalid if(bpo.getCaseId()==po.getCaseId()){
                		result.setName(customerPO.getCustomerName());
        				result.setContNo(bpo.getPolicyCode());
        				BigDecimal billFee = BigDecimal.ZERO;
        				if(po.getFeeAmount()!=null){
        					billFee = po.getFeeAmount();
        					result.setBillFee(po.getFeeAmount().toString());
        					sumBillFee = sumBillFee.add(billFee);
        				}else{
        					result.setBillFee("0");
        				}
        				BigDecimal inDutyFee = BigDecimal.ZERO;
        				if(StringUtils.isNotBlank(po.getMedFeeItem())&& !po.getMedFeeItem().startsWith("CO")){
        					inDutyFee = po.getCalcAmount();
        					sumInDutyFee = sumInDutyFee.add(inDutyFee);
        				}
        				result.setInDutyFee(inDutyFee.toString());
        				
        				BigDecimal outDutyFee = billFee.subtract(inDutyFee).setScale(2,BigDecimal.ROUND_DOWN);
        				result.setOutDutyFee(outDutyFee.toString());
        				if(null!=po.getMedFeeItem()&&po.getMedFeeItem().equals("CW001")){
        					 result.setFeeItemName("其它-不细分");//@invalid 老核心CW001对应"其它-不细分"
        					 
        				}else if(null!=po.getMedFeeItem()&&po.getMedFeeItem().equals("CM003")){
       					 result.setFeeItemName("药费-中成药");//@invalid 老核心CM003对应"药费-中成药"
        				}else if(null!=po.getMedFeeItem()&&po.getMedFeeItem().equals("CM004")){
        					result.setFeeItemName("药费-草药");//@invalid 老核心CM004对应"药费-草药"
        				}else if(null!=po.getMedFeeItem()&&po.getMedFeeItem().equals("CC007")){
        					result.setFeeItemName("手术费-不细分");//@invalid 老核心CC007对应"手术费-不细分"
        				}else{
        					result.setFeeItemName(po.getMedFeeItem()==null?"":CodeMapperUtils.getOldNameByNewCode("INVOICE_TYPE", po.getMedFeeItem(),"CLM"));
        					//@invalid 老核心转不去返回新核心费用名称
        					if(result.getFeeItemName().equals("")&&po.getMedFeeItem()!=null){
        						ClaimBillItemPO claimbillitempo=new ClaimBillItemPO();
        						claimbillitempo.setMedFeeItem(po.getMedFeeItem());
        						claimbillitempo=claimbillItemDao.queryFeeNameByMedfeeitem(claimbillitempo);
        						result.setFeeItemName(null==claimbillitempo.getData().get("name")?"":claimbillitempo.getData().get("name").toString());
        					}
        				}
        				 if(result.getFeeItemName().equals("药费-西药费")){//@invalid 报文比对
        					 result.setFeeItemName("药费-西药");
        				 }
        				 
        				 
        				result.setMark(po.getDeductRemark()==null?"":po.getDeductRemark());
        				ClaimBillPO claimBillPO = new ClaimBillPO();
        				claimBillPO.setBillId(po.getBillId());
        				claimBillPO = claimBillDao.findClaimBill(claimBillPO);
                        result.setStartDate(null==claimBillPO.getTreatStart()?"":DateUtilsEx.date2String(claimBillPO.getTreatStart(), "yyyy-MM-dd"));
        				result.setEndDate(null==claimBillPO.getTreatEnd()?"":DateUtilsEx.date2String(claimBillPO.getTreatEnd(), "yyyy-MM-dd"));
        				result.setThirdFee(null==thirdFee?"":thirdFee.toString());
        				result.setSocialInsuranceFee(null==socialInsuranceFee?"":socialInsuranceFee.toString());
        				result.setAuditUserName(getUserName(claimCasePO.getAuditorId()));
        				result.setExamUserName(getUserName(claimCasePO.getApproverId()));
        				result.setEndCaseDate(null==claimCasePO.getEndCaseTime()?"":DateUtilsEx.date2String(claimCasePO.getEndCaseTime(), "yyyy-MM-dd"));
        				resultList.add(result);
        				resultCount++;
            		}
            	}
            	
            	sumOutDutyFee = sumBillFee.subtract(sumInDutyFee);
            	for(Result rest :resultList ){
    				rest.setPayMoney(payMoney);
    				rest.setSumBillFee(sumBillFee.toString());
    				rest.setSumInDutyFee(sumInDutyFee.toString());
    				rest.setSumOutDutyFee(sumOutDutyFee.toString());
    			}
            	outputData.setResult(orderResult(resultList));
			}
			/*@invalid for (ClaimBillItemPO po : claimBillItem) { 和老核心逻辑不一致
				
				result = new Result();
				result.setName(customerPO.getCustomerName());
				result.setContNo("");
				
				ClaimBillItemPO claimBillItemPO = new ClaimBillItemPO();
				claimBillItemPO.setBillItemId(po.getBillItemId());
				claimBillItemPO = claimbillItemDao.findClaimBillItem(claimBillItemPO);
				
				BigDecimal billFee = BigDecimal.ZERO;
				if(claimBillItemPO.getFeeAmount()!=null){
					billFee = claimBillItemPO.getFeeAmount();
					result.setBillFee(claimBillItemPO.getFeeAmount().toString());
					sumBillFee = sumBillFee.add(billFee);
				}else{
					result.setBillFee("0");
				}
				
				BigDecimal inDutyFee = BigDecimal.ZERO;
				if(StringUtils.isNotBlank(claimBillItemPO.getMedFeeItem())&& !claimBillItemPO.getMedFeeItem().startsWith("CO")){
					inDutyFee = claimBillItemPO.getCalcAmount();
					sumInDutyFee = sumInDutyFee.add(inDutyFee);
				}
				result.setInDutyFee(inDutyFee.toString());
				
				BigDecimal outDutyFee = billFee.subtract(inDutyFee).setScale(2,BigDecimal.ROUND_DOWN);
				result.setOutDutyFee(outDutyFee.toString());
				result.setFeeItemName(claimBillItemPO.getMedFeeItem()==null?"":CodeMapperUtils.getOldNameByNewCode("INVOICE_TYPE", claimBillItemPO.getMedFeeItem(),"CLM"));
				 if(result.getFeeItemName().equals("药费-西药费")){//@invalid 报文比对
					 result.setFeeItemName("药费-西药");
				 }
				result.setMark(claimBillItemPO.getDeductRemark()==null?"":claimBillItemPO.getDeductRemark());
				
				ClaimBillPO claimBillPO = new ClaimBillPO();
				claimBillPO.setBillId(po.getBillId());
				claimBillPO = claimBillDao.findClaimBill(claimBillPO);

				
				
				result.setStartDate(null==claimBillPO.getTreatStart()?"":DateUtilsEx.date2String(claimBillPO.getTreatStart(), "yyyy-MM-dd"));
				result.setEndDate(null==claimBillPO.getTreatEnd()?"":DateUtilsEx.date2String(claimBillPO.getTreatEnd(), "yyyy-MM-dd"));
				*/
				
				//@invalid result.setSumBillFee(claimBillPO.getSumAmount()==null?"":claimBillPO.getSumAmount().toString());
				
//@invalid				sumInDutyFee = new BigDecimal(0);
//@invalid				ClaimBillItemPO querySumInDutyFee = new ClaimBillItemPO();
//@invalid				//querySumInDutyFee.setBillId(po.getBillId());
//@invalid				querySumInDutyFee.setCaseId(po.getCaseId());
//@invalid				querySumInDutyFee = claimbillItemDao.querySumInDutyFeeByBillId(claimBillItemPO);
//@invalid				//BigDecimal calcAmount = claimBillPO.getCalcAmount();
//@invalid				if(querySumInDutyFee == null || querySumInDutyFee.getBigDecimal("sumindutyfee") ==null ){
//@invalid					result.setSumInDutyFee("0");
//@invalid				}else{
//@invalid					//calcAmount = calcAmount.setScale(2);
//@invalid					sumInDutyFee = querySumInDutyFee.getBigDecimal("sumindutyfee");
//@invalid					result.setSumInDutyFee(sumInDutyFee.toString());
//@invalid				}
				
				//@invalid BigDecimal  sumOutDutyFee = claimBillPO.getSumAmount().subtract(sumInDutyFee).stripTrailingZeros();
				//@invalid result.setSumOutDutyFee(sumOutDutyFee.toString());
				
			/*@invalid 	result.setThirdFee(null==thirdFee?"":thirdFee.toString());
				result.setSocialInsuranceFee(null==socialInsuranceFee?"":socialInsuranceFee.toString());
				result.setAuditUserName(getUserName(claimCasePO.getAuditorId()));
				result.setExamUserName(getUserName(claimCasePO.getApproverId()));
				result.setEndCaseDate(null==claimCasePO.getEndCaseTime()?"":DateUtilsEx.date2String(claimCasePO.getEndCaseTime(), "yyyy-MM-dd"));

				resultList.add(result);
			}
			
			sumOutDutyFee = sumBillFee.subtract(sumInDutyFee);
			
			for(Result rest :resultList ){
				rest.setPayMoney(payMoney);
				rest.setSumBillFee(sumBillFee.toString());
				rest.setSumInDutyFee(sumInDutyFee.toString());
				rest.setSumOutDutyFee(sumOutDutyFee.toString());
			}
			
			outputData.setResult(orderResult(resultList)); */
		}
		
		return outputData;
	
	}

	/**
	 * 
	 * @description 结果排序
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param resultList 入参
	 * @return List<Result> 结果出参
	 */
	private List<Result> orderResult(List<Result> resultList){
		//1.判断入参是否为空
		if(resultList == null || resultList.isEmpty()){
			return resultList;
		}
		//2.将入参进行排序
		for(int i = 0; i<resultList.size() ;  i++){
			for(int j =0; j<resultList.size()-i-1; j++){
				//@invalid 升序
				if(resultList.get(j).getContNo().compareTo(resultList.get(j+1).getContNo())>0){
					
					Result temp = resultList.get(j+1);
					resultList.set(j+1, resultList.get(j));
					resultList.set(j, temp);
					
				}
			}
		}
		
		
		return resultList;
	} 
	
	/**
	 * 
	 * @description 获取用户名
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param userId 用户Id
	 * @return String 用户名
	 */
	private String getUserName(BigDecimal userId){
		//1.判断入参是否为空
		if(userId==null){
			return "";
		}
		
		//2.根据入参查询用户信息T_UDMP_USER
		UserPO userPO = new UserPO();
		userPO.setUserId(userId);
		userPO = userDao.findUser(userPO);
		
		if(userPO== null || userPO.getUserId()==null){
			return "";
		}
		return userPO.getRealName();
	}
	/**
	 * 
	 * @description 移除副本
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param list 入参
	 * @return List<Result> 结果
	 */
	public  List<Result> removeDuplicate(List<Result>list){  
		//1.新建新集合
        List<Result> listTemp = new ArrayList<Result>();
        //2.复制入参集合并返回
        for(int i=0;i<list.size();i++){  
            if(!listTemp.contains(list.get(i))){  
                listTemp.add(list.get(i));  
            }  
        }  
        return listTemp;  
    }
	
	
	public IClaimCaseDao getClaimCaseDao() {
		return claimCaseDao;
	}

	public void setClaimCaseDao(IClaimCaseDao claimCaseDao) {
		this.claimCaseDao = claimCaseDao;
	}

	public IClaimLiabBillRelationDao getClaimLiabBillRelationDao() {
		return claimLiabBillRelationDao;
	}

	public void setClaimLiabBillRelationDao(
			IClaimLiabBillRelationDao claimLiabBillRelationDao) {
		this.claimLiabBillRelationDao = claimLiabBillRelationDao;
	}

	public IClaimBillDao getClaimBillDao() {
		return claimBillDao;
	}

	public void setClaimBillDao(IClaimBillDao claimBillDao) {
		this.claimBillDao = claimBillDao;
	}

	public IClaimLiabDao getClaimLiabDao() {
		return claimLiabDao;
	}

	public void setClaimLiabDao(IClaimLiabDao claimLiabDao) {
		this.claimLiabDao = claimLiabDao;
	}

	public IClaimBillPaidDao getClaimBillPaidDao() {
		return claimBillPaidDao;
	}

	public void setClaimBillPaidDao(IClaimBillPaidDao claimBillPaidDao) {
		this.claimBillPaidDao = claimBillPaidDao;
	}
    public ICustomerDao getCustomerDao() {
		return customerDao;
	}

	public void setCustomerDao(ICustomerDao customerDao) {
		this.customerDao = customerDao;
	}

	public IClobDao getClobDao() {
		return clobDao;
	}
	public void setClobDao(IClobDao clobDao) {
		this.clobDao = clobDao;
	}

	public IUserDao getUserDao() {
		return userDao;
	}
	public void setUserDao(IUserDao userDao) {
		this.userDao = userDao;
	}

	public IClaimBillItemDao getClaimbillItemDao() {
		return claimbillItemDao;
	}
	public void setClaimbillItemDao(IClaimBillItemDao claimbillItemDao) {
		this.claimbillItemDao = claimbillItemDao;
	}

	
}
