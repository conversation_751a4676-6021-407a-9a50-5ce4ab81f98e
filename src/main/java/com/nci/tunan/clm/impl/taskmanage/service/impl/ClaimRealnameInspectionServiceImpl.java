package com.nci.tunan.clm.impl.taskmanage.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.tunan.clm.dao.IClaimRealnameInspectionDao;
import com.nci.tunan.clm.impl.taskmanage.service.IClaimRealnameInspectionService;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.impl.util.CodeUtils;
import com.nci.tunan.clm.interfaces.model.bo.ClaimRealnameInspectionBO;
import com.nci.tunan.clm.interfaces.model.po.ClaimRealnameInspectionPO;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;

/** 
 * @description 理赔查验配置service实现类
 * <AUTHOR> <EMAIL>
 * @date 2021-11-15下午1:48:31
 * @.belongToModule CLM-理赔系统/理赔查验配置
 */
public class ClaimRealnameInspectionServiceImpl implements IClaimRealnameInspectionService{

	private IClaimRealnameInspectionDao claimRealnameInspectionDao;
	
	public IClaimRealnameInspectionDao getClaimRealnameInspectionDao() {
		return claimRealnameInspectionDao;
	}

	public void setClaimRealnameInspectionDao(
			IClaimRealnameInspectionDao claimRealnameInspectionDao) {
		this.claimRealnameInspectionDao = claimRealnameInspectionDao;
	}

	/**
	 * 访问理赔查验配置页面（分页查询数据）
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimRealnameInspectionBO 理赔配置bo对象
	 * @param currentPage<ClaimRealnameInspectionBO> 理赔配置分页对象
	 * @return CurrentPage<ClaimRealnameInspectionBO> 理赔配置分页对象
	 * @throws Exception 
	*/
	@Override
	public CurrentPage<ClaimRealnameInspectionBO> findClaimRealnameInspection(ClaimRealnameInspectionBO claimRealnameInspectionBO,
			CurrentPage<ClaimRealnameInspectionBO> currentPage) throws Exception {
		CurrentPage<ClaimRealnameInspectionPO> currentPagePO = BeanUtils.copyCurrentPage(ClaimRealnameInspectionPO.class, currentPage);
		//1.分页查询理赔配置结果 t_claim_realname_inspection
		 CurrentPage<ClaimRealnameInspectionPO> claimRealnameInspectionPOPage = claimRealnameInspectionDao.queryClaimRealnameInspectionForPage(
				 BeanUtils.copyProperties(ClaimRealnameInspectionPO.class, claimRealnameInspectionBO), currentPagePO);
		 //2.分页po转分页bo
		 CurrentPage<ClaimRealnameInspectionBO> claimRealnameInspectionBOPage = BeanUtils.copyCurrentPage(ClaimRealnameInspectionBO.class, claimRealnameInspectionPOPage);
		//3.获取分页bo中的理赔查验配置结果集，将理赔类型和人员范围转换为字符串，存在bo对象中
		 List<ClaimRealnameInspectionBO> claimRealnameInspectionList = claimRealnameInspectionBOPage.getPageItems();
		 if(claimRealnameInspectionList.size() > 0){
			 for (ClaimRealnameInspectionBO realNameBO : claimRealnameInspectionList) {
				 //3.1.理赔类型字符串
				 String claimType = realNameBO.getClaimTypes();
				 String claimTypeName = "";
				 String[] claimTypeArr = claimType.split(",");
				 for (String claimTypeStr : claimTypeArr) {
					 claimTypeName = claimTypeName + "," + CodeUtils.getValueByCode("APP___CLM__DBUSER.T_CLAIM_TYPE", claimTypeStr.trim());
				}
				 claimTypeName= claimTypeName.substring(1, claimTypeName.length());
				 realNameBO.setClaimTypesName(claimTypeName);
				 //3.2.人员范围字符串
				 String personnelScope = realNameBO.getPersonTypes();
				 String personnelScopeName = "";
				 if(personnelScope.contains("1")){
					 personnelScopeName = personnelScopeName + "," + "受益人";
				 }
				 if(personnelScope.contains("2")){
					 personnelScopeName = personnelScopeName + "," + "受托人";
				 }
				 if(personnelScope.contains("3")){
					personnelScopeName = personnelScopeName + "," + "被保险人";
				 }			
				 personnelScopeName= personnelScopeName.substring(1, personnelScopeName.length());
				 realNameBO.setPersonTypesName(personnelScopeName);
			}
		 }
		return claimRealnameInspectionBOPage;
	}

	/**
	 * 确认--校验并保存理赔查验配置参数
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimRealnameInspectionBO 理赔配置bo对象
	 * @return 
	 * @throws Exception 
	*/
	@Override
	public boolean saveClaimRealnameInspection(ClaimRealnameInspectionBO claimRealnameInspectionBO) throws Exception {		
		//1.查验与数据库中有效数据是否重叠（机构、理赔类型、查验人范围一致，配置生效起止日期存在交叉，提示错误/配置范围一致，起止如期不存在交叉，也可成功保存）
		Date effectiveStartTime = claimRealnameInspectionBO.getStartDate();
		Date effectiveEndTime = claimRealnameInspectionBO.getEndDate();
		ClaimRealnameInspectionPO realNamePO = new ClaimRealnameInspectionPO();
		realNamePO.setOrganCode(claimRealnameInspectionBO.getOrganCode());
		realNamePO.setClaimTypes(claimRealnameInspectionBO.getClaimTypes());
		realNamePO.setPersonTypes(claimRealnameInspectionBO.getPersonTypes());
		realNamePO.setOperateType(ClaimConstant.ADD);
		//1.1.查询机构、理赔类型、查验人范围一致的数据 t_claim_realname_inspection
		List<ClaimRealnameInspectionPO> findClaimRealnameInspectionPO = claimRealnameInspectionDao.findAllClaimRealnameInspection(realNamePO);
		if(findClaimRealnameInspectionPO.size() > 0){
			for (int i = 0; i < findClaimRealnameInspectionPO.size(); i++) {
				Date startTime = findClaimRealnameInspectionPO.get(i).getStartDate();
				Date endTime = findClaimRealnameInspectionPO.get(i).getEndDate();
				//1.2.配置起止期存在交叉，返回false，提示错误信息
				if (((effectiveStartTime.getTime() >= startTime.getTime())
                        && effectiveStartTime.getTime() < endTime.getTime())
                || ((effectiveStartTime.getTime() > startTime.getTime())
                        && effectiveStartTime.getTime() <= endTime.getTime())
                || ((startTime.getTime() >= effectiveStartTime.getTime())
                        && startTime.getTime() < effectiveEndTime.getTime())
                || ((startTime.getTime() > effectiveStartTime.getTime())
                        && startTime.getTime() <= effectiveEndTime.getTime())){
					return false;
					}
			}
		}
		//1.3.起止日期不重叠，则正常保存数据t_real_name_check_config
		ClaimRealnameInspectionPO claimRealnameInspectionPO = BeanUtils.copyProperties(ClaimRealnameInspectionPO.class, claimRealnameInspectionBO);
		claimRealnameInspectionPO.setOperatorCode(new BigDecimal(AppUserContext.getCurrentUser().getUserId()));
		claimRealnameInspectionPO.setIsValid(ClaimConstant.LIABILITY_STATUS_EFFECTIVE);//@invalid 有效 1
		claimRealnameInspectionPO.setOperateType(ClaimConstant.ADD);
		claimRealnameInspectionPO.setOperateDate(WorkDateUtil.getWorkDate());
		claimRealnameInspectionDao.addClaimRealnameInspection(claimRealnameInspectionPO);
		return true;
	}

	/**
	 * 删除--删除有效且在已生效数据（假删除，删除后状态变为无效）并更新数据操作员（当前系统登录用户）、操作类型（删除）和操作时间（系统当前时间）（如果未生效数据，直接删除）
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param listId 序列号
	 * @throws Exception 
	*/
	@Override
	public void deleteRealName(BigDecimal listId) throws Exception {
		ClaimRealnameInspectionPO claimRealnameInspectionPO = new ClaimRealnameInspectionPO();
		claimRealnameInspectionPO.setListId(listId);
		//1.将删除带回的listId，存入理赔配置结果对象中，查出单条数据t_real_name_check_config
		claimRealnameInspectionPO = claimRealnameInspectionDao.findClaimRealnameInspection(claimRealnameInspectionPO);
		//2.获取配置起止日期
		Date startTime = claimRealnameInspectionPO.getStartDate();
		Date endTime = claimRealnameInspectionPO.getEndDate();
		//3.获取当前时间
		Date time = WorkDateUtil.getWorkDate();
		//4.对比，是否在生效日
		if(time.getTime() >= startTime.getTime() && time.getTime() <= endTime.getTime()){
			//4.1.在生效日内，点击删除按钮后，状态变为"无效",并更新数据操作员、操作类型、操作时间
			claimRealnameInspectionPO.setIsValid(ClaimConstant.LIABILITY_STATUS_LOSE_INVALID);//@invalid 无效 - 0
			claimRealnameInspectionPO.setOperatorCode(new BigDecimal(AppUserContext.getCurrentUser().getUserId()));
			claimRealnameInspectionPO.setOperateType(ClaimConstant.DELETE);
			claimRealnameInspectionPO.setOperateDate(WorkDateUtil.getWorkDate());
			claimRealnameInspectionDao.updateClaimRealnameInspection(claimRealnameInspectionPO);
		}else{
			//4.2.未生效直接删除
			claimRealnameInspectionDao.deleteClaimRealnameInspection(claimRealnameInspectionPO);
		}
	}

}
