
package com.nci.tunan.clm.impl.imports.pds.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;

import com.nci.core.common.factory.BOServiceFactory;
import com.nci.core.common.interfaces.vo.CustomerBaseInfoVO;
import com.nci.tunan.clm.dao.IBenefitInsuredDao;
import com.nci.tunan.clm.dao.IBusinessProductDao;
import com.nci.tunan.clm.dao.IClaimBusiProdDao;
import com.nci.tunan.clm.dao.IClaimCaseDao;
import com.nci.tunan.clm.dao.IClaimLiabDao;
import com.nci.tunan.clm.dao.IClaimMatchJournaDao;
import com.nci.tunan.clm.dao.IContractBusiProdDao;
import com.nci.tunan.clm.dao.IContractMasterDao;
import com.nci.tunan.clm.dao.IContractProductDao;
import com.nci.tunan.clm.dao.IContractProductOtherDao;
import com.nci.tunan.clm.dao.ICountryDao;
import com.nci.tunan.clm.dao.ICustomerDao;
import com.nci.tunan.clm.dao.IInsuredListDao;
import com.nci.tunan.clm.dao.IJobCodeDao;
import com.nci.tunan.clm.dao.IPolicyHolderDao;
import com.nci.tunan.clm.dao.IProductLifeDao;
import com.nci.tunan.clm.impl.imports.pds.IClaimPDSService;
import com.nci.tunan.clm.impl.report.service.IClaimPASService;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.impl.util.CodeUtils;
import com.nci.tunan.clm.impl.util.CommonMethodUtil;
import com.nci.tunan.clm.imports.ICLMServiceUtil;
import com.nci.tunan.clm.interfaces.model.bo.AccutionBO;
import com.nci.tunan.clm.interfaces.model.bo.AccutionListBO;
import com.nci.tunan.clm.interfaces.model.bo.AccutorBO;
import com.nci.tunan.clm.interfaces.model.bo.AccutorBenefitBO;
import com.nci.tunan.clm.interfaces.model.bo.AccutorRelativityBO;
import com.nci.tunan.clm.interfaces.model.bo.CalcCashValueBO;
import com.nci.tunan.clm.interfaces.model.bo.CalcPrepareProductBO;
import com.nci.tunan.clm.interfaces.model.bo.CalcProductIdBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimLiabBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimSubCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClmCalcMedicalCfgBO;
import com.nci.tunan.clm.interfaces.model.bo.ClmCaseOneMoreInfoBO;
import com.nci.tunan.clm.interfaces.model.bo.ClmPayBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractBusiProdBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractMasterBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractProductBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractProductOtherBO;
import com.nci.tunan.clm.interfaces.model.bo.FmsAndAccutorParamBO;
import com.nci.tunan.clm.interfaces.model.bo.InsuredListBO;
import com.nci.tunan.clm.interfaces.model.bo.LiabFomulaParamBO;
import com.nci.tunan.clm.interfaces.model.bo.LiabilityBO;
import com.nci.tunan.clm.interfaces.model.bo.LiabilityParameterBO;
import com.nci.tunan.clm.interfaces.model.bo.ParamAccutorBO;
import com.nci.tunan.clm.interfaces.model.bo.PolicyHolderBO;
import com.nci.tunan.clm.interfaces.model.po.BenefitInsuredPO;
import com.nci.tunan.clm.interfaces.model.po.BusinessProductPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimBusiProdPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimCasePO;
import com.nci.tunan.clm.interfaces.model.po.ClaimLiabPO;
import com.nci.tunan.clm.interfaces.model.po.ClaimMatchJournaPO;
import com.nci.tunan.clm.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.clm.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.clm.interfaces.model.po.ContractProductOtherPO;
import com.nci.tunan.clm.interfaces.model.po.ContractProductPO;
import com.nci.tunan.clm.interfaces.model.po.CountryPO;
import com.nci.tunan.clm.interfaces.model.po.CustomerPO;
import com.nci.tunan.clm.interfaces.model.po.InsuredListPO;
import com.nci.tunan.clm.interfaces.model.po.JobCodePO;
import com.nci.tunan.clm.interfaces.model.po.PolicyHolderPO;
import com.nci.tunan.clm.interfaces.model.po.ProductLifePO;
import com.nci.tunan.clm.util.ClaimCalcUtil;
import com.nci.tunan.cs.common.CommonUtil;
import com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueReqVO;
import com.nci.tunan.mms.interfaces.calc.exports.cashvaluecal.vo.MmsCalcCashValueResVO;
import com.nci.tunan.pa.interfaces.serviceData.calcSurvivalAmount.SurvivalAmountReqVO;
import com.nci.tunan.pa.interfaces.serviceData.calcSurvivalAmount.SurvivalAmountResVO;
import com.nci.tunan.pa.interfaces.serviceData.querypayduelist.QuerPayDueReqVO;
import com.nci.tunan.pa.interfaces.serviceData.querypayduelist.QuerPayDueReqVOParam;
import com.nci.tunan.pa.interfaces.serviceData.querypayduelist.QueryPayDueResVO;
import com.nci.tunan.pa.interfaces.serviceData.querypayduelist.QueryPayDueVO;
import com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapReqVO;
import com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapResVO;
import com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapVO;
import com.nci.tunan.prd.interfaces.calc.exports.canexemptproduct.vo.PrdBusinessCodeResVO;
import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.AccutorBenefitResVO;
import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.ClmCalcParamCfgVO;
import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.DutyMateTruthFigureListReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.DutyMateTruthFigureListResVO;
import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.DutyMateTruthFigureReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.DutyMateTruthFigureResVO;
import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.LiabParamResVO;
import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.LiabilityVO;
import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.ProductIdReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.ProductIdResVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.AccutorBenefitReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.AccutorPreDataReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.AccutorRelaValueReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.AccutorResVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ClmCaseOneMoreInfoReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ClmPayResVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.FmsAndAccutorParamVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.LiabParamValueReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.LiabilityReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.LiabilityResVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.MateAdjustmentReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.MateAdjustmentResVO;
import com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ParamAccutorReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.querybonusinfo.vo.PrdQueryBonusInfoReqVO;
import com.nci.tunan.prd.interfaces.calc.exports.querybonusinfo.vo.PrdQueryBonusInfoResVO;
import com.nci.udmp.framework.bizservice.IBizService;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.spring.SpringContextUtils;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.xml.transform.XmlHelper;
/**
 * 匹配理算调接口计算及部分结算项处理service实现
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统/匹配理算调接口计算
 * @date 2017年9月12日 下午2:27:51
 */

public class ClaimPDSServiceImpl implements IBizService, IClaimPDSService {
    /**
     * 服务总接口
     */
	private ICLMServiceUtil cLMServiceUtil;
	/**
	 * 险种抄单Dao
	 */
	private IContractBusiProdDao contractBusiProdDao;
	/**
     * 精算产品/责任组Dao
     */
	private IProductLifeDao productLifeDao;
	/**
     * 保单抄单Dao
     */
	private IContractMasterDao contractMasterDao;
	/**
     * 险种被保人抄单表Dao
     */
	private IBenefitInsuredDao benefitInsuredDao;
	/**
     * 被保人Dao
     */
	private IInsuredListDao insuredListDao;
	/**
     * 业务产品Dao
     */
	private IBusinessProductDao businessProductDao;

	/**
     * 结算项service类
     */
	private IClaimPASService claimPASService;
	/**
     * 理赔匹配理算日志表Dao
     */
	private IClaimMatchJournaDao claimMatchJournaDao;
	/**
     * 险种理算Dao
     */
	private IClaimBusiProdDao claimBusiProdDao;
	/**
     * 投保人Dao
     */
	private IPolicyHolderDao policyHolderDao;
	/**
     * 责任组Dao
     */
	private IContractProductDao contractProductDao;
	/**
     * 赔案Dao
     */
	private IClaimCaseDao claimCaseDao;
	/**
	 * 赔案Dao
	 */
	private IClaimLiabDao claimLiabDao;

    /**
     * 保单责任组备用抄单表Dao接口
     */
    private IContractProductOtherDao contractProductOtherDao;
    
    private ICustomerDao customerDao;
    /**
     * JobCodeDao职业信息码值接口
     */
    private IJobCodeDao jobCodeDao;
    /**
     * 国家代码表Dao接口
     */
    private ICountryDao countryDao;
    /**
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	/**
     * 匹配理算准备接口
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSubCaseBO 赔案子信息
     * @throws BizException 异常处理
     */
	public void queryPDSforClaimCalc(List<ClaimSubCaseBO> claimSubCaseBOList,String isToLog)
			throws BizException {
		//1删除匹配理算日志(T_CLAIM_MATCH_JOURNA)，然后准备重新记录
		//1.1 如果存在子赔案并且删除标识为是则删除
		ClaimMatchJournaPO claimMatchJournaPO = new ClaimMatchJournaPO();
		if(claimSubCaseBOList.size()>0&&ClaimConstant.YES.equals(isToLog)){
			claimMatchJournaPO.setCaseId(claimSubCaseBOList.get(0).getCaseId());
			//1.2 根据子赔案的赔案id删除匹配理算日志
			claimMatchJournaDao.deleteClaimMatchJournaByCaseId(claimMatchJournaPO);
		}
		//@invalid 2 调用产品工厂接口将获取的值，匹配存入ClaimSubCaseBO对象
		//2 将claimProductList里,没有匹配到的claimProduct移除
	    DutyMateTruthFigureListReqVO inputData = new DutyMateTruthFigureListReqVO();
	    List<DutyMateTruthFigureReqVO> dutyMateTruthFigureListReqVO = new ArrayList<DutyMateTruthFigureReqVO>();
	    inputData.setDutyMateTruthFigureListReqVO(dutyMateTruthFigureListReqVO);
	    A:for(ClaimSubCaseBO claimSubCaseBO : claimSubCaseBOList){
	        DutyMateTruthFigureReqVO dutyMateTruthFigureReqVO = new DutyMateTruthFigureReqVO();
	        dutyMateTruthFigureReqVO.setLiabCategory(new BigDecimal(ClaimCalcUtil.claimTypeSwitch(Integer.parseInt(claimSubCaseBO.getClaimType()))));
	        dutyMateTruthFigureReqVO.setAccidentCause(claimSubCaseBO.getAccReason());
	        
	        List<ProductIdReqVO> productIdList = new ArrayList<ProductIdReqVO>();
	        if(claimSubCaseBO.getCalcPrepareProductBOList()==null){
	            continue A;
	        }
	        for (CalcPrepareProductBO calcPrePareProductBO : claimSubCaseBO.getCalcPrepareProductBOList()) {
	            ProductIdReqVO productsReqVO = new ProductIdReqVO();
	            productsReqVO = BeanUtils.copyProperties(ProductIdReqVO.class, calcPrePareProductBO);
	            productsReqVO.setProductInfoReqVOList(calcPrePareProductBO.getProductInfoReqVOList());
	            productIdList.add(productsReqVO);
	        }
	        dutyMateTruthFigureReqVO.setProductIdList(productIdList);
	        dutyMateTruthFigureListReqVO.add(dutyMateTruthFigureReqVO);
	    }
	    logger.debug("*************************匹配理算准备接口入参："+ XmlHelper.classToXml(inputData)+"***************************************");
	    //3 调用责任匹配与理算准备接口
	    DutyMateTruthFigureListResVO dutyMateTruthFigureListResVO = cLMServiceUtil
				.prdidutymatetruthfigureuccdutymatetruthfigure(inputData);
	    logger.debug("*************************匹配理算准备接口出参："+ XmlHelper.classToXml(dutyMateTruthFigureListResVO)+"***************************************");
		if (dutyMateTruthFigureListResVO == null|| dutyMateTruthFigureListResVO.getDutyMateTruthFigureListResVO() == null||dutyMateTruthFigureListResVO.getDutyMateTruthFigureListResVO().size()==0) {
			return;
		}
		//4 解析匹配理算出参
		try {
			List<ClaimMatchJournaPO> claimMatchJournaPOList = new ArrayList<ClaimMatchJournaPO>();
		    for(ClaimSubCaseBO claimSubCaseBO : claimSubCaseBOList){
		        for(DutyMateTruthFigureResVO dutyMateTruthFigureResVO:dutyMateTruthFigureListResVO.getDutyMateTruthFigureListResVO()){
		            if(dutyMateTruthFigureResVO.getLiabCategory().equals(new BigDecimal(ClaimCalcUtil.claimTypeSwitch(Integer.parseInt(claimSubCaseBO.getClaimType()))))){
		            	List<CalcProductIdBO> calcProductIdBOList = new ArrayList<CalcProductIdBO>();
		            	for (ProductIdResVO productResVO : dutyMateTruthFigureResVO.getProductIdList()) {
		            		//4.1 将责任按责任组分开
		            		CalcProductIdBO calcProductIdBO = new CalcProductIdBO();
		            		//4.2 查询责任组返回所对应责任组
		            		com.nci.tunan.pa.interfaces.vo.ContractProductVO contractProductVO = null;
		            		com.nci.tunan.pa.interfaces.vo.ContractProductVO accProductVO = null;
		            		for(com.nci.tunan.pa.interfaces.vo.ContractProductVO contractProduct : claimSubCaseBO.getPolicyData().getContractProductVOList()){
		            			if(productResVO.getItemId().equals(contractProduct.getItemId())&&productResVO.getProductId().equals(contractProduct.getProductId())){
		            				contractProductVO = contractProduct;
		            			}
		            		}
		            		if(contractProductVO==null){
		            			for(com.nci.tunan.pa.interfaces.vo.ContractProductVO contractProduct : claimSubCaseBO.getPolicyData2().getContractProductVOList()){
			            			if(productResVO.getItemId().equals(contractProduct.getItemId())&&productResVO.getProductId().equals(contractProduct.getProductId())){
			            				contractProductVO = contractProduct;
			            			}
			            		}
		            		}
		            		//4.3 获取事故日期责任组层数据
		            		for(com.nci.tunan.pa.interfaces.vo.ContractProductVO contractProduct : claimSubCaseBO.getPolicyAccData().getContractProductVOList()){
		            			if(productResVO.getItemId().equals(contractProduct.getItemId())){
		            				accProductVO = contractProduct;
		            			}
		            		}
		            		//4.4 查询责任组返回所对应险种
		            		com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO contractBusiProdVO = null;
		            		for(com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO contractBusiProd : claimSubCaseBO.getPolicyData().getContractBusiProdVOList()){
		            			if(contractProductVO.getPolicyId().equals(contractBusiProd.getPolicyId())
		            					&&contractProductVO.getBusiItemId().equals(contractBusiProd.getBusiItemId())
		            					&&productResVO.getBusiPrdId().equals(contractBusiProd.getBusiPrdId())){
		            				contractBusiProdVO = contractBusiProd;
		            			}
		            		}
		            		if(contractBusiProdVO==null && claimSubCaseBO.getPolicyData2()!=null){
		            			for(com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO contractBusiProd : claimSubCaseBO.getPolicyData2().getContractBusiProdVOList()){
			            			if(contractProductVO.getPolicyId().equals(contractBusiProd.getPolicyId())
			            					&&contractProductVO.getBusiItemId().equals(contractBusiProd.getBusiItemId())
			            					&&productResVO.getBusiPrdId().equals(contractBusiProd.getBusiPrdId())){
			            				contractBusiProdVO = contractBusiProd;
			            			}
			            		}
		            		}
		            		if(contractBusiProdVO==null && claimSubCaseBO.getPolicyAccData()!=null){
		            			for(com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO contractBusiProd : claimSubCaseBO.getPolicyAccData().getContractBusiProdVOList()){
			            			if(contractProductVO.getPolicyId().equals(contractBusiProd.getPolicyId())
			            					&&contractProductVO.getBusiItemId().equals(contractBusiProd.getBusiItemId())
			            					&&productResVO.getBusiPrdId().equals(contractBusiProd.getBusiPrdId())){
			            				contractBusiProdVO = contractBusiProd;
			            			}
			            		}
		            		}
		            		calcProductIdBO.setMainBusiPrdId(productResVO.getMainBusiPrdId());
		            		calcProductIdBO.setBusiPrdId(productResVO.getBusiPrdId());
		            		calcProductIdBO.setProductId(productResVO.getProductId());
		            		calcProductIdBO.setItemId(productResVO.getItemId());
		            		calcProductIdBO.setAccProductBO(BeanUtils.copyProperties(ContractProductBO.class, accProductVO));
		            		calcProductIdBO.setContractProductBO(BeanUtils.copyProperties(ContractProductBO.class, contractProductVO));
		            		calcProductIdBO.setContractBusiProdBO(BeanUtils.copyProperties(ContractBusiProdBO.class, contractBusiProdVO));
		            		Set<BigDecimal> relaBusiPrdIds = new HashSet<BigDecimal>();
		            		if(calcProductIdBO.getMainBusiPrdId()==null && calcProductIdBO.getMainBusiPrdId().equals(calcProductIdBO.getBusiPrdId())){
		            			for(com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO contractBusiProd : claimSubCaseBO.getPolicyData().getContractBusiProdVOList()){
		            				if(contractProductVO.getBusiItemId().equals(contractBusiProd.getMasterBusiItemId())){
		            					relaBusiPrdIds.add(contractBusiProd.getBusiPrdId());
			            			}
		            			}for(com.nci.tunan.pa.interfaces.vo.ContractBusiProdVO contractBusiProd : claimSubCaseBO.getPolicyData2().getContractBusiProdVOList()){
		            				if(contractProductVO.getBusiItemId().equals(contractBusiProd.getMasterBusiItemId())){
		            					relaBusiPrdIds.add(contractBusiProd.getBusiPrdId());
			            			}
			            		}
		            		}else{
		            			relaBusiPrdIds.add(calcProductIdBO.getMainBusiPrdId());
		            		}
		            		calcProductIdBO.setRelaBusiPrdIds(new ArrayList<BigDecimal>(relaBusiPrdIds));
		            		List<ClaimLiabBO> claimLiabBOList = new ArrayList<ClaimLiabBO>();
		            		//4.5 循环处理责任信息
		            		for (LiabilityVO liabilityVO : productResVO.getLiabIdList()) {
		            			//4.5.1 判断责任匹配结果，如果为是给理赔给付责任理算对象赋值
		            			if(ClaimConstant.YES.equals(liabilityVO.getLiabMatchRes())){
		            				ClaimLiabBO claimLiabBO = new ClaimLiabBO();
		            				LiabilityBO liabilityBO = new LiabilityBO();
		            				//@invalid 4.5 copy责任相关性
		            				BeanUtils.copyProperties(liabilityBO, liabilityVO);
		            				//@invalid 4.6 copy责任公式参数
		            				List<LiabFomulaParamBO> liabFomulaParamBOList = new ArrayList<LiabFomulaParamBO>();
		            				if (liabilityVO.getParamList() != null) {
		            					for (LiabParamResVO paramResVO : liabilityVO.getParamList()) {
		            						LiabFomulaParamBO liabFomulaParamBO = new LiabFomulaParamBO();
		            						BeanUtils.copyProperties(liabFomulaParamBO,paramResVO);
		            						
		            						liabFomulaParamBOList.add(liabFomulaParamBO);
		            					}
		            				}
		            				//4.5.1.1 添加责任公式参数
		            				liabilityBO.setLiabFomulaParamBOList(liabFomulaParamBOList);
		            				//@invalid 4.7.copy责任匹配参数（手术代码）
		            				//4.5.1.2 添加责任匹配参数（手术代码）
		            				liabilityBO.setLiabilityParameterBO( BeanUtils.copyList(LiabilityParameterBO.class, liabilityVO.getLiabilityParameterResVO()));
		            				//4.5.1.3 添加责任参数是否匹配标识
		            				liabilityBO.setLiabParamFlag(liabilityVO.getLiabParamFlag());
		            				//4.5.1.4 添加责任参数2
		            				List<ClmCalcMedicalCfgBO> clmCalcMedicalCfgBOList = BeanUtils.copyList(ClmCalcMedicalCfgBO.class,liabilityVO.getClmCalcMedicalCfgVO());
		            				liabilityBO.setClmCalcMedicalCfgBOList(clmCalcMedicalCfgBOList);
		            				//4.5.1.5 添加互斥责任代码
		            				List<BigDecimal> liabIDlist = new ArrayList<BigDecimal>();
		            				if (liabilityVO.getLiabCodelist() != null) {
		            					//@invalid 4.9.1.用英文逗号拆分
		            					String[] liabCodeList = liabilityVO.getLiabCodelist().split(ClaimConstant.COMMA);
		            					//@invalid 4.9.1.如果没有拆分开再用中文逗号拆分
		            					if (liabCodeList.length != 0&& liabCodeList[0].equals(liabilityVO.getLiabCodelist())) {
		            						liabCodeList = liabilityVO.getLiabCodelist().split(ClaimConstant.COMMA_CHINA);
		            					}
		            					for (int i = 0; i < liabCodeList.length; i++) {
		            						liabIDlist.add(new BigDecimal(liabCodeList[i]));
		            					}
		            				}
		            				liabilityBO.setLiabIdlist(liabIDlist);
		            				//4.5.1.6 添加累加器相关性
		            				List<AccutorBenefitBO> accutorBenefitList = new ArrayList<AccutorBenefitBO>();
		            				if (liabilityVO.getAccutorBenefitList() != null) {
		            					for (AccutorBenefitResVO accutorResVO : liabilityVO.getAccutorBenefitList()) {
		            						AccutorBenefitBO accutorBenefitBO = new AccutorBenefitBO();
		            						BeanUtils.copyProperties(accutorBenefitBO,accutorResVO);
		            						AccutorRelativityBO accutorRelativity = new AccutorRelativityBO();
		            						BeanUtils.copyProperties(accutorRelativity,accutorResVO.getAccutorRelativity());
		            						accutorBenefitBO.setAccutorRelativity(accutorRelativity);
		            						accutorBenefitList.add(accutorBenefitBO);
		            					}
		            				}
		            				liabilityBO.setAccutorBenefitList(accutorBenefitList);
		            				//4.5.1.7 添加累加器公式参数
		            				List<ParamAccutorBO> paramAccutorBOs = BeanUtils.copyList(ParamAccutorBO.class,liabilityVO.getParamAccutorList());
		            				liabilityBO.setParamAccutorList(paramAccutorBOs);
		            				//4.5.1.8 累加器静态参数
		            				Map<String, String> calcParamCfg = new HashMap<String, String>();
		            				for (ClmCalcParamCfgVO clmCalcParamCfgVO : liabilityVO.getClmCalcParamCfgVOList()) {
		            					calcParamCfg.put(clmCalcParamCfgVO.getParamType(),clmCalcParamCfgVO.getParamValue());
		            				}
		            				liabilityBO.setCalcParamCfg(calcParamCfg);
		            				claimLiabBO.setLiabilityBO(liabilityBO);
		            				claimLiabBOList.add(claimLiabBO);
		            			}else if(ClaimConstant.YES.equals(isToLog)){//4.5.2  如果理算日志删除标识为是，则保存匹配相关因子
		            				if(liabilityVO.getClmMatchInfoVOs()!=null&&liabilityVO.getClmMatchInfoVOs().size()>0){
		            					//4.5.2.1 找到其中匹配相关因子最多的集合
		            					List<com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.ClmMatchInfoVO>  clmMatchInfoList = null;
		            					int count = 0;
		            					for(List<com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.ClmMatchInfoVO>  clmMatchInfos : liabilityVO.getClmMatchInfoVOs()){
		            						int countSub = 0;
		            						for(com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.ClmMatchInfoVO clmMatchInfo : clmMatchInfos){
		            							if(ClaimConstant.YES.equals(clmMatchInfo.getMatchResult())){
		            								countSub++;
		            							}
		            						}
		            						if(countSub>=count){
		            							clmMatchInfoList = clmMatchInfos;
		            							count = countSub;
		            						}
		            					}
		            					//******* 保存匹配相关因子最多的集合
		            					if(clmMatchInfoList!=null){
		            						for(com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.ClmMatchInfoVO  clmMatchInfo : clmMatchInfoList){
		            							claimMatchJournaPO = new ClaimMatchJournaPO();
		            							claimMatchJournaPO.setCaseId(claimSubCaseBO.getCaseId());
		            							claimMatchJournaPO.setPolicyId(contractBusiProdVO.getPolicyId());
		            							claimMatchJournaPO.setPolicyCode(contractBusiProdVO.getPolicyCode());
		            							claimMatchJournaPO.setBusiItemId(contractBusiProdVO.getBusiItemId());
		            							claimMatchJournaPO.setBusiProdCode(contractBusiProdVO.getBusiProdCode());
		            							claimMatchJournaPO.setItemId(contractProductVO.getItemId());
		            							claimMatchJournaPO.setProductId(contractProductVO.getProductId());
		            							claimMatchJournaPO.setLiabId(liabilityVO.getLiabId());
		            							claimMatchJournaPO.setRelativeName(CodeUtils.getValueByCode("T_PRODUCT_RELATIVITY", clmMatchInfo.getRelativeId()));
		            							if("14".equals(clmMatchInfo.getRelativeId())){
		            								claimMatchJournaPO.setClmValue(ClaimCalcUtil.claimTypeSwitchClaim(Integer.parseInt(clmMatchInfo.getClmReqValue())));
		            								claimMatchJournaPO.setRelativeValue(ClaimCalcUtil.claimTypeSwitchClaim(Integer.parseInt(clmMatchInfo.getRelativeValue())));
		            							}else{
		            								claimMatchJournaPO.setRelativeValue(clmMatchInfo.getRelativeValue());
		            								claimMatchJournaPO.setClmValue(clmMatchInfo.getClmReqValue());
		            							}
		            							if(clmMatchInfo.getMatchResult()!=null){
		            								claimMatchJournaPO.setMatchResult(new BigDecimal(clmMatchInfo.getMatchResult()));
		            							}
		            							if(clmMatchInfo.getRelativeId()!=null){
		            								claimMatchJournaPO.setRelativeId(new BigDecimal(clmMatchInfo.getRelativeId()));
		            							}
		            							if(claimMatchJournaPO.getRelativeName()==null){
		            								claimMatchJournaPO.setRelativeName("");
		            							}
		            							if(claimMatchJournaPO.getClmValue()==null){
		            								claimMatchJournaPO.setClmValue("");
		            							}
		            							if(claimMatchJournaPO.getRelativeValue()==null){
		            								claimMatchJournaPO.setRelativeValue("");
		            							}
		            							claimMatchJournaPOList.add(claimMatchJournaPO);
		            						}
		            					}
		            				}
		            			}
		            		}
		            		calcProductIdBO.setClaimLiabBOList(claimLiabBOList);
		            		calcProductIdBOList.add(calcProductIdBO);
		            	}
		            	claimSubCaseBO.setCalcProductIdBOList(calcProductIdBOList);
		            }
		        }
		    }
		    //5 判断删除标识，如果为是则组装理赔匹配理算日志对象，并批量增加数据(T_claim_Match_Journa)
		    if(ClaimConstant.YES.equals(isToLog)){
		    	claimMatchJournaDao.batchSaveClaimMatchJourna(claimMatchJournaPOList);
		    }
		} catch (Exception e) {
			e.printStackTrace();
			throw new BizException("匹配理算处理失败，请求理算准备接口/处理返回数据失败！",e.getStackTrace());
		}
	}

	/**
	 * 调产品计算理算数据
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param claimSubCaseBO 赔案子信息
	 * @throws BizException 异常处理
	 */
	public void calcClaim(List<ClaimSubCaseBO> claimSubCaseBOList) throws BizException {
		MateAdjustmentReqVO mateAdjustmentReqVO = new MateAdjustmentReqVO();
		List<com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ProductIdReqVO> productReqVOList = new ArrayList<com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ProductIdReqVO>();
		try {
			//1取出需要进行理算的责任组,并合并相同的数据
			List<CalcProductIdBO> calcProdcutIdBOs = new ArrayList<CalcProductIdBO>();
			for(ClaimSubCaseBO claimSubCaseBO : claimSubCaseBOList){
				//1.1 遍历理算的责任组数据并判断责任集合的ItemId，ProductId(和责任组的是否一致
				A:for (CalcProductIdBO calcProductIdBO : claimSubCaseBO.getCalcProductIdBOList()) {
					for(CalcProductIdBO calcProductId : calcProdcutIdBOs){
						if(calcProductId.getItemId().equals(calcProductIdBO.getItemId())&&calcProductId.getProductId().equals(calcProductIdBO.getProductId())){
							calcProductId.getClaimLiabBOList().addAll(calcProductIdBO.getClaimLiabBOList());
							continue A;
						}
					}
					//1.2 组装理算产品数据
					CalcProductIdBO productBO = new CalcProductIdBO();
					productBO.setRelaBusiPrdIds(calcProductIdBO.getRelaBusiPrdIds());
					productBO.setProductId(calcProductIdBO.getProductId());
					productBO.setItemId(calcProductIdBO.getItemId());
					productBO.setContractProductBO(calcProductIdBO.getContractProductBO());
					productBO.setClaimLiabBOList(new ArrayList<ClaimLiabBO>());
					productBO.getClaimLiabBOList().addAll(calcProductIdBO.getClaimLiabBOList());
					productBO.setSwitchingProductsFlag(calcProductIdBO.getSwitchingProductsFlag());
					calcProdcutIdBOs.add(productBO);
				}
			}
			//对入参进行排序，按照生效日进行拍排序，对于转投，转投前在前，转投后在后
			Collections.sort(calcProdcutIdBOs, new Comparator<CalcProductIdBO>() {
				@Override
				public int compare(CalcProductIdBO o1, CalcProductIdBO o2) {
					return o1.getContractProductBO().getValidateDate().compareTo(o2.getContractProductBO().getValidateDate());
				}
			});
			//2循环责任组并设置匹配理算接口入参vo集合
				for (CalcProductIdBO calcProductIdBO : calcProdcutIdBOs) {
                    //2.1 组装匹配理算接口产品ID入参VO
					com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ProductIdReqVO productReqVO = new com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ProductIdReqVO();
					//2.1.1 传入关联险种
					productReqVO.setRelaBusiPrdIds(calcProductIdBO.getRelaBusiPrdIds());
					//2.1.2.传入责任组ID
					productReqVO.setProductId(calcProductIdBO.getProductId());
					//2.1.3.传入责任组业务ID
					productReqVO.setItemId(calcProductIdBO.getItemId());
					//2.1.4.传入责任组所属险种ID
					productReqVO.setBusiPrdId(calcProductIdBO.getContractProductBO()
							.getBusiPrdId());
					//2.1.5.传入责任组所属险种主险ID
					productReqVO.setMainBusiPrdId(calcProductIdBO.getContractProductBO().getMasterBusiPrdId());
					productReqVO.setSwitchingProductsFlag(calcProductIdBO.getSwitchingProductsFlag());
					List<LiabilityReqVO> LiabilityReqVOList = new ArrayList<LiabilityReqVO>();
					//2.1.6 遍历责任id集合，组装保险责任VBO,传入责任组相关参数
					for (ClaimLiabBO claimLiabBO : calcProductIdBO.getClaimLiabBOList()) {

						LiabilityBO liabilityBO = claimLiabBO.getLiabilityBO();
						//******* 传入责任相关性值
						LiabilityReqVO liabilityReqVO = new LiabilityReqVO();
						BeanUtils.copyProperties(liabilityReqVO, liabilityBO);
						//******* 传fms公式和累加器相关参数
						List<FmsAndAccutorParamVO> fmsAndAccutorParamVOs = new ArrayList<FmsAndAccutorParamVO>();
						//对入参进行排序，按照保单年度进行排序，对于续保，按年度排序
						Collections.sort(liabilityBO.getFmsAndAccutorParamBOs(), new Comparator<FmsAndAccutorParamBO>() {
							@Override
							public int compare(FmsAndAccutorParamBO o1, FmsAndAccutorParamBO o2) {
								return o1.getPolicyYear().compareTo(o2.getPolicyYear());
							}
						});
						
						for (FmsAndAccutorParamBO fmsAndAccutorParam : liabilityBO.getFmsAndAccutorParamBOs()) {
							FmsAndAccutorParamVO fmsAndAccutorParamVO = new FmsAndAccutorParamVO();
							List<ClmCaseOneMoreInfoReqVO> clmCaseInfoList = new ArrayList<ClmCaseOneMoreInfoReqVO>();
							fmsAndAccutorParamVO.setPolicyYear(fmsAndAccutorParam.getPolicyYear());
							fmsAndAccutorParamVO.setClmCaseInfoList(clmCaseInfoList);
							ClmCaseOneMoreInfoReqVO clmCaseInfo = null;
							for(ClmCaseOneMoreInfoBO clmCaseOneInfoBO : fmsAndAccutorParam.getClmCaseInfoList()){
								clmCaseInfo = null;
								clmCaseInfo = BeanUtils.copyProperties(ClmCaseOneMoreInfoReqVO.class, clmCaseOneInfoBO);
								clmCaseInfo.setParamList(BeanUtils.copyList(LiabParamValueReqVO.class,clmCaseOneInfoBO.getLiabFomulaParamBOList()));
								clmCaseInfo.setAccutorPreData(BeanUtils.copyList(AccutorPreDataReqVO.class,clmCaseOneInfoBO.getAccutorPreData()));
								clmCaseInfo.setParamAccutorList(BeanUtils.copyList(ParamAccutorReqVO.class,clmCaseOneInfoBO.getParamAccutorList()));
								if(clmCaseOneInfoBO.getAccutorBenefitList()!=null&&clmCaseOneInfoBO.getAccutorBenefitList().size()>0){
									//账单累加器
									List<AccutorBenefitReqVO> accutorBenefitReqVOs = new ArrayList<AccutorBenefitReqVO>();
									
									for (AccutorBenefitBO accutorBenefit : clmCaseOneInfoBO.getAccutorBenefitList()) {
										AccutorBenefitReqVO accutorBenefitReqVO = new AccutorBenefitReqVO();
										BeanUtils.copyProperties(accutorBenefitReqVO,accutorBenefit);
										accutorBenefitReqVO.setAccutorRelaValue(BeanUtils.copyProperties(AccutorRelaValueReqVO.class,accutorBenefit.getAccutorRelativity()));
										accutorBenefitReqVOs.add(accutorBenefitReqVO);
									}
									clmCaseInfo.setAccutorList(accutorBenefitReqVOs);
								}
								
								clmCaseInfoList.add(clmCaseInfo);
							}
							fmsAndAccutorParamVOs.add(fmsAndAccutorParamVO);
						}
						liabilityReqVO.setFmsAndAccutorParamList(fmsAndAccutorParamVOs);
						//2.1.6.3 传入累加器
						List<AccutorBenefitReqVO> accutorBenefitReqVOs = new ArrayList<AccutorBenefitReqVO>();

						for (AccutorBenefitBO accutorBenefit : liabilityBO
								.getAccutorBenefitList()) {
							AccutorBenefitReqVO accutorBenefitReqVO = new AccutorBenefitReqVO();
							BeanUtils.copyProperties(accutorBenefitReqVO,
									accutorBenefit);
							accutorBenefitReqVO.setAccutorRelaValue(BeanUtils
									.copyProperties(AccutorRelaValueReqVO.class,
											accutorBenefit.getAccutorRelativity()));
							accutorBenefitReqVOs.add(accutorBenefitReqVO);
						}
						liabilityReqVO.setAccutorList(accutorBenefitReqVOs);
						//2.1.6.4 传入附加险理赔终止主险给付基本保额对应的终了红利参数
						LiabilityReqVOList.add(liabilityReqVO);
					}

					productReqVO.setLiabilityList(LiabilityReqVOList);
					productReqVOList.add(productReqVO);
				}
			
		} catch (Exception e) {
			e.printStackTrace();
			throw new BizException("匹配理算处理失败，原因是未计算出相关数据！", e.getStackTrace());
		}
		//3 调用匹配理算接口方法
		mateAdjustmentReqVO.setProductIdList(productReqVOList);
		long startTime = System.currentTimeMillis();
		logger.debug("*************************匹配理算接口入参***************************************");
	    logger.debug(XmlHelper.classToXml(mateAdjustmentReqVO));
		MateAdjustmentResVO mateAdjustmentResVO = cLMServiceUtil
				.prdimateadjustmentuccmateadjustment(mateAdjustmentReqVO);
		long endTime = System.currentTimeMillis();
		logger.debug("*************************匹配理算接口出参***************************************");
	    logger.debug(XmlHelper.classToXml(mateAdjustmentResVO));
	    logger.debug("匹配理算准备接口耗时"+(endTime-startTime));
	    //4 判断调用接口后返回结果是否为空，不为空则循环设置理赔给付责任理算出参对象
		if (mateAdjustmentResVO.getProductIdList() == null || mateAdjustmentResVO.getProductIdList().size() == 0) {
			List<CalcProductIdBO> calcProductIdBOs = new ArrayList<CalcProductIdBO>();
			for(ClaimSubCaseBO claimSubCaseBO : claimSubCaseBOList){
				claimSubCaseBO.setCalcProductIdBOList(calcProductIdBOs);
			}
			return;
		} else {//4.1遍历接口返回的责任id集合
			List<ClaimMatchJournaPO> claimMatchJournaPOList = new ArrayList<ClaimMatchJournaPO>();
			for(ClaimSubCaseBO claimSubCaseBO : claimSubCaseBOList){
				List<CalcProductIdBO> calcProductIdBOList = new ArrayList<CalcProductIdBO>();
				for (CalcProductIdBO calcProductIdBO : claimSubCaseBO.getCalcProductIdBOList()) {
					List<ClaimLiabBO> claimLiabBOList = new ArrayList<ClaimLiabBO>();
					for (com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ProductIdResVO productResVO : mateAdjustmentResVO.getProductIdList()) {
						if (calcProductIdBO.getItemId().equals(productResVO.getItemId())&&calcProductIdBO.getProductId().equals(productResVO.getProductId())) {
							for (ClaimLiabBO claimLiabBO : calcProductIdBO.getClaimLiabBOList()) {
								for (LiabilityResVO LiabilityResVO : productResVO.getLiabilityList()) {
									if (ClaimConstant.YES.equals(LiabilityResVO.getLiabMatchRes())) {
										if (claimLiabBO.getLiabId().equals(LiabilityResVO.getLiabId())) {
											// 4.2 获取理算金额和基本赔付额
											if (LiabilityResVO.getCalcPayAmount() != null) {
												claimLiabBO.getLiabilityBO().setBscPay(LiabilityResVO.getBasicPayAmount());
												claimLiabBO.getLiabilityBO().setCalcPay(LiabilityResVO.getCalcPayAmount());
												if(LiabilityResVO.getClmPayVO().size() != 0){
													claimLiabBO.getLiabilityBO().setCalcPayState(LiabilityResVO.getClmPayVO().get(0).getCalcPayState());
												}
												
											} else {
												claimLiabBO.getLiabilityBO().setBscPay(new BigDecimal(0));
												claimLiabBO.getLiabilityBO().setCalcPay(new BigDecimal(0));
												if(LiabilityResVO.getClmPayVO().size() != 0){
												    claimLiabBO.getLiabilityBO().setCalcPayState(LiabilityResVO.getClmPayVO().get(0).getCalcPayState());
												}
											}
											claimLiabBO.getLiabilityBO().setIsWaived(LiabilityResVO.getIsWaived());
											//4.3 设置关系矩阵相关参数
											//4.3.1 附加险理赔终止主险是否给付基本保额对应的终了红利标识
											//4.3.2 主险是否给付累计红利保额对应的终了红利和现金价值
											//4.3.3 附加险理赔终止主险终止标志
											//4.3.4 附加险理赔终止主险停交保费标志
											//4.3.5 理赔时主附险等比例减少基本保额标志
											claimLiabBO.getLiabilityBO().setTermMainBonusAmountFlag(LiabilityResVO.getTermMainBonusAmountFlag());
											claimLiabBO.getLiabilityBO().setTermBonusSAAmountFlag(LiabilityResVO.getTermBonusSAAmountFlag());
											claimLiabBO.getLiabilityBO().setRiderTerminateMainFlag(LiabilityResVO.getRiderTerminateMainFlag());
											claimLiabBO.getLiabilityBO().setRiderTerminateMainPremFlag(LiabilityResVO.getRiderTerminateMainPremFlag());
											claimLiabBO.getLiabilityBO().setRiderDeductMainAmountFlag(LiabilityResVO.getRiderDeductMainAmountFlag());
											//4.4 判断老系统责任id设置老系统责任Id和合并责任代码
											if (LiabilityResVO.getClauseLiabId() != null) {
												//4.4.1 老系统责任id不为空取老核心数据值
												claimLiabBO.getLiabilityBO().setClauseLiabId(LiabilityResVO.getClauseLiabId());
												claimLiabBO.getLiabilityBO().setClauseLiabCode(LiabilityResVO.getClauseLiabCode());
											} else {
												//4.4.2 老系统责任id为空取新核心数据值
												claimLiabBO.getLiabilityBO().setClauseLiabId(LiabilityResVO.getLiabId());
												claimLiabBO.getLiabilityBO().setClauseLiabCode(LiabilityResVO.getLiabCode());
											}
											claimLiabBO.getLiabilityBO().setLibaCode(LiabilityResVO.getLiabCode());
											//4.5 设置是否分期给付标识
											claimLiabBO.getLiabilityBO().setIsPayPanFlag(LiabilityResVO
																	.getIsPayPanFlag());
											//4.6 获取分期金额
											if(LiabilityResVO.getClmPayVO().size() != 0){
												if(LiabilityResVO.getClmPayVO().get(0).getFirstPayAmount() != null){
													claimLiabBO.getLiabilityBO().setInstalmentPay(LiabilityResVO.getClmPayVO().get(0).getFirstPayAmount());
												}
											}
											//4.7 设置转年金标识
											claimLiabBO.getLiabilityBO().setIsMustSign(LiabilityResVO.getIsMustSign());
											//4.8 关联豁免险种
											claimLiabBO.getLiabilityBO().setRelaBusiPrdId(LiabilityResVO.getRelaBusiPrdId());
											//4.9 获取累加器结果
											if (LiabilityResVO.getClmPayVO() != null) {
												List<ClmPayBO> clmPayBOs = new ArrayList<ClmPayBO>();
												for (ClmPayResVO clmPayResVO : LiabilityResVO.getClmPayVO()) {
													ClmPayBO clmPayBO = new ClmPayBO();
													BeanUtils.copyProperties(clmPayBO, clmPayResVO);
													//4.9.1 取剩余有效保额
													if (LiabilityResVO.getClauseLiabId() != null&&clmPayResVO.getReMainValidSumAmount()!=null) {
														//******* 如果老系统责任ID不为空则需要合并，取汇总限额出来的剩余有效保额
														clmPayBO.setSurplusEffectAmount(clmPayResVO.getReMainValidSumAmount());
													} else {
														//******* 如果老系统责任ID为空则取责任自己的剩余有效保额
														clmPayBO.setSurplusEffectAmount(clmPayResVO.getReMainValidAmount());
													}
													if(clmPayResVO.getCalcPayAmount() != null && clmPayResVO.getFirstPayAmount()!=null && ClaimConstant.YES.equals(LiabilityResVO.getIsPayPanFlag())&&clmPayBO.getSurplusEffectAmount()==null){
													    clmPayBO.setSurplusEffectAmount(clmPayResVO.getCalcPayAmount().subtract(clmPayResVO.getFirstPayAmount()));
													}else if(clmPayBO.getSurplusEffectAmount()==null){
														clmPayBO.setSurplusEffectAmount(BigDecimal.ZERO);
													}
													//4.9.2 取赔付前剩余有效保额
													if(clmPayResVO.getReMainValidPreSumAmount()==null&&clmPayResVO.getReMainValidPreAmount()==null){
														clmPayBO.setBeforeSurplusEffectAmount(claimLiabBO.getAmount());
													}else if(clmPayResVO.getReMainValidPreSumAmount()!=null&&clmPayResVO.getReMainValidPreAmount()!=null){
														if(clmPayResVO.getReMainValidPreSumAmount().compareTo(clmPayResVO.getReMainValidPreAmount())>0){
															clmPayBO.setBeforeSurplusEffectAmount(clmPayResVO.getReMainValidPreSumAmount());
														}else{
															clmPayBO.setBeforeSurplusEffectAmount(clmPayResVO.getReMainValidPreAmount());
														}
													}else if(clmPayResVO.getReMainValidPreSumAmount()!=null){
														clmPayBO.setBeforeSurplusEffectAmount(clmPayResVO.getReMainValidPreSumAmount());
													}else if(clmPayResVO.getReMainValidPreAmount()!=null){
														clmPayBO.setBeforeSurplusEffectAmount(clmPayResVO.getReMainValidPreAmount());
													}
													if (clmPayResVO.getAccutorList() != null) {
														List<AccutorBO> AccutorBOs = new ArrayList<AccutorBO>();
														for (AccutorResVO accutorResVO : clmPayResVO.getAccutorList()) {
															AccutorBO accutorBO = new AccutorBO();
															accutorBO.setAccutionBO(BeanUtils.copyProperties(AccutionBO.class,accutorResVO.getAccutionVO()));
															accutorBO.setAccutionListBO(BeanUtils.copyProperties(AccutionListBO.class,accutorResVO.getAccutionListVO()));
															AccutorBOs.add(accutorBO);
														}
														clmPayBO.setAccutorList(AccutorBOs);
														clmPayBO.setConditionType(clmPayResVO.getConditionType());
													}
													clmPayBOs.add(clmPayBO);
												}
												claimLiabBO.getLiabilityBO().setClmPayBOs(clmPayBOs);
											}
											//4.10  获取责任匹配信息,如果该责任匹配到则获取其中全部成功的那条
											for(List<com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ClmMatchInfoVO> clmMatchInfoList : LiabilityResVO.getClmMatchInfoVOs()){
												boolean flag = true;
												for(com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ClmMatchInfoVO clmMatchInfo : clmMatchInfoList){
													if(ClaimConstant.NO.equals(clmMatchInfo.getMatchResult())){
														flag = false;
													}
												}
												if(flag){
													for(com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ClmMatchInfoVO clmMatchInfo : clmMatchInfoList){
														ClaimMatchJournaPO claimMatchJournaPO = new ClaimMatchJournaPO();
														claimMatchJournaPO.setCaseId(claimSubCaseBO.getCaseId());
														claimMatchJournaPO.setPolicyId(calcProductIdBO.getContractProductBO().getPolicyId());
														claimMatchJournaPO.setPolicyCode(calcProductIdBO.getContractProductBO().getPolicyCode());
														claimMatchJournaPO.setBusiItemId(calcProductIdBO.getContractProductBO().getBusiItemId());
														claimMatchJournaPO.setBusiProdCode(calcProductIdBO.getContractBusiProdBO().getBusiProdCode());
														claimMatchJournaPO.setItemId(calcProductIdBO.getItemId());
														claimMatchJournaPO.setProductId(calcProductIdBO.getProductId());
														claimMatchJournaPO.setLiabId(LiabilityResVO.getLiabId());
														claimMatchJournaPO.setRelativeName(CodeUtils.getValueByCode("T_PRODUCT_RELATIVITY", clmMatchInfo.getRelativeId()));
														if("14".equals(clmMatchInfo.getRelativeId())){
															claimMatchJournaPO.setClmValue(ClaimCalcUtil.claimTypeSwitchClaim(Integer.parseInt(clmMatchInfo.getClmReqValue())));
															claimMatchJournaPO.setRelativeValue(ClaimCalcUtil.claimTypeSwitchClaim(Integer.parseInt(clmMatchInfo.getRelativeValue())));
														}else{
															claimMatchJournaPO.setRelativeValue(clmMatchInfo.getRelativeValue());
															claimMatchJournaPO.setClmValue(clmMatchInfo.getClmReqValue());
														}
														if(clmMatchInfo.getMatchResult()!=null){
															claimMatchJournaPO.setMatchResult(new BigDecimal(clmMatchInfo.getMatchResult()));
														}
														if(clmMatchInfo.getRelativeId()!=null){
															claimMatchJournaPO.setRelativeId(new BigDecimal(clmMatchInfo.getRelativeId()));
														}
														if(claimMatchJournaPO.getRelativeName()==null){
		                                    				claimMatchJournaPO.setRelativeName("");
		                                    			}
		                                    			if(claimMatchJournaPO.getClmValue()==null){
		                                    				claimMatchJournaPO.setClmValue("");
		                                    			}
		                                    			if(claimMatchJournaPO.getRelativeValue()==null){
		                                    				claimMatchJournaPO.setRelativeValue("");
		                                    			}
														claimMatchJournaPOList.add(claimMatchJournaPO);
													}
												}
											}
											claimLiabBOList.add(claimLiabBO);
										}
									}else{
										if(claimLiabBO.getLiabId().equals(LiabilityResVO.getLiabId())){
											//4.11 获取责任匹配信息,如果该责任没匹配到则获取其中一部分
											if(LiabilityResVO.getClmMatchInfoVOs()!=null&&LiabilityResVO.getClmMatchInfoVOs().size()>0){
												//4.11.1 找到其中匹配相关因子最多的集合
		                                    	List<com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ClmMatchInfoVO>  clmMatchInfoList = null;
		                                    	int count = 0;
		                                    	for(List<com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ClmMatchInfoVO>  clmMatchInfos : LiabilityResVO.getClmMatchInfoVOs()){
		                                    		int countSub = 0;
		                                    		for(com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ClmMatchInfoVO clmMatchInfo : clmMatchInfos){
		                                    			if(ClaimConstant.YES.equals(clmMatchInfo.getMatchResult())){
		                                    				countSub++;
		                                    			}
		                                    		}
		                                    		if(countSub>=count){
		                                    			clmMatchInfoList = clmMatchInfos;
		                                    			count = countSub;
		                                    		}
		                                    	}
		                                    	//4.11.2 保存匹配相关因子最多的集合
		                                    	if(clmMatchInfoList!=null){
		                                    		for(com.nci.tunan.prd.interfaces.calc.exports.mateadjustment.vo.ClmMatchInfoVO  clmMatchInfo : clmMatchInfoList){
		                                    			ClaimMatchJournaPO claimMatchJournaPO = new ClaimMatchJournaPO();
		                                    			claimMatchJournaPO.setCaseId(claimSubCaseBO.getCaseId());
		                                    			claimMatchJournaPO.setPolicyId(calcProductIdBO.getContractProductBO().getPolicyId());
		                                    			claimMatchJournaPO.setPolicyCode(calcProductIdBO.getContractProductBO().getPolicyCode());
		                                    			claimMatchJournaPO.setBusiItemId(calcProductIdBO.getContractProductBO().getBusiItemId());
		                                    			claimMatchJournaPO.setBusiProdCode(calcProductIdBO.getContractBusiProdBO().getBusiProdCode());
		                                    			claimMatchJournaPO.setItemId(calcProductIdBO.getItemId());
		                                    			claimMatchJournaPO.setProductId(calcProductIdBO.getProductId());
		                                    			claimMatchJournaPO.setLiabId(LiabilityResVO.getLiabId());
		                                    			claimMatchJournaPO.setRelativeName(CodeUtils.getValueByCode("T_PRODUCT_RELATIVITY", clmMatchInfo.getRelativeId()));
		                                    			if("14".equals(clmMatchInfo.getRelativeId())){
		                                    				claimMatchJournaPO.setClmValue(ClaimCalcUtil.claimTypeSwitchClaim(Integer.parseInt(clmMatchInfo.getClmReqValue())));
		                                    				claimMatchJournaPO.setRelativeValue(ClaimCalcUtil.claimTypeSwitchClaim(Integer.parseInt(clmMatchInfo.getRelativeValue())));
		                                    			}else{
		                                    				claimMatchJournaPO.setRelativeValue(clmMatchInfo.getRelativeValue());
		                                    				claimMatchJournaPO.setClmValue(clmMatchInfo.getClmReqValue());
		                                    			}
		                                    			if(clmMatchInfo.getMatchResult()!=null){
		                                    				claimMatchJournaPO.setMatchResult(new BigDecimal(clmMatchInfo.getMatchResult()));
		                                    			}
		                                    			if(clmMatchInfo.getRelativeId()!=null){
		                                    				claimMatchJournaPO.setRelativeId(new BigDecimal(clmMatchInfo.getRelativeId()));
		                                    			}
		                                    			if(claimMatchJournaPO.getRelativeName()==null){
		                                    				claimMatchJournaPO.setRelativeName("");
		                                    			}
		                                    			if(claimMatchJournaPO.getClmValue()==null){
		                                    				claimMatchJournaPO.setClmValue("");		                                    			}
		                                    			if(claimMatchJournaPO.getRelativeValue()==null){
		                                    				claimMatchJournaPO.setRelativeValue("");
		                                    			}
		                                    			claimMatchJournaPOList.add(claimMatchJournaPO);
		                                    		}
		                                    	}
											}
										}
									}
								}
							}
						}
					}
					calcProductIdBO.setClaimLiabBOList(claimLiabBOList);
					calcProductIdBOList.add(calcProductIdBO);
				}
				claimSubCaseBO.setCalcProductIdBOList(calcProductIdBOList);
			}
			//5 组装理赔匹配理算日志入参对象，批量保存理算数据信息(T_CLAIM_MATCH_JOURNA)
			claimMatchJournaDao.batchSaveClaimMatchJourna(claimMatchJournaPOList);
		}
	}

	/**
	 * 计算现金价值
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractProductBO 赔案险种信息
	 * @param claimSubCaseBO 赔案子信息
	 * @return 现价结果
	 */
	public CalcCashValueBO calcCashValue(ContractProductBO contractProductBO,
			ClaimSubCaseBO claimSubCaseBO,ContractProductOtherBO contractProductOtherBO) {
		try {
			//1 查询险种信息,(T_CONTRACT_BUSI_PROD)
			ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
			contractBusiProdPO.setCaseId(claimSubCaseBO.getCaseId());
			contractBusiProdPO.setBusiItemId(contractProductBO.getBusiItemId());
			contractBusiProdPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
			contractBusiProdPO = contractBusiProdDao.findAllContractBusiProd(
					contractBusiProdPO).get(0);
			Date dateFlag = claimSubCaseBO.getClaimDate();
			//@invalid 判断前台传入的计算截止日期不为空，直接使用前台传入的日期即可
			if(claimSubCaseBO.getComputeEndTime() != null){
			    dateFlag = claimSubCaseBO.getComputeEndTime();
			}else{
				//@invalid 如果前台没有传入，需要查询险种理算表(T_CLAIM_BUSI_PROD)获取日期
	            ClaimBusiProdPO claimBusiProdPO = new ClaimBusiProdPO();
	            claimBusiProdPO.setCaseId(claimSubCaseBO.getCaseId());
	            claimBusiProdPO.setBusiItemId(contractProductBO.getBusiItemId());
	            claimBusiProdPO.setPolicyCode(contractProductBO.getPolicyCode());
	            List<ClaimBusiProdPO> claimBusiProdPOList = claimBusiProdDao.findAllClaimBusiProd(claimBusiProdPO);
	            if(CollectionUtilEx.isNotEmpty(claimBusiProdPOList)){
	                if(claimBusiProdPOList.get(0).getComputeEndTime() != null){
	                    dateFlag = claimBusiProdPOList.get(0).getComputeEndTime();
	                }
	            }
			}
			//2 查询险种定义表信息(T_BUSINESS_PRODUCT)
			BusinessProductPO businessProductPO = new BusinessProductPO();
			businessProductPO.setProductCodeSys(contractBusiProdPO.getBusiProdCode());
			businessProductPO = businessProductDao.findAllBusinessProduct(businessProductPO).get(0);
			//3 查询保单信息(T_CONTRACT_MASTER)
			ContractMasterPO contractMasterPO = new ContractMasterPO();
			contractMasterPO.setPolicyCode(contractProductBO.getPolicyCode());
			contractMasterPO.setCaseId(claimSubCaseBO.getCaseId());
			contractMasterPO.setCurFlag(new BigDecimal(1));
			contractMasterPO = contractMasterDao
					.findContractMasterByCaseIdAndPolicyCode(contractMasterPO);
			if (ClaimConstant.PRODUCT_CATEGORY1_20003.equals(businessProductPO
					.getProductCategory1())
					|| ClaimConstant.PRODUCT_CATEGORY1_20004
							.equals(businessProductPO.getProductCategory1())) {
				BigDecimal investValue = claimPASService.queryInvestValue(
						contractProductBO, claimSubCaseBO,"");
				//4 调用计算投连万能扣费金额接口查询退保费用
				com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeReqVO inputData = new com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeReqVO();
				List<com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsAccountFeeInfoReqVO> mmsAccountFeeInfoReqVOs = 
				        new ArrayList<com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsAccountFeeInfoReqVO>();
				com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsAccountFeeInfoReqVO mmsAccountFeeInfoReqVO = 
				        new com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsAccountFeeInfoReqVO();
				List<HashMap<String, Object>> keyValueList = new ArrayList<HashMap<String, Object>>();
				HashMap<String, Object> map = new HashMap<String, Object>();
				map.put("PolicyMonth", (int) DateUtilsEx.getMonthAmount(
						contractProductBO.getValidateDate(),
						dateFlag));
				map.put("PolicyEffectiveDate", contractProductBO.getValidateDate());
				map.put("PolicyYear", (int) DateUtilsEx.getYearAmount(
						contractProductBO.getValidateDate(),
						dateFlag));
				
				map.put("AccountValue", investValue);
				map.put("ProductId", contractProductBO.getProductId());
				keyValueList.add(map);
				mmsAccountFeeInfoReqVO.setBusinessPrdId(contractBusiProdPO
						.getBusiPrdId());
				mmsAccountFeeInfoReqVO.setChargeType(ClaimConstant.CHARGE_TYPE);
				mmsAccountFeeInfoReqVO.setProductCodeSys(contractBusiProdPO
						.getBusiProdCode());
				mmsAccountFeeInfoReqVO.setKeyValueList(keyValueList);
				mmsAccountFeeInfoReqVOs.add(mmsAccountFeeInfoReqVO);
				inputData.setAccountFeeInfoReqVOList(mmsAccountFeeInfoReqVOs);
				com.nci.tunan.mms.interfaces.calc.exports.calcaccountfee.vo.MmsCalcAccountFeeResVO outputData = cLMServiceUtil
						.prdimmscalcaccountfeeucccalcAccountFee(inputData);
				BigDecimal fee = BigDecimal.ZERO;
				if (outputData != null
						&& outputData.getAccountFeeInfoResVOList() != null
						&& outputData.getAccountFeeInfoResVOList().size() > 0
						&& outputData.getAccountFeeInfoResVOList().get(0) != null
						&& outputData.getAccountFeeInfoResVOList().get(0)
								.getFees() != null
						&& outputData.getAccountFeeInfoResVOList().get(0)
								.getFees().size() > 0
						&& outputData.getAccountFeeInfoResVOList().get(0)
								.getFees().get(0) != null
						&& outputData.getAccountFeeInfoResVOList().get(0)
								.getFees().get(0).get("Value") != null) {
					fee = new BigDecimal(outputData
							.getAccountFeeInfoResVOList().get(0).getFees()
							.get(0).get("Value"));
					CalcCashValueBO calcCashValueBO = new CalcCashValueBO();
					calcCashValueBO.setBaseCashValue(fee);
					return calcCashValueBO;
				}
			} else {
                //5 设置计算险种现金价值入参对象
				MmsCalcCashValueReqVO inputData = new MmsCalcCashValueReqVO();
				//6 查询责任是否为基本责任()
				ProductLifePO productLifePO = new ProductLifePO();
				productLifePO.setProductId(contractProductBO.getProductId());
				productLifePO = productLifeDao
						.findProductLifeByProductId(productLifePO);
				//7 查询生存给付应领记录
				QueryPayDueResVO paiquerypaydueuccqueryPayDue = null;
				if(contractProductBO.getPaiquerypaydueuccqueryPayDue()!=null){
					paiquerypaydueuccqueryPayDue = contractProductBO.getPaiquerypaydueuccqueryPayDue();
				}else{
					QuerPayDueReqVO payDueinputData = new QuerPayDueReqVO();
					QuerPayDueReqVOParam querPayDueReqVOParam=new QuerPayDueReqVOParam();
					querPayDueReqVOParam.setBusiProdCode(contractBusiProdPO.getBusiProdCode());
					querPayDueReqVOParam.setEndDate(dateFlag);
					querPayDueReqVOParam.setPolicyCode(contractProductBO.getPolicyCode());
					querPayDueReqVOParam.setProductCode(contractProductBO.getProductCode()); 
					List<QuerPayDueReqVOParam> querPayDueReqVOParamList=new ArrayList<QuerPayDueReqVOParam>();
					querPayDueReqVOParamList.add(querPayDueReqVOParam);
					payDueinputData.setQuerPayDueReqVOParam( querPayDueReqVOParamList);
					paiquerypaydueuccqueryPayDue = cLMServiceUtil
							.paiquerypaydueuccqueryPayDue(payDueinputData);
					contractProductBO.setPaiquerypaydueuccqueryPayDue(paiquerypaydueuccqueryPayDue);
				}
				
				 
				List<QueryPayDueVO> queryPayDueVOList = null;
				if (paiquerypaydueuccqueryPayDue != null&&paiquerypaydueuccqueryPayDue.getQueryPayDueResVOParam()!=null) {
					queryPayDueVOList = paiquerypaydueuccqueryPayDue.getQueryPayDueResVOParam().get(0).getQueryPayDueVOList();
				}
				//8 查询主险种被保人列表(T_BENEFIT_INSURED)
				BenefitInsuredPO benefitInsuredPO = new BenefitInsuredPO();
				benefitInsuredPO.setBusiItemId(contractProductBO
						.getBusiItemId());
				benefitInsuredPO.setCaseId(claimSubCaseBO.getCaseId());
				benefitInsuredPO.setCurFlag(new BigDecimal(1));
				List<BenefitInsuredPO> benefitInsuredPOs = benefitInsuredDao
						.findAllBenefitInsured(benefitInsuredPO);
				//9 查询保单是否在第一次领取日前，查询上次分红日
				com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoResVO claimQueryPolicyInfoResVO = null;
				if(contractProductBO.getClaimQueryPolicyInfoResVO()==null||contractProductBO.getClaimQueryPolicyInfoResVO().getCliamMap()!=null
						||contractProductBO.getClaimQueryPolicyInfoResVO().getCliamMap().get(ClaimConstant.IS_STAR_ANNUITY)==null
						||contractProductBO.getClaimQueryPolicyInfoResVO().getCliamMap().get(ClaimConstant.LAST_ALLOCATE_DATE)==null
						||contractProductBO.getClaimQueryPolicyInfoResVO().getCliamMap().get(ClaimConstant.TOP_UP_SA)==null
						||contractProductBO.getClaimQueryPolicyInfoResVO().getCliamMap().get(ClaimConstant.ADD_OPTLIAB_DATE)==null){
					com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO claimQueryPolicyInfoVO = 
					        new com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO();
					claimQueryPolicyInfoVO.setBusiItemId(contractProductBO
							.getBusiItemId());
					claimQueryPolicyInfoVO.setClaimDate(claimSubCaseBO
							.getClaimDate());
					claimQueryPolicyInfoVO.setItemId(contractProductBO.getItemId());
					claimQueryPolicyInfoVO.setPolicyId(contractProductBO
							.getPolicyId());
					List<String> typeFlagList = new ArrayList<String>();
					typeFlagList.add(ClaimConstant.IS_STAR_ANNUITY);
					typeFlagList.add(ClaimConstant.LAST_ALLOCATE_DATE);
					typeFlagList.add(ClaimConstant.TOP_UP_SA);
					typeFlagList.add(ClaimConstant.ADD_OPTLIAB_DATE);
					claimQueryPolicyInfoVO.setTypeFlagList(typeFlagList);
					claimQueryPolicyInfoResVO = cLMServiceUtil
							.paiclaimquerypolicyinfouccqueryPolicyInfo(claimQueryPolicyInfoVO);
					if(contractProductBO.getClaimQueryPolicyInfoResVO()==null||contractProductBO.getClaimQueryPolicyInfoResVO().getCliamMap()!=null){
						contractProductBO.setClaimQueryPolicyInfoResVO(claimQueryPolicyInfoResVO);
					}else{
						contractProductBO.getClaimQueryPolicyInfoResVO().getCliamMap().putAll(claimQueryPolicyInfoResVO.getCliamMap());
					}
				}
				claimQueryPolicyInfoResVO = contractProductBO.getClaimQueryPolicyInfoResVO();

				PrdQueryBonusInfoResVO outputDataRate = null;
				if(contractProductBO.getOutputDataRate()==null){
					PrdQueryBonusInfoReqVO inputDataRate = new PrdQueryBonusInfoReqVO();
					inputDataRate.setBusinessPrdId(contractBusiProdPO
							.getBusiPrdId());
					inputDataRate.setRateType(new BigDecimal(0));
					inputDataRate.setStartDate(dateFlag);
					inputDataRate.setChargeYear(contractProductBO.getChargeYear());
					inputDataRate.setOptionLiab(new BigDecimal(productLifePO
							.getOptionType()));
					inputDataRate
					.setProductCode(contractProductBO.getProductCode());
					inputDataRate.setBasicChargeYear(contractProductBO
							.getChargeYear());
					inputDataRate.setPolicyYear(new BigDecimal(
							(int) DateUtilsEx.getYearAmount(
									contractProductBO.getValidateDate(),
									dateFlag)));
					inputDataRate.setProductId(contractProductBO.getProductId());
					inputDataRate.setSysDate(WorkDateUtil.getWorkDate());
					inputDataRate.setAccidentDate(dateFlag);
					inputDataRate.setRenewalFlag(Integer
							.parseInt(ClaimConstant.YES));
					if (contractBusiProdPO.getRenewDecision() == null
							|| !contractBusiProdPO.getRenewDecision().equals(
									ClaimConstant.DECISION_DESC_ALREADY)) { 
						//@invalid 没有续保或转保
						inputDataRate.setRenewalFlag(Integer
								.parseInt(ClaimConstant.NO));
					}
					inputDataRate.setPayPeriodFlag(new BigDecimal(ClaimConstant.NO));
					inputDataRate.setFirstPeriodFlag(new BigDecimal(ClaimConstant.NO));
                     
                        if (queryPayDueVOList != null && queryPayDueVOList.size() != 0) {
                            int count=0;
                          //@invalid 判断领取方式，如果月领，首年内的值都传1，如果是年领的首年传1.
                            if(ClaimConstant.PAY_TYPE_FORE.equals(queryPayDueVOList.get(0).getPlanFreq())){
                                //@invalid 月领
                                for(QueryPayDueVO queryPayDueVO:queryPayDueVOList){
                                    if(ClaimConstant.LIAB_ID_1106.equals(queryPayDueVO.getLiabId())){
                                        inputDataRate.setPayPeriodFlag(new BigDecimal(ClaimConstant.YES));
                                        count++;
                                    }
                                }
                                if(count <= 12){
                                    inputDataRate.setFirstPeriodFlag(new BigDecimal(ClaimConstant.YES));
                                }
                            } else {
                                //@invalid 其他领取方式
                                for(QueryPayDueVO queryPayDueVO:queryPayDueVOList){
                                    if(ClaimConstant.LIAB_ID_1106.equals(queryPayDueVO.getLiabId())){
                                        inputDataRate.setPayPeriodFlag(new BigDecimal(ClaimConstant.YES));
                                        count++;
                                    }
                                }
                                if(count==1){
                                    inputDataRate.setFirstPeriodFlag(new BigDecimal(ClaimConstant.YES));
                                }
                            }
                            
                        }
					
					
					
					contractProductBO.setOutputDataRate(cLMServiceUtil.prdiprdquerybonusinfouccfindBonusInfo(inputDataRate));
				}
				outputDataRate = contractProductBO.getOutputDataRate();
				//@invalid 查询当期缴费情况				
				QueryPremArapReqVO PremInputData = new QueryPremArapReqVO();
				PremInputData.setPolicyCode(contractProductBO.getPolicyCode());
				PremInputData.setStartTime(contractProductBO.getValidateDate());
				PremInputData.setEndTime(dateFlag);
				PremInputData.setBusiProdCode(contractBusiProdPO.getBusiProdCode());
				List<BigDecimal> itemIdList = new ArrayList<BigDecimal>();
				itemIdList.add(contractProductBO.getItemId());
				PremInputData.setItemIdList(itemIdList);
				//11 查询当期保费缴费情况
				/* 990产品现金价值邮件确认
				BigDecimal prem = ClaimConstant.BIGDECIMAL_ZERO;
				Date dueTime = null;
				QueryPremArapResVO paiquerypremarapuccqueryPremArapMsg = cLMServiceUtil.paiquerypremarapuccqueryPremArapMsg(PremInputData);
				if(paiquerypremarapuccqueryPremArapMsg.getPremArapVOs() == null || paiquerypremarapuccqueryPremArapMsg.getPremArapVOs().size() == 0){
					//@invalid 接口只会查续期缴费记录，当没有记录则证明当期保费已交
					prem = contractProductBO.getStdPremAf();
					//@invalid 保单经过时间(月数）
					dueTime = dateFlag;
				}else{
					List<com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapVO> qureyPremArapVOList= paiquerypremarapuccqueryPremArapMsg.getPremArapVOs();
					for (int i = 0; i < qureyPremArapVOList.size(); i++) {
			            for (int j = 0; j < i; j++) {
			                if (qureyPremArapVOList.get(i).getDueTime().compareTo(qureyPremArapVOList.get(j).getDueTime()) > 0) {
			                	com.nci.tunan.pa.interfaces.serviceData.querypremaraplist.QueryPremArapVO temp = qureyPremArapVOList.get(j);
			                	qureyPremArapVOList.set(j, qureyPremArapVOList.get(i)) ;
			                	qureyPremArapVOList.set(i, temp);
			                }
			            }
			        }
					
					//@invalid 当期保费、趸交保费（不含加费）   
					//@invalid 当期保费未缴时，年缴类型为传0，其余类型正常传当期保费，当期保费已交则全部正常传当期保费
					//@invalid 当期保费未缴时，计算到宽限期开始的前一天，当期保费已交时，计算到理赔出险日
					for (int i = 0; i < qureyPremArapVOList.size(); i++) {
						if(qureyPremArapVOList.get(i).getDueTime().compareTo(dateFlag)<0){
							if (dueTime == null) {
								dueTime = qureyPremArapVOList.get(i).getDueTime();
							}
							if (dueTime != null && qureyPremArapVOList.get(i).getDueTime().compareTo(dueTime)==0) {
								if(qureyPremArapVOList.get(i).getFeeStatus()!=null){
									//01是已收费，19是已核销都是已缴所以传当期保费
									if(qureyPremArapVOList.get(i).getFeeStatus().equals(ClaimConstant.FEE_STATUS_01) || qureyPremArapVOList.get(i).getFeeStatus().equals(ClaimConstant.FEE_STATUS_19)
											|| qureyPremArapVOList.get(i).getFeeStatus().equals(ClaimConstant.FEE_STATUS_16)){
										if (qureyPremArapVOList.get(i).getArapFlag().equals(ClaimConstant.STRING_TWO)) {
							            	prem = prem.subtract(qureyPremArapVOList.get(i).getFeeAmount());
							            } else {
							            	prem = prem.add(qureyPremArapVOList.get(i).getFeeAmount());
							            	break;
							            }
										
									}
								}
							}
						}
					}
				} **/
				//当期保费
				inputData.setPremium( contractProductBO.getStdPremAf());
				// 保单经过时间(月数）
	/*			if(ClaimConstant.BIGDECIMAL_ZERO.compareTo(prem)<0){
					inputData.setPolicyMonth(new BigDecimal(DateUtilsEx.getMonthAmount(contractProductBO.getValidateDate(),dateFlag)));
				}else{
					inputData.setPolicyMonth(new BigDecimal(DateUtilsEx.getMonthAmount(contractProductBO.getValidateDate(),dueTime)));
				}*/
				inputData.setPolicyMonth(new BigDecimal(DateUtilsEx.getMonthAmount(contractProductBO.getValidateDate(),dateFlag)));
				//@invalid  保额红利利率
				inputData.setBonusRate(outputDataRate.getBonusRate());
				//@invalid  保额红利利率
				inputData.setBonusRate1(outputDataRate.getBonusRate());
				//@invalid  红利公布日
				inputData.setBonusAnnounceDate(outputDataRate
						.getRateReleaseDate());
				//@invalid 责任组
				inputData.setProductId(contractProductBO.getProductId());
				//@invalid 续保标志
				inputData.setRenewalFlag(1);
				if (contractBusiProdPO.getRenewDecision() == null
						|| !contractBusiProdPO.getRenewDecision().equals(
								ClaimConstant.DECISION_DESC_ALREADY)) {
					inputData.setRenewalFlag(0);
				}
				//@invalid 交费频率
				inputData.setChargeMode(contractProductBO.getPremFreq()
						.intValue());
				//@invalid 交费期间
				inputData.setChargeYear(contractProductBO.getChargeYear()
						.intValue());
				//@invalid 长期护理保险金最高给付期限
				if (ClaimConstant.CLM_PRODCODE_EIGHTTWOSEVEN.equals(contractProductBO.getProductCode())) {
					inputData.setLtciMaxBenefitPeriod(contractProductOtherBO.getMaxBenefitPeriod()
							.intValue());
					inputData.setCurPremiumPaidFlag(ClaimConstant.BIGDECIMAL_ZERO);
					}
				//@invalid 被保人
				for (BenefitInsuredPO po : benefitInsuredPOs) {
					InsuredListPO insuredListPO = new InsuredListPO();
					insuredListPO.setCaseId(claimSubCaseBO.getCaseId());
					insuredListPO.setListId(po.getInsuredId());
					insuredListPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
					List<InsuredListPO> insuredListPOs = insuredListDao
							.findAllInsuredList(insuredListPO);
					BigDecimal age = insuredListPOs.get(0).getInsuredAge();

					//@invalid 查询被保人客户详细信息
					com.nci.core.common.interfaces.vo.CustomerVO customerVO = new com.nci.core.common.interfaces.vo.CustomerVO();
					CustomerBaseInfoVO customerBaseInfo = new CustomerBaseInfoVO();
					customerBaseInfo.setCustomerId(insuredListPOs.get(0)
							.getCustomerId());
					customerVO.setCustomerBaseInfo(customerBaseInfo);
					com.nci.core.common.interfaces.vo.CustomerVO insured = BOServiceFactory
							.getCustomerUCC().queryCustomerBaseInfo(customerVO);
					if (insured != null) {
						//@invalid 连生险中的丈夫年龄（118、144产品用的）
						if (insured.getCustomerBaseInfo().getCustomerGender()
								.compareTo(new BigDecimal(1)) == 0) {
							inputData.setHusbandAge(age.intValue());
						}
						//@invalid 连生险中的妻子年龄（118、144产品用的）
						if (insured.getCustomerBaseInfo().getCustomerGender()
								.compareTo(new BigDecimal(2)) == 0) {
							inputData.setHusbandAge(age.intValue());
							inputData.setWifeAge(age.intValue());
						}
						//@invalid 第一被保险人年龄
						if (new BigDecimal(1).equals(po.getOrderId())) {
							inputData.setAge01(age.intValue());
						}
						//@invalid 第二被保险人年龄
						if (new BigDecimal(2).equals(po.getOrderId())) {
							inputData.setAge02(age.intValue());
						}
						//@invalid 被保人性别
						inputData.setGender(insured.getCustomerBaseInfo()
								.getCustomerGender().intValue());
						//@invalid 被保险人年龄
						inputData.setAge(age.intValue());
						//@invalid 被保人年龄
						inputData.setPolicyYearAge(age.intValue());
					}
				}
				//@invalid 交费期间类型
				inputData.setChargePeriod(Integer.parseInt(contractProductBO
						.getChargePeriod()));
				//@invalid 交费期间类型 为1 趸交，做特殊处理 置换为2（产品需求王书娟确认的）
				if(contractProductBO.getChargePeriod() != null && contractProductBO.getChargePeriod().equals(ClaimConstant.PREM_FREQ_ONE.toString())){
					inputData.setChargePeriod(Integer.parseInt(ClaimConstant.PREM_FREQ_TWO.toString()));
				}
				//@invalid 保障年期期间
				inputData.setCoverageYear(contractProductBO.getCoverageYear()
						.intValue());
				//@invalid 保单状态
				inputData.setLiabilityStatus(contractProductBO
						.getLiabilityState().intValue());
				//@invalid 保单生效日
				inputData.setPolicyEffectiveDate(contractProductBO.getValidateDate());
				//@invalid 享受健康服务标志
				if (contractProductBO.getHealthServiceFlag() != null) {
					inputData.setHealthServiceFlag(contractProductBO.getHealthServiceFlag().intValue());
				} else {
					inputData.setHealthServiceFlag(0);
				}
				//@invalid 已交保费（不含加费）
				inputData.setGrossPremium(contractProductBO.getTotalPremAf().subtract(contractProductBO.getExtraPremAf()));
				//@invalid 保障年期类型
				inputData.setCoveragePeriod(contractProductBO.getCoveragePeriod());
				//@invalid  查询主险的保障年期
				ContractBusiProdPO contractBusiProd = new ContractBusiProdPO();
				contractBusiProd.setCaseId(contractProductBO.getCaseId());
				contractBusiProd.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
				List<ContractBusiProdPO> contractBusiProds = contractBusiProdDao.findAllContractBusiProd(contractBusiProd);
				com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodReqVO guaranteePeriodReqVO = 
						new com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodReqVO();
				
				for(ContractBusiProdPO contractBusiProdP: contractBusiProds){
				    if(contractBusiProdP.getMasterBusiItemId() == null){
				        ContractProductPO contractProductPO = new ContractProductPO();
		                contractProductPO.setCaseId(contractProductBO.getCaseId());
		                contractProductPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
		                contractProductPO.setBusiItemId(contractBusiProdP.getBusiItemId());
		                List<ContractProductPO> contractProductPOs = contractProductDao.findAllContractProduct(contractProductPO);
		                if(contractProductPOs.size() != ClaimConstant.ZERO){
		                    inputData.setMainCoverageYear(contractProductPOs.get(0).getCoverageYear().intValue());
					    	ICLMServiceUtil clmServiceUtil = (ICLMServiceUtil) SpringContextUtils.getBean("CLM_clmServiceUtil");
	        				guaranteePeriodReqVO.setBusiItemId(contractBusiProdP.getBusiItemId());
	        				guaranteePeriodReqVO.setPayDueDate(claimSubCaseBO
	        						.getClaimDate());
	        				com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodResVO guaranteePeriodResVO = clmServiceUtil
	        						.paiguaranteeperiodparamuccqueryGuaranteePeriodParam(guaranteePeriodReqVO);
	        				if (guaranteePeriodResVO != null
	        						&& guaranteePeriodResVO.getGuaranteePeriod() != null) {
	        					inputData.setIsMainguaranteeReceivePeriod(Integer.parseInt(guaranteePeriodResVO.getGuaranteePeriod().toString()));
	        				}
		                    if(contractProductPOs.get(0).getPayYear()!=null){
		                    	inputData.setIsMainpayYear(Integer.parseInt(contractProductPOs.get(0).getPayYear().toString()));
		                    }
		                }
				    }else {
				        ContractProductPO contractProductPO = new ContractProductPO();
		                contractProductPO.setCaseId(contractProductBO.getCaseId());
		                contractProductPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
		                if(ClaimConstant.CLM_BUSI_PRODCODE_NINETHREEFIVE.equals(contractBusiProdP.getBusiProdCode())){
		                	contractProductPO.setBusiItemId(contractBusiProdP.getMasterBusiItemId());
		                }else {
		                	contractProductPO.setBusiItemId(contractBusiProdP.getBusiItemId());
						}
		                List<ContractProductPO> contractProductPOs = contractProductDao.findAllContractProduct(contractProductPO);
		                if(contractProductPOs.size() != ClaimConstant.ZERO){ 
					    	ICLMServiceUtil clmServiceUtil = (ICLMServiceUtil) SpringContextUtils.getBean("CLM_clmServiceUtil");
					    	   if(ClaimConstant.CLM_BUSI_PRODCODE_NINETHREEFIVE.equals(contractBusiProdP.getBusiProdCode())){
					    		   guaranteePeriodReqVO.setBusiItemId(contractBusiProdP.getMasterBusiItemId());
				                }else {
				                	guaranteePeriodReqVO.setBusiItemId(contractBusiProdP.getBusiItemId());
								}
	        				guaranteePeriodReqVO.setPayDueDate(claimSubCaseBO
	        						.getClaimDate());
	        				com.nci.tunan.pa.interfaces.serviceData.guaranteeperiodparam.GuaranteePeriodResVO guaranteePeriodResVO = clmServiceUtil
	        						.paiguaranteeperiodparamuccqueryGuaranteePeriodParam(guaranteePeriodReqVO);
	        				if (guaranteePeriodResVO != null
	        						&& guaranteePeriodResVO.getGuaranteePeriod() != null) {
	        					inputData.setGuaranteeReceivePeriod((Integer.parseInt(guaranteePeriodResVO.getGuaranteePeriod().toString())));
	        				}
		                }
					}
				    
				}
				
				//@invalid 份数
				inputData.setUnit(contractProductBO.getUnit());
				//@invalid 保单实际经过天数
				inputData.setPolicydays((int) DateUtilsEx.getDayAmount(contractProductBO.getValidateDate(),dateFlag));
				//@invalid 保单年度
				inputData.setPolicyYear((int) DateUtilsEx.getYearAmount(contractProductBO.getValidateDate(),dateFlag));
				if(contractProductOtherBO != null){  
                    if(StringUtils.isNotBlank(contractProductOtherBO.getField1())){
                        inputData.setPlanType(Integer.parseInt(contractProductOtherBO.getField1()));
                    }
                }
				//累计给付的疾病保险金已达到基本保险金额（由于产品现价公式为保全提供使用，所以理赔传入默认值）
				inputData.setIsPayToSA(Integer.parseInt(ClaimConstant.STRING_ZERO));
				//@invalid 健康体检服务是否使用标识(869产品使用,理赔默认传1)
				inputData.setMedicalCardUseFlag(Integer.parseInt(ClaimConstant.STRING_ONE));
				//@invalid 领取频率
				if (contractProductBO.getPayFreq() != null) {
					inputData.setPayType(Integer.parseInt(contractProductBO.getPayFreq()));
				} else {
					//@invalid 如果在责任组抄单数据中查不到，调保单接口查询
					String payFreq = "";
					ICLMServiceUtil clmServiceUtil = (ICLMServiceUtil) SpringContextUtils.getBean("CLM_clmServiceUtil");
		    		com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO inputDat = 
		    		        new com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO();
		    		inputDat.setBusiItemId(contractProductBO.getBusiItemId());
		    		inputDat.setClaimDate(dateFlag);
		    		inputDat.setItemId(contractProductBO.getItemId());
		    		inputDat.setPolicyId(contractProductBO.getPolicyId());
		    		List<String> typeFlagList = new ArrayList<String>();
		    		typeFlagList.add(ClaimConstant.IS_PAY_FREQ);
		    		inputDat.setTypeFlagList(typeFlagList);
		    		com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoResVO  outputData= clmServiceUtil.paiclaimquerypolicyinfouccqueryPolicyInfo(inputDat);
		    		if(outputData!=null&&outputData.getCliamMap()!=null&&outputData.getCliamMap().get("isPayFreq")!=null){
		    			String[] payFreqs = outputData.getCliamMap().get("isPayFreq").split(",");
		    			if(payFreqs.length > 1){
		    				for(int i=0;i<payFreqs.length;i++){
		    					if(!"".equals(payFreqs[i])&&!"null".equals(payFreqs[i])){
		    						payFreq = outputData.getCliamMap().get("isPayFreq").split(",")[i];
		    						break;
		    					}
		    				}
		    			}else if (!"null".equals(outputData.getCliamMap().get("isPayFreq"))){
		    				payFreq = outputData.getCliamMap().get("isPayFreq");
		    			}
		    		}
		    		if(!"".equals(payFreq)){
		    			if("A".equals(payFreq)){
		    				inputData.setPayType(ClaimConstant.TEN);
		    			}else{
		    				inputData.setPayType(Integer.parseInt(payFreq));
		    			}
		    		}
				}
				//@invalid 领取类型
				if (contractProductBO.getPayPeriod() != null) {
					inputData.setMatchPayWay(Integer.parseInt(contractProductBO.getPayPeriod()));
				}
				//@invalid 约定领取年龄
				if (contractProductBO.getPayYear() != null) {
					inputData.setPayYear(contractProductBO.getPayYear().intValue());
				}
				//@invalid 基本保额
				inputData.setSA(contractProductBO.getAmount());
				//@invalid 事故发生日
				inputData.setAccidentDate(dateFlag);
				//@invalid 可选责任终止日
				inputData.setOptLiabTerminalDate(contractProductBO.getExpiryDate());
				//@invalid 是否为整年度分红标志位
				inputData.setIsWholePolicyFlag(0);
				//@invalid 可选责任生效日
				inputData.setOptLiabEffectDate(contractProductBO.getValidateDate());
				//@invalid 满期保险金
				inputData.setMaturityAmount(new BigDecimal(0));
				//@invalid 健康服务/体检费 注：751001和752001责任组的总保费就是健康服务费、其他的没有健康服务费
				if ("751001".equals(contractProductBO.getProductCode())|| "752001".equals(contractProductBO.getProductCode())) {
					inputData.setHealthServicePremium(contractProductBO.getTotalPremAf());
				} else {
					inputData.setHealthServicePremium(new BigDecimal(0));
				}
				inputData.setPreliabilityStatus(ClaimConstant.LIABILITY_STATUS_EFFECTIVE.intValue());
				//@invalid 上一保单年度的保单状态
				if (!ClaimConstant.LIABILITY_STATUS_EFFECTIVE.equals(contractProductBO
						.getLiabilityState())) {
					if (CommonMethodUtil.getPolicyYear(contractProductBO.getValidateDate(),contractMasterPO.getLapseDate()) < inputData.getPolicyYear()) {
						inputData.setPreliabilityStatus(ClaimConstant.LIABILITY_STATUS_LOSE_EFFECTIVENESS.intValue());
					}
				}
				
				//@invalid 累积到T个年度f个整月的红利保额
				if (contractProductBO.getBonusSa() != null) {
					inputData.setBonusSA(contractProductBO.getBonusSa());
				} else {
					inputData.setBonusSA(new BigDecimal(ClaimConstant.ZERO));
				}
				//@invalid 要计算现价的险种的生效日　
				inputData.setBusinessEffectDate(contractProductBO.getValidateDate());
				//@invalid 要计算现价的险种的满期日，切记不是险种的终止日期，而是满期日　
				if (contractProductBO.getCoveragePeriod().equals(ClaimConstant.COVERAGE_PERIOD_D)) {
					inputData.setBusinessExpiredDate(DateUtilsEx.addDay(contractProductBO.getValidateDate(),contractProductBO.getCoverageYear().intValue()));
				} else if (contractProductBO.getCoveragePeriod().equals(ClaimConstant.COVERAGE_PERIOD_M)) {
					inputData.setBusinessExpiredDate(DateUtilsEx.addMonth(	contractProductBO.getValidateDate(),contractProductBO.getCoverageYear().intValue()));
				} else if (contractProductBO.getCoveragePeriod().equals(ClaimConstant.COVERAGE_PERIOD_Y)) {
					inputData.setBusinessExpiredDate(DateUtilsEx.addYear(contractProductBO.getValidateDate(),contractProductBO.getCoverageYear().intValue()));
				}else{
					inputData.setBusinessExpiredDate(contractProductBO.getExpiryDate());
				}
				inputData.setBusinessExpiredDate(DateUtilsEx.addDay(inputData.getBusinessExpiredDate(), -1));
				//@invalid 当期缴费标志
				inputData.setCurPremiumPaidFlag(new BigDecimal(ClaimConstant.YES));
				//@invalid 基本责任交费期间
				inputData.setBasicChargeYear(contractProductBO.getChargeYear().intValue());
				inputData.setRenewalFlag(Integer.parseInt(ClaimConstant.YES));
				if (contractBusiProdPO.getRenewDecision() == null|| !contractBusiProdPO.getRenewDecision().equals(ClaimConstant.DECISION_DESC_ALREADY)) { //@invalid  没有续保或转保
					inputData.setRenewalFlag(Integer.parseInt(ClaimConstant.NO));
				}
				//@invalid 保单应收应付表查询接口
				int getTimes = 0;
				//@invalid 本年度缴费次数-对于非年缴型期交产品
				int curYearPayCounts = 0;
				QueryPremArapResVO outPutData5 = null;
				if(contractProductBO.getOutPutData5()==null){
					QueryPremArapReqVO inputData5 = new QueryPremArapReqVO();
					inputData5.setPolicyCode(contractProductBO.getPolicyCode());
					inputData5.setStartTime(contractProductBO.getValidateDate());
					inputData5.setEndTime(WorkDateUtil.getWorkDate());
					outPutData5 = cLMServiceUtil.paiquerypremarapuccqueryPremArapMsg(inputData5);
					contractProductBO.setOutPutData5(outPutData5);
				}else{
					outPutData5 = contractProductBO.getOutPutData5();
				}
				List<QueryPremArapVO> queryPremArapVOList = outPutData5.getPremArapVOs();
				if (queryPremArapVOList != null) {
					for (int i = 0; i < queryPremArapVOList.size(); i++) {
						QueryPremArapVO queryPremArapVO = queryPremArapVOList.get(i);
						if (ClaimConstant.PAYMENT_STATUS_SUCCEED.equals(queryPremArapVO.getFeeStatus())) {
							getTimes++;
						}
						if (ClaimConstant.PAYMENT_STATUS_SUCCEED.equals(queryPremArapVO.getFeeStatus())) {
							Integer curYear = CommonMethodUtil.getPolicyYear(queryPremArapVO.getDueTime(),WorkDateUtil.getWorkDate());
							if (curYear == 1) {
								curYearPayCounts++;
							}
						}
					}
				}
				//@invalid 连续交费次数
				inputData.setGetTimes(new BigDecimal(getTimes));
				//@invalid 本年度缴费次数-对于非年缴型期交产品
				inputData.setCurYearPayCounts(curYearPayCounts);
				//@invalid 生存金接口获取参数
				SurvivalAmountReqVO SurvivalAmountReqVO = new SurvivalAmountReqVO();
				SurvivalAmountReqVO.setItemId(contractProductBO.getItemId());
				SurvivalAmountReqVO.setPayDate(dateFlag);
				SurvivalAmountResVO outData = cLMServiceUtil
						.paicalcsurvivalamountucccalculateNexAmount(SurvivalAmountReqVO);
				//@invalid 下一年度尚未领取的保单年度末生存领取金额
				if(outData != null ){
				    if (outData.getNextBasicAmount() == null) {
	                    inputData.setNextYearSurvivalPayment(new BigDecimal(0));
	                } else {
	                    inputData.setNextYearSurvivalPayment(outData.getNextBasicAmount());
	                }
	                //@invalid 第t个保单年度末（即第t+1个保单年度初）的基本保额对应的生存领取金额
	                if (outData.getCurBasicAmount() == null) {
	                    inputData.setCurYearSP(new BigDecimal(0));
	                } else {
	                    inputData.setCurYearSP(outData.getCurBasicAmount());
	                }
	                //@invalid 下一年保单年度内退保前累计的基本保额对应生存领取金额
	                if (outData.getBasicAmount() == null) {
	                    inputData.setTopUpNYSP(new BigDecimal(0));
	                } else {
	                    inputData.setTopUpNYSP(outData.getBasicAmount());
	                }
	                //@invalid 第t+1保单年度内退保前累计生存领取金占保险金额比例
	                if (outData.getBonusAmount() == null) {
	                    inputData.setBtopUpNYSPRate(new BigDecimal(0));
	                } else {
	                    inputData.setBtopUpNYSPRate(outData.getBonusAmount());
	                }
	                //@invalid 第t个保单年度末（即第t+1个保单年度初）生存领取金占保险金额比例
	                if (outData.getCurBonusAmount() == null) {
	                    inputData.setBcurYearSPRate(new BigDecimal(0));
	                } else {
	                    inputData.setBcurYearSPRate(outData.getCurBonusAmount());
	                }
	                //@invalid 下一年度（t+1保单年度末）尚未领取的保单年度末红利保额相关的生存领取金占红利保额的比例
	                if (outData.getNextBonusAmount() == null) {
	                    inputData.setNYSurvivalPaymentPerBonusSA(new BigDecimal(0));
	                } else {
	                    inputData.setNYSurvivalPaymentPerBonusSA(outData.getNextBonusAmount());
	                }
				} else {
				    inputData.setNextYearSurvivalPayment(new BigDecimal(0));  
				    inputData.setCurYearSP(new BigDecimal(0));
				    inputData.setTopUpNYSP(new BigDecimal(0));
				    inputData.setBtopUpNYSPRate(new BigDecimal(0));
				    inputData.setNYSurvivalPaymentPerBonusSA(new BigDecimal(0));
				    inputData.setBcurYearSPRate(new BigDecimal(0));
				}
				

				if (queryPayDueVOList == null || queryPayDueVOList.size() == 0) {
					//@invalid 进入领取期标志
					inputData.setPayPeriodFlag(new BigDecimal(0));
					//@invalid 领取金额
					inputData.setPayAmount(new BigDecimal(0));
					//@invalid 是否第一次进入领取期
					inputData.setFirstPeriodFlag(new BigDecimal(0));
				} else {
				    int count=0;
				    inputData.setPayAmount(new BigDecimal(0));
				    if(ClaimConstant.PAY_TYPE_FORE.equals(queryPayDueVOList.get(0).getPlanFreq())){
				      //@invalid 月领
				        for(QueryPayDueVO queryPayDueVO:queryPayDueVOList){
	                        if(ClaimConstant.LIAB_ID_1106.equals(queryPayDueVO.getLiabId())){
	                            //@invalid 进入领取期标志
	                            inputData.setPayPeriodFlag(new BigDecimal(ClaimConstant.YES));
	                            //@invalid 领取金额
	                            inputData.setPayAmount(inputData.getPayAmount().add(queryPayDueVO.getFeeAmount()));
	                            count++;
	                        }
	                    }
	                    if(count <= 12){
	                        //@invalid 是否第一次进入领取期
	                        inputData.setFirstPeriodFlag(new BigDecimal(ClaimConstant.YES));
	                    }
				    } else {
				      //@invalid 其他领取方式
                        for(QueryPayDueVO queryPayDueVO:queryPayDueVOList){
                            if(ClaimConstant.LIAB_ID_1106.equals(queryPayDueVO.getLiabId())){
                                //@invalid 进入领取期标志
                                inputData.setPayPeriodFlag(new BigDecimal(ClaimConstant.YES));
                                //@invalid 领取金额
                                inputData.setPayAmount(inputData.getPayAmount().add(queryPayDueVO.getFeeAmount()));
                                count++;
                            }
                        }
                        if(count==1){
                            //@invalid 是否第一次进入领取期
                            inputData.setFirstPeriodFlag(new BigDecimal(ClaimConstant.YES));
                        }
				    }
                        
				    
				}
				//@invalid 最近一次的红利分红日
				if (claimQueryPolicyInfoResVO != null
						&& claimQueryPolicyInfoResVO.getCliamMap() != null
						&& claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.LAST_ALLOCATE_DATE) != null) {
					inputData.setLatestBonusDate(DateUtilsEx.toDate(claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.LAST_ALLOCATE_DATE), "yyyy-MM-dd"));
				} else {
					inputData.setLatestBonusDate(contractProductBO.getValidateDate());
				}
				if(new BigDecimal(ClaimConstant.NO).equals(contractProductBO.getIsMasterItem())){
					//@invalid k年f个月，前f个月累计追加可选责任保额
					inputData.setTopUpSA(new BigDecimal(claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.TOP_UP_SA)));
					
					//@invalid 新增可选责任呢标志
					//@invalid 计算可选责任首次新增日期所在保单年度
					int year1 = (int)DateUtilsEx.getYearAmount(contractProductBO.getValidateDate(), DateUtilsEx.toDate(claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.ADD_OPTLIAB_DATE)));
					//@invalid 计算理赔日期所在保单年度
					int year2 = (int)DateUtilsEx.getYearAmount(contractProductBO.getValidateDate(), dateFlag);
					if(year1==year2){
						//@invalid  可选责任标志（包含新契约新增）1.新增0.未新增
						inputData.setAddOptLiabFlag(1);
					}else{
						inputData.setAddOptLiabFlag(0);
					}
				}
				//@invalid 保单结算现价接口没传参数
				//@invalid 保险金给付发生标志
				inputData.setInsurancePaidFlag(0);
				//@invalid 是否已赔付重度恶性肿瘤确诊保险金
				if(contractProductBO.getProductCode().equals(ClaimConstant.CLM_PRODCODE_EIGHTEIGHTSEVEN)){
					ClaimLiabPO claimLiabPO = new ClaimLiabPO();
					claimLiabPO.setItemId(contractProductBO.getItemId());
					claimLiabPO.getData().put("liab_id_list", ClaimConstant.CLM_PRODCODE_EIGHTZERROSEVENTHREE);
					claimLiabPO = claimLiabDao.findLiabSumActualPay(claimLiabPO);
					if(claimLiabPO.getActualPay() != null && claimLiabPO.getActualPay().compareTo(ClaimConstant.BIGDECIMAL_ZERO) > 0){
						inputData.setInsuranceSeriousPaidFlag(ClaimConstant.BIGDECIMAL_ONE.intValue());
					} else {
						inputData.setInsuranceSeriousPaidFlag(ClaimConstant.BIGDECIMAL_ZERO.intValue());
					}
				} else {
					inputData.setInsuranceSeriousPaidFlag(ClaimConstant.BIGDECIMAL_ZERO.intValue());
				}
				//@invalid T_BUSINESS_PRODUCT字段WAIVER_FLAG=1时传值投保人性别、投保人年龄、每期豁免保费
				if(businessProductPO.getWaiverFlag().compareTo(ClaimConstant.BIGDECIMAL_ONE)==ClaimConstant.ZERO){
					//@invalid  查询投保人
					PolicyHolderPO policyHolderPO = new PolicyHolderPO();
					policyHolderPO.setCaseId(claimSubCaseBO.getCaseId());
					policyHolderPO.setPolicyCode(contractProductBO.getPolicyCode());
					policyHolderPO.setCurFlag(ClaimConstant.CURFLAG_ONE);
					policyHolderPO = policyHolderDao.findPolicyHolderAge(policyHolderPO);
					//@invalid 投保人性别
					if(policyHolderPO.getData().get("customer_gender")!=null){
						BigDecimal gender = (BigDecimal)policyHolderPO.getData().get("customer_gender");
						inputData.setAppntGender(gender.intValue());
					}
					Integer appntAge = null;
					if(policyHolderPO.getData().get("customer_birthday") != null){
						appntAge = CommonUtil.getAge((Date)policyHolderPO.getData().get("customer_birthday"), contractProductBO.getValidateDate());
					}
					if(appntAge!=null){
						//@invalid 投保人年龄
						inputData.setAppntAge(appntAge);
					}
					//@invalid 查询该保单下所有责任组
					ContractProductPO contractProduct = new ContractProductPO();
					contractProduct.setCaseId(claimSubCaseBO.getCaseId());
					contractProduct.setPolicyCode(contractProductBO.getPolicyCode());
					contractProduct.setCurFlag(ClaimConstant.CURFLAG_ONE);
					List<ContractProductPO> contractProductPOList = contractProductDao.findAllContractProduct(contractProduct);
					//@invalid 每期豁免保费：本保单下此险种附加的主险及其他长期附加险的每期保费的和
					if(ClaimConstant.BIGDECIMAL_ONE.equals(contractBusiProdPO.getWaiver())){
					    inputData.setExemptPremium(contractProductBO.getAmount());
					}
					InsuredListPO insuredListPo = new InsuredListPO();
					insuredListPo.setCaseId(claimSubCaseBO.getCaseId());
					insuredListPo.setPolicyCode(contractProductBO.getPolicyCode());
					insuredListPo.setCurFlag(ClaimConstant.CURFLAG_ONE);
					List<InsuredListPO> insuredList = insuredListDao.findAllInsuredList(insuredListPo);
					if(ClaimConstant.CLM_BUSI_PRODCODE_FOURSIXEIGHT.equals(contractProductBO.getBusiProdCode()) && StringUtils.isNotBlank(insuredList.get(0).getInsuredBodyOption())) {
						inputData.setInsuredBodyOption(Integer.parseInt(insuredList.get(0).getInsuredBodyOption()));
					}
				}
				InsuredListPO insuredListPo = new InsuredListPO();
				insuredListPo.setCaseId(claimSubCaseBO.getCaseId());
				insuredListPo.setPolicyCode(contractProductBO.getPolicyCode());
				insuredListPo.setCurFlag(ClaimConstant.CURFLAG_ONE);
				List<InsuredListPO> insuredList = insuredListDao.findAllInsuredList(insuredListPo);
				if(insuredList.size()>=2){
					inputData.setIsMoreInsured(Integer.parseInt("1"));
				      InsuredListPO youngPerson = Collections.min(insuredList, new Comparator<InsuredListPO>() {
				            @Override
				            public int compare(InsuredListPO o1, InsuredListPO o2) {
				                return Integer.compare(o1.getInsuredAge().intValue(), o2.getInsuredAge().intValue());
				            }
				        });
				      inputData.setYoungerAge(Integer.parseInt(youngPerson.getInsuredAge().toString()));
						CustomerPO customerPO = new CustomerPO();
						customerPO.setCustomerId(youngPerson.getCustomerId());
						customerPO = customerDao.findCustomerByCustomerId(customerPO);
				      inputData.setYoungerGender(Integer.parseInt(customerPO.getCustomerGender().toString()));
				      InsuredListPO OldPerson = Collections.max(insuredList, new Comparator<InsuredListPO>() {
				            @Override
				            public int compare(InsuredListPO o1, InsuredListPO o2) {
				                return Integer.compare(o1.getInsuredAge().intValue(), o2.getInsuredAge().intValue());
				            }
				        });
				       inputData.setOlderAge(Integer.parseInt(OldPerson.getInsuredAge().toString()));
						CustomerPO customerPO2 = new CustomerPO();
						customerPO2.setCustomerId(OldPerson.getCustomerId());
						customerPO2 = customerDao.findCustomerByCustomerId(customerPO2);
				       inputData.setOlderGender(Integer.parseInt(customerPO2.getCustomerGender().toString()));
				}else {
					inputData.setIsMoreInsured(Integer.parseInt("0"));
				}
				ContractProductOtherPO contractProductOtherPO = new ContractProductOtherPO();					
				contractProductOtherPO.setCaseId(claimSubCaseBO.getCaseId());
				contractProductOtherPO.setItemId(contractProductBO.getItemId());
				contractProductOtherPO.setCurFlag(ClaimConstant.FLAG_CLAIM_DATE);
				contractProductOtherPO.setCopyDate(claimSubCaseBO.getClaimDate());
				List<ContractProductOtherPO> contractProductOtherPOs = contractProductOtherDao.findAllContractProductOther(contractProductOtherPO);
				if(contractProductOtherPOs.size() > 0){
					contractProductOtherPO = contractProductOtherPOs.get(0);
				}
				if(contractProductOtherPO.getAnnuityStartPayDay()!=null){
					inputData.setAnnuityStartPayDay(Integer.valueOf(contractProductOtherPO.getAnnuityStartPayDay()));
				}
				ClaimLiabPO claimLiabPO =new ClaimLiabPO();
				claimLiabPO.setPolicyCode(contractProductBO.getPolicyCode());
				claimLiabPO.setBusiItemId(contractProductBO.getBusiItemId());
				ClaimLiabPO claimLiabPO2=claimLiabDao.findActualTotalByPolicycode(claimLiabPO);
				if(claimLiabPO2.getData().get("actualpaytotal")!=null){
	                inputData.setPreClaimsPaymentTotal(new BigDecimal(claimLiabPO2.getData().get("actualpaytotal").toString()));
				}else {
				    inputData.setPreClaimsPaymentTotal(ClaimConstant.BIGDECIMAL_ZERO);
                }
                if (contractProductOtherPO.getField9()!=null) {
                    inputData.setPayPeriodYear(Integer.valueOf(contractProductOtherPO.getField9()));
                }
                if (contractProductOtherPO.getDeathBenefitPlan()!=null) {
                    inputData.setDeathPlanType(contractProductOtherPO.getDeathBenefitPlan());
                }
				logger.debug("*************************现金价值调产品接口入参"+XmlHelper.classToXml(inputData)+"*************************");
 				//12 调用计算险种现金价值接口方法，查询现金价值信息
				MmsCalcCashValueResVO outputData = cLMServiceUtil.prdimmscalccashvalueucccalcCashValue(inputData);
 				logger.debug("*************************现金价值调产品接口出参"+XmlHelper.classToXml(outputData)+"*************************");
				CalcCashValueBO calcCashValueBO = null;
				if (outputData != null) {
					calcCashValueBO = BeanUtils.copyProperties(CalcCashValueBO.class, outputData);
				}
				return calcCashValueBO;
			}
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("查询现金价值失败");
			throw new BizException("系统异常");
		}
	}

	/**
	 * 计算终了红利
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractProductBO 责任组详细信息
	 * @param claimSubCaseBO 子赔案信息
	 * @param flag 是否给付累计红利保额对应的终了红利
	 * @return 终了红利结果
	 */
	public BigDecimal calcTBAmount(ContractProductBO contractProductBO, ClaimSubCaseBO claimSubCaseBO,boolean flag){
		try {
			//1 调用查询终了红利标识接口查询终了红利标识
			com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagReqVO inputDataTBFlag = new com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagReqVO();
			List<BigDecimal> productIdList = new ArrayList<BigDecimal>();
			productIdList.add(contractProductBO.getProductId());
			inputDataTBFlag.setProductId(productIdList);
			com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagResVO outputDataTBFlag = cLMServiceUtil
					.prdiprdquerytbflaguccqueryTBFlag(inputDataTBFlag);
			
			if (outputDataTBFlag != null&& outputDataTBFlag.getPrdQueryTBFlagInfoResVOList().size() != 0
					&& outputDataTBFlag.getPrdQueryTBFlagInfoResVOList().get(0).getTerminalBonusFlag() != null
					&& ClaimConstant.YES.equals(outputDataTBFlag.getPrdQueryTBFlagInfoResVOList().get(0).getTerminalBonusFlag().trim())) {
				//2 查询保单信息(T_CONTRACT_MASTER)
				ContractMasterPO contractMasterPO = new ContractMasterPO();
				contractMasterPO.setPolicyCode(contractProductBO
						.getPolicyCode());
				contractMasterPO.setCaseId(claimSubCaseBO.getCaseId());
				contractMasterPO.setCurFlag(new BigDecimal(1));
				contractMasterPO = contractMasterDao
						.findContractMasterByCaseIdAndPolicyCode(contractMasterPO);
				//3 查询险种信息(T_CONTRACT_BUSI_PROD)
				ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
				contractBusiProdPO.setCaseId(claimSubCaseBO.getCaseId());
				contractBusiProdPO.setBusiItemId(contractProductBO
						.getBusiItemId());
				contractBusiProdPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
				contractBusiProdPO = contractBusiProdDao
						.findAllContractBusiProd(contractBusiProdPO).get(0);
				//4 查询险种定义表信息(T_BUSINESS_PRODUCT)
			 	BusinessProductPO businessProductPO = new BusinessProductPO();
			 	businessProductPO.setProductCodeSys(contractBusiProdPO.getBusiProdCode());
			 	businessProductPO = businessProductDao.findAllBusinessProduct(businessProductPO).get(0);
				//5 查询主险种被保人列表信息(T_BENEFIT_INSURED)
				BenefitInsuredPO benefitInsuredPO = new BenefitInsuredPO();
				benefitInsuredPO.setBusiItemId(contractProductBO
						.getBusiItemId());
				benefitInsuredPO.setCaseId(claimSubCaseBO.getCaseId());
				benefitInsuredPO.setCurFlag(new BigDecimal(1));
				List<BenefitInsuredPO> benefitInsuredPOs = benefitInsuredDao
						.findAllBenefitInsured(benefitInsuredPO);
				//6 查询责任是否为基本责任(T_PRODUCT_LIFE)
				ProductLifePO productLifePO = new ProductLifePO();
				productLifePO.setProductId(contractProductBO.getProductId());
				productLifePO = productLifeDao
						.findProductLifeByProductId(productLifePO);
				//7 调用查询生存给付应领记录接口查询生存给付应领记录
				QuerPayDueReqVO payDueinputData = new QuerPayDueReqVO();
				QuerPayDueReqVOParam querPayDueReqVOParam=new QuerPayDueReqVOParam();
				querPayDueReqVOParam.setBusiProdCode(contractBusiProdPO.getBusiProdCode());
				querPayDueReqVOParam.setEndDate(claimSubCaseBO.getClaimDate());
				querPayDueReqVOParam.setPolicyCode(contractProductBO.getPolicyCode());
				querPayDueReqVOParam.setProductCode(contractProductBO
						.getProductCode()); 
				List<QuerPayDueReqVOParam> querPayDueReqVOParamList=new ArrayList<QuerPayDueReqVOParam>();
				querPayDueReqVOParamList.add(querPayDueReqVOParam);
				payDueinputData.setQuerPayDueReqVOParam( querPayDueReqVOParamList);
				 
				QueryPayDueResVO paiquerypaydueuccqueryPayDue = cLMServiceUtil
						.paiquerypaydueuccqueryPayDue(payDueinputData);
				List<QueryPayDueVO> queryPayDueVOList = null;
				if (paiquerypaydueuccqueryPayDue != null&&paiquerypaydueuccqueryPayDue.getQueryPayDueResVOParam()!=null) {
					queryPayDueVOList = paiquerypaydueuccqueryPayDue.getQueryPayDueResVOParam().get(0).getQueryPayDueVOList();
				}
				//8 查询保单是否在第一次领取日前，查询上次分红日
				com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO claimQueryPolicyInfoVO = 
				        new com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO();
				claimQueryPolicyInfoVO.setBusiItemId(contractProductBO.getBusiItemId());
				claimQueryPolicyInfoVO.setClaimDate(claimSubCaseBO.getClaimDate());
				claimQueryPolicyInfoVO.setItemId(contractProductBO.getItemId());
				claimQueryPolicyInfoVO.setPolicyId(contractProductBO.getPolicyId());
				List<String> typeFlagList = new ArrayList<String>();
				typeFlagList.add(ClaimConstant.IS_STAR_ANNUITY);
				typeFlagList.add(ClaimConstant.LAST_ALLOCATE_DATE);
				claimQueryPolicyInfoVO.setTypeFlagList(typeFlagList);
				com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoResVO claimQueryPolicyInfoResVO = cLMServiceUtil
						.paiclaimquerypolicyinfouccqueryPolicyInfo(claimQueryPolicyInfoVO);
				//9 调用计算累积红利信息接口查询信息
				PrdQueryBonusInfoReqVO inputDataRate = new PrdQueryBonusInfoReqVO();
				inputDataRate
						.setBusinessPrdId(businessProductPO.getBusinessPrdId());
				inputDataRate.setRateType(new BigDecimal(0));
				inputDataRate.setStartDate(claimSubCaseBO.getClaimDate());
				inputDataRate.setChargeYear(contractProductBO.getChargeYear());
				inputDataRate.setOptionLiab(new BigDecimal(productLifePO
						.getOptionType()));
				inputDataRate
						.setProductCode(contractProductBO.getProductCode());
				inputDataRate.setBasicChargeYear(contractProductBO
						.getChargeYear());
				inputDataRate.setPolicyYear(new BigDecimal(
						(int) DateUtilsEx.getYearAmount(
								contractProductBO.getValidateDate(),
								claimSubCaseBO.getClaimDate())));
				inputDataRate.setProductId(contractProductBO.getProductId());
				inputDataRate.setSysDate(WorkDateUtil.getWorkDate());
				inputDataRate.setAccidentDate(claimSubCaseBO.getClaimDate());
				inputDataRate.setRenewalFlag(Integer
						.parseInt(ClaimConstant.YES));
				if (contractBusiProdPO.getRenewDecision() == null
						|| !contractBusiProdPO.getRenewDecision().equals(
								ClaimConstant.DECISION_DESC_ALREADY)) { 
					inputDataRate.setRenewalFlag(Integer
							.parseInt(ClaimConstant.NO));
				}
				inputDataRate.setPayPeriodFlag(new BigDecimal(ClaimConstant.NO));
				inputDataRate.setFirstPeriodFlag(new BigDecimal(ClaimConstant.NO));
				if (queryPayDueVOList != null && queryPayDueVOList.size() != 0) {
				    int count = 0;
				    if(ClaimConstant.PAY_TYPE_FORE.equals(queryPayDueVOList.get(0).getPlanFreq())){
				      //@invalid 月领
				        for(QueryPayDueVO queryPayDueVO : queryPayDueVOList){
	                        if(ClaimConstant.LIAB_ID_1106.equals(queryPayDueVO.getLiabId())){
	                            inputDataRate.setPayPeriodFlag(new BigDecimal(ClaimConstant.YES));
	                            count++;
	                        }
	                    }
	                    if (count <= 12) {
	                        inputDataRate.setFirstPeriodFlag(new BigDecimal(ClaimConstant.YES));
	                    }
				    } else {
				      //@invalid 其他领取方式
				        for(QueryPayDueVO queryPayDueVO : queryPayDueVOList){
	                        if(ClaimConstant.LIAB_ID_1106.equals(queryPayDueVO.getLiabId())){
	                            inputDataRate.setPayPeriodFlag(new BigDecimal(ClaimConstant.YES));
	                            count++;
	                        }
	                    }
	                    if (count == 1) {
	                        inputDataRate.setFirstPeriodFlag(new BigDecimal(ClaimConstant.YES));
	                    }
				    }
	                      
				    
				}
				logger.debug(XmlHelper.classToXml(inputDataRate));
				PrdQueryBonusInfoResVO outputDataRate = cLMServiceUtil.prdiprdquerybonusinfouccfindBonusInfo(inputDataRate);
				inputDataRate.setRateType(new BigDecimal(2));
				logger.debug(XmlHelper.classToXml(inputDataRate));
				PrdQueryBonusInfoResVO tbRate = cLMServiceUtil.prdiprdquerybonusinfouccfindBonusInfo(inputDataRate);
                //10 设置终了红利计算接口查询入参对象
				com.nci.tunan.mms.interfaces.calc.exports.calctbamount.vo.MmsCalcTBAmountReqVO inputDataTB = new com.nci.tunan.mms.interfaces.calc.exports.calctbamount.vo.MmsCalcTBAmountReqVO();
				//@invalid 主险的产品id
				inputDataTB.setProductId(contractProductBO.getProductId());
				//@invalid 事故发生日
				inputDataTB.setAccidentDate(claimSubCaseBO.getClaimDate());
				//@invalid  可选责任生效日
				inputDataTB.setOptLiabEffectDate(contractProductBO
						.getValidateDate());
				//@invalid 可选责任终止日
				inputDataTB.setOptLiabTerminalDate(claimSubCaseBO
						.getClaimDate());
				//@invalid 保单年度
				inputDataTB.setPolicyMonth(new BigDecimal(DateUtilsEx
						.getMonthAmount(
								contractProductBO.getValidateDate(),
								claimSubCaseBO.getClaimDate())));
				//@invalid 保单生效日
				inputDataTB.setPolicyEffectiveDate(contractProductBO.getValidateDate());
				//@invalid 系统计算的基本保险金额
				inputDataTB.setSA(contractProductBO.getAmount());
				//@invalid  系统记录的红利保额
				if(claimQueryPolicyInfoResVO!=null&&claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.BONUS_SA)!=null){
					inputDataTB.setBonusSA(new BigDecimal(claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.BONUS_SA)));
				}else if (contractProductBO.getBonusSa()!= null) {
					inputDataTB.setBonusSA(contractProductBO.getBonusSa());
				}else {
					inputDataTB.setBonusSA(new BigDecimal(ClaimConstant.ZERO));
				}
				//@invalid 投保人年龄 
				InsuredListPO insuredListPO = new InsuredListPO();
				insuredListPO.setCaseId(claimSubCaseBO.getCaseId());
				insuredListPO
						.setListId(benefitInsuredPOs.get(0).getInsuredId());
				insuredListPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
				List<InsuredListPO> insuredListPOList = insuredListDao
						.findAllInsuredList(insuredListPO);
				inputDataTB.setAge(insuredListPOList.get(0).getInsuredAge());
				//@invalid 保额红利利率
				inputDataTB.setBonusRate(outputDataRate.getBonusRate());
				//@invalid 保额红利利率
				inputDataTB.setBonusRate1(outputDataRate.getBonusRate());
				//@invalid 红利公布日
				inputDataTB.setBonusAnnounceDate(outputDataRate
						.getRateReleaseDate());
				//@invalid 约定领取年龄
				if (contractProductBO.getPayYear() != null) {
					inputDataTB.setPayYear(contractProductBO.getPayYear()
							.intValue());
				}
				//@invalid 交费期间
				inputDataTB.setChargeYear(contractProductBO.getChargeYear()
						.intValue());
				//@invalid 是否基本责任标识
				inputDataTB.setIsBiscLiabFlag(Integer.parseInt(productLifePO
						.getOptionType()));
				//@invalid 是否计算红利保额
				inputDataTB.setIsHasBonusSA(BigDecimal
						.valueOf(ClaimConstant.ONE));
				//@invalid 进入领取期标志
				inputDataTB.setPayPeriodFlag(new BigDecimal(0));
				inputDataTB.setFirstPeriodFlag(new BigDecimal(0));
				if (queryPayDueVOList != null && queryPayDueVOList.size() != 0) {
					//@invalid 进入领取期标志
				    int count = 0;
				  //@invalid 判断领取方式，如果月领，首年内的值都传1，如果是年领的首年传1.
				    if(ClaimConstant.PAY_TYPE_FORE.equals(queryPayDueVOList.get(0).getPlanFreq())){
				      //@invalid 月领
				        for(QueryPayDueVO queryPayDueVO : queryPayDueVOList){
	                        
	                        if(ClaimConstant.LIAB_ID_1106.equals(queryPayDueVO.getLiabId())){
	                            inputDataTB.setPayPeriodFlag(new BigDecimal(ClaimConstant.YES));
	                            count++;
	                        }
	                    }
	                    if (count <= 12) {
	                        inputDataTB.setFirstPeriodFlag(new BigDecimal(ClaimConstant.YES));
	                    }
				        
                    } else {
                      //@invalid 其他领取方式
                        for(QueryPayDueVO queryPayDueVO : queryPayDueVOList){
                            
                            if(ClaimConstant.LIAB_ID_1106.equals(queryPayDueVO.getLiabId())){
                                inputDataTB.setPayPeriodFlag(new BigDecimal(ClaimConstant.YES));
                                count++;
                            }
                        }
                        if (count == 1) {
                            inputDataTB.setFirstPeriodFlag(new BigDecimal(ClaimConstant.YES));
                        }
                    }
                    
				}
				//@invalid 基本责任交费期间
				inputDataTB.setBasicChargeYear(contractProductBO
						.getChargeYear().intValue());
				//@invalid 终了红利率           
				inputDataTB.setTBRate(tbRate.getTBRate());
				//@invalid 续保标志
				inputDataTB.setRenewalFlag(1);
				if (contractBusiProdPO.getRenewDecision() == null
						|| !contractBusiProdPO.getRenewDecision().equals(
								ClaimConstant.DECISION_DESC_ALREADY)) {
					inputDataTB.setRenewalFlag(0);
				}
				//@invalid 最近一次的红利分红日
				if (claimQueryPolicyInfoResVO != null
						&& claimQueryPolicyInfoResVO.getCliamMap() != null
						&& claimQueryPolicyInfoResVO.getCliamMap().get(
								ClaimConstant.LAST_ALLOCATE_DATE) != null) {
					inputDataTB.setLatestBonusDate(DateUtilsEx.toDate(claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.LAST_ALLOCATE_DATE), "yyyy-MM-dd"));
				} else {
					inputDataTB.setLatestBonusDate(contractProductBO.getValidateDate());
				}
				
				if(flag){
					inputDataTB.setIsHasBonusSA(new BigDecimal(ClaimConstant.YES));
				}else{
					inputDataTB.setIsHasBonusSA(new BigDecimal(ClaimConstant.NO));
				}
				inputDataTB.setSystemDate(WorkDateUtil.getWorkDate());
				logger.debug(XmlHelper.classToXml(inputDataTB));
				//11 调用终了红利计算接口查询终了红利
				com.nci.tunan.mms.interfaces.calc.exports.calctbamount.vo.MmsCalcTBAmountResVO outputDataTB = cLMServiceUtil
						.prdimmscalctbamountucccalcTBAmount(inputDataTB);
				if (outputDataTB != null && outputDataTB.getTBAmount() != null) {
					return outputDataTB.getTBAmount();
				} else {
					return null;
				}
			}
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("查询终了红利失败");
			throw new BizException("系统异常");
		}
	}

	/**
	 * 查询累积红利保额
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractProductBO 赔案险种信息
	 * @param claimSubCaseBO 赔案子信息
	 * @return 累积红利保额
	 */
	public BigDecimal queryBonusAmount(ContractProductBO contractProductBO, ClaimSubCaseBO claimSubCaseBO){
		try {
			//1 调用查询终了红利标识接口查询终了红利标识
			com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagReqVO inputDataTBFlag = new com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagReqVO();
			List<BigDecimal> productIdList = new ArrayList<BigDecimal>();
			productIdList.add(contractProductBO.getProductId());
			inputDataTBFlag.setProductId(productIdList);
			com.nci.tunan.prd.interfaces.query.exports.querytbflag.vo.PrdQueryTBFlagResVO outputDataTBFlag = cLMServiceUtil
					.prdiprdquerytbflaguccqueryTBFlag(inputDataTBFlag);
			if (outputDataTBFlag != null&& outputDataTBFlag.getPrdQueryTBFlagInfoResVOList().size() != 0
					&& outputDataTBFlag.getPrdQueryTBFlagInfoResVOList().get(0).getTerminalBonusFlag() != null
					&& ClaimConstant.YES.equals(outputDataTBFlag.getPrdQueryTBFlagInfoResVOList().get(0).getTerminalBonusFlag().trim())) {
				//2 据入参对象查询赔案(T_CLAIM_CASE)
				ClaimCasePO claimCasePO = new ClaimCasePO();
				claimCasePO.setCaseId(claimSubCaseBO.getCaseId());
				claimCasePO = claimCaseDao.findClaimCaseByCaseId(claimCasePO);
				//3 据入参查询保单信息(T_CONTRACT_MASTER)
				ContractMasterPO contractMasterPO = new ContractMasterPO();
				contractMasterPO.setPolicyCode(contractProductBO.getPolicyCode());
				contractMasterPO.setCaseId(claimSubCaseBO.getCaseId());
				contractMasterPO.setCurFlag(new BigDecimal(1));
				contractMasterPO = contractMasterDao.findContractMasterByCaseIdAndPolicyCode(contractMasterPO);
				//4 查询险种信息(T_CONTRACT_BUSI_PROD)
				ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
				contractBusiProdPO.setCaseId(claimSubCaseBO.getCaseId());
				contractBusiProdPO.setBusiItemId(contractProductBO.getBusiItemId());
				contractBusiProdPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
				contractBusiProdPO = contractBusiProdDao.findAllContractBusiProd(contractBusiProdPO).get(0);
				//5 查询主险种被保人列表信息(T_BENEFIT_INSURED)
				BenefitInsuredPO benefitInsuredPO = new BenefitInsuredPO();
				benefitInsuredPO.setBusiItemId(contractProductBO.getBusiItemId());
				benefitInsuredPO.setCaseId(claimSubCaseBO.getCaseId());
				benefitInsuredPO.setCurFlag(new BigDecimal(1));
				List<BenefitInsuredPO> benefitInsuredPOs = benefitInsuredDao.findBenefitInsuredSex(benefitInsuredPO);
				//6 查询责任是否为基本责任
				ProductLifePO productLifePO = new ProductLifePO();
				productLifePO.setProductId(contractProductBO.getProductId());
				productLifePO = productLifeDao.findProductLifeByProductId(productLifePO);
				//7 根据主险的被保人查询对应保单层信息(T_INSURED_LIST)
				InsuredListPO insuredListPO = new InsuredListPO();
				insuredListPO.setCaseId(claimSubCaseBO.getCaseId());
				insuredListPO.setListId(benefitInsuredPOs.get(0).getInsuredId());
				insuredListPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
				List<InsuredListPO> insuredListPOList = insuredListDao.findAllInsuredList(insuredListPO);
				//8 查询保单是否在第一次领取日前，查询上次分红日
				com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO claimQueryPolicyInfoVO = 
				        new com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO();
				claimQueryPolicyInfoVO.setBusiItemId(contractProductBO.getBusiItemId());
				claimQueryPolicyInfoVO.setClaimDate(claimSubCaseBO.getClaimDate());
				claimQueryPolicyInfoVO.setItemId(contractProductBO.getItemId());
				claimQueryPolicyInfoVO.setPolicyId(contractProductBO.getPolicyId());
				List<String> typeFlagList = new ArrayList<String>();
				typeFlagList.add(ClaimConstant.IS_STAR_ANNUITY);
				typeFlagList.add(ClaimConstant.LAST_ALLOCATE_DATE);
				claimQueryPolicyInfoVO.setTypeFlagList(typeFlagList);
				com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoResVO  claimQueryPolicyInfoResVO= 
				        cLMServiceUtil.paiclaimquerypolicyinfouccqueryPolicyInfo(claimQueryPolicyInfoVO);
				//9 查询生存给付应领记录
                
                QuerPayDueReqVO payDueinputData = new QuerPayDueReqVO();
                QuerPayDueReqVOParam querPayDueReqVOParam=new QuerPayDueReqVOParam();
                querPayDueReqVOParam.setBusiProdCode(contractBusiProdPO.getBusiProdCode());
                querPayDueReqVOParam.setEndDate(claimSubCaseBO.getClaimDate());
                querPayDueReqVOParam.setPolicyCode(contractProductBO.getPolicyCode());
                querPayDueReqVOParam.setProductCode(contractProductBO
                        .getProductCode()); 
                List<QuerPayDueReqVOParam> querPayDueReqVOParamList=new ArrayList<QuerPayDueReqVOParam>();
                querPayDueReqVOParamList.add(querPayDueReqVOParam);
                payDueinputData.setQuerPayDueReqVOParam( querPayDueReqVOParamList);
                 
                QueryPayDueResVO paiquerypaydueuccqueryPayDue = cLMServiceUtil
                        .paiquerypaydueuccqueryPayDue(payDueinputData);
                List<QueryPayDueVO> queryPayDueVOList = null;
                if (paiquerypaydueuccqueryPayDue != null&&paiquerypaydueuccqueryPayDue.getQueryPayDueResVOParam()!=null) {
                    queryPayDueVOList = paiquerypaydueuccqueryPayDue.getQueryPayDueResVOParam().get(0).getQueryPayDueVOList();
                }
				//10 设置计算累计红利保额接口入参
				com.nci.tunan.mms.interfaces.calc.exports.calcbonusamount.vo.MmsCalcBonusAmountReqVO inputData = 
				        new com.nci.tunan.mms.interfaces.calc.exports.calcbonusamount.vo.MmsCalcBonusAmountReqVO();
				//@invalid 投保人年龄
				inputData.setAge(insuredListPOList.get(0).getInsuredAge().intValue());
				//@invalid 红利保额
				if(claimQueryPolicyInfoResVO!=null&&claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.BONUS_SA)!=null){
				    inputData.setBonusSA(new BigDecimal(claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.BONUS_SA)));
				}else if(contractProductBO.getBonusSa()!=null){
					inputData.setBonusSA(contractProductBO.getBonusSa());
				}else{
					inputData.setBonusSA(BigDecimal.ZERO);
				}
				//@invalid 第t-1个保单年度末的基本保险金额
				inputData.setSA(contractProductBO.getAmount());
				//@invalid productId : 责任组编码
				inputData.setProductId(contractProductBO.getProductId());
				//@invalid policyMonth : 保单年度月数
				inputData.setPolicyMonth(new BigDecimal(DateUtilsEx.getMonthAmount(
						contractProductBO.getValidateDate(),
						claimSubCaseBO.getClaimDate())));
				//@invalid payYear : 约定领取年龄
				if (contractProductBO.getPayYear() != null) {
					inputData.setPayYear(contractProductBO.getPayYear().intValue());
				}
				//@invalid policyEffectiveDate : 保单生效日
				inputData.setPolicyEffectiveDate(contractProductBO.getValidateDate());
				//@invalid accidentDate : 事故发生日
				inputData.setAccidentDate(claimSubCaseBO.getClaimDate());
				//@invalid optLiabEffectDate : 可选责任生效日
				inputData.setOptLiabEffectDate(contractProductBO.getValidateDate());
				//@invalid optLiabTerminalDate : 可选责任终止日
				inputData.setOptLiabTerminalDate(claimSubCaseBO.getClaimDate());
				//@invalid isWholePolicyFlag : 是否整年度标识（0-是 1-否）
				inputData.setIsWholePolicyFlag(0);
				//@invalid isBiscLiabFlag : 是否基本责任标识
				inputData.setIsBiscLiabFlag(Integer.parseInt(productLifePO.getOptionType()));
				
				//@invalid chargeYear: 交费期间 
				inputData.setChargeYear(contractProductBO.getChargeYear().intValue());
				//@invalid firstPeriodFlag: 是否刚进去第一次领取 &&payPeriodFlag: 进入领取期标志
				inputData.setFirstPeriodFlag(BigDecimal.ZERO);
                inputData.setPayPeriodFlag(BigDecimal.ZERO);
				if(queryPayDueVOList != null && queryPayDueVOList.size() != 0){
					//@invalid 进入领取期标志
                    int count = 0;
                    if(ClaimConstant.PAY_TYPE_FORE.equals(queryPayDueVOList.get(0).getPlanFreq())){
                      //@invalid 月领
                        for(QueryPayDueVO queryPayDueVO : queryPayDueVOList){
                            if(ClaimConstant.LIAB_ID_1106.equals(queryPayDueVO.getLiabId())){
                                inputData.setPayPeriodFlag(new BigDecimal(ClaimConstant.YES));
                                count++;
                            }
                        }
                        if (count <= 12) {
                            inputData.setFirstPeriodFlag(new BigDecimal(ClaimConstant.YES));
                        }
                    } else {
                      //@invalid 其他领取方式
                        for(QueryPayDueVO queryPayDueVO : queryPayDueVOList){
                            if(ClaimConstant.LIAB_ID_1106.equals(queryPayDueVO.getLiabId())){
                                inputData.setPayPeriodFlag(new BigDecimal(ClaimConstant.YES));
                                count++;
                            }
                        }
                        if (count == 1) {
                            inputData.setFirstPeriodFlag(new BigDecimal(ClaimConstant.YES));
                        }
                    }
                        
                        
                    
				}
				//@invalid basicChargeYear:　基本责任领取区间 
				inputData.setBasicChargeYear(contractProductBO.getChargeYear().intValue());
				//@invalid renewalFlag : 续保标志 
				inputData.setRenewalFlag(Integer.parseInt(ClaimConstant.YES));
				if (contractBusiProdPO.getRenewDecision() == null
						|| !contractBusiProdPO.getRenewDecision().equals(
								ClaimConstant.DECISION_DESC_ALREADY)) {
					inputData.setRenewalFlag(Integer.parseInt(ClaimConstant.NO));
				}
				//@invalid latestBonusDate : 最近一次年度分红对应的保单生效对应日，如未进行过分红则为生效日
				if(claimQueryPolicyInfoResVO!=null&&claimQueryPolicyInfoResVO.getCliamMap()!=null&&claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.LAST_ALLOCATE_DATE)!=null){
					inputData.setLatestBonusDate(DateUtilsEx.toDate(claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.LAST_ALLOCATE_DATE), "yyyy-MM-dd"));
				}else{
					inputData.setLatestBonusDate(contractProductBO.getValidateDate());
				}
				if(claimCasePO.getRegisteConfTime() != null){
					inputData.setSystemDate(claimCasePO.getRegisteConfTime());
				} else {
					inputData.setSystemDate(claimCasePO.getRegisteTime());
				}
				logger.debug("******************匹配理算处理数据查询年度红利入参******************");
				logger.debug(XmlHelper.classToXml(inputData));
				//11 调用计算红利相关金额接口查询累积红利保额
				com.nci.tunan.mms.interfaces.calc.exports.calcbonusamount.vo.MmsCalcBonusAmountResVO outputData =  cLMServiceUtil.prdimmscalcbonusamountucccalcBonusAmount(inputData);
				if(outputData!=null && outputData.getBasicBonusCoverage() != null && BigDecimal.ZERO.compareTo(outputData.getBasicBonusCoverage())!=0){
					BigDecimal bounsAmount = outputData.getBasicBonusCoverage().subtract(contractProductBO.getAmount());
					return bounsAmount;
				}
			}
			return null;
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("查询累计红利保额失败");
			throw new BizException("系统异常");
		}
	}

	/**
	 * 现金分红计算
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractProductBOList 赔案险种信息
	 * @param claimSubCaseBO 赔案子信息
	 * @return MmsCalcCashBonusListResVO 分红信息
	 */
	public com.nci.tunan.mms.interfaces.calc.exports.calccashbonus.vo.MmsCalcCashBonusListResVO calcCashBonusList(List<ContractProductBO> contractProductBOList, ClaimSubCaseBO claimSubCaseBO){
		com.nci.tunan.mms.interfaces.calc.exports.calccashbonus.vo.MmsCalcCashBonusListResVO outputData;
		try {
			//1 查询保单信息(T_CONTRACT_MASTER)
			ContractMasterPO contractMasterPO = new ContractMasterPO();
			contractMasterPO.setPolicyCode(contractProductBOList.get(0)
					.getPolicyCode());
			contractMasterPO.setCaseId(claimSubCaseBO.getCaseId());
			contractMasterPO.setCurFlag(new BigDecimal(1));
			contractMasterPO = contractMasterDao
					.findContractMasterByCaseIdAndPolicyCode(contractMasterPO);
			//2 查询主险种被保人列表
			BenefitInsuredPO benefitInsuredPO = new BenefitInsuredPO();
			benefitInsuredPO.setBusiItemId(contractProductBOList.get(0)
					.getBusiItemId());
			benefitInsuredPO.setCaseId(claimSubCaseBO.getCaseId());
			benefitInsuredPO.setCurFlag(new BigDecimal(1));
			List<BenefitInsuredPO> benefitInsuredPOs = benefitInsuredDao
					.findAllBenefitInsured(benefitInsuredPO);
			com.nci.tunan.mms.interfaces.calc.exports.calccashbonus.vo.MmsCalcCashBonusListReqVO inputData = new com.nci.tunan.mms.interfaces.calc.exports.calccashbonus.vo.MmsCalcCashBonusListReqVO();
			List<com.nci.tunan.mms.interfaces.calc.exports.calccashbonus.vo.MmsCalcCashBonusReqVO> calcCashBonusReqVOList = 
			        new ArrayList<com.nci.tunan.mms.interfaces.calc.exports.calccashbonus.vo.MmsCalcCashBonusReqVO>();
			//3 循环组装计算现金红利请求对象集合
			for (ContractProductBO contractProductBO : contractProductBOList) {
				//3.1 查询险种信息
				//@invalid 查询险种
				ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
				contractBusiProdPO.setCaseId(claimSubCaseBO.getCaseId());
				contractBusiProdPO.setBusiItemId(contractProductBO.getBusiItemId());
				contractBusiProdPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
				contractBusiProdPO = contractBusiProdDao.findAllContractBusiProd(contractBusiProdPO).get(0);
				//3.2 查询险种定义信息
				BusinessProductPO businessProductPO = new BusinessProductPO();
				businessProductPO.setProductCodeSys(contractBusiProdPO.getBusiProdCode());
				businessProductPO = businessProductDao.findAllBusinessProduct(businessProductPO).get(0);
				//3.3 查询保单是否在第一次领取日前，查询上次分红日
				com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO claimQueryPolicyInfoVO = 
				        new com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoReqVO();
				claimQueryPolicyInfoVO.setBusiItemId(contractProductBO
						.getBusiItemId());
				claimQueryPolicyInfoVO.setClaimDate(claimSubCaseBO
						.getClaimDate());
				claimQueryPolicyInfoVO.setItemId(contractProductBO.getItemId());
				claimQueryPolicyInfoVO.setPolicyId(contractProductBO
						.getPolicyId());
				List<String> typeFlagList = new ArrayList<String>();
				typeFlagList.add(ClaimConstant.IS_STAR_ANNUITY);
				typeFlagList.add(ClaimConstant.LAST_ALLOCATE_DATE);
				typeFlagList.add(ClaimConstant.MARGIN_BASE_AMOUNT);
				claimQueryPolicyInfoVO.setTypeFlagList(typeFlagList);
				com.nci.tunan.pa.interfaces.serviceData.claimquerypolicyinfo.ClaimQueryPolicyInfoResVO claimQueryPolicyInfoResVO = cLMServiceUtil
						.paiclaimquerypolicyinfouccqueryPolicyInfo(claimQueryPolicyInfoVO);
                //3.4 设置查询入参所需信息
				com.nci.tunan.mms.interfaces.calc.exports.calccashbonus.vo.MmsCalcCashBonusReqVO calcCashBonusReqVO = 
				        new com.nci.tunan.mms.interfaces.calc.exports.calccashbonus.vo.MmsCalcCashBonusReqVO();
				//@invalid 是否为整年度分红标志位
				calcCashBonusReqVO.setIsWholePolicyFlag(Integer
						.parseInt(ClaimConstant.NO));
				//@invalid 缴费年限或年龄
				calcCashBonusReqVO.setChargeYear(contractProductBO
						.getChargeYear().intValue());
				//@invalid 责任组ID
				calcCashBonusReqVO.setProductId(contractProductBO
						.getProductId());
				//@invalid 保单年度//保单月度 
				calcCashBonusReqVO.setPolicyYear((int) DateUtilsEx
						.getYearAmount(contractProductBO.getValidateDate(),
								claimSubCaseBO.getClaimDate()));
				calcCashBonusReqVO.setPolicyMonth(new BigDecimal(
						DateUtilsEx.getMonthAmount(
								contractProductBO.getValidateDate(),
								claimSubCaseBO.getClaimDate())));
				//@invalid 被保人投保时年龄
				InsuredListPO insuredListPO = new InsuredListPO();
				insuredListPO.setCaseId(claimSubCaseBO.getCaseId());
				insuredListPO.setListId(benefitInsuredPOs.get(0).getInsuredId());
				insuredListPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
				List<InsuredListPO> insuredListPOs = insuredListDao
						.findAllInsuredList(insuredListPO);
				BigDecimal age = insuredListPOs.get(0).getInsuredAge();
				calcCashBonusReqVO.setAge(age);
				//@invalid 现金金分红计算日期 
				calcCashBonusReqVO.setCbCalDate(claimSubCaseBO.getClaimDate());
				//@invalid 被保人性别
				com.nci.core.common.interfaces.vo.CustomerVO customerVO = new com.nci.core.common.interfaces.vo.CustomerVO();
				CustomerBaseInfoVO customerBaseInfo = new CustomerBaseInfoVO();
				customerBaseInfo.setCustomerId(insuredListPOs.get(0)
						.getCustomerId());
				customerVO.setCustomerBaseInfo(customerBaseInfo);
				com.nci.core.common.interfaces.vo.CustomerVO insured = BOServiceFactory
						.getCustomerUCC().queryCustomerBaseInfo(customerVO);
				if (insured != null) {
					calcCashBonusReqVO.setGender(insured.getCustomerBaseInfo()
							.getCustomerGender().intValue());
				}
				
				//@invalid 保额 
				calcCashBonusReqVO.setSA(contractProductBO.getAmount());
				//@invalid 事故发生日
				calcCashBonusReqVO.setAccidentDate(claimSubCaseBO.getClaimDate());
				
				 //@invalid （即最近一次年度分红对应的保单生效对应日，如未进行过分红则为生效日为空即可，
				 //@invalid 例如保单管理在保单生效对应日2016/9/19进行了一次年度分红则latestBonusDate就为2016/9/19，
				 //@invalid 适用于基本责任和可选责任分红 如保单生效对应日为2016/4/1，在红利公布日2016/7/1进行年度分红，
				 //@invalid latestBonusDate为2016/4/1），日期型
				if(claimQueryPolicyInfoResVO!=null&&claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.LAST_ALLOCATE_DATE)!=null){
					calcCashBonusReqVO.setLatestBonusDate(DateUtilsEx.toDate(claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.LAST_ALLOCATE_DATE), "yyyy-MM-dd"));
				}else{
					calcCashBonusReqVO.setLatestBonusDate(contractProductBO.getValidateDate());
				}
				//@invalid 保单年度末年龄
				BigDecimal policyYearAge = insuredListPOs.get(0).getInsuredAge().add(new BigDecimal(DateUtilsEx.getYearAmount(contractProductBO.getValidateDate(), claimSubCaseBO.getClaimDate())));
				calcCashBonusReqVO.setPolicyYearAge(policyYearAge.intValue());
				//@invalid 险种类型
				calcCashBonusReqVO.setProductType(businessProductPO.getProductCategory3());
				//@invalid 险种代码 
				calcCashBonusReqVO.setBusinessPrdCode(businessProductPO.getProductCodeSys());
				//@invalid 保单生效日
				calcCashBonusReqVO.setPolicyEffectiveDate(contractProductBO.getValidateDate());
				//@invalid 年缴保费（不含加费）
				calcCashBonusReqVO.setPremium(contractProductBO.getStdPremAf());
				 //@invalid 保障年期期间
				if (contractProductBO.getCoverageYear() != null) {
					calcCashBonusReqVO.setCoverageYear(contractProductBO.getCoverageYear()
						.intValue());
				}
				//@invalid 约定领取年龄
				if (contractProductBO.getPayYear() != null) {
					calcCashBonusReqVO.setPayYear(contractProductBO.getPayYear().intValue());
				}
				//@invalid 996产品利差基础
				if(claimQueryPolicyInfoResVO!=null&&claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.MARGIN_BASE_AMOUNT)!=null){
					calcCashBonusReqVO.setMarginBaseAmount(new BigDecimal(claimQueryPolicyInfoResVO.getCliamMap().get(ClaimConstant.MARGIN_BASE_AMOUNT)));
				}else {
					calcCashBonusReqVO.setMarginBaseAmount(null);
				}
				calcCashBonusReqVOList.add(calcCashBonusReqVO);
			}
			inputData.setMmsCalcCashBonusReqVO(calcCashBonusReqVOList);
			logger.debug(XmlHelper.classToXml(inputData));
			//4 调用现金红利计算接口计算现金分红
			outputData = cLMServiceUtil.prdimmscalccashbonuslistucccalcCashBonusList(inputData);
			return outputData;
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("现金分红计算失败");
			throw new BizException("系统异常");
		}
	}
	/**
	 * @description 判断是否处于等待期
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.clm.impl.imports.pds.IClaimPDSService#queryObservationPeriod(com.nci.tunan.clm.interfaces.model.bo.ContractBusiProdBO, java.math.BigDecimal)
	 * @param contractBusiProdBO 险种信息
	 * @param caseId 赔案ID
	 * @return 等待期,如产品未返回则等待期为0
	*/
	public boolean queryObservationPeriod(ContractBusiProdBO contractBusiProdBO,BigDecimal caseId,Date claimDate,BigDecimal accReason){
		//如果险种为空则返回false,出险原因不等于空，且为意外则返回false;
		if(contractBusiProdBO==null||(accReason!=null&&ClaimConstant.ACC_REASON_TWO.compareTo(accReason)==0)){
			return false;
		}
		com.nci.tunan.prd.interfaces.query.exports.queryobservationperiod.vo.PrdQueryObservationPeriodReqVO inputData = new com.nci.tunan.prd.interfaces.query.exports.queryobservationperiod.vo.PrdQueryObservationPeriodReqVO();
		List<com.nci.tunan.prd.interfaces.query.exports.queryobservationperiod.vo.PrdQueryObservationProductReqVO> observationPeriodReqVOs = new ArrayList<com.nci.tunan.prd.interfaces.query.exports.queryobservationperiod.vo.PrdQueryObservationProductReqVO>();
		
		
		com.nci.tunan.prd.interfaces.query.exports.queryobservationperiod.vo.PrdQueryObservationProductReqVO observationPeriodReqVO = new com.nci.tunan.prd.interfaces.query.exports.queryobservationperiod.vo.PrdQueryObservationProductReqVO();
		List<BigDecimal> productIdList = new ArrayList<BigDecimal>();
		observationPeriodReqVO.setBusinessPrdId(contractBusiProdBO.getBusiPrdId());
		//查询赔案ID下所有责任组
		if(contractBusiProdBO.getContractProductBOs()==null||contractBusiProdBO.getContractProductBOs().size()==0){
			ContractProductPO contractProductPO = new ContractProductPO();
			contractProductPO.setCaseId(caseId);
			contractProductPO.setBusiItemId(contractBusiProdBO.getBusiItemId());
			contractProductPO.setCurFlag(ClaimConstant.FLAG_CURRENT_DATE);
			List<ContractProductPO> contractProductPOList = contractProductDao.findAllContractProduct(contractProductPO);
			for(ContractProductPO contractProduct : contractProductPOList){
				productIdList.add(contractProduct.getProductId());
			}
		}else{
			for(ContractProductBO contractProductBO : contractBusiProdBO.getContractProductBOs()){
				productIdList.add(contractProductBO.getProductId());
			}
		}
		observationPeriodReqVO.setProductIdList(productIdList);
		observationPeriodReqVOs.add(observationPeriodReqVO);
		inputData.setObservationPeriodReqVOs(observationPeriodReqVOs);
		logger.debug("查询等待期接口入参",XmlHelper.classToXml(inputData));
		com.nci.tunan.prd.interfaces.query.exports.queryobservationperiod.vo.PrdQueryObservationPeriodResVO outputData = cLMServiceUtil.prdiprdobservationperioduccqueryObservationPeriod(inputData);
		logger.debug("查询等待期接口出参",XmlHelper.classToXml(outputData));
		
		if(outputData!=null&&outputData.getObservationProducts()!=null&&outputData.getObservationProducts().size()>0){
			if(StringUtils.isNotBlank(outputData.getObservationProducts().get(0).getRelaValue())){
				if(contractBusiProdBO.getRerinstateDate()!=null&&claimDate.compareTo(contractBusiProdBO.getRerinstateDate()) >= 0){
					Date waitingPeriodEnd =  DateUtilsEx.addDay(contractBusiProdBO.getRerinstateDate(), Integer.parseInt(outputData.getObservationProducts().get(0).getRelaValue()));
					//出险日大于等于生效日期小于等于等待期止期则处于等待期
					if(waitingPeriodEnd.compareTo(claimDate)>0&&contractBusiProdBO.getRerinstateDate().compareTo(claimDate)<=0){
						return true;
					}
				}else if(contractBusiProdBO.getValidateDate()!=null){
					Date waitingPeriodEnd =  DateUtilsEx.addDay(contractBusiProdBO.getValidateDate(), Integer.parseInt(outputData.getObservationProducts().get(0).getRelaValue()));
					//出险日大于等于生效日期小于等于等待期止期则处于等待期
					if(waitingPeriodEnd.compareTo(claimDate)>0&&contractBusiProdBO.getValidateDate().compareTo(claimDate)<=0){
						return true;
					}
				}
			}
		}
		return false;
	}
	/**
	 * @description 根据产品ID返回可豁免险种集合
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param businessPrdId 产品ID
	 * @return 可豁免险种集合
	*/
	public List<String> queryRemitBusiProdList(BigDecimal businessPrdId){
		List<String> busiProdList = new ArrayList<String>();
		//入参为空返回空集合
		if(businessPrdId==null){
			return busiProdList;
		}
		com.nci.tunan.prd.interfaces.calc.exports.canexemptproduct.vo.PrdCanExemptProductReqVO inputData = new com.nci.tunan.prd.interfaces.calc.exports.canexemptproduct.vo.PrdCanExemptProductReqVO();
		inputData.setBusinessPrdId(businessPrdId);
		com.nci.tunan.prd.interfaces.calc.exports.canexemptproduct.vo.PrdCanExemptProductResVO outputData = cLMServiceUtil.prdiprdcanexemptproductucccanexemptproduct(inputData);
		////将产品返回的险种号组装成集合返回
		if(outputData!=null&&outputData.getBusinessCodeResVOList()!=null){
			for(PrdBusinessCodeResVO prdBusinessCodeResVO : outputData.getBusinessCodeResVOList()){
				busiProdList.add(prdBusinessCodeResVO.getBusinessCode());
			}
		}
		return busiProdList;
	}
	/**
	 * @description 计算保费参数准备(豁免险种退费使用，非豁免险种请勿使用)
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractProductBO 责任组信息
	 * @param insuredListPO 被保人信息
	 * @param claimCaseBO 赔案信息
	 * @param contractMasterBO 保单层信息
	 * @return 
	*/
	public BigDecimal setPrdPremCalRequestData(ContractProductBO contractProductBO,InsuredListBO insuredListBO,ClaimCaseBO claimCaseBO,ContractMasterBO contractMasterBO,PolicyHolderBO policyHolderBO){
		com.nci.core.common.interfaces.vo.CustomerVO customerVO = new com.nci.core.common.interfaces.vo.CustomerVO();
		CustomerBaseInfoVO customerBaseInfo = new CustomerBaseInfoVO();
		customerBaseInfo.setCustomerId(claimCaseBO.getInsuredId());
		customerVO.setCustomerBaseInfo(customerBaseInfo);
		com.nci.core.common.interfaces.vo.CustomerVO insured = BOServiceFactory.getCustomerUCC().queryCustomerBaseInfo(customerVO);
		com.nci.tunan.mms.interfaces.calc.exports.calpremandsa.vo.PrdPremCalByMMsReqVO inputData = new com.nci.tunan.mms.interfaces.calc.exports.calpremandsa.vo.PrdPremCalByMMsReqVO();
		//@invalid 28 责任组编码 
		inputData.setProductId(contractProductBO.getProductId().toString());
		//@invalid 29 被保险人年龄 
		if(insuredListBO!=null){
			inputData.setAge(insuredListBO.getInsuredAge().intValue());
		}
		//@invalid 30 被保人性别
		if(insured.getCustomerBaseInfo().getCustomerGender()!=null){
			inputData.setGender(insured.getCustomerBaseInfo().getCustomerGender().intValue());
		}
		
		//@invalid 31 被保人职业类别 
		int insuredJob = -1;
		JobCodePO jobCodePO = null;
		if (insured.getCustomerBaseInfo().getJobCode() != null) {
			jobCodePO = new JobCodePO();
			jobCodePO.setJobCode(insured.getCustomerBaseInfo().getJobCode());
			jobCodePO = jobCodeDao.findJobCode(jobCodePO);
			try {
				insuredJob = Integer.parseInt(jobCodePO.getJobUwLevel());
	        } catch (NumberFormatException e) {
	        	insuredJob= 0;
	        }
		}
		inputData.setJobClass(insuredJob);
		
		//@invalid 32 缴费年期类型
		inputData.setChargePeriod(contractProductBO.getChargePeriod());
		
		//@invalid 33 缴费年限或年龄
		if(contractProductBO.getChargeYear()!=null){
			inputData.setChargeYear(contractProductBO.getChargeYear().intValue());
		}
		
		//@invalid 34 标准保障年限或年龄 
		if(contractProductBO.getCoverageYear()!=null){
			inputData.setCoverageYear(contractProductBO.getCoverageYear().intValue());
		}
		
		//@invalid 35 保障年期类型 
		inputData.setCoveragePeriod(contractProductBO.getCoveragePeriod());    		
		
		//@invalid 36 保额 
		inputData.setSA(contractProductBO.getAmount());
		
		//@invalid 37 分数 
		if(contractProductBO.getUnit()!=null){
			inputData.setUnit(contractProductBO.getUnit());
		}
		
		//@invalid 38 保单生效日(只有保全和险种premiumEffectiveDate)
		inputData.setProductEffectiveDate(contractProductBO.getValidateDate());
		
		if(contractProductBO.getPremFreq()!=null){
			inputData.setChargeMode(contractProductBO.getPremFreq().intValue());
		}
		
		inputData.setInitRenewFlag(ClaimConstant.ONE+"");
		
		inputData.setDutyEffectiveDate(contractProductBO.getValidateDate());
		
		inputData.setInsuredBirthday(insured.getCustomerBaseInfo().getCustomerBirthday());
		
		inputData.setGetRate(contractProductBO.getPayoutRate());
		
		inputData.setGetYear(contractProductBO.getPayYear());
		
		if(contractMasterBO.getOrganCode()!=null){
		    if(contractMasterBO.getOrganCode().length()>=ClaimConstant.FOUR){
		        inputData.setManagerCom(contractMasterBO.getOrganCode().substring(0,ClaimConstant.FOUR));
		    }else{
		        inputData.setManagerCom(contractMasterBO.getOrganCode());
		    }
		}
		
		if(contractProductBO.getAmount()!=null){
			inputData.setAmount(contractProductBO.getAmount().intValue());
		}
		
		if(contractProductBO.getPayYear()!=null){
			inputData.setPayYear(contractProductBO.getPayYear().intValue());
		}
		
		inputData.setAge01(inputData.getAge());
		
		if(contractProductBO.getDeductibleFranchise()!=null){
			inputData.setDeductible(contractProductBO.getDeductibleFranchise());
		}
		
		if(contractProductBO.getPayFreq()!=null){
			inputData.setPayfreq(Integer.parseInt(contractProductBO.getPayFreq()));
		}
		
		if(insured.getCustomerBaseInfo().getCountryCode()!=null){
			CountryPO countryPO = new CountryPO();
			countryPO.setCountryCode(insured.getCustomerBaseInfo().getCountryCode());
			countryPO = countryDao.findCountry(countryPO);
			if(countryPO.getCountryUwLevel()!=null){
				inputData.setCountryCode(countryPO.getCountryUwLevel().intValue());
			}
		}
		
		if(contractProductBO.getPayoutRate()!=null){
			inputData.setMaturityRate(contractProductBO.getPayoutRate().intValue());
		}
		
		if(contractProductBO.getPayFreq()!=null){
			inputData.setPayType(Integer.parseInt(contractProductBO.getPayFreq()));
		}
		
		inputData.setPolicyYear(1);
		
		 //@invalid 42 是否为保全进行的计算
	    inputData.setIsBaoquanFlag(Integer.parseInt(ClaimConstant.NO));
	    //@invalid 43 投保时约定的基本保额
	  	inputData.setInsuredSA(contractProductBO.getAmount());
	  	
		inputData.setIsMoreInsured(Integer.parseInt("0"));
		//每期豁免保费
		inputData.setExemptPremium(contractProductBO.getAmount());
		
		customerVO = new com.nci.core.common.interfaces.vo.CustomerVO();
		customerBaseInfo = new CustomerBaseInfoVO();
		customerBaseInfo.setCustomerId(policyHolderBO.getCustomerId());
		customerVO.setCustomerBaseInfo(customerBaseInfo);
		com.nci.core.common.interfaces.vo.CustomerVO policyHoler = BOServiceFactory.getCustomerUCC().queryCustomerBaseInfo(customerVO);
		//投保人年龄
		inputData.setAppntAge(CommonUtil.getAge(policyHoler.getCustomerBaseInfo().getCustomerBirthday(), contractProductBO.getValidateDate()));
		//投保人性别
		inputData.setAppntGender(policyHoler.getCustomerBaseInfo().getCustomerGender().intValue());
		//投保日期
        if(contractProductBO.getApplyDate()!=null){
            inputData.setInsuranceEffectiveDate(contractProductBO.getApplyDate());  
        }
        ContractProductOtherPO contractProductOtherPO = new ContractProductOtherPO();
        contractProductOtherPO.setCaseId(claimCaseBO.getCaseId());
        contractProductOtherPO.setItemId(contractProductBO.getItemId());
        contractProductOtherPO.setCopyDate(contractProductBO.getCopyDate());
        contractProductOtherPO.setCurFlag(ClaimConstant.FLAG_CLAIM_DATE);
        if(contractProductOtherDao.findAllContractProductOther(contractProductOtherPO).size() > 0){
            contractProductOtherPO = contractProductOtherDao.findAllContractProductOther(contractProductOtherPO).get(0);
        }
        //领取期间
        if (contractProductOtherPO.getField9()!=null) {
            inputData.setPayPeriodYear(Integer.valueOf(contractProductOtherPO.getField9()));
        }
        ////家庭单保障计划类别折扣
        if(contractProductBO.getDiscountType()!=null){
                 inputData.setFamilyPlanTypeDiscount(Integer.valueOf(contractProductBO.getDiscountType().toString()));  
         }
        //保障计划
        if(contractProductOtherPO.getField1()!=null){
                 inputData.setPlanType(contractProductOtherPO.getField1());
         }
        //身故保险金计划
        if(contractProductOtherPO.getDeathBenefitPlan()!=null){
            inputData.setDeathPlanType(contractProductOtherPO.getDeathBenefitPlan());
        }
		com.nci.tunan.mms.interfaces.calc.exports.calpremandsa.vo.PrdPremCalByMMSResVO outputData= cLMServiceUtil.prdicalpremandsaucccalProductPremAndSA(inputData);
		if(outputData!=null&&outputData.getPremium()!=null){
			return outputData.getPremium();
		}else{
			return null;
		}
	}
	public IClaimPASService getClaimPASService() {
		return claimPASService;
	} 

	public void setClaimPASService(IClaimPASService claimPASService) {
		this.claimPASService = claimPASService;
	}

	public IBusinessProductDao getBusinessProductDao() {
		return businessProductDao;
	}

	public void setBusinessProductDao(IBusinessProductDao businessProductDao) {
		this.businessProductDao = businessProductDao;
	}

	public ICLMServiceUtil getcLMServiceUtil() {
		return cLMServiceUtil;
	}

	public void setcLMServiceUtil(ICLMServiceUtil cLMServiceUtil) {
		this.cLMServiceUtil = cLMServiceUtil;
	}

	public IContractBusiProdDao getContractBusiProdDao() {
		return contractBusiProdDao;
	}

	public void setContractBusiProdDao(IContractBusiProdDao contractBusiProdDao) {
		this.contractBusiProdDao = contractBusiProdDao;
	}

	public IProductLifeDao getProductLifeDao() {
		return productLifeDao;
	}

	public void setProductLifeDao(IProductLifeDao productLifeDao) {
		this.productLifeDao = productLifeDao;
	}

	public IContractMasterDao getContractMasterDao() {
		return contractMasterDao;
	}

	public void setContractMasterDao(IContractMasterDao contractMasterDao) {
		this.contractMasterDao = contractMasterDao;
	}

	public IBenefitInsuredDao getBenefitInsuredDao() {
		return benefitInsuredDao;
	}

	public void setBenefitInsuredDao(IBenefitInsuredDao benefitInsuredDao) {
		this.benefitInsuredDao = benefitInsuredDao;
	}

	public IInsuredListDao getInsuredListDao() {
		return insuredListDao;
	}

	public void setInsuredListDao(IInsuredListDao insuredListDao) {
		this.insuredListDao = insuredListDao;
	}

	public static Logger getLogger() {
		return logger;
	}

	public static void setLogger(Logger logger) {
		ClaimPDSServiceImpl.logger = logger;
	}

	public IClaimMatchJournaDao getClaimMatchJournaDao() {
		return claimMatchJournaDao;
	}

	public void setClaimMatchJournaDao(IClaimMatchJournaDao claimMatchJournaDao) {
		this.claimMatchJournaDao = claimMatchJournaDao;
	}
	
	public IClaimBusiProdDao getClaimBusiProdDao() {
        return claimBusiProdDao;
    }

    public void setClaimBusiProdDao(IClaimBusiProdDao claimBusiProdDao) {
        this.claimBusiProdDao = claimBusiProdDao;
    }

	public IPolicyHolderDao getPolicyHolderDao() {
		return policyHolderDao;
	}

	public void setPolicyHolderDao(IPolicyHolderDao policyHolderDao) {
		this.policyHolderDao = policyHolderDao;
	}

	public IContractProductDao getContractProductDao() {
		return contractProductDao;
	}

	public void setContractProductDao(IContractProductDao contractProductDao) {
		this.contractProductDao = contractProductDao;
	}

	public IClaimCaseDao getClaimCaseDao() {
		return claimCaseDao;
	}

	public void setClaimCaseDao(IClaimCaseDao claimCaseDao) {
		this.claimCaseDao = claimCaseDao;
	}

	public IClaimLiabDao getClaimLiabDao() {
		return claimLiabDao;
	}

	public void setClaimLiabDao(IClaimLiabDao claimLiabDao) {
		this.claimLiabDao = claimLiabDao;
	}

	public IContractProductOtherDao getContractProductOtherDao() {
		return contractProductOtherDao;
	}

	public void setContractProductOtherDao(
			IContractProductOtherDao contractProductOtherDao) {
		this.contractProductOtherDao = contractProductOtherDao;
	}

	public ICustomerDao getCustomerDao() {
		return customerDao;
	}
	public void setCustomerDao(ICustomerDao customerDao) {
		this.customerDao = customerDao;
	}

	public IJobCodeDao getJobCodeDao() {
		return jobCodeDao;
	}

	public void setJobCodeDao(IJobCodeDao jobCodeDao) {
		this.jobCodeDao = jobCodeDao;
	}

	public ICountryDao getCountryDao() {
		return countryDao;
	}

	public void setCountryDao(ICountryDao countryDao) {
		this.countryDao = countryDao;
	}
	
}
