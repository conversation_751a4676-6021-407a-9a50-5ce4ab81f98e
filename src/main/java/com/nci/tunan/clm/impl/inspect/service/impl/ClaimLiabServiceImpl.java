package com.nci.tunan.clm.impl.inspect.service.impl;
 
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;

import com.nci.tunan.clm.dao.IClaimLiabDao;
import com.nci.tunan.clm.dao.IContractBusiProdDao;
import com.nci.tunan.clm.dao.IPermissionCompDao;
import com.nci.tunan.clm.dao.IPremArapDao;
import com.nci.tunan.clm.impl.inspect.service.IClaimLiabService;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO;
import com.nci.tunan.clm.interfaces.model.bo.ClaimLiabBO;
import com.nci.tunan.clm.interfaces.model.bo.PermissionInfoBO;
import com.nci.tunan.clm.interfaces.model.bo.QueryClaimCaseRecordBO;
import com.nci.tunan.clm.interfaces.model.po.ClaimLiabPO;
import com.nci.tunan.clm.interfaces.model.po.PermissionInfoPO;
import com.nci.tunan.clm.interfaces.model.po.PremArapPO;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.logging.LoggerFactory;
 
 
/**
 * 理赔给付责任理算service实现
 * @description 
 * @<NAME_EMAIL>
 * @.belongToModule CLM-理赔系统
 * @date 2015-10-20 15:36:20  
 */
 public class ClaimLiabServiceImpl   implements IClaimLiabService  {
 	/** 
	 * @Fields logger :  日志工具
 	 */
 	private static Logger logger = LoggerFactory.getLogger();

	/** 
     * @Fields claimCaseDao : 注入理赔给付责任理算 dao 
     */
 	private IClaimLiabDao claimLiabDao;
 	/** 
 	 * @Fields permissionCompDao : 关于权限的组合Dao接口
 	*/ 
 	private IPermissionCompDao permissionCompDao;
 	
    private IPremArapDao premArapDao;
 	
 	/**
 	 * 查询理算金额
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * @<NAME_EMAIL>
 	 * @see com.nci.tunan.clm.impl.inspect.service.IClaimLiabService#findAllClaimLiab(com.nci.tunan.clm.interfaces.model.bo.ClaimCaseBO)
 	 * @param claimCaseBO 赔案主表对象
 	 * @return  List<ClaimLiabBO> 理赔给付责任理算
 	*/
 	@Override
     public List<ClaimLiabBO> findAllClaimLiab(ClaimCaseBO claimCaseBO) {
 	     ClaimLiabPO claimLiabPO = new ClaimLiabPO();
 	     claimLiabPO.setCaseId(claimCaseBO.getCaseId());
 	     //1.调用注入理赔给付责任理算dao层，查询相同caseId的数据
 	     List<ClaimLiabPO> claimLiabPOList = claimLiabDao.findAllClaimLiab(claimLiabPO);
         return BeanUtils.copyList(ClaimLiabBO.class, claimLiabPOList);
     }
 	/**
 	 * 查询审核 审批等级
 	 * @description
 	 * @version V1.0.0
 	 * @title
 	 * @<NAME_EMAIL>
 	 * @param permissionInfoBO 任务信息
 	 * @return List<PermissionInfoBO> 任务信息
 	*/
 	public List<PermissionInfoBO> findAuditLevel(PermissionInfoBO permissionInfoBO){
 	    PermissionInfoPO permissionInfoPO = BeanUtils.copyProperties(PermissionInfoPO.class, permissionInfoBO);
 	    //1.调用关于权限的组合Dao接口，查询审核审批权限
 	    List<PermissionInfoPO> permissionInfoPOList = permissionCompDao.queryAuditLevel(permissionInfoPO);
 	    return BeanUtils.copyList(PermissionInfoBO.class, permissionInfoPOList);
 	}
 	
 	 /**
     * @description 通过caseID查询涉案保单信息
     * @version V1.0.0
     * @title
     * @<NAME_EMAIL>
     * @param claimCaseBO 赔案主表对象
 	 * @return  List<ClaimLiabBO> 理赔给付责任理算
    */
    public List<ClaimLiabBO> findAllClaimLiabByCaseID(ClaimCaseBO claimCaseBO){
    	 ClaimLiabPO claimLiabPO = new ClaimLiabPO();
	     claimLiabPO.setCaseId(claimCaseBO.getCaseId());
	     //1.调用理赔给付责任理算dao层，根据caseID查询理算信息
	     List<ClaimLiabPO> claimLiabPOList = claimLiabDao.findAllClaimLiabByCaseID(claimLiabPO);
        return BeanUtils.copyList(ClaimLiabBO.class, claimLiabPOList);
    }
 	
    /**
     * 
     * @description 根据保项结论及理算金额判断初始化显示案件标识
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.clm.impl.inspect.service.IClaimLiabService#findCaseFlagByClaimLiab(com.nci.tunan.clm.interfaces.model.bo.ClaimLiabBO)
     * @param claimLiabBOL 理赔给付责任理算BO对象
     * @return 案件标识
     */
    @Override
	public String findCaseFlagByClaimLiab(ClaimLiabBO claimLiabBOL) {
		//1.根据入参对象查询赔案下所有保项信息
    	String caseFlag = ClaimConstant.NULL_STR;
    	ClaimLiabPO claimLiabPO = new ClaimLiabPO();
	    claimLiabPO.setCaseId(claimLiabBOL.getCaseId());
    	List<ClaimLiabPO> claimLiabPOList = claimLiabDao.findAllClaimLiab(claimLiabPO);
    	//2.循环统计赔付结论等信息
    	//2.1.设置各个初始化值
    	BigDecimal  threeCalcPay = ClaimConstant.BIGDECIMAL_ZERO;//2.1.1.设置"3给付"初始化值("全部给付","部分给付","协议给付")
    	BigDecimal rejectCalcPay = ClaimConstant.BIGDECIMAL_ZERO;//2.1.2.设置"全部拒付"初始化值
    	BigDecimal pardonCalcPay = ClaimConstant.BIGDECIMAL_ZERO;//2.1.3.设置"通融给付"初始化值
    	boolean normalFlag = false;     //@invalid 普通案件判断标识
        boolean difficultFlag = false;  //@invalid 疑难案件判断标识
    	//2.2.循环统计不同赔付结论下的合计统计金额
    	for(ClaimLiabPO  claimLiabPOL : claimLiabPOList){
    		if(claimLiabPOL.getLiabConclusion() !=null){
    			if((claimLiabPOL.getLiabConclusion().compareTo(ClaimConstant.CLAIM_LIAB_DECISION_ALLPAY)==ClaimConstant.ZERO)
    					||(claimLiabPOL.getLiabConclusion().compareTo(ClaimConstant.CLAIM_LIAB_DECISION_PARTPAY)==ClaimConstant.ZERO)
    					||(claimLiabPOL.getLiabConclusion().compareTo(ClaimConstant.CLAIM_LIAB_DECISION_AGREEMENTPAY)==ClaimConstant.ZERO)){
    				//2.2.1.判断是否是"全部给付","部分给付","协议给付"，是则统计金额(普通案件范围)
    				normalFlag = true;
    				threeCalcPay = threeCalcPay.add(claimLiabPOL.getCalcPay());
    			}else if(claimLiabPOL.getLiabConclusion().compareTo(ClaimConstant.CLAIM_LIAB_DECISION_REFUSEPAY)==ClaimConstant.ZERO){
    				//2.2.2.判断是否是"全部拒付"，是则统计金额(疑难案件范围)
    				difficultFlag = true;
    				rejectCalcPay = rejectCalcPay.add(claimLiabPOL.getCalcPay());
    			}else{
    				//2.2.3.判断是否是"通融给付"，是则统计金额(疑难案件范围)
    				difficultFlag = true;
    				pardonCalcPay = pardonCalcPay.add(claimLiabPOL.getCalcPay());
    			}
    		}
    	}
    	//3.按照规则判断普通案件和疑难案件范围内金额大小，并确定案件标识："全部拒付"和"通融给付"均属于疑难案件范围，将全部拒付和通融给付理算金额统计加和作为疑难案件比较金额
    	pardonCalcPay = pardonCalcPay.add(rejectCalcPay);
    	//3.1.当同时存在普通案件和疑难案件范围时，比较普通案件范围内合计金额与疑难案件合计金额的值的大小，确定案件标识
    	if(normalFlag && difficultFlag){
    		if(threeCalcPay.compareTo(pardonCalcPay)>=ClaimConstant.ZERO){//3.1.1.判断普通案件金额大于疑难案件金额，定为普通案件标识
        		caseFlag = ClaimConstant.STRING_ONE;
        	}else{//3.1.2.判断普通案件金额小于疑难案件金额，定为疑难案件标识
        		caseFlag = ClaimConstant.STRING_THREE;
        	}
    	}else if(normalFlag){//3.2.当只有普通案件范围时，为普通案件
    		caseFlag = ClaimConstant.STRING_ONE;
    	}else if(difficultFlag){//3.3.当只有疑难案件范围时，为疑难案件
    		caseFlag = ClaimConstant.STRING_THREE;
    	}
		return caseFlag;
	}
    
    
    /**
	 * 保单-理赔接口
	 */
	@Override
	public QueryClaimCaseRecordBO findClaimCaseRecord(QueryClaimCaseRecordBO queryClaimCaseRecordBO) {
		//@invalid 查询截止至当前查询日期保单涉及的已经结案且理赔金额>0的赔案信息（排除新增附加险涉及的理赔记录）
		ClaimLiabPO claimLiabPO = new ClaimLiabPO();
		claimLiabPO.setPolicyCode(queryClaimCaseRecordBO.getPolicyCode());
		claimLiabPO.getData().put("query_date", queryClaimCaseRecordBO.getQueryClaimCaseDate());
		claimLiabPO.getData().put("apply_date", queryClaimCaseRecordBO.getApplyDate());
		List<ClaimLiabPO> claimLiabPOs = claimLiabDao.findClaimCaseRecord(claimLiabPO);
		QueryClaimCaseRecordBO claimCaseRecordBO = new QueryClaimCaseRecordBO();
		if(claimLiabPOs.size() > 0){
			//@invalid 理赔记录数
			claimCaseRecordBO.setPayRecord(new BigDecimal(claimLiabPOs.size()));
			
			List<ClaimLiabBO> claimLiabBOs = new ArrayList<ClaimLiabBO>();
			for (ClaimLiabPO claimLiabPO2 : claimLiabPOs) {
				ClaimLiabBO claimLiabBO = new ClaimLiabBO();
				//@invalid 赔案下保单参与理算的理赔金（排除掉新增附加险）
				claimLiabBO.setActualPay(claimLiabPO2.getActualPay());
				//@invalid 获取理赔金额的给付日期
		        PremArapPO premArapPO = new PremArapPO();
		        premArapPO.setBusinessCode(claimLiabPO2.getData().get("case_no").toString());
		        List<PremArapPO> premArapPOList = premArapDao.findAllPremArapByReletionNO(premArapPO);
		        for (PremArapPO premArapPO2 : premArapPOList) {
		        	if(premArapPO2.getFinishTime() != null){
		        		claimLiabBO.setPayDate(premArapPO2.getFinishTime());
		        		break;
		        	}
		        }
				claimLiabBOs.add(claimLiabBO);
			}
			claimCaseRecordBO.setClaimCaseRecord(claimLiabBOs);
		}
		return claimCaseRecordBO;
	}
 	
 	public IClaimLiabDao getClaimLiabDao() {
        return claimLiabDao;
    }
    public void setClaimLiabDao(IClaimLiabDao claimLiabDao) {
        this.claimLiabDao = claimLiabDao;
    }
    public IPermissionCompDao getPermissionCompDao() {
        return permissionCompDao;
    }
    public void setPermissionCompDao(IPermissionCompDao permissionCompDao) {
        this.permissionCompDao = permissionCompDao;
    }
	public IPremArapDao getPremArapDao() {
		return premArapDao;
	}
	public void setPremArapDao(IPremArapDao premArapDao) {
		this.premArapDao = premArapDao;
	}
	 }
 