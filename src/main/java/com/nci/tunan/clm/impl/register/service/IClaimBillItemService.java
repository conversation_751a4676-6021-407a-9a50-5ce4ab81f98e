package com.nci.tunan.clm.impl.register.service;

import java.util.List;
import java.util.Map;

import com.nci.tunan.clm.interfaces.model.bo.ClaimBillItemBO;
import com.nci.udmp.framework.model.CurrentPage;



 /** 
 * @description 赔案账单信息
 * <AUTHOR> <EMAIL>
 * @date 2015-05-15
 * @.belongToModule CLM-理赔系统/赔案账单
*/
public interface IClaimBillItemService {
 	/**
 	 * @description 增加数据
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBO 账单信息
 	 * @return 添加结果
 	*/
 	public ClaimBillItemBO addClaimBillItem(ClaimBillItemBO claimBillItemBO);

 	/**
 	 * @description 修改数据
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBO 账单信息
 	 * @return 修改结果
 	*/
 	public ClaimBillItemBO updateClaimBillItem(ClaimBillItemBO claimBillItemBO);
 	
 	/**
 	 * @description 删除数据
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBO 账单信息
 	 * @return 删除是否成功
 	*/
 	public boolean deleteClaimBillItem(ClaimBillItemBO claimBillItemBO);
 	
 	/**
 	 * @description 查询单条数据
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBO 账单信息
 	 * @return 账单信息
 	*/
 	public ClaimBillItemBO findClaimBillItem(ClaimBillItemBO claimBillItemBO);
 	
 	/**
 	 * @description 查询账单信息
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBO 账单信息
 	 * @return 账单信息
 	*/
 	public List<ClaimBillItemBO> findAllClaimBillItem(ClaimBillItemBO claimBillItemBO);
 	
 	/**
 	 * @description 查询数据条数
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBO 查询条件
 	 * @return 查询结果条数
 	*/
 	public int findClaimBillItemTotal(ClaimBillItemBO claimBillItemBO);
 	
 	/**
 	 * @description 分页查询数据
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBO 账单信息
 	 * @param currentPage 分页对象
 	 * @return 查询结果集
 	*/
 	public CurrentPage<ClaimBillItemBO> queryClaimBillItemForPage(ClaimBillItemBO claimBillItemBO, CurrentPage<ClaimBillItemBO> currentPage);

 	/**
 	 * @description 批量增加数据
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBOList 对象列表
 	 * @return 批量添加是否成功
 	*/
 	public boolean batchSaveClaimBillItem(List<ClaimBillItemBO> claimBillItemBOList);
 	
 	/**
 	 * @description 批量修改数据
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBOList 赔案账单信息
 	 * @return 是否成功
 	*/
 	public boolean batchUpdateClaimBillItem(List<ClaimBillItemBO> claimBillItemBOList);
 	
 	/**
 	 * @description 批量删除数据
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBOList 账单信息
 	 * @return  批量删除是否成功
 	*/
 	public boolean batchDeleteClaimBillItem(List<ClaimBillItemBO> claimBillItemBOList);
 	
 	/**
 	 * @description 查询所有数据 ，重新组装为map
 	 * @version
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @param claimBillItemBO 账单信息
 	 * @return 查询结果存放到map中
 	*/
 	public List<Map<String, Object>> findAllMapClaimBillItem(ClaimBillItemBO claimBillItemBO);
 	
 }
  