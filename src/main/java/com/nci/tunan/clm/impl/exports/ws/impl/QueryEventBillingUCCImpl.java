package com.nci.tunan.clm.impl.exports.ws.impl;

import com.nci.tunan.cap.interfaces.vo.transferaccountdtl.ColPaySysOutputData;
import com.nci.tunan.clm.impl.exports.ws.IQueryEventBillingUCC;
import com.nci.tunan.clm.impl.mobile.service.IQueryEventBillingService;
import com.nci.tunan.clm.interfaces.exports.ws.iqueryeventbillingucc.queryeventbilling.vo.InputData;
import com.nci.tunan.clm.interfaces.exports.ws.iqueryeventbillingucc.queryeventbilling.vo.OutputData;
import com.nci.udmp.framework.exception.app.BizException;

public class QueryEventBillingUCCImpl implements IQueryEventBillingUCC {
	
	private IQueryEventBillingService queryEventBillingService;

	public IQueryEventBillingService getQueryEventBillingService() {
		return queryEventBillingService;
	}

	public void setQueryEventBillingService(IQueryEventBillingService queryEventBillingService) {
		this.queryEventBillingService = queryEventBillingService;
	}

	@Override
	public OutputData queryEventBilling(InputData inputdate) throws BizException {
		OutputData outputdata = new OutputData();
		
		//入参五要素校验
		if(inputdate.getBrithday() ==null || "".equals(inputdate.getBrithday())) {
			outputdata.setResultCode("1");
			outputdata.setResultMsg("查询失败，客户五要素均不可为空");
			return outputdata;
		}
		if(inputdate.getCustomerName() ==null || "".equals(inputdate.getCustomerName())) {
			outputdata.setResultCode("1");
			outputdata.setResultMsg("查询失败，客户五要素均不可为空");
			return outputdata;
		}
		if(inputdate.getIdNo() ==null || "".equals(inputdate.getIdNo())) {
			outputdata.setResultCode("1");
			outputdata.setResultMsg("查询失败，客户五要素均不可为空");
			return outputdata;
		}
		if(inputdate.getIdType() ==null || "".equals(inputdate.getIdType())) {
			outputdata.setResultCode("1");
			outputdata.setResultMsg("查询失败，客户五要素均不可为空");
			return outputdata;
		}
		if(inputdate.getSex() ==null || "".equals(inputdate.getSex())) {
			outputdata.setResultCode("1");
			outputdata.setResultMsg("查询失败，客户五要素均不可为空");
			return outputdata;
		}
		
		outputdata =   queryEventBillingService.queryEventBilling(inputdate);
        if(outputdata == null || outputdata.getCaselist() == null || outputdata.getCaselist().getCase().size() == 0) {
        	outputdata.setResultCode("1");
        	outputdata.setResultMsg("未查询到任何数据，请检查输入条件");
        	
        	return outputdata;
        }
        outputdata.setResultCode("0");
        outputdata.setResultMsg("");
		  return outputdata;
	}

}
