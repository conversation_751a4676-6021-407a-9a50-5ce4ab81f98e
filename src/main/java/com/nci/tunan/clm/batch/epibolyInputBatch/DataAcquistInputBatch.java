package com.nci.tunan.clm.batch.epibolyInputBatch;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.nci.tunan.clm.batch.epibolyInputBatch.service.IDataAcquistInputBatchService;
import com.nci.tunan.clm.batch.epibolyInputBatch.service.IEpibolyInputBatchService;
import com.nci.tunan.clm.interfaces.model.po.OutsourceCaseCompPO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.exception.BatchBizException;

/**
 * 外包录入接受报文批处理
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统 --外包录入接受报文批处理
 * @date 2015年5月15日 上午9:54:45
 */
public class DataAcquistInputBatch extends AbstractBatchJobForMod{

	/** 接受报文处理service */
	private IDataAcquistInputBatchService dataAcquistInputBatchService;
	
	/**
     * 
     * @description 设置批处理的模值
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#queryCounts(com.nci.udmp.component.batch.context.JobSessionContext)
     * @param jobSessionContext 作业中共享的全局配置信息
     * @return jobSessionContext 作业中共享的全局配置信息
     */
    @Override
    public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
        //1.设置模值
        jobSessionContext.setModNum(2); 
        return jobSessionContext;
    }

    /**
     * 查询外包为未回复的案件
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#query(com.nci.udmp.component.batch.context.JobSessionContext, long, int)
     * @param jobSessionContext 作业中共享的全局配置信息
     * @param start 开始条数
     * @param counts 条数
     * @return List<JobData> 结果集
     */
	public List<JobData> query(JobSessionContext jobSessionContext, long start,
			int counts) {
		List<JobData> datas = new ArrayList<JobData>();
		if(StringUtils.isNotEmpty(jobSessionContext.getParams().get(0).getParamValue())){
			//设置机构参数
			String organStrs = jobSessionContext.getParams().get(0).getParamValue();
			
			List<OutsourceCaseCompPO> outsourceCaseCompPOs = dataAcquistInputBatchService.queryBatchService(organStrs,start,counts);
			for (OutsourceCaseCompPO outsourceCaseCompPO : outsourceCaseCompPOs) {
				JobData data = new JobData();
				data.set("outsourceCaseCompPO", outsourceCaseCompPO);
				datas.add(data);
			}
			logger.info("管理机构为{}查询出的未回复的案件{}",organStrs,outsourceCaseCompPOs.size());
		}else{
			logger.info("理赔接收批处理未设置机构参数");
		}
		return datas;
	}
	
	 
	/**
     * 外包接收报文业务逻辑处理
     * @description 
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBactchJobForMod#write(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
     * @param jobSessionContext 作业中共享的全局配置信息
     * @param mapData 满足条件的数据
     */
	@Override
	public void write(JobSessionContext jobSessionContext, JobData mapData) {
	   //1.外包接收报文业务逻辑处理（无）
//@invalid		Map object = (Map) mapData.get("mapData");
//@invalid		epibolyInputBatchService.saveClaimData(object);
	}

	/**
     * 批处理自带生成方法
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#getIdName()
     * @return string
     */
	@Override
	public String getIdName() {
		//1.批处理自带生成方法
		return null;
	}

	/**
     * 批处理自带生成方法
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#isCanBeRun()
     * @return false 是否成功
     */
	@Override
	public boolean isCanBeRun() {
		//1.批处理自带生成方法
		return true;
	}

	/**
     * 批处理自带生成方法
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobStop()
     */
	@Override
	public void jobStop() {
		//1.批处理自带生成方法
	}


	public IDataAcquistInputBatchService getDataAcquistInputBatchService() {
		return dataAcquistInputBatchService;
	}

	public void setDataAcquistInputBatchService(
			IDataAcquistInputBatchService dataAcquistInputBatchService) {
		this.dataAcquistInputBatchService = dataAcquistInputBatchService;
	}

	/**
     * 批处理任务的具体操作
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#execute(com.nci.udmp.component.batch.context.JobSessionContext, java.util.List, int)
     * @param jobSessionContext 上下文参数
     * @param resultDatas 需要处理的结果集
     * @param modNum 上下文参数
     * @return List<JobData> 处理后的结果集
     */
    @Override
    public List<JobData> execute(JobSessionContext jobSessionContext, List<JobData> resultDatas, int modNum) {
        //1.外包接收报文业务逻辑处理
        try {
            for(JobData mapData: resultDatas){
                OutsourceCaseCompPO outsourceCaseCompPO = (OutsourceCaseCompPO) mapData.get("outsourceCaseCompPO");
                dataAcquistInputBatchService.query(outsourceCaseCompPO);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("理赔接收批处理异常" ,e);
            throw new BatchBizException("理赔接收批处理异常");
        }
        return resultDatas;
    }

    /**
     * 批处理自带生成方法
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobErrorAudit(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
     * @param jobSessionContext 上下文对象
     * @param jobData 数据对象
     */
    @Override
    public void jobErrorAudit(JobSessionContext jobSessionContext, JobData jobData) {
        //1.批处理自带生成方法
    }

}
