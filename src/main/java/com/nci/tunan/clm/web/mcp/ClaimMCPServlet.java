package com.nci.tunan.clm.web.mcp;

import com.nci.tunan.clm.dao.IClaimRelativityCalcDao;
import com.nci.tunan.clm.impl.util.ClaimConstant;
import com.nci.tunan.clm.interfaces.model.po.ClaimRelativityCalcPO;
import com.nci.udmp.util.logging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.context.support.SpringBeanAutowiringSupport;

import javax.servlet.ServletConfig;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@WebServlet("/saveClaimConfig")
public class ClaimMCPServlet extends HttpServlet {
    /**
     * 序列号
     */
    private static final long serialVersionUID = 1L;
    /**
     * 日志工具
     */
    private static Logger logger = LoggerFactory.getLogger();
    
   private static  final String PATH = "com.nci.tunan.clm.impl.calc.service.impl.relation.";
   
   private static  final String CALC_FORM_PARAM = "CalcFormParam";
   
   private static  final String CALC_RELATIVITY = "CalcRelativity";
    /**
     * 理赔理算相关性计算定义表Dao
     */
    @Autowired
    private IClaimRelativityCalcDao claimRelativityCalcDao;

    /**
     *
     * @description 初始化
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see javax.servlet.GenericServlet#init(javax.servlet.ServletConfig)
     * @param config 入参
     * @throws ServletException
     */
    public void init(ServletConfig config) throws ServletException {
        SpringBeanAutowiringSupport.processInjectionBasedOnServletContext(this,
                config.getServletContext());
    }

    /**
     *
     * @description 申请上传
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see javax.servlet.http.HttpServlet#doGet(javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse)
     * @param request 请求
     * @param response 返回
     * @throws ServletException
     * @throws IOException
     */
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        PrintWriter out = response.getWriter();
        //@invalid  获取收到报文
        BufferedReader reader = request.getReader();
        String line = "";
        StringBuffer inputString = new StringBuffer();
        while ((line = reader.readLine()) != null) {
            inputString.append(line);
        }
        Timestamp reqTime = new Timestamp(new Date().getTime()); //@invalid 请求时间
        logger.info("接受到的请求报文:++" + inputString.toString());
        
        
        // todo 解析报文
        
        // todo 模拟 请求产品获取相关因子和公式参数
        List<Map<String, String>> productMapList = new ArrayList<>();
        HashMap<String, String> formParamMap = new HashMap<>();
        formParamMap.put("relativityType","1");
        formParamMap.put("relativityId","163");
        productMapList.add(formParamMap);
        HashMap<String, String> relativityMap = new HashMap<>();
        relativityMap.put("relativityType","2");
        relativityMap.put("relativityId","11");
        productMapList.add(relativityMap);
        
        // 获取配置表数据判断是否需要新新增
        List<ClaimRelativityCalcPO> insertList = new ArrayList<>();
        List<ClaimRelativityCalcPO> allClaimRelativityCalc = claimRelativityCalcDao.findAllClaimRelativityCalc(new ClaimRelativityCalcPO());
        for (Map<String, String> productMap : productMapList) {
            if (productMap.containsKey("relativityType") && productMap.containsKey("relativityId")) {
                String relativityType = productMap.get("relativityType");
                String relativityId = productMap.get("relativityId");
                if (StringUtils.isEmpty(relativityType) || StringUtils.isEmpty(relativityId)) {
                    continue;
                }
                boolean exists = true;
                if(allClaimRelativityCalc != null && !allClaimRelativityCalc.isEmpty()) {
                    for (ClaimRelativityCalcPO claimRelativityCalc : allClaimRelativityCalc) {
                        if (claimRelativityCalc.getRelativityType().equals(new BigDecimal(relativityType))
                                && claimRelativityCalc.getRelativityId().equals(new BigDecimal(relativityId))) {
                            exists = false;
                            break;
                        }
                    }
                }

                // 只有不存在的记录才添加
                if (!exists) {
                    ClaimRelativityCalcPO claimRelativityCalcPO = new ClaimRelativityCalcPO();
                    claimRelativityCalcPO.setRelativityType(new BigDecimal(relativityType));
                    claimRelativityCalcPO.setRelativityId(new BigDecimal(relativityId));
                    claimRelativityCalcPO.setDataType(BigDecimal.ONE);
                    claimRelativityCalcPO.setOperator(String.valueOf(ClaimConstant.ELENEN));
                    String calcMethod = "";
                    if (ClaimConstant.RELATIVITY_TYPE_2.equals(claimRelativityCalcPO.getRelativityType())) {
                        calcMethod = PATH + CALC_FORM_PARAM +claimRelativityCalcPO.getRelativityId();
                    } else if (ClaimConstant.RELATIVITY_TYPE_1.equals(claimRelativityCalcPO.getRelativityType())) {
                        calcMethod = PATH + CALC_RELATIVITY +claimRelativityCalcPO.getRelativityId();
                    }
                    if (StringUtils.isEmpty(calcMethod)) {
                        throw new RuntimeException("未知参数");
                    }
                    claimRelativityCalcPO.setCalcMethod(calcMethod);
                    insertList.add(claimRelativityCalcPO);
                }
            }
        }
        
        if (CollectionUtils.isNotEmpty(insertList)) {
            claimRelativityCalcDao.batchSaveClaimRelativityCalc(insertList);
        }
    }
    /**
     *
     * @description 发送
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see javax.servlet.http.HttpServlet#doPost(javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse)
     * @param request 请求
     * @param response 返回
     * @throws ServletException
     * @throws IOException
     */
    protected void doPost(HttpServletRequest request,HttpServletResponse response) throws ServletException, IOException {
        doGet(request, response);
    }
}
