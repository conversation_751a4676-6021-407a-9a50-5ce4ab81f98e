
package com.nci.tunan.clm.claimReCheckSurveyCancel;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>OutputData complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="OutputData">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="RecheckStatusVOs" type="{http://www.newchinalife.com/service/bd}RecheckStatusVOs"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
/**
 *  前置调查检查出参
 * @description 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 上午11:59:19
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OutputData", propOrder = {
    "recheckStatusVOs"
})
public class OutputData {

    /** 
     * @Fields recheckStatusVOs :前置调查检查vo 
    */ 
    @XmlElement(name = "RecheckStatusVOs", required = true)
    protected RecheckStatusVOs recheckStatusVOs;

    /**
     * 获取recheckStatusVOs属性的值。
     * 
     * @return
     *     possible object is
     *     {@link RecheckStatusVOs }
     *     
     */
    public RecheckStatusVOs getRecheckStatusVOs() {
        return recheckStatusVOs;
    } 

    /**
     * 设置recheckStatusVOs属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link RecheckStatusVOs }
     *     
     */
    public void setRecheckStatusVOs(RecheckStatusVOs value) {
        this.recheckStatusVOs = value;
    }

}
