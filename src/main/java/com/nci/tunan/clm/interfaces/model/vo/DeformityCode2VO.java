package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;

/**
 * 
 * @description 伤残代码2
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class DeformityCode2VO extends BaseVO {	
	 /** 
	* @Fields deformityCode2Name :  伤残代码2名称
 	*/ 
	private String deformityCode2Name;
	 /** 
	* @Fields deformityType :  伤残类型
 	*/ 
	private String deformityType;
	 /** 
	* @Fields deformityCode2 :  伤残代码2
 	*/ 
	private String deformityCode2;
	 /** 
	* @Fields deformityCode1 :  伤残代码1
 	*/ 
	private String deformityCode1;
	 /** 
	* @Fields deformityRate :  残疾给付比例
 	*/ 
	private BigDecimal deformityRate;
	 /** 
	* @Fields icfCode :  ICF编码
 	*/ 
	private String icfCode;
	 /** 
	* @Fields deformityGrade :  伤残级别
 	*/ 
	private String deformityGrade;
		
	 public void setDeformityCode2Name(String deformityCode2Name) {
		this.deformityCode2Name = deformityCode2Name;
	}
	
	public String getDeformityCode2Name() {
		return deformityCode2Name;
	}
	 public void setDeformityType(String deformityType) {
		this.deformityType = deformityType;
	}
	
	public String getDeformityType() {
		return deformityType;
	}
	 public void setDeformityCode2(String deformityCode2) {
		this.deformityCode2 = deformityCode2;
	}
	
	public String getDeformityCode2() {
		return deformityCode2;
	}
	 public void setDeformityCode1(String deformityCode1) {
		this.deformityCode1 = deformityCode1;
	}
	
	public String getDeformityCode1() {
		return deformityCode1;
	}
	 public void setDeformityRate(BigDecimal deformityRate) {
		this.deformityRate = deformityRate;
	}
	
	public BigDecimal getDeformityRate() {
		return deformityRate;
	}
	 public void setIcfCode(String icfCode) {
		this.icfCode = icfCode;
	}
	
	public String getIcfCode() {
		return icfCode;
	}
	 public void setDeformityGrade(String deformityGrade) {
		this.deformityGrade = deformityGrade;
	}
	
	public String getDeformityGrade() {
		return deformityGrade;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "DeformityCode2VO [" +
				"deformityCode2Name="+deformityCode2Name+","+
"deformityType="+deformityType+","+
"deformityCode2="+deformityCode2+","+
"deformityCode1="+deformityCode1+","+
"deformityRate="+deformityRate+","+
"icfCode="+icfCode+","+
"deformityGrade="+deformityGrade+"]";
    }
}
