package com.nci.tunan.clm.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseBO;

import java.math.BigDecimal;

/** 
 * @description ClaimSubmitInfoVO对象
 * <AUTHOR> 
 * @date 2023-11-09 14:52:31  
 */
public class ClaimSubmitInfoBO extends BaseBO {	
		 /** 
	* @Fields reqClob :  请求报文
 	*/ 
	private String reqClob;
	 /** 
	* @Fields submitType :  报送类型（1-理赔税优报送）
 	*/ 
	private BigDecimal submitType;
			 /** 
	* @Fields listId :  主键
 	*/ 
	private BigDecimal listId;
	 /** 
	* @Fields respClob :  返回报文
 	*/ 
	private String respClob;
				 /** 
	* @Fields taskListId :  报送任务表主键
 	*/ 
	private BigDecimal taskListId;
		
	public void setReqClob(String reqClob) {
		this.reqClob = reqClob;
	}
	
	public String getReqClob() {
		return reqClob;
	}
	 public void setSubmitType(BigDecimal submitType) {
		this.submitType = submitType;
	}
	
	public BigDecimal getSubmitType() {
		return submitType;
	}
			 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
	 public void setRespClob(String respClob) {
		this.respClob = respClob;
	}
	
	public String getRespClob() {
		return respClob;
	}
				 public void setTaskListId(BigDecimal taskListId) {
		this.taskListId = taskListId;
	}
	
	public BigDecimal getTaskListId() {
		return taskListId;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimSubmitInfoVO [" +
				"reqClob="+reqClob+","+
"submitType="+submitType+","+
"listId="+listId+","+
"respClob="+respClob+","+
"taskListId="+taskListId+"]";
    }
}
