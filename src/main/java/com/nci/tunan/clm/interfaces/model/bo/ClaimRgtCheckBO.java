package com.nci.tunan.clm.interfaces.model.bo;

import java.math.BigDecimal;
import java.sql.Date;
import org.slf4j.Logger;
import com.nci.udmp.framework.model.BaseBO;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * 
 * @description 自定义数据复核机制 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class ClaimRgtCheckBO extends BaseBO {
    /**
     * 日志工具
     */
	private static Logger logger = LoggerFactory.getLogger();
	/**
     * 类型
     */
	private String types;
	/**
     * 插入人
     */
	private BigDecimal insertBy;
	/**
     * ruleValue
     */
	private String ruleValue;
	/**
     * 规则类型
     */
	private String ruleType;
	/**
     * 规则号
     */
	private String ruleNo;
	/**
     * 更新人
     */
	private BigDecimal updateBy;
	/**
     * 规则状态
     */
	private String ruleState;
	/**
     * 参数标识
     */
	private String paraFlag;
	/**
     * 插入时间戳
     */
	private Date insertTimestamp;
	/**
     * checkRuleId
     */
	private BigDecimal checkRuleId;
	/**
     * 更新时间戳
     */
	private Date updateTimestamp;
	/**
     * 插入时间
     */
	private Date insertTime;
	/**
     * 更新时间
     */
	private Date updateTime;

	public String getTypes() {
		return types;
	}

	public void setTypes(String types) {
		this.types = types;
	}

	public BigDecimal getInsertBy() {
		return insertBy;
	}

	public void setInsertBy(BigDecimal insertBy) {
		this.insertBy = insertBy;
	}

	public String getRuleValue() {
		return ruleValue;
	}

	public void setRuleValue(String ruleValue) {
		this.ruleValue = ruleValue;
	}

	public String getRuleType() {
		return ruleType;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}

	public String getRuleNo() {
		return ruleNo;
	}

	public void setRuleNo(String ruleNo) {
		this.ruleNo = ruleNo;
	}

	public BigDecimal getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(BigDecimal updateBy) {
		this.updateBy = updateBy;
	}

	public String getRuleState() {
		return ruleState;
	}

	public void setRuleState(String ruleState) {
		this.ruleState = ruleState;
	}

	public String getParaFlag() {
		return paraFlag;
	}

	public void setParaFlag(String paraFlag) {
		this.paraFlag = paraFlag;
	}

	public Date getInsertTimestamp() {
		return insertTimestamp;
	}

	public void setInsertTimestamp(Date insertTimestamp) {
		this.insertTimestamp = insertTimestamp;
	}

	public BigDecimal getCheckRuleId() {
		return checkRuleId;
	}

	public void setCheckRuleId(BigDecimal checkRuleId) {
		this.checkRuleId = checkRuleId;
	}

	public Date getUpdateTimestamp() {
		return updateTimestamp;
	}

	public void setUpdateTimestamp(Date updateTimestamp) {
		this.updateTimestamp = updateTimestamp;
	}

	public Date getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public String getBizId() {
		return null;
	}

}
