package com.nci.tunan.clm.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 调查浏览
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class SurveyBrowseVO extends BaseVO {
    /**
     * 浏览主键ID
     */
	private BigDecimal browseId;
	/**
	 * 赔案id
	 */
	private BigDecimal caseId;
	/**
	 * 调查结论id
	 */
	private BigDecimal conclusionId;
	/**
	 * 审核人id
	 */
	private BigDecimal auditorId;
	/**
	 * 浏览时间
	 */
	private Date browseTime;
	public BigDecimal getBrowseId() {
		return browseId;
	}
	public void setBrowseId(BigDecimal browseId) {
		this.browseId = browseId;
	}
	public BigDecimal getCaseId() {
		return caseId;
	}
	public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	public BigDecimal getConclusionId() {
		return conclusionId;
	}
	public void setConclusionId(BigDecimal conclusionId) {
		this.conclusionId = conclusionId;
	}
	public BigDecimal getAuditorId() {
		return auditorId;
	}
	public void setAuditorId(BigDecimal auditorId) {
		this.auditorId = auditorId;
	}
	public Date getBrowseTime() {
		return browseTime;
	}
	public void setBrowseTime(Date browseTime) {
		this.browseTime = browseTime;
	}
	@Override
	public String getBizId() {
		return null;
	}
}
