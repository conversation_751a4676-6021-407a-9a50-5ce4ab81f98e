package com.nci.tunan.clm.interfaces.model.po;

import java.math.BigDecimal;
import java.lang.String;
import com.nci.udmp.framework.model.BasePO;

/** 
 * @description ProductLifePO对象
 * <AUTHOR> 
 * @date 2015-12-30 20:01:54  
 */
public class ProductLifePO extends BasePO {
 	/** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
	// optionType  ---  String  ---  CHAR_1_0_必选/可选 0 必须 1 可选;
		// businessProdId  ---  BigDecimal  ---  NUMBER_16_0_业务产品id;
		// productId  ---  BigDecimal  ---  NUMBER_16_0_精算产品ID/责任组id;
		// occupationAddfeeUnit  ---  BigDecimal  ---  NUMBER_10_0_职业加费单位;
		// productName  ---  String  ---  VARCHAR2_100_0_责任组名称;
		// counterWay  ---  String  ---  CHAR_1_0_保费计算方向;
		// premiumUnit  ---  BigDecimal  ---  NUMBER_10_0_单位保费;
		// healthAddfeeUnit  ---  BigDecimal  ---  NUMBER_10_0_健康加费单位;
		// hobbyAddfeeUnit  ---  BigDecimal  ---  NUMBER_10_0_爱好加费单位;
		// internalId  ---  String  ---  VARCHAR2_20_0_精算产品编码;
		// cashvalueUnit  ---  BigDecimal  ---  NUMBER_10_0_现价单位;
		// saUnit  ---  BigDecimal  ---  NUMBER_10_0_单位保额;
	
		public void setOptionType(String optionType){
		setString("option_type", optionType);
	}
	
	public String getOptionType(){
		return getString("option_type");
	}
		public void setBusinessPrdId(BigDecimal businessProdId){
		setBigDecimal("business_prd_id", businessProdId);
	}
	
	public BigDecimal getBusinessPrdId(){
		return getBigDecimal("business_prd_id");
	}
		public void setProductId(BigDecimal productId){
		setBigDecimal("product_id", productId);
	}
	
	public BigDecimal getProductId(){
		return getBigDecimal("product_id");
	}
		public void setOccupationAddfeeUnit(BigDecimal occupationAddfeeUnit){
		setBigDecimal("occupation_addfee_unit", occupationAddfeeUnit);
	}
	
	public BigDecimal getOccupationAddfeeUnit(){
		return getBigDecimal("occupation_addfee_unit");
	}
		public void setProductName(String productName){
		setString("product_name", productName);
	}
	
	public String getProductName(){
		return getString("product_name");
	}
		public void setCounterWay(String counterWay){
		setString("counter_way", counterWay);
	}
	
	public String getCounterWay(){
		return getString("counter_way");
	}
		public void setPremiumUnit(BigDecimal premiumUnit){
		setBigDecimal("premium_unit", premiumUnit);
	}
	
	public BigDecimal getPremiumUnit(){
		return getBigDecimal("premium_unit");
	}
		public void setHealthAddfeeUnit(BigDecimal healthAddfeeUnit){
		setBigDecimal("health_addfee_unit", healthAddfeeUnit);
	}
	
	public BigDecimal getHealthAddfeeUnit(){
		return getBigDecimal("health_addfee_unit");
	}
		public void setHobbyAddfeeUnit(BigDecimal hobbyAddfeeUnit){
		setBigDecimal("hobby_addfee_unit", hobbyAddfeeUnit);
	}
	
	public BigDecimal getHobbyAddfeeUnit(){
		return getBigDecimal("hobby_addfee_unit");
	}
		public void setInternalId(String internalId){
		setString("internal_id", internalId);
	}
	
	public String getInternalId(){
		return getString("internal_id");
	}
		public void setCashvalueUnit(BigDecimal cashvalueUnit){
		setBigDecimal("cashvalue_unit", cashvalueUnit);
	}
	
	public BigDecimal getCashvalueUnit(){
		return getBigDecimal("cashvalue_unit");
	}
		public void setSaUnit(BigDecimal saUnit){
		setBigDecimal("sa_unit", saUnit);
	}
	
	public BigDecimal getSaUnit(){
		return getBigDecimal("sa_unit");
	}
		
	@Override
    public String toString() {
        return "ProductLifePO [" +
				"optionType="+getOptionType()+","+
"businessPrdId="+getBusinessPrdId()+","+
"productId="+getProductId()+","+
"occupationAddfeeUnit="+getOccupationAddfeeUnit()+","+
"productName="+getProductName()+","+
"counterWay="+getCounterWay()+","+
"premiumUnit="+getPremiumUnit()+","+
"healthAddfeeUnit="+getHealthAddfeeUnit()+","+
"hobbyAddfeeUnit="+getHobbyAddfeeUnit()+","+
"internalId="+getInternalId()+","+
"cashvalueUnit="+getCashvalueUnit()+","+
"saUnit="+getSaUnit()+"]";
    }	
 }
