
package com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>hop complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�����ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="hop"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="frms_hospitalname"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_hospcode"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_hospital_grade"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "hop", propOrder = {
    "frmsHospitalname",
    "frmsHospitalno",
    "frmsHospcode",
    "frmsHospitalGrade"
})
public class Hop {

    @XmlElement(name = "frms_hospitalname", required = true)
    protected String frmsHospitalname;
    @XmlElement(name = "frms_hospitalno", required = true)
    protected String frmsHospitalno;
    @XmlElement(name = "frms_hospcode", required = true)
    protected String frmsHospcode;
    @XmlElement(name = "frms_hospital_grade", required = true)
    protected String frmsHospitalGrade;

    /**
     * ��ȡfrmsHospitalname���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsHospitalname() {
        return frmsHospitalname;
    }

    /**
     * ����frmsHospitalname���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsHospitalname(String value) {
        this.frmsHospitalname = value;
    }

    /**
     * ��ȡfrmsHospcode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsHospcode() {
        return frmsHospcode;
    }

    /**
     * ����frmsHospcode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsHospcode(String value) {
        this.frmsHospcode = value;
    }

    /**
     * ��ȡfrmsHospitalGrade���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsHospitalGrade() {
        return frmsHospitalGrade;
    }

    /**
     * ����frmsHospitalGrade���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsHospitalGrade(String value) {
        this.frmsHospitalGrade = value;
    }

	public String getFrmsHospitalno() {
		return frmsHospitalno;
	}

	public void setFrmsHospitalno(String frmsHospitalno) {
		this.frmsHospitalno = frmsHospitalno;
	}

}
