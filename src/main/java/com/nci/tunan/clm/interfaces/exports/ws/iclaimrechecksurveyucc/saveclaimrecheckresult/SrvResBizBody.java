package com.nci.tunan.clm.interfaces.exports.ws.iclaimrechecksurveyucc.saveclaimrecheckresult;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody")
public class SrvResBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private final static  long serialVersionUID = 1L;
    
    @XmlElement(name = "OutputData")
    protected com.nci.tunan.clm.interfaces.vo.claimReCheck.ClaimReCheckResultResVO outputData;

    public com.nci.tunan.clm.interfaces.vo.claimReCheck.ClaimReCheckResultResVO getOutputData() {
        return outputData;
    }
    public void setOutputData(com.nci.tunan.clm.interfaces.vo.claimReCheck.ClaimReCheckResultResVO outputData) {
        this.outputData = outputData;
    }
}



