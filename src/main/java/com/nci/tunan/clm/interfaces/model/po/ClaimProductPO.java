package com.nci.tunan.clm.interfaces.model.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

/**
 * @description ClaimProductPO对象
 * <AUTHOR>
 * @date 2015-07-23 15:06:51
 */
public class ClaimProductPO extends BasePO {
    /** 属性 --- java类型 --- oracle类型_数据长度_小数位长度_注释信息 */
    // actualPay --- BigDecimal --- NUMBER_18_2_实际给付金额;
    // productId --- BigDecimal --- NUMBER_7_0_产品编码;
    // waiveStart --- Date --- DATE_7_0_豁免起期;
    // claimProdId --- BigDecimal --- NUMBER_16_0_序列号;
    // itemId --- BigDecimal --- NUMBER_16_0_责任组ID;
    // insuredId --- BigDecimal --- NUMBER_10_0_出险人ID;
    // caseId --- BigDecimal --- NUMBER_16_0_赔案ID;
    // isWaived --- BigDecimal --- NUMBER_1_0_豁免标识;
    // policyCode --- String --- VARCHAR2_20_0_保单号;
    // waiveAmt --- BigDecimal --- NUMBER_18_2_豁免总金额;
    // waiveEnd --- Date --- DATE_7_0_豁免止期;
    // waiveReason --- String --- CHAR_2_0_豁免原因;
    // busiItemId --- BigDecimal --- NUMBER_16_0_业务险种ID;
    // calcPay --- BigDecimal --- NUMBER_18_2_理算金额;
    // policyId --- BigDecimal --- NUMBER_16_0_保单ID;
    // basicPay --- BigDecimal --- NUMBER_18_2_基础给付金额;
	// amount --- BigDecimal --- NUMBER_18_2_扣减后的保额;
	
	public void setBusiProdCode(String busiProdCode) {
		setString("busi_prod_code", busiProdCode);
	}
	
	public String getBusiProdCode() {
		return getString("busi_prod_code");
	}
	public void setPolicyUniversalOutbounsamoun(BigDecimal policyUniversalOutbounsamoun) {
		setBigDecimal("policy_universal_outbounsamoun", policyUniversalOutbounsamoun);
	}
	
	public BigDecimal getPolicyUniversalOutbounsamoun() {
		return getBigDecimal("policy_universal_outbounsamoun");
	}
	public void setDeductOmnipotentAccount(BigDecimal deductOmnipotentAccount) {
		setBigDecimal("deduct_omnipotent_account", deductOmnipotentAccount);
	}
	
	public BigDecimal getDeductOmnipotentAccount() {
		return getBigDecimal("deduct_omnipotent_account");
	}
	public void setBonusInterestFee(BigDecimal bonusInterestFee) {
		setBigDecimal("bonus_interest_fee", bonusInterestFee);
	}
	
	public BigDecimal getBonusInterestFee() {
		return getBigDecimal("bonus_interest_fee");
	}
	public void setInterestAccountFee(BigDecimal interestAccountFee) {
		setBigDecimal("interest_account_fee", interestAccountFee);
	}
	
	public BigDecimal getInterestAccountFee() {
		return getBigDecimal("interest_account_fee");
	}
	public void setAmount(BigDecimal amount) {
        setBigDecimal("amount", amount);
    }

    public BigDecimal getAmount() {
        return getBigDecimal("amount");
    }
	
    public void setActualPay(BigDecimal actualPay) {
        setBigDecimal("actual_pay", actualPay);
    }

    public BigDecimal getActualPay() {
        return getBigDecimal("actual_pay");
    }

    public void setProductId(BigDecimal productId) {
        setBigDecimal("product_id", productId);
    }

    public BigDecimal getProductId() {
        return getBigDecimal("product_id");
    }

    public void setWaiveStart(Date waiveStart) {
        setUtilDate("waive_start", waiveStart);
    }

    public Date getWaiveStart() {
        return getUtilDate("waive_start");
    }

    public void setClaimProdId(BigDecimal claimProdId) {
        setBigDecimal("claim_prod_id", claimProdId);
    }

    public BigDecimal getClaimProdId() {
        return getBigDecimal("claim_prod_id");
    }

    public void setItemId(BigDecimal itemId) {
        setBigDecimal("item_id", itemId);
    }

    public BigDecimal getItemId() {
        return getBigDecimal("item_id");
    }

    public void setInsuredId(BigDecimal insuredId) {
        setBigDecimal("insured_id", insuredId);
    }

    public BigDecimal getInsuredId() {
        return getBigDecimal("insured_id");
    }

    public void setCaseId(BigDecimal caseId) {
        setBigDecimal("case_id", caseId);
    }

    public BigDecimal getCaseId() {
        return getBigDecimal("case_id");
    }

    public void setIsWaived(BigDecimal isWaived) {
        setBigDecimal("is_waived", isWaived);
    }

    public BigDecimal getIsWaived() {
        return getBigDecimal("is_waived");
    }

    public void setPolicyCode(String policyCode) {
        setString("policy_code", policyCode);
    }

    public String getPolicyCode() {
        return getString("policy_code");
    }

    public void setWaiveAmt(BigDecimal waiveAmt) {
        setBigDecimal("waive_amt", waiveAmt);
    }

    public BigDecimal getWaiveAmt() {
        return getBigDecimal("waive_amt");
    }

    public void setWaiveEnd(Date waiveEnd) {
        setUtilDate("waive_end", waiveEnd);
    }

    public Date getWaiveEnd() {
        return getUtilDate("waive_end");
    }

    public void setWaiveReason(String waiveReason) {
        setString("waive_reason", waiveReason);
    }

    public String getWaiveReason() {
        return getString("waive_reason");
    }

    public void setBusiItemId(BigDecimal busiItemId) {
        setBigDecimal("busi_item_id", busiItemId);
    }

    public BigDecimal getBusiItemId() {
        return getBigDecimal("busi_item_id");
    }

    public void setCalcPay(BigDecimal calcPay) {
        setBigDecimal("calc_pay", calcPay);
    }

    public BigDecimal getCalcPay() {
        return getBigDecimal("calc_pay");
    }

    public void setPolicyId(BigDecimal policyId) {
        setBigDecimal("policy_id", policyId);
    }

    public BigDecimal getPolicyId() {
        return getBigDecimal("policy_id");
    }

    public void setBasicPay(BigDecimal basicPay) {
        setBigDecimal("basic_pay", basicPay);
    }

    public BigDecimal getBasicPay() {
        return getBigDecimal("basic_pay");
    }
    public void setLiabilityState(BigDecimal liabilityState){
        setBigDecimal("liability_state", liabilityState);
    }
    public BigDecimal getLiabilityState(){
        return getBigDecimal("liability_state");
    }
    public void setEndCause(String endCause){
    	setString("end_cause", endCause);
    }
    public String getEndCause(){
    	return getString("end_cause");
    }
    public void setValidateDate(Date validateDate){
    	setUtilDate("validate_date", validateDate);
    }
    public Date getValidateDate(){
    	return getUtilDate("validate_date");
    }
    public void setExpiryDate(Date expiryDate){
    	setUtilDate("expiry_date", expiryDate);
    }
    public Date getExpiryDate(){
    	return getUtilDate("expiry_date");
    }
    @Override
    public String toString() {
        return "ClaimProductPO [" + "actualPay=" + getActualPay() + "," + "productId=" + getProductId() + ","
                + "waiveStart=" + getWaiveStart() + "," + "claimProdId=" + getClaimProdId() + "," + "itemId="
                + getItemId() + "," + "insuredId=" + getInsuredId() + "," + "caseId=" + getCaseId() + "," + "isWaived="
                + getIsWaived() + "," + "policyCode=" + getPolicyCode() + "," + "waiveAmt=" + getWaiveAmt() + ","
                + "waiveEnd=" + getWaiveEnd() + "," + "waiveReason=" + getWaiveReason() + "," + "busiItemId="
                + getBusiItemId() + "," + "calcPay=" + getCalcPay() + "," + "policyId=" + getPolicyId() + ","
                + "basicPay=" + getBasicPay() + "]";
    }
}
