package com.nci.tunan.clm.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description ClaimNopayPrepareVO对象
 * <AUTHOR> 
 * @date 2023-12-18 10:43:13  
 */
public class ClaimNopayPrepareBO extends BaseBO {	
	 /** 
	* @Fields rptrName :  报案受理人
 	*/ 
	private String rptrName;
	 /** 
	* @Fields claimType :  理赔类型
 	*/ 
	private String claimType;
	 /** 
	* @Fields caseFrom :  案件来源
 	*/ 
	private String caseFrom;
	 /** 
	* @Fields accResult2 :  出险结果2
 	*/ 
	private String accResult2;
	 /** 
	* @Fields accResult1 :  出险结果1
 	*/ 
	private String accResult1;
	 /** 
	* @Fields busiProdName :  出险险种简称
 	*/ 
	private String busiProdName;
	 /** 
	* @Fields preCalcAmnt :  调整前预估金额
 	*/ 
	private BigDecimal preCalcAmnt;
	 /** 
	* @Fields caseNo :  赔案号
 	*/ 
	private String caseNo;
	 /** 
	* @Fields countDate :  统计日期
 	*/ 
	private Date countDate;
	 /** 
	* @Fields rptrDate :  报案日期
 	*/ 
	private Date rptrDate;
	 /** 
	* @Fields busiProdCode :  出险险种代码
 	*/ 
	private String busiProdCode;
	 /** 
	* @Fields surveyMainBy :  调查主管代码
 	*/ 
	private String surveyMainBy;
		 /** 
	* @Fields organCode :  保单管理机构
 	*/ 
	private String organCode;
		 /** 
	* @Fields listId :  主键
 	*/ 
	private BigDecimal listId;
	 /** 
	* @Fields calcAmnt :  预估理赔金额
 	*/ 
	private BigDecimal calcAmnt;
	 /** 
	* @Fields accReason :  出险原因
 	*/ 
	private String accReason;
			 /** 
	* @Fields insuredName :  被保险人姓名
 	*/ 
	private String insuredName;
	 /** 
	* @Fields isEliminate :  剔除标识
 	*/ 
	private String isEliminate;
	 /** 
	* @Fields amount :  出险险种基本保额
 	*/ 
	private BigDecimal amount;
	 /** 
	* @Fields organCode2 :  保单中支代码
 	*/ 
	private String organCode2;
	 /** 
	* @Fields organCode1 :  保单分公司代码
 	*/ 
	private String organCode1;
	 /** 
	* @Fields policyCode :  保单号
 	*/ 
	private String policyCode;
	 /** 
	* @Fields claimDate :  出险日期
 	*/ 
	private Date claimDate;
		 /** 
	* @Fields createDate :  清单生成日期
 	*/ 
	private Date createDate;
	 /** 
	* @Fields surveyBy :  调查人代码
 	*/ 
	private String surveyBy;
		 /** 
	* @Fields registerName :  立案人
 	*/ 
	private String registerName;
		
	 public void setRptrName(String rptrName) {
		this.rptrName = rptrName;
	}
	
	public String getRptrName() {
		return rptrName;
	}
	 public void setClaimType(String claimType) {
		this.claimType = claimType;
	}
	
	public String getClaimType() {
		return claimType;
	}
	 public void setCaseFrom(String caseFrom) {
		this.caseFrom = caseFrom;
	}
	
	public String getCaseFrom() {
		return caseFrom;
	}
	 public void setAccResult2(String accResult2) {
		this.accResult2 = accResult2;
	}
	
	public String getAccResult2() {
		return accResult2;
	}
	 public void setAccResult1(String accResult1) {
		this.accResult1 = accResult1;
	}
	
	public String getAccResult1() {
		return accResult1;
	}
	 public void setBusiProdName(String busiProdName) {
		this.busiProdName = busiProdName;
	}
	
	public String getBusiProdName() {
		return busiProdName;
	}
	 public void setPreCalcAmnt(BigDecimal preCalcAmnt) {
		this.preCalcAmnt = preCalcAmnt;
	}
	
	public BigDecimal getPreCalcAmnt() {
		return preCalcAmnt;
	}
	 public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}
	
	public String getCaseNo() {
		return caseNo;
	}
	 public void setCountDate(Date countDate) {
		this.countDate = countDate;
	}
	
	public Date getCountDate() {
		return countDate;
	}
	 public void setRptrDate(Date rptrDate) {
		this.rptrDate = rptrDate;
	}
	
	public Date getRptrDate() {
		return rptrDate;
	}
	 public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}
	
	public String getBusiProdCode() {
		return busiProdCode;
	}
	 public void setSurveyMainBy(String surveyMainBy) {
		this.surveyMainBy = surveyMainBy;
	}
	
	public String getSurveyMainBy() {
		return surveyMainBy;
	}
		 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
		 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
	 public void setCalcAmnt(BigDecimal calcAmnt) {
		this.calcAmnt = calcAmnt;
	}
	
	public BigDecimal getCalcAmnt() {
		return calcAmnt;
	}
	 public void setAccReason(String accReason) {
		this.accReason = accReason;
	}
	
	public String getAccReason() {
		return accReason;
	}
			 public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}
	
	public String getInsuredName() {
		return insuredName;
	}
	 public void setIsEliminate(String isEliminate) {
		this.isEliminate = isEliminate;
	}
	
	public String getIsEliminate() {
		return isEliminate;
	}
	 public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	
	public BigDecimal getAmount() {
		return amount;
	}
	 public void setOrganCode2(String organCode2) {
		this.organCode2 = organCode2;
	}
	
	public String getOrganCode2() {
		return organCode2;
	}
	 public void setOrganCode1(String organCode1) {
		this.organCode1 = organCode1;
	}
	
	public String getOrganCode1() {
		return organCode1;
	}
	 public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	
	public String getPolicyCode() {
		return policyCode;
	}
	 public void setClaimDate(Date claimDate) {
		this.claimDate = claimDate;
	}
	
	public Date getClaimDate() {
		return claimDate;
	}
		 public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	
	public Date getCreateDate() {
		return createDate;
	}
	 public void setSurveyBy(String surveyBy) {
		this.surveyBy = surveyBy;
	}
	
	public String getSurveyBy() {
		return surveyBy;
	}
		 public void setRegisterName(String registerName) {
		this.registerName = registerName;
	}
	
	public String getRegisterName() {
		return registerName;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimNopayPrepareVO [" +
				"rptrName="+rptrName+","+
"claimType="+claimType+","+
"caseFrom="+caseFrom+","+
"accResult2="+accResult2+","+
"accResult1="+accResult1+","+
"busiProdName="+busiProdName+","+
"preCalcAmnt="+preCalcAmnt+","+
"caseNo="+caseNo+","+
"countDate="+countDate+","+
"rptrDate="+rptrDate+","+
"busiProdCode="+busiProdCode+","+
"surveyMainBy="+surveyMainBy+","+
"organCode="+organCode+","+
"listId="+listId+","+
"calcAmnt="+calcAmnt+","+
"accReason="+accReason+","+
"insuredName="+insuredName+","+
"isEliminate="+isEliminate+","+
"amount="+amount+","+
"organCode2="+organCode2+","+
"organCode1="+organCode1+","+
"policyCode="+policyCode+","+
"claimDate="+claimDate+","+
"createDate="+createDate+","+
"surveyBy="+surveyBy+","+
"registerName="+registerName+"]";
    }
}
