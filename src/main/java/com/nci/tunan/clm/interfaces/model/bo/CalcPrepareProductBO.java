package com.nci.tunan.clm.interfaces.model.bo;

import java.math.BigDecimal;
import java.util.List;

import com.nci.tunan.prd.interfaces.calc.exports.dutymatetruthfigure.vo.ProductInfoReqVO;
import com.nci.udmp.framework.model.BaseBO;
/**
 * 
 * @description 预理算相关
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class CalcPrepareProductBO extends BaseBO{
	/**
	 * @Fields policyId : 险种所属保单id
	 */
	private BigDecimal policyId;
	/**
	 * @Fields policyId : 险种所属保单id
	 */
	private String policyCode;
	/**
     * @Fields mainBusiPrdId : （出险险种的主险id）
     */
    private BigDecimal mainBusiPrdId;
    /**
     * @Fields mainBusiItemId : （出险险种的主险业务id）
     */
    private BigDecimal mainBusiItemId;
    /**
     * @Fields busiPrdId : （出险险种id）
     */
    private BigDecimal busiPrdId;
    /**
     * @Fields busiItemId : （出险险种业务id）
     */
    private BigDecimal busiItemId;
    /**
     * @Fields productReqVOList : 责任组信息集合
     */
    private List<ProductInfoReqVO> productInfoReqVOList;
    /**
     * @Fields isAccidentPolicyHolder : 出险人是否为投保人(0-否，1-是)
     */
    private String isAccidentPolicyHolder;
    /**
     * @Fields isAccidentFirstInsured : 出险人是否为第一被保人(0-否，1-是)
     */
    private String isAccidentFirstInsured;
    /**
     * @Fields isAccidentSecondInsured : 出险人是否为第二被保人(0-否，1-是)
     */
    private String isAccidentSecondInsured;
    /** 
    * @Fields isConvertPolicy : 是否为转换保单 
    */ 
    private String isConvertPolicy;
    
	public String getIsConvertPolicy() {
		return isConvertPolicy;
	}

	public void setIsConvertPolicy(String isConvertPolicy) {
		this.isConvertPolicy = isConvertPolicy;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}

	public BigDecimal getMainBusiItemId() {
		return mainBusiItemId;
	}

	public void setMainBusiItemId(BigDecimal mainBusiItemId) {
		this.mainBusiItemId = mainBusiItemId;
	}

	public BigDecimal getBusiItemId() {
		return busiItemId;
	}

	public void setBusiItemId(BigDecimal busiItemId) {
		this.busiItemId = busiItemId;
	}

	public BigDecimal getMainBusiPrdId() {
		return mainBusiPrdId;
	}

	public void setMainBusiPrdId(BigDecimal mainBusiPrdId) {
		this.mainBusiPrdId = mainBusiPrdId;
	}

	public BigDecimal getBusiPrdId() {
		return busiPrdId;
	}

	public void setBusiPrdId(BigDecimal busiPrdId) {
		this.busiPrdId = busiPrdId;
	}

	public List<ProductInfoReqVO> getProductInfoReqVOList() {
		return productInfoReqVOList;
	}

	public void setProductInfoReqVOList(List<ProductInfoReqVO> productInfoReqVOList) {
		this.productInfoReqVOList = productInfoReqVOList;
	}

	public String getIsAccidentPolicyHolder() {
		return isAccidentPolicyHolder;
	}

	public void setIsAccidentPolicyHolder(String isAccidentPolicyHolder) {
		this.isAccidentPolicyHolder = isAccidentPolicyHolder;
	}

	public String getIsAccidentFirstInsured() {
		return isAccidentFirstInsured;
	}

	public void setIsAccidentFirstInsured(String isAccidentFirstInsured) {
		this.isAccidentFirstInsured = isAccidentFirstInsured;
	}

	public String getIsAccidentSecondInsured() {
		return isAccidentSecondInsured;
	}

	public void setIsAccidentSecondInsured(String isAccidentSecondInsured) {
		this.isAccidentSecondInsured = isAccidentSecondInsured;
	}

	@Override
	public String getBizId() {
		
		return null;
	}
}
