package com.nci.tunan.clm.interfaces.peripheral.exports.r00101002470.iclaimendorsementucc.queryclaimendorsementinfo;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody")
public class SrvResBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(name = "OutputData")
    protected com.nci.tunan.clm.interfaces.peripheral.exports.r00101002470.vo.OutputData outputData;

    public com.nci.tunan.clm.interfaces.peripheral.exports.r00101002470.vo.OutputData getOutputData() {
        return outputData;
    }
    public void setOutputData(com.nci.tunan.clm.interfaces.peripheral.exports.r00101002470.vo.OutputData outputData) {
        this.outputData = outputData;
    }
}



