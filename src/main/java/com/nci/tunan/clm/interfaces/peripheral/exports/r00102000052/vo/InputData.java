package com.nci.tunan.clm.interfaces.peripheral.exports.r00102000052.vo;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlElement;

public class InputData implements Serializable{
    // 赔案号
    private String RptNo;
    // 出险人代码
    private String CustomerNo;
    // 单证代码
    private String AffixCode;
    // 单证名称
    private String AffixName;
    // 是否必需
    private String NeedFlag;
    // 单证件数
    private String ReadyCount;
    // 提交形式
    private String Property;
    // 操作员 文档上是String类型
    private String CCOperator; 
    // 管理机构
    private String MngCom;
    @XmlElement(name = "RptNo") 
    public String getRptNo() {
        return RptNo;
    }
    public void setRptNo(String rptNo) {
        RptNo = rptNo;
    }
    @XmlElement(name = "CustomerNo") 
    public String getCustomerNo() {
        return CustomerNo;
    }
    public void setCustomerNo(String customerNo) {
        CustomerNo = customerNo;
    }
    @XmlElement(name = "AffixCode") 
    public String getAffixCode() {
        return AffixCode;
    }
    public void setAffixCode(String affixCode) {
        AffixCode = affixCode;
    }
    @XmlElement(name = "AffixName") 
    public String getAffixName() {
        return AffixName;
    }
    public void setAffixName(String affixName) {
        AffixName = affixName;
    }
    @XmlElement(name = "NeedFlag") 
    public String getNeedFlag() {
        return NeedFlag;
    }
    public void setNeedFlag(String needFlag) {
        NeedFlag = needFlag;
    }
    @XmlElement(name = "ReadyCount") 
    public String getReadyCount() {
        return ReadyCount;
    }
    public void setReadyCount(String readyCount) {
        ReadyCount = readyCount;
    }
    @XmlElement(name = "Property") 
    public String getProperty() {
        return Property;
    }
    public void setProperty(String property) {
        Property = property;
    }
    @XmlElement(name = "CCOperator") 
    public String getCCOperator() {
        return CCOperator;
    }
    public void setCCOperator(String cCOperator) {
        CCOperator = cCOperator;
    }
    @XmlElement(name = "MngCom") 
    public String getMngCom() {
        return MngCom;
    }
    public void setMngCom(String mngCom) {
        MngCom = mngCom;
    }
    
    
   
    
}
