package com.nci.tunan.clm.interfaces.model.vo;

import java.math.BigDecimal;

import java.util.Date;
import java.util.List;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 调查申请表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class SurveyApplyVO extends BaseVO {	
	 /** 
	* @Fields relatedId :  业务关联号:分期给付号
 	*/ 
	private BigDecimal relatedId;
	 /** 
	* @Fields surveyType :  调查类型
 -紧急类
 -常规类
 -预付类
 	*/ 
	private BigDecimal surveyType;
	 /** 
	* @Fields applySection :  提起阶段
 	*/ 
	private BigDecimal applySection;
	 /** 
		* @Fields applySection :  提起阶段
	 	*/ 
		private String applySectionStr;
	 /** 
	* @Fields internalResualt :  内务调查结果
 	*/ 
	private String internalResualt;
	 /** 
	* @Fields surveyRuleId :  序号ID
 	*/ 
	private BigDecimal surveyRuleId;
	 /** 
	* @Fields surveyDocId :  生调通知书号
 	*/ 
	private BigDecimal surveyDocId;
	 /** 
	* @Fields surveyPer :  调查分配人
 	*/ 
	private BigDecimal surveyPer;
	 /** 
	* @Fields remark :  备注
 	*/ 
	private String remark;
	 /** 
	* @Fields surveyStatus :  调查状态
 	*/ 
	private BigDecimal surveyStatus;
	 /** 
	* @Fields applyDate :  发起日期
 	*/ 
	private Date applyDate;
	/** 
	 * @Fields applyDate :  发起日期Two
	 */ 
	private Date applyDateTwo;
	 /** 
	* @Fields surveyMode :  调查方式
 	*/ 
	private String surveyMode;
	 /** 
	* @Fields repealReason :  撤销原因
 	*/ 
	private String repealReason;
	 /** 
	* @Fields caseNo :  赔案号
 	*/ 
	private String caseNo;
	 /** 
	* @Fields applyCode :  投保单号
 	*/ 
	private String applyCode;
	 /** 
	* @Fields surveyDesc :  调查描述
 	*/ 
	private String surveyDesc;
			 /** 
	* @Fields applyOrg :  发起机构
 	*/ 
	private String applyOrg;
	 /** 
	* @Fields applyId :  主键流水号
 	*/ 
	private BigDecimal applyId;
	 /** 
	* @Fields bizType :  业务类型
 	*/ 
	private BigDecimal bizType;
	/** 
	 * @Fields bizTypeStr :  业务类型(String类型)
	 */ 
	private String bizTypeStr;
	 /** 
	* @Fields surveyAdvice :  调查指导意见
 	*/ 
	private String surveyAdvice;
	 /** 
	* @Fields surveyReason :  调查原因
 	*/ 
	private String surveyReason;
		 /** 
	* @Fields csApplyCode :  保全申请号
 	*/ 
	private String csApplyCode;
	 /** 
	* @Fields surveyOrg :  调查机构
 	*/ 
	private String surveyOrg;
		 /** 
	* @Fields csBackground :  保全业务背景介绍
 	*/ 
	private String csBackground;
	 /** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
	 /** 
	* @Fields csAcceptCode :  保全受理号
 	*/ 
	private String csAcceptCode;
	 /** 
	* @Fields policyCode :  保单号
 	*/ 
	private String policyCode;
	 /** 
	* @Fields surveyByLevel :  调查员等级
 	*/ 
	private BigDecimal surveyByLevel;
	 /** 
	* @Fields surveyCode :  调查编号
 	*/ 
	private String surveyCode;
	 /** 
	* @Fields applyPer :  发起调查人
 	*/ 
	private BigDecimal applyPer;
			 /** 
	* @Fields csItem :  保全项目
 	*/ 
	private String csItem;
    /** 
    * @Fields surveyConclusion :  调查结论
    */ 
    private String surveyConclusion;
    /** 
     * @Fields surveyRemark :  调查备注
     */ 
    private String surveyRemark;
     /** 
      * @Fields surveyItem :  调查项目
      */ 
    private String surveyItem;	
    /** 
     * @Fields surveyItem :  调查项目
     */ 
    private String surveyItemList;	
    /** 
     * @Fields customerId : 客户号
     */ 
    private BigDecimal customerId;
    /** 
     * @Fields positiveFlag : 阳性标识
     */ 
    private BigDecimal positiveFlag;
    /** 
     * @Fields customerVip :vip标识
     */ 
    private BigDecimal customerVip;
    /** 
     * @Fields finishDate :调查完成时间
     */ 
    private Date finishDate;
    /** 
     * @Fields aging :时效
     */ 
    private double aging;      
     
    /**
     * @Fields aging 调查确认结论 
     */
   private BigDecimal positiveConclusion; 
   
   /** 
    * @Fields aging :报案确认页面是否勾选调查
    */ 
   private String isSurveyChecked;     
   
   private BigDecimal priorityClaim; 
   
   private Date startDate; 
   
   private Date endDate; 
   
   private String organCode;
   
   
   /** 
	* @Fields applyDate :  发起日期
	*/ 
	private String applyDateStr;
	
	/** 
    * @Fields positiveFlag : 阳性标识
    */ 
   private String positiveFlagStr;
   
   /** 
	* @Fields surveyStatus :  调查状态
	*/ 
	private String surveyStatusStr;
	
	 /** 
	    * @Fields aging :申请时间
	    */ 
	private String insertTime;
	
	 /**
     * 受理人
     */
    private String acceptorName;
	   
    /**
     * 保单被保人姓名集合
     */
    private List<String> customerNames;
    
    
    private String flag;
    
    
    
   public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

public List<String> getCustomerNames() {
		return customerNames;
	}

	public void setCustomerNames(List<String> customerNames) {
		this.customerNames = customerNames;
	}

public String getAcceptorName() {
		return acceptorName;
	}

	public void setAcceptorName(String acceptorName) {
		this.acceptorName = acceptorName;
	}

public String getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(String insertTime) {
		this.insertTime = insertTime;
	}

public String getApplyDateStr() {
	return applyDateStr;
}

public void setApplyDateStr(String applyDateStr) {
	this.applyDateStr = applyDateStr;
}



public String getPositiveFlagStr() {
	return positiveFlagStr;
}

public void setPositiveFlagStr(String positiveFlagStr) {
	this.positiveFlagStr = positiveFlagStr;
}

public String getSurveyStatusStr() {
	return surveyStatusStr;
}

public void setSurveyStatusStr(String surveyStatusStr) {
	this.surveyStatusStr = surveyStatusStr;
}

public BigDecimal getPriorityClaim() {
	return priorityClaim;
}

public void setPriorityClaim(BigDecimal priorityClaim) {
	this.priorityClaim = priorityClaim;
}

public String getIsSurveyChecked() {
    return isSurveyChecked;
   }

    public void setIsSurveyChecked(String isSurveyChecked) {
        this.isSurveyChecked = isSurveyChecked;
    }

    public String getSurveyItemList() {
    	return surveyItemList;
    }
    
    public void setSurveyItemList(String surveyItemList) {
    	this.surveyItemList = surveyItemList;
    }
    
    public BigDecimal getPositiveConclusion() {
        return positiveConclusion;
    }
    
    public void setPositiveConclusion(BigDecimal positiveConclusion) {
        this.positiveConclusion = positiveConclusion;
    }
    
    public double getAging() {
           return aging;
    }

   public void setAging(double aging) {
       this.aging = aging;
   }

    public BigDecimal getCustomerId() {
        return customerId;
    }

    public void setCustomerId(BigDecimal customerId) {
        this.customerId = customerId;
    }

    public BigDecimal getPositiveFlag() {
        return positiveFlag;
    }

    public void setPositiveFlag(BigDecimal positiveFlag) {
        this.positiveFlag = positiveFlag;
    }

    public BigDecimal getCustomerVip() {
        return customerVip;
    }

    public void setCustomerVip(BigDecimal customerVip) {
        this.customerVip = customerVip;
    }

    public Date getFinishDate() {
        return finishDate;
    }

    public void setFinishDate(Date finishDate) {
        this.finishDate = finishDate;
    }

    public String getBizTypeStr() {
        return bizTypeStr;
    }

    public void setBizTypeStr(String bizTypeStr) {
        this.bizTypeStr = bizTypeStr;
    }

    public Date getApplyDateTwo() {
        return applyDateTwo;
    }

    public void setApplyDateTwo(Date applyDateTwo) {
        this.applyDateTwo = applyDateTwo;
    }

    public void setRelatedId(BigDecimal relatedId) {
		this.relatedId = relatedId;
	}
	
	public BigDecimal getRelatedId() {
		return relatedId;
	}
	 public void setSurveyType(BigDecimal surveyType) {
		this.surveyType = surveyType;
	}
	
	public BigDecimal getSurveyType() {
		return surveyType;
	}
	 public void setApplySection(BigDecimal applySection) {
		this.applySection = applySection;
	}
	
	public BigDecimal getApplySection() {
		return applySection;
	}
	 public void setInternalResualt(String internalResualt) {
		this.internalResualt = internalResualt;
	}
	
	public String getInternalResualt() {
		return internalResualt;
	}
	 public void setSurveyRuleId(BigDecimal surveyRuleId) {
		this.surveyRuleId = surveyRuleId;
	}
	
	public BigDecimal getSurveyRuleId() {
		return surveyRuleId;
	}
	 public void setSurveyDocId(BigDecimal surveyDocId) {
		this.surveyDocId = surveyDocId;
	}
	
	public BigDecimal getSurveyDocId() {
		return surveyDocId;
	}
	 public void setSurveyPer(BigDecimal surveyPer) {
		this.surveyPer = surveyPer;
	}
	
	public BigDecimal getSurveyPer() {
		return surveyPer;
	}
	 public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRemark() {
		return remark;
	}
	 public void setSurveyStatus(BigDecimal surveyStatus) {
		this.surveyStatus = surveyStatus;
	}
	
	public BigDecimal getSurveyStatus() {
		return surveyStatus;
	}
	 public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}
	
	public Date getApplyDate() {
		return applyDate;
	}
	 public void setSurveyMode(String surveyMode) {
		this.surveyMode = surveyMode;
	}
	
	public String getSurveyMode() {
		return surveyMode;
	}
	 public void setRepealReason(String repealReason) {
		this.repealReason = repealReason;
	}
	
	public String getRepealReason() {
		return repealReason;
	}
	 public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}
	
	public String getCaseNo() {
		return caseNo;
	}
	 public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}
	
	public String getApplyCode() {
		return applyCode;
	}
	 public void setSurveyDesc(String surveyDesc) {
		this.surveyDesc = surveyDesc;
	}
	
	public String getSurveyDesc() {
		return surveyDesc;
	}
			 public void setApplyOrg(String applyOrg) {
		this.applyOrg = applyOrg;
	}
	
	public String getApplyOrg() {
		return applyOrg;
	}
	 public void setApplyId(BigDecimal applyId) {
		this.applyId = applyId;
	}
	
	public BigDecimal getApplyId() {
		return applyId;
	}
	 public void setBizType(BigDecimal bizType) {
		this.bizType = bizType;
	}
	
	public BigDecimal getBizType() {
		return bizType;
	}
	 public void setSurveyAdvice(String surveyAdvice) {
		this.surveyAdvice = surveyAdvice;
	}
	
	public String getSurveyAdvice() {
		return surveyAdvice;
	}
	 public void setSurveyReason(String surveyReason) {
		this.surveyReason = surveyReason;
	}
	
	public String getSurveyReason() {
		return surveyReason;
	}
		 public void setCsApplyCode(String csApplyCode) {
		this.csApplyCode = csApplyCode;
	}
	
	public String getCsApplyCode() {
		return csApplyCode;
	}
	 public void setSurveyOrg(String surveyOrg) {
		this.surveyOrg = surveyOrg;
	}
	
	public String getSurveyOrg() {
		return surveyOrg;
	}
		 public void setCsBackground(String csBackground) {
		this.csBackground = csBackground;
	}
	
	public String getCsBackground() {
		return csBackground;
	}
	 public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	
	public BigDecimal getCaseId() {
		return caseId;
	}
	 public void setCsAcceptCode(String csAcceptCode) {
		this.csAcceptCode = csAcceptCode;
	}
	
	public String getCsAcceptCode() {
		return csAcceptCode;
	}
	 public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	
	public String getPolicyCode() {
		return policyCode;
	}
	 public void setSurveyByLevel(BigDecimal surveyByLevel) {
		this.surveyByLevel = surveyByLevel;
	}
	
	public BigDecimal getSurveyByLevel() {
		return surveyByLevel;
	}
	 public void setSurveyCode(String surveyCode) {
		this.surveyCode = surveyCode;
	}
	
	public String getSurveyCode() {
		return surveyCode;
	}
	 public void setApplyPer(BigDecimal applyPer) {
		this.applyPer = applyPer;
	}
	
	public BigDecimal getApplyPer() {
		return applyPer;
	}
			 public void setCsItem(String csItem) {
		this.csItem = csItem;
	}
	
	public String getCsItem() {
		return csItem;
	}
		
	public String getSurveyConclusion() {
        return surveyConclusion;
    }

    public void setSurveyConclusion(String surveyConclusion) {
        this.surveyConclusion = surveyConclusion;
    }

    public String getSurveyRemark() {
        return surveyRemark;
    }

    public void setSurveyRemark(String surveyRemark) {
        this.surveyRemark = surveyRemark;
    }

    public String getSurveyItem() {
        return surveyItem;
    }

    public void setSurveyItem(String surveyItem) {
        this.surveyItem = surveyItem;
    }

    public String getApplySectionStr() {
		return applySectionStr;
	}

	public void setApplySectionStr(String applySectionStr) {
		this.applySectionStr = applySectionStr;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	@Override
    public String getBizId() {
        return null;
    }

    @Override
	public String toString() {
		return "SurveyApplyVO [relatedId=" + relatedId + ", surveyType="
				+ surveyType + ", applySection=" + applySection
				+ ", applySectionStr=" + applySectionStr + ", internalResualt="
				+ internalResualt + ", surveyRuleId=" + surveyRuleId
				+ ", surveyDocId=" + surveyDocId + ", surveyPer=" + surveyPer
				+ ", remark=" + remark + ", surveyStatus=" + surveyStatus
				+ ", applyDate=" + applyDate + ", applyDateTwo=" + applyDateTwo
				+ ", surveyMode=" + surveyMode + ", repealReason="
				+ repealReason + ", caseNo=" + caseNo + ", applyCode="
				+ applyCode + ", surveyDesc=" + surveyDesc + ", applyOrg="
				+ applyOrg + ", applyId=" + applyId + ", bizType=" + bizType
				+ ", bizTypeStr=" + bizTypeStr + ", surveyAdvice="
				+ surveyAdvice + ", surveyReason=" + surveyReason
				+ ", csApplyCode=" + csApplyCode + ", surveyOrg=" + surveyOrg
				+ ", csBackground=" + csBackground + ", caseId=" + caseId
				+ ", csAcceptCode=" + csAcceptCode + ", policyCode="
				+ policyCode + ", surveyByLevel=" + surveyByLevel
				+ ", surveyCode=" + surveyCode + ", applyPer=" + applyPer
				+ ", csItem=" + csItem + ", surveyConclusion="
				+ surveyConclusion + ", surveyRemark=" + surveyRemark
				+ ", surveyItem=" + surveyItem + ", surveyItemList="
				+ surveyItemList + ", customerId=" + customerId
				+ ", positiveFlag=" + positiveFlag + ", customerVip="
				+ customerVip + ", finishDate=" + finishDate + ", aging="
				+ aging + ", positiveConclusion=" + positiveConclusion
				+ ", isSurveyChecked=" + isSurveyChecked + ", priorityClaim="
				+ priorityClaim + ", startDate=" + startDate + ", endDate="
				+ endDate + ", organCode=" + organCode + ", applyDateStr="
				+ applyDateStr + ", positiveFlagStr=" + positiveFlagStr
				+ ", surveyStatusStr=" + surveyStatusStr + ", insertTime="
				+ insertTime + ", acceptorName=" + acceptorName
				+ ", customerNames=" + customerNames + ", flag=" + flag + "]";
	}
}
