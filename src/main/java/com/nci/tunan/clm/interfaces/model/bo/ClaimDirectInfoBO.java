package com.nci.tunan.clm.interfaces.model.bo;

import com.nci.udmp.framework.model.*;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/** 
 * @description ClaimDirectInfoBO对象
 * <AUTHOR> 
 * @date 2024-10-09 10:18:45  
 */
public class ClaimDirectInfoBO extends BaseBO {	
	 /** 
	* @Fields firstDirTime :  首次调用直连时间
 	*/ 
	private Date firstDirTime;
		 /** 
	* @Fields getMedicalDateTime :  获取医疗数据时间
 	*/ 
	private Date getMedicalDateTime;
		 /** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
	 /** 
	* @Fields chenkFinishTime :  核查通过完成时间
 	*/ 
	private Date chenkFinishTime;
			 /** 
	* @Fields listId :  主键
 	*/ 
	private BigDecimal listId;
		 /** 
	* @Fields queryId :  快赔编码
 	*/ 
	private String queryId;
		 /** 
	* @Fields ifFeedbackClaimResult :  是否反馈理赔结果（T
 	*/ 
	private BigDecimal ifFeedbackClaimResult;
	
	/**
	 * @Fields connDeleBillNo : 直连删除账单号(存储直连数据核查明细删除的账单号,多个时以、分割)
	 */
	private String connDeleBillNo;	
	
	/** 
	* @Fields registerNo :  住院登记号(上海一码通直赔流程)
 	*/ 
	private String registerNo;
	/** 
	* @Fields revokeFlag :  退费标识（1-已退费，上海一码通直赔流程-ZP012-商保直赔撤销接口）
 	*/ 
	private BigDecimal revokeFlag;
	/** 
	* @Fields ymtpReportNo :  一码通赔报案唯一号(上海一码通直赔流程)
 	*/ 
	private String ymtpReportNo;
	/** 
	* @Fields preCalcInterTime :  理算预结算接口调用时间(上海一码通直赔流程，ZP010-商保直赔门诊住院预理算接口)
 	*/ 
	private Date preCalcInterTime;
	/** 
	* @Fields calcInterTime :  理算结算接口调用时间(上海一码通直赔流程，ZP011-商保直赔门诊住院理算结算接口)
 	*/ 
	private Date calcInterTime;
	/** 
	* @Fields cancelInterTime :  直赔撤销接口调用时间(上海一码通直赔流程，ZP012-商保直赔撤销接口)
 	*/ 
	private Date cancelInterTime;
	/** 
	* @Fields tempBussNo :  临时业务号(关联T_CLAIM_AUTH_CUSTOMER_INFO表的临时业务号)
 	*/ 
	private String tempBussNo;
		
	 public String getRegisterNo() {
		return registerNo;
	}

	public void setRegisterNo(String registerNo) {
		this.registerNo = registerNo;
	}

	public BigDecimal getRevokeFlag() {
		return revokeFlag;
	}

	public void setRevokeFlag(BigDecimal revokeFlag) {
		this.revokeFlag = revokeFlag;
	}

	public String getYmtpReportNo() {
		return ymtpReportNo;
	}

	public void setYmtpReportNo(String ymtpReportNo) {
		this.ymtpReportNo = ymtpReportNo;
	}

	public Date getPreCalcInterTime() {
		return preCalcInterTime;
	}

	public void setPreCalcInterTime(Date preCalcInterTime) {
		this.preCalcInterTime = preCalcInterTime;
	}

	public Date getCalcInterTime() {
		return calcInterTime;
	}

	public void setCalcInterTime(Date calcInterTime) {
		this.calcInterTime = calcInterTime;
	}

	public Date getCancelInterTime() {
		return cancelInterTime;
	}

	public void setCancelInterTime(Date cancelInterTime) {
		this.cancelInterTime = cancelInterTime;
	}

	public String getTempBussNo() {
		return tempBussNo;
	}

	public void setTempBussNo(String tempBussNo) {
		this.tempBussNo = tempBussNo;
	}

	public void setFirstDirTime(Date firstDirTime) {
		this.firstDirTime = firstDirTime;
	}
	
	public Date getFirstDirTime() {
		return firstDirTime;
	}
		 public void setGetMedicalDateTime(Date getMedicalDateTime) {
		this.getMedicalDateTime = getMedicalDateTime;
	}
	
	public Date getGetMedicalDateTime() {
		return getMedicalDateTime;
	}
		 public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	
	public BigDecimal getCaseId() {
		return caseId;
	}
	 public void setChenkFinishTime(Date chenkFinishTime) {
		this.chenkFinishTime = chenkFinishTime;
	}
	
	public Date getChenkFinishTime() {
		return chenkFinishTime;
	}
			 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
		 public void setQueryId(String queryId) {
		this.queryId = queryId;
	}
	
	public String getQueryId() {
		return queryId;
	}
		 public void setIfFeedbackClaimResult(BigDecimal ifFeedbackClaimResult) {
		this.ifFeedbackClaimResult = ifFeedbackClaimResult;
	}
	
	public BigDecimal getIfFeedbackClaimResult() {
		return ifFeedbackClaimResult;
	}
	
	
	public String getConnDeleBillNo() {
        return connDeleBillNo;
    }

    public void setConnDeleBillNo(String connDeleBillNo) {
        this.connDeleBillNo = connDeleBillNo;
    }

    @Override
    public String getBizId() {
        return null;
    }

	@Override
	public String toString() {
		return "ClaimDirectInfoBO [firstDirTime=" + firstDirTime
				+ ", getMedicalDateTime=" + getMedicalDateTime + ", caseId="
				+ caseId + ", chenkFinishTime=" + chenkFinishTime + ", listId="
				+ listId + ", queryId=" + queryId + ", ifFeedbackClaimResult="
				+ ifFeedbackClaimResult + ", registerNo=" + registerNo
				+ ", revokeFlag=" + revokeFlag + ", ymtpReportNo="
				+ ymtpReportNo + ", preCalcInterTime=" + preCalcInterTime
				+ ", calcInterTime=" + calcInterTime + ", cancelInterTime="
				+ cancelInterTime + ", tempBussNo=" + tempBussNo + "]";
	}
    
}
