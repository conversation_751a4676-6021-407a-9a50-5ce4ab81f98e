package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 医院等级表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class HospitalLevelVO extends BaseVO {	
	 /** 
	* @Fields name :  医院等级名称
 	*/ 
	private String name;
	 /** 
	* @Fields code :  医院等级代码
 	*/ 
	private String code;
		
	 public void setName(String name) {
		this.name = name;
	}
	
	public String getName() {
		return name;
	}
	 public void setCode(String code) {
		this.code = code;
	}
	
	public String getCode() {
		return code;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "HospitalLevelVO [" +
				"name="+name+","+
"code="+code+"]";
    }
}
