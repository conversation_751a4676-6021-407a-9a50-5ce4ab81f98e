package com.nci.tunan.clm.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseBO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @description 调查任务批次表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class ClaimSurveyBatchBO extends BaseBO {	
	 /** 
	* @Fields bizType :  业务类型
前置调查
复堪调查
 	*/ 
	private BigDecimal bizType;
	 /** 
	* @Fields surveyReason :  调查原因
 	*/ 
	private String surveyReason;
	 /** 
	* @Fields planId :  调查计划ID
 	*/ 
	private BigDecimal planId;
			 /** 
	* @Fields surveyMode :  调查方式
上载
抽取
 	*/ 
	private String surveyMode;
	 /** 
	* @Fields organCode :  上载机构
 	*/ 
	private String organCode;
		 /** 
	* @Fields uploadDate :  上载日期
 	*/ 
	private Date uploadDate;
	 /** 
	* @Fields planName :  调查名称
 	*/ 
	private String planName;
	 /** 
	* @Fields batchId :  主键流水号
 	*/ 
	private BigDecimal batchId;
		 /** 
	* @Fields extractRule :  外部提数规则
 	*/ 
	private String extractRule;
	/** 
	    * @Fields extractRule :  要生成复勘任务的赔案号
	    */ 
	private String caseNoS;
	/**
	 * 上载起日期
	 */
	private Date uploadDateStar;
	/**
	 * 上载止日期
	 */
	private Date uploadDateEnd;
	/**
	 * 操作人
	 */
	private String insertName;

	 public String getInsertName() {
		return insertName;
	}

	public void setInsertName(String insertName) {
		this.insertName = insertName;
	}

	public Date getUploadDateStar() {
		return uploadDateStar;
	}

	public void setUploadDateStar(Date uploadDateStar) {
		this.uploadDateStar = uploadDateStar;
	}

	public Date getUploadDateEnd() {
		return uploadDateEnd;
	}

	public void setUploadDateEnd(Date uploadDateEnd) {
		this.uploadDateEnd = uploadDateEnd;
	}

	public void setBizType(BigDecimal bizType) {
		this.bizType = bizType;
	}
	
	public BigDecimal getBizType() {
		return bizType;
	}
	 public void setSurveyReason(String surveyReason) {
		this.surveyReason = surveyReason;
	}
	
	public String getSurveyReason() {
		return surveyReason;
	}
	 public void setPlanId(BigDecimal planId) {
		this.planId = planId;
	}
	
	public BigDecimal getPlanId() {
		return planId;
	}
			 public void setSurveyMode(String surveyMode) {
		this.surveyMode = surveyMode;
	}
	
	public String getSurveyMode() {
		return surveyMode;
	}
	 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
		 public void setUploadDate(Date uploadDate) {
		this.uploadDate = uploadDate;
	}
	
	public Date getUploadDate() {
		return uploadDate;
	}
	 public void setPlanName(String planName) {
		this.planName = planName;
	}
	
	public String getPlanName() {
		return planName;
	}
	 public void setBatchId(BigDecimal batchId) {
		this.batchId = batchId;
	}
	
	public BigDecimal getBatchId() {
		return batchId;
	}
		 public void setExtractRule(String extractRule) {
		this.extractRule = extractRule;
	}
	
	public String getExtractRule() {
		return extractRule;
	}
				
	public String getCaseNoS() {
        return caseNoS;
    }

    public void setCaseNoS(String caseNoS) {
        this.caseNoS = caseNoS;
    }

    @Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimSurveyBatchbBO [" +
				"bizType="+bizType+","+
"surveyReason="+surveyReason+","+
"planId="+planId+","+
"surveyMode="+surveyMode+","+
"organCode="+organCode+","+
"uploadDate="+uploadDate+","+
"planName="+planName+","+
"batchId="+batchId+","+
"extractRule="+extractRule+"]";
    }
}
