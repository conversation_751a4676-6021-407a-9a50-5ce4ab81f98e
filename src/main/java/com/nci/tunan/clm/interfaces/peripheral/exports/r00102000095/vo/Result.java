package com.nci.tunan.clm.interfaces.peripheral.exports.r00102000095.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class Result implements Serializable {
	// 返回码
	private String ReturnCode;
	// 返回信息
	private String RerurnMsg;

	@XmlElement(name = "ReturnCode")
	public String getReturnCode() {
		return ReturnCode;
	}

	public void setReturnCode(String returnCode) {
		ReturnCode = returnCode;
	}

	@XmlElement(name = "RerurnMsg")
	public String getRerurnMsg() {
		return RerurnMsg;
	}

	public void setRerurnMsg(String rerurnMsg) {
		RerurnMsg = rerurnMsg;
	}

}
