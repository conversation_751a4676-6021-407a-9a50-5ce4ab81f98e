package com.nci.tunan.clm.interfaces.exports.ws.iqueryclaimcasemsgucc.queryclaimcasemsg;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody")
public class SrvResBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private final static  long serialVersionUID = 1L;
    
    @XmlElement(name = "OutputData")
    protected com.nci.tunan.clm.interfaces.vo.queryclaimtelephoneservicedata.QueryClaimTeleServiceResVO outputData;

    public com.nci.tunan.clm.interfaces.vo.queryclaimtelephoneservicedata.QueryClaimTeleServiceResVO getOutputData() {
        return outputData;
    }
    public void setOutputData(com.nci.tunan.clm.interfaces.vo.queryclaimtelephoneservicedata.QueryClaimTeleServiceResVO outputData) {
        this.outputData = outputData;
    }
}



