package com.nci.tunan.clm.interfaces.model.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

/**
 * @description 归档核对清单组合PO
 * <AUTHOR>
 * @date 2015年10月30日 下午2:06:37
 */
public class CheckFileListPO extends BasePO {

    // 归档核对清单
    public void setPages(BigDecimal pages) {
        setBigDecimal("pages", pages);
    }

    public BigDecimal getPages() {
        return getBigDecimal("pages");
    }
    
    public void setClaimType(String claimType) {
        setString("claim_type", claimType);
    }

    public String getClaimType() {
        return getString("claim_type");
    }
	
	
    public void setCaseNo(String caseNo) {
        setString("case_no", caseNo);
    }

    public String getCaseNo() {
        return getString("case_no");
    }
    public void setAuditorId(BigDecimal auditorId) {
    	setBigDecimal("auditor_id", auditorId);
    }
    
    public BigDecimal getAuditorId() {
    	return getBigDecimal("auditor_id");
    }

    public void setRegisteTime(Date registeTime) {
        setUtilDate("registe_time", registeTime);
    }

    public Date getRegisteTime() {
        return getUtilDate("registe_time");
    }

    public void setEndTime(Date endTime) {
        setUtilDate("end_time", endTime);
    }

    public Date getEndTime() {
        return getUtilDate("end_time");
    }
    
    public Date getStartEndCaseTime() {
        return getUtilDate("start_end_case_time");
    }

    public void setStartEndCaseTime(Date startEndCaseTime) {
        setUtilDate("start_end_case_time", startEndCaseTime);
    }

    public Date getEndCaseTime() {
        return getUtilDate("end_case_time");
    }

    public void setEndCaseTime(Date endCaseTime) {
        setUtilDate("end_case_time", endCaseTime);
    }

    public void setStartTime(Date startTime) {
        setUtilDate("start_time", startTime);
    }

    public Date getStartTime() {
        return getUtilDate("start_time");
    }

    public void setOrganCode(String organCode) {
        setString("organ_code", organCode);
    }

    public String getOrganCode() {
        return getString("organ_code");
    }

    public String getOrganName() {
        return getString("organ_name");
    }

    public void setOrganName(String organName) {
        setString("organ_name", organName);
    }

    public void setPolicyCode(String policyCode) {
        setString("policy_code", policyCode);
    }

    public String getPolicyCode() {
        return getString("policy_code");
    }

    public void setPolicyType(String policyType) {
        setString("policy_type", policyType);
    }

    public String getPolicyType() {
        return getString("policy_type");
    }

    public void setUserName(String userName) {
        setString("user_name", userName);
    }

    public String getUserName() {
        return getString("user_name");
    }

    public void setRealName(String realName) {
        setString("real_name", realName);
    }

    public String getRealName() {
        return getString("real_name");
    }

    public void setCustomerName(String customerName) {
        setString("customer_name", customerName);
    }

    public String getCustomerName() {
        return getString("customer_name");
    }

    /* 保费领取清单 */
    public void setCustomerCertiCode(String customerCertiCode) {
        setString("customer_certi_code", customerCertiCode);
    }

    public String getCustomerCertiCode() {
        return getString("customer_certi_code");
    }

    public void setMobileTel(String mobileTel) {
        setString("mobile_tel", mobileTel);
    }

    public String getMobileTel() {
        return getString("mobile_tel");
    }

    public void setPayeeName(String payeeName) {
        setString("payee_name", payeeName);
    }

    public String getPayeeName() {
        return getString("payee_name");
    }

    public void setPayeeRelation(String payeeRelation) {
        setString("payee_relation", payeeRelation);
    }

    public String getPayeeRelation() {
        return getString("payee_relation");
    }

    public void setInsuredName(String insuredName) {
        setString("insured_id_name", insuredName);
    }

    public String getInsuredName() {
        return getString("insured_id_name");
    }

    public void setCaseStatus(String caseStatus) {
        setString("case_status", caseStatus);
    }

    public String getCaseStatus() {
        return getString("case_status");
    }

    public void setCaseApplyType(BigDecimal caseApplyType) {
        setBigDecimal("case_apply_type", caseApplyType);
    }

    public BigDecimal getCaseApplyType() {
        return getBigDecimal("case_apply_type");
    }

    // 领取保费和业务员信息
    public void setFeeAmount(BigDecimal feeAmount) {
        setBigDecimal("fee_amount", feeAmount);
    }

    public BigDecimal getFeeAmount() {
        return getBigDecimal("fee_amount");
    }

    public void setAgentName(String agentName) {
        setString("agent_name", agentName);
    }

    public String getAgentName() {
        return getString("agent_name");
    }

    public void setAgentMobile(String agentMobile) {
        setString("agent_mobile", agentMobile);
    }

    public String getAgentMobile() {
        return getString("agent_mobile");
    }
    
    public String getScanBatchNo(){
        return getString("scan_batch_no");
    }
    
    public void setScanBatchNo(String scanBatchNo){
        setString("scan_batch_no", scanBatchNo);
    }
}
