package com.nci.tunan.clm.interfaces.model.bo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseBO;

/** 
 * @description ClaimInspectType1VO对象
 * <AUTHOR> 
 * @date 2023-06-27 14:46:00  
 */
public class ClaimInspectType1BO extends BaseBO {	
		 /** 
	* @Fields inspectType1Id :  主键序列
 	*/ 
	private BigDecimal inspectType1Id;
			 /** 
	* @Fields inspectType1Desc :  检查大类名称
 	*/ 
	private String inspectType1Desc;
					
		 public void setInspectType1Id(BigDecimal inspectType1Id) {
		this.inspectType1Id = inspectType1Id;
	}
	
	public BigDecimal getInspectType1Id() {
		return inspectType1Id;
	}
			 public void setInspectType1Desc(String inspectType1Desc) {
		this.inspectType1Desc = inspectType1Desc;
	}
	
	public String getInspectType1Desc() {
		return inspectType1Desc;
	}
					
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimInspectType1VO [" +
				"inspectType1Id="+inspectType1Id+","+
"inspectType1Desc="+inspectType1Desc+"]";
    }
}
