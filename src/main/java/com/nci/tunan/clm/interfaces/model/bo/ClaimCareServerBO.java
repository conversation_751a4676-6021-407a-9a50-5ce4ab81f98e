package com.nci.tunan.clm.interfaces.model.bo;

import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;
import com.nci.udmp.framework.model.BaseBO;

/**
 * 
 * @description 维护服务人员信息
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class ClaimCareServerBO extends BaseBO {
    /**
     * @Fields validFlag : 有效标志
     */
    private BigDecimal validFlag;
    /**
     * @Fields volunteerJob : 志愿者职务
     */
    private String volunteerJob;
    /**
     * @Fields volunteerRecord : 志愿履历
     */
    private String volunteerRecord;
    /**
     * @Fields name : 服务人员姓名
     */
    private String name;
    /**
     * @Fields workRecord : 工作履历
     */
    private String workRecord;
    /**
     * @Fields serverStartDate : 服务起期
     */
    private Date serverStartDate;
    /**
     * @Fields serverType : 服务人员类型
     */
    private BigDecimal serverType;
    /** 
     * @Fields serverTypeName :  服务人员类型名称
     */ 
     private String serverTypeName;
    /**
     * @Fields agentType : 业务员类型
     */
    private BigDecimal agentType;
    /**
     * @Fields phone : 电话
     */
    private String phone;
    /**
     * @Fields organCode : 服务机构
     */
    private String organCode;
    /** 
     * @Fields organCodeName :  服务机构名称
     */ 
     private String organCodeName;
    /**
     * @Fields entryDate : 入司日期
     */
    private Date entryDate;
    /**
     * @Fields certiCode : 证件号码
     */
    private String certiCode;
    /**
     * @Fields mobile : 手机号码
     */
    private String mobile;
    /**
     * @Fields volunteerLevel : 志愿者级别
     */
    private String volunteerLevel;
    /**
     * @Fields serverId : 服务人员ID
     */
    private BigDecimal serverId;
    /**
     * @Fields email : 电子邮箱
     */
    private String email;
    /**
     * @Fields certiType : 证件类型
     */
    private String certiType;
    /**
     * @Fields serverCode : 用户Id/业务员代码
     */
    private String serverCode;
    
    /** 
     * @Fields serverId :  服务人员类型-检索用
     */ 
     private BigDecimal serverTypesel;
     /** 
      * @Fields serverId :  服务人员ID-检索用
      */ 
     private BigDecimal serverIdsel;
      /** 
       * @Fields serverId :  姓名-检索用
       */ 
     private String namesel;
     /**
      * 有效标识中文显示
      */
     private String validFlagName;

    public String getValidFlagName() {
        return validFlagName;
    }

    public void setValidFlagName(String validFlagName) {
        this.validFlagName = validFlagName;
    }

    public BigDecimal getServerIdsel() {
        return serverIdsel;
    }

    public void setServerIdsel(BigDecimal serverIdsel) {
        this.serverIdsel = serverIdsel;
    }

    public String getNamesel() {
        return namesel;
    }

    public void setNamesel(String namesel) {
        this.namesel = namesel;
    }

    public BigDecimal getServerTypesel() {
        return serverTypesel;
    }

    public void setServerTypesel(BigDecimal serverTypesel) {
        this.serverTypesel = serverTypesel;
    }

    public void setValidFlag(BigDecimal validFlag) {
        this.validFlag = validFlag;
    }

    public BigDecimal getValidFlag() {
        return validFlag;
    }

    public void setVolunteerJob(String volunteerJob) {
        this.volunteerJob = volunteerJob;
    }

    public String getVolunteerJob() {
        return volunteerJob;
    }

    public void setVolunteerRecord(String volunteerRecord) {
        this.volunteerRecord = volunteerRecord;
    }

    public String getVolunteerRecord() {
        return volunteerRecord;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setWorkRecord(String workRecord) {
        this.workRecord = workRecord;
    }

    public String getWorkRecord() {
        return workRecord;
    }

    public void setServerStartDate(Date serverStartDate) {
        this.serverStartDate = serverStartDate;
    }

    public Date getServerStartDate() {
        return serverStartDate;
    }

    public void setServerType(BigDecimal serverType) {
        this.serverType = serverType;
    }

    public BigDecimal getServerType() {
        return serverType;
    }

    public void setAgentType(BigDecimal agentType) {
        this.agentType = agentType;
    }

    public BigDecimal getAgentType() {
        return agentType;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setEntryDate(Date entryDate) {
        this.entryDate = entryDate;
    }

    public Date getEntryDate() {
        return entryDate;
    }

    public void setCertiCode(String certiCode) {
        this.certiCode = certiCode;
    }

    public String getCertiCode() {
        return certiCode;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setVolunteerLevel(String volunteerLevel) {
        this.volunteerLevel = volunteerLevel;
    }

    public String getVolunteerLevel() {
        return volunteerLevel;
    }

    public void setServerId(BigDecimal serverId) {
        this.serverId = serverId;
    }

    public BigDecimal getServerId() {
        return serverId;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmail() {
        return email;
    }

    public void setCertiType(String certiType) {
        this.certiType = certiType;
    }

    public String getCertiType() {
        return certiType;
    }

    public String getServerCode() {
        return serverCode;
    }

    public void setServerCode(String serverCode) {
        this.serverCode = serverCode;
    }

    public String getServerTypeName() {
        return serverTypeName;
    }

    public void setServerTypeName(String serverTypeName) {
        this.serverTypeName = serverTypeName;
    }

    public String getOrganCodeName() {
        return organCodeName;
    }

    public void setOrganCodeName(String organCodeName) {
        this.organCodeName = organCodeName;
    }

    @Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "ClaimCareServerBO [validFlag=" + validFlag + ", volunteerJob=" + volunteerJob + ", volunteerRecord="
                + volunteerRecord + ", name=" + name + ", workRecord=" + workRecord + ", serverStartDate="
                + serverStartDate + ", serverType=" + serverType + ", serverTypeName=" + serverTypeName
                + ", agentType=" + agentType + ", phone=" + phone + ", organCode=" + organCode + ", organCodeName="
                + organCodeName + ", entryDate=" + entryDate + ", certiCode=" + certiCode + ", mobile=" + mobile
                + ", volunteerLevel=" + volunteerLevel + ", serverId=" + serverId + ", email=" + email + ", certiType="
                + certiType + ", serverCode=" + serverCode + "]";
    }

}
