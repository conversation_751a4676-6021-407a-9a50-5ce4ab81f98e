package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;

/** 
 * @description ClaimTaxTransTaskVO对象
 * <AUTHOR> 
 * @date 2023-11-08 10:32:55  
 */
public class ClaimTaxTransTaskVO extends BaseVO {	
		 /** 
	* @Fields taskStatus :  任务状态（0-待报送、1-已报送、2-报送失败，3-待查询、4-已回退）
 	*/ 
	private BigDecimal taskStatus;
		 /** 
	* @Fields asynchNum :  异步预约码
 	*/ 
	private String asynchNum;
	 /** 
	* @Fields taskType :  任务类型（0-税优理赔结案报送，1-税优理赔关闭报送）
 	*/ 
	private BigDecimal taskType;
		 /** 
	* @Fields listId :  主键
 	*/ 
	private BigDecimal listId;
				 /** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
	/** 
	* @Fields claimNum : 客户号
	*/ 
	private BigDecimal customerId;
	/** 
	* @Fields policyCode : 保单号 
	*/ 
	private String policyCode;
	
	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public BigDecimal getCustomerId() {
		return customerId;
	}

	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}

		public void setTaskStatus(BigDecimal taskStatus) {
		this.taskStatus = taskStatus;
	}
	
	public BigDecimal getTaskStatus() {
		return taskStatus;
	}
		 public void setAsynchNum(String asynchNum) {
		this.asynchNum = asynchNum;
	}
	
	public String getAsynchNum() {
		return asynchNum;
	}
	 public void setTaskType(BigDecimal taskType) {
		this.taskType = taskType;
	}
	
	public BigDecimal getTaskType() {
		return taskType;
	}
		 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
				 public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	
	public BigDecimal getCaseId() {
		return caseId;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimTaxTransTaskVO [" +
				"taskStatus="+taskStatus+","+
"asynchNum="+asynchNum+","+
"taskType="+taskType+","+
"listId="+listId+","+
"caseId="+caseId+"]";
    }
}
