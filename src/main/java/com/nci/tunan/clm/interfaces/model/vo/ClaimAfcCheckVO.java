package com.nci.tunan.clm.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 理赔事后质检任务表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class ClaimAfcCheckVO extends BaseVO{

	  /**
 * @Fields caseNo : 赔案号
 */
private String caseNo;
/**
 * 出险人
 */
private String customerName;
/**
 * @Fields checkBy : 质检人
 */
private BigDecimal checkBy;
/**
 * @Fields checkBy : 质检人姓名
 */
private String checkName;
/**
 * 审批起期
 */
private Date approveStartDate;
/**
 * 审批止期
 */
private Date approveEndDate;



/**
 * @Fields planId : 质检计划编号
 */
private BigDecimal planId;
/**
 * @Fields planName : 质检计划名称
 */
private String planName;

/**
 * @Fields claimCode: 理赔类型代码
 */
private String claimCode;
/**
 * @Fields claimType : 理赔类型
 */
private String claimType;

/**
 * @Fields auditorName : 审核人
 */
private String auditorName;
/**
 * @Fields approverName : 审批人
 */
private String approverName;
/**
 * @Fields caseFlag : 案件标识
 */
private BigDecimal caseFlag;
/**
 * 管理机构
 */
private String organCode;
/**
 * 审批日期
 */
private Date approveTime;

/**
 *  taskId : 任务编号
 */
private BigDecimal taskId;

/**
 *  insuredId :出险人编号
 */
private BigDecimal insuredId;
/**
 *  auditorId :审核人编号
 */
private BigDecimal auditorId;
/**
 *  approverId :审批人编号
 */
private BigDecimal approverId;
/**
 * @Fields caseId : 赔案ID
 */
private BigDecimal caseId;

public void setCaseId(BigDecimal caseId) {
	    this.caseId = caseId;
	}
	public BigDecimal getCaseId() {
	    return caseId;
	}


public void setPlanId(BigDecimal planId) {
    this.planId = planId;
}
public BigDecimal getPlanId() {
    return planId;
}

public void setInsuredId(BigDecimal insuredId) {
    this.insuredId = insuredId;
}
public BigDecimal getInsuredId() {
    return insuredId;
}
	
public void setAuditorId(BigDecimal auditorId) {
    this.auditorId = auditorId;
}
public BigDecimal getAuditorId() {
    return auditorId;
}

public void setApproverId(BigDecimal approverId) {
    this.approverId = approverId;
}
public BigDecimal getApproverId() {
    return approverId;
}

	public String getPlanName() {
		return planName;
	}
	public void setPlanName(String planName) {
		this.planName = planName;
	}
	
	public Date getApproveStartDate() {
		return approveStartDate;
	}
	public void setApproveStartDate(Date approveStartDate) {
		this.approveStartDate = approveStartDate;
	}
	
	public Date getApproveEndDate() {
		return approveEndDate;
	}
	public void setApproveEndDate(Date approveEndDate) {
		this.approveEndDate = approveEndDate;
	}

	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getOrganCode() {
		return organCode;
	}
	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

public String getCheckName() {
    return checkName;
}
public void setCheckName(String checkName) {
    this.checkName = checkName;
}

public String getAuditorName() {
    return auditorName;
}
public void setAuditorName(String auditorName) {
    this.auditorName = auditorName;
}

public String getApproverName() {
    return approverName;
}
public void setApproverName(String approverName) {
    this.approverName = approverName;
}

public void setTaskId(BigDecimal taskId) {
    this.taskId = taskId;
}
public BigDecimal getTaskId() {
    return taskId;
}

public void setCaseNo(String caseNo) {
    this.caseNo = caseNo;
}
public String getCaseNo() {
    return caseNo;
}


public void setCheckBy(BigDecimal checkBy) {
    this.checkBy = checkBy;
}
public BigDecimal getCheckBy() {
    return checkBy;
}

public void setApproveTime(Date approveTime) {
    this.approveTime = approveTime;
}

public Date getApproveTime() {
    return approveTime;
}

public String getClaimCode() {
    return claimCode;
}
public void setClaimCode(String claimCode) {
    this.claimCode = claimCode;
}

public String getClaimType() {
    return claimType;
}
public void setClaimType(String claimType) {
    this.claimType = claimType;
}

public BigDecimal getCaseFlag() {
    return caseFlag;
}

public void setCaseFlag(BigDecimal caseFlag) {
    this.caseFlag = caseFlag;
}
@Override
public String getBizId() {
    return null;
}

@Override
public String toString() {
    return "ClaimAfcTaskVO [" + "planName=" + planName + "," + "caseNo=" + caseNo + "," + "claimType="
            + claimType + "," + "customerName=" + customerName + "," + "auditorName=" + auditorName + "," + "approverName="
            + approverName + "," + "checkName=" + checkName + "," + "caseFlag=" + caseFlag + "," + "organCode=" + organCode + ","
            + "approveTime=" + approveTime+ "]";
}

}
