package com.nci.tunan.clm.interfaces.model.vo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseVO;

/** 
 * @description ClaimInstalmentLiabVO对象
 * <AUTHOR> 
 * @date 2025-09-19 14:19:54  
 */
public class ClaimInstalmentLiabVO extends BaseVO {	
	 /** 
	* @Fields instalmentId :  分期给付应领ID，T
 	*/ 
	private BigDecimal instalmentId;
	 /** 
	* @Fields productId :  精算产品ID
 	*/ 
	private BigDecimal productId;
	 /** 
	* @Fields payeeId :  领款人ID，T
 	*/ 
	private BigDecimal payeeId;
	 /** 
	* @Fields serverCode :  对公服务机构
 	*/ 
	private String serverCode;
		 /** 
	* @Fields liabId :  责任编码
 	*/ 
	private BigDecimal liabId;
		 /** 
	* @Fields itemId :  责任组ID
 	*/ 
	private BigDecimal itemId;
	 /** 
	* @Fields liabName :  责任名称
 	*/ 
	private String liabName;
	 /** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
	 /** 
	* @Fields busiProdCode :  业务产品编码
 	*/ 
	private String busiProdCode;
		 /** 
	* @Fields beneId :  受益人ID，T
 	*/ 
	private BigDecimal beneId;
	 /** 
	* @Fields policyCode :  保单号
 	*/ 
	private String policyCode;
		 /** 
	* @Fields listId :  主键ID
 	*/ 
	private BigDecimal listId;
	 /** 
	* @Fields liabCode :  责任代码
 	*/ 
	private String liabCode;
			 /** 
	* @Fields calcPay :  理算金额
 	*/ 
	private BigDecimal calcPay;
	 /** 
	* @Fields busiItemId :  业务险种ID
 	*/ 
	private BigDecimal busiItemId;
	 /** 
	* @Fields policyId :  保单ID
 	*/ 
	private BigDecimal policyId;
		
	 public void setInstalmentId(BigDecimal instalmentId) {
		this.instalmentId = instalmentId;
	}
	
	public BigDecimal getInstalmentId() {
		return instalmentId;
	}
	 public void setProductId(BigDecimal productId) {
		this.productId = productId;
	}
	
	public BigDecimal getProductId() {
		return productId;
	}
	 public void setPayeeId(BigDecimal payeeId) {
		this.payeeId = payeeId;
	}
	
	public BigDecimal getPayeeId() {
		return payeeId;
	}
	 public void setServerCode(String serverCode) {
		this.serverCode = serverCode;
	}
	
	public String getServerCode() {
		return serverCode;
	}
		 public void setLiabId(BigDecimal liabId) {
		this.liabId = liabId;
	}
	
	public BigDecimal getLiabId() {
		return liabId;
	}
		 public void setItemId(BigDecimal itemId) {
		this.itemId = itemId;
	}
	
	public BigDecimal getItemId() {
		return itemId;
	}
	 public void setLiabName(String liabName) {
		this.liabName = liabName;
	}
	
	public String getLiabName() {
		return liabName;
	}
	 public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	
	public BigDecimal getCaseId() {
		return caseId;
	}
	 public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}
	
	public String getBusiProdCode() {
		return busiProdCode;
	}
		 public void setBeneId(BigDecimal beneId) {
		this.beneId = beneId;
	}
	
	public BigDecimal getBeneId() {
		return beneId;
	}
	 public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	
	public String getPolicyCode() {
		return policyCode;
	}
		 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
	 public void setLiabCode(String liabCode) {
		this.liabCode = liabCode;
	}
	
	public String getLiabCode() {
		return liabCode;
	}
			 public void setCalcPay(BigDecimal calcPay) {
		this.calcPay = calcPay;
	}
	
	public BigDecimal getCalcPay() {
		return calcPay;
	}
	 public void setBusiItemId(BigDecimal busiItemId) {
		this.busiItemId = busiItemId;
	}
	
	public BigDecimal getBusiItemId() {
		return busiItemId;
	}
	 public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}
	
	public BigDecimal getPolicyId() {
		return policyId;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimInstalmentLiabVO [" +
				"instalmentId="+instalmentId+","+
"productId="+productId+","+
"payeeId="+payeeId+","+
"serverCode="+serverCode+","+
"liabId="+liabId+","+
"itemId="+itemId+","+
"liabName="+liabName+","+
"caseId="+caseId+","+
"busiProdCode="+busiProdCode+","+
"beneId="+beneId+","+
"policyCode="+policyCode+","+
"listId="+listId+","+
"liabCode="+liabCode+","+
"calcPay="+calcPay+","+
"busiItemId="+busiItemId+","+
"policyId="+policyId+"]";
    }
}
