package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;

/** 
 * @description ClaimRiskLibImportVO对象
 * <AUTHOR> 
 * @date 2023-02-06 10:32:58  
 */
public class ClaimRiskLibImportVO extends BaseVO {	
	 /** 
	* @Fields errDesc :  失败原因描述
 	*/ 
	private String errDesc;
	 /** 
	* @Fields riskLibType :  风控类型
 	*/ 
	private String riskLibType;
		 /** 
	* @Fields batchNo :  导入批次号
 	*/ 
	private String batchNo;
	 /** 
	* @Fields inspectDemand :  风控等级
 	*/ 
	private String inspectDemand;
	 /** 
	* @Fields importResult :  导入结果
 	*/ 
	private String importResult;
	 /** 
	* @Fields checkRoles :  行为方
 	*/ 
	private String checkRoles;
		 /** 
	* @Fields libMakeLevel :  定制层级
 	*/ 
	private String libMakeLevel;
	 /** 
	* @Fields basisVersion :  文号/版本
 	*/ 
	private String basisVersion;
	 /** 
	* @Fields libDemand :  核查要求
 	*/ 
	private String libDemand;
	 /** 
	* @Fields seqNo :  序号
 	*/ 
	private BigDecimal seqNo;
		 /** 
	* @Fields thresholdValue :  风险阈值
 	*/ 
	private String thresholdValue;
	 /** 
	* @Fields thresholdType :  风险阈值类型
 	*/ 
	private String thresholdType;
	 /** 
	* @Fields riskLibPoint :  风控要点
 	*/ 
	private String riskLibPoint;
		 /** 
	* @Fields libBasis :  风控依据
 	*/ 
	private String libBasis;
	 /** 
	* @Fields inspectFreq :  建议检查频率
 	*/ 
	private String inspectFreq;
	 /** 
	* @Fields listId :  主键序列
 	*/ 
	private BigDecimal listId;
			 /** 
	* @Fields isRelClm :  是否与赔案有关
 	*/ 
	private String isRelClm;
	 /** 
	* @Fields warnType :  预警类型
 	*/ 
	private String warnType;
		
	 public void setErrDesc(String errDesc) {
		this.errDesc = errDesc;
	}
	
	public String getErrDesc() {
		return errDesc;
	}
	 public void setRiskLibType(String riskLibType) {
		this.riskLibType = riskLibType;
	}
	
	public String getRiskLibType() {
		return riskLibType;
	}
		 public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}
	
	public String getBatchNo() {
		return batchNo;
	}
	 public void setInspectDemand(String inspectDemand) {
		this.inspectDemand = inspectDemand;
	}
	
	public String getInspectDemand() {
		return inspectDemand;
	}
	 public void setImportResult(String importResult) {
		this.importResult = importResult;
	}
	
	public String getImportResult() {
		return importResult;
	}
	 public void setCheckRoles(String checkRoles) {
		this.checkRoles = checkRoles;
	}
	
	public String getCheckRoles() {
		return checkRoles;
	}
		 public void setLibMakeLevel(String libMakeLevel) {
		this.libMakeLevel = libMakeLevel;
	}
	
	public String getLibMakeLevel() {
		return libMakeLevel;
	}
	 public void setBasisVersion(String basisVersion) {
		this.basisVersion = basisVersion;
	}
	
	public String getBasisVersion() {
		return basisVersion;
	}
	 public void setLibDemand(String libDemand) {
		this.libDemand = libDemand;
	}
	
	public String getLibDemand() {
		return libDemand;
	}
	 public void setSeqNo(BigDecimal seqNo) {
		this.seqNo = seqNo;
	}
	
	public BigDecimal getSeqNo() {
		return seqNo;
	}
		 public void setThresholdValue(String thresholdValue) {
		this.thresholdValue = thresholdValue;
	}
	
	public String getThresholdValue() {
		return thresholdValue;
	}
	 public void setThresholdType(String thresholdType) {
		this.thresholdType = thresholdType;
	}
	
	public String getThresholdType() {
		return thresholdType;
	}
	 public void setRiskLibPoint(String riskLibPoint) {
		this.riskLibPoint = riskLibPoint;
	}
	
	public String getRiskLibPoint() {
		return riskLibPoint;
	}
		 public void setLibBasis(String libBasis) {
		this.libBasis = libBasis;
	}
	
	public String getLibBasis() {
		return libBasis;
	}
	 public void setInspectFreq(String inspectFreq) {
		this.inspectFreq = inspectFreq;
	}
	
	public String getInspectFreq() {
		return inspectFreq;
	}
	 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
			 public void setIsRelClm(String isRelClm) {
		this.isRelClm = isRelClm;
	}
	
	public String getIsRelClm() {
		return isRelClm;
	}
	 public void setWarnType(String warnType) {
		this.warnType = warnType;
	}
	
	public String getWarnType() {
		return warnType;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimRiskLibImportVO [" +
				"errDesc="+errDesc+","+
"riskLibType="+riskLibType+","+
"batchNo="+batchNo+","+
"inspectDemand="+inspectDemand+","+
"importResult="+importResult+","+
"checkRoles="+checkRoles+","+
"libMakeLevel="+libMakeLevel+","+
"basisVersion="+basisVersion+","+
"libDemand="+libDemand+","+
"seqNo="+seqNo+","+
"thresholdValue="+thresholdValue+","+
"thresholdType="+thresholdType+","+
"riskLibPoint="+riskLibPoint+","+
"libBasis="+libBasis+","+
"inspectFreq="+inspectFreq+","+
"listId="+listId+","+
"isRelClm="+isRelClm+","+
"warnType="+warnType+"]";
    }
}
