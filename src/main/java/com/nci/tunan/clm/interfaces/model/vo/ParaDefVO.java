package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @description 部门相关信息
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class ParaDefVO extends BaseVO {	
	 /** 
	* @Fields deptId :  部门ID
 	*/ 
	private BigDecimal deptId;
	 /** 
	* @Fields paraId :  参数ID
 	*/ 
	private BigDecimal paraId;
	 /** 
	* @Fields moduleId :  系统模块
 	*/ 
	private BigDecimal moduleId;
	 /** 
	* @Fields systemId :  系统编号:
产品工厂:063,保单管理:064,新契约:065,核保:066,理赔:067,保全:068,工作流:069,规则管理平台:070,柜面:071,内容平台:072,接入渠道整合平台:073,打印:074,收付费:075,营销支持:076
 	*/ 
	private String systemId;
	 /** 
	* @Fields singleParaValue :  单个参数值
 	*/ 
	private String singleParaValue;
	 /** 
	* @Fields endDate :  停用日期
 	*/ 
	private Date endDate;
	 /** 
	* @Fields paraValue :  参数数值
 	*/ 
	private String paraValue;
	 /** 
	* @Fields deptRela :  是否部门相关
 	*/ 
	private String deptRela;
	 /** 
	* @Fields paraName :  名称
 	*/ 
	private String paraName;
	 /** 
	* @Fields paraDesc :  描述信息
 	*/ 
	private String paraDesc;
	 /** 
	* @Fields systemAdmin :  是否系统管理员管理
 	*/ 
	private String systemAdmin;
	 /** 
	* @Fields startDate :  启用日期
 	*/ 
	private Date startDate;
	 /** 
	* @Fields organRela :  是否机构相关
 	*/ 
	private String organRela;
	 /** 
	* @Fields timeRela :  是否时间相关
 	*/ 
	private String timeRela;
	 /** 
	* @Fields scopeCode :  参数范围: global	:全局参数,system	系统级参数,application	应用级参数
 	*/ 
	private String scopeCode;
	 /** 
	* @Fields paraValueName :  参数数值名称
 	*/ 
	private String paraValueName;
	 /** 
	* @Fields dataType :  数据类型
 	*/ 
	private BigDecimal dataType;
	 /** 
	* @Fields organId :  机构ID
 	*/ 
	private BigDecimal organId;
		
	 public void setDeptId(BigDecimal deptId) {
		this.deptId = deptId;
	}
	
	public BigDecimal getDeptId() {
		return deptId;
	}
	 public void setParaId(BigDecimal paraId) {
		this.paraId = paraId;
	}
	
	public BigDecimal getParaId() {
		return paraId;
	}
	 public void setModuleId(BigDecimal moduleId) {
		this.moduleId = moduleId;
	}
	
	public BigDecimal getModuleId() {
		return moduleId;
	}
	 public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	
	public String getSystemId() {
		return systemId;
	}
	 public void setSingleParaValue(String singleParaValue) {
		this.singleParaValue = singleParaValue;
	}
	
	public String getSingleParaValue() {
		return singleParaValue;
	}
	 public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
	public Date getEndDate() {
		return endDate;
	}
	 public void setParaValue(String paraValue) {
		this.paraValue = paraValue;
	}
	
	public String getParaValue() {
		return paraValue;
	}
	 public void setDeptRela(String deptRela) {
		this.deptRela = deptRela;
	}
	
	public String getDeptRela() {
		return deptRela;
	}
	 public void setParaName(String paraName) {
		this.paraName = paraName;
	}
	
	public String getParaName() {
		return paraName;
	}
	 public void setParaDesc(String paraDesc) {
		this.paraDesc = paraDesc;
	}
	
	public String getParaDesc() {
		return paraDesc;
	}
	 public void setSystemAdmin(String systemAdmin) {
		this.systemAdmin = systemAdmin;
	}
	
	public String getSystemAdmin() {
		return systemAdmin;
	}
	 public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	
	public Date getStartDate() {
		return startDate;
	}
	 public void setOrganRela(String organRela) {
		this.organRela = organRela;
	}
	
	public String getOrganRela() {
		return organRela;
	}
	 public void setTimeRela(String timeRela) {
		this.timeRela = timeRela;
	}
	
	public String getTimeRela() {
		return timeRela;
	}
	 public void setScopeCode(String scopeCode) {
		this.scopeCode = scopeCode;
	}
	
	public String getScopeCode() {
		return scopeCode;
	}
	 public void setParaValueName(String paraValueName) {
		this.paraValueName = paraValueName;
	}
	
	public String getParaValueName() {
		return paraValueName;
	}
	 public void setDataType(BigDecimal dataType) {
		this.dataType = dataType;
	}
	
	public BigDecimal getDataType() {
		return dataType;
	}
	 public void setOrganId(BigDecimal organId) {
		this.organId = organId;
	}
	
	public BigDecimal getOrganId() {
		return organId;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ParaDefVO [" +
				"deptId="+deptId+","+
"paraId="+paraId+","+
"moduleId="+moduleId+","+
"systemId="+systemId+","+
"singleParaValue="+singleParaValue+","+
"endDate="+endDate+","+
"paraValue="+paraValue+","+
"deptRela="+deptRela+","+
"paraName="+paraName+","+
"paraDesc="+paraDesc+","+
"systemAdmin="+systemAdmin+","+
"startDate="+startDate+","+
"organRela="+organRela+","+
"timeRela="+timeRela+","+
"scopeCode="+scopeCode+","+
"paraValueName="+paraValueName+","+
"dataType="+dataType+","+
"organId="+organId+"]";
    }
}
