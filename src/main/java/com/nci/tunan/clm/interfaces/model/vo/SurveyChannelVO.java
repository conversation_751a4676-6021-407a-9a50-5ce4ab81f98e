package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @description 固定管理渠道信息表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class SurveyChannelVO extends BaseVO {	
	 /** 
	* @Fields validFlag :  有效标志
 	*/ 
	private BigDecimal validFlag;
	 /** 
	* @Fields contactPerJob :  联系人职务
 	*/ 
	private String contactPerJob;
	 /** 
	* @Fields channelId :  渠道ID
 	*/ 
	private BigDecimal channelId;
	 /** 
	* @Fields safeguardPhone :  维护人联系方式
 	*/ 
	private String safeguardPhone;
	 /** 
	* @Fields isCooperation :  是否签订合作协议
 	*/ 
	private BigDecimal isCooperation;
	 /** 
	* @Fields remark :  备注
 	*/ 
	private String remark;
	 /** 
	* @Fields contactPhone :  联系电话
 	*/ 
	private String contactPhone;
	 /** 
	* @Fields channelName :  渠道名称
 	*/ 
	private String channelName;
		 /** 
	* @Fields filialeOrg :  二级机构
 	*/ 
	private String filialeOrg;
	 /** 
	* @Fields channelType :  渠道类型
 	*/ 
	private String channelType;
		 /** 
	* @Fields contactPer :  联系人
 	*/ 
	private String contactPer;
	 /** 
	* @Fields contactMobile :  联系手机
 	*/ 
	private String contactMobile;
	 /** 
	* @Fields channelLevel :  渠道等级
 	*/ 
	private String channelLevel;
	 /** 
	* @Fields mediueOrg :  三级机构
 	*/ 
	private String mediueOrg;
			 /** 
	* @Fields contactDept :  联络部门/科室
 	*/ 
	private String contactDept;
	 /** 
	* @Fields sameBusiNamae :  同业名称
 	*/ 
	private String sameBusiNamae;
	 /** 
	* @Fields safeguardBy :  渠道维护人
 	*/ 
	private BigDecimal safeguardBy;
	 /** 
	* @Fields isJudExpertise :  是否具备司法鉴定资质
 	*/ 
	private BigDecimal isJudExpertise;
			 /** 
	* @Fields channelAddress :  渠道地址
 	*/ 
	private String channelAddress;
	/**
	 * @Fields time :  操作日期
	 */
	private Date time;
	/**
     * @Fields time :  操作人员
     */
	private BigDecimal operationPerson;
	/**
	 * @Fields 客户选择渠道结果的索引用于回显使用 :  选择的渠道结果索引值
	 */
	private String channelIndex;
	
		
	 public String getChannelIndex() {
        return channelIndex;
    }

    public void setChannelIndex(String channelIndex) {
        this.channelIndex = channelIndex;
    }

    public void setValidFlag(BigDecimal validFlag) {
		this.validFlag = validFlag;
	}
	
	public BigDecimal getValidFlag() {
		return validFlag;
	}
	 public void setContactPerJob(String contactPerJob) {
		this.contactPerJob = contactPerJob;
	}
	public String getFilialeOrg() {
        return filialeOrg;
    }

    public void setFilialeOrg(String filialeOrg) {
        this.filialeOrg = filialeOrg;
    }

    public String getContactPerJob() {
		return contactPerJob;
	}
	 public void setChannelId(BigDecimal channelId) {
		this.channelId = channelId;
	}
	
	public BigDecimal getChannelId() {
		return channelId;
	}
	 public void setSafeguardPhone(String safeguardPhone) {
		this.safeguardPhone = safeguardPhone;
	}
	
	public String getSafeguardPhone() {
		return safeguardPhone;
	}
	 public void setIsCooperation(BigDecimal isCooperation) {
		this.isCooperation = isCooperation;
	}
	
	public BigDecimal getIsCooperation() {
		return isCooperation;
	}
	 public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRemark() {
		return remark;
	}
	 public void setContactPhone(String contactPhone) {
		this.contactPhone = contactPhone;
	}
	
	public String getContactPhone() {
		return contactPhone;
	}
	 public void setChannelName(String channelName) {
		this.channelName = channelName;
	}
	
	public String getChannelName() {
		return channelName;
	}
	 public void setChannelType(String channelType) {
		this.channelType = channelType;
	}
	
	public String getChannelType() {
		return channelType;
	}
		 public void setContactPer(String contactPer) {
		this.contactPer = contactPer;
	}
	
	public String getContactPer() {
		return contactPer;
	}
	 public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}
	
	public String getContactMobile() {
		return contactMobile;
	}
	 public void setChannelLevel(String channelLevel) {
		this.channelLevel = channelLevel;
	}
	
	public String getChannelLevel() {
		return channelLevel;
	}
	 public void setMediueOrg(String mediueOrg) {
		this.mediueOrg = mediueOrg;
	}
	
	public String getMediueOrg() {
		return mediueOrg;
	}
			 public void setContactDept(String contactDept) {
		this.contactDept = contactDept;
	}
	
	public String getContactDept() {
		return contactDept;
	}
	 public void setSameBusiNamae(String sameBusiNamae) {
		this.sameBusiNamae = sameBusiNamae;
	}
	
	public String getSameBusiNamae() {
		return sameBusiNamae;
	}
	 public void setSafeguardBy(BigDecimal safeguardBy) {
		this.safeguardBy = safeguardBy;
	}
	
	public BigDecimal getSafeguardBy() {
		return safeguardBy;
	}
	 public void setIsJudExpertise(BigDecimal isJudExpertise) {
		this.isJudExpertise = isJudExpertise;
	}
	
	public BigDecimal getIsJudExpertise() {
		return isJudExpertise;
	}
			 public void setChannelAddress(String channelAddress) {
		this.channelAddress = channelAddress;
	}
	
	public String getChannelAddress() {
		return channelAddress;
	}
	public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public BigDecimal getOperationPerson() {
        return operationPerson;
    }

    public void setOperationPerson(BigDecimal operationPerson) {
        this.operationPerson = operationPerson;
    }
    
    @Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "SurveyChannelVO [" +
				"validFlag="+validFlag+","+
"contactPerJob="+contactPerJob+","+
"channelId="+channelId+","+
"safeguardPhone="+safeguardPhone+","+
"isCooperation="+isCooperation+","+
"remark="+remark+","+
"contactPhone="+contactPhone+","+
"channelName="+channelName+","+
"filialeOrg="+filialeOrg+","+
"channelType="+channelType+","+
"contactPer="+contactPer+","+
"contactMobile="+contactMobile+","+
"channelLevel="+channelLevel+","+
"mediueOrg="+mediueOrg+","+
"contactDept="+contactDept+","+
"sameBusiNamae="+sameBusiNamae+","+
"safeguardBy="+safeguardBy+","+
"isJudExpertise="+isJudExpertise+","+
"channelAddress="+channelAddress+"]";
    }
}
