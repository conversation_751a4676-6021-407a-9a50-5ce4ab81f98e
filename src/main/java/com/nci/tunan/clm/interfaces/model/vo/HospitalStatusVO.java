package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;

/**
 * 
 * @description 医院状态表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class HospitalStatusVO extends BaseVO {	
	 /** 
	* @Fields name :  医院状态名称
 	*/ 
	private String name;
	 /** 
	* @Fields code :  医院状态代码
 	*/ 
	private BigDecimal code;
		
	 public void setName(String name) {
		this.name = name;
	}
	
	public String getName() {
		return name;
	}
	 public void setCode(BigDecimal code) {
		this.code = code;
	}
	
	public BigDecimal getCode() {
		return code;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "HospitalStatusVO [" +
				"name="+name+","+
"code="+code+"]";
    }
}
