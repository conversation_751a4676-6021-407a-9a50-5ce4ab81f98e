package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;
import java.math.BigDecimal;
import java.lang.String;

/**
 * 
 * @description 理赔调查规则保单机构表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class ClaimSurveyOrganVO extends BaseVO {	
		 /** 
	* @Fields organCode :  保单机构
 	*/ 
	private String organCode;
	 /** 
	* @Fields surveyRuleCode :  规则编码
 	*/ 
	private String surveyRuleCode;
			 /** 
	* @Fields listId :  主键流水号
 	*/ 
	private BigDecimal listId;
					
	/** 
	* @Fields organName :  机构名称
 	*/ 
	private String organName;
					
	public String getOrganName() {
		return organName;
	}

	public void setOrganName(String organName) {
		this.organName = organName;
	}
	
	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
	 public void setSurveyRuleCode(String surveyRuleCode) {
		this.surveyRuleCode = surveyRuleCode;
	}
	
	public String getSurveyRuleCode() {
		return surveyRuleCode;
	}
			 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
					
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimSurveyOrganVO [" +
				"organCode="+organCode+","+
"surveyRuleCode="+surveyRuleCode+","+
"organName="+organName+","+
"listId="+listId+"]";
    }
}
