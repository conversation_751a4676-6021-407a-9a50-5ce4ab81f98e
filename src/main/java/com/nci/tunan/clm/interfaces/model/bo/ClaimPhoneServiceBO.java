package com.nci.tunan.clm.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;

/**
 * 
 * @description 服务电话维护表 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class ClaimPhoneServiceBO extends BaseVO {	
		 /** 
	* @Fields organCode :  服务机构代码
 	*/ 
	private String organCode;
	 /** 
	* @Fields phoneServiceId :  序号ID
 	*/ 
	private BigDecimal phoneServiceId;
		 /** 
	* @Fields orgName :  机构名称
 	*/ 
	private String orgName;
		 /** 
	* @Fields email :  EMAIL
 	*/ 
	private String email;
			 /** 
	* @Fields servicePhone :  服务电话
 	*/ 
	private String servicePhone;
			
		 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
	 public void setPhoneServiceId(BigDecimal phoneServiceId) {
		this.phoneServiceId = phoneServiceId;
	}
	
	public BigDecimal getPhoneServiceId() {
		return phoneServiceId;
	}
		 public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	
	public String getOrgName() {
		return orgName;
	}
		 public void setEmail(String email) {
		this.email = email;
	}
	
	public String getEmail() {
		return email;
	}
			
	public String getServicePhone() {
        return servicePhone;
    }

    public void setServicePhone(String servicePhone) {
        this.servicePhone = servicePhone;
    }

    @Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimPhoneServiceVO [" +
				"organCode="+organCode+","+
"phoneServiceId="+phoneServiceId+","+
"orgName="+orgName+","+
"email="+email+","+
"servicePhone="+servicePhone+"]";
    }
}
