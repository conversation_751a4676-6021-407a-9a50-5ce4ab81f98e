package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;
import java.util.Date;

/** 
 * @description InspectResultTraceVO对象
 * <AUTHOR> 
 * @date 2023-02-07 14:16:00  
 */
public class InspectResultTraceVO extends BaseVO {	
	 /** 
	* @Fields inspectOrg :  修订人机构
 	*/ 
	private String inspectOrg;
		 /** 
	* @Fields inspectDesc :  差错描述
 	*/ 
	private String inspectDesc;
	 /** 
	* @Fields inspectUser :  修订人
 	*/ 
	private BigDecimal inspectUser;
		 /** 
	* @Fields pointId :  抽检任务要点表ID，T
 	*/ 
	private BigDecimal pointId;
			 /** 
	* @Fields listId :  主键序列
 	*/ 
	private BigDecimal listId;
	 /** 
	* @Fields inspectResult :  1-差错
2-正确
3-其他
 	*/ 
	private BigDecimal inspectResult;
	 /** 
	* @Fields inspectTime :  修订时间
 	*/ 
	private Date inspectTime;
				
	 public void setInspectOrg(String inspectOrg) {
		this.inspectOrg = inspectOrg;
	}
	
	public String getInspectOrg() {
		return inspectOrg;
	}
		 public void setInspectDesc(String inspectDesc) {
		this.inspectDesc = inspectDesc;
	}
	
	public String getInspectDesc() {
		return inspectDesc;
	}
	 public void setInspectUser(BigDecimal inspectUser) {
		this.inspectUser = inspectUser;
	}
	
	public BigDecimal getInspectUser() {
		return inspectUser;
	}
		 public void setPointId(BigDecimal pointId) {
		this.pointId = pointId;
	}
	
	public BigDecimal getPointId() {
		return pointId;
	}
			 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
	 public void setInspectResult(BigDecimal inspectResult) {
		this.inspectResult = inspectResult;
	}
	
	public BigDecimal getInspectResult() {
		return inspectResult;
	}
	 public void setInspectTime(Date inspectTime) {
		this.inspectTime = inspectTime;
	}
	
	public Date getInspectTime() {
		return inspectTime;
	}
				
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "InspectResultTraceVO [" +
				"inspectOrg="+inspectOrg+","+
"inspectDesc="+inspectDesc+","+
"inspectUser="+inspectUser+","+
"pointId="+pointId+","+
"listId="+listId+","+
"inspectResult="+inspectResult+","+
"inspectTime="+inspectTime+"]";
    }
}
