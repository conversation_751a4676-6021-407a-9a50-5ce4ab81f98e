package com.nci.tunan.clm.interfaces.model.vo;


import java.lang.String;
import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 职业代码
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class JobCodeVO extends BaseVO {	
	 /** 
	* @Fields jobUwLevel :  职业给付系数
 	*/ 
	private String jobUwLevel;
	 /** 
	* @Fields jobName :  职业名称
 	*/ 
	private String jobName;
	 /** 
	* @Fields jobCategory : 职业类别代码
 	*/ 
	private String jobCategory;
	/**
	 * 职业类别名称
	 */
	private String jobCategoryName;
	 /** 
	* @Fields jobCode :  职业代码
 	*/ 
	private String jobCode;
	/**
	 * @Fields oldJobCode :  旧职业代码
	 */
	private String oldJobCode;
	/**
	 * @Fields oldJobCategoryName :  旧职业类别名称
	 */
	private String oldJobCategoryName;
	
	/**
	 * @Fields oldJobUwLevel :  旧职业给付系数
	 */
	private String oldJobUwLevel;
	
	
	
	 public String getOldJobCode() {
		return oldJobCode;
	}

	public void setOldJobCode(String oldJobCode) {
		this.oldJobCode = oldJobCode;
	}

	public String getOldJobCategoryName() {
		return oldJobCategoryName;
	}

	public void setOldJobCategoryName(String oldJobCategoryName) {
		this.oldJobCategoryName = oldJobCategoryName;
	}

	public String getOldJobUwLevel() {
		return oldJobUwLevel;
	}

	public void setOldJobUwLevel(String oldJobUwLevel) {
		this.oldJobUwLevel = oldJobUwLevel;
	}

	public void setJobUwLevel(String jobUwLevel) {
		this.jobUwLevel = jobUwLevel;
	}
	
	public String getJobUwLevel() {
		return jobUwLevel;
	}
	 public void setJobName(String jobName) {
		this.jobName = jobName;
	}
	
	public String getJobName() {
		return jobName;
	}
	 public void setJobCategory(String jobCategory) {
		this.jobCategory = jobCategory;
	}
	
	public String getJobCategory() {
		return jobCategory;
	}
	 public void setJobCode(String jobCode) {
		this.jobCode = jobCode;
	}
	
	public String getJobCode() {
		return jobCode;
	}
		
	public String getJobCategoryName() {
		return jobCategoryName;
	}

	public void setJobCategoryName(String jobCategoryName) {
		this.jobCategoryName = jobCategoryName;
	}

	@Override
    public String getBizId() {
        return null;
    }

	@Override
	public String toString() {
		return "JobCodeVO [jobUwLevel=" + jobUwLevel + ", jobName=" + jobName
				+ ", jobCategory=" + jobCategory + ", jobCategoryName="
				+ jobCategoryName + ", jobCode=" + jobCode + ", oldJobCode="
				+ oldJobCode + ", oldJobCategoryName=" + oldJobCategoryName
				+ ", oldJobUwLevel=" + oldJobUwLevel + "]";
	}
    
   
}
