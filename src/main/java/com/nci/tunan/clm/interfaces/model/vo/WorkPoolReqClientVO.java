package com.nci.tunan.clm.interfaces.model.vo;

import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 个人工作池(Request)
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统-个人工作池
 * @date  2015年5月15日 上午10:08:31
 */
public class WorkPoolReqClientVO extends BaseVO{
	/**
	 * 序列号
	 */
	private static final long serialVersionUID = 3629813497556631224L;

	/** 查询任务类型 */
	protected String queryTaskType;

	/** 赔案号 */
	protected String caseNo;

	/** 出险人姓名 */
	protected String customerName;

	/** 出险人性别 */
	protected String customerSex;

	/** 证件号码 */
	protected String customerId;

	/** 出险日期 */
	protected Date accDate;

	/** 绿色通道标识 */
	protected String greenFlag;

	/** 出险人客户号 */
	protected String customerNo;

	/** 理赔类型 */
	protected String claimType;

	/** 报案操作人 */
	protected String reportOperator;

	/** 机构（赔案报案机构） */
	protected String reportManagecom;

	/** 报案时间 */
	protected Date reportTime;

	/** 是否自动核保标识 */
	protected String autoUwFlag;

	/** 签收人 */
	protected String signOperator;

	/** 签收时间 */
	protected Date signTime;

	/** 签收机构 */
	protected String signManagecom;

	/** 赔案状态 */
	protected String claimState;

	/** 赔案权限 */
	protected String claimAuthority;

	/** 预付标识 */
	protected String advancePayFlag;

	/** 调查标识 */
	protected String surveyFlag;

	/** 二核标识 */
	protected String claimUwFlag;

	/** 补充单证标识 */
	protected String supplyCertifyFlag;

	/** 协谈标识 */
	protected String treatyFlag;

	/** 合议标识 */
	protected String discussFalg;

	/** 事中质检标识 */
	protected String middleCheckFlag;

	/** 立案操作人 */
	protected String regOperator;

	/** 机构（立案机构） */
	protected String regManagecom;

	/** 立案时间 */
	protected Date regTime;

	/** 总时效 */
	protected String countTime;

	/** 审核结论 */
	protected String exaConclusion;

	/** 出险人和受益人关系 */
	protected String beneRelation;

	/** 受益人年龄 */
	protected String beneAge;

	/** 短信接收人ID */
	protected String recipientID;

	/** 消息类型 */
	protected String messageType;

	/** 接收人类型 */
	protected String recipientType;

	/** 短信代码 */
	protected String noteID;

	/** 邮件代码 */
	protected String mailID;

	/** 是否已发起理赔关怀 */
	protected String careFlag;

	/** 是否已报案 */
	protected String clmFlag;

	/** 外包录入标识 */
	protected String externalFlag;

	/** 数据处理状态（回传数据处理节点） */
	protected String dealState;

	/** 外包录入是否存在问题件标识 */
	protected String outerIssueFlag;

	/** 立案结论 */
	protected String regConclusion;

	/** 挂起状态 */
	protected String hangupFlag;

	/** 复核校验结果 */
	protected String approveResult;

	/** 复核案件结论 */
	protected String approveConclusion;

	/** 预付申请结论（是否取消） */
	protected String prePayQConclusion;

	/** 预付审批结论 */
	protected String prePayPConclusion;

	/** 自核规则校验结论（是否需要审核） */
	protected String ruleConclusion;

	/** 审批规则校验结论（是否需要审批） */
	protected String appRuleConclusion;

	/** 审批结论 */
	protected String appConclusion;

	/** 案件抽检规则校验结论（是否需要抽检） */
	protected String caseExtractConclusion;

	/** 案件抽检结论（抽检是否通过） */
	protected String qualityCheckConclusion;

	/** 业务类型 */
	protected String businessType;

	/** 任务打分 */
	protected Double taskGrade;

	/** 路径ID */
	protected String routeId;

	/** 模板标识 */
	protected String processFlag;

	/** 节点编码 */
	protected String taskCode;

	/** 工作流任务ID */
	protected String bpmTaskId;

	/** 任务总数 */
	protected String bpmTaskNum;

	/** 角色编码 */
	protected String roleId;

	/** 操作员编码 */
	protected String operator;

	/** 登陆机构 */
	protected String managecom;

	/**
	 * 分页
	 */
	private int currentPage;

	/**
	 * 页面尺寸
	 */
	private int pageSize;

	public int getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(int currentPage) {
		this.currentPage = currentPage;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerSex() {
		return customerSex;
	}

	public void setCustomerSex(String customerSex) {
		this.customerSex = customerSex;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public Date getAccDate() {
		return accDate;
	}

	public void setAccDate(Date accDate) {
		this.accDate = accDate;
	}

	public String getGreenFlag() {
		return greenFlag;
	}

	public void setGreenFlag(String greenFlag) {
		this.greenFlag = greenFlag;
	}

	public String getCustomerNo() {
		return customerNo;
	}

	public void setCustomerNo(String customerNo) {
		this.customerNo = customerNo;
	}

	public String getClaimType() {
		return claimType;
	}

	public void setClaimType(String claimType) {
		this.claimType = claimType;
	}

	public String getReportOperator() {
		return reportOperator;
	}

	public void setReportOperator(String reportOperator) {
		this.reportOperator = reportOperator;
	}

	public String getReportManagecom() {
		return reportManagecom;
	}

	public void setReportManagecom(String reportManagecom) {
		this.reportManagecom = reportManagecom;
	}

	public Date getReportTime() {
		return reportTime;
	}

	public void setReportTime(Date reportTime) {
		this.reportTime = reportTime;
	}

	public String getAutoUwFlag() {
		return autoUwFlag;
	}

	public void setAutoUwFlag(String autoUwFlag) {
		this.autoUwFlag = autoUwFlag;
	}

	public String getSignOperator() {
		return signOperator;
	}

	public void setSignOperator(String signOperator) {
		this.signOperator = signOperator;
	}

	public Date getSignTime() {
		return signTime;
	}

	public void setSignTime(Date signTime) {
		this.signTime = signTime;
	}

	public String getSignManagecom() {
		return signManagecom;
	}

	public void setSignManagecom(String signManagecom) {
		this.signManagecom = signManagecom;
	}

	public String getClaimState() {
		return claimState;
	}

	public void setClaimState(String claimState) {
		this.claimState = claimState;
	}

	public String getClaimAuthority() {
		return claimAuthority;
	}

	public void setClaimAuthority(String claimAuthority) {
		this.claimAuthority = claimAuthority;
	}

	public String getSurveyFlag() {
		return surveyFlag;
	}

	public void setSurveyFlag(String surveyFlag) {
		this.surveyFlag = surveyFlag;
	}

	public String getClaimUwFlag() {
		return claimUwFlag;
	}

	public void setClaimUwFlag(String claimUwFlag) {
		this.claimUwFlag = claimUwFlag;
	}

	public String getSupplyCertifyFlag() {
		return supplyCertifyFlag;
	}

	public void setSupplyCertifyFlag(String supplyCertifyFlag) {
		this.supplyCertifyFlag = supplyCertifyFlag;
	}

	public String getTreatyFlag() {
		return treatyFlag;
	}

	public void setTreatyFlag(String treatyFlag) {
		this.treatyFlag = treatyFlag;
	}

	public String getDiscussFalg() {
		return discussFalg;
	}

	public void setDiscussFalg(String discussFalg) {
		this.discussFalg = discussFalg;
	}

	public String getMiddleCheckFlag() {
		return middleCheckFlag;
	}

	public void setMiddleCheckFlag(String middleCheckFlag) {
		this.middleCheckFlag = middleCheckFlag;
	}

	public String getRegOperator() {
		return regOperator;
	}

	public void setRegOperator(String regOperator) {
		this.regOperator = regOperator;
	}

	public String getRegManagecom() {
		return regManagecom;
	}

	public void setRegManagecom(String regManagecom) {
		this.regManagecom = regManagecom;
	}

	public Date getRegTime() {
		return regTime;
	}

	public void setRegTime(Date regTime) {
		this.regTime = regTime;
	}

	public String getCountTime() {
		return countTime;
	}

	public void setCountTime(String countTime) {
		this.countTime = countTime;
	}

	public String getExaConclusion() {
		return exaConclusion;
	}

	public void setExaConclusion(String exaConclusion) {
		this.exaConclusion = exaConclusion;
	}

	public String getBeneRelation() {
		return beneRelation;
	}

	public void setBeneRelation(String beneRelation) {
		this.beneRelation = beneRelation;
	}

	public String getBeneAge() {
		return beneAge;
	}

	public void setBeneAge(String beneAge) {
		this.beneAge = beneAge;
	}

	public String getRecipientID() {
		return recipientID;
	}

	public void setRecipientID(String recipientID) {
		this.recipientID = recipientID;
	}

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	public String getRecipientType() {
		return recipientType;
	}

	public void setRecipientType(String recipientType) {
		this.recipientType = recipientType;
	}

	public String getNoteID() {
		return noteID;
	}

	public void setNoteID(String noteID) {
		this.noteID = noteID;
	}

	public String getMailID() {
		return mailID;
	}

	public void setMailID(String mailID) {
		this.mailID = mailID;
	}

	public String getCareFlag() {
		return careFlag;
	}

	public void setCareFlag(String careFlag) {
		this.careFlag = careFlag;
	}

	public String getClmFlag() {
		return clmFlag;
	}

	public void setClmFlag(String clmFlag) {
		this.clmFlag = clmFlag;
	}

	public String getAdvancePayFlag() {
		return advancePayFlag;
	}

	public void setAdvancePayFlag(String advancePayFlag) {
		this.advancePayFlag = advancePayFlag;
	}

	public String getExternalFlag() {
		return externalFlag;
	}

	public void setExternalFlag(String externalFlag) {
		this.externalFlag = externalFlag;
	}

	public String getDealState() {
		return dealState;
	}

	public void setDealState(String dealState) {
		this.dealState = dealState;
	}

	public String getOuterIssueFlag() {
		return outerIssueFlag;
	}

	public void setOuterIssueFlag(String outerIssueFlag) {
		this.outerIssueFlag = outerIssueFlag;
	}

	public String getRegConclusion() {
		return regConclusion;
	}

	public void setRegConclusion(String regConclusion) {
		this.regConclusion = regConclusion;
	}

	public String getHangupFlag() {
		return hangupFlag;
	}

	public void setHangupFlag(String hangupFlag) {
		this.hangupFlag = hangupFlag;
	}

	public String getApproveResult() {
		return approveResult;
	}

	public void setApproveResult(String approveResult) {
		this.approveResult = approveResult;
	}

	public String getApproveConclusion() {
		return approveConclusion;
	}

	public void setApproveConclusion(String approveConclusion) {
		this.approveConclusion = approveConclusion;
	}

	public String getPrePayQConclusion() {
		return prePayQConclusion;
	}

	public void setPrePayQConclusion(String prePayQConclusion) {
		this.prePayQConclusion = prePayQConclusion;
	}

	public String getPrePayPConclusion() {
		return prePayPConclusion;
	}

	public void setPrePayPConclusion(String prePayPConclusion) {
		this.prePayPConclusion = prePayPConclusion;
	}

	public String getRuleConclusion() {
		return ruleConclusion;
	}

	public void setRuleConclusion(String ruleConclusion) {
		this.ruleConclusion = ruleConclusion;
	}

	public String getAppRuleConclusion() {
		return appRuleConclusion;
	}

	public void setAppRuleConclusion(String appRuleConclusion) {
		this.appRuleConclusion = appRuleConclusion;
	}

	public String getAppConclusion() {
		return appConclusion;
	}

	public void setAppConclusion(String appConclusion) {
		this.appConclusion = appConclusion;
	}

	public String getCaseExtractConclusion() {
		return caseExtractConclusion;
	}

	public void setCaseExtractConclusion(String caseExtractConclusion) {
		this.caseExtractConclusion = caseExtractConclusion;
	}

	public String getQualityCheckConclusion() {
		return qualityCheckConclusion;
	}

	public void setQualityCheckConclusion(String qualityCheckConclusion) {
		this.qualityCheckConclusion = qualityCheckConclusion;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public Double getTaskGrade() {
		return taskGrade;
	}

	public void setTaskGrade(Double taskGrade) {
		this.taskGrade = taskGrade;
	}

	public String getRouteId() {
		return routeId;
	}

	public void setRouteId(String routeId) {
		this.routeId = routeId;
	}

	public String getProcessFlag() {
		return processFlag;
	}

	public void setProcessFlag(String processFlag) {
		this.processFlag = processFlag;
	}

	public String getTaskCode() {
		return taskCode;
	}

	public void setTaskCode(String taskCode) {
		this.taskCode = taskCode;
	}

	public String getBpmTaskId() {
		return bpmTaskId;
	}

	public void setBpmTaskId(String bpmTaskId) {
		this.bpmTaskId = bpmTaskId;
	}

	public String getBpmTaskNum() {
		return bpmTaskNum;
	}

	public void setBpmTaskNum(String bpmTaskNum) {
		this.bpmTaskNum = bpmTaskNum;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getManagecom() {
		return managecom;
	}

	public void setManagecom(String managecom) {
		this.managecom = managecom;
	}

	public String getQueryTaskType() {
		return queryTaskType;
	}

	public void setQueryTaskType(String queryTaskType) {
		this.queryTaskType = queryTaskType;
	}
	@Override
	public String getBizId() {
		return null;
	}

}
