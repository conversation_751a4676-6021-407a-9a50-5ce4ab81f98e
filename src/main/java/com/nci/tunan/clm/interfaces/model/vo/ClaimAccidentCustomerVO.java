package com.nci.tunan.clm.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.Date;
import org.slf4j.Logger;
import com.nci.udmp.framework.model.BaseVO;
import com.nci.udmp.util.logging.LoggerFactory;
/**
 * 
 * @description 理赔事件客户  
 * <AUTHOR> <EMAIL>  
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class ClaimAccidentCustomerVO extends BaseVO {
	/** 
	 * @Fields logger : logger
	*/ 
	private static Logger logger = LoggerFactory.getLogger();

	/** 
	 * @Fields insuredId : 出险人ID
	*/ 
	private BigDecimal insuredId;

	/** 
	 * @Fields accName : 姓名
	*/ 
	private String accName;

	/** 
	 * @Fields accSex : 性别
	*/ 
	private String accSex;

	/** 
	 * @Fields accBirth : accBirth2
	*/ 
	private Date accBirth;

	/** 
	 * @Fields accBirth2 : accBirth2 
	*/ 
	private String accBirth2;

	/** 
	 * @Fields accCertiNo : accCertiNo
	*/ 
	private String accCertiNo;

	/** 
	 * @Fields accCertiType : accCertiType
	*/ 
	private Integer accCertiType;

	/** 
	 * @Fields policyNo : policyNo
	*/ 
	private String policyNo;

	/** 
	 * @Fields customerNo : 客户号
	*/ 
	private String customerNo;

	/** 
	 * @Fields isVIP : 是否VIP
	*/ 
	private String isVIP;

	public BigDecimal getInsuredId() {
		return insuredId;
	}

	public void setInsuredId(BigDecimal insuredId) {
		this.insuredId = insuredId;
	}

	public String getAccName() {
		return accName;
	}

	public void setAccName(String accName) {
		this.accName = accName;
	}

	public String getAccSex() {
		return accSex;
	}

	public void setAccSex(String accSex) {
		this.accSex = accSex;
	}

	public Date getAccBirth() {
		return accBirth;
	}

	public void setAccBirth(Date accBirth) {
		this.accBirth = accBirth;
	}

	public String getAccBirth2() {
		return accBirth2;
	}

	public void setAccBirth2(String accBirth2) {
		this.accBirth2 = accBirth2;
	}

	public String getAccCertiNo() {
		return accCertiNo;
	}

	public void setAccCertiNo(String accCertiNo) {
		this.accCertiNo = accCertiNo;
	}

	public Integer getAccCertiType() {
		return accCertiType;
	}

	public void setAccCertiType(Integer accCertiType) {
		this.accCertiType = accCertiType;
	}

	public String getPolicyNo() {
		return policyNo;
	}

	public void setPolicyNo(String policyNo) {
		this.policyNo = policyNo;
	}

	public String getCustomerNo() {
		return customerNo;
	}

	public void setCustomerNo(String customerNo) {
		this.customerNo = customerNo;
	}

	public String getIsVIP() {
		return isVIP;
	}

	public void setIsVIP(String isVIP) {
		this.isVIP = isVIP;
	}

	@Override
	public String getBizId() {
		return null;
	}

}
