package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.math.BigDecimal;
import java.util.Date;

/** 
 * @description ClaimReciveDirectInfoVO对象
 * <AUTHOR> 
 * @date 2024-10-09 10:19:36  
 */
public class ClaimReciveDirectInfoVO extends BaseVO {	
				 /** 
	* @Fields reciveDate :  获取成功时间
 	*/ 
	private Date reciveDate;
	 /** 
	* @Fields listId :  主键
 	*/ 
	private BigDecimal listId;
			 /** 
	* @Fields reciveFlag :  是否已获取（T
 	*/ 
	private BigDecimal reciveFlag;
		 /** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
		
				 public void setReciveDate(Date reciveDate) {
		this.reciveDate = reciveDate;
	}
	
	public Date getReciveDate() {
		return reciveDate;
	}
	 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
			 public void setReciveFlag(BigDecimal reciveFlag) {
		this.reciveFlag = reciveFlag;
	}
	
	public BigDecimal getReciveFlag() {
		return reciveFlag;
	}
		 public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	
	public BigDecimal getCaseId() {
		return caseId;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimReciveDirectInfoVO [" +
				"reciveDate="+reciveDate+","+
"listId="+listId+","+
"reciveFlag="+reciveFlag+","+
"caseId="+caseId+"]";
    }
}
