package com.nci.tunan.clm.interfaces.model.po;

import java.math.BigDecimal;
import java.util.Date;
import com.nci.udmp.framework.model.BasePO;

/** 
 * @description ClaimHolidayPO对象
 * <AUTHOR> 
 * @date 2017-07-28 18:01:12  
 */
public class ClaimHolidayPO extends BasePO {
 	/** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
	// holidayId  ---  BigDecimal  ---  NUMBER_16_0_主键ID;
	// holidayYearCode  ---  String  ---  VARCHAR2_4_0_年份;
    // holidayNameCode  ---  String  ---  VARCHAR2_2_0_节假日名称;
    // holidayStartTime  ---  Date  ---  DATE_7_0_节假日起期;
	// holidayEndTime  ---  Date  ---  DATE_7_0_节假日止期;
				
	public void setHolidayId(BigDecimal holidayId){
		setBigDecimal("holiday_id", holidayId);
	}
	
	public BigDecimal getHolidayId(){
		return getBigDecimal("holiday_id");
	}
	
	public void setHolidayYearCode(String holidayYearCode){
		setString("holiday_year_code", holidayYearCode);
	}
	
	public String getHolidayYearCode(){
		return getString("holiday_year_code");
	}
	
	public void setHolidayNameCode(String holidayNameCode){
        setString("holiday_name_code", holidayNameCode);
    }
    
    public String getHolidayNameCode(){
        return getString("holiday_name_code");
    }
    
    public void setHolidayStartTime(Date holidayStartTime){
        setUtilDate("holiday_start_time", holidayStartTime);
    }
    
    public Date getHolidayStartTime(){
        return getUtilDate("holiday_start_time");
    }
    
	public void setHolidayEndTime(Date holidayEndTime){
		setUtilDate("holiday_end_time", holidayEndTime);
	}
	
	public Date getHolidayEndTime(){
		return getUtilDate("holiday_end_time");
	}
				
	@Override
    public String toString() {
        return "ClaimHolidayPO [" +
				      "holidayId="+getHolidayId()+","+
				      "holidayYearCode="+getHolidayYearCode()+","+
				      "holidayNameCode="+getHolidayNameCode()+","+
				      "holidayStartTime="+getHolidayStartTime()+","+
				      "holidayEndTime="+getHolidayEndTime()+"]";
    }	
 }
