package com.nci.tunan.clm.interfaces.model.vo;

import java.lang.String;
import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 伤残等级代码表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class DeformityGradeVO extends BaseVO {	
	 /** 
	* @Fields deformityType :  伤残类型
 	*/ 
	private String deformityType;
	 /** 
	* @Fields deformityGradeName :  伤残级别名称
 	*/ 
	private String deformityGradeName;
	 /** 
	* @Fields deformityGrade :  伤残级别
 	*/ 
	private String deformityGrade;
		
	 public void setDeformityType(String deformityType) {
		this.deformityType = deformityType;
	}
	
	public String getDeformityType() {
		return deformityType;
	}
	 public void setDeformityGradeName(String deformityGradeName) {
		this.deformityGradeName = deformityGradeName;
	}
	
	public String getDeformityGradeName() {
		return deformityGradeName;
	}
	 public void setDeformityGrade(String deformityGrade) {
		this.deformityGrade = deformityGrade;
	}
	
	public String getDeformityGrade() {
		return deformityGrade;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "DeformityGradeVO [" +
				"deformityType="+deformityType+","+
"deformityGradeName="+deformityGradeName+","+
"deformityGrade="+deformityGrade+"]";
    }
}
