package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @description 保单投保人抄单表 
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class PolicyHolderVO extends BaseVO {	
	 /** 
	* @Fields addressId :  地址ID
 	*/ 
	private BigDecimal addressId;
		 /** 
	* @Fields jobUnderwrite :  职业核保等级
 	*/ 
	private String jobUnderwrite;
	 /** 
	* @Fields customerHeight :  客户身高
 	*/ 
	private BigDecimal customerHeight;
	 /** 
	* @Fields customerId :  客户号
 	*/ 
	private BigDecimal customerId;
		 /** 
	* @Fields jobCode :  职业代码，关联到职业代码表
 	*/ 
	private String jobCode;
	 /** 
	* @Fields customerWeight :  客户体重
 	*/ 
	private BigDecimal customerWeight;
	 /** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
	 /** 
	* @Fields applyCode :  投保单号
 	*/ 
	private String applyCode;
		 /** 
	* @Fields logId :  主键流水号
 	*/ 
	private BigDecimal logId;
	 /** 
	* @Fields copyDate :  理赔抄单基准日
 	*/ 
	private Date copyDate;
	 /** 
	* @Fields policyCode :  保单号
 	*/ 
	private String policyCode;
		 /** 
	* @Fields listId :  记录ID
 	*/ 
	private BigDecimal listId;
			 /** 
	* @Fields policyId :  保单ID
 	*/ 
	private BigDecimal policyId;
	 /** 
	* @Fields curFlag :  当前抄单标记
 	*/ 
	private BigDecimal curFlag;
	/**
	 * 投保人姓名
	 */
	private String policyHolderName;
	/**
     * 投保人地址信息
     */
	private String policyHolderAddress;	
	 /**
     * 与出险人关系ID
     */
    private String holderAndCustomerRealtionID;
	/**
	 * 与出险人关系
	 */
	private String holderAndCustomerRealtion;
	/**
	 * 联系电话
	 */
	private String policyHolderPhone;
	
	/** 
	* @Fields customerCertiCode :  证件号码
 	*/ 
    private String customerCertiCode;
	
    /**
     * @Fields policyCodeSex :  投保人性别
     */
    private BigDecimal policyCodeSex;
    /**
     * @Fields isHignSurvey :  是否调研
     */
    private String isHignSurvey;
    /**
	 * @Fields annualIncomeCeil : 年收入
	 */
	private BigDecimal annualIncomeCeil;
	/**
	 * @Fields incomeSource : 收入来源
	 */
	private String incomeSource;
	/**
	 * @Fields residentType : 居民类型
	 */
	private String residentType;
	/**
	 * @Fields agentRelation : 投保人与销售人员关系
	 */
	private String agentRelation;
	/**
	 * @Fields applicantSpePeople : 特殊人员类型
	 */
	private BigDecimal applicantSpePeople;
	/**
	 * @Fields sociSecu : 社保标识
	 */
	private BigDecimal sociSecu;
	/**
	 * @Fields newResident : 新市民标识
	 */
	private BigDecimal newResident;
	/**
	 * @Fields exceptionHealthFlag : 异常健康告知标识
	 */
    private BigDecimal exceptionHealthFlag;
	/**
	 * @Fields insuredBodyOption : 最终的优选体标准体选项 1_标准体，2_优选体
	 */
	private String insuredBodyOption;
	
	/**
	 * 
	 * @Fields :EducationalBackground : 学历
	 */
	private String educationalBackground;
	
	/**
	 * @Fields :riskEstimateScore : 风险筛查分数
	 */
	private String riskEstimateScore;
	
	/**
	 * 
	 * @Fields :nonSmoker : 非吸烟者
	 */
	private String nonSmoker;
	
	 public BigDecimal getNewResident() {
		return newResident;
	}

	public void setNewResident(BigDecimal newResident) {
		this.newResident = newResident;
	}

	public BigDecimal getSociSecu() {
		return sociSecu;
	}

	public void setSociSecu(BigDecimal sociSecu) {
		this.sociSecu = sociSecu;
	}

	public BigDecimal getApplicantSpePeople() {
		return applicantSpePeople;
	}

	public void setApplicantSpePeople(BigDecimal applicantSpePeople) {
		this.applicantSpePeople = applicantSpePeople;
	}

	public String getAgentRelation() {
		return agentRelation;
	}

	public void setAgentRelation(String agentRelation) {
		this.agentRelation = agentRelation;
	}

	public BigDecimal getAnnualIncomeCeil() {
		return annualIncomeCeil;
	}

	public void setAnnualIncomeCeil(BigDecimal annualIncomeCeil) {
		this.annualIncomeCeil = annualIncomeCeil;
	}

	public String getIncomeSource() {
		return incomeSource;
	}

	public void setIncomeSource(String incomeSource) {
		this.incomeSource = incomeSource;
	}

	public String getResidentType() {
		return residentType;
	}

	public void setResidentType(String residentType) {
		this.residentType = residentType;
	}

	public String getIsHignSurvey() {
		return isHignSurvey;
	}

	public void setIsHignSurvey(String isHignSurvey) {
		this.isHignSurvey = isHignSurvey;
	}

	public BigDecimal getPolicyCodeSex() {
		return policyCodeSex;
	}

	public void setPolicyCodeSex(BigDecimal policyCodeSex) {
		this.policyCodeSex = policyCodeSex;
	}

	public String getCustomerCertiCode() {
		return customerCertiCode;
	}

	public void setCustomerCertiCode(String customerCertiCode) {
		this.customerCertiCode = customerCertiCode;
	}

	public String getHolderAndCustomerRealtionID() {
		return holderAndCustomerRealtionID;
	}

	public void setHolderAndCustomerRealtionID(String holderAndCustomerRealtionID) {
		this.holderAndCustomerRealtionID = holderAndCustomerRealtionID;
	}

	public String getPolicyHolderName() {
        return policyHolderName;
    }

    public void setPolicyHolderName(String policyHolderName) {
        this.policyHolderName = policyHolderName;
    }

    public String getPolicyHolderAddress() {
        return policyHolderAddress;
    }

    public void setPolicyHolderAddress(String policyHolderAddress) {
        this.policyHolderAddress = policyHolderAddress;
    }

    public String getHolderAndCustomerRealtion() {
        return holderAndCustomerRealtion;
    }

    public void setHolderAndCustomerRealtion(String holderAndCustomerRealtion) {
        this.holderAndCustomerRealtion = holderAndCustomerRealtion;
    }

    public String getPolicyHolderPhone() {
        return policyHolderPhone;
    }

    public void setPolicyHolderPhone(String policyHolderPhone) {
        this.policyHolderPhone = policyHolderPhone;
    }

    public void setAddressId(BigDecimal addressId) {
		this.addressId = addressId;
	}
	
	public BigDecimal getAddressId() {
		return addressId;
	}
		
	 public String getJobUnderwrite() {
		return jobUnderwrite;
	}

	public void setJobUnderwrite(String jobUnderwrite) {
		this.jobUnderwrite = jobUnderwrite;
	}

	public void setCustomerHeight(BigDecimal customerHeight) {
		this.customerHeight = customerHeight;
	}
	
	public BigDecimal getCustomerHeight() {
		return customerHeight;
	}
	 public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}
	
	public BigDecimal getCustomerId() {
		return customerId;
	}
		 public void setJobCode(String jobCode) {
		this.jobCode = jobCode;
	}
	
	public String getJobCode() {
		return jobCode;
	}
	 public void setCustomerWeight(BigDecimal customerWeight) {
		this.customerWeight = customerWeight;
	}
	
	public BigDecimal getCustomerWeight() {
		return customerWeight;
	}
	 public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	
	public BigDecimal getCaseId() {
		return caseId;
	}
	 public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}
	
	public String getApplyCode() {
		return applyCode;
	}
		 public void setLogId(BigDecimal logId) {
		this.logId = logId;
	}
	
	public BigDecimal getLogId() {
		return logId;
	}
	 public void setCopyDate(Date copyDate) {
		this.copyDate = copyDate;
	}
	
	public Date getCopyDate() {
		return copyDate;
	}
	 public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	
	public String getPolicyCode() {
		return policyCode;
	}
		 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
			 public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}
	
	public BigDecimal getPolicyId() {
		return policyId;
	}
	 public void setCurFlag(BigDecimal curFlag) {
		this.curFlag = curFlag;
	}
	
	public BigDecimal getCurFlag() {
		return curFlag;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    	
    public BigDecimal getExceptionHealthFlag() {
		return exceptionHealthFlag;
	}

	public void setExceptionHealthFlag(BigDecimal exceptionHealthFlag) {
		this.exceptionHealthFlag = exceptionHealthFlag;
	}

	
	public String getInsuredBodyOption() {
		return insuredBodyOption;
	}

	public void setInsuredBodyOption(String insuredBodyOption) {
		this.insuredBodyOption = insuredBodyOption;
	}

	public String getEducationalBackground() {
		return educationalBackground;
	}

	public void setEducationalBackground(String educationalBackground) {
		this.educationalBackground = educationalBackground;
	}

	public String getRiskEstimateScore() {
		return riskEstimateScore;
	}

	public void setRiskEstimateScore(String riskEstimateScore) {
		this.riskEstimateScore = riskEstimateScore;
	}
	
	
	public String getNonSmoker() {
		return nonSmoker;
	}

	public void setNonSmoker(String nonSmoker) {
		this.nonSmoker = nonSmoker;
	}

	@Override
	public String toString() {
		return "PolicyHolderVO [addressId=" + addressId + ", jobUnderwrite="
				+ jobUnderwrite + ", customerHeight=" + customerHeight
				+ ", customerId=" + customerId + ", jobCode=" + jobCode
				+ ", customerWeight=" + customerWeight + ", caseId=" + caseId
				+ ", applyCode=" + applyCode + ", logId=" + logId
				+ ", copyDate=" + copyDate + ", policyCode=" + policyCode
				+ ", listId=" + listId + ", policyId=" + policyId
				+ ", curFlag=" + curFlag + ", policyHolderName="
				+ policyHolderName + ", policyHolderAddress="
				+ policyHolderAddress + ", holderAndCustomerRealtionID="
				+ holderAndCustomerRealtionID + ", holderAndCustomerRealtion="
				+ holderAndCustomerRealtion + ", policyHolderPhone="
				+ policyHolderPhone + ", customerCertiCode="
				+ customerCertiCode + ", policyCodeSex=" + policyCodeSex
				+ ", isHignSurvey=" + isHignSurvey + ", annualIncomeCeil="
				+ annualIncomeCeil + ", incomeSource=" + incomeSource
				+ ", residentType=" + residentType + ", agentRelation="
				+ agentRelation + ", applicantSpePeople=" + applicantSpePeople
				+ ", sociSecu=" + sociSecu + ", newResident=" + newResident
				+ ", exceptionHealthFlag=" + exceptionHealthFlag + "]";
	}

	
}
