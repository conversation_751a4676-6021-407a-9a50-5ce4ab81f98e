package com.nci.tunan.clm.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseBO;

import java.math.BigDecimal;


/**
 * 
 * @description 理赔欺诈图
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class ClaimCheatAtlasBO extends BaseBO {	
	 /** 
	* @Fields frmsRuleScore :  规则分值
 	*/ 
	private String frmsRuleScore;
			 /** 
	* @Fields caseNo :  赔案号
 	*/ 
	private String caseNo;
		 /** 
	* @Fields policyCode :  保单号
 	*/ 
	private String policyCode;
	 /** 
	* @Fields frmsCustomerCount :  关联客户数
 	*/ 
	private String frmsCustomerCount;
		 /** 
	* @Fields frmsRuleCode :  规则代码
 	*/ 
	private String frmsRuleCode;
	 /** 
	* @Fields frmsClaimPay :  关联案值金额
 	*/ 
	private BigDecimal frmsClaimPay;
		 /** 
	* @Fields cheatAtlasId :  主键ID
 	*/ 
	private BigDecimal cheatAtlasId;
	 /** 
	* @Fields DANGERTYPE dangertype : 风险类型(03-代理人层风险聚集、04-客户层风险聚集)
	*/ 
	private String dangerType;
	/** 
   * @Fields dangerLevel :  风险等级
   */ 
   private String dangerLevel;	
			
	 public String getDangerType() {
		return dangerType;
	}

	public String getDangerLevel() {
		return dangerLevel;
	}

	public void setDangerType(String dangerType) {
		this.dangerType = dangerType;
	}

	public void setDangerLevel(String dangerLevel) {
		this.dangerLevel = dangerLevel;
	}

	public void setFrmsRuleScore(String frmsRuleScore) {
		this.frmsRuleScore = frmsRuleScore;
	}
	
	public String getFrmsRuleScore() {
		return frmsRuleScore;
	}
			 public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}
	
	public String getCaseNo() {
		return caseNo;
	}
		 public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	
	public String getPolicyCode() {
		return policyCode;
	}
	 public void setFrmsCustomerCount(String frmsCustomerCount) {
		this.frmsCustomerCount = frmsCustomerCount;
	}
	
	public String getFrmsCustomerCount() {
		return frmsCustomerCount;
	}
		 public void setFrmsRuleCode(String frmsRuleCode) {
		this.frmsRuleCode = frmsRuleCode;
	}
	
	public String getFrmsRuleCode() {
		return frmsRuleCode;
	}
	 public void setFrmsClaimPay(BigDecimal frmsClaimPay) {
		this.frmsClaimPay = frmsClaimPay;
	}
	
	public BigDecimal getFrmsClaimPay() {
		return frmsClaimPay;
	}
		 public void setCheatAtlasId(BigDecimal cheatAtlasId) {
		this.cheatAtlasId = cheatAtlasId;
	}
	
	public BigDecimal getCheatAtlasId() {
		return cheatAtlasId;
	}
			
	@Override
    public String getBizId() {
        return null;
    }

	@Override
	public String toString() {
		return "ClaimCheatAtlasBO [frmsRuleScore=" + frmsRuleScore + ", caseNo=" + caseNo + ", policyCode=" + policyCode
				+ ", frmsCustomerCount=" + frmsCustomerCount + ", frmsRuleCode=" + frmsRuleCode + ", frmsClaimPay="
				+ frmsClaimPay + ", cheatAtlasId=" + cheatAtlasId + ", dangerType=" + dangerType + ", dangerLevel="
				+ dangerLevel + "]";
	}
}
