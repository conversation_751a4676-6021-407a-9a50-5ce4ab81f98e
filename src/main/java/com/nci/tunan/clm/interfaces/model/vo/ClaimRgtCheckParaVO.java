package com.nci.tunan.clm.interfaces.model.vo;

import java.math.BigDecimal;
import java.sql.Date;
import org.slf4j.Logger;
import com.nci.udmp.framework.model.BaseVO;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * 
 * @description 理赔立案复核参数表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class ClaimRgtCheckParaVO extends BaseVO {

	/**
	 * 序列号
	 */
	private static Logger logger = LoggerFactory.getLogger();

	/**
	 * 插入人
	 */
	private BigDecimal insertBy;

	/**
	 * ruleValue
	 */
	private String ruleValue;

	/**
	 * subParaStart
	 */
	private String subParaStart;

	/**
	 * 规则类型
	 */
	private String ruleType;

	/**
	 * ruleState
	 */
	private String ruleState;

	/**
	 * 插入时间
	 */
	private Date insertTimestamp;

	/**
	 * 插入时间
	 */
	private Date insertTime;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 规则号
	 */
	private String ruleNo;

	/**
	 * 更新人
	 */
	private BigDecimal updateBy;

	/**
	 * checkParaId
	 */
	private BigDecimal checkParaId;

	/**
	 * 更新时间戳
	 */
	private Date updateTimestamp;

	/**
	 * subParaEnd
	 */
	private String subParaEnd;

	public BigDecimal getInsertBy() {
		return insertBy;
	}

	public void setInsertBy(BigDecimal insertBy) {
		this.insertBy = insertBy;
	}

	public String getRuleValue() {
		return ruleValue;
	}

	public void setRuleValue(String ruleValue) {
		this.ruleValue = ruleValue;
	}

	public String getSubParaStart() {
		return subParaStart;
	}

	public void setSubParaStart(String subParaStart) {
		this.subParaStart = subParaStart;
	}

	public String getRuleType() {
		return ruleType;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}

	public String getRuleState() {
		return ruleState;
	}

	public void setRuleState(String ruleState) {
		this.ruleState = ruleState;
	}

	public Date getInsertTimestamp() {
		return insertTimestamp;
	}

	public void setInsertTimestamp(Date insertTimestamp) {
		this.insertTimestamp = insertTimestamp;
	}

	public Date getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getRuleNo() {
		return ruleNo;
	}

	public void setRuleNo(String ruleNo) {
		this.ruleNo = ruleNo;
	}

	public BigDecimal getUpdateBy() {
		return updateBy;
	}

	public void setUpdateBy(BigDecimal updateBy) {
		this.updateBy = updateBy;
	}

	public BigDecimal getCheckParaId() {
		return checkParaId;
	}

	public void setCheckParaId(BigDecimal checkParaId) {
		this.checkParaId = checkParaId;
	}

	public Date getUpdateTimestamp() {
		return updateTimestamp;
	}

	public void setUpdateTimestamp(Date updateTimestamp) {
		this.updateTimestamp = updateTimestamp;
	}

	public String getSubParaEnd() {
		return subParaEnd;
	}

	public void setSubParaEnd(String subParaEnd) {
		this.subParaEnd = subParaEnd;
	}

	@Override
	public String getBizId() {
		return null;
	}

}
