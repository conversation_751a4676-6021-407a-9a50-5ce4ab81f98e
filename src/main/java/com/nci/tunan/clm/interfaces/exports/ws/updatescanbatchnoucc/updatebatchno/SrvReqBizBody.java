package com.nci.tunan.clm.interfaces.exports.ws.updatescanbatchnoucc.updatebatchno;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody")
public class SrvReqBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private final static  long serialVersionUID = 1L;
    
    @XmlElement(name = "InputData")
    protected com.nci.tunan.clm.interfaces.vo.updateScanBatchNo.UpdateBatchNoReqVO inputData;
    
    public com.nci.tunan.clm.interfaces.vo.updateScanBatchNo.UpdateBatchNoReqVO getInputData() {
        return inputData;
    }
    public void setInputData(com.nci.tunan.clm.interfaces.vo.updateScanBatchNo.UpdateBatchNoReqVO inputData) {
        this.inputData = inputData;
    }
}


