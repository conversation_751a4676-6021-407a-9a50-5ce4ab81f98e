package com.nci.tunan.clm.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseBO;

import java.math.BigDecimal;

/**
 * 
 * @description 理赔失能信息表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class ClaimDisabilityBO extends BaseBO {	
	 /** 
	* @Fields disabilityType :  失能类型
 	*/ 
	private BigDecimal disabilityType;
		 /** 
	* @Fields disabilityGrade :  失能等级
 	*/ 
	private String disabilityGrade;
			 /** 
	* @Fields disabilityDetail :  失能程度
 	*/ 
	private String disabilityDetail;
	 /** 
	* @Fields disabilityId :  序列号
 	*/ 
	private BigDecimal disabilityId;
				 /** 
	* @Fields payRate :  给付比例
 	*/ 
	private BigDecimal payRate;
	 /** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
		
	 public void setDisabilityType(BigDecimal disabilityType) {
		this.disabilityType = disabilityType;
	}
	
	public BigDecimal getDisabilityType() {
		return disabilityType;
	}
		 public void setDisabilityGrade(String disabilityGrade) {
		this.disabilityGrade = disabilityGrade;
	}
	
	public String getDisabilityGrade() {
		return disabilityGrade;
	}
			 public void setDisabilityDetail(String disabilityDetail) {
		this.disabilityDetail = disabilityDetail;
	}
	
	public String getDisabilityDetail() {
		return disabilityDetail;
	}
	 public void setDisabilityId(BigDecimal disabilityId) {
		this.disabilityId = disabilityId;
	}
	
	public BigDecimal getDisabilityId() {
		return disabilityId;
	}
				 public void setPayRate(BigDecimal payRate) {
		this.payRate = payRate;
	}
	
	public BigDecimal getPayRate() {
		return payRate;
	}
	 public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	
	public BigDecimal getCaseId() {
		return caseId;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimDisabilityBO [" +
				"disabilityType="+disabilityType+","+
"disabilityGrade="+disabilityGrade+","+
"disabilityDetail="+disabilityDetail+","+
"disabilityId="+disabilityId+","+
"payRate="+payRate+","+
"caseId="+caseId+"]";
    }
}
