/*package com.nci.tunan.clm.interfaces.model.sign.bo;

import com.nci.udmp.framework.model.BaseBO;

*//**
 * 签收确认调工作流的BO
 * <AUTHOR>
 *
 *//*
public class SignConfirmReqBO extends BaseBO {
	private String businessType;
	private String roleId;
	private String operator;
	private String managecom;
	private Double taskGrade;
	private String routeId;
	private String processFlag;
	private String bpmTaskId;
	public String getBusinessType() {
		return businessType;
	}
	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}
	public String getRoleId() {
		return roleId;
	}
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	public String getOperator() {
		return operator;
	}
	public void setOperator(String operator) {
		this.operator = operator;
	}
	public String getManagecom() {
		return managecom;
	}
	public void setManagecom(String managecom) {
		this.managecom = managecom;
	}
	public Double getTaskGrade() {
		return taskGrade;
	}
	public void setTaskGrade(Double taskGrade) {
		this.taskGrade = taskGrade;
	}
	public String getRouteId() {
		return routeId;
	}
	public void setRouteId(String routeId) {
		this.routeId = routeId;
	}
	public String getProcessFlag() {
		return processFlag;
	}
	public void setProcessFlag(String processFlag) {
		this.processFlag = processFlag;
	}
	public String getBpmTaskId() {
		return bpmTaskId;
	}
	public void setBpmTaskId(String bpmTaskId) {
		this.bpmTaskId = bpmTaskId;
	}
	@Override
	public String getBizId() {
		return null;
	}
}*/

package com.nci.tunan.clm.interfaces.model.bo;

import java.util.Date;

import com.nci.udmp.framework.model.BaseBO;

/**
 * 
 * @description 签收确认请求对象
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date  2015年5月15日 上午10:08:31
 */
public class SignConfirmReqBO extends BaseBO {

	/**
	 * 序列号
	 */
	private static final long serialVersionUID = 3629813497556631224L;

	/** 赔案号 */
	protected String caseNo;

	/** 出险人姓名 */
	protected String customerName;

	/**出险人性别 */
	protected String customerSex;

	/** 证件号码 */
	protected String customerId;

	/** 出险日期 */
	protected Date accDate;

	/** 绿色通道标识 */
	protected String greenFlag;

	/**出险人客户号 */
	protected String customerNo;

	/**理赔类型 */
	protected String claimType;
	
	/**报案操作人 */
	protected String reportOperator;
	
	/**机构（赔案报案机构） */
	protected String reportManagecom;
	
	/**报案时间 */
	protected Date reportTime;
	
	/**预付标识 */
	protected String advancePayFlag;
	
	/**外包录入标识 */
	protected String externalFlag;
	
	/**二核标识 */
	protected String claimUwFlag;
	
	/**补充单证标识 */
	protected String supplyCertifyFlag;
	
	/** 协谈标识 */
	protected String treatyFlag;
	
	/** 合议标识 */
	protected String discussFlag;
	
	/** 审核结论 */
	protected String auditConclusion;
	
	/** 签收人 */
	protected String signOperator;

	/** 签收时间 */
	protected Date signTime;

	/** 签收机构 */
	protected String signManagecom;
	
	/** 立案操作人 */
	protected String rgtOperator;
	
	/** 机构（立案机构） */
	protected String rgtManagecom;
	
	/**立案时间 */
	protected Date rgtTime;

	/**赔案状态 */
	protected String claimState;

	/** 赔案权限 */
	protected String claimAuthority;

	/**调查标识 */
	protected String surveyFlag;

	/**立案结论 */
	protected String regConclusion;

	/**业务类型 */
	protected String businessType;

	/**任务打分 */
	protected Double taskGrade;

	/**路径ID */
	protected String routeId;

	/**工作流任务ID */
	protected String bpmTaskId;

	/**角色编码 */
	protected String roleId;

	/**操作员编码 */
	protected String operator;

	/**登陆机构 */
	protected String managecom;
	
	/**模板标识 */
	protected String processFlag;

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getCustomerSex() {
		return customerSex;
	}

	public void setCustomerSex(String customerSex) {
		this.customerSex = customerSex;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public Date getAccDate() {
		return accDate;
	}

	public void setAccDate(Date accDate) {
		this.accDate = accDate;
	}

	public String getGreenFlag() {
		return greenFlag;
	}

	public void setGreenFlag(String greenFlag) {
		this.greenFlag = greenFlag;
	}

	public String getCustomerNo() {
		return customerNo;
	}

	public void setCustomerNo(String customerNo) {
		this.customerNo = customerNo;
	}

	public String getClaimType() {
		return claimType;
	}

	public void setClaimType(String claimType) {
		this.claimType = claimType;
	}

	public String getSignOperator() {
		return signOperator;
	}

	public void setSignOperator(String signOperator) {
		this.signOperator = signOperator;
	}

	public Date getSignTime() {
		return signTime;
	}

	public void setSignTime(Date signTime) {
		this.signTime = signTime;
	}

	public String getSignManagecom() {
		return signManagecom;
	}

	public void setSignManagecom(String signManagecom) {
		this.signManagecom = signManagecom;
	}

	public String getClaimState() {
		return claimState;
	}

	public void setClaimState(String claimState) {
		this.claimState = claimState;
	}

	public String getClaimAuthority() {
		return claimAuthority;
	}

	public void setClaimAuthority(String claimAuthority) {
		this.claimAuthority = claimAuthority;
	}

	public String getSurveyFlag() {
		return surveyFlag;
	}

	public void setSurveyFlag(String surveyFlag) {
		this.surveyFlag = surveyFlag;
	}

	public String getRegConclusion() {
		return regConclusion;
	}

	public void setRegConclusion(String regConclusion) {
		this.regConclusion = regConclusion;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public Double getTaskGrade() {
		return taskGrade;
	}

	public void setTaskGrade(Double taskGrade) {
		this.taskGrade = taskGrade;
	}

	public String getRouteId() {
		return routeId;
	}

	public void setRouteId(String routeId) {
		this.routeId = routeId;
	}

	public String getBpmTaskId() {
		return bpmTaskId;
	}

	public void setBpmTaskId(String bpmTaskId) {
		this.bpmTaskId = bpmTaskId;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getManagecom() {
		return managecom;
	}

	public void setManagecom(String managecom) {
		this.managecom = managecom;
	}

	
	public String getReportOperator (){
	    return reportOperator;
	}

	
	public void setReportOperator (String reportOperator){
	    this.reportOperator = reportOperator;
	}

	
	public String getReportManagecom (){
	    return reportManagecom;
	}

	
	public void setReportManagecom (String reportManagecom){
	    this.reportManagecom = reportManagecom;
	}
	
	
	public Date getReportTime (){
	    return reportTime;
	}

	
	public void setReportTime (Date reportTime){
	    this.reportTime = reportTime;
	}

	public String getAdvancePayFlag (){
	    return advancePayFlag;
	}

	
	public void setAdvancePayFlag (String advancePayFlag){
	    this.advancePayFlag = advancePayFlag;
	}

	
	public String getExternalFlag (){
	    return externalFlag;
	}

	
	public void setExternalFlag (String externalFlag){
	    this.externalFlag = externalFlag;
	}

	
	public String getClaimUwFlag (){
	    return claimUwFlag;
	}

	
	public void setClaimUwFlag (String claimUwFlag){
	    this.claimUwFlag = claimUwFlag;
	}

	
	public String getSupplyCertifyFlag (){
	    return supplyCertifyFlag;
	}

	
	public void setSupplyCertifyFlag (String supplyCertifyFlag){
	    this.supplyCertifyFlag = supplyCertifyFlag;
	}

	
	public String getTreatyFlag (){
	    return treatyFlag;
	}

	
	public void setTreatyFlag (String treatyFlag){
	    this.treatyFlag = treatyFlag;
	}

	
	
	public String getDiscussFlag (){
	    return discussFlag;
	}

	
	public void setDiscussFlag (String discussFlag){
	    this.discussFlag = discussFlag;
	}

	public String getAuditConclusion (){
	    return auditConclusion;
	}

	
	public void setAuditConclusion (String auditConclusion){
	    this.auditConclusion = auditConclusion;
	}

	
	
	public String getRgtOperator (){
	    return rgtOperator;
	}

	
	public void setRgtOperator (String rgtOperator){
	    this.rgtOperator = rgtOperator;
	}

	
	public String getRgtManagecom (){
	    return rgtManagecom;
	}

	
	public void setRgtManagecom (String rgtManagecom){
	    this.rgtManagecom = rgtManagecom;
	}

	public Date getRgtTime (){
	    return rgtTime;
	}

	
	public void setRgtTime (Date rgtTime){
	    this.rgtTime = rgtTime;
	}

	
	public String getProcessFlag (){
	    return processFlag;
	}

	
	public void setProcessFlag (String processFlag){
	    this.processFlag = processFlag;
	}

	@Override
	public String getBizId() {
		return null;
	}
}
