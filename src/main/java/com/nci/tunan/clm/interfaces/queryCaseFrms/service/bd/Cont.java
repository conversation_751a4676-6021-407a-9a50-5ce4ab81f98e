
package com.nci.tunan.clm.interfaces.queryCaseFrms.service.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>cont complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�����ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="cont"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="frms_contno"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *               &lt;minLength value="1"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_polno"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *               &lt;minLength value="1"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_contno_conttype"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *               &lt;minLength value="1"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_riskname"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_riskperiod"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_insurance_kind"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_getdutykind"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_kindname"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_givereason"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_agentcode"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_agentname"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_rearagentcode"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_appntmobile"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_contno_active_time"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_contno_cvalidate"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_insurer_code"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_insurer"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_insured_code"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_insured"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="frms_manage8_code"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;whiteSpace value="collapse"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "cont", propOrder = {
    "frmsContno",
    "frmsPolno",
    "frmsContnoConttype",
    "frmsRiskname",
    "frmsRiskperiod",
    "frmsInsuranceKind",
    "frmsGetdutykind",
    "frmsKindname",
    "frmsGivereason",
    "frmsAgentcode",
    "frmsAgentname",
    "frmsRearagentcode",
    "frmsAppntmobile",
    "frmsContnoActiveTime",
    "frmsContnoCvalidate",
    "frmsInsurerCode",
    "frmsInsurer",
    "frmsInsuredCode",
    "frmsInsured",
    "frmsManage8Code"
})
public class Cont {

    @XmlElement(name = "frms_contno", required = true)
    protected String frmsContno;
    @XmlElement(name = "frms_polno", required = true)
    protected String frmsPolno;
    @XmlElement(name = "frms_contno_conttype", required = true)
    protected String frmsContnoConttype;
    @XmlElement(name = "frms_riskname", required = true)
    protected String frmsRiskname;
    @XmlElement(name = "frms_riskperiod", required = true)
    protected String frmsRiskperiod;
    @XmlElement(name = "frms_insurance_kind", required = true)
    protected String frmsInsuranceKind;
    @XmlElement(name = "frms_getdutykind", required = true)
    protected String frmsGetdutykind;
    @XmlElement(name = "frms_kindname", required = true)
    protected String frmsKindname;
    @XmlElement(name = "frms_givereason", required = true)
    protected String frmsGivereason;
    @XmlElement(name = "frms_agentcode", required = true)
    protected String frmsAgentcode;
    @XmlElement(name = "frms_agentname", required = true)
    protected String frmsAgentname;
    @XmlElement(name = "frms_rearagentcode", required = true)
    protected String frmsRearagentcode;
    @XmlElement(name = "frms_appntmobile", required = true)
    protected String frmsAppntmobile;
    @XmlElement(name = "frms_contno_active_time", required = true)
    protected String frmsContnoActiveTime;
    @XmlElement(name = "frms_contno_cvalidate", required = true)
    protected String frmsContnoCvalidate;
    @XmlElement(name = "frms_insurer_code", required = true)
    protected String frmsInsurerCode;
    @XmlElement(name = "frms_insurer", required = true)
    protected String frmsInsurer;
    @XmlElement(name = "frms_insured_code", required = true)
    protected String frmsInsuredCode;
    @XmlElement(name = "frms_insured", required = true)
    protected String frmsInsured;
    @XmlElement(name = "frms_manage8_code", required = true)
    protected String frmsManage8Code;

    /**
     * ��ȡfrmsContno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsContno() {
        return frmsContno;
    }

    /**
     * ����frmsContno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsContno(String value) {
        this.frmsContno = value;
    }

    /**
     * ��ȡfrmsPolno���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsPolno() {
        return frmsPolno;
    }

    /**
     * ����frmsPolno���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsPolno(String value) {
        this.frmsPolno = value;
    }

    /**
     * ��ȡfrmsContnoConttype���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsContnoConttype() {
        return frmsContnoConttype;
    }

    /**
     * ����frmsContnoConttype���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsContnoConttype(String value) {
        this.frmsContnoConttype = value;
    }

    /**
     * ��ȡfrmsRiskname���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsRiskname() {
        return frmsRiskname;
    }

    /**
     * ����frmsRiskname���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsRiskname(String value) {
        this.frmsRiskname = value;
    }

    /**
     * ��ȡfrmsRiskperiod���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsRiskperiod() {
        return frmsRiskperiod;
    }

    /**
     * ����frmsRiskperiod���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsRiskperiod(String value) {
        this.frmsRiskperiod = value;
    }

    /**
     * ��ȡfrmsInsuranceKind���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsInsuranceKind() {
        return frmsInsuranceKind;
    }

    /**
     * ����frmsInsuranceKind���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsInsuranceKind(String value) {
        this.frmsInsuranceKind = value;
    }

    /**
     * ��ȡfrmsGetdutykind���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsGetdutykind() {
        return frmsGetdutykind;
    }

    /**
     * ����frmsGetdutykind���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsGetdutykind(String value) {
        this.frmsGetdutykind = value;
    }

    /**
     * ��ȡfrmsKindname���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsKindname() {
        return frmsKindname;
    }

    /**
     * ����frmsKindname���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsKindname(String value) {
        this.frmsKindname = value;
    }

    /**
     * ��ȡfrmsGivereason���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsGivereason() {
        return frmsGivereason;
    }

    /**
     * ����frmsGivereason���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsGivereason(String value) {
        this.frmsGivereason = value;
    }

    /**
     * ��ȡfrmsAgentcode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsAgentcode() {
        return frmsAgentcode;
    }

    /**
     * ����frmsAgentcode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsAgentcode(String value) {
        this.frmsAgentcode = value;
    }

    /**
     * ��ȡfrmsAgentname���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsAgentname() {
        return frmsAgentname;
    }

    /**
     * ����frmsAgentname���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsAgentname(String value) {
        this.frmsAgentname = value;
    }

    /**
     * ��ȡfrmsRearagentcode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsRearagentcode() {
        return frmsRearagentcode;
    }

    /**
     * ����frmsRearagentcode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsRearagentcode(String value) {
        this.frmsRearagentcode = value;
    }

    /**
     * ��ȡfrmsAppntmobile���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsAppntmobile() {
        return frmsAppntmobile;
    }

    /**
     * ����frmsAppntmobile���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsAppntmobile(String value) {
        this.frmsAppntmobile = value;
    }

    /**
     * ��ȡfrmsContnoActiveTime���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsContnoActiveTime() {
        return frmsContnoActiveTime;
    }

    /**
     * ����frmsContnoActiveTime���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsContnoActiveTime(String value) {
        this.frmsContnoActiveTime = value;
    }

    /**
     * ��ȡfrmsContnoCvalidate���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsContnoCvalidate() {
        return frmsContnoCvalidate;
    }

    /**
     * ����frmsContnoCvalidate���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsContnoCvalidate(String value) {
        this.frmsContnoCvalidate = value;
    }

    /**
     * ��ȡfrmsInsurerCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsInsurerCode() {
        return frmsInsurerCode;
    }

    /**
     * ����frmsInsurerCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsInsurerCode(String value) {
        this.frmsInsurerCode = value;
    }

    /**
     * ��ȡfrmsInsurer���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsInsurer() {
        return frmsInsurer;
    }

    /**
     * ����frmsInsurer���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsInsurer(String value) {
        this.frmsInsurer = value;
    }

    /**
     * ��ȡfrmsInsuredCode���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsInsuredCode() {
        return frmsInsuredCode;
    }

    /**
     * ����frmsInsuredCode���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsInsuredCode(String value) {
        this.frmsInsuredCode = value;
    }

    /**
     * ��ȡfrmsInsured���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsInsured() {
        return frmsInsured;
    }

    /**
     * ����frmsInsured���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsInsured(String value) {
        this.frmsInsured = value;
    }

    /**
     * ��ȡfrmsManage8Code���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFrmsManage8Code() {
        return frmsManage8Code;
    }

    /**
     * ����frmsManage8Code���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFrmsManage8Code(String value) {
        this.frmsManage8Code = value;
    }

}
