package com.nci.tunan.clm.interfaces.model.vo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.nci.tunan.clm.interfaces.model.bo.ClaimLiabBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractAgentBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractBeneBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractBusiProdBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractMasterBO;
import com.nci.tunan.clm.interfaces.model.bo.ContractProductBO;
import com.nci.tunan.clm.interfaces.model.bo.InsuredListBO;
import com.nci.tunan.clm.interfaces.model.bo.PolicyHolderBO;
import com.nci.udmp.framework.model.BaseVO;
import com.nci.udmp.util.bean.BeanUtils;

/**
 * 
 * @description 保单基本信息抄单表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统-保单基本信息
 * @date 2015-05-15 18:12:26
 */
public class ContractMasterVO extends BaseVO {	
	 /** 
	* @Fields policyPwd :  保单密码
 	*/ 
	private String policyPwd;
	 /** 
	* @Fields mediaType :  保单的发送形式：电子/纸质/并行
 	*/ 
	private BigDecimal mediaType;
	 /** 
	* @Fields applyCode :  投保单号码
 	*/ 
	private String applyCode;
	 /** 
	* @Fields organCode :  保单管理机构
 	*/ 
	private String organCode;
	 /** 
	* @Fields channelType :  销售渠道
 	*/ 
	private String channelType;
	 /** 
	* @Fields saleAgentName :  交叉销售人员姓名
 	*/ 
	private String saleAgentName;
	 /** 
	* @Fields insuredFamily :  家庭险保单标志（1/0）
 	*/ 
	private BigDecimal insuredFamily;
		 /** 
	* @Fields serviceHandlerName :  网点经办人姓名
 	*/ 
	private String serviceHandlerName;
	 /** 
	* @Fields policyId :  保单ID
 	*/ 
	private BigDecimal policyId;
	 /** 
	* @Fields derivation :  保单来源(契约、保全、续保、其他)
 	*/ 
	private String derivation;
		 /** 
	* @Fields basicRemark :  基本信息备注项
 	*/ 
	private String basicRemark;
	 /** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
	 /** 
	* @Fields policyType :  保单类型：个险/团险/汇缴
 	*/ 
	private String policyType;
	 /** 
	* @Fields expiryDate :  保单终止日期
 	*/ 
	private Date expiryDate;
	 /** 
	* @Fields pwdInvalidFlag :  保单密码是否有效
 	*/ 
	private BigDecimal pwdInvalidFlag;
	 /** 
	* @Fields submitChannel :  投保单的递交渠道
 	*/ 
	private BigDecimal submitChannel;
	 /** 
	* @Fields liabilityState :  保单效力状态
 	*/ 
	private BigDecimal liabilityState;
	 /** 
	* @Fields copyDate :  理赔抄单基准日
 	*/ 
	private Date copyDate;
	 /** 
	* @Fields policyCode :  保单号
 	*/ 
	private String policyCode;
	 /** 
	* @Fields saleAgentCode :  交叉销售人员编码
 	*/ 
	private String saleAgentCode;
	 /** 
	* @Fields branchCode :  保单所属分公司
 	*/ 
	private String branchCode;
			 /** 
	* @Fields agencyCode :  中介机构代码
 	*/ 
	private String agencyCode;
	 /** 
	* @Fields moneyCode :  投保币种代码
 	*/ 
	private String moneyCode;
	 /** 
	* @Fields serviceHandlerCode :  网点经办人编码
 	*/ 
	private String serviceHandlerCode;
	 /** 
	* @Fields applyDate :  投保日期
 	*/ 
	private Date applyDate;
	 /** 
	* @Fields initialValidateDate :  续保保单的原始生效日期
 	*/ 
	private Date initialValidateDate;
		 /** 
	* @Fields submissionDate :  交单日期
 	*/ 
	private Date submissionDate;
	 /** 
	* @Fields eServiceFlag :  电子函件服务
 	*/ 
	private BigDecimal eServiceFlag;
	 /** 
	* @Fields serviceBankBranch :  服务网点
 	*/ 
	private String serviceBankBranch;
	 /** 
	* @Fields dcIndi :  是否是通过数据迁移的保单
 	*/ 
	private BigDecimal dcIndi;
		 /** 
	* @Fields endCause :  保单终止原因
 	*/ 
	private String endCause;
	 /** 
	* @Fields issueDate :  保单承保日
 	*/ 
	private Date issueDate;
	 /** 
	* @Fields lapseCause :  失效原因（记录最后一次失效的原因）
 	*/ 
	private String lapseCause;
	 /** 
	* @Fields decisionCode :  核保决定
 	*/ 
	private String decisionCode;
	 /** 
	* @Fields agentOrgId :  代理人管理机构
 	*/ 
	private String agentOrgId;
	 /** 
	* @Fields validdateDate :  生效日期
 	*/ 
	private Date validdateDate;
	 /** 
	* @Fields logId :  主键流水号
 	*/ 
	private BigDecimal logId;
	 /** 
	* @Fields serviceBank :  服务银行
 	*/ 
	private String serviceBank;
	 /** 
	* @Fields langCode :  保单语言
 	*/ 
	private String langCode;
	 /** 
	* @Fields formerId :  转换保单的原保单ID
 	*/ 
	private BigDecimal formerId;
	 /** 
	* @Fields curFlag :  当前抄单标记
 	*/ 
	private BigDecimal curFlag;
	/**
	 * @Fields paidupDate : 缴费终止日期
	 */
	private Date paidupDate;
    /** 
    * @Fields initialType :  当期缴费频率
    */ 
    private BigDecimal initialType;
    /**
     * @Fields claimCase : 是否涉及本赔案
     */
	private String claimCase;
	/**
	 * @Fields customerRole : 出险人角色 
	 */
	private String customerRole;
	
    /** 
    * @Fields businessPrdId : 险种ID
    */ 
    private BigDecimal businessPrdId; //险种ID
    /**
	 * @Fields insureDuty ：保险责任 
	 */
    private String insureDuty;
    /**
     * 险种名称
     */
    private String productAbbrName; 
    /**
     * 宽限期的校验
     */
    private String shortVerifyflag; 
    /**
     * 投保人ID
     */
    private BigDecimal holderId; 
    /**
     * 投保人姓名
     */
    private String holderName; 
    /**
     * 案件标志ID
     */
    private BigDecimal caseFlag;
    /**
     * 案件标志Name
     */
    private String caseFlagName; 
    /**
     * 被保人ID
     */
    private BigDecimal insuredId; 
    /**
     * 被保人姓名
     */
    private String insuredName; 
    /**
     * 是否被选择
     */
    private BigDecimal isChecked; 
    /**
     * 机构名称
     */
    private String organCodeName; 
    /**
     * 赔案号
     */
    private String caseNo; 
    /**
     * 核保ID
     */
    private BigDecimal uwId;
    /**
     * 是否成功标志
     */
    private String successFlag; 
    /**
     * 销售渠道
     */
    private String uwGrade;
    /**
     * 二核类型
     */
    private BigDecimal claimUwType; 
    /**
     * 二核类型名称
     */
    private String claimUwTypeName;
    /**
     * 二核状态
     */
    private String claimUwStatus; 
    /**
     * 二核状态名称
     */
    private String claimUwStatusName; 
    /**
     * 二核处理结论
     */
    private BigDecimal claimUwConculsion;
    /**
     * 实际缴纳保费
     */
    private BigDecimal realityPrem; 
    
    /**
     * @Fields interestMode : 利差领取方式
     */
    private BigDecimal interestMode;
    /**
     * @Fields inputDate : 录单日期
     */
    private Date inputDate;
    /**
     * @Fields inputType : 保单录入方案
     */
    private String inputType;
    /**
     * @Fields subinputType : 出单标识
     */
    private String subinputType;
    /**
     * @Fields saleType : 销售方式
     */
    private String saleType;
    /**
     * @Fields lapseDate : 失效日期
     */
    private Date lapseDate;
    /**
     * @Fields rerinstateDate : 复效日期
     */
    private Date rerinstateDate;
    /**
     * @Fields suspendCause : 保单中止原因
     */
    private String suspendCause;
    /**
     * @Fields suspendDate : 保单中止日期
     */
    private Date suspendDate;
    /**
     * @Fields initialPremDate : 首期缴费日期
     */
    private Date initialPremDate;
    /**
     * @Fields saleComCode : 合作机构代码
     */
    private String saleComCode;
    /**
     *  @Fields totalPremAf : 总保费
     */
    private BigDecimal totalPremAf;
    /**
     * 年缴保费
     */
    private BigDecimal nextPrem;
    /**
     *  @Fields statisticChannel : 网销渠道分类代码
     */
    private String statisticChannel;
    /**
     * 未告知情况
     */
    private String uwNotInformSituation; 
    /**
     * 发起核保次数
     */
    private BigDecimal undwerCount; 
    /**
     * 保额
     */
    private BigDecimal amount; 
    /***
     * 保单挂起状态
     */
    private String holdStatus; 
    /**
     * 保单下所有理赔类型
     */
    private String policyTypeName; 
    /**
     * 二核结论
     */
    private String policyDecision;
    
    /**
     * 投保人风险等级
     */
    private String customerRiskLevel;
    /**
     * 投保人风险等级名字
     */
    private String customerRiskLevelName;
    
    /**
     * 业务员等级
     */
    private String agentLevel;
    /** 
     * @Fields liabilityState :  保单效力状态
     */ 
     private String liabilityStateName;
     /**
      * MDM保单号
      */
     private String policyCodeMDM;
     /**
      * success4Detail
      */
     private String success4Detail;
     
     /** 
 	* @Fields validdateDate :  生效日期字符串
  	*/ 
 	private String validdateDateStr;
 	
 	/**
     * 社保状态
     */
    private BigDecimal sociSecu;
    /**
     * 受益人和被保人集合
     */
    private List<ContractBeneVO> beneAndInsureList;
    /**
     * 受益人和被保人数量
     */
    private String beneAndInsureCount;
   	/** 
   	* @Fields isModuleResult : 被保险人告知信息
   	*/ 
   	private String isModuleResult;
   	
   	/**
     * 销售方式（团体）
     *  
     */
    private String groupSaleType;
    /**
     * 关联保单号
     */
    private String relationPolicyCode;
    /**
     * 投保时间
     */
    private String applyTime;
    /**
     * 保单关联类型
     */
    /**
     * 出险距离复效时间
     */
    private String gapRerinstateDate;
    /**
     * 出险日距离投保日
     * 
     */
    private String claimDayAmount;
    /**
     * 非涉案保单中退还保费显示
     */
    private BigDecimal returnPrem;
    /** 
	* @Fields multiMainriskFlag :  多主险标识
 	*/
	private BigDecimal multiMainriskFlag;
	/** 
	* @Fields isMutualInsured :  是否互保件
 	*/
	private BigDecimal isMutualInsured; 
	/**
     * @Fields meetPovStandardFlag : 是否符合扶贫标准 
     */
    private BigDecimal meetPovStandardFlag;
    /**
     * @Fields banknrtFalg : 银代非实时标识
     */
    private BigDecimal banknrtFalg;
    /** 
	 * @Fields specialAccountFlag : 特殊账户标记（1-中银保信个人养老金账）
	 */ 
	private String specialAccountFlag;
	/** 
	 * @Fields isChannelSelfInsured : 渠道自保件标识
	 */ 
	private BigDecimal isChannelSelfInsured;
	/** 
	 * @Fields isChannelMutualInsured : 渠道互保件标识
	 */ 
	private BigDecimal isChannelMutualInsured;
	
	/**
	 * @Fields taxExtensionSumPrem :税延保费合计
	 */
	private BigDecimal taxExtensionSumPrem;
	
	/**
	 * 共同参保保单类型(1-主参保人保单、2-共同参保人保单)
	 */
	private String JointlyInsuredType;
	
	private BigDecimal selfApplyFlag;
	
	/**
	 * policySequenceNo : 分单编码
	 */
	private String policySequenceNo;
	/**
	 * perFinPvtBankCode : 个金私银保单标识
	 */
	private String perFinPvtBankCode;
	
	/**
	 * policyLockFlag : 保单锁定标识
	 */
	private BigDecimal policyLockFlag;	
	/**
	 * isDfltAcknowledgeDate : 是否默认回执日期
	 */
	private BigDecimal isDfltAcknowledgeDate;
	
	/**
	 * 合并签名标识 mergeSignatureFlag
	 */
	private BigDecimal mergeSignatureFlag;
	
	
	public BigDecimal getPolicyLockFlag() {
		return policyLockFlag;
	}

	public void setPolicyLockFlag(BigDecimal policyLockFlag) {
		this.policyLockFlag = policyLockFlag;
	}
	
	
	public String getPerFinPvtBankCode() {
		return perFinPvtBankCode;
	}

	public void setPerFinPvtBankCode(String perFinPvtBankCode) {
		this.perFinPvtBankCode = perFinPvtBankCode;
	}

	public String getPolicySequenceNo() {
		return policySequenceNo;
	}

	public void setPolicySequenceNo(String policySequenceNo) {
		this.policySequenceNo = policySequenceNo;
	}

	public String getJointlyInsuredType() {
		return JointlyInsuredType;
	}
	
	public void setJointlyInsuredType(String jointlyInsuredType) {
		JointlyInsuredType = jointlyInsuredType;
	}
	
	public BigDecimal getIsChannelSelfInsured() {
		return isChannelSelfInsured;
	}
	
	/**
	 * LAPSE_LOAN_SUSPEND_DATE : 续期失效后贷款中止日期
	 */
	private Date lapseLoanSuspendDate;
	
	/**
	 * TOTAL_POLICY_SEQUENCE_NO : 保单编码
	 */
	private String totalPolicySequenceNo;
	
	
	public BigDecimal getTaxExtensionSumPrem() {
		return taxExtensionSumPrem;
	}

	public void setTaxExtensionSumPrem(BigDecimal taxExtensionSumPrem) {
		this.taxExtensionSumPrem = taxExtensionSumPrem;
	}
	
	public String getTotalPolicySequenceNo() {
		return totalPolicySequenceNo;
	}

	public void setTotalPolicySequenceNo(String totalPolicySequenceNo) {
		this.totalPolicySequenceNo = totalPolicySequenceNo;
	}

	public Date getLapseLoanSuspendDate() {
		return lapseLoanSuspendDate;
	}

	public void setLapseLoanSuspendDate(Date lapseLoanSuspendDate) {
		this.lapseLoanSuspendDate = lapseLoanSuspendDate;
	}

	public void setIsChannelSelfInsured(BigDecimal isChannelSelfInsured) {
		this.isChannelSelfInsured = isChannelSelfInsured;
	}

	public BigDecimal getIsChannelMutualInsured() {
		return isChannelMutualInsured;
	}

	public void setIsChannelMutualInsured(BigDecimal isChannelMutualInsured) {
		this.isChannelMutualInsured = isChannelMutualInsured;
	}

	public String getSpecialAccountFlag() {
		return specialAccountFlag;
 	}

 	public void setSpecialAccountFlag(String specialAccountFlag) {
 		this.specialAccountFlag = specialAccountFlag;
 	}
	public BigDecimal getBanknrtFalg() {
		return banknrtFalg;
	}

	public void setBanknrtFalg(BigDecimal banknrtFalg) {
		this.banknrtFalg = banknrtFalg;
	}

	public BigDecimal getMeetPovStandardFlag() {
		return meetPovStandardFlag;
	}

	public void setMeetPovStandardFlag(BigDecimal meetPovStandardFlag) {
		this.meetPovStandardFlag = meetPovStandardFlag;
	}
	
		 public BigDecimal getIsMutualInsured() {
		return isMutualInsured;
	}

	public void setIsMutualInsured(BigDecimal isMutualInsured) {
		this.isMutualInsured = isMutualInsured;
	}

		public BigDecimal getMultiMainriskFlag() {
		return multiMainriskFlag;
	}

	public void setMultiMainriskFlag(BigDecimal multiMainriskFlag) {
		this.multiMainriskFlag = multiMainriskFlag;
	}
    
    public BigDecimal getReturnPrem() {
		return returnPrem;
	}

	public void setReturnPrem(BigDecimal returnPrem) {
		this.returnPrem = returnPrem;
	}

	public String getClaimDayAmount() {
		return claimDayAmount;
	}

	public void setClaimDayAmount(String claimDayAmount) {
		this.claimDayAmount = claimDayAmount;
	}

	public String getGapRerinstateDate() {
		return gapRerinstateDate;
	}

	public void setGapRerinstateDate(String gapRerinstateDate) {
		this.gapRerinstateDate = gapRerinstateDate;
	}
    private BigDecimal policyRelationType;
    
    public BigDecimal getPolicyRelationType() {
        return policyRelationType;
    }

    public void setPolicyRelationType(BigDecimal policyRelationType) {
        this.policyRelationType = policyRelationType;
    }

    public String getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(String applyTime) {
        this.applyTime = applyTime;
    }

    
    public String getRelationPolicyCode() {
		return relationPolicyCode;
	}

	public void setRelationPolicyCode(String relationPolicyCode) {
		this.relationPolicyCode = relationPolicyCode;
	}

	public BigDecimal getSociSecu() {
		return sociSecu;
	}

	public void setSociSecu(BigDecimal sociSecu) {
		this.sociSecu = sociSecu;
	}

	public String getSuccess4Detail() {
		return success4Detail;
	}

	public void setSuccess4Detail(String success4Detail) {
		this.success4Detail = success4Detail;
	}

	public String getPolicyCodeMDM() {
        return policyCodeMDM;
    }

    public void setPolicyCodeMDM(String policyCodeMDM) {
        this.policyCodeMDM = policyCodeMDM;
    }

    public String getLiabilityStateName() {
        return liabilityStateName;
    }

    public void setLiabilityStateName(String liabilityStateName) {
        this.liabilityStateName = liabilityStateName;
    }
    public String getAgentLevel() {
        return agentLevel;
    }

    public void setAgentLevel(String agentLevel) {
        this.agentLevel = agentLevel;
    }


    /**
     * 是否可撤销二核
     */
    private String isCancelUw;
    
    public String getIsCancelUw() {
		return isCancelUw;
	}

	public void setIsCancelUw(String isCancelUw) {
		this.isCancelUw = isCancelUw;
	}

	public String getCustomerRiskLevelName() {
		return customerRiskLevelName;
	}

	public void setCustomerRiskLevelName(String customerRiskLevelName) {
		this.customerRiskLevelName = customerRiskLevelName;
	}

	public String getCustomerRiskLevel() {
		return customerRiskLevel;
	}

	public void setCustomerRiskLevel(String customerRiskLevel) {
		this.customerRiskLevel = customerRiskLevel;
	}

	public String getPolicyDecision() {
		return policyDecision;
	}

	public void setPolicyDecision(String policyDecision) {
		this.policyDecision = policyDecision;
	}

    
  	 public String getInsureDuty() {
		return insureDuty;
	}

	public void setInsureDuty(String insureDuty) {
		this.insureDuty = insureDuty;
	}

	public String getShortVerifyflag() {
		return shortVerifyflag;
	}

	public void setShortVerifyflag(String shortVerifyflag) {
		this.shortVerifyflag = shortVerifyflag;
	}

	public String getHoldStatus() {
  			return holdStatus;
  		}

  		public void setHoldStatus(String holdStatus) {
  			this.holdStatus = holdStatus;
    }
    
    public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getRealityPrem() {
		return realityPrem;
	}

	public void setRealityPrem(BigDecimal realityPrem) {
		this.realityPrem = realityPrem;
	}

	public BigDecimal getUndwerCount() {

		return undwerCount;
	}

	public void setUndwerCount(BigDecimal undwerCount) {
		this.undwerCount = undwerCount;
	}

	public String getUwNotInformSituation() {
		return uwNotInformSituation;
	}

	public void setUwNotInformSituation(String uwNotInformSituation) {
		this.uwNotInformSituation = uwNotInformSituation;
	}

	public String getStatisticChannel() {
		return statisticChannel;
	}

	public void setStatisticChannel(String statisticChannel) {
		this.statisticChannel = statisticChannel;
	}

	public BigDecimal getNextPrem() {
		return nextPrem;
	}

	public void setNextPrem(BigDecimal nextPrem) {
		this.nextPrem = nextPrem;
	}
    
	public BigDecimal getTotalPremAf() {
		return totalPremAf;
	}

	public void setTotalPremAf(BigDecimal totalPremAf) {
		this.totalPremAf = totalPremAf;
	}
	public String getSaleComCode() {
		return saleComCode;
	}

	public void setSaleComCode(String saleComCode) {
		this.saleComCode = saleComCode;
	}
    
	public Date getInitialPremDate() {
		return initialPremDate;
	}

	public void setInitialPremDate(Date initialPremDate) {
		this.initialPremDate = initialPremDate;
	}

	public BigDecimal getInterestMode() {
		return interestMode;
	}

	public void setInterestMode(BigDecimal interestMode) {
		this.interestMode = interestMode;
	}

	public Date getInputDate() {
		return inputDate;
	}

	public void setInputDate(Date inputDate) {
		this.inputDate = inputDate;
	}

	public String getInputType() {
		return inputType;
	}

	public void setInputType(String inputType) {
		this.inputType = inputType;
	}

	public String getSubinputType() {
		return subinputType;
	}

	public void setSubinputType(String subinputType) {
		this.subinputType = subinputType;
	}

	public String getSaleType() {
		return saleType;
	}

	public void setSaleType(String saleType) {
		this.saleType = saleType;
	}

	public Date getLapseDate() {
		return lapseDate;
	}

	public void setLapseDate(Date lapseDate) {
		this.lapseDate = lapseDate;
	}

	public Date getRerinstateDate() {
		return rerinstateDate;
	}

	public void setRerinstateDate(Date rerinstateDate) {
		this.rerinstateDate = rerinstateDate;
	}

	public String getSuspendCause() {
		return suspendCause;
	}

	public void setSuspendCause(String suspendCause) {
		this.suspendCause = suspendCause;
	}

	public Date getSuspendDate() {
		return suspendDate;
	}

	public void setSuspendDate(Date suspendDate) {
		this.suspendDate = suspendDate;
	}

	public BigDecimal getClaimUwConculsion() {
        return claimUwConculsion;
    }

    public void setClaimUwConculsion(BigDecimal claimUwConculsion) {
        this.claimUwConculsion = claimUwConculsion;
    }

    public BigDecimal getClaimUwType() {
        return claimUwType;
    }

    public void setClaimUwType(BigDecimal claimUwType) {
        this.claimUwType = claimUwType;
    }

    public String getClaimUwTypeName() {
        return claimUwTypeName;
    }

    public void setClaimUwTypeName(String claimUwTypeName) {
        this.claimUwTypeName = claimUwTypeName;
    }

    public String getClaimUwStatus() {
        return claimUwStatus;
    }

    public void setClaimUwStatus(String claimUwStatus) {
        this.claimUwStatus = claimUwStatus;
    }

    public String getClaimUwStatusName() {
        return claimUwStatusName;
    }

    public void setClaimUwStatusName(String claimUwStatusName) {
        this.claimUwStatusName = claimUwStatusName;
    }

    public String getUwGrade() {
        return uwGrade;
    }

    public void setUwGrade(String uwGrade) {
        this.uwGrade = uwGrade;
    }

    public BigDecimal getUwId() {
        return uwId;
    }

    public void setUwId(BigDecimal uwId) {
        this.uwId = uwId;
    }

    public String getSuccessFlag() {
        return successFlag;
    }

    public void setSuccessFlag(String successFlag) {
        this.successFlag = successFlag;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getOrganCodeName() {
        return organCodeName;
    }

    public void setOrganCodeName(String organCodeName) {
        this.organCodeName = organCodeName;
    }

    public BigDecimal getIsChecked() {
        return isChecked;
    }

    public void setIsChecked(BigDecimal isChecked) {
        this.isChecked = isChecked;
    }

    public BigDecimal getInsuredId() {
        return insuredId;
    }

    public void setInsuredId(BigDecimal insuredId) {
        this.insuredId = insuredId;
    }

    public String getInsuredName() {
        return insuredName;
    }

    public void setInsuredName(String insuredName) {
        this.insuredName = insuredName;
    }

    public BigDecimal getCaseFlag() {
        return caseFlag;
    }

    public void setCaseFlag(BigDecimal caseFlag) {
        this.caseFlag = caseFlag;
    }

    public String getCaseFlagName() {
        return caseFlagName;
    }

    public void setCaseFlagName(String caseFlagName) {
        this.caseFlagName = caseFlagName;
    }

    public BigDecimal getHolderId() {
        return holderId;
    }

    public void setHolderId(BigDecimal holderId) {
        this.holderId = holderId;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public BigDecimal getBusinessPrdId() {
        return businessPrdId;
    }

    public void setBusinessPrdId(BigDecimal businessPrdId) {
        this.businessPrdId = businessPrdId;
    }

    public String getProductAbbrName() {
        return productAbbrName;
    }

    public void setProductAbbrName(String productAbbrName) {
        this.productAbbrName = productAbbrName;
    }

    public String getCustomerRole() {
        return customerRole;
    }

    public void setCustomerRole(String customerRole) {
        this.customerRole = customerRole;
    }

    public String getClaimCase() {
        return claimCase;
    }

    public void setClaimCase(String claimCase) {
        this.claimCase = claimCase;
    }

    public BigDecimal getInitialType() {
        return initialType;
    }

    public void setInitialType(BigDecimal initialType) {
        this.initialType = initialType;
    }

    public Date getPaidupDate() {
        return paidupDate;
    }

    public void setPaidupDate(Date paidupDate) {
        this.paidupDate = paidupDate;
    }

    public void setPolicyPwd(String policyPwd) {
		this.policyPwd = policyPwd;
	}
	
	public String getPolicyPwd() {
		return policyPwd;
	}
	 public void setMediaType(BigDecimal mediaType) {
		this.mediaType = mediaType;
	}
	
	public BigDecimal getMediaType() {
		return mediaType;
	}
	 public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}
	
	public String getApplyCode() {
		return applyCode;
	}
	 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
	 public void setChannelType(String channelType) {
		this.channelType = channelType;
	}
	
	public String getChannelType() {
		return channelType;
	}
	 public void setSaleAgentName(String saleAgentName) {
		this.saleAgentName = saleAgentName;
	}
	
	public String getSaleAgentName() {
		return saleAgentName;
	}
	 public void setInsuredFamily(BigDecimal insuredFamily) {
		this.insuredFamily = insuredFamily;
	}
	
	public BigDecimal getInsuredFamily() {
		return insuredFamily;
	}
		 public void setServiceHandlerName(String serviceHandlerName) {
		this.serviceHandlerName = serviceHandlerName;
	}
	
	public String getServiceHandlerName() {
		return serviceHandlerName;
	}
	 public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}
	
	public BigDecimal getPolicyId() {
		return policyId;
	}
	 public void setDerivation(String derivation) {
		this.derivation = derivation;
	}
	
	public String getDerivation() {
		return derivation;
	}

		 public void setBasicRemark(String basicRemark) {
		this.basicRemark = basicRemark;
	}
	
	public String getBasicRemark() {
		return basicRemark;
	}
	 public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	
	public BigDecimal getCaseId() {
		return caseId;
	}
	 public void setPolicyType(String policyType) {
		this.policyType = policyType;
	}
	
	public String getPolicyType() {
		return policyType;
	}
	 public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}
	
	public Date getExpiryDate() {
		return expiryDate;
	}
	 public void setPwdInvalidFlag(BigDecimal pwdInvalidFlag) {
		this.pwdInvalidFlag = pwdInvalidFlag;
	}
	
	public BigDecimal getPwdInvalidFlag() {
		return pwdInvalidFlag;
	}
	
	 public BigDecimal getSubmitChannel() {
        return submitChannel;
    }

    public void setSubmitChannel(BigDecimal submitChannel) {
        this.submitChannel = submitChannel;
    }

    public void setLiabilityState(BigDecimal liabilityState) {
		this.liabilityState = liabilityState;
	}
	
	public BigDecimal getLiabilityState() {
		return liabilityState;
	}
	 public void setCopyDate(Date copyDate) {
		this.copyDate = copyDate;
	}
	
	public Date getCopyDate() {
		return copyDate;
	}
	 public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	
	public String getPolicyCode() {
		return policyCode;
	}
	 public void setSaleAgentCode(String saleAgentCode) {
		this.saleAgentCode = saleAgentCode;
	}
	
	public String getSaleAgentCode() {
		return saleAgentCode;
	}
	 public void setBranchCode(String branchCode) {
		this.branchCode = branchCode;
	}
	
	public String getBranchCode() {
		return branchCode;
	}
			 public void setAgencyCode(String agencyCode) {
		this.agencyCode = agencyCode;
	}
	
	public String getAgencyCode() {
		return agencyCode;
	}
	 public void setMoneyCode(String moneyCode) {
		this.moneyCode = moneyCode;
	}
	
	public String getMoneyCode() {
		return moneyCode;
	}
	 public void setServiceHandlerCode(String serviceHandlerCode) {
		this.serviceHandlerCode = serviceHandlerCode;
	}
	
	public String getServiceHandlerCode() {
		return serviceHandlerCode;
	}
	 public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}
	
	public Date getApplyDate() {
		return applyDate;
	}
	 public void setInitialValidateDate(Date initialValidateDate) {
		this.initialValidateDate = initialValidateDate;
	}
	
	public Date getInitialValidateDate() {
		return initialValidateDate;
	}
		 public void setSubmissionDate(Date submissionDate) {
		this.submissionDate = submissionDate;
	}
	
	public Date getSubmissionDate() {
		return submissionDate;
	}
	 
	 public void setEServiceFlag(BigDecimal eServiceFlag) {
		this.eServiceFlag = eServiceFlag;
	}
	
	public BigDecimal getEServiceFlag() {
		return eServiceFlag;
	}
	 public void setServiceBankBranch(String serviceBankBranch) {
		this.serviceBankBranch = serviceBankBranch;
	}
	
	public String getServiceBankBranch() {
		return serviceBankBranch;
	}
	 public void setDcIndi(BigDecimal dcIndi) {
		this.dcIndi = dcIndi;
	}
	
	public BigDecimal getDcIndi() {
		return dcIndi;
	}
		 public void setEndCause(String endCause) {
		this.endCause = endCause;
	}
	
	public String getEndCause() {
		return endCause;
	}
	 public void setIssueDate(Date issueDate) {
		this.issueDate = issueDate;
	}
	
	public Date getIssueDate() {
		return issueDate;
	}
	 public void setLapseCause(String lapseCause) {
		this.lapseCause = lapseCause;
	}
	
	public String getLapseCause() {
		return lapseCause;
	}
	 public void setDecisionCode(String decisionCode) {
		this.decisionCode = decisionCode;
	}
	
	public String getDecisionCode() {
		return decisionCode;
	}
	 public void setAgentOrgId(String agentOrgId) {
		this.agentOrgId = agentOrgId;
	}
	
	public String getAgentOrgId() {
		return agentOrgId;
	}
	 public void setValiddateDate(Date validdateDate) {
		this.validdateDate = validdateDate;
	}
	
	public Date getValiddateDate() {
		return validdateDate;
	}
	 public void setLogId(BigDecimal logId) {
		this.logId = logId;
	}
	
	public BigDecimal getLogId() {
		return logId;
	}
	 public void setServiceBank(String serviceBank) {
		this.serviceBank = serviceBank;
	}
	
	public String getServiceBank() {
		return serviceBank;
	}
	 public void setLangCode(String langCode) {
		this.langCode = langCode;
	}
	
	public String getLangCode() {
		return langCode;
	}
	 public void setFormerId(BigDecimal formerId) {
		this.formerId = formerId;
	}
	
	public BigDecimal getFormerId() {
		return formerId;
	}
	 public void setCurFlag(BigDecimal curFlag) {
		this.curFlag = curFlag;
	}
	
	public BigDecimal getCurFlag() {
		return curFlag;
	}
	
	
	public String getIsModuleResult() {
		return isModuleResult;
	}

	public void setIsModuleResult(String isModuleResult) {
		this.isModuleResult = isModuleResult;
	}
	
	public String getGroupSaleType() {
		return groupSaleType;
	}

	public void setGroupSaleType(String groupSaleType) {
		this.groupSaleType = groupSaleType;
	}

	@Override
    public String getBizId() {
        return null;
    }
    
    
    /**
	 * 责任组信息
	 */
    private List<ContractProductVO> contractProductVOList = new ArrayList<ContractProductVO>();
    /**
	 * 险种信息
	 */
    private List<ContractBusiProdVO> contractBusiProdVOList = new ArrayList<ContractBusiProdVO>();
    /**
	 * 投保人信息
	 */
    private PolicyHolderVO policyHolderVO = new PolicyHolderVO();
	/**
	 * 被保人信息
	 */
	private List<InsuredListVO>  insuredListVOList = new ArrayList<InsuredListVO>();
	/**
	 * 受益人信息
	 */
	private List<ContractBeneVO> contractBeneVOList = new ArrayList<ContractBeneVO>(); 
	/**
	 *理赔责任给付
	 */
	private List<ClaimLiabVO> claimLiabVOList= new ArrayList<ClaimLiabVO>(); 
	/**
	 * 代理人信息
	 */
	private ContractAgentVO ContractAgentVO;
	
	public ContractMasterBO convertToBO(){
		ContractMasterBO destBo = new  ContractMasterBO();
		
		destBo = BeanUtils.copyProperties(ContractMasterBO.class,this);
		if(claimLiabVOList != null && claimLiabVOList.size() !=0){
			destBo.setClaimLiabBOList(BeanUtils.copyList(ClaimLiabBO.class,claimLiabVOList));
		}
		if(contractProductVOList != null && contractProductVOList.size() !=0){
			destBo.setContractProductBOList(BeanUtils.copyList(ContractProductBO.class,contractProductVOList));
		}
		if(contractBusiProdVOList != null && contractBusiProdVOList.size() != 0){
			destBo.setContractBusiProdBOList(BeanUtils.copyList(ContractBusiProdBO.class,contractBusiProdVOList));
		}
		if(policyHolderVO != null){
			destBo.setPolicyHolderBO(BeanUtils.copyProperties(PolicyHolderBO.class,policyHolderVO));
		}
		if(insuredListVOList !=null && insuredListVOList.size() != 0){
			destBo.setInsuredListBOList(BeanUtils.copyList(InsuredListBO.class, insuredListVOList));
		}
		if(contractBeneVOList != null && insuredListVOList.size() !=0){
			destBo.setContractBeneBOList(BeanUtils.copyList(ContractBeneBO.class,contractBeneVOList));
		}
		if(ContractAgentVO != null){
			destBo.setContractAgentBO(BeanUtils.copyProperties(ContractAgentBO.class, ContractAgentVO));
		}
		
		return destBo;
	}
	
	public PolicyHolderVO getPolicyHolderVO() {
		return policyHolderVO;
	}

	public void setPolicyHolderVO(PolicyHolderVO policyHolderVO) {
		this.policyHolderVO = policyHolderVO;
	}
	

	public List<ClaimLiabVO> getClaimLiabVOList() {
		return claimLiabVOList;
	}

	public void setClaimLiabVOList(List<ClaimLiabVO> claimLiabVOList) {
		this.claimLiabVOList = claimLiabVOList;
	}

	public List<InsuredListVO> getInsuredListVOList() {
		return insuredListVOList;
	}

	public void setInsuredListVOList(List<InsuredListVO> insuredListVOList) {
		this.insuredListVOList = insuredListVOList;
	}

	public List<ContractBeneVO> getContractBeneVOList() {
		return contractBeneVOList;
	}

	public void setContractBeneVOList(List<ContractBeneVO> contractBeneVOList) {
		this.contractBeneVOList = contractBeneVOList;
	}

	public ContractAgentVO getContractAgentVO() {
		return ContractAgentVO;
	}

	public void setContractAgentVO(ContractAgentVO contractAgentVO) {
		ContractAgentVO = contractAgentVO;
	}

	public BigDecimal geteServiceFlag() {
		return eServiceFlag;
	}

	public void seteServiceFlag(BigDecimal eServiceFlag) {
		this.eServiceFlag = eServiceFlag;
	}

	public List<ContractProductVO> getContractProductVOList() {
		return contractProductVOList;
	}

	public void setContractProductVOList(
			List<ContractProductVO> contractProductVOList) {
		this.contractProductVOList = contractProductVOList;
	}

	public List<ContractBusiProdVO> getContractBusiProdVOList() {
		return contractBusiProdVOList;
	}

	public void setContractBusiProdVOList(
			List<ContractBusiProdVO> contractBusiProdVOList) {
		this.contractBusiProdVOList = contractBusiProdVOList;
	}
 

	public String getPolicyTypeName() {
        return policyTypeName;
    }

    public void setPolicyTypeName(String policyTypeName) {
        this.policyTypeName = policyTypeName;
    }

    public String getValiddateDateStr() {
		return validdateDateStr;
	}

	public void setValiddateDateStr(String validdateDateStr) {
		this.validdateDateStr = validdateDateStr;
	}
	
	

	public List<ContractBeneVO> getBeneAndInsureList() {
		return beneAndInsureList;
	}

	public void setBeneAndInsureList(List<ContractBeneVO> beneAndInsureList) {
		this.beneAndInsureList = beneAndInsureList;
	}
	

	public String getBeneAndInsureCount() {
		return beneAndInsureCount;
	}

	public void setBeneAndInsureCount(String beneAndInsureCount) {
		this.beneAndInsureCount = beneAndInsureCount;
	}

	

	public BigDecimal getSelfApplyFlag() {
		return selfApplyFlag;
	}

	public void setSelfApplyFlag(BigDecimal selfApplyFlag) {
		this.selfApplyFlag = selfApplyFlag;
	}

	public BigDecimal getIsDfltAcknowledgeDate() {
		return isDfltAcknowledgeDate;
	}

	public void setIsDfltAcknowledgeDate(BigDecimal isDfltAcknowledgeDate) {
		this.isDfltAcknowledgeDate = isDfltAcknowledgeDate;
	}
	
	public BigDecimal getMergeSignatureFlag() {
		return mergeSignatureFlag;
	}

	public void setMergeSignatureFlag(BigDecimal mergeSignatureFlag) {
		this.mergeSignatureFlag = mergeSignatureFlag;
	}

	@Override
	public String toString() {
		return "ContractMasterVO [policyPwd=" + policyPwd + ", mediaType="
				+ mediaType + ", applyCode=" + applyCode + ", organCode="
				+ organCode + ", channelType=" + channelType
				+ ", saleAgentName=" + saleAgentName + ", insuredFamily="
				+ insuredFamily + ", serviceHandlerName=" + serviceHandlerName
				+ ", policyId=" + policyId + ", derivation=" + derivation
				+ ", basicRemark=" + basicRemark + ", caseId=" + caseId
				+ ", policyType=" + policyType + ", expiryDate=" + expiryDate
				+ ", pwdInvalidFlag=" + pwdInvalidFlag + ", submitChannel="
				+ submitChannel + ", liabilityState=" + liabilityState
				+ ", copyDate=" + copyDate + ", policyCode=" + policyCode
				+ ", saleAgentCode=" + saleAgentCode + ", branchCode="
				+ branchCode + ", agencyCode=" + agencyCode + ", moneyCode="
				+ moneyCode + ", serviceHandlerCode=" + serviceHandlerCode
				+ ", applyDate=" + applyDate + ", initialValidateDate="
				+ initialValidateDate + ", submissionDate=" + submissionDate
				+ ", eServiceFlag=" + eServiceFlag + ", serviceBankBranch="
				+ serviceBankBranch + ", dcIndi=" + dcIndi + ", endCause="
				+ endCause + ", issueDate=" + issueDate + ", lapseCause="
				+ lapseCause + ", decisionCode=" + decisionCode
				+ ", agentOrgId=" + agentOrgId + ", validdateDate="
				+ validdateDate + ", logId=" + logId + ", serviceBank="
				+ serviceBank + ", langCode=" + langCode + ", formerId="
				+ formerId + ", curFlag=" + curFlag + ", paidupDate="
				+ paidupDate + ", initialType=" + initialType + ", claimCase="
				+ claimCase + ", customerRole=" + customerRole
				+ ", businessPrdId=" + businessPrdId + ", insureDuty="
				+ insureDuty + ", productAbbrName=" + productAbbrName
				+ ", shortVerifyflag=" + shortVerifyflag + ", holderId="
				+ holderId + ", holderName=" + holderName + ", caseFlag="
				+ caseFlag + ", caseFlagName=" + caseFlagName + ", insuredId="
				+ insuredId + ", insuredName=" + insuredName + ", isChecked="
				+ isChecked + ", organCodeName=" + organCodeName + ", caseNo="
				+ caseNo + ", uwId=" + uwId + ", successFlag=" + successFlag
				+ ", uwGrade=" + uwGrade + ", claimUwType=" + claimUwType
				+ ", claimUwTypeName=" + claimUwTypeName + ", claimUwStatus="
				+ claimUwStatus + ", claimUwStatusName=" + claimUwStatusName
				+ ", claimUwConculsion=" + claimUwConculsion + ", realityPrem="
				+ realityPrem + ", interestMode=" + interestMode
				+ ", inputDate=" + inputDate + ", inputType=" + inputType
				+ ", subinputType=" + subinputType + ", saleType=" + saleType
				+ ", lapseDate=" + lapseDate + ", rerinstateDate="
				+ rerinstateDate + ", suspendCause=" + suspendCause
				+ ", suspendDate=" + suspendDate + ", initialPremDate="
				+ initialPremDate + ", saleComCode=" + saleComCode
				+ ", totalPremAf=" + totalPremAf + ", nextPrem=" + nextPrem
				+ ", statisticChannel=" + statisticChannel
				+ ", uwNotInformSituation=" + uwNotInformSituation
				+ ", undwerCount=" + undwerCount + ", amount=" + amount
				+ ", holdStatus=" + holdStatus + ", policyTypeName="
				+ policyTypeName + ", policyDecision=" + policyDecision
				+ ", customerRiskLevel=" + customerRiskLevel
				+ ", customerRiskLevelName=" + customerRiskLevelName
				+ ", agentLevel=" + agentLevel + ", liabilityStateName="
				+ liabilityStateName + ", policyCodeMDM=" + policyCodeMDM
				+ ", success4Detail=" + success4Detail + ", validdateDateStr="
				+ validdateDateStr + ", sociSecu=" + sociSecu
				+ ", beneAndInsureList=" + beneAndInsureList
				+ ", beneAndInsureCount=" + beneAndInsureCount
				+ ", isModuleResult=" + isModuleResult + ", groupSaleType="
				+ groupSaleType + ", relationPolicyCode=" + relationPolicyCode
				+ ", applyTime=" + applyTime + ", gapRerinstateDate="
				+ gapRerinstateDate + ", claimDayAmount=" + claimDayAmount
				+ ", returnPrem=" + returnPrem + ", multiMainriskFlag="
				+ multiMainriskFlag + ", isMutualInsured=" + isMutualInsured
				+ ", meetPovStandardFlag=" + meetPovStandardFlag
				+ ", banknrtFalg=" + banknrtFalg + ", specialAccountFlag="
				+ specialAccountFlag + ", isChannelSelfInsured="
				+ isChannelSelfInsured + ", isChannelMutualInsured="
				+ isChannelMutualInsured + ", taxExtensionSumPrem="
				+ taxExtensionSumPrem + ", JointlyInsuredType="
				+ JointlyInsuredType + ", selfApplyFlag=" + selfApplyFlag
				+ ", lapseLoanSuspendDate=" + lapseLoanSuspendDate
				+ ", totalPolicySequenceNo=" + totalPolicySequenceNo
				+ ", policyRelationType=" + policyRelationType
				+ ", isCancelUw=" + isCancelUw + ", contractProductVOList="
				+ contractProductVOList + ", contractBusiProdVOList="
				+ contractBusiProdVOList + ", policyHolderVO=" + policyHolderVO
				+ ", insuredListVOList=" + insuredListVOList
				+ ", contractBeneVOList=" + contractBeneVOList
				+ ", claimLiabVOList=" + claimLiabVOList + ", ContractAgentVO="
				+ ContractAgentVO 
				+ ", perFinPvtBankCode=" + perFinPvtBankCode
				+ ", isDfltAcknowledgeDate=" + isDfltAcknowledgeDate + "]";
	}
}
