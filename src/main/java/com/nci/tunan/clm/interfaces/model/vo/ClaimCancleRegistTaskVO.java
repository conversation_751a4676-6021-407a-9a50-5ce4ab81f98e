package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;

/** 
 * @description ClaimCancleRegistTaskVO对象
 * <AUTHOR> 
 * @date 2022-08-25 14:02:40  
 */
public class ClaimCancleRegistTaskVO extends BaseVO {	
	 /** 
	* @Fields listId :  主键
 	*/ 
	private BigDecimal listId;
	 /** 
	* @Fields isComplete :  是否已完成(T
 	*/ 
	private BigDecimal isComplete;
	 /** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
		
	public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
	public void setIsComplete(BigDecimal isComplete) {
		this.isComplete = isComplete;
	}
	
	public BigDecimal getIsComplete() {
		return isComplete;
	}
	 public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	
	public BigDecimal getCaseId() {
		return caseId;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimCancleRegistTaskVO [" +
				"listId="+listId+","+
				"isComplete="+isComplete+","+
				"caseId="+caseId+"]";
    }
}
