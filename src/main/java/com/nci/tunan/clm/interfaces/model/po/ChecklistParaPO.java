package com.nci.tunan.clm.interfaces.model.po;

import java.math.BigDecimal;
import java.lang.String;
import com.nci.udmp.framework.model.BasePO;

/** 
 * @description ChecklistParaPO对象
 * <AUTHOR> 
 * @date 2015-08-25 09:59:38  
 */
public class ChecklistParaPO extends BasePO {
 	/** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
	// claimType  ---  String  ---  CHAR_2_0_理赔类型;
		// accReason  ---  BigDecimal  ---  NUMBER_1_0_出险原因;
		// cureStatus  ---  String  ---  CHAR_2_0_治疗情况;
		// checklistCode  ---  String  ---  VARCHAR2_10_0_单证编码;
	    // organCode  ---  String  ---  VARCHAR2_8_0_保单管理机构;
	
		public void setClaimType(String claimType){
		setString("claim_type", claimType);
	}
	
	public String getClaimType(){
		return getString("claim_type");
	}
		public void setAccReason(BigDecimal accReason){
		setBigDecimal("acc_reason", accReason);
	}
	
	public BigDecimal getAccReason(){
		return getBigDecimal("acc_reason");
	}
		public void setCureStatus(String cureStatus){
		setString("cure_status", cureStatus);
	}
	
	public String getCureStatus(){
		return getString("cure_status");
	}
		public void setChecklistCode(String checklistCode){
		setString("checklist_code", checklistCode);
	}
	
	public String getChecklistCode(){
		return getString("checklist_code");
	}
	
	public void setOrganCode(String organCode){
		setString("organ_code", organCode);
	}
	
	public String getOrganCode(){
		return getString("organ_code");
	}
	
		
	@Override
    public String toString() {
        return "ChecklistParaPO [" +
				"claimType="+getClaimType()+","+
"accReason="+getAccReason()+","+
"cureStatus="+getCureStatus()+","+
"checklistCode="+getChecklistCode()+"]";
    }	
 }
