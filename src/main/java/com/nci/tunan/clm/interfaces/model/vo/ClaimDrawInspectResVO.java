package com.nci.tunan.clm.interfaces.model.vo;


import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 抽取任务列表
 * <AUTHOR> yang<PERSON><PERSON><EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 
 */
public class ClaimDrawInspectResVO extends BaseVO {
	/** 
	* @Fields bpmTaskId :  工作流Id
	*/ 
	private String bpmTaskId;
	/** 
	* @Fields caseId :  赔案ID
	*/ 
	private String caseId;
	/** 
	* @Fields caseNo :  赔案号
	*/ 
	private String caseNo;
	/** 
	* @Fields taskCode :  任务编号
	*/ 
	private String taskCode;
	/** 
	* @Fields organCode :  机构
	*/ 
	private String organCode;
	/** 
	* @Fields inspectProCode :  检查批次号
 	*/ 
	private String inspectProCode;
	/** 
	* @Fields departmentName :  检查单位
	*/ 
	private String departmentName;
	/** 
	* @Fields inspectType1Desc :  检查大类
	*/ 
	private String inspectType1Desc;
	/** 
	 * @Fields inspectType2Desc :  检查小类
	 */ 
	private String inspectType2Desc;
	/** 
	* @Fields modeDesc :  检查方式
	*/ 
	private String modeDesc;
	/** 
	* @Fields inspectUser :  检查人
	*/ 
	private String inspectUser;
	/** 
	* @Fields inspectProDesc :  检查描述
	*/ 
	private String inspectProDesc;
	
	private BigDecimal taskPointId;
	/** 
	* @Fields riskLibId :  风控库信息表ID
	*/ 
	private String riskLibId;
	/** 
	* @Fields taskId :  抽取任务ID
	*/ 
	private String taskId;
	/** 
	* @Fields inspectUserName : 检查人用户名 
	*/ 
	private String inspectUserName;
	
	public String getInspectUserName() {
		return inspectUserName;
	}

	public void setInspectUserName(String inspectUserName) {
		this.inspectUserName = inspectUserName;
	}

	public String getBpmTaskId() {
		return bpmTaskId;
	}

	public void setBpmTaskId(String bpmTaskId) {
		this.bpmTaskId = bpmTaskId;
	}

	public String getCaseId() {
		return caseId;
	}

	public void setCaseId(String caseId) {
		this.caseId = caseId;
	}

	public String getCaseNo() {
		return caseNo;
	}

	public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getInspectProCode() {
		return inspectProCode;
	}

	public void setInspectProCode(String inspectProCode) {
		this.inspectProCode = inspectProCode;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	public String getInspectType1Desc() {
		return inspectType1Desc;
	}

	public void setInspectType1Desc(String inspectType1Desc) {
		this.inspectType1Desc = inspectType1Desc;
	}

	public String getInspectType2Desc() {
		return inspectType2Desc;
	}

	public void setInspectType2Desc(String inspectType2Desc) {
		this.inspectType2Desc = inspectType2Desc;
	}

	public String getModeDesc() {
		return modeDesc;
	}

	public void setModeDesc(String modeDesc) {
		this.modeDesc = modeDesc;
	}

	public String getInspectUser() {
		return inspectUser;
	}

	public void setInspectUser(String inspectUser) {
		this.inspectUser = inspectUser;
	}

	public String getInspectProDesc() {
		return inspectProDesc;
	}

	public void setInspectProDesc(String inspectProDesc) {
		this.inspectProDesc = inspectProDesc;
	}

	public BigDecimal getTaskPointId() {
		return taskPointId;
	}

	public void setTaskPointId(BigDecimal taskPointId) {
		this.taskPointId = taskPointId;
	}

	public String getRiskLibId() {
		return riskLibId;
	}

	public void setRiskLibId(String riskLibId) {
		this.riskLibId = riskLibId;
	}

	public String getTaskId() {
		return taskId;
	}

	public void setTaskId(String taskId) {
		this.taskId = taskId;
	}

	public String getTaskCode() {
		return taskCode;
	}

	public void setTaskCode(String taskCode) {
		this.taskCode = taskCode;
	}

	@Override
    public String getBizId() {
        return null;
    }

	@Override
	public String toString() {
		return "ClaimDrawInspectResVO [bpmTaskId=" + bpmTaskId + ", caseId="
				+ caseId + ", caseNo=" + caseNo + ", taskCode=" + taskCode
				+ ", organCode=" + organCode + ", inspectProCode="
				+ inspectProCode + ", departmentName=" + departmentName
				+ ", inspectType1Desc=" + inspectType1Desc
				+ ", inspectType2Desc=" + inspectType2Desc + ", modeDesc="
				+ modeDesc + ", inspectUser=" + inspectUser
				+ ", inspectProDesc=" + inspectProDesc + ", taskPointId="
				+ taskPointId + ", riskLibId=" + riskLibId + ", taskId="
				+ taskId + "]";
	}


}
