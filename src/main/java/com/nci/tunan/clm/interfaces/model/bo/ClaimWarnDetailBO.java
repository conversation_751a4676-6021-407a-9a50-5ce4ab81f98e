package com.nci.tunan.clm.interfaces.model.bo;

import com.nci.udmp.framework.model.BaseBO;

import java.math.BigDecimal;

/** 
 * @description ClaimWarnDetailVO对象
 * <AUTHOR> 
 * @date 2023-02-07 14:20:47  
 */
public class ClaimWarnDetailBO extends BaseBO {	
			 /** 
	* @Fields caseNo :  赔案号
 	*/ 
	private String caseNo;
	 /** 
	* @Fields actualValue :  实际指标
 	*/ 
	private BigDecimal actualValue;
	 /** 
	* @Fields organCode :  赔案签收机构
 	*/ 
	private String organCode;
		 /** 
	* @Fields noticeCode :  短信项目
 	*/ 
	private String noticeCode;
	 /** 
	* @Fields mobTel :  手机号
 	*/ 
	private String mobTel;
		 /** 
	* @Fields warnId :  风控预警信息表ID，T
 	*/ 
	private BigDecimal warnId;
	 /** 
	* @Fields listId :  主键ID
 	*/ 
	private BigDecimal listId;
				
			 public void setCaseNo(String caseNo) {
		this.caseNo = caseNo;
	}
	
	public String getCaseNo() {
		return caseNo;
	}
	 public void setActualValue(BigDecimal actualValue) {
		this.actualValue = actualValue;
	}
	
	public BigDecimal getActualValue() {
		return actualValue;
	}
	 public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	
	public String getOrganCode() {
		return organCode;
	}
		 public void setNoticeCode(String noticeCode) {
		this.noticeCode = noticeCode;
	}
	
	public String getNoticeCode() {
		return noticeCode;
	}
	 public void setMobTel(String mobTel) {
		this.mobTel = mobTel;
	}
	
	public String getMobTel() {
		return mobTel;
	}
		 public void setWarnId(BigDecimal warnId) {
		this.warnId = warnId;
	}
	
	public BigDecimal getWarnId() {
		return warnId;
	}
	 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
				
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimWarnDetailBO [" +
				"caseNo="+caseNo+","+
"actualValue="+actualValue+","+
"organCode="+organCode+","+
"noticeCode="+noticeCode+","+
"mobTel="+mobTel+","+
"warnId="+warnId+","+
"listId="+listId+"]";
    }
}
