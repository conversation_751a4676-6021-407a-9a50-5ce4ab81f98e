package com.nci.tunan.clm.interfaces.model.vo;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 初审审核结论码表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class AuditConclusionVO extends BaseVO {	
	 /** 
	* @Fields conclusionCode :  结论编码
 	*/ 
	private BigDecimal conclusionCode;
	 /** 
	* @Fields conclusionDesc :  结论描述
 	*/ 
	private String conclusionDesc;
	
	/** 
	 * @Fields otherReason : 其他原因
	*/ 
	private String otherReason;
	
	
		
	 public String getOtherReason() {
		return otherReason;
	}

	public void setOtherReason(String otherReason) {
		this.otherReason = otherReason;
	}

	public void setConclusionCode(BigDecimal conclusionCode) {
		this.conclusionCode = conclusionCode;
	}
	
	public BigDecimal getConclusionCode() {
		return conclusionCode;
	}
	 public void setConclusionDesc(String conclusionDesc) {
		this.conclusionDesc = conclusionDesc;
	}
	
	public String getConclusionDesc() {
		return conclusionDesc;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "AuditConclusionVO [" +
				"conclusionCode="+conclusionCode+","+
"conclusionDesc="+conclusionDesc+"]";
    }
}
