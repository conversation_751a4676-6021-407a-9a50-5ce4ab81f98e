package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;

/** 
 * @description ClaimIntelAuditOpinionVO对象
 * <AUTHOR> 
 * @date 2022-07-25 10:12:50  
 */
public class ClaimIntelAuditOpinionVO extends BaseVO {	
				 /** 
	* @Fields listId :  主键
 	*/ 
	private BigDecimal listId;
				 /** 
	* @Fields auditOpinion :  智能审核意见
 	*/ 
	private String auditOpinion;
	 /** 
	* @Fields caseId :  赔案ID
 	*/ 
	private BigDecimal caseId;
		
				 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
				 public void setAuditOpinion(String auditOpinion) {
		this.auditOpinion = auditOpinion;
	}
	
	public String getAuditOpinion() {
		return auditOpinion;
	}
	 public void setCaseId(BigDecimal caseId) {
		this.caseId = caseId;
	}
	
	public BigDecimal getCaseId() {
		return caseId;
	}
		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimIntelAuditOpinionVO [" +
				"listId="+listId+","+
"auditOpinion="+auditOpinion+","+
"caseId="+caseId+"]";
    }
}
