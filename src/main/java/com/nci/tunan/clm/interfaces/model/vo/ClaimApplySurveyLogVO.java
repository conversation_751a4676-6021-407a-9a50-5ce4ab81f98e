package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;
import java.util.Date;

/** 
 * @description ClaimApplySurveyLogVO对象
 * <AUTHOR> 
 * @date 2022-07-09 10:47:47  
 */
public class ClaimApplySurveyLogVO extends BaseVO {	
	 /** 
	* @Fields intervalNum :  出险时间距离生效时间
 	*/ 
	private BigDecimal intervalNum;
	 /** 
	* @Fields accReason :  出险原因
 	*/ 
	private BigDecimal accReason;
	 /** 
	* @Fields claimType :  理赔类型
 	*/ 
	private String claimType;
		 /** 
	* @Fields submitUser :  提交人
 	*/ 
	private BigDecimal submitUser;
	 /** 
	* @Fields operationType :  操作类型（C-新增，D-删除，U—更新）
 	*/ 
	private String operationType;
		 /** 
	* @Fields amount :  保额（万元）
 	*/ 
	private BigDecimal amount;
	 /** 
	* @Fields intervalType :  出险时间距离生效时间类型
 	*/ 
	private String intervalType;
			 /** 
	* @Fields amountSymbol :  保额计算符号
 	*/ 
	private String amountSymbol;
	 /** 
	* @Fields listId :  主键
 	*/ 
	private BigDecimal listId;
			 /** 
	* @Fields submitDate :  提交日期
 	*/ 
	private Date submitDate;
	 /** 
	* @Fields intervalSymbol :  出险时间距离生效时间计算符号
 	*/ 
	private String intervalSymbol;
	
	/** 
	* @Fields Date :  查询起期
 	*/ 
	private Date startTime;

	/** 
	* @Fields Date :  查询止期
 	*/ 
	private Date endTime;
	/** 
	* @Fields accReason :  出险原因
 	*/ 
	private String accReasonStr;
	 
	/** 
	* @Fields claimType :  理赔类型
 	*/ 
	private String claimTypeStr;
	 /** 
	* @Fields submitUser :  提交人
	*/ 
	private String submitUserStr;
	 /** 
	* @Fields operationType :  操作类型（C-新增，D-删除，U—更新）
	*/ 
	private String operationTypeStr;
	
		
	 public void setIntervalNum(BigDecimal intervalNum) {
		this.intervalNum = intervalNum;
	}
	
	public BigDecimal getIntervalNum() {
		return intervalNum;
	}
	 public void setAccReason(BigDecimal accReason) {
		this.accReason = accReason;
	}
	
	public BigDecimal getAccReason() {
		return accReason;
	}
	 public void setClaimType(String claimType) {
		this.claimType = claimType;
	}
	
	public String getClaimType() {
		return claimType;
	}
		 public void setSubmitUser(BigDecimal submitUser) {
		this.submitUser = submitUser;
	}
	
	public BigDecimal getSubmitUser() {
		return submitUser;
	}
	 public void setOperationType(String operationType) {
		this.operationType = operationType;
	}
	
	public String getOperationType() {
		return operationType;
	}
		 public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}
	
	public BigDecimal getAmount() {
		return amount;
	}
	 public void setIntervalType(String intervalType) {
		this.intervalType = intervalType;
	}
	
	public String getIntervalType() {
		return intervalType;
	}
			 public void setAmountSymbol(String amountSymbol) {
		this.amountSymbol = amountSymbol;
	}
	
	public String getAmountSymbol() {
		return amountSymbol;
	}
	 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
			 public void setSubmitDate(Date submitDate) {
		this.submitDate = submitDate;
	}
	
	public Date getSubmitDate() {
		return submitDate;
	}
	 public void setIntervalSymbol(String intervalSymbol) {
		this.intervalSymbol = intervalSymbol;
	}
	
	public String getIntervalSymbol() {
		return intervalSymbol;
	}
	
	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	
	public String getAccReasonStr() {
		return accReasonStr;
	}

	public void setAccReasonStr(String accReasonStr) {
		this.accReasonStr = accReasonStr;
	}

	public String getClaimTypeStr() {
		return claimTypeStr;
	}

	public void setClaimTypeStr(String claimTypeStr) {
		this.claimTypeStr = claimTypeStr;
	}

	public String getSubmitUserStr() {
		return submitUserStr;
	}

	public void setSubmitUserStr(String submitUserStr) {
		this.submitUserStr = submitUserStr;
	}

	public String getOperationTypeStr() {
		return operationTypeStr;
	}

	public void setOperationTypeStr(String operationTypeStr) {
		this.operationTypeStr = operationTypeStr;
	}

		
	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "ClaimApplySurveyLogVO [" +
				"intervalNum="+intervalNum+","+
"accReason="+accReason+","+
"claimType="+claimType+","+
"submitUser="+submitUser+","+
"operationType="+operationType+","+
"amount="+amount+","+
"intervalType="+intervalType+","+
"amountSymbol="+amountSymbol+","+
"listId="+listId+","+
"submitDate="+submitDate+","+
"intervalSymbol="+intervalSymbol+"]";
    }
}
