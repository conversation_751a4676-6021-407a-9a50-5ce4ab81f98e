package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;

/**
 * 
 * @description 理赔片区用户表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class ClaimAreaPersonVO extends BaseVO {
	/**
	 * 机构代码
	 */
	private String organCode;
	/**
	 * 序列号
	 */
	private BigDecimal areaPersonId;
	/**
	 * 片区编号
	 */
	private String areaCode;
	/**
	 * 片区Id
	 */
	private BigDecimal areaId;
	/**
	 * 用户Id
	 */
	private BigDecimal userId;
	/**
	 * centerCode
	 */
	private String centerCode;
	
	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public BigDecimal getAreaPersonId() {
		return areaPersonId;
	}

	public void setAreaPersonId(BigDecimal areaPersonId) {
		this.areaPersonId = areaPersonId;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public BigDecimal getAreaId() {
		return areaId;
	}

	public void setAreaId(BigDecimal areaId) {
		this.areaId = areaId;
	}

	public BigDecimal getUserId() {
		return userId;
	}

	public void setUserId(BigDecimal userId) {
		this.userId = userId;
	}

	public String getCenterCode() {
		return centerCode;
	}

	public void setCenterCode(String centerCode) {
		this.centerCode = centerCode;
	}

	@Override
	public String getBizId() {
		return null;
	}
}
	

