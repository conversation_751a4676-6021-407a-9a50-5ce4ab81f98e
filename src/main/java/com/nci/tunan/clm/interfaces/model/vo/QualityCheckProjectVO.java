package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;
/**
 * 
 * @description 质检项目
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class QualityCheckProjectVO extends BaseVO{	
	/** 
	* @Fields taskId :  任务Id
	*/ 
	private BigDecimal taskId;
	 /** 
	* @Fields validFlag :  有效标识
	*/ 
	private BigDecimal validFlag;
				 /** 
	* @Fields gistDesc :  质检要点描述
	*/ 
	private String gistDesc;
	 /** 
	* @Fields gistId :  质检要点ID
	*/ 
	private BigDecimal gistId;
	/** 
	* @Fields itemId :  质检项目ID
	*/ 
	private BigDecimal itemId;
	/**
	 * 质检要点备注
	 */
	private String remark;//
	/**
	 * 质检要点结果
	 */
	private BigDecimal checkResult;//
	/***
	 * 判断页面上质检要点信息是否有修改 如果有修改置为1
	 */
	private BigDecimal isUpdate;//
	/**
	 * 修正质检要点结果
	 */
	private BigDecimal reviseCheckResult;//
	/***
	 * 修正质检要点备注
	 */
	private String reviseRemark;//
	
	public BigDecimal getTaskId() {
		return taskId;
	}

	public void setTaskId(BigDecimal taskId) {
		this.taskId = taskId;
	}
	public BigDecimal getIsUpdate() {
		return isUpdate;
	}

	public void setIsUpdate(BigDecimal isUpdate) {
		this.isUpdate = isUpdate;
	}

	public BigDecimal getCheckResult() {
		return checkResult;
	}
	public void setCheckResult(BigDecimal checkResult) {
		this.checkResult = checkResult;
	}
	
	public BigDecimal getReviseCheckResult() {
		return reviseCheckResult;
	}
	public void setReviseCheckResult(BigDecimal reviseCheckResult) {
		this.reviseCheckResult = reviseCheckResult;
	}
	
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getReviseRemark() {
		return reviseRemark;
	}
	public void setReviseRemark(String reviseRemark) {
		this.reviseRemark = reviseRemark;
	}

	
	public void setValidFlag(BigDecimal validFlag) {
		this.validFlag = validFlag;
	}
	
	public BigDecimal getValidFlag() {
		return validFlag;
	}
	public void setGistDesc(String gistDesc) {
		this.gistDesc = gistDesc;
	}
	
	public String getGistDesc() {
		return gistDesc;
	}
	 public void setGistId(BigDecimal gistId) {
		this.gistId = gistId;
	}
	
	public BigDecimal getGistId() {
		return gistId;
	}
	public void setItemId(BigDecimal itemId) {
		this.itemId = itemId;
	}
	
	public BigDecimal getItemId() {
		return itemId;
	}
			
	@Override
   public String getBizId() {
       return null;
   }
   
  
}
