package com.nci.tunan.clm.interfaces.peripheral.exports.r06701001911.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class OutputData implements Serializable {

    /**
     * @Fields serialVersionUID :
     */
    private final static  long serialVersionUID = 1L;

    private UwPolicylist uwPolicylist;
    private SecodUwPolicyList secodUwPolicyList;
  /*  private UwPolicyDetailList uwPolicyDetailList;*/

    @XmlElement(name = "UwPolicylist")
    public UwPolicylist getUwPolicylist() {
        return uwPolicylist;
    }

    public void setUwPolicylist(UwPolicylist uwPolicylist) {
        this.uwPolicylist = uwPolicylist;
    }

    @XmlElement(name = "SecodUwPolicyList")
    public SecodUwPolicyList getSecodUwPolicyList() {
        return secodUwPolicyList;
    }

    public void setSecodUwPolicyList(SecodUwPolicyList secodUwPolicyList) {
        this.secodUwPolicyList = secodUwPolicyList;
    }

    /*@XmlElement(name = "UwPolicyDetailList")
    public UwPolicyDetailList getUwPolicyDetailList() {
        return uwPolicyDetailList;
    }

    public void setUwPolicyDetailList(UwPolicyDetailList uwPolicyDetailList) {
        this.uwPolicyDetailList = uwPolicyDetailList;
    }*/

}
