package com.nci.tunan.clm.interfaces.model.vo;

import com.nci.udmp.framework.model.BaseVO;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @description 理赔单证表
 * <AUTHOR> <EMAIL> 
 * @.belongToModule CLM-理赔系统
 * @date 2015-05-15 18:12:26
 */
public class ClaimChecklistVO extends BaseVO {
    /**
     * @Fields supCancelReason : 撤销补扫原因
     */
    private String supCancelReason;
    /**
     * @Fields supFalg : 补扫标记
     */
    private BigDecimal supFalg;
    /**
     * @Fields checklistId : 顺序号
     */
    private BigDecimal checklistId;
    /**
     * @Fields caseId : 赔案ID
     */
    private BigDecimal caseId;
    /**
     * @Fields caseNo : 赔案号
     */
    private String caseNo;
    /**
     * @Fields supScanReason : 补扫原因
     */
    private String supScanReason;
    /**
     * @Fields checklistCode : 单证编码
     */
    private String checklistCode;
    /**
     * @Fields lackReason : 不齐全原因
     */
    private String lackReason;
    /**
     * @Fields isManual : 是否手工添加
     */
    private BigDecimal isManual;
    /**
     * @Fields isChecked : 是否退还原件
     */
    private BigDecimal isChecked;
    /**
     * @Fields docSubmMode : 单证提交形式
     */
    private BigDecimal docSubmMode;
    /**
     * @Fields checklistOption : 单证检查结论
     */
    private BigDecimal checklistOption;
    /**
     * @Fields checklistStatus : 单证状态（待扫描、已扫描、待重扫）
     */
    private BigDecimal checklistStatus;
    /**
     * @Fields listUnit : 单证页数
     */
    private BigDecimal listUnit;
    /**
     * checklistname
     */
    private String checklistname;
    /**
     * checklistItem
     */
    private String checklistItem;
    /**
     * 类型
     */
    private String typeDesc;
    /**
     * docType
     */
    private BigDecimal docType;
    /**
     * checked
     */
    private BigDecimal checked;
    /**
     * @Fields userId : 用户id
     */
    private BigDecimal userId;
    /**
     * @Fields organCode : 机构代码
     */
    private String organCode;
    /**
     * 事件号
     */
    private String accidentNo;
    
	 /** 
	* @Fields remark :  备注
	*/ 
	private String remark;
		 /** 
	* @Fields isChecklistMemo :  是否为单证问题件
	*/ 
	private BigDecimal isChecklistMemo;
	/**
	 * problemNo
	 */
	private String problemNo; 
	
	/**
	 * 操作员
	 */
	private String operator;
	/**
	 * isSucFlag
	 */
	private String isSucFlag ;
	/**
	 * 拒付通知书寄送日期
	 */
	private Date rejectDocSendTime;
    /**
     * 是否拒付通知书类单证标识
     */
	private String isRejectPayDoc;
	/**
	 * 是否实名查验单证标识
	 */
	private String isRealNameDoc;
    /**
     * 从那个页面进入影像扫描 
     */
	private String busiStatus;
	
	public String getBusiStatus() {
		return busiStatus;
	}

	public void setBusiStatus(String busiStatus) {
		this.busiStatus = busiStatus;
	}

	public String getIsRealNameDoc() {
		return isRealNameDoc;
	}

	public void setIsRealNameDoc(String isRealNameDoc) {
		this.isRealNameDoc = isRealNameDoc;
	}

	public String getIsRejectPayDoc() {
		return isRejectPayDoc;
	}

	public void setIsRejectPayDoc(String isRejectPayDoc) {
		this.isRejectPayDoc = isRejectPayDoc;
	}

	public Date getRejectDocSendTime() {
		return rejectDocSendTime;
	}

	public void setRejectDocSendTime(Date rejectDocSendTime) {
		this.rejectDocSendTime = rejectDocSendTime;
	}

	public String getIsSucFlag() {
		return isSucFlag;
	}

	public void setIsSucFlag(String isSucFlag) {
		this.isSucFlag = isSucFlag;
	}

	public String getProblemNo() {
		return problemNo;
	}

	public void setProblemNo(String problemNo) {
		this.problemNo = problemNo;
	}

	public String getRemark() {
			return remark;
		}

		public void setRemark(String remark) {
			this.remark = remark;
		}

		public BigDecimal getIsChecklistMemo() {
			return isChecklistMemo;
		}

		public void setIsChecklistMemo(BigDecimal isChecklistMemo) {
			this.isChecklistMemo = isChecklistMemo;
		}

	public String getAccidentNo() {
        return accidentNo;
    }

    public void setAccidentNo(String accidentNo) {
        this.accidentNo = accidentNo;
    }

    public BigDecimal getUserId() {
        return userId;
    }

    public void setUserId(BigDecimal userId) {
        this.userId = userId;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getChecklistname() {
        return checklistname;
    }

    public void setChecklistname(String checklistname) {
        this.checklistname = checklistname;
    }

    public String getChecklistItem() {
        return checklistItem;
    }

    public void setChecklistItem(String checklistItem) {
        this.checklistItem = checklistItem;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }

    public BigDecimal getDocType() {
        return docType;
    }

    public void setDocType(BigDecimal docType) {
        this.docType = docType;
    }

    public BigDecimal getChecked() {
        return checked;
    }

    public void setChecked(BigDecimal checked) {
        this.checked = checked;
    }

    public void setSupCancelReason(String supCancelReason) {
        this.supCancelReason = supCancelReason;
    }

    public String getSupCancelReason() {
        return supCancelReason;
    }

    public void setSupFalg(BigDecimal supFalg) {
        this.supFalg = supFalg;
    }

    public BigDecimal getSupFalg() {
        return supFalg;
    }

    public void setChecklistId(BigDecimal checklistId) {
        this.checklistId = checklistId;
    }

    public BigDecimal getChecklistId() {
        return checklistId;
    }

    public void setCaseId(BigDecimal caseId) {
        this.caseId = caseId;
    }

    public BigDecimal getCaseId() {
        return caseId;
    }

    public void setSupScanReason(String supScanReason) {
        this.supScanReason = supScanReason;
    }

    public String getSupScanReason() {
        return supScanReason;
    }

    public void setChecklistCode(String checklistCode) {
        this.checklistCode = checklistCode;
    }

    public String getChecklistCode() {
        return checklistCode;
    }

    public void setLackReason(String lackReason) {
        this.lackReason = lackReason;
    }

    public String getLackReason() {
        return lackReason;
    }

    public void setIsManual(BigDecimal isManual) {
        this.isManual = isManual;
    }

    public BigDecimal getIsManual() {
        return isManual;
    }

    public void setIsChecked(BigDecimal isChecked) {
        this.isChecked = isChecked;
    }

    public BigDecimal getIsChecked() {
        return isChecked;
    }

    public void setDocSubmMode(BigDecimal docSubmMode) {
        this.docSubmMode = docSubmMode;
    }

    public BigDecimal getDocSubmMode() {
        return docSubmMode;
    }

    public void setChecklistOption(BigDecimal checklistOption) {
        this.checklistOption = checklistOption;
    }

    public BigDecimal getChecklistOption() {
        return checklistOption;
    }

    public void setChecklistStatus(BigDecimal checklistStatus) {
        this.checklistStatus = checklistStatus;
    }

    public BigDecimal getChecklistStatus() {
        return checklistStatus;
    }

    public void setListUnit(BigDecimal listUnit) {
        this.listUnit = listUnit;
    }

    public BigDecimal getListUnit() {
        return listUnit;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public void setCaseNo(String caseNo) {
        this.caseNo = caseNo;
    }

    public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	@Override
    public String getBizId() {
        return null;
    }

    @Override
    public String toString() {
        return "ClaimChecklistVO [" + "supCancelReason=" + supCancelReason
                + "," + "supFalg=" + supFalg + "," + "checklistId="
                + checklistId + "," + "caseId=" + caseId + ","
                + "supScanReason=" + supScanReason + "," + "checklistCode="
                + checklistCode + "," + "lackReason=" + lackReason + ","
                + "isManual=" + isManual + "," + "isChecked=" + isChecked + ","
                + "docSubmMode=" + docSubmMode + "," + "checklistOption="
                + checklistOption + "," + "checklistStatus=" + checklistStatus + ","
                + "isRealNameDoc=" + "," + isRealNameDoc
                + "," + "listUnit=" + listUnit + "]";
    }
}
