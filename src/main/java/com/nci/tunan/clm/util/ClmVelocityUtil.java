package com.nci.tunan.clm.util;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Properties;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.apache.velocity.exception.ParseErrorException;
import org.apache.velocity.exception.ResourceNotFoundException;

import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.util.Constants;
/** 理赔模版工具类
 * @description 
 * @<NAME_EMAIL> 
 * @.belongToModule CLM-理赔系统/匹配理算
 * @date 2015年12月18日 上午9:41:59 
*/
public class ClmVelocityUtil {

    /**
     * @Fields context : 模板上下文
     */
    private VelocityContext context;

    /**
     * @Fields template : 模板操作类
     */
    private Template template;

    /**
     * @Fields templateString : 模板
     */
    private String templateString;

    /**
     * @description 初始化获取上下文信息
     * @version 1.0
     * @title 初始化获取上下文信息
     * @<NAME_EMAIL>
     * @param propertiesPath 配置路径 String
     * @throws Exception Exception 初始化异常
     */
    public void init(String propertiesPath) throws Exception {
        //初始化获取上下文信息
        if (propertiesPath != null && propertiesPath.trim().length() > 0) {
            Velocity.init(propertiesPath);
        } else {
            Velocity.init();
        }
        context = new VelocityContext();
    }

    /**
     * @description 初始化上下文信息
     * @version 1.0
     * @title 初始化上下文信息
     * @<NAME_EMAIL>
     * @param properties 配置文件
     * @throws Exception Exception 初始化异常
     */
    public void init(Properties properties) throws Exception {
        //初始化获取上下文信息
        Velocity.init(properties);
        context = new VelocityContext();
    }

    /**
     * @description 初始化上下文信息
     * @version 1.0
     * @title 初始化上下文信息
     * @<NAME_EMAIL>
     * @throws Exception Exception 初始化异常
     */
    public void init() throws Exception {
        Properties p = new Properties();
        p.setProperty("resource.loader", "class");
        p.setProperty("class.resource.loader.class",
                "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        // 设置velocity的编码
        p.setProperty(Velocity.ENCODING_DEFAULT, "GBK");
        p.setProperty(Velocity.INPUT_ENCODING, "GBK");
        p.setProperty(Velocity.OUTPUT_ENCODING, "GBK");

        Velocity.init(p);
        context = new VelocityContext();
    }

    /**
     * @description 将公有信息放入上下文
     * @version 1.0
     * @title 将公有信息放入上下文
     * @<NAME_EMAIL>
     * @param key String
     * @param value Object
     */
    public void put(String key, Object value) {
    	//将公有信息放入上下文
        context.put(key, value);
    }

    /**
     * @description 设置模板
     * @version 1.0
     * @title 设置模板
     * @<NAME_EMAIL>
     * @param templateFile 模版文件
     * @throws Exception Exception
     */
    public void setTemplate(String templateFile) throws Exception {
        try {
            //设置模板
            template = Velocity.getTemplate(templateFile);
        } catch (ResourceNotFoundException rnfe) {
            rnfe.printStackTrace();
            throw new Exception(" 错误： 找不到模板 " + templateFile);
        } catch (ParseErrorException pee) {
            throw new Exception(" 解悉模板错误 " + templateFile + ":" + pee);
        } catch (Exception e) {
            throw new Exception(e.toString());
        }

    }

    /**
     * @description 设置模版
     * @version 1.0
     * @title 设置模版
     * <AUTHOR>
     * @param templateFile 模版文件
     * @param characterSet 字符集
     * @throws Exception Exception
     */
    public void setTemplate(String templateFile, String characterSet) throws Exception {
        try {
            //设置模版
            template = Velocity.getTemplate(templateFile, characterSet);
        } catch (ResourceNotFoundException rnfe) {
            rnfe.printStackTrace();
            throw new Exception(" 错误： 找不到模板 " + templateFile);
        } catch (ParseErrorException pee) {
            throw new Exception(" 解悉模板错误 " + templateFile + ":" + pee);
        } catch (Exception e) {
            throw new Exception(e.toString());
        }

    }

    /**
     * @description 将输入流转换成字符串
     * @version 1.0
     * @param is InputStream 输入流
     * @param characterSet String 字符集
     * @return String 输入流转换的字符串
     * @throws IOException IOException
     */
    public static String getStringFromStream(InputStream is, String characterSet) throws IOException {
        if (null == is) {
            return null;
        }
        try {
            InputStreamReader reader = new InputStreamReader(is, characterSet);
            char[] buffer = new char[1024];
            StringWriter writer = new StringWriter();
            int bytesRead;
            //将输入流转换成字符串
            while ((bytesRead = reader.read(buffer)) != -1) {
                writer.write(buffer, 0, bytesRead);
            }

            return writer.toString();
        } finally {
            if (null != is) {
                is.close();
            }
        }
    }

    /**
     * @description 设置模板
     * @version 1.0
     * @title 设置模板
     * @<NAME_EMAIL>
     * @param templateFile 模板文件
     * @param characterSet 字符集
     */
    public void setTemplateString(String templateFile, String characterSet) {

        try {
            //设置模板
            InputStream is =
            //@invalid this.getClass().getClassLoader().getResourceAsStream(templateFile);
            Thread.currentThread().getContextClassLoader().getResourceAsStream(templateFile);
            templateString = getStringFromStream(is, characterSet);
        } catch (IOException e) {

            e.printStackTrace();
            throw new BizException("系统异常");
        }
    }

    /**
     * @description 转换为文本
     * @throws Exception Exception
     * @return String
     */
    public String toText() throws Exception {
        //转换为文本
        StringWriter sw = new StringWriter();
        try {
            template.merge(context, sw);
        } catch (Exception e) {
            throw new Exception(e.toString());
        }
        return sw.toString();
    }

    /**
     * @description 转换为文本
     * @version 1.0
     * @title 转换为文本
     * <AUTHOR>
     * @return 文本
     * @throws Exception 转换异常
     */
    public String toTextWithTemplateString() throws Exception {
        //转换为文本
        StringWriter sw = new StringWriter();
        try {
            Velocity.evaluate(context, sw, "mystring", templateString);
        } catch (Exception e) {
            throw new Exception(e.toString());
        }
        return sw.toString();
    }

    /**
     * @description 转换为文件
     * @param fileName 文件名
     * @param path 路径
     * @throws Exception Exception
     */
    public void toFile(String path, String fileName) throws Exception {
        try {
            //1. 替换模板内容
            StringWriter sw = new StringWriter();
            template.merge(context, sw);

            //2. 创建目录路径
            File dir = new File(path);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            //3. 创建文件
            File file = new File(dir.getPath(), fileName);
            if (!file.exists()) {
                file.createNewFile();
            }

            PrintWriter out = new PrintWriter(new BufferedWriter(new FileWriter(file)));
            out.println(sw.toString());
            out.close();
            
//@invalid            PrintWriter filewriter = new PrintWriter(new
//@invalid            FileOutputStream(fileName), true);
//@invalid            filewriter.println(sw.toString()); filewriter.close();
            
        } catch (Exception e) {
            throw new Exception(e.toString());
        }

    }

    /**
     * @description 转化为文件
     * @version 1.0
     * @title 转化为文件
     * <AUTHOR>
     * @param path 文件路径
     * @param fileName 文件名
     * @throws Exception 转换异常
     */
    public void toFileWithTemplateString(String path, String fileName) throws Exception {
        try {
            //1. 替换模板内容
            StringWriter sw = new StringWriter();
            Velocity.evaluate(context, sw, "mystring", templateString);

            //2. 创建目录路径
            File dir = new File(path);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            //3. 创建文件
            File file = new File(dir.getPath(), fileName + ".java");
            if (!file.exists()) {
                file.createNewFile();
            }

            PrintWriter out = new PrintWriter(new BufferedWriter(new FileWriter(file)));
            out.println(sw.toString());
            out.close();
            
//@invalid            PrintWriter filewriter = new PrintWriter(new
//@invalid            FileOutputStream(fileName), true);
//@invalid            filewriter.println(sw.toString()); filewriter.close();
            
        } catch (Exception e) {
            throw new Exception(e.toString());
        }

    }

    /**
     * @description 转化为文件
     * @version 1.0
     * @title 转化为文件
     * <AUTHOR>
     * @param path 路径
     * @param fileName 文件名
     * @throws Exception 转换异常
     */
    public void toXmlFileWithTemplateString(String path, String fileName) throws Exception {
        try {
            //1. 替换模板内容
            StringWriter sw = new StringWriter();
            Velocity.evaluate(context, sw, "mystring", templateString);

            //2. 创建目录路径
            File dir = new File(path);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            //3. 创建文件
            File file = new File(dir.getPath(), fileName + ".xml");
            if (!file.exists()) {
                file.createNewFile();
            }

            PrintWriter out = new PrintWriter(new BufferedWriter(new FileWriter(file)));
            out.println(sw.toString());
            out.close();
            
//@invalid            PrintWriter filewriter = new PrintWriter(new
//@invalid            FileOutputStream(fileName), true);
//@invalid            filewriter.println(sw.toString()); filewriter.close();
            
        } catch (Exception e) {
            throw new Exception(e.toString());
        }

    }

}
