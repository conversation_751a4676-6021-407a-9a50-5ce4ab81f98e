package com.nci.tunan.clm.claimSYServiceClient.taxAmountQuery.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

/** 
 * #76401 需求取消45061个人税收递延型养老年金保险保全功能需求
 * @description 税延产品-交费可用税延额度查询接口(QRY003)
 * <AUTHOR> <EMAIL> 
 * @date 2020年6月22日 下午1:44:25 
 * @.belongToModule  CLM-理赔系统/税延产品
*/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "requestBody", propOrder = {
    "jsonString"
})
public class RequestBody {

    protected String jsonString;

    public String getJsonString() {
        return jsonString;
    }

    public void setJsonString(String value) {
        this.jsonString = value;
    }

}
