package com.nci.tunan.clm.claimSYServiceClient.claimInfoUpload.client;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

/** 
 * #69356 需求取消-43419-个人税收递延型养老年金保险理赔系统需求
 * @description 税延产品-理赔信息上传接口(CLM001)
 * <AUTHOR> <EMAIL> 
 * @date 2020年6月22日 下午1:44:25 
 * @.belongToModule  CLM-理赔系统/税延产品
*/
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "responseBody", propOrder = {
    "jsonString"
})
public class ResponseBody {

    protected String jsonString;

    public String getJsonString() {
        return jsonString;
    }

    public void setJsonString(String value) {
        this.jsonString = value;
    }

}
