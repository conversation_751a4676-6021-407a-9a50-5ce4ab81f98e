package com.nci.tunan.clm.claimSYServiceClient.customerProfileInfoQuery.customerProfileBO;

import java.util.List;

/** 
 * #69356 需求取消-43419-个人税收递延型养老年金保险理赔系统需求
 * @description 税延产品-客户概要信息查询接口(QRY001)
 * <AUTHOR> <EMAIL> 
 * @date 2020年6月22日 下午1:44:25 
 * @.belongToModule  CLM-理赔系统/税延产品
*/
public class CustomerBO {
	/** 
	* 平台客户编码
	*//*
	private String customerNo;
	*//** 
	* 国籍
	*//*
	private String nationality;
	*//** 
	* 保单列表
	*//*
	private List<PolicyBO> policyList;
	*//** 
	* 理赔信息列表
	*//*
	private List<ClaimBO> claimList;
	public String getCustomerNo() {
		return customerNo;
	}
	public void setCustomerNo(String customerNo) {
		this.customerNo = customerNo;
	}
	public String getNationality() {
		return nationality;
	}
	public void setNationality(String nationality) {
		this.nationality = nationality;
	}
	public List<PolicyBO> getPolicyList() {
		return policyList;
	}
	public void setPolicyList(List<PolicyBO> policyList) {
		this.policyList = policyList;
	}
	public List<ClaimBO> getClaimList() {
		return claimList;
	}
	public void setClaimList(List<ClaimBO> claimList) {
		this.claimList = claimList;
	}*/
}
