package com.nci.tunan.clm.claimSYServiceClient.customerProfileInfoQuery.client;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;

/** 
 * #69356 需求取消-43419-个人税收递延型养老年金保险理赔系统需求
 * @description 税延产品-客户概要信息查询接口(QRY001)
 * <AUTHOR> <EMAIL> 
 * @date 2020年6月22日 下午1:44:25 
 * @.belongToModule  CLM-理赔系统/税延产品
*/
@XmlRegistry
public class ObjectFactory {

    private final static QName _Header_QNAME = new QName("http://www.ciitc.com.cn/tdp", "header");
    private final static QName _Resheader_QNAME = new QName("http://www.ciitc.com.cn/tdp", "resheader");
    private final static QName _Request_QNAME = new QName("http://www.ciitc.com.cn/tdp", "request");
    private final static QName _Response_QNAME = new QName("http://www.ciitc.com.cn/tdp", "response");

    public ObjectFactory() {
    }

    public ResponseHeader createResponseHeader() {
        return new ResponseHeader();
    }

    public RequestHeader createRequestHeader() {
        return new RequestHeader();
    }

    public RequestBody createRequestBody() {
        return new RequestBody();
    }

    public ResponseBody createResponseBody() {
        return new ResponseBody();
    }

    public ResponseHeader.ResultMessage createResponseHeaderResultMessage() {
        return new ResponseHeader.ResultMessage();
    }

    @XmlElementDecl(namespace = "http://www.ciitc.com.cn/tdp", name = "header")
    public JAXBElement<RequestHeader> createHeader(RequestHeader value) {
        return new JAXBElement<RequestHeader>(_Header_QNAME, RequestHeader.class, null, value);
    }

    @XmlElementDecl(namespace = "http://www.ciitc.com.cn/tdp", name = "resheader")
    public JAXBElement<ResponseHeader> createResheader(ResponseHeader value) {
        return new JAXBElement<ResponseHeader>(_Resheader_QNAME, ResponseHeader.class, null, value);
    }

    @XmlElementDecl(namespace = "http://www.ciitc.com.cn/tdp", name = "request")
    public JAXBElement<RequestBody> createRequest(RequestBody value) {
        return new JAXBElement<RequestBody>(_Request_QNAME, RequestBody.class, null, value);
    }

    @XmlElementDecl(namespace = "http://www.ciitc.com.cn/tdp", name = "response")
    public JAXBElement<ResponseBody> createResponse(ResponseBody value) {
        return new JAXBElement<ResponseBody>(_Response_QNAME, ResponseBody.class, null, value);
    }

}
