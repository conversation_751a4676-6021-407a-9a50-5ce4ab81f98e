package com.nci.tunan.clm.claimSYServiceClient.customerProfileInfoQuery.service.impl;

import java.util.UUID;

import org.slf4j.Logger;

import com.nci.tunan.clm.claimSYServiceClient.customerProfileInfoQuery.client.RequestBody;
import com.nci.tunan.clm.claimSYServiceClient.customerProfileInfoQuery.client.RequestHeader;
import com.nci.tunan.clm.claimSYServiceClient.customerProfileInfoQuery.customerProfileBO.BodyBO;
import com.nci.tunan.clm.claimSYServiceClient.customerProfileInfoQuery.customerProfileBO.CustomerQryBO;
import com.nci.tunan.clm.claimSYServiceClient.customerProfileInfoQuery.dao.ICustomerProfileInfoQueryDao;
import com.nci.tunan.clm.claimSYServiceClient.customerProfileInfoQuery.service.ICustomerProfileInfoQueryService;
import com.nci.udmp.util.json.JsonUtilsEx;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;

/** 
 * #69356 需求取消-43419-个人税收递延型养老年金保险理赔系统需求
 * @description 税延产品-客户概要信息查询接口(QRY001)
 * <AUTHOR> <EMAIL> 
 * @date 2020年6月22日 下午1:44:25 
 * @.belongToModule  CLM-理赔系统/税延产品
*/
public class CustomerProfileInfoQueryServiceImpl implements ICustomerProfileInfoQueryService{
	private static Logger logger = LoggerFactory.getLogger();
	
	/*#69356 需求取消-43419-个人税收递延型养老年金保险理赔系统需求
	 * private ICustomerProfileInfoQueryDao customerProInfoQryDao;
	public ICustomerProfileInfoQueryDao getCustomerProInfoQryDao() {
		return customerProInfoQryDao;
	}
	public void setCustomerProInfoQryDao(ICustomerProfileInfoQueryDao customerProInfoQryDao) {
		this.customerProInfoQryDao = customerProInfoQryDao;
	}*/
	
	/*#69356 需求取消-43419-个人税收递延型养老年金保险理赔系统需求
	 * public void getCustomerQryData(RequestHeader reqHeader,
			RequestBody reqBody, BodyBO inputData) {
		logger.debug("CustomerProfileInfoQueryServiceImpl=======获取数据，组装申请报文");
		CustomerQryBO customer = new CustomerQryBO();
		customer.setStartDate(DateUtilsEx.formatDate(inputData.getCustomer().getStartDate(), "yyyy-MM-dd"));
		customer.setEndDate(DateUtilsEx.formatDate(inputData.getCustomer().getEndDate(), "yyyy-MM-dd"));
		inputData.getCustomer().setStartDate(customer.getStartDate());
		inputData.getCustomer().setEndDate(customer.getEndDate());
		
		reqHeader.setUserName("xinhuarenshou02");
	    reqHeader.setPassword("CiitcXHRS02");
	    reqHeader.setTransType("QRY001");
	    reqHeader.setTransNo(UUID.randomUUID().toString().replaceAll("-", ""));
	    reqHeader.setTransDate(DateUtilsEx.getTodayTime());
		//header.setAreaCode(""); //不是必传项
	    reqHeader.setRecordNum(1);
	    reqHeader.setVersion("1.0.0.0");
		String bodyString = JsonUtilsEx.objToJson(inputData);
		reqBody.setJsonString(bodyString);
	}*/
	
}
