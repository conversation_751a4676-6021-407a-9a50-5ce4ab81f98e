package com.nci.tunan.cs.interfaces.peripheral.exports.r00101000024.vo;

import java.io.Serializable;

//领取形式变更（初始化）
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlType(propOrder = { "edorAcceptNo", "contNo", "bussType" })
public class InputData implements Serializable{
	
	private String edorAcceptNo;
	
	@XmlElement(name = "EdorAcceptNo")
	public String getEdorAcceptNo() {
		return edorAcceptNo;
	}

	public void setEdorAcceptNo(String edorAcceptNo) {
		this.edorAcceptNo = edorAcceptNo;
	}   //保单号

	//143124 保单号
	private String contNo;
	
	@XmlElement(name="ContNo")
	public String getContNo() {
		return contNo;
	}
	public void setContNo(String contNo) {
		this.contNo = contNo;
	}
	
    /**
     * 业务场景 0-既往数据 1-最新数据
     */
    private String bussType;

    @XmlElement(name = "bussType")
    public String getBussType() {
        return bussType;
    }

    public void setBussType(String bussType) {
        this.bussType = bussType;
    }
    
}
