package com.nci.tunan.cs.interfaces.peripheral.exports.r06802003134.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlType(name = "insuredNotificationList", propOrder = {
"customerID",
"notificationList"
})
public class InsuredNotification implements Serializable {

	private static final long serialVersionUID = 1L;
	
	/**
	 * 被保险人客户号
	 */
	private String customerID;
	
	/**
	 * 被保人告知信息
	 */
	private List<Notification> notificationList;

	@XmlElement(name="customerID")
	public String getCustomerID() {
		return customerID;
	}

	public void setCustomerID(String customerID) {
		this.customerID = customerID;
	}

	@XmlElement(name="notificationList")
	public List<Notification> getNotificationList() {
		return notificationList;
	}

	public void setNotificationList(List<Notification> notificationList) {
		this.notificationList = notificationList;
	}

	
	
}
