package com.nci.tunan.cs.interfaces.peripheral.exports.r06802003148.vo;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.nci.tunan.cs.interfaces.peripheral.exports.r06802003148.vo.InsuredInfo;

public class InsuredInfoList {

	private String insuredNo;
	private String insuredName;
	private List<InsuredInfo> insuredInfo;
	
	@XmlElement(name="insuredNo")
	public String getInsuredNo() {
		return insuredNo;
	}
	public void setInsuredNo(String insuredNo) {
		this.insuredNo = insuredNo;
	}
	
	@XmlElement(name="insuredName")
	public String getInsuredName() {
		return insuredName;
	}
	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}
	
	@XmlElement(name="insuredInfo")
	public List<InsuredInfo> getInsuredInfo() {
		return insuredInfo;
	}
	public void setInsuredInfo(List<InsuredInfo> insuredInfo) {
		this.insuredInfo = insuredInfo;
	}
	
	
}
