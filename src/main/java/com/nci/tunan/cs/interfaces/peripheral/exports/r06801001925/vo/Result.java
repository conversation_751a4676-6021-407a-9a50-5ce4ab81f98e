package com.nci.tunan.cs.interfaces.peripheral.exports.r06801001925.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.nci.tunan.cs.interfaces.peripheral.exports.r00101000626.vo.PolNoItem;
//保全核保照会下核保照会查询
public class Result implements Serializable{

	//保单号码
	private String contno;
	
	//管理机构编码
	private String managecom;
	
	//管理机构名称
	private String codename;
	
	//销售渠道
	private String salechnl;
	
	//业务员编码
	private String AgentCode;
	
	//其他声明
	private String Remark;
	
	//被保人
	private List<Insured> Insured;
	
	
	@XmlElement(name = "contno")
	public String getContno() {
		return contno;
	}
	public void setContno(String contno) {
		this.contno = contno;
	}
	
	@XmlElement(name = "managecom")
	public String getManagecom() {
		return managecom;
	}
	public void setManagecom(String managecom) {
		this.managecom = managecom;
	}
	
	@XmlElement(name = "codename")
	public String getCodename() {
		return codename;
	}
	public void setCodename(String codename) {
		this.codename = codename;
	}

	@XmlElement(name = "salechnl")
	public String getSalechnl() {
		return salechnl;
	}
	public void setSalechnl(String salechnl) {
		this.salechnl = salechnl;
	}
	
	@XmlElement(name = "Remark")
	public String getRemark() {
		return Remark;
	}
	public void setRemark(String Remark) {
		this.Remark = Remark;
	}

	@XmlElement(name = "AgentCode")
	public String getAgentCode() {
		return AgentCode;
	}
	public void setAgentCode(String AgentCode) {
		this.AgentCode = AgentCode;
	}
	
	@XmlElement(name="Insured")
	public List<Insured> getInsured() {
		return Insured;
	}
	public void setInsured(List<Insured> Insured) {
		this.Insured = Insured;
	}
	
}
