package com.nci.tunan.cs.interfaces.peripheral.exports.r06802003154.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@SuppressWarnings("serial")
@XmlType(name = "DeadBnf", propOrder = {
        "customerNo",
        "name",
        "sex",
        "relationToInsured",
        "relationToInsuredNm",
        "IDType",
        "IDNo",
        "birthday",
        "idStartDate",
        "idEndDate",
        "nativeplace",
        "occupationType",
        "mobile",
        "province",
        "city",
        "county",
        "townStreetCode",
        "townStreetName",
        "street",
        "bnfGrade",
        "bnfGradeName",
        "bnfLotNumerator",
        "bnfLotDenominator",
})
public class DeadBnf implements Serializable {

    // Name 姓名 bd:DeadBnf
    private String name;
    // CustomerNo 客户号 bd:DeadBnf
    private String customerNo;
    // RelationToInsured 与被保人关系 bd:DeadBnf
    private String relationToInsured;
    private String relationToInsuredNm;// 与被保人关系名称
    // Sex 性别 bd:DeadBnf
    private String sex;
    // Birthday 出生日期 bd:DeadBnf
    private String birthday;
    // IDType 证件类型 bd:DeadBnf
    private String iDType;
    // IDNo 证件号码 bd:DeadBnf
    private String iDNo;
    // IdStartDate 证件有效期起期 bd:DeadBnf
    private String idStartDate;
    // IdEndDate 证件有效期止期 bd:DeadBnf
    private String idEndDate;
    // Nativeplace 国籍 bd:DeadBnf
    private String nativeplace;
    // OccupationType 职业小类 bd:DeadBnf
    private String occupationType;
    // BnfGrade 受益顺序 bd:DeadBnf
    private String bnfGrade;
    private String bnfGradeName;
    // BnfLotNumerator 受益分子 bd:DeadBnf
    private String bnfLotNumerator;
    // BnfLotDenominator 受益分母 bd:DeadBnf
    private String bnfLotDenominator;
    // Mobile 手机号 bd:DeadBnf
    private String mobile;
    // Province 省 bd:DeadBnf
    private String province;
    // City 市 bd:DeadBnf
    private String city;
    // County 县（区） bd:DeadBnf
    private String county;
    // 街道/镇编码
    private String townStreetCode;
    // 街道/镇名称
    private String townStreetName;
    // Street 详细地址 bd:DeadBnf
    private String street;

    @XmlElement(name = "Name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @XmlElement(name = "CustomerNo")
    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    @XmlElement(name = "RelationToInsured")
    public String getRelationToInsured() {
        return relationToInsured;
    }

    public void setRelationToInsured(String relationToInsured) {
        this.relationToInsured = relationToInsured;
    }

   
    
    @XmlElement(name = "Sex")
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    @XmlElement(name = "Birthday")
    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    @XmlElement(name = "IDType")
    public String getIDType() {
        return iDType;
    }

    public void setIDType(String iDType) {
        this.iDType = iDType;
    }

    @XmlElement(name = "IDNo")
    public String getIDNo() {
        return iDNo;
    }

    public void setIDNo(String iDNo) {
        this.iDNo = iDNo;
    }

    @XmlElement(name = "IdStartDate")
    public String getIdStartDate() {
        return idStartDate;
    }

    public void setIdStartDate(String idStartDate) {
        this.idStartDate = idStartDate;
    }

    @XmlElement(name = "IdEndDate")
    public String getIdEndDate() {
        return idEndDate;
    }

    public void setIdEndDate(String idEndDate) {
        this.idEndDate = idEndDate;
    }

    @XmlElement(name = "Nativeplace")
    public String getNativeplace() {
        return nativeplace;
    }

    public void setNativeplace(String nativeplace) {
        this.nativeplace = nativeplace;
    }

    @XmlElement(name = "OccupationType")
    public String getOccupationType() {
        return occupationType;
    }

    public void setOccupationType(String occupationType) {
        this.occupationType = occupationType;
    }

    @XmlElement(name = "BnfGrade")
    public String getBnfGrade() {
        return bnfGrade;
    }

    public void setBnfGrade(String bnfGrade) {
        this.bnfGrade = bnfGrade;
    }

    
    @XmlElement(name = "BnfGradeName")
    public String getBnfGradeName() {
        return bnfGradeName;
    }

    public void setBnfGradeName(String bnfGradeName) {
        this.bnfGradeName = bnfGradeName;
    }

    @XmlElement(name = "BnfLotNumerator")
    public String getBnfLotNumerator() {
        return bnfLotNumerator;
    }

    public void setBnfLotNumerator(String bnfLotNumerator) {
        this.bnfLotNumerator = bnfLotNumerator;
    }

    @XmlElement(name = "BnfLotDenominator")
    public String getBnfLotDenominator() {
        return bnfLotDenominator;
    }

    public void setBnfLotDenominator(String bnfLotDenominator) {
        this.bnfLotDenominator = bnfLotDenominator;
    }

    @XmlElement(name = "Mobile")
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @XmlElement(name = "Province")
    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    @XmlElement(name = "City")
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @XmlElement(name = "County")
    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }
    
    @XmlElement(name = "townStreetCode")
    public String getTownStreetCode() {
		return townStreetCode;
	}

	public void setTownStreetCode(String townStreetCode) {
		this.townStreetCode = townStreetCode;
	}

	@XmlElement(name = "townStreetName")
	public String getTownStreetName() {
		return townStreetName;
	}

	public void setTownStreetName(String townStreetName) {
		this.townStreetName = townStreetName;
	}

	@XmlElement(name = "Street")
    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }
    @XmlElement(name="RelationToInsuredNm")
	public String getRelationToInsuredNm() {
		return relationToInsuredNm;
	}

	public void setRelationToInsuredNm(String relationToInsuredNm) {
		this.relationToInsuredNm = relationToInsuredNm;
	}

}
