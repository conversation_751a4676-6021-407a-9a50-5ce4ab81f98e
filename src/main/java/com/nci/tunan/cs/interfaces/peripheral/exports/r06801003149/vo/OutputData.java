package com.nci.tunan.cs.interfaces.peripheral.exports.r06801003149.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

@SuppressWarnings("serial")
public class OutputData implements Serializable {
	//试算成功的标志0-成功, 1-失败,	2-异常
	private String ResultCode;
	//提示话术1失败的原因,	2 异常失败的原因
	private String ResultMsg;
	
	@XmlElement(name="ResultCode")
	public String getResultCode() {
		return ResultCode;
	}
	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}
	@XmlElement(name="ResultMsg")
	public String getResultMsg() {
		return ResultMsg;
	}
	public void setResultMsg(String resultMsg) {
		ResultMsg = resultMsg;
	}

}
