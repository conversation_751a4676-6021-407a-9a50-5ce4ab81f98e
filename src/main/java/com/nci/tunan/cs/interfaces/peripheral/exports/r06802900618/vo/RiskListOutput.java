package com.nci.tunan.cs.interfaces.peripheral.exports.r06802900618.vo;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;

/** 
 * @description 退保金额明细列表
 * <AUTHOR> <EMAIL> 
 * @date 2024年11月11日 下午4:51:27 
 * @.belongToModule 保全子系统 
*/
public class RiskListOutput {
	
	/** 
	* 退保金额明细
	*/ 
	private List<RiskListInfoOutput> riskListInfoOutput;
	@XmlElement(name="riskListInfoOutput")
	public List<RiskListInfoOutput> getRiskListInfoOutput() {
		return riskListInfoOutput;
	}

	public void setRiskListInfoOutput(List<RiskListInfoOutput> riskListInfoOutput) {
		this.riskListInfoOutput = riskListInfoOutput;
	}
	
}
