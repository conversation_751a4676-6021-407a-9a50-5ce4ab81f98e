package com.nci.tunan.cs.interfaces.peripheral.exports.r06802901087.vo;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.nci.tunan.cs.interfaces.peripheral.exports.r06802901087.vo.RelationInfo;

/** 
 * @description 
 * <AUTHOR> <EMAIL> 
 * @date 2025年4月27日 上午10:52:11 
 * @.belongToModule 保全子系统 
*/
public class RelationInfoList {
	/**
	 * 变更险种关联关系信息
	 */
	private List<RelationInfo> relationInfo;

	@XmlElement(name="relationInfo")
	public List<RelationInfo> getRelationInfo() {
		return relationInfo;
	}

	public void setRelationInfo(List<RelationInfo> relationInfo) {
		this.relationInfo = relationInfo;
	}
	
}
