package com.nci.tunan.cs.interfaces.peripheral.exports.r06802901087.icontractrstrialucc.csendorserstrial;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody")
public class SrvReqBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;

    @XmlElement(name = "inputData")
    protected com.nci.tunan.cs.interfaces.peripheral.exports.r06802901087.vo.InputData inputData;
    
    public com.nci.tunan.cs.interfaces.peripheral.exports.r06802901087.vo.InputData getInputData() {
        return inputData;
    }
    public void setInputData(com.nci.tunan.cs.interfaces.peripheral.exports.r06802901087.vo.InputData inputData) {
        this.inputData = inputData;
    }
}


