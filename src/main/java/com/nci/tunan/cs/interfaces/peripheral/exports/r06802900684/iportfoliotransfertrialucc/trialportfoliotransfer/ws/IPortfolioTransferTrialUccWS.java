package com.nci.tunan.cs.interfaces.peripheral.exports.r06802900684.iportfoliotransfertrialucc.trialportfoliotransfer.ws;

import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.ws.Holder;

import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.tunan.cs.interfaces.peripheral.exports.r06802900684.iportfoliotransfertrialucc.trialportfoliotransfer.SrvReqBody;
import com.nci.tunan.cs.interfaces.peripheral.exports.r06802900684.iportfoliotransfertrialucc.trialportfoliotransfer.SrvResBody;

/**
 * IPortfolioTransferTrialUccWS
 * 
 * @description 投资组合转换试算接口
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @.belongToModule cs-保全系统
 * @date 2022年6月9日 下午14:12:26
 */
@WebService
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface IPortfolioTransferTrialUccWS {
	public void trialPortfolioTransfer(
			@WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, partName = "parametersReqHeader") SysHeader parametersReqHeader,
			@WebParam(name = "request", targetNamespace = "http://www.newchinalife.com/service/bd", partName = "parametersReqBody") SrvReqBody parametersReqBody,
			@WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, mode = WebParam.Mode.OUT, partName = "parametersResHeader") Holder<SysHeader> parametersResHeader,
			@WebParam(name = "response", targetNamespace = "http://www.newchinalife.com/service/bd", mode = WebParam.Mode.OUT, partName = "parametersResBody") Holder<SrvResBody> parametersResBody);
}
