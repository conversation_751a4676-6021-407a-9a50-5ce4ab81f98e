package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000180.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

//附加险期满不续保（提交）
public class InputData implements Serializable {
	// 客户/保单号
	private String OtherNo;
	// 申请号码类型
	private String OtherNoType;
	// 申请人
	private String EdorApp;
	// 申请方式
	private String AppType;
	// 保全申请提交日期
	private String EdorAppDate;
	// 批改项目
	private String EdorType;
	// 批改项目保全申请提交日期
	private String EdorItemAppDate;
	// 保单险种号码
	private String Polno;

	@XmlElement(name = "OtherNo")
	public String getOtherNo() {
		return OtherNo;
	}

	public void setOtherNo(String otherNo) {
		OtherNo = otherNo;
	}

	@XmlElement(name = "OtherNoType")
	public String getOtherNoType() {
		return OtherNoType;
	}

	public void setOtherNoType(String otherNoType) {
		OtherNoType = otherNoType;
	}

	@XmlElement(name = "EdorApp")
	public String getEdorApp() {
		return EdorApp;
	}

	public void setEdorApp(String edorApp) {
		EdorApp = edorApp;
	}

	@XmlElement(name = "AppType")
	public String getAppType() {
		return AppType;
	}

	public void setAppType(String appType) {
		AppType = appType;
	}

	@XmlElement(name = "EdorAppDate")
	public String getEdorAppDate() {
		return EdorAppDate;
	}

	public void setEdorAppDate(String edorAppDate) {
		EdorAppDate = edorAppDate;
	}

	@XmlElement(name = "EdorType")
	public String getEdorType() {
		return EdorType;
	}

	public void setEdorType(String edorType) {
		EdorType = edorType;
	}

	@XmlElement(name = "EdorItemAppDate")
	public String getEdorItemAppDate() {
		return EdorItemAppDate;
	}

	public void setEdorItemAppDate(String edorItemAppDate) {
		EdorItemAppDate = edorItemAppDate;
	}

	@XmlElement(name = "Polno")
	public String getPolno() {
		return Polno;
	}

	public void setPolno(String polno) {
		Polno = polno;
	}

}
