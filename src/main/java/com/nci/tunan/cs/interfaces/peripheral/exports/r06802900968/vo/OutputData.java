package com.nci.tunan.cs.interfaces.peripheral.exports.r06802900968.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class OutputData implements Serializable{

	private static final long serialVersionUID = 6535613498654737075L;

	/** 处理结果  0：成功；1：失败； **/
	private String resultCode;
	
	/** 保全受理号 **/
	private String edorAcceptNo;
	
	/** 保全受理状态 **/
	private String edorState;
	
	/** 错误代码
	 * 000001-受理规则不通过
	 * 000002-录入规则不通过
	 * 000003-保存按钮规则不通过
	 * 000004-投保规则不通过
	 * 000005-自动核保不通过
	 * 000006-自动复核不通过
	 * 000099-校验规则不通过
	 *  **/
	private String errorCode;
	
	/** 处理结果描述 **/
	private String resultMsg;

	
	@XmlElement(name = "ResultCode")
	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	@XmlElement(name = "EdorAcceptNo")
	public String getEdorAcceptNo() {
		return edorAcceptNo;
	}

	public void setEdorAcceptNo(String edorAcceptNo) {
		this.edorAcceptNo = edorAcceptNo;
	}

	@XmlElement(name = "EdorState")
	public String getEdorState() {
		return edorState;
	}

	public void setEdorState(String edorState) {
		this.edorState = edorState;
	}

	@XmlElement(name = "ErrorCode")
	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	@XmlElement(name = "ResultMsg")
	public String getResultMsg() {
		return resultMsg;
	}

	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}
	
}
