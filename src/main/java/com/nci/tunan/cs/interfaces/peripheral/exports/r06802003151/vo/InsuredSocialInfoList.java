package com.nci.tunan.cs.interfaces.peripheral.exports.r06802003151.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

/**157477 create 被保人社保状态列表
 * <AUTHOR>
 *
 */
public class InsuredSocialInfoList implements Serializable {

	private static final long serialVersionUID = 1L;
	private List<InsuredSocialInfo> insuredSocialInfo;
	@XmlElement(name="InsuredSocialInfo")
	public List<InsuredSocialInfo> getInsuredSocialInfo() {
		return insuredSocialInfo;
	}
	public void setInsuredSocialInfo(List<InsuredSocialInfo> insuredSocialInfo) {
		this.insuredSocialInfo = insuredSocialInfo;
	}
	
}
