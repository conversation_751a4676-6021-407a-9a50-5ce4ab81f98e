package com.nci.tunan.cs.interfaces.peripheral.exports.r06802003105.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
/**
 * <AUTHOR>
 * @data 2024年5月11日 下午3:17:15
 * 社保状态变更提交 被保险人列表
 * 涉及多被保险人社保状态变更时，使用此节点信息
 */
public class InsuredList implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private List<Insured> Insured;//被保险人信息

	@XmlElement(name="Insured")
	public List<Insured> getInsured() {
		return Insured;
	}

	public void setInsured(List<Insured> insured) {
		Insured = insured;
	}	



	
	
}
