package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000171.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class Result  implements Serializable {
	  /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String EdorState;//操作结果码
	private String EdorStateName;//操作结果说明
	private String PolNo;//保单险种号码
	private String StandMoney;//变更后领取金额
	private String EdorAcceptNo;//保全受理号
	
	@XmlElement(name="EdorState")	
	public String getEdorState() {
		return EdorState;
	}
	public void setEdorState(String edorState) {
		EdorState = edorState;
	}
	@XmlElement(name="EdorStateName")	
	public String getEdorStateName() {
		return EdorStateName;
	}
	public void setEdorStateName(String edorStateName) {
		EdorStateName = edorStateName;
	}
	@XmlElement(name="PolNo")	
	public String getPolNo() {
		return PolNo;
	}
	public void setPolNo(String polNo) {
		PolNo = polNo;
	}
	@XmlElement(name="StandMoney")	
	public String getStandMoney() {
		return StandMoney;
	}
	public void setStandMoney(String standMoney) {
		StandMoney = standMoney;
	}
	@XmlElement(name="EdorAcceptNo")	
	public String getEdorAcceptNo() {
		return EdorAcceptNo;
	}
	public void setEdorAcceptNo(String edorAcceptNo) {
		EdorAcceptNo = edorAcceptNo;
	}
}
