package com.nci.tunan.cs.interfaces.peripheral.exports.r06801001918;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * @description 
 * <AUTHOR> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2018年3月22日 下午4:56:07
 */
public class CsDisplayType {
	
	public static final Map<String,String> displayTypeMap = new HashMap<String,String>();
	
	static{
		displayTypeMap.put("SP","2");
		displayTypeMap.put("PG","2");
		displayTypeMap.put("CB","3");
		displayTypeMap.put("CA","3");
		displayTypeMap.put("DC","3");
		displayTypeMap.put("MR","2");
		displayTypeMap.put("PC","1");
		displayTypeMap.put("FM","3");
		displayTypeMap.put("CF","1");
		displayTypeMap.put("RE","2");
		displayTypeMap.put("RA","2");
		displayTypeMap.put("PL","1");
		displayTypeMap.put("LR","1");
		displayTypeMap.put("CW","1");
		displayTypeMap.put("PF","1");
		displayTypeMap.put("CS","1");
		displayTypeMap.put("CP","1");
		displayTypeMap.put("LN","2");
		displayTypeMap.put("RF","2");
		displayTypeMap.put("RL","2");
		displayTypeMap.put("PR","2");
		displayTypeMap.put("AP","2");
		displayTypeMap.put("YS","2");
		displayTypeMap.put("XX","2");
		displayTypeMap.put("EA","4");
		displayTypeMap.put("CD","2");
		displayTypeMap.put("PT","3");
		displayTypeMap.put("PA","3");
		displayTypeMap.put("PU","2");
		displayTypeMap.put("XT","2");
		displayTypeMap.put("BC","2");
		displayTypeMap.put("MD","2");
		displayTypeMap.put("MC","1");
		displayTypeMap.put("HI","1");
		displayTypeMap.put("CC","1");
		displayTypeMap.put("FK","1");
		displayTypeMap.put("IO","1");
		displayTypeMap.put("CM","1");
		displayTypeMap.put("AG","1");
		displayTypeMap.put("AE","1");
		displayTypeMap.put("TI","2");
		displayTypeMap.put("IT","2");
		displayTypeMap.put("XD","3");
		displayTypeMap.put("DA","2");
		displayTypeMap.put("NS","2");
		displayTypeMap.put("AZ","3");
		displayTypeMap.put("RG","1");
		displayTypeMap.put("AI","2");
		displayTypeMap.put("RR","2");
		displayTypeMap.put("TR","2");
		displayTypeMap.put("TA","2");
		displayTypeMap.put("AM","2");
		displayTypeMap.put("CT","3");
		displayTypeMap.put("DT","2");
		displayTypeMap.put("EN","2");
		displayTypeMap.put("ER","2");
		displayTypeMap.put("GB","3");
		displayTypeMap.put("GC","2");
		displayTypeMap.put("GM","2");
		displayTypeMap.put("LC","3");
	}

}
