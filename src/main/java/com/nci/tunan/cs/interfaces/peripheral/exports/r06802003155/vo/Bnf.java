package com.nci.tunan.cs.interfaces.peripheral.exports.r06802003155.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@SuppressWarnings("serial")
@XmlType(name = "Bnf", propOrder = { "relationNo",
        "name",
        "sex",
        "relationToInsured",
        "relationToName",
        "IDType",
        "IDNo",
        "birthday",
        "bnfIdEffStartDate",
        "bnfIdEffEndDate",
        "native",
        "occupationCode",
        "mobile",
        "province",
        "city",
        "county",
        "townStreetCode",
        "townStreetName",
        "street",
        "bnfGrade",
        "bnfLotNumerator",
        "bnfLotDenominator",
        "phoneNoCheckResult",
        "checkFailReason",
        "checkTime",
        "afterTaxMoney"
      })
public class Bnf implements Serializable {
    private String relationNo;// 客户号
    private String name;// 姓名
    private String sex;// 性别
    private String relationToInsured;// 与被保人关系
    private String relationToName;// 与被保人关系名称
    private String iDType;// 证件类型
    private String iDNo;// 证件号
    private String birthday;// 出生日期
    private String bnfIdEffStartDate;// 证件有效期起期
    private String bnfIdEffEndDate;// 证件有效期止期
    private String Native;
    private String occupationCode;// 职业小类
    private String mobile;// 手机号
    private String province;// 省
    private String city;// 市
    private String county;// 县
    private String townStreetCode;//街道/镇编码
    private String townStreetName;//街道/镇名称
    private String street;// 地址
    private String bnfGrade;// 受益顺序
    private String bnfLotNumerator;// 受益分子
    private String bnfLotDenominator;// 受益分母
    private String phoneNoCheckResult;// 被核验人手机号核验结果
    private String checkFailReason;// 失败原因
    private String checkTime;// 核验时间

	// 上一自然年度税后年收入
	private String afterTaxMoney;

	@XmlElement(name = "AfterTaxMoney")
	public String getAfterTaxMoney() {
		return afterTaxMoney;
	}

	public void setAfterTaxMoney(String afterTaxMoney) {
		this.afterTaxMoney = afterTaxMoney;
	}
    @XmlElement(name = "RelationNo")
    public String getRelationNo() {
        return relationNo;
    }

    public void setRelationNo(String relationNo) {
        this.relationNo = relationNo;
    }

    @XmlElement(name = "Name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @XmlElement(name = "Sex")
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    @XmlElement(name = "RelationToInsured")
    public String getRelationToInsured() {
        return relationToInsured;
    }

    public void setRelationToInsured(String relationToInsured) {
        this.relationToInsured = relationToInsured;
    }

    @XmlElement(name = "RelationToName")
    public String getRelationToName() {
        return relationToName;
    }

    public void setRelationToName(String relationToName) {
        this.relationToName = relationToName;
    }

    @XmlElement(name = "IDType")
    public String getIDType() {
        return iDType;
    }

    public void setIDType(String iDType) {
        this.iDType = iDType;
    }

    @XmlElement(name = "IDNo")
    public String getIDNo() {
        return iDNo;
    }

    public void setIDNo(String iDNo) {
        this.iDNo = iDNo;
    }

    @XmlElement(name = "Birthday")
    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    @XmlElement(name = "BnfIdEffStartDate")
    public String getBnfIdEffStartDate() {
        return bnfIdEffStartDate;
    }

    public void setBnfIdEffStartDate(String bnfIdEffStartDate) {
        this.bnfIdEffStartDate = bnfIdEffStartDate;
    }

    @XmlElement(name = "BnfIdEffEndDate")
    public String getBnfIdEffEndDate() {
        return bnfIdEffEndDate;
    }

    public void setBnfIdEffEndDate(String bnfIdEffEndDate) {
        this.bnfIdEffEndDate = bnfIdEffEndDate;
    }

    @XmlElement(name = "Native")
    public String getNative() {
        return Native;
    }

    public void setNative(String native1) {
        Native = native1;
    }

    @XmlElement(name = "OccupationCode")
    public String getOccupationCode() {
        return occupationCode;
    }

    public void setOccupationCode(String occupationCode) {
        this.occupationCode = occupationCode;
    }

    @XmlElement(name = "Mobile")
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    @XmlElement(name = "Province")
    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    @XmlElement(name = "City")
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @XmlElement(name = "County")
    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }
    
    @XmlElement(name = "townStreetCode")
    public String getTownStreetCode() {
		return townStreetCode;
	}

	public void setTownStreetCode(String townStreetCode) {
		this.townStreetCode = townStreetCode;
	}

	@XmlElement(name = "townStreetName")
	public String getTownStreetName() {
		return townStreetName;
	}

	public void setTownStreetName(String townStreetName) {
		this.townStreetName = townStreetName;
	}

	@XmlElement(name = "Street")
    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    @XmlElement(name = "BnfGrade")
    public String getBnfGrade() {
        return bnfGrade;
    }

    public void setBnfGrade(String bnfGrade) {
        this.bnfGrade = bnfGrade;
    }

    @XmlElement(name = "BnfLotNumerator")
    public String getBnfLotNumerator() {
        return bnfLotNumerator;
    }

    public void setBnfLotNumerator(String bnfLotNumerator) {
        this.bnfLotNumerator = bnfLotNumerator;
    }

    @XmlElement(name = "BnfLotDenominator")
    public String getBnfLotDenominator() {
        return bnfLotDenominator;
    }

    public void setBnfLotDenominator(String bnfLotDenominator) {
        this.bnfLotDenominator = bnfLotDenominator;
    }

    @XmlElement(name = "PhoneNoCheckResult")
	public String getPhoneNoCheckResult() {
		return phoneNoCheckResult;
	}

	public void setPhoneNoCheckResult(String phoneNoCheckResult) {
		this.phoneNoCheckResult = phoneNoCheckResult;
	}

	@XmlElement(name = "CheckFailReason")
	public String getCheckFailReason() {
		return checkFailReason;
	}

	public void setCheckFailReason(String checkFailReason) {
		this.checkFailReason = checkFailReason;
	}

	@XmlElement(name = "CheckTime")
	public String getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(String checkTime) {
		this.checkTime = checkTime;
	}

    
    
}
