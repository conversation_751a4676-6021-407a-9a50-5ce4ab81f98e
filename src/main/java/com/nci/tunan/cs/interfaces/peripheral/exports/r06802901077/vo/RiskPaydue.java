package com.nci.tunan.cs.interfaces.peripheral.exports.r06802901077.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlType(name = "RiskPaydue", propOrder = {"busiProdCode","productNameSys","productAbbrSys","riskStates","riskStatesDetail","paidCount","payDueDate","feeAmount"})
public class RiskPaydue implements Serializable{

	private static final long serialVersionUID = -3590537226679555548L;

	//@invalid 险种代码  返回产品定义中的“产品代码（系统用）”，如“00485000”
	private String busiProdCode;
	
	//@invalid 险种全称 返回产品定义中的“产品名称（系统用）”，如“ 485 双被保险人增额终身寿险”
	private String productNameSys;
	
	//@invalid 险种简称 返回产品定义中的“产品简称”，如“双被保险人增额终身寿”
	private String productAbbrSys;
	//@invalid 险种状态
	private String riskStates;
	//@invalid 险种状态描述
	private String riskStatesDetail;
	
	//@invalid 应付期数
	private String paidCount;
	
	//@invalid 应领日期 yyyy-MM-dd
	private String payDueDate;
	
	//@invalid 应领金额 单位“元”，小数点后保留两位，如“1650.00”
	private String feeAmount;

	@XmlElement(name="riskStates")
	public String getRiskStates() {
		return riskStates;
	}

	public void setRiskStates(String riskStates) {
		this.riskStates = riskStates;
	}
	@XmlElement(name="riskStatesDetail")
	public String getRiskStatesDetail() {
		return riskStatesDetail;
	}

	public void setRiskStatesDetail(String riskStatesDetail) {
		this.riskStatesDetail = riskStatesDetail;
	}

	@XmlElement(name="busiProdCode")
	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	@XmlElement(name="productNameSys")
	public String getProductNameSys() {
		return productNameSys;
	}

	public void setProductNameSys(String productNameSys) {
		this.productNameSys = productNameSys;
	}

	@XmlElement(name="productAbbrSys")
	public String getProductAbbrSys() {
		return productAbbrSys;
	}

	public void setProductAbbrSys(String productAbbrSys) {
		this.productAbbrSys = productAbbrSys;
	}

	@XmlElement(name="paidCount")
	public String getPaidCount() {
		return paidCount;
	}

	public void setPaidCount(String paidCount) {
		this.paidCount = paidCount;
	}

	@XmlElement(name="payDueDate")
	public String getPayDueDate() {
		return payDueDate;
	}

	public void setPayDueDate(String payDueDate) {
		this.payDueDate = payDueDate;
	}

	@XmlElement(name="feeAmount")
	public String getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(String feeAmount) {
		this.feeAmount = feeAmount;
	}
}
