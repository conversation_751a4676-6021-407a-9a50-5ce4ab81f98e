package com.nci.tunan.cs.interfaces.peripheral.exports.r00101002795.vo;

import javax.xml.bind.annotation.XmlElement;

/**
 * 双录自保件接口
 * 服务编码:P00001002796
 */
public class CustomerInfo {
	
	// 投保人姓名
	private String AppntName;
	
	// 投保人证件类型
	private String AppntIDType;
	
	// 投保人证件号码
	private String AppntIDNO;

	// 性别
	private String Sex;

	// 出生日期
	private String Birthday;

	@XmlElement(name="AppntName")
	public String getAppntName() {
		return AppntName;
	}

	public void setAppntName(String appntName) {
		AppntName = appntName;
	}
	@XmlElement(name="AppntIDType")
	public String getAppntIDType() {
		return AppntIDType;
	}

	public void setAppntIDType(String appntIDType) {
		AppntIDType = appntIDType;
	}
	@XmlElement(name="AppntIDNO")
	public String getAppntIDNO() {
		return AppntIDNO;
	}

	public void setAppntIDNO(String appntIDNO) {
		AppntIDNO = appntIDNO;
	}
	
	@XmlElement(name="Sex")	
	public String getSex() {
		return Sex;
	}

	public void setSex(String sex) {
		Sex = sex;
	}
	
	@XmlElement(name="Birthday")
	public String getBirthday() {
		return Birthday;
	}

	public void setBirthday(String birthday) {
		Birthday = birthday;
	}
	
}
