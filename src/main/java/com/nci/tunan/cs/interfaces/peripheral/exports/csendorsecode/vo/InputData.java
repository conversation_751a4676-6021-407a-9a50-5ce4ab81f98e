package com.nci.tunan.cs.interfaces.peripheral.exports.csendorsecode.vo;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlElement;

/**
 * 保全批单号快查input
 * <AUTHOR>
 *
 */
public class InputData implements Serializable{

	//保全批单号
	
	private String endorseCode;

	@XmlElement(name = "endorseCode")
	public String getEndorseCode() {
		return endorseCode;
	}

	public void setEndorseCode(String endorseCode) {
		this.endorseCode = endorseCode;
	}
}
