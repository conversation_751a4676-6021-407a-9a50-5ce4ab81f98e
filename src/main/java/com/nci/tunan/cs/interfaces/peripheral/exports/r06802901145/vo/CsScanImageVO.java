package com.nci.tunan.cs.interfaces.peripheral.exports.r06802901145.vo;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class CsScanImageVO {
    /**
     * 应备资料类型
     */
    private String billcardCode;
    
    /**
     * 扫描审核状态
     */
    private String imageScanStatus;
    
    /**
     * 重扫原因
     */
    private String rescanCause;

    
    /**
     * 新扫描
     */
    private String scanOperationType;

    
    /**
     * 扫描时间(年月日，时分秒)
     */
    private String scanTime;
    
    /**
     * 扫描人员编码
     */
    private String scanUserCode;
    
    /**
     * 扫描人员类型
     */
    private String scanUserType;
    
    /**
     * 上载时间(年月日，时分秒)
     */
    private String uploadTime;
    
    /**
     * 影像具体信息
     */
    private List<ImagePageVO> imagePageVOs;

    @XmlElement(name = "billcardCode")
	public String getBillcardCode() {
		return billcardCode;
	}

	public void setBillcardCode(String billcardCode) {
		this.billcardCode = billcardCode;
	}

	@XmlElement(name = "imageScanStatus")
	public String getImageScanStatus() {
		return imageScanStatus;
	}

	public void setImageScanStatus(String imageScanStatus) {
		this.imageScanStatus = imageScanStatus;
	}

	@XmlElement(name = "rescanCause")
	public String getRescanCause() {
		return rescanCause;
	}

	public void setRescanCause(String rescanCause) {
		this.rescanCause = rescanCause;
	}

	@XmlElement(name = "scanOperationType")
	public String getScanOperationType() {
		return scanOperationType;
	}

	public void setScanOperationType(String scanOperationType) {
		this.scanOperationType = scanOperationType;
	}

	@XmlElement(name = "scanTime")
	public String getScanTime() {
		return scanTime;
	}

	public void setScanTime(String scanTime) {
		this.scanTime = scanTime;
	}

	@XmlElement(name = "scanUserCode")
	public String getScanUserCode() {
		return scanUserCode;
	}

	public void setScanUserCode(String scanUserCode) {
		this.scanUserCode = scanUserCode;
	}

	@XmlElement(name = "scanUserType")
	public String getScanUserType() {
		return scanUserType;
	}

	public void setScanUserType(String scanUserType) {
		this.scanUserType = scanUserType;
	}

	@XmlElement(name = "uploadTime")
	public String getUploadTime() {
		return uploadTime;
	}

	public void setUploadTime(String uploadTime) {
		this.uploadTime = uploadTime;
	}

	@XmlElement(name = "imagePageVOs")
	public List<ImagePageVO> getImagePageVOs() {
		return imagePageVOs;
	}

	public void setImagePageVOs(List<ImagePageVO> imagePageVOs) {
		this.imagePageVOs = imagePageVOs;
	}

    
    
    

    

}
