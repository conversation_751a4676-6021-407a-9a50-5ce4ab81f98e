package com.nci.tunan.cs.interfaces.peripheral.exports.r06802003148.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.nci.tunan.cs.interfaces.peripheral.exports.r06802003148.vo.InsuredInfoList;
public class InputData implements Serializable {

  private static final long serialVersionUID = 1L;

  private String contNo;
  private String oldRiskCode;
  private String newRiskCode;
  private String amnt;
  private String mult;
  private String rNewPlan;
  private String reNewFlag;
  /*private String insuredSocial;*///157477 放到社保状态列表
  private String appType;
  private String appCustomerName;
  // 转换前险种所属主险代码      101465增加
  private String oldMainRiskCode;
	// 是否包含可选责任      169801 增加
	private String includeLiabilityFlag;
	@XmlElement(name="includeLiabilityFlag")
	public String getIncludeLiabilityFlag() {
		return includeLiabilityFlag;
	}
	public void setIncludeLiabilityFlag(String includeLiabilityFlag) {
		this.includeLiabilityFlag = includeLiabilityFlag;
	}
	//被保人信息      169801 增加
	private List<InsuredInfoList> insuredInfoList;
	@XmlElement(name="insuredInfoList")
	public List<InsuredInfoList> getInsuredInfoList() {
		return insuredInfoList;
	}
	public void setInsuredInfoList(List<InsuredInfoList> insuredInfoList) {
		this.insuredInfoList = insuredInfoList;
	}
/**
  * 157477变更险种的险种ID 
  */
  private String busiItemId;
  /**
   * 157477被保人社保状态列表
   */
  private InsuredSocialInfoList insuredSocialInfoList;
  @XmlElement(name="InsuredSocialInfoList")
  public InsuredSocialInfoList getInsuredSocialInfoList() {
	return insuredSocialInfoList;
  }
  public void setInsuredSocialInfoList(InsuredSocialInfoList insuredSocialInfoList) {
	this.insuredSocialInfoList = insuredSocialInfoList;
  }
  @XmlElement(name="BusiItemId")
  public String getBusiItemId() {
	return busiItemId;
  }
  public void setBusiItemId(String busiItemId) {
	  this.busiItemId = busiItemId;
  }
@XmlElement(name="OldMainRiskCode")
  public String getOldMainRiskCode() {
	return oldMainRiskCode;
  }
  public void setOldMainRiskCode(String oldMainRiskCode) {
	this.oldMainRiskCode = oldMainRiskCode;
  }

  @XmlElement(name="ContNo")
  public String getContNo() {
	return contNo;
  }
  public void setContNo(String contNo) {
	this.contNo = contNo;
  }
  @XmlElement(name="OldRiskCode")
  public String getOldRiskCode() {
	return oldRiskCode;
  }
  public void setOldRiskCode(String oldRiskCode) {
	this.oldRiskCode = oldRiskCode;
  }
  @XmlElement(name="NewRiskCode")
  public String getNewRiskCode() {
	return newRiskCode;
  }
  public void setNewRiskCode(String newRiskCode) {
	this.newRiskCode = newRiskCode;
  }
  @XmlElement(name="Amnt")
  public String getAmnt() {
	return amnt;
  }
  public void setAmnt(String amnt) {
	this.amnt = amnt;
  }
  @XmlElement(name="Mult")
  public String getMult() {
	return mult;
  }
  public void setMult(String mult) {
	this.mult = mult;
  }
  @XmlElement(name="RNewPlan")
  public String getrNewPlan() {
	return rNewPlan;
  }
  public void setrNewPlan(String rNewPlan) {
	this.rNewPlan = rNewPlan;
  }
  @XmlElement(name="ReNewFlag")
  public String getReNewFlag() {
	return reNewFlag;
  }
  public void setReNewFlag(String reNewFlag) {
	this.reNewFlag = reNewFlag;
  }
  @XmlElement(name="AppType")
  public String getAppType() {
	return appType;
  }
  public void setAppType(String appType) {
	this.appType = appType;
  }
  @XmlElement(name="AppCustomerName")
  public String getAppCustomerName() {
	return appCustomerName;
  }
  public void setAppCustomerName(String appCustomerName) {
	  this.appCustomerName = appCustomerName;
  }
  
  
  
}
