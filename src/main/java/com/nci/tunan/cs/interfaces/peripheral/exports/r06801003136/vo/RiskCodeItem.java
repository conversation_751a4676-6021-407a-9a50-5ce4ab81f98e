package com.nci.tunan.cs.interfaces.peripheral.exports.r06801003136.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


@XmlType(name = "RiskCodeItem", propOrder = {
"riskCode",
"payform",
"standbyFlag1",
"amnt",
"prem",
"mult",
"insuYear",
"payIntv",
"payEndYear",
"insuYearFlag",
"newSLFlag",
"mainRiskCode",
"insuredCustomerId",//155289
"renew"
})
public class RiskCodeItem implements Serializable {

	private static final long serialVersionUID = 1L;
	// 险种代码
	private String riskCode;
	// 缴费形式
	private String payform;
	// 保障计划
	private String standbyFlag1;
	// 保额
	private String amnt;
	//保费
	private String prem;
	// 份数
	private String mult;
	// 保险期间
	private String insuYear;
	// 缴费方式
	private String payIntv;
	// 缴费期间
	private String payEndYear;
	// 保险期间单位
	private String insuYearFlag;
	//该附加险是否是本次业务需要双录
	private String newSLFlag;
	private String mainRiskCode;	 //主险代码
	private String renew;  // 是否续保
	//155289 被保人客户号
	private String insuredCustomerId;
	@XmlElement(name = "InsuredCustomerId")
	public String getInsuredCustomerId() {
		return insuredCustomerId;
	}
	public void setInsuredCustomerId(String insuredCustomerId) {
		this.insuredCustomerId = insuredCustomerId;
	}
	@XmlElement(name="MainRiskCode")
	public String getMainRiskCode() {
		return mainRiskCode;
	}
	public void setMainRiskCode(String mainRiskCode) {
		this.mainRiskCode = mainRiskCode;
	}
	@XmlElement(name="RiskCode")
	public String getRiskCode() {
		return riskCode;
	}
	public void setRiskCode(String riskCode) {
		this.riskCode = riskCode;
	}
	@XmlElement(name="Payform")
	public String getPayform() {
		return payform;
	}
	public void setPayform(String payform) {
		this.payform = payform;
	}
	
	@XmlElement(name="StandbyFlag1")
	public String getStandbyFlag1() {
		return standbyFlag1;
	}
	public void setStandbyFlag1(String standbyFlag1) {
		this.standbyFlag1 = standbyFlag1;
	}
	@XmlElement(name="Amnt")
	public String getAmnt() {
		return amnt;
	}
	public void setAmnt(String amnt) {
		this.amnt = amnt;
	}
	@XmlElement(name="Prem")
	public String getPrem() {
		return prem;
	}
	public void setPrem(String prem) {
		this.prem = prem;
	}
	@XmlElement(name="Mult")
	public String getMult() {
		return mult;
	}
	public void setMult(String mult) {
		this.mult = mult;
	}
	
	@XmlElement(name="InsuYear")
	public String getInsuYear() {
		return insuYear;
	}
	public void setInsuYear(String insuYear) {
		this.insuYear = insuYear;
	}
	
	@XmlElement(name="PayIntv")
	public String getPayIntv() {
		return payIntv;
	}
	public void setPayIntv(String payIntv) {
		this.payIntv = payIntv;
	}
	@XmlElement(name="PayEndYear")
	public String getPayEndYear() {
		return payEndYear;
	}
	public void setPayEndYear(String payEndYear) {
		this.payEndYear = payEndYear;
	}
	
	@XmlElement(name="InsuYearFlag")
	public String getInsuYearFlag() {
		return insuYearFlag;
	}
	public void setInsuYearFlag(String insuYearFlag) {
		this.insuYearFlag = insuYearFlag;
	}
	@XmlElement(name="NewSLFlag")
	public String getNewSLFlag() {
		return newSLFlag;
	}
	public void setNewSLFlag(String newSLFlag) {
		this.newSLFlag = newSLFlag;
	}
	@XmlElement(name="Renew")
	public String getRenew() {
		return renew;
	}

	public void setRenew(String renew) {
		this.renew = renew;
	}
}
