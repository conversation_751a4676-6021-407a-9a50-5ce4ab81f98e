package com.nci.tunan.cs.interfaces.peripheral.exports.r06802901077.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlType(name = "OutputData", propOrder = {"resultCode","resultMsg","refundsCount","riskPaydueList"})
public class OutputData implements Serializable{

	private static final long serialVersionUID = -417726414021315938L;

	//@invalid 处理结果 0：成功；1：失败；  试算成功为成功，否则为失败
	private String resultCode;
	
	//@invalid 处理结果描述 只有失败才返回提示语，以个险核心返回内容为准
	private String resultMsg;
	
	//@invalid 补退费金额
	private String refundsCount;
	//@invalid 险种应领未领红利信息列表
	private RiskPaydueList riskPaydueList;

	@XmlElement(name="refundsCount")
	public String getRefundsCount() {
		return refundsCount;
	}

	public void setRefundsCount(String refundsCount) {
		this.refundsCount = refundsCount;
	}

	@XmlElement(name="resultCode")
	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	@XmlElement(name="resultMsg")
	public String getResultMsg() {
		return resultMsg;
	}

	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}

	@XmlElement(name="riskPaydueList")
	public RiskPaydueList getRiskPaydueList() {
		return riskPaydueList;
	}

	public void setRiskPaydueList(RiskPaydueList riskPaydueList) {
		this.riskPaydueList = riskPaydueList;
	}

	@Override
	public String toString() {
		return "OutputData [resultCode=" + resultCode + ", resultMsg=" + resultMsg + ", riskPaydueList="
				+ riskPaydueList + "]";
	}
}
