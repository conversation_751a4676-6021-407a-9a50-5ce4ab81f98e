package com.nci.tunan.cs.interfaces.peripheral.exports.r06802003154.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class OutputData implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private List<Result> resultLists;

	@XmlElement(name = "Result")
	public List<Result> getResultLists() {
		return resultLists;
	}

	public void setResultLists(List<Result> resultLists) {
		this.resultLists = resultLists;
	}

	
}
