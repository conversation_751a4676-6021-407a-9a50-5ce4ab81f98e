package com.nci.tunan.cs.interfaces.model.vo;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description LoanBusiCfgLogVO对象
 * <AUTHOR>
 * @date 2024-12-16 19:42:46
 */
public class LoanBusiCfgLogVO extends BaseVO {
	private static final long serialVersionUID = -6774878719620288284L;
	/**
	 * @Fields operateFlag : t_operation_type
	 */
	private String operateFlag;
	/**
	 * @Fields remark : 备注
	 */
	private String remark;
	/**
	 * @Fields maxStdPremAf : 标准保费上限
	 */
	private BigDecimal maxStdPremAf;
	/**
	 * @Fields validateDateEnd : 保单生效日止期
	 */
	private Date validateDateEnd;
	/**
	 * @Fields chargeType : 缴费期间类型
	 */
	private BigDecimal chargeType;
	/**
	 * @Fields minStdPremAf : 标准保费下限
	 */
	private BigDecimal minStdPremAf;
	/**
	 * @Fields maxLoanRatio : 可贷比例
	 */
	private BigDecimal maxLoanRatio;
	/**
	 * @Fields isCarefullyChosen : 0-不是，1-是
	 */
	private String isCarefullyChosen;
	/**
	 * @Fields organCode : 机构代码
	 */
	private String organCode;
	/**
	 * @Fields channelType : 渠道
	 */
	private String channelType;
	/**
	 * @Fields validDate : 生效日期
	 */
	private Date validDate;
	/**
	 * @Fields validateDateStart : 保单生效日起期
	 */
	private Date validateDateStart;
	/**
	 * @Fields chargeYear : 交费年期
	 */
	private BigDecimal chargeYear;
	/**
	 * @Fields periodType : 保险期间
	 */
	private String periodType;
	/**
	 * @Fields businessProdId : 业务产品Id
	 */
	private BigDecimal businessProdId;
	/**
	 * @Fields minPaidPrem : 已缴保费下限
	 */
	private BigDecimal minPaidPrem;
	/**
	 * @Fields operaterId : null
	 */
	private BigDecimal operaterId;
	/**
	 * @Fields expiredDate : 失效日期
	 */
	private Date expiredDate;
	/**
	 * @Fields operaterOrganCode : null
	 */
	private String operaterOrganCode;
	/**
	 * @Fields loanFlag : 是否可以贷款标识
	 */
	private BigDecimal loanFlag;
	/**
	 * @Fields logId : null
	 */
	private BigDecimal logId;
	/**
	 * @Fields chargePeriod : 交费年期类型
	 */
	private String chargePeriod;
	/**
	 * @Fields maxPaidPrem : 已缴保费上限
	 */
	private BigDecimal maxPaidPrem;
	/**
	 * @Fields isFollowMaster : “贷款利率”和“贷款逾期利率”是否同主险
	 */
	private BigDecimal isFollowMaster;
	/**
	 * @Fields cfgId : 主键
	 */
	private BigDecimal cfgId;

	public void setOperateFlag(String operateFlag) {
		this.operateFlag = operateFlag;
	}

	public String getOperateFlag() {
		return operateFlag;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getRemark() {
		return remark;
	}

	public void setMaxStdPremAf(BigDecimal maxStdPremAf) {
		this.maxStdPremAf = maxStdPremAf;
	}

	public BigDecimal getMaxStdPremAf() {
		return maxStdPremAf;
	}

	public void setValidateDateEnd(Date validateDateEnd) {
		this.validateDateEnd = validateDateEnd;
	}

	public Date getValidateDateEnd() {
		return validateDateEnd;
	}

	public void setChargeType(BigDecimal chargeType) {
		this.chargeType = chargeType;
	}

	public BigDecimal getChargeType() {
		return chargeType;
	}

	public void setMinStdPremAf(BigDecimal minStdPremAf) {
		this.minStdPremAf = minStdPremAf;
	}

	public BigDecimal getMinStdPremAf() {
		return minStdPremAf;
	}

	public void setMaxLoanRatio(BigDecimal maxLoanRatio) {
		this.maxLoanRatio = maxLoanRatio;
	}

	public BigDecimal getMaxLoanRatio() {
		return maxLoanRatio;
	}

	public void setIsCarefullyChosen(String isCarefullyChosen) {
		this.isCarefullyChosen = isCarefullyChosen;
	}

	public String getIsCarefullyChosen() {
		return isCarefullyChosen;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setValidDate(Date validDate) {
		this.validDate = validDate;
	}

	public Date getValidDate() {
		return validDate;
	}

	public void setValidateDateStart(Date validateDateStart) {
		this.validateDateStart = validateDateStart;
	}

	public Date getValidateDateStart() {
		return validateDateStart;
	}

	public void setChargeYear(BigDecimal chargeYear) {
		this.chargeYear = chargeYear;
	}

	public BigDecimal getChargeYear() {
		return chargeYear;
	}

	public void setPeriodType(String periodType) {
		this.periodType = periodType;
	}

	public String getPeriodType() {
		return periodType;
	}

	public void setBusinessProdId(BigDecimal businessProdId) {
		this.businessProdId = businessProdId;
	}

	public BigDecimal getBusinessProdId() {
		return businessProdId;
	}

	public void setMinPaidPrem(BigDecimal minPaidPrem) {
		this.minPaidPrem = minPaidPrem;
	}

	public BigDecimal getMinPaidPrem() {
		return minPaidPrem;
	}

	public void setOperaterId(BigDecimal operaterId) {
		this.operaterId = operaterId;
	}

	public BigDecimal getOperaterId() {
		return operaterId;
	}

	public void setExpiredDate(Date expiredDate) {
		this.expiredDate = expiredDate;
	}

	public Date getExpiredDate() {
		return expiredDate;
	}

	public void setOperaterOrganCode(String operaterOrganCode) {
		this.operaterOrganCode = operaterOrganCode;
	}

	public String getOperaterOrganCode() {
		return operaterOrganCode;
	}

	public void setLoanFlag(BigDecimal loanFlag) {
		this.loanFlag = loanFlag;
	}

	public BigDecimal getLoanFlag() {
		return loanFlag;
	}

	public void setLogId(BigDecimal logId) {
		this.logId = logId;
	}

	public BigDecimal getLogId() {
		return logId;
	}

	public void setChargePeriod(String chargePeriod) {
		this.chargePeriod = chargePeriod;
	}

	public String getChargePeriod() {
		return chargePeriod;
	}

	public void setMaxPaidPrem(BigDecimal maxPaidPrem) {
		this.maxPaidPrem = maxPaidPrem;
	}

	public BigDecimal getMaxPaidPrem() {
		return maxPaidPrem;
	}

	public void setIsFollowMaster(BigDecimal isFollowMaster) {
		this.isFollowMaster = isFollowMaster;
	}

	public BigDecimal getIsFollowMaster() {
		return isFollowMaster;
	}

	public void setCfgId(BigDecimal cfgId) {
		this.cfgId = cfgId;
	}

	public BigDecimal getCfgId() {
		return cfgId;
	}

	@Override
	public String getBizId() {
		return null;
	}

	@Override
	public String toString() {
		return "LoanBusiCfgLogVO [" + "operateFlag=" + operateFlag + "," + "remark=" + remark + "," + "maxStdPremAf="
				+ maxStdPremAf + "," + "validateDateEnd=" + validateDateEnd + "," + "chargeType=" + chargeType + ","
				+ "minStdPremAf=" + minStdPremAf + "," + "maxLoanRatio=" + maxLoanRatio + "," + "isCarefullyChosen="
				+ isCarefullyChosen + "," + "organCode=" + organCode + "," + "channelType=" + channelType + ","
				+ "validDate=" + validDate + "," + "validateDateStart=" + validateDateStart + "," + "chargeYear="
				+ chargeYear + "," + "periodType=" + periodType + "," + "businessProdId=" + businessProdId + ","
				+ "minPaidPrem=" + minPaidPrem + "," + "operaterId=" + operaterId + "," + "expiredDate=" + expiredDate
				+ "," + "operaterOrganCode=" + operaterOrganCode + "," + "loanFlag=" + loanFlag + "," + "logId=" + logId
				+ "," + "chargePeriod=" + chargePeriod + "," + "maxPaidPrem=" + maxPaidPrem + "," + "isFollowMaster="
				+ isFollowMaster + "," + "cfgId=" + cfgId + "]";
	}
}
