package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000172.vo;

import javax.xml.bind.annotation.XmlElement;

public class RiskDutyList {
	// 保险险种号码
	private String polNo;
	// 责任编码
	private String dutyCode;
	// 给付责任编码
	private String getDutyCode;
	// 给付责任类型
	private String getDutyKind;
	// 变更后领取方式
	private String getIntv;
	// 变更前领取标准
	private String pGStandMoney;

	@XmlElement(name = "PolNo")
	public String getPolNo() {
		return polNo;
	}

	public void setPolNo(String polNo) {
		this.polNo = polNo;
	}

	@XmlElement(name = "DutyCode")
	public String getDutyCode() {
		return dutyCode;
	}

	public void setDutyCode(String dutyCode) {
		this.dutyCode = dutyCode;
	}

	@XmlElement(name = "GetDutyCode")
	public String getGetDutyCode() {
		return getDutyCode;
	}

	public void setGetDutyCode(String getDutyCode) {
		this.getDutyCode = getDutyCode;
	}

	@XmlElement(name = "GetDutyKind")
	public String getGetDutyKind() {
		return getDutyKind;
	}

	public void setGetDutyKind(String getDutyKind) {
		this.getDutyKind = getDutyKind;
	}

	@XmlElement(name = "GetIntv")
	public String getGetIntv() {
		return getIntv;
	}

	public void setGetIntv(String getIntv) {
		this.getIntv = getIntv;
	}

	@XmlElement(name = "PGStandMoney")
	public String getPGStandMoney() {
		return pGStandMoney;
	}

	public void setPGStandMoney(String pGStandMoney) {
		this.pGStandMoney = pGStandMoney;
	}
}
