package com.nci.tunan.cs.interfaces.peripheral.exports.r06802003104.isocialsecuritychangetrialseconducc.socialsecuritychangetrialsecond;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody")
public class SrvReqBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(name = "InputData")
    protected com.nci.tunan.cs.interfaces.peripheral.exports.r06802003104.vo.InputData inputData;
    
    public com.nci.tunan.cs.interfaces.peripheral.exports.r06802003104.vo.InputData getInputData() {
        return inputData;
    }
    public void setInputData(com.nci.tunan.cs.interfaces.peripheral.exports.r06802003104.vo.InputData inputData) {
        this.inputData = inputData;
    }
}


