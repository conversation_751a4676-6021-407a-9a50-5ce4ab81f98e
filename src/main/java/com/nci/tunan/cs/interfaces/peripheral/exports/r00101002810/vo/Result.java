package com.nci.tunan.cs.interfaces.peripheral.exports.r00101002810.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

/**
 * 分红类型判断接口
 * 服务编码:P00001002811
 * <AUTHOR>
 * 2018-8-31 11:05
 */
public class Result implements Serializable{

	//保单号
	private String contNO ;
	
	//红利类型
	private String bonusFlag;
	
	//红利类型名称
	private String bonusFlagName;
	
	//分红方式
	private String bonusType;
	
	//分红方式名称
	private String bonusTypeName;

	//责任组代码
	private String ProductCode;
		
	//责任组名称
	private String ProductName;
	@XmlElement(name="ProductCode")
	public String getProductCode() {
		return ProductCode;
	}

	public void setProductCode(String productCode) {
		ProductCode = productCode;
	}
	@XmlElement(name="ProductName")
	public String getProductName() {
		return ProductName;
	}

	public void setProductName(String productName) {
		ProductName = productName;
	}

	@XmlElement(name="ContNo")
	public String getContNO() {
		return contNO;
	}

	public void setContNO(String contNO) {
		this.contNO = contNO;
	}

	@XmlElement(name="BonusFlag")
	public String getBonusFlag() {
		return bonusFlag;
	}

	public void setBonusFlag(String bonusFlag) {
		this.bonusFlag = bonusFlag;
	}

	@XmlElement(name="BonusFlagName")
	public String getBonusFlagName() {
		return bonusFlagName;
	}

	public void setBonusFlagName(String bonusFlagName) {
		this.bonusFlagName = bonusFlagName;
	}

	@XmlElement(name="BonusType")
	public String getBonusType() {
		return bonusType;
	}

	public void setBonusType(String bonusType) {
		this.bonusType = bonusType;
	}

	@XmlElement(name="BonusTypeName")
	public String getBonusTypeName() {
		return bonusTypeName;
	}

	public void setBonusTypeName(String bonusTypeName) {
		this.bonusTypeName = bonusTypeName;
	}
	
	
	
}
