package com.nci.tunan.cs.interfaces.peripheral.exports.r06802900686.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class CheckInfoVo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 人脸对比验真人姓名
	 */
	private String checkName;

	/**
	 * 人脸对比验真人身份证号码
	 */
	private String checkIDNo;

	/**
	 * 人脸对比身份验真时间
	 */
	private String verifyDate;

	/**
	 * 相似度
	 */
	private String similar;

	@XmlElement(name = "CheckName")
	public String getCheckName() {
		return checkName;
	}

	public void setCheckName(String checkName) {
		this.checkName = checkName;
	}

	@XmlElement(name = "CheckIDNo")
	public String getCheckIDNo() {
		return checkIDNo;
	}

	public void setCheckIDNo(String checkIDNo) {
		this.checkIDNo = checkIDNo;
	}

	@XmlElement(name = "VerifyDate")
	public String getVerifyDate() {
		return verifyDate;
	}

	public void setVerifyDate(String verifyDate) {
		this.verifyDate = verifyDate;
	}

	@XmlElement(name = "Similar")
	public String getSimilar() {
		return similar;
	}

	public void setSimilar(String similar) {
		this.similar = similar;
	}

}
