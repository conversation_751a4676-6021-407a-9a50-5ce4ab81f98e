package com.nci.tunan.cs.interfaces.peripheral.exports.r00102000180.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

//附加险期满不续保（提交）
public class Result implements Serializable {
	// 操作结果码
	private String edorState;
	// 受理号
	private String edorAcceptNo;
	// 操作结果说明
	private String edorStateName;

	@XmlElement(name = "EdorState")
	public String getEdorState() {
		return edorState;
	}

	public void setEdorState(String edorState) {
		this.edorState = edorState;
	}

	@XmlElement(name = "EdorAcceptNo")
	public String getEdorAcceptNo() {
		return edorAcceptNo;
	}

	public void setEdorAcceptNo(String edorAcceptNo) {
		this.edorAcceptNo = edorAcceptNo;
	}

	@XmlElement(name = "EdorStateName")
	public String getEdorStateName() {
		return edorStateName;
	}

	public void setEdorStateName(String edorStateName) {
		this.edorStateName = edorStateName;
	}
}
