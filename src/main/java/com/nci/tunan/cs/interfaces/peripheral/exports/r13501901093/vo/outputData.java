package com.nci.tunan.cs.interfaces.peripheral.exports.r13501901093.vo;

import java.io.Serializable;

public class outputData implements Serializable{

	/**
	 * 序列化
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 查询成功标志
	 * 1-成功 2-失败
	 */
	private String resultCode;
	
	/**
	 * 查询成功或失败的提示语
	 */
	private String resultMsg;
	
	/**
	 * 保单号
	 */
	private String policyCode;
	
	/**
	 * 保单生效日期
	 */
	private String validateDate;
	
	/**
	 * 约定红利领取信息列表
	 */
	private bonusCollectionList bonusCollectionList;
	
	/**
	 * 分红信息列表
	 */
	private policyBonusInfoList policyBonusInfoList;

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getResultMsg() {
		return resultMsg;
	}

	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getValidateDate() {
		return validateDate;
	}

	public void setValidateDate(String validateDate) {
		this.validateDate = validateDate;
	}

	public bonusCollectionList getBonusCollectionList() {
		return bonusCollectionList;
	}

	public void setBonusCollectionList(bonusCollectionList bonusCollectionList) {
		this.bonusCollectionList = bonusCollectionList;
	}

	public policyBonusInfoList getPolicyBonusInfoList() {
		return policyBonusInfoList;
	}

	public void setPolicyBonusInfoList(policyBonusInfoList policyBonusInfoList) {
		this.policyBonusInfoList = policyBonusInfoList;
	}
	
	
	
}
