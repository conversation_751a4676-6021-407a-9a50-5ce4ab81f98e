package com.nci.tunan.cs.interfaces.peripheral.exports.csendorsecode.icsendorsecodeisexisteducc.isexistedcsendorsecode;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody")
public class SrvReqBizBody {

	/**
     * serialVersionUID 
     */
    @SuppressWarnings("unused")
	private static final long serialVersionUID = 1L;
    
	@XmlElement(name = "InputData")
	private com.nci.tunan.cs.interfaces.peripheral.exports.csendorsecode.vo.InputData inputData;

	public com.nci.tunan.cs.interfaces.peripheral.exports.csendorsecode.vo.InputData getInputData() {
		return inputData;
	}

	public void setInputData(
			com.nci.tunan.cs.interfaces.peripheral.exports.csendorsecode.vo.InputData inputData) {
		this.inputData = inputData;
	}
	
}
