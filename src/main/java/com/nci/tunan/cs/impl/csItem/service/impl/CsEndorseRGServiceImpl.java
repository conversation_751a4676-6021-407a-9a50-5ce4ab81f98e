package com.nci.tunan.cs.impl.csItem.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.nci.tunan.cs.calc.ICsCalcSAMService;
import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.dao.ICsContractInvestDao;
import com.nci.tunan.cs.dao.ICsFundServiceDao;
import com.nci.tunan.cs.dao.ICsInsuredListDao;
import com.nci.tunan.cs.dao.ICsPrecontPayDao;
import com.nci.tunan.cs.dao.IPolicyRetrieveDao;
import com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService;
import com.nci.tunan.cs.model.bo.BusinessProductBO;
import com.nci.tunan.cs.model.bo.CsAcceptChangeBO;
import com.nci.tunan.cs.model.bo.CsContractBusiProdBO;
import com.nci.tunan.cs.model.bo.CsContractInvestBO;
import com.nci.tunan.cs.model.bo.CsContractProductBO;
import com.nci.tunan.cs.model.bo.CsCustomerBO;
import com.nci.tunan.cs.model.bo.CsFundServiceBO;
import com.nci.tunan.cs.model.bo.CsInsuredListBO;
import com.nci.tunan.cs.model.bo.CsPayDueBO;
import com.nci.tunan.cs.model.bo.CsPayPlanBO;
import com.nci.tunan.cs.model.bo.CsPolicyAccountTransListBO;
import com.nci.tunan.cs.model.bo.CsPolicyChangeBO;
import com.nci.tunan.cs.model.bo.CsPremArapBO;
import com.nci.tunan.cs.model.bo.CsPremArapForQueryBO;
import com.nci.tunan.cs.model.bo.PolicyRetrieveBO;
import com.nci.tunan.cs.model.po.CsAcceptChangePO;
import com.nci.tunan.cs.model.po.CsApplicationPO;
import com.nci.tunan.cs.model.po.CsBenefitInsuredPO;
import com.nci.tunan.cs.model.po.CsBonusAllocatePO;
import com.nci.tunan.cs.model.po.CsContractBusiProdPO;
import com.nci.tunan.cs.model.po.CsContractInvestPO;
import com.nci.tunan.cs.model.po.CsContractMasterPO;
import com.nci.tunan.cs.model.po.CsContractProductPO;
import com.nci.tunan.cs.model.po.CsFundServicePO;
import com.nci.tunan.cs.model.po.CsInsuredListPO;
import com.nci.tunan.cs.model.po.CsPayDuePO;
import com.nci.tunan.cs.model.po.CsPayPlanPO;
import com.nci.tunan.cs.model.po.CsPolicyAccountPO;
import com.nci.tunan.cs.model.po.CsPolicyAccountTransListPO;
import com.nci.tunan.cs.model.po.CsPolicyChangePO;
import com.nci.tunan.cs.model.po.CsPolicyHolderPO;
import com.nci.tunan.cs.model.po.CsPrecontPayPO;
import com.nci.tunan.cs.model.po.CsPremArapPO;
import com.nci.tunan.cs.model.po.PolicyRetrievePO;
import com.nci.tunan.cs.model.pocomp.CsQueryParamPO;
import com.nci.tunan.cs.model.vo.CsEndorseRGPolicyVO;
import com.nci.tunan.cs.model.vo.CsEndorseRGVO;
import com.nci.tunan.mms.interfaces.query.exports.interestrate.vo.MmsInterestRateReqVO;
import com.nci.tunan.mms.interfaces.query.exports.interestrate.vo.MmsInterestRateVO;
import com.nci.tunan.pa.common.dao.IFormulaDao;
import com.nci.tunan.pa.dao.IContractInvestDao;
import com.nci.tunan.pa.dao.ICustomerDao;
import com.nci.tunan.pa.dao.IFundSettlementDao;
import com.nci.tunan.pa.dao.IPayDueDao;
import com.nci.tunan.pa.dao.IPolicyAccountTransListDao;
import com.nci.tunan.pa.dao.IPolicyChangeDao;
import com.nci.tunan.pa.dao.IPremArapDao;
import com.nci.tunan.pa.dao.ISurvivalDeductionTaskDao;
import com.nci.tunan.pa.imports.impl.PRDServiceImpl;
import com.nci.tunan.pa.interfaces.model.bo.CustomerBO;
import com.nci.tunan.pa.interfaces.model.bo.PremArapBO;
import com.nci.tunan.pa.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.pa.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.pa.interfaces.model.po.ContractProductPO;
import com.nci.tunan.pa.interfaces.model.po.CustomerPO;
import com.nci.tunan.pa.interfaces.model.po.FundSettlementPO;
import com.nci.tunan.pa.interfaces.model.po.FundTransPO;
import com.nci.tunan.pa.interfaces.model.po.PayDuePO;
import com.nci.tunan.pa.interfaces.model.po.PremArapPO;
import com.nci.tunan.pa.interfaces.model.po.SurvivalDeductionTaskPO;
import com.nci.tunan.pa.util.BatchDateUtil;
import com.nci.tunan.prd.interfaces.calc.exports.calcannutime.vo.PrdCalcAnnuTimeResVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;

/**
 * 
 * @description  生存保险金追回 Service实现类 
 * @<NAME_EMAIL> 
 * @date 2020-12-23 下午10:31:14 
 * @.belongToModule 保全
 */
public class CsEndorseRGServiceImpl extends CSItemBaseServiceImpl implements ICsEndorseRGService {
    /**计算生存金念经service*/
    @Autowired
    private ICsCalcSAMService csCalcSAMService;
    
    @Autowired
    private ICsPrecontPayDao csPrecontPayDao;
	/**
	 * policyRetrieveDao
	 */
	private IPolicyRetrieveDao policyRetrieveDao;
	/**Code Table of ARAP FlagDao*/
	private IPremArapDao premArapDao;
	/**客户表Dao*/
	private ICustomerDao customerDao;
	/**保单被保人列表Dao*/
	private ICsInsuredListDao csInsuredListDao;
	/**万能保单结算记录表Dao*/
	private IFundSettlementDao fundSettlementDao;
	/**
	 * formulaDao
	 */
	private IFormulaDao formulaDao;

	
	/**生存给付应领表Dao*/
	private IPayDueDao payDueDao;
	
	/**保单投资连结表Dao*/
	private ICsContractInvestDao csContractInvestDao; 
	/**保单投资连结表Dao*/
	private IContractInvestDao contractInvestDao;
	
	/**业务项目定义表Dao*/
	private ICsFundServiceDao csFundServiceDao;
	/**保单账户交易详细记录表Dao*/
	private IPolicyAccountTransListDao policyAccountTransListDao;
	/**保单变更履历表Dao*/
	private IPolicyChangeDao policyChangeDao;
	  /**
     * 产品工厂接口类
     */
    private PRDServiceImpl prdIAS;
    /**
     * @Fields 累计声息账户注销--注销
     */
    public static final BigDecimal POLICY_ACCOUNT_STATUS = new BigDecimal(2);
    /**
	 * 年金/生存金/满期金抵扣任务dao
	 */
	@Autowired
	private ISurvivalDeductionTaskDao survivalDeductionTaskDao;
    
    /**
     * 
     * @description  点击上一步  删除保存信息
     * @version V1.0.0
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.tunan.cs.impl.csItem.service.impl.CSItemBaseServiceImpl#clearChangeInfo(java.math.BigDecimal)
     * @param param   acceptId
     * @return
     */
    @Override
    public boolean clearChangeInfo(BigDecimal acceptId) {
        if (acceptId == null) {
            return false;
        }
        CsAcceptChangePO csAcceptChangePO = new  CsAcceptChangePO();
        csAcceptChangePO.setAcceptId(acceptId);
        CsAcceptChangePO findCsAcceptChange = this.csAcceptChangeDao.findCsAcceptChange(csAcceptChangePO);
        //1.删除追回信息表
        PolicyRetrievePO policyRetrievePO = new PolicyRetrievePO();
        policyRetrievePO.setAcceptCode(findCsAcceptChange.getAcceptCode());
        List<PolicyRetrievePO> findAllPolicyRetrieve = policyRetrieveDao.findAllPolicyRetrieve(policyRetrievePO);
        for(PolicyRetrievePO policyRetrieve:findAllPolicyRetrieve){
        	 this.policyRetrieveDao.deletePolicyRetrieve(policyRetrieve);
        }
       
        //2.删除应收应付信息cs_prem_arap 
        CsPremArapPO csPremArapPO = new CsPremArapPO();
        csPremArapPO.setBusinessCode(findCsAcceptChange.getAcceptCode());
        List<CsPremArapPO> findAllCsPremArap = csPremArapDao.findAllCsPremArap(csPremArapPO);
        for(CsPremArapPO csPremArap:findAllCsPremArap){
        	  this.csPremArapDao.deleteCsPremArap(csPremArap);
        }
        this.clearChange(acceptId);
		return true;
    }
	/**
	 * 清除变更信息
	 * 
	 * @return
	 */
	@Override
	public void clearChange(BigDecimal acceptId) {
		// 查询保单变更主表
		CsAcceptChangeBO changeBO = new CsAcceptChangeBO();
		changeBO.setAcceptId(acceptId);
		List<CsPolicyChangeBO> csPolicyChangeBOs = this.findPolicyChangeList(changeBO);
		for (CsPolicyChangeBO csPolicyChangeBO : csPolicyChangeBOs) {
			// 还原t_cs_pay_plan表
			CsPayPlanPO csPayPlanNew = new CsPayPlanPO();
			csPayPlanNew.setChangeId(csPolicyChangeBO.getChangeId());
			csPayPlanNew.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
			csPayPlanNew.setOldNew(Constants.NEW);
			List<CsPayPlanPO> csPayPlanPONews = csPayPlanDao.findAllCsPayPlan(csPayPlanNew);
			for (CsPayPlanPO fePayPlanNew : csPayPlanPONews) {
				CsPayPlanPO csPayPlanOld = new CsPayPlanPO();
				csPayPlanOld.setChangeId(fePayPlanNew.getChangeId());
				csPayPlanOld.setPolicyChgId(fePayPlanNew.getPolicyChgId());
				csPayPlanOld.setBusiItemId(fePayPlanNew.getBusiItemId());
				csPayPlanOld.setOldNew(Constants.OLD);
				csPayPlanOld.setPlanId(fePayPlanNew.getPlanId());
				csPayPlanOld = csPayPlanDao.findCsPayPlan(csPayPlanOld);
				if (csPayPlanOld == null || csPayPlanOld.getLogId() == null) {
					csPayPlanDao.deleteCsPayPlan(fePayPlanNew);
				} else {
					csPayPlanOld.setLogId(fePayPlanNew.getLogId());
					csPayPlanOld.setOldNew(Constants.NEW);
					csPayPlanDao.updateCsPayPlan(csPayPlanOld);
				}
			}
			// 还原t_cs_pay_due表
			CsPayDuePO csPayDueNew = new CsPayDuePO();
			csPayDueNew.setChangeId(csPolicyChangeBO.getChangeId());
			csPayDueNew.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
			csPayDueNew.setOldNew(Constants.NEW);
			List<CsPayDuePO> csPayDuePONews = csPayDueDao.findAllCsPayDue(csPayDueNew);
			for (CsPayDuePO fePayDueNew : csPayDuePONews) {
				CsPayDuePO csPayDueOld = new CsPayDuePO();
				csPayDueOld.setChangeId(fePayDueNew.getChangeId());
				csPayDueOld.setPolicyChgId(fePayDueNew.getPolicyChgId());
				csPayDueOld.setOldNew(Constants.OLD);
				csPayDueOld.setPayId(fePayDueNew.getPayId());
				csPayDueOld = csPayDueDao.findCsPayDue(csPayDueOld);
				if (csPayDueOld == null || csPayDueOld.getLogId() == null) {
					csPayDueDao.deleteCsPayDue(fePayDueNew);
				} else {
					csPayDueOld.setLogId(fePayDueNew.getLogId());
					csPayDueOld.setOldNew(Constants.NEW);
					csPayDueDao.updateCsPayDue(csPayDueOld);
				}
			}
		}
	}

    
    /**
     * 
     * @description  计算追回金额  应收金额
     * @version V1.0.0 
     * @title
     * @<NAME_EMAIL>
     * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findRGFeeAmount(java.util.List, com.nci.tunan.cs.model.vo.CsEndorseRGPolicyVO, java.util.Date, java.math.BigDecimal)
     * @param param  param findCsPayDueBOList 生存给付应领表BO
     * @param param  param csEndorseRGPolicyVO
     * @param param  param applyTime
     * @param param  param customerId
     * @return
     */
	@Override
	public CsEndorseRGVO findRGFeeAmount(List<CsPayDueBO> findCsPayDueBOList,
			CsEndorseRGPolicyVO csEndorseRGPolicyVO,Date applyTime,BigDecimal customerId) {
//	    clearChange()方法已还原上次保存的信息，此处无需再次还原
//	    //1.再次保存，还原账户信息
//	    CsAcceptChangePO csAcceptChange = new CsAcceptChangePO();
//        csAcceptChange.setChangeId(csEndorseRGPolicyVO.getChangeId());
//        //2.查询受理变更信息
//        List<CsAcceptChangePO> findAllCsAcceptChange = csAcceptChangeDao.findAllCsAcceptChange(csAcceptChange);
//        if(findAllCsAcceptChange.get(0).getIsSaved()!=null){
//            recoverMsg(csEndorseRGPolicyVO.getChangeId(),csEndorseRGPolicyVO.getPolicyChgId());
//        }
        
	    BigDecimal retriveTotalAmount = new BigDecimal(0);//@invalid  追回金额合计
        BigDecimal premArapFeeAmount = new BigDecimal(0);//@invalid  应收金额合计
		CsEndorseRGVO csEndorseRGInfoVO = new CsEndorseRGVO();
		for(CsPayDueBO csPayDue : findCsPayDueBOList){
		    csEndorseRGInfoVO = this.caculateRGFeeAmount(csPayDue,csEndorseRGPolicyVO,applyTime,customerId,Constants.NEW);
			if (Constants.SERVICE_CODE__AG.equals(csEndorseRGInfoVO.getServiceCode())) {
				return csEndorseRGInfoVO;
			}
		    if(csEndorseRGInfoVO.getRetriveFee()!=null){
			    retriveTotalAmount = retriveTotalAmount.add(csEndorseRGInfoVO.getRetriveFee());
		    }
		    if(csEndorseRGInfoVO.getArapFee() !=null ){
			    premArapFeeAmount = premArapFeeAmount.add(csEndorseRGInfoVO.getArapFee());
		    }
		    
		    //查询在此笔生存金发放后是否根据约定的计划领取频率信息更新了领取频率、领取标准
		    this.saveRGForCsRepcontPay(csPayDue);		    
		    
		}
		if(retriveTotalAmount.compareTo(Constants.LING) != 0 || premArapFeeAmount.compareTo(Constants.LING) != 0 ){
			/**
			 * @invalid  Redmin:47068   
			 *  修改原因：入追回包含现金红利，需要将责任组现金红利最后一次的发放日期还原
			 */
			CsContractProductBO csContractProductBO = new CsContractProductBO();
			csContractProductBO.setPolicyChgId(csEndorseRGPolicyVO.getPolicyChgId());
			csContractProductBO.setOldNew(Constants.NEW);
			//3.查询保单险种责任组信息t_cs_contract_product
			List<CsContractProductBO> csContractProductBOs = this.findAllCsContractProducts(csContractProductBO);
			if(CollectionUtils.isNotEmpty(csContractProductBOs)){
				for (CsContractProductBO csContractProductBO2 : csContractProductBOs) {
					boolean isUnpdate = false;
					List<CsBonusAllocatePO> bonusAllocateList = new ArrayList<CsBonusAllocatePO>();
					CsBonusAllocatePO csBonusAllocatePO = new CsBonusAllocatePO();
					csBonusAllocatePO.setPolicyChgId(csContractProductBO2.getPolicyChgId());
					csBonusAllocatePO.setBusiItemId(csContractProductBO2.getBusiItemId());
					csBonusAllocatePO.setItemId(csContractProductBO2.getItemId());
					csBonusAllocatePO.setOldNew(Constants.NEW);
					List<CsBonusAllocatePO> csBonusAllocateList = csBonusAllocateDao.findAllCsBonusAllocate(csBonusAllocatePO);
					for (CsBonusAllocatePO csBonusAllocatePO2 : csBonusAllocateList) {
						if(Constants.OPERATIONTYPE_UPDATE.equals(csBonusAllocatePO2.getOperationType())){
							isUnpdate = true;
						}else if(!Constants.OPERATIONTYPE_UPDATE.equals(csBonusAllocatePO2.getOperationType())
								&& csBonusAllocatePO2.getAllocateDate().before(csEndorseRGPolicyVO.getReitrieveDate())){
							bonusAllocateList.add(csBonusAllocatePO2);
						}
					}
					if(CollectionUtils.isNotEmpty(bonusAllocateList) && isUnpdate){
						//3.1 根据红利发放时间排序红利记录，去追回前最后一次的发放时间为责任组最后一次发放时间
						Date  lastBonusAllocate = sortCsBonusAllocate(bonusAllocateList);
						csContractProductBO2.setLastBonusDate(lastBonusAllocate);
						csContractProductBO2.setOperationType(Constants.OPERATIONTYPE_UPDATE);
					}else if(CollectionUtils.isEmpty(bonusAllocateList) && isUnpdate){
						//3.2如果追回的为第一次现金红利，则赋值为空
						csContractProductBO2.setLastBonusDate(null);
						csContractProductBO2.setOperationType(Constants.OPERATIONTYPE_UPDATE);
					}
					if(Constants.OPERATIONTYPE_UPDATE.equals(csContractProductBO2.getOperationType())){
						//4.更新保单险种责任组信息
						csContractProductDao.updateCsContractProduct(BeanUtils.copyProperties(CsContractProductPO.class, csContractProductBO2));
					}
					
				}
			}
		}
		
		csEndorseRGInfoVO.setRetriveFee(retriveTotalAmount);
		csEndorseRGInfoVO.setArapFee(premArapFeeAmount);
		return csEndorseRGInfoVO;
	}
	
	
	/**
	 * 
	 * @description  recoverMsg
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param changeId changeId
	 * @param policyChgId policyChgId
	 */
    private void recoverMsg(BigDecimal changeId, BigDecimal policyChgId) {

        CsContractInvestPO csContractInvest = new CsContractInvestPO();
        csContractInvest.setInvestAccountType(Constants.INVEST_ACCOUNT_TYPE_2);// @invalid 915
        csContractInvest.setPolicyChgId(policyChgId);
        csContractInvest.setOldNew(Constants.NEW);
        List<CsContractInvestPO> csContractInvestPOs = csContractInvestDao.findAllCsContractInvest(csContractInvest);

        if (CollectionUtilEx.isNotEmpty(csContractInvestPOs)) {
            for (CsContractInvestPO csNewConInvestPO : csContractInvestPOs) {
                CsContractInvestPO oldCsConInvestPO = new CsContractInvestPO();
                oldCsConInvestPO.setPolicyChgId(policyChgId);
                oldCsConInvestPO.setBusiItemId(csNewConInvestPO.getBusiItemId());
                oldCsConInvestPO.setListId(csNewConInvestPO.getListId());
                oldCsConInvestPO.setOldNew(Constants.OLD);
                CsContractInvestPO oldCsContractInvest = this.csContractInvestDao.findCsContractInvest(oldCsConInvestPO);

                oldCsContractInvest.setOldNew(Constants.NEW);
                oldCsContractInvest.setLogId(csNewConInvestPO.getLogId());
                this.csContractInvestDao.updateCsContractInvest(oldCsContractInvest);

                CsFundServicePO csFundService = new CsFundServicePO();
                csFundService.setChangeId(changeId);
                csFundService.setPolicyChgId(policyChgId);
                csFundService.setPolicyId(csNewConInvestPO.getPolicyId());
                csFundService.setBusiItemId(csNewConInvestPO.getBusiItemId());
                csFundService.setAccountCode(csNewConInvestPO.getAccountCode());
                // 2.查询基金定义表Service信息
                List<CsFundServicePO> findAllFundService = this.csFundServiceDao.findAllFundService(csFundService);
                for (CsFundServicePO csFundServicePO : findAllFundService) {
                    // 3.循环遍历并删除指定基金定义表Service信息
                    this.csFundServiceDao.deleteFundService(csFundServicePO);
                }
            }
        }

    }

    /***
	 * 
	 * @description  计算追回金额
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL>
	 * @param param  param csPayDue
	 * @param param  param oldNew   是否计算利息  提供给退保使用
	 * @param param  param customerId 
	 * @param param  param applyTime 
	 * @param param  param csEndorseRGPolicyVO 
	 * @return
	 */
	private CsEndorseRGVO caculateRGFeeAmount(CsPayDueBO csPayDue, CsEndorseRGPolicyVO csEndorseRGPolicyVO, Date applyTime, BigDecimal customerId, String oldNew) {
	    
	    CsEndorseRGVO csEndorseRGInfoVO = new CsEndorseRGVO();
	    BigDecimal retriveTotalAmount = new BigDecimal(0);//@invalid  追回金额合计
        BigDecimal premArapFeeAmount = new BigDecimal(0);//@invalid  应收金额合计
	    
        /**@invalid 判断是否做过追回   SurvivalType=02 已经做过追回  不可以再次追回*/
        if(csPayDue.getSurvivalType()!=null&&csPayDue.getSurvivalType().compareTo(new BigDecimal(2))==0){
            return csEndorseRGInfoVO;
        }
        
        if(csPayDue.getUnitNumber()==null){
            return csEndorseRGInfoVO;
        }
        //1.查找应收应付信息
        // TC29816存在一些unitnumber转存，在t表查询不到 需要查询视图V_PREM_ARAP_ALL
        PremArapPO premArapBO = new PremArapPO();
        premArapBO.setArapFlag(Constants.ARAP_FlAG_OUT);
        premArapBO.setUnitNumber(csPayDue.getUnitNumber());
        List<PremArapPO> findAllPremArap = this.premArapDao.findAllVPremArapAll(premArapBO);
        if(findAllPremArap.size()==0){
        	logger.info("CsEndorseRGServiceImpl.caculateRGFeeAmount==UnitNumber={}未查询到不做处理",csPayDue.getUnitNumber());
            return csEndorseRGInfoVO;
        }
        
        //2.查询生存金给付计划
        CsPayPlanPO csPayPlanPO = new CsPayPlanPO();
        csPayPlanPO.setPlanId(csPayDue.getPlanId());
        csPayPlanPO.setChangeId(csPayDue.getChangeId());
        csPayPlanPO.setOldNew(Constants.NEW);
        csPayPlanPO.setPolicyChgId(csEndorseRGPolicyVO.getPolicyChgId());
        CsPayPlanPO csPayPlanBO = csPayPlanDao.findCsPayPlan(csPayPlanPO);
        
        if(csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_1)){
            findAllPremArap = this.sorPremArapList(findAllPremArap);
        }

        for(PremArapPO premArap:findAllPremArap){
            //3.通过ag发放  或者银行转账在途阻断
            if(premArap.getServiceCode()!=null&&premArap.getServiceCode().equals("AG")){
                csEndorseRGInfoVO.setServiceCode(premArap.getServiceCode());
                return csEndorseRGInfoVO;
            }
            if(premArap.getDerivType()!=null&&premArap.getDerivType().equals("004")){
                csEndorseRGInfoVO.setDerivType(premArap.getDerivType());
                return csEndorseRGInfoVO;
            }
            if(csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_4)){
            	continue;
            }
            retriveTotalAmount = retriveTotalAmount.add(premArap.getFeeAmount());//@invalid  追回金额合计
            
            /**
             * @invalid 查询追回费用
             */
            csEndorseRGPolicyVO = findRetriveFee(premArap,csEndorseRGPolicyVO,applyTime,customerId,csPayDue,csPayPlanBO,oldNew);
            
            if(csEndorseRGPolicyVO.getAmountLi()!=null){
                retriveTotalAmount = retriveTotalAmount.add(csEndorseRGPolicyVO.getAmountLi());//@invalid 追回金额
            }
            if(csEndorseRGPolicyVO.getPremArapFeeAmount()!=null){
                premArapFeeAmount = premArapFeeAmount.add(csEndorseRGPolicyVO.getPremArapFeeAmount());//@invalid 应收金额
            }
        }
        //4.返回参数信息
        csEndorseRGInfoVO.setArapFee(premArapFeeAmount); //@invalid  应收金额合计
        csEndorseRGInfoVO.setRetriveFee(retriveTotalAmount); //@invalid  追回金额合计
        return csEndorseRGInfoVO;
    }



	/**
	 * 
	 * @description 对现金红利进行排序  
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param findAllPremArap findAllPremArap
	 * @return 
	 */
	private List<PremArapPO> sorPremArapList(List<PremArapPO> findAllPremArap) {
	    List<PremArapPO> sortList = sortList(findAllPremArap);
        return sortList;
    }
	
	/**
	 * map
	 */
	static Map<String,Integer> map = new HashMap<String,Integer>();
    {
        map.put("P004620100", 1);
        map.put("P004630100", 2);
        map.put("P004620200", 3);
        map.put("P004630200", 4);
        map.put("P004620000", 5);
        map.put("P004630000", 6);
    }
    
    /**
     * 
     * @description sortList
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param lstFeeAmountByUNBO lstFeeAmountByUNBO
     * @return
     */
    private List<PremArapPO> sortList(List<PremArapPO> lstFeeAmountByUNBO) {
        
        
        Collections.sort(lstFeeAmountByUNBO, new Comparator<PremArapPO>() {
            @Override
            public int compare(PremArapPO bo1, PremArapPO bo2) {
                return map.get(bo2.getFeeType()).compareTo(map.get(bo1.getFeeType()));
            }
        });
       
      
        return lstFeeAmountByUNBO;
    }

    /**
	 * 
	 * @description    查询追回金额  专供回退 退保   (累计声息账户不计算利息    新增一个参数进行判断是否计算利息)
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findRGFeeAmount(java.util.List)
	 * @param    findCsPayDueBOList 生存给付应领表BO
	 */
    public List<CsEndorseRGPolicyVO> findFeeAmount(List<CsPayDueBO> findCsPayDueBOList,String isFlag){
    	if(findCsPayDueBOList.size()==0){
    		return null;
    	}
    	CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
		csPolicyChangePO.setChangeId(findCsPayDueBOList.get(0).getChangeId());
		//1.查询保单变更管理表信息
		CsPolicyChangePO findCsPolicyChange = this.csPolicyChangeDao.findCsPolicyChange(csPolicyChangePO);
		
		CsApplicationPO csApplicationPO = new CsApplicationPO();
		csApplicationPO.setChangeId(findCsPolicyChange.getChangeId());
		//2.查询保全申请信息表信息
		CsApplicationPO findCsApplication = this.csApplicationDao.findCsApplication(csApplicationPO);
		
		
		List<CsEndorseRGPolicyVO> csEndorseRGPolicyVOList = new ArrayList<CsEndorseRGPolicyVO>();
		
		for(CsPayDueBO csPayDue : findCsPayDueBOList){
			CsEndorseRGPolicyVO csEndorseRGPolicyVO = new CsEndorseRGPolicyVO();
			csEndorseRGPolicyVO.setChangeId(findCsPolicyChange.getChangeId());
			csEndorseRGPolicyVO.setPolicyChgId(findCsPolicyChange.getPolicyChgId());
			csEndorseRGPolicyVO.setAcceptId(findCsPolicyChange.getAcceptId());
			
			 /** @invalid 判断是否做过追回   SurvivalType=02 已经做过追回  不可以再次追回 */
	        if(csPayDue.getSurvivalType()!=null&&csPayDue.getSurvivalType().compareTo(new BigDecimal(2))==0){
	            continue;
	        }
	        
	        if(csPayDue.getUnitNumber()==null){
	        	continue;
	        }
	        //3.查找应收应付信息
	        PremArapPO premArapBO = new PremArapPO();
	        premArapBO.setArapFlag(Constants.ARAP_FlAG_OUT);
	        premArapBO.setUnitNumber(csPayDue.getUnitNumber());
	        List<PremArapPO> findAllPremArap = this.premArapDao.findAllPremArap(premArapBO);
	        if(findAllPremArap.size()==0){
	        	continue;
	        }
	        
	        //4.查询生存金给付计划
	        CsPayPlanPO csPayPlanPO = new CsPayPlanPO();
	        csPayPlanPO.setPlanId(csPayDue.getPlanId());
	        csPayPlanPO.setChangeId(csPayDue.getChangeId());
	        csPayPlanPO.setOldNew(Constants.NEW);
	        CsPayPlanPO csPayPlanBO = csPayPlanDao.findCsPayPlan(csPayPlanPO);
	        
	        if(csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_1)){
	            findAllPremArap = this.sorPremArapList(findAllPremArap);
	        }
	        
	        for(PremArapPO premArap:findAllPremArap){
	            //@invalid 通过ag发放  或者银行转账在途阻断
	            if(premArap.getServiceCode()!=null&&premArap.getServiceCode().equals("AG")){
	               continue;
	            }
	            if(premArap.getDerivType()!=null&&premArap.getFeeStatus().equals("004")){
	                continue;
	            }
	      
	  	            
	            /**
	             * @invalid 查询追回费用
	             */
	            CsEndorseRGPolicyVO csEndorseRGPolicy = findRetriveFee(premArap,csEndorseRGPolicyVO,findCsPolicyChange.getApplyTime(),
	            		findCsApplication.getCustomerId(),csPayDue,csPayPlanBO,isFlag);
	            
	            csEndorseRGPolicy.setAmount(premArap.getFeeAmount());//@invalid 追回总金额	            
	            csEndorseRGPolicy.setItemId(csPayPlanBO.getItemId());//@invalid 责任组
	            csEndorseRGPolicy.setBusiItemId(csPayPlanBO.getBusiItemId());//@invalid 险种id
	            csEndorseRGPolicy.setPolicyCode(csPayPlanBO.getPolicyCode());//@invalid 保单号
	            if(csEndorseRGPolicy.getNotPremFee()==null){
	            	csEndorseRGPolicy.setNotPremFee(Constants.LING);//@invalid 不可收付金额   记账
	            }
	            if(csEndorseRGPolicy.getPremArapFeeAmount()==null){
	            	csEndorseRGPolicy.setPremArapFeeAmount(Constants.LING);//@invalid 应收金额  追回的金额
	            }
	            csEndorseRGPolicy.setPayPlanType(csPayPlanBO.getPayPlanType());//@invalid 类型　
	            csEndorseRGPolicy.setFeeStatus(premArap.getFeeStatus());//@invalid 状态
	            csEndorseRGPolicy.setSurvivalMode(csPayDue.getSurvivalMode());//@invalid 发放方式
	            csEndorseRGPolicy.setPayNum(csPayDue.getPayNum());//@invalid 发放期数
	            csEndorseRGPolicy.setUnitNumber(premArap.getUnitNumber());
	            csEndorseRGPolicyVOList.add(csEndorseRGPolicy);	            
	        }
		}
		return csEndorseRGPolicyVOList;
    }
    
    
    /**
	 * 
	 * @description    查询追回金额  专供回退 退保   (累计声息账户不计算利息    新增一个参数进行判断是否计算利息)
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findRGFeeAmount(java.util.List)
	 * @param    findCsPayDueBOList 生存给付应领表BO
	 */
	@Override
	public void findRGFeeAmount(List<CsPayDueBO> findCsPayDueBOList){
		
		CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
		csPolicyChangePO.setChangeId(findCsPayDueBOList.get(0).getChangeId());
		//1.查询保单变更管理表信息
		CsPolicyChangePO findCsPolicyChange = this.csPolicyChangeDao.findCsPolicyChange(csPolicyChangePO);
		
		CsEndorseRGPolicyVO csEndorseRGPolicyVO = new CsEndorseRGPolicyVO();
		csEndorseRGPolicyVO.setChangeId(findCsPolicyChange.getChangeId());
		csEndorseRGPolicyVO.setPolicyChgId(findCsPolicyChange.getPolicyChgId());
		csEndorseRGPolicyVO.setAcceptId(findCsPolicyChange.getAcceptId());
		
		CsApplicationPO csApplicationPO = new CsApplicationPO();
		csApplicationPO.setChangeId(findCsPolicyChange.getChangeId());
		//2.查询保全申请信息表信息
		CsApplicationPO findCsApplication = this.csApplicationDao.findCsApplication(csApplicationPO);
		
		for(CsPayDueBO csPayDue : findCsPayDueBOList){
			caculateRGFeeAmount(csPayDue,csEndorseRGPolicyVO,findCsPolicyChange.getApplyTime(),findCsApplication.getCustomerId(),Constants.OLD);
		}
	}
	


	/**
	 * 
	 * @description 保存生存保险金追回信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#savePolicyRetrive(java.util.List)
	 * @param policyRetrieveBOs olicyRetrieveBOs
	 * @return
	 */
	@Override
	public boolean savePolicyRetrive(List<PolicyRetrieveBO> policyRetrieveBOs){
		List<PolicyRetrievePO> policyRetrievePOs = BeanUtils.copyList(PolicyRetrievePO.class, policyRetrieveBOs);
		boolean result = policyRetrieveDao.batchSavePolicyRetrieve(policyRetrievePOs);
		return result;
	}
	
	/**
	 * 
	 * @description 删除生存保险金追回信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#deletePolicyRetrive(com.nci.tunan.cs.model.bo.PolicyRetrieveBO)
	 * @param policyRetrieveBO policyRetrieveBO
	 * @return
	 */
	@Override
	public boolean deletePolicyRetrive(PolicyRetrieveBO policyRetrieveBO){
		PolicyRetrievePO policyRetrievePO = BeanUtils.copyProperties(PolicyRetrievePO.class, policyRetrieveBO);
		List<PolicyRetrievePO> policyRetrievePOs = policyRetrieveDao.findAllPolicyRetrieve(policyRetrievePO);
		if (policyRetrievePOs == null || policyRetrievePOs.size() == 0) {
			return true;
		}
		policyRetrieveDao.batchDeletePolicyRetrieve(policyRetrievePOs);
		return true;
	}

	/**
	 * 
	 * @description  查询保单所有险种信息
	 * @version 
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findContractBusiProd(com.nci.tunan.cs.model.bo.CsContractBusiProdBO)
	 * @param csContractBusiProdBO csContractBusiProdBO
	 * @return
	 */
	@Override
	public List<CsContractBusiProdBO> findContractBusiProd(
			CsContractBusiProdBO csContractBusiProdBO) {
		List<CsContractBusiProdPO> csContractBusiProdPOs= csContractBusiProdDao.findAllCsContractBusiProd(BeanUtils.copyProperties(CsContractBusiProdPO.class, csContractBusiProdBO));
		return BeanUtils.copyList(CsContractBusiProdBO.class, csContractBusiProdPOs);
	}

	

	/**
	 * 
	 * @description 查找收付费数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findRetreivePremArap(com.nci.tunan.pa.interfaces.model.bo.PremArapBO)
	 * @param premArapBO premArapBO
	 * @return
	 */
	@Override
	public List<PremArapBO> findRetreivePremArap(PremArapBO premArapBO) {
		PremArapPO premArapPO = BeanUtils.copyProperties(PremArapPO.class, premArapBO);
		List<PremArapPO> premArapPOs = premArapDao.findRetreivePremArap(premArapPO);
		List<PremArapBO> premArapBOs = BeanUtils.copyList(PremArapBO.class, premArapPOs);
		return premArapBOs;	
	}
	
	/**
	 * 
	 * @description 查找收付费数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findRetreivePremArapForPage(com.nci.tunan.pa.interfaces.model.bo.PremArapBO, com.nci.udmp.framework.model.CurrentPage)
	 * @param premArapBO  PremArapBO应收应付BO对象
	 * @param currentPageBO 分页信息
	 * @return
	 */
	@Override
	public CurrentPage<PremArapBO> findRetreivePremArapForPage(PremArapBO premArapBO,
			CurrentPage<PremArapBO> currentPageBO) {
		PremArapPO premArapPO = BeanUtils.copyProperties(PremArapPO.class, premArapBO);
		CurrentPage<PremArapPO> currentPagePO = BeanUtils.copyCurrentPage(PremArapPO.class, currentPageBO);
		currentPagePO = premArapDao.findRetreivePremArapForPage(premArapPO, currentPagePO);
		return BeanUtils.copyCurrentPage(PremArapBO.class, currentPagePO);
	}


	

	
	/**
	 * 
	 * @description 保存保单账户流水信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#saveCsPolicyAccountTransList(com.nci.tunan.cs.model.bo.CsPolicyAccountTransListBO)
	 * @param csPolicyAccountTransListBO 保单投资连结交易ListBO
	 * @return
	 */
	@Override
	public boolean saveCsPolicyAccountTransList(
			CsPolicyAccountTransListBO csPolicyAccountTransListBO) {
		csPolicyAccountTransListDao.addCsPolicyAccountTransList(BeanUtils.copyProperties(CsPolicyAccountTransListPO.class, csPolicyAccountTransListBO));
		return false;
	}


     /** 
     * @description 根据条件查询人口状态代码表信息
     * @version V1.0.0
     * <AUTHOR> <EMAIL>
     * @param param  param BigDecimal 
     * @return 
     */
    @Override
    public List<CustomerBO> queryLiveStatus(BigDecimal busiItemId) {
        CustomerPO customerPO = new CustomerPO();
        customerPO.setBigDecimal("busi_item_id", busiItemId);
        return BeanUtils.copyList(CustomerBO.class, customerDao.queryLiveStatus(customerPO));
    }

    /**
     * 
     * @description 查询已保存的生存保险金追回信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findAllPolicyRetrieveBOs(com.nci.tunan.cs.model.bo.PolicyRetrieveBO)
     * @param policyRetrieveBO policyRetrieveBO
     * @return
     */
	@Override
	public List<PolicyRetrieveBO> findAllPolicyRetrieveBOs(
			PolicyRetrieveBO policyRetrieveBO) {
		List<PolicyRetrievePO> policyRetrievePOs = policyRetrieveDao.findAllPolicyRetrieve(BeanUtils.copyProperties(PolicyRetrievePO.class, policyRetrieveBO));
		return BeanUtils.copyList(PolicyRetrieveBO.class, policyRetrievePOs);
	}


     /** 
     * @description 根据条件查询客户表信息
     * @version V1.0.0
     * <AUTHOR> <EMAIL>
     * @param    CsInsuredListBO 保单被保人列表BO
     * @return 
     */
	@Override
	public CustomerBO findCustomerVOByChangeID(CsInsuredListBO csInsuredListBO) {
		csInsuredListBO.setOldNew("1");
		List<CsInsuredListPO> csInsuredListPOList = csInsuredListDao.findAllCsInsuredList(BeanUtils.copyProperties(CsInsuredListPO.class, csInsuredListBO));
		if(null == csInsuredListPOList || csInsuredListPOList.size() ==0){
			return null;
		}
		// 462险种只判断第一被保人的生存状态
		BigDecimal customerID = null;
		for (CsInsuredListPO csInsuredListPO : csInsuredListPOList) {
			CsBenefitInsuredPO csBenefitInsuredPO = new CsBenefitInsuredPO();
			csBenefitInsuredPO.setInsuredId(csInsuredListPO.getListId());
			csBenefitInsuredPO.setPolicyChgId(csInsuredListPO.getPolicyChgId());
			csBenefitInsuredPO.setOldNew("1");
			List<CsBenefitInsuredPO> csBenefitInsuredPOs = csBenefitInsuredDao.findAllCsBenefitInsured(csBenefitInsuredPO);
			if(CollectionUtils.isNotEmpty(csBenefitInsuredPOs)){
				for (CsBenefitInsuredPO cip : csBenefitInsuredPOs) {
					if(cip.getOrderId().compareTo(BigDecimal.ONE) == 0){
						customerID = csInsuredListPO.getCustomerId();
					}
				}
			}
		}
		if(customerID == null){
			customerID = csInsuredListPOList.get(0).getCustomerId();
		}
		CustomerPO customerPO = new CustomerPO();
		customerPO.setCustomerId(customerID);
		List<CustomerPO> customerPOList = customerDao.findAllCustomer(customerPO);
        return  BeanUtils.copyProperties(CustomerBO.class, customerPOList.get(0));
	}
	
	/**
	 * 
	 * @description 查询生存金领取形式
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findPayDueByTime()
	 * @return
	 */
	@Override
	public List<PayDuePO> findPayDueByTime(PayDuePO payDuePO) {
		return payDueDao.queryPayDue(payDuePO);
	}
	
	

	
	/**
	 * 
	 * @description 更新万能险账户信息 并保存账户交易记录
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#updateInvestAccount(java.math.BigDecimal, java.math.BigDecimal, java.math.BigDecimal)
	 * @param    policyId policyId
	 * @param    transAmount transAmount
	 * @param    transBusiItemId transBusiItemId
	 */
	private void updateInvestAccount(CsContractInvestPO csContractInvestBO,
			BigDecimal amount, Date acceptTime) { 
	  
        if (csContractInvestBO != null) {
            /** 1.保存账户交易记录 */
            CsFundServicePO csFundServicePO = new CsFundServicePO();
            csFundServicePO.setPolicyId(csContractInvestBO.getPolicyId()); //@invalid  保单id
            csFundServicePO.setBusiItemId(csContractInvestBO.getBusiItemId());
            if (csContractInvestBO.getBusiItemId() == null) {
                CsContractBusiProdPO csContractBusiProdPO = new CsContractBusiProdPO();
                csContractBusiProdPO.setChangeId(csContractInvestBO.getChangeId());
                csContractBusiProdPO.setBusiPrdId(csContractInvestBO.getProductId());
                //2.查询保全险种表信息
                List<CsContractBusiProdPO> csContractBusiProdPOs = csContractBusiProdDao
                        .findAllCsContractBusiProd(csContractBusiProdPO);
                if (CollectionUtils.isNotEmpty(csContractBusiProdPOs)) {
                    csFundServicePO.setBusiItemId(csContractBusiProdPOs.get(0).getBusiItemId()); //@invalid  险种id
                } else {
                    csFundServicePO.setBusiItemId(new BigDecimal(0));
                }
            }

            CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
            //@invalid 和songdf沟通，修改以下一行代码，原先是setAcceptId 2018-04-10 by tanyh
            csPolicyChangePO.setChangeId(csContractInvestBO.getChangeId());
//@invalid      csPolicyChangePO.setPolicyId(csContractInvestBO.getPolicyId());
            csPolicyChangePO = csPolicyChangeDao.findCsPolicyChange(csPolicyChangePO);
            //@invalid 和songdf沟通增加以下一行代码，原先是直接csPolicyChangePO获取acceptCode 2018-04-10 by tanyh
            CsAcceptChangeBO csAcceptChangeBO=super.findCsAcceptChangeBOByAcceptId(csPolicyChangePO.getAcceptId());
            csFundServicePO.setAcceptCode(csAcceptChangeBO.getAcceptCode());
            csFundServicePO.setItemId(csContractInvestBO.getItemId()); //@invalid  责任组id
         
            csFundServicePO.setProductId(csContractInvestBO.getProductId()); //@invalid  精算产品id
            csFundServicePO.setChangeId(csContractInvestBO.getChangeId()); //@invalid  保全变更id
            csFundServicePO.setPolicyChgId(csContractInvestBO.getPolicyChgId()); //@invalid  保单变更id
            csFundServicePO.setDistriType(Constants.DISTRI_TYPE_OUT); //@invalid  资金分配类型:转出
            csFundServicePO.setAccountCode(csContractInvestBO.getAccountCode()); //@invalid  投资基金账户代码
            csFundServicePO.setSwitchInCode(null); //@invalid  转换后基金账户代码
            csFundServicePO.setTargetId(null); //@invalid  转移/转换目标险种id
            csFundServicePO.setAssignUnit(null); //@invalid  By amount or unit
            csFundServicePO.setApplyAmount(amount); //@invalid  申请交易金额
            csFundServicePO.setApplyUnits(null); //@invalid  申请交易单位数
            csFundServicePO.setApplyTime(acceptTime); //@invalid  交易申请时间
            csFundServicePO.setFundProcessStatus(Constants.FUND_PROCESS_STATUS_WAIT); //@invalid  交易申请处理状态
                                                                                      //@invalid  0:等待交易
                                                                                      //@invalid  1:交易完成
                                                                                      //@invalid  2:交易取消
            csFundServicePO.setMoneyCode(Constants.MONEY_CODE_CHINA); //@invalid  货币Id
            csFundServicePO.setSaFactor(null); //@invalid  费用因子
            csFundServicePO.setTransChargeFee(new BigDecimal(0)); //@invalid  基金交易费用
            csFundServicePO.setTransCode(Constants.TRANS_CODE_43); //@invalid  交易类型码
            csFundServicePO.setTerminalInterest(new BigDecimal(0)); //@invalid  terminal
                                                                    //@invalid  interest
            csFundServicePO.setValidateFlag(Constants.YES_NO__NO); //@invalid  是否生效
            csFundServicePO.setTransType(new BigDecimal(0));
           //@invalid 先查删了再加
            CsFundServicePO csFundServicePO1=new CsFundServicePO();
            csFundServicePO1.setItemId(csContractInvestBO.getItemId()); //@invalid  责任组id
            csFundServicePO1.setChangeId(csContractInvestBO.getChangeId()); //@invalid  保全变更id
            csFundServicePO1.setPolicyChgId(csContractInvestBO.getPolicyChgId()); //@invalid  保单变更id
            csFundServicePO1.setAccountCode(csContractInvestBO.getAccountCode()); //@invalid  投资基金账户代码
            csFundServicePO1.setTransCode(Constants.TRANS_CODE_OUT); //@invalid  交易类型码
    		List<CsFundServicePO> fundServicePOList=csFundServiceDao.findAllFundService(csFundServicePO1);
    		//4.保存基金定义表Service
            CsFundServicePO addFundService = csFundServiceDao.addFundService(csFundServicePO);
            if (addFundService.getApplyAmount() != null) {
                //5.更新投连万能账户信息
                CsContractInvestPO csContractInvestPO = new CsContractInvestPO();

                csContractInvestPO = BeanUtils.copyProperties(CsContractInvestPO.class, csContractInvestBO);
                csContractInvestPO.setOperationType(Constants.OPERATIONTYPE_UPDATE);
                csContractInvestPO.setOldNew(Constants.NEW);
                csContractInvestDao.updateCsContractInvest(csContractInvestPO);

            }

        }
    }
    
    
   
    
    /**
     * 
     * @description 查询新增的保全基金交易记录
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findAllCsFundService(com.nci.tunan.cs.model.bo.CsFundServiceBO)
     * @param    csFundServiceBO 基金定义表Service
     * @return
     */
    @Override
	public List<CsFundServiceBO> findAllCsFundService(CsFundServiceBO csFundServiceBO) {
    	List<CsFundServicePO> csFundServicePOs = csFundServiceDao.findAllFundService(BeanUtils.copyProperties(CsFundServicePO.class,
                csFundServiceBO));
        List<CsFundServiceBO> csFundServiceBOs = BeanUtils.copyList(CsFundServiceBO.class, csFundServicePOs);
        return csFundServiceBOs;
	}
    

    /**
     * 
     * @description 删除新增的保全基金交易记录
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#deleteCsFundService(java.util.List)
     * @param csFundServiceBOList 基金定义表Service
     * @return
     */
	public boolean deleteCsFundService(List<CsFundServiceBO> csFundServiceBOList) {
		boolean flag = false;
		for (CsFundServiceBO csFundServiceBO : csFundServiceBOList) {
			flag = csFundServiceDao.deleteFundService(BeanUtils.copyProperties(CsFundServicePO.class, csFundServiceBO));
			if (!flag) {
				break;
			}
		}
		return flag;
	}
	
	/**
	 * 
	 * @description 删除修改的账户信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param   csContractInvestBO 保单投资连结表BO
	 * @return
	 */
	public boolean deleteCsContractInvest(CsContractInvestBO csContractInvestBO) {
		return csContractInvestDao.deleteCsContractInvest(
				BeanUtils.copyProperties(CsContractInvestPO.class, csContractInvestBO));
	}
	
 
	/**
	 * 
	 * @description 查询客户信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findCustomer(com.nci.tunan.cs.model.bo.CsCustomerBO)
	 * @param csCustomerBO   csCustomerBO
	 * @return
	 */
	@Override
	public CsCustomerBO findCustomer(CsCustomerBO csCustomerBO) {
		CustomerPO customerPO = new CustomerPO();
		customerPO.setCustomerId(csCustomerBO.getCustomerId());
		CustomerPO findCustomer = this.customerDao.findCustomer(customerPO);
        return  BeanUtils.copyProperties(CsCustomerBO.class, findCustomer);
	}
	

	/**
	 * 
	 * @description 查询追回的发放记录
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findRGPayDueList(com.nci.tunan.cs.model.bo.CsPayDueBO)
	 * @param csPayDueBO csPayDueBO
	 * @return
	 */
	@Override
	public List<CsPayDueBO> findRGPayDueList(CsPayDueBO csPayDueBO) {
		List<CsPayDuePO> csPayDueList = csPayDueDao.findRGPayDueList(BeanUtils.copyProperties(CsPayDuePO.class,csPayDueBO));
		return BeanUtils.copyList(CsPayDueBO.class,csPayDueList);
	}

	@Override
     /** 
     * @description 根据条件查询保费记录表信息
     * @version V1.0.0
     * <AUTHOR> <EMAIL>
     * @param  PremArapBO 应收应付表BO
     * @return 
     */
	public PremArapBO findCsPremArap(PremArapBO premArapBO) {
		PremArapPO findPremArap = this.premArapDao.findPremArap(BeanUtils.copyProperties(PremArapPO.class,premArapBO));
		return BeanUtils.copyProperties(PremArapBO.class,findPremArap);
	}
	
	

	/**
	 * 
	 * @description  查询保单投资连结表
	 * @version 
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findAllCsContractInvest(com.nci.tunan.cs.model.bo.CsContractInvestBO)
	 * @param csContractInvestBO csContractInvestBO
	 * @return
	 */
	@Override
	public List<CsContractInvestBO> findAllCsContractInvest(
			CsContractInvestBO csContractInvestBO) {
		CsContractInvestPO csContractInvestPO = BeanUtils.copyProperties(CsContractInvestPO.class, csContractInvestBO);
		List<CsContractInvestPO> findAllCsContractInvest = csContractInvestDao.findAllCsContractInvest(csContractInvestPO); 
		return BeanUtils.copyList(CsContractInvestBO.class, findAllCsContractInvest);
	}
  
	@Override
     /** 
     * @description 根据条件更新保费记录表信息
     * @version V1.0.0
     * <AUTHOR> <EMAIL>
     * @param  PremArapBO 应收应付表BO
     * @return 
     */
	public void updatePremArapStatus(PremArapBO premArap) {
		 premArapDao.updatePremArapByUnitNumberForPA(BeanUtils.copyProperties(PremArapPO.class, premArap));
	}


	

	/**
	 * 
	 * @description 查询追回金额
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseRGService#findRetriveFee(com.nci.tunan.pa.interfaces.model.po.PremArapPO, com.nci.tunan.cs.model.vo.CsEndorseRGPolicyVO, java.util.Date, java.math.BigDecimal, com.nci.tunan.cs.model.bo.CsPayDueBO, com.nci.tunan.cs.model.po.CsPayPlanPO, java.lang.String)
	 * @param premArap premArap
	 * @param csEndorseRGPolicyVO csEndorseRGPolicyVO
	 * @param acceptTime acceptTime
	 * @param customerId customerId
	 * @param csPayDue csPayDue
	 * @param csPayPlanBO csPayPlanBO
	 * @param isCalInterest isCalInterest
	 * @return
	 */
	@Override
	public CsEndorseRGPolicyVO findRetriveFee(PremArapPO premArap,
			CsEndorseRGPolicyVO csEndorseRGPolicyVO, Date acceptTime,
			BigDecimal customerId, CsPayDueBO csPayDue,CsPayPlanPO csPayPlanBO, String isCalInterest) {
		
	    /**1.应领未领的  进行核销处理*/
		if(!csPayDue.getFeeStatus().equals(Constants.FEE_STATUS_ZERO)){
			if((Constants.BUSIPROD_CODE_933.equals(csPayDue.getBusiProdCode()) || Constants.BUSIPROD_CODE_9331.equals(csPayDue.getBusiProdCode())) && Constants.PAY_PLAN_TYPE_SURVIVALBENEFIT.equals(csPayPlanBO.getPayPlanType())){
				/** 933产品养老年金 追回万能账户 特殊判断**/
				
				
			}else if(csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_1)
					||csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_SURVIVALBENEFIT)
					||csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_10)){
				/**1.1追回账户处理  推送首付费  1.生存金 2.现金红利 3.理赔金转年仅 */	
				csEndorseRGPolicyVO = findPayPlanType3(csPayPlanBO,premArap,csEndorseRGPolicyVO,acceptTime,customerId,csPayDue,isCalInterest);
			}
			
			 /**1.2约定年金追回处理 */
			if(csPayPlanBO.getPayPlanType().equals("8")){
				csEndorseRGPolicyVO = findPayPlanType8(csPayPlanBO,premArap,csEndorseRGPolicyVO,acceptTime,customerId,csPayDue,isCalInterest);
			}
		}
		
		//@invalid 修改pay_plan  pay_due
		updateCsPayPlans(premArap,csPayDue);
	
		return csEndorseRGPolicyVO;
	}

	
	/**
	 * 
	 * @description 修改t_cs_pay_plan
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param premArapBO premArapBO
	 * @param csPayDue csPayDue
	 */
    private void updateCsPayPlans(PremArapPO premArapBO, CsPayDueBO csPayDue) {

			//1.更新生存给付应领表t_pay_due
			csPayDue.setOperationType(Constants.OPERATIONTYPE_UPDATE);
			CsPayDuePO csPayDuePO = BeanUtils.copyProperties(CsPayDuePO.class,csPayDue);
			this.csPayDueDao.updateCsPayDue(csPayDuePO);
			
            String deduFlag = csPayDuePO.getDeductionFlag();
            if (deduFlag != null && deduFlag.equals("1")) {
                return;
            }
			
			
			//2.查询生存金给付计划
			CsPayPlanPO csPayPlanPO = new CsPayPlanPO();
			csPayPlanPO.setPlanId(csPayDue.getPlanId());
			csPayPlanPO.setChangeId(csPayDue.getChangeId());
			csPayPlanPO.setOldNew(Constants.NEW);
			csPayPlanPO.setPolicyChgId(csPayDue.getPolicyChgId());
			CsPayPlanPO findCsPayPlan = csPayPlanDao.findCsPayPlan(csPayPlanPO);
			if(findCsPayPlan!=null){
				//2.1更新生存金给付计划表的下期应领日期变更到生存金追回日的本期
				if (findCsPayPlan.getPayDueDate().getTime() > csPayDue.getPayDueDate().getTime()) {
					findCsPayPlan.setPayDueDate(csPayDue.getPayDueDate());
				}
				findCsPayPlan.setOperationType(Constants.OPERATIONTYPE_UPDATE);
				findCsPayPlan.setPayStatus(new BigDecimal(1));
				csPayPlanDao.updateCsPayPlan(findCsPayPlan);
				
				/**
				 * @invalid Redmine:47068(关于累积生息与万能账户补结息需求)
				 * 如果追回的是现金红利，则需要将现金红利记录改为修改为修改
				 */
				if(findCsPayPlan.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_1)){
					
					CsBonusAllocatePO csBonusAllocatePO = new CsBonusAllocatePO();
					csBonusAllocatePO.setPolicyChgId(csPayDue.getPolicyChgId());
					csBonusAllocatePO.setBusiItemId(csPayDue.getBusiItemId());
					csBonusAllocatePO.setItemId(csPayDue.getItemId());
					csBonusAllocatePO.setAllocateDueDate(csPayDue.getPayDueDate());
					csBonusAllocatePO.setOldNew(Constants.NEW);
					//3.查询所有分红详细信息表
					List<CsBonusAllocatePO> csBonusAllocateList = csBonusAllocateDao.findAllCsBonusAllocate(csBonusAllocatePO);
					//4.遍历查询到的分红详细信息
					for (CsBonusAllocatePO csBonusAllocatePO2 : csBonusAllocateList) {
						csBonusAllocatePO2.setOperationType(Constants.OPERATIONTYPE_UPDATE);
						csBonusAllocatePO2.setValidFlag(Constants.LING);
						//4.1更新分红详细信息
						csBonusAllocateDao.updateCsBonusAllocate(csBonusAllocatePO2);
						
					}
				}
				
			}
	}


	/**
	 * 
	 * @description 生存金追回生存金现金红利批处理
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPayPlanBO csPayPlanBO
	 * @param premArap csPayPlanBO
	 * @param csEndorseRGPolicyVO csEndorseRGPolicyVO
	 * @param acceptTime acceptTime
	 * @param customerId customerId
	 * @param csPayDue csPayDue
	 * @param isCalInterest isCalInterest
	 * @return
	 */
private CsEndorseRGPolicyVO findPayPlanType3(CsPayPlanPO csPayPlanBO, PremArapPO premArap, 
		CsEndorseRGPolicyVO csEndorseRGPolicyVO, Date acceptTime, BigDecimal customerId, CsPayDueBO csPayDue,String isCalInterest) {
	
    
	    BigDecimal changeId = csEndorseRGPolicyVO.getChangeId();
		BigDecimal policyChgId = csEndorseRGPolicyVO.getPolicyChgId();
		
		CsContractMasterPO csContractMaster = new CsContractMasterPO();
        csContractMaster.setOldNew(Constants.OLD);
        csContractMaster.setPolicyId(csPayPlanBO.getPolicyId());
        csContractMaster.setPolicyChgId(csEndorseRGPolicyVO.getPolicyChgId());
        CsContractMasterPO findCsContractMaster = this.csContractMasterDao.findCsContractMaster(csContractMaster);
       
    
		/** 
		 *1.发放方式为累计声息生存金,累计声息账户到责任层
		 */
		if((csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_SURVIVALBENEFIT)
				||csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_10))
				&&csPayDue.getSurvivalMode().compareTo(new BigDecimal(2))==0){
			csEndorseRGPolicyVO = this.findPayPlanType3SurvivalMode2(csPayPlanBO,policyChgId,changeId,premArap,acceptTime,customerId,csPayDue,csEndorseRGPolicyVO,isCalInterest);
		}
		
		/**
		 *2.现金红利 、累计声息账户
		 */
		if(csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_1)
				&&csPayDue.getSurvivalMode().compareTo(new BigDecimal(2))==0){
			csEndorseRGPolicyVO = this.findPayPlanType1SurvivalMode2(csPayPlanBO,policyChgId,changeId,premArap,acceptTime,customerId,csPayDue,csEndorseRGPolicyVO,isCalInterest);
		}
		
		
		/**
		 * 3.发放方式为万能账户
		 */
		if(csPayDue.getSurvivalMode().compareTo(Constants.FOUR)==0){
			CsContractInvestPO csContractInvestBO = new CsContractInvestPO();
			csContractInvestBO.setInvestAccountType(Constants.INVEST_ACCOUNT_TYPE_2);//@invalid 915
			csContractInvestBO.setPolicyChgId(policyChgId);
			csContractInvestBO.setOldNew(Constants.NEW);
			csContractInvestBO = this.csContractInvestDao.findCsContractInvest(csContractInvestBO);
			
			ContractBusiProdPO contractBusiProd = new ContractBusiProdPO();
			if(null != csContractInvestBO.getListId()){
				contractBusiProd.setBusiItemId(csContractInvestBO.getBusiItemId());
				contractBusiProd.setPolicyId(csContractInvestBO.getPolicyId());
				contractBusiProd = this.contractBusiProdDao.findContractBusiProd(contractBusiProd);
			}
			if(null != csContractInvestBO.getListId() && null != contractBusiProd && null != contractBusiProd.getMasterBusiItemId()){
				csEndorseRGPolicyVO = this.findPayPlanType1Or3SurvivalMode4(csPayPlanBO,policyChgId,changeId,premArap,acceptTime,customerId,csPayDue,csEndorseRGPolicyVO,isCalInterest);
			}else{
				//3.保单下没有万能险账户，默认按现金处理
				csEndorseRGPolicyVO = this.findPayPlanType1Or3SurvivalMode1(csPayPlanBO,policyChgId,changeId,premArap,acceptTime,customerId,csPayDue,csEndorseRGPolicyVO,isCalInterest);
			}
			
		}
		
		/**
		 * @invalid 发放方式为现金
		 */
		if(csPayDue.getSurvivalMode().compareTo(new BigDecimal(1))==0){
			csEndorseRGPolicyVO = this.findPayPlanType1Or3SurvivalMode1(csPayPlanBO,policyChgId,changeId,premArap,acceptTime,customerId,csPayDue,csEndorseRGPolicyVO,isCalInterest);
		}
		return csEndorseRGPolicyVO;
		
	}


/**
 * 
 * @description 现金红利 生存金 发放方式为现金处理
 * @version
 * @title
 * <AUTHOR> <EMAIL>
 * @param csPayPlanBO 险种生存给付计划表BO
 * @param policyChgId  policyChgId
 * @param changeId policyChgId
 * @param premArap premArap
 * @param acceptTime acceptTime
 * @param customerId customerId
 * @param csPayDue csPayDue
 * @param csEndorseRGPolicyVO csEndorseRGPolicyVO
 * @param isCalInterest  isCalInterest
 * @return
 */
private CsEndorseRGPolicyVO findPayPlanType1Or3SurvivalMode1(
			CsPayPlanPO csPayPlanBO, BigDecimal policyChgId,
			BigDecimal changeId, PremArapPO premArap, Date acceptTime,
			BigDecimal customerId, CsPayDueBO csPayDue,
			CsEndorseRGPolicyVO csEndorseRGPolicyVO, String isCalInterest) {
	
	    BigDecimal amount = premArap.getFeeAmount();//@invalid 追回金额
	    BigDecimal premArapFeeAmount = new BigDecimal(0);//@invalid  应收金额合计
	    BigDecimal amountLi = new BigDecimal(0);//@invalid 利息
	    String isReissueInterest = Constants.ORDER_STATUS_0;//@invalid 判断是否是补发利息   0：否  1：是
	    String isPrem = Constants.ORDER_STATUS_0;//@invalid 判断是否产生补退费
	    
	    //@invalid  查找应收应付信息
        PremArapPO premArapBO = new PremArapPO();
        premArapBO.setArapFlag(Constants.ARAP_FlAG_OUT);
        premArapBO.setUnitNumber(csPayDue.getUnitNumber());
        List<PremArapPO> findAllPremArap = this.premArapDao.findAllPremArap(premArapBO);
        for(PremArapPO premArapPO : findAllPremArap){
        	if(Constants.FEE_STATUS_WAITING.equals(premArapPO.getFeeStatus())){
        		isPrem = Constants.ORDER_STATUS_0;
        		break;
        	}
        }
	    
	    
	    if(csPayDue.getReissueInterest()!=null && premArap.getFeeAmount().compareTo(csPayDue.getReissueInterest())==0){
            isReissueInterest = Constants.TRANS_TYPE_IN;
        }
	    
		premArapFeeAmount = premArapFeeAmount.add(amount);
		
		/**1.产生收付费*/
		if(Constants.ONE_STRING.equals(isCalInterest) && Constants.ZERO_STRING.equals(isPrem)){
		    saveRGPremArapCus(acceptTime,amount,csEndorseRGPolicyVO, customerId, Constants.FEE_STATUS_ZERO,csPayDue,csPayPlanBO,isReissueInterest,null);
		}

		csEndorseRGPolicyVO.setAmountLi(amountLi);
		csEndorseRGPolicyVO.setPremArapFeeAmount(premArapFeeAmount);
		csEndorseRGPolicyVO.setNotPremFee(Constants.LING);
		csEndorseRGPolicyVO.setIsReissueInterest(isReissueInterest);
		csEndorseRGPolicyVO.setItemId(csPayPlanBO.getItemId());
		return csEndorseRGPolicyVO;
	}



/***
 * @description  现金红利  生存金   万能账户   经和收付费沟通  账户结息不推送收付费
 * @version V1.0.0
 * @title
 * @<NAME_EMAIL>
 * @param    csPayPlanBO 险种生存给付计划表BO
 * @param    policyChgId policyChgId
 * @param    changeId changeId
 * @param    premArap premArap
 * @param    acceptTime acceptTime
 * @param    customerId customerId
 * @param    csPayDue csPayDue
 * @param    csEndorseRGPolicyVO csEndorseRGPolicyVO
 * @param    isCalInterest  是否计算利息  0：不需要计算利息  1：需要计算利息
 * @return
 */
private CsEndorseRGPolicyVO findPayPlanType1Or3SurvivalMode4(
			CsPayPlanPO csPayPlanBO, BigDecimal policyChgId,
			BigDecimal changeId, PremArapPO premArap, Date acceptTime,
			BigDecimal customerId, CsPayDueBO csPayDue,
			CsEndorseRGPolicyVO csEndorseRGPolicyVO,String isCalInterest) {
	
	    BigDecimal transAmount = new BigDecimal(0);//@invalid  交易金额
		BigDecimal notPremFee = new BigDecimal(0);  //@invalid  不可收付金额合计
		BigDecimal premArapFeeAmount = new BigDecimal(0);//@invalid  应收金额合计
		BigDecimal amount = premArap.getFeeAmount();//@invalid 追回金额
		BigDecimal amountLi = new BigDecimal(0);//@invalid 利息
		String isReissueInterest = Constants.REISSUEINTEREST_ZERO;//@invalid 判断是否是补发利息   0：否  1：是
		String isInterestCapital = Constants.INTERESTCAPITAL_ZERO; //@invalid 判断账户金额是否为0，账户金额为0 费用类型单独处理   0：否  1：是
		Date calDate = csPayDue.getPayDueDate();
		if (csPayPlanBO != null && Constants.PAY_PLAN_TYPE_1.equals(csPayPlanBO.getPayPlanType())) {
			calDate = getCalDate(csPayDue);
		}

		/**1.查询保单万能险账户,如果有生存金转入指定万能险账户,则进行处理*/
		CsContractInvestPO csContractInvestBO = new CsContractInvestPO();
		csContractInvestBO.setInvestAccountType(Constants.INVEST_ACCOUNT_TYPE_2);//@invalid 915
		csContractInvestBO.setPolicyChgId(policyChgId);
		csContractInvestBO.setOldNew(Constants.NEW);
		csContractInvestBO = this.csContractInvestDao.findCsContractInvest(csContractInvestBO);
		
		/**2.判断是否是补发利息*/
		if(csPayDue.getReissueInterest()!=null && premArap.getFeeAmount().compareTo(csPayDue.getReissueInterest())==0){
		    isReissueInterest = Constants.REISSUEINTEREST_ONE;
		}
		
		/**3.判断账户金额是否为0*/
		if(csContractInvestBO.getInterestCapital()!=null 
		        && csContractInvestBO.getInterestCapital().compareTo(Constants.LING)==0){
		    isInterestCapital =Constants.INTERESTCAPITAL_ONE;
		}
		
		/**4.账户金额为0 ，和需求确认 暂时按现金处理  ，无利息*/
		if(isInterestCapital.equals(Constants.INTERESTCAPITAL_ONE) && isCalInterest.equals(Constants.NEW)){
		      premArapFeeAmount=premArap.getFeeAmount();
		      /**产生补退费***/
			  this.saveXYPremArap(acceptTime,premArap.getFeeAmount(),amountLi,notPremFee,premArapFeeAmount,csEndorseRGPolicyVO, customerId, csPayDue,csPayPlanBO,isReissueInterest);
	          
	          csEndorseRGPolicyVO.setAmountLi(amountLi);
	          csEndorseRGPolicyVO.setPremArapFeeAmount(amount);
	          csEndorseRGPolicyVO.setItemId(csPayPlanBO.getItemId());
	          return csEndorseRGPolicyVO;
		}
		
		 /**
         * @invalid 从该笔金额进入万能账户后至保险金追回申请提交日、
         * 合同中止日与账户注销日的较早者（不含）实际经过的结算日个数+1 
         */
		if(isCalInterest!=null && isCalInterest.equals(Constants.NEW) && isReissueInterest.equals(Constants.REISSUEINTEREST_ZERO)){
		   
		    /**5.查询合同终止日期*/
	        CsContractMasterPO csContractMasterPO = new CsContractMasterPO();
	        csContractMasterPO.setPolicyCode(csPayPlanBO.getPolicyCode());
	        csContractMasterPO.setOldNew(Constants.NEW);
	        CsContractMasterPO findCsContractMaster = this.csContractMasterDao.findCsContractMaster(csContractMasterPO);
	        if(findCsContractMaster.getLiabilityState().compareTo(Constants.LIABILITY_STATE__TERMINATED)==0){
	            Date expiryDate = findCsContractMaster.getExpiryDate();
	            if(acceptTime.getTime()<expiryDate.getTime()){
	                acceptTime = expiryDate;
	            }
	        }
	        
	        if(csContractInvestBO!=null&&csContractInvestBO.getListId()!=null){
	            amountLi = sumInvest(csContractInvestBO,calDate,acceptTime,csPayPlanBO.getPayPlanType(),csPayDue.getFeeAmount());
	        }
	        if(amountLi==null){
	            amountLi=new BigDecimal(0);
	        }
	        
	        amount = amount.add(amountLi);//@invalid 万能账户加上账户结息
		}
	
		/**6.万能险账户余额>如果生存金追回金额    更新万能险账户余额为  原账户余额-生存金追回金额  并增加fund_trans交易记录 */
		if(csContractInvestBO.getInterestCapital().compareTo(amount) >= 0) {
			BigDecimal InterestCapital = csContractInvestBO.getInterestCapital().subtract(amount);
			csContractInvestBO.setInterestCapital(InterestCapital);
			notPremFee = notPremFee.add(amount); 
			transAmount = amount;
			
			/**@invalid 产生补退费 */
			if(Constants.NEW.equals(isCalInterest)){
				this.saveXYPremArap(acceptTime,premArap.getFeeAmount(),amountLi,notPremFee,premArapFeeAmount,csEndorseRGPolicyVO, customerId, csPayDue,csPayPlanBO,isReissueInterest);
			}
			
			
		} else {
		    
		    /**7.如果生存金追回金额>万能险账户余额  那么剩余的金额由客户补上    更新应收金额*/
			premArapFeeAmount = premArapFeeAmount.add(amount.subtract(csContractInvestBO.getInterestCapital()));//@invalid 向客户收取的金额
			transAmount = csContractInvestBO.getInterestCapital();
			notPremFee = notPremFee.add(csContractInvestBO.getInterestCapital()); 
			csContractInvestBO.setInterestCapital(new BigDecimal(0.00)); //@invalid  万能险账户金额被扣完了
			
			/**8.产生补退费*/
			if(Constants.NEW.equals(isCalInterest)){
				saveXYPremArap(acceptTime,premArap.getFeeAmount(),amountLi,notPremFee,premArapFeeAmount,csEndorseRGPolicyVO, customerId, csPayDue,csPayPlanBO,isReissueInterest);
			}
		}
		csContractInvestBO.setChangeId(csEndorseRGPolicyVO.getChangeId());
		csContractInvestBO.setPolicyChgId(policyChgId);
		if(!"2".equals(isCalInterest)){
		//@invalid  更新万能险账户信息 并保存账户交易记录
		if(transAmount.compareTo(Constants.LING)>0){
			updateInvestAccount(csContractInvestBO, transAmount, acceptTime);
		}
//		账户外追回金额不需要保存账户交易记录
//		if(premArapFeeAmount.compareTo(Constants.LING)>0){
//			updateInvestAccount(csContractInvestBO, premArapFeeAmount, acceptTime);
//		}
		}
		
		csEndorseRGPolicyVO.setAmountLi(amountLi);
		csEndorseRGPolicyVO.setPremArapFeeAmount(premArapFeeAmount);
		csEndorseRGPolicyVO.setNotPremFee(notPremFee);
		csEndorseRGPolicyVO.setIsReissueInterest(isReissueInterest);
		csEndorseRGPolicyVO.setItemId(csPayPlanBO.getItemId());
		return csEndorseRGPolicyVO;
	}


/***
 * 
 * @description  计算现金红利 累计声息账户  经和收付费沟通  账户结息不推送收付费
 * @version V1.0.0
 * @title
 * @<NAME_EMAIL>
 * @param    csPayPlanBO 险种生存给付计划表BO
 * @param    policyChgId policyChgId
 * @param    changeId changeId
 * @param    premArap premArap
 * @param    acceptTime acceptTime
 * @param    customerId customerId
 * @param    csPayDue csPayDue
 * @param    csEndorseRGPolicyVO csEndorseRGPolicyVO
 * @param    isCalInterest  是否计算利息  1：需要计算利息   0：不需要计算利息
 * @return
 */
private CsEndorseRGPolicyVO findPayPlanType1SurvivalMode2(CsPayPlanPO csPayPlanBO, BigDecimal policyChgId, BigDecimal changeId,
		PremArapPO premArap, Date acceptTime, BigDecimal customerId, CsPayDueBO csPayDue, 
		CsEndorseRGPolicyVO csEndorseRGPolicyVO,String isCalInterest) {
	
		BigDecimal transAmount = new BigDecimal(0);//@invalid  交易金额
		BigDecimal interestBalance = new BigDecimal(0);//@invalid  累计生息账户利息余额
		BigDecimal capital_balance = new BigDecimal(0);//@invalid  累计生息账户本金
		BigDecimal interest_capital = new BigDecimal(0);//@invalid  累计生息账户本金利息和
		BigDecimal notPremFee = new BigDecimal(0);  //@invalid  不可收付金额合计
		BigDecimal premArapFeeAmount = new BigDecimal(0);//@invalid  应收金额合计
		BigDecimal amount = premArap.getFeeAmount();//@invalid 追回金额
		BigDecimal amountLi = new BigDecimal(0);//@invalid 利息
		String isReissueInterest = Constants.REISSUEINTEREST_ZERO;//@invalid 判断是否是补发利息   0：否  1：是
		String isInterestCapital = Constants.INTERESTCAPITAL_ZERO; //@invalid 判断账户金额是否为0，账户金额为0 费用类型单独处理   0：否  1：是
		Date calDate = csPayDue.getPayDueDate();
		if (csPayPlanBO != null && Constants.PAY_PLAN_TYPE_1.equals(csPayPlanBO.getPayPlanType())) {
			calDate = getCalDate(csPayDue);
		}

		//@invalid  查询红利账户----对应现金红利累计声息发放方式
		CsPolicyAccountPO csPolicyAccountBonus = new CsPolicyAccountPO();
		csPolicyAccountBonus.setPolicyChgId(policyChgId);
		csPolicyAccountBonus.setBusiItemId(csPayPlanBO.getBusiItemId());
		csPolicyAccountBonus.setItemId(csPayPlanBO.getItemId());
		csPolicyAccountBonus.setOldNew(Constants.NEW);
		csPolicyAccountBonus.setAccountType(Constants.CS_POLICY_ACCOUNT_TYPE_ACCOUNTCASH);//@invalid  现金分红累计生息账户
		List<CsPolicyAccountPO> csPolicyAccountBonusList = this.csPolicyAccountDao.findAllCsPolicyAccount(csPolicyAccountBonus);
		CsPolicyAccountPO csPolicyAccountBO = csPolicyAccountBonusList.get(0);
		
		/**1.判断是否为补发现金红利*/
		if(csPayDue.getReissueInterest()!=null && premArap.getFeeAmount().compareTo(csPayDue.getReissueInterest())==0){
			isReissueInterest = Constants.REISSUEINTEREST_ONE;
		}
		
		/**2.判断累计声息账户是否注销*/
		if(csPolicyAccountBO.getPolicyAccountStatus()!=null && csPolicyAccountBO.getPolicyAccountStatus().equals(Constants.TWO_STRING)){
			isInterestCapital = Constants.INTERESTCAPITAL_ONE;
		}
		
		/**3.如果累计声息账户注销 */
		if(isInterestCapital.equals(Constants.NEW)){
			/**3.1产生补退费*/
			saveXYPremArap(acceptTime,premArap.getFeeAmount(),amountLi,notPremFee,premArap.getFeeAmount(),csEndorseRGPolicyVO, customerId,csPayDue,csPayPlanBO,isReissueInterest);
			
			csEndorseRGPolicyVO.setAmountLi(amountLi);
			csEndorseRGPolicyVO.setPremArapFeeAmount(amount);
			csEndorseRGPolicyVO.setItemId(csPayPlanBO.getItemId());
			return csEndorseRGPolicyVO;
		}
		
		if (isCalInterest!=null && isCalInterest.equals(Constants.NEW) && isReissueInterest.equals(Constants.REISSUEINTEREST_ZERO)) {
			/**3.2查询现金红利累计声息的利息*/
			if(csPolicyAccountBonusList.size()>0){
				BigDecimal interestBalance2 = csPolicyAccountBO.getInterestBalance();
				csPolicyAccountBO.setInterestBalance(csPayDue.getFeeAmount());
				/**
				 *@invalid 需求变更47068,涉及缺陷6525修改
				 */
				BigDecimal partAmountLi = getRuturnAmountForAcc(csPolicyAccountBO, calDate);
				amountLi = amountLi.add(partAmountLi);
				csPolicyAccountBO.setInterestBalance(interestBalance2);
			}
			if(amountLi!=null){
				amount = amount.add(amountLi);//@invalid 如果有结息，加上结算利息
			}
		}
		interestBalance = csPolicyAccountBO.getInterestBalance(); //@invalid  累计生息账户利息余额
		capital_balance = csPolicyAccountBO.getCapitalBalance(); //@invalid  累计生息账户本金
		interest_capital = csPolicyAccountBO.getInterestCapital(); //@invalid  累计生息账户本金利息和
		
		/*** 入账金额各自计算追回金额，计算精度问题可能导致追回金额大于账户余额， 如账户无出账记录且追回金额大于账户余额，增加特殊处理（ALM：17224） ***/
		boolean onlyInFlag = true;
		CsPolicyAccountTransListPO csPolicyAccountTransList = new CsPolicyAccountTransListPO();
		csPolicyAccountTransList.setChangeId(csPolicyAccountBO.getChangeId());
		csPolicyAccountTransList.setPolicyChgId(csPolicyAccountBO.getPolicyChgId());
		csPolicyAccountTransList.setOldNew(Constants.OLD);
		csPolicyAccountTransList.setAccountId(csPolicyAccountBO.getAccountId());
		csPolicyAccountTransList.setTransType(Constants.TRANS_TYPE_OUT);
//		csPolicyAccountTransList.set("start_date", csEndorseRGPolicyVO.getReitrieveDate());
		List<CsPolicyAccountTransListPO> csPolicyAccountTransLists = this.csPolicyAccountTransListDao.findAllCsPolicyAccountTransListBySpeCondition(csPolicyAccountTransList);
		if (!CollectionUtils.isEmpty(csPolicyAccountTransLists)) {
			for (CsPolicyAccountTransListPO csTransListPO : csPolicyAccountTransLists) {
				if ("5".equals(csTransListPO.getTransCode())) {
					continue;
				}
				onlyInFlag = false;
				break;
			}
		}
		/**@invalid追回金额  > 账户余额 */
		if (amount.compareTo(interest_capital) > 0 && onlyInFlag) {
			notPremFee = notPremFee.add(interest_capital); //@invalid  不可收付金额合计
			transAmount  = interest_capital;
			amountLi = interest_capital.subtract(premArap.getFeeAmount());
			interestBalance = new BigDecimal(0); //@invalid  利息余额
			capital_balance = new BigDecimal(0); //@invalid  本金
			interest_capital = new BigDecimal(0); //@invalid  本息和
			
			/**@invalid 产生补退费*/
			if(Constants.NEW.equals(isCalInterest)){
				 /**
				 * @invalid 需求变更47068,涉及缺陷6525修改
				 */
				this.saveXYPremArap(acceptTime,transAmount,amountLi,notPremFee,premArapFeeAmount,csEndorseRGPolicyVO, customerId, csPayDue,csPayPlanBO,isReissueInterest);
			}
		} 
		/*** 入账金额各自计算追回金额，计算精度问题可能导致追回金额大于账户余额， 如账户无出账记录且追回金额大于账户余额，增加特殊处理（ALM：17224） ***/
		/**@invalid追回金额  > 账户余额*/
		else if (amount.compareTo(interest_capital) > 0) {
			notPremFee = notPremFee.add(interest_capital); //@invalid  不可收付金额合计
			premArapFeeAmount = premArapFeeAmount.add(amount.subtract(interest_capital));  //@invalid  应向客户收取的金额
			transAmount  = interest_capital;
			interestBalance = new BigDecimal(0); //@invalid  利息余额
			capital_balance = new BigDecimal(0); //@invalid  本金
			interest_capital = new BigDecimal(0); //@invalid  本息和
			
			/**@invalid 产生补退费*/
			if(Constants.NEW.equals(isCalInterest)){
				 /**
				 * @invalid 需求变更47068,涉及缺陷6525修改
				 */
				this.saveXYPremArap(acceptTime,transAmount,amountLi,notPremFee,premArapFeeAmount,csEndorseRGPolicyVO, customerId, csPayDue,csPayPlanBO,isReissueInterest);
			}
		} 
		/**@invalid 追回的生存金  < 账户余额*/
		else {  
			notPremFee = notPremFee.add(amount);
			transAmount = amount;
			//@invalid 追回金额  >= 利息
			if (amount.compareTo(interestBalance) >= 0) {
				capital_balance = capital_balance.subtract(amount.subtract(interestBalance)); //@invalid  本金
				interestBalance = new BigDecimal(0); //@invalid  利息余额
			} else {
				//@invalid  追回金额  < 利息
				interestBalance = interestBalance.subtract(amount); //@invalid  利息
			}
			interest_capital = interest_capital.subtract(amount); //@invalid  本息和
			
			/**@invalid 产生补退费*/
			if(Constants.NEW.equals(isCalInterest)){
				 /**
				 *@invalid 需求变更47068,涉及缺陷6525修改
				 */
				this.saveXYPremArap(acceptTime,transAmount,amountLi,notPremFee,premArapFeeAmount,csEndorseRGPolicyVO, customerId, csPayDue,csPayPlanBO,isReissueInterest);
			}
			
		}
		
		if (!Constants.FLAG_TWO.equals(isCalInterest)) {
			CsPolicyAccountPO csPolicyAccountbonus = csPolicyAccountBonusList.get(0);
			csPolicyAccountbonus.setInterestBalance(interestBalance);
			csPolicyAccountbonus.setCapitalBalance(capital_balance);
			csPolicyAccountbonus.setInterestCapital(interest_capital);
			csPolicyAccountbonus.setOperationType(Constants.OPERATIONTYPE_UPDATE);
			updatePolicyAccount(csPolicyAccountbonus, transAmount, csEndorseRGPolicyVO.getChangeId(), policyChgId,acceptTime);
		}
		
		csEndorseRGPolicyVO.setAmountLi(amountLi);
		csEndorseRGPolicyVO.setPremArapFeeAmount(premArapFeeAmount);
		csEndorseRGPolicyVO.setNotPremFee(notPremFee);
		csEndorseRGPolicyVO.setIsReissueInterest(isReissueInterest);
		csEndorseRGPolicyVO.setItemId(csPayPlanBO.getItemId());
		return csEndorseRGPolicyVO;
		
	}


/***
 * 
 * @description  追回的生存金  <= 账户余额    经和收付费沟通  账户结息不推送收付费
 * @version V1.0.0
 * @title
 * @<NAME_EMAIL>
 * @param    acceptTime acceptTime
 * @param    feeAmount  追回金额
 * @param    amountLi 账户利息
 * @param    notPremFee 记账金额
 * @param    premArapFeeAmount  向客户应收金额
 * @param    csEndorseRGPolicyVO csEndorseRGPolicyVO
 * @param    customerId customerId
 * @param    feeStatus16 feeStatus16
 * @param    csPayDue csPayDue
 * @param    csPayPlanBO 险种生存给付计划表BO
 * @param    isReissueInterest isReissueInterest
 */
private void saveXYPremArap(Date acceptTime, BigDecimal feeAmount, BigDecimal amountLi, BigDecimal notPremFee,
        BigDecimal premArapFeeAmount, CsEndorseRGPolicyVO csEndorseRGPolicyVO, BigDecimal customerId,
        CsPayDueBO csPayDue, CsPayPlanPO csPayPlanBO, String isReissueInterest) {
	
	/**1.年金、现金红利累计声息账户*/
	if(csPayDue.getSurvivalMode().compareTo(Constants.SURVIVAL_TYPE_2)==0){
		//@invalid a.从账户应收记账
        saveRGPremArapCus(acceptTime, feeAmount, csEndorseRGPolicyVO, customerId, Constants.FEE_STATUS_16,csPayDue,csPayPlanBO,isReissueInterest,null);
        //@invalid b.账户金额不足，应收
        if(premArapFeeAmount.compareTo(Constants.LING)>0){
            saveRGPremArapCus(acceptTime, premArapFeeAmount, csEndorseRGPolicyVO, customerId, Constants.FEE_STATUS_ZERO,csPayDue,csPayPlanBO,isReissueInterest,null);
        }
	}
	
	/**1.年金、现金红利万能账户*/
	if(csPayDue.getSurvivalMode().compareTo(Constants.FOUR)==0){
		//@invalid a.主线应收  附件险应付
		String isMasterItem = "1";//@invalid 1:是主险  0：是附加险
        saveRGPremArapCus(acceptTime, feeAmount, csEndorseRGPolicyVO, customerId, Constants.FEE_STATUS_16,csPayDue,csPayPlanBO,isReissueInterest,isMasterItem);
        saveRGPremArapCus(acceptTime, feeAmount, csEndorseRGPolicyVO, customerId, Constants.FEE_STATUS_16,csPayDue,csPayPlanBO,isReissueInterest,Constants.OLD);

        //@invalid b.账户金额不足，差额应收
        if(premArapFeeAmount.compareTo(Constants.LING)>0){
            saveRGPremArapCus(acceptTime, premArapFeeAmount, csEndorseRGPolicyVO, customerId, Constants.FEE_STATUS_ZERO,csPayDue,csPayPlanBO,isReissueInterest,Constants.OLD);
        }
	}
    
}


/**
 * 
 * @description  计算生存金  累计声息账户  经和收付费沟通  账户结息不推送收付费
 * @version V1.0.0
 * @title
 * @<NAME_EMAIL>
 * @param    csPayPlanBO 险种生存给付计划表BO
 * @param    policyChgId  policyChgId
 * @param    changeId changeId
 * @param    premArap premArap
 * @param    acceptTime acceptTime
 * @param    customerId customerId
 * @param    csPayDue csPayDue
 * @param    csEndorseRGPolicyVO csEndorseRGPolicyVO
 * @param    isCalInterest  是否计算利息且产生补退费  1：需要计算利息  0：计算利息  
 * @return
 */
private CsEndorseRGPolicyVO findPayPlanType3SurvivalMode2(
			CsPayPlanPO csPayPlanBO, BigDecimal policyChgId, BigDecimal changeId,
			PremArapPO premArap, Date acceptTime, BigDecimal customerId, CsPayDueBO csPayDue, 
			CsEndorseRGPolicyVO csEndorseRGPolicyVO,String isCalInterest) {
	
		BigDecimal transAmount = new BigDecimal(0);//@invalid  交易金额
		BigDecimal interestBalance = new BigDecimal(0);//@invalid  累计生息账户利息余额
		BigDecimal capital_balance = new BigDecimal(0);//@invalid  累计生息账户本金
		BigDecimal interest_capital = new BigDecimal(0);//@invalid  累计生息账户本金利息和
		BigDecimal notPremFee = new BigDecimal(0);  //@invalid  不可收付金额合计
		BigDecimal premArapFeeAmount = new BigDecimal(0);//@invalid  应收金额合计
		BigDecimal amount = premArap.getFeeAmount();//@invalid 追回金额
		BigDecimal amountLi = new BigDecimal(0);//@invalid 利息
		String isInterestCapital = Constants.INTERESTCAPITAL_ZERO; //@invalid 判断账户金额是否为0，账户金额为0 费用类型单独处理   0：否  1：是
	
	    /**1.查询保单累计生息账户，累计生息账户目前按挂在责任组上处理*/
		CsPolicyAccountPO csPolicyAccountBO = new CsPolicyAccountPO();
		csPolicyAccountBO.setPolicyChgId(policyChgId);
		csPolicyAccountBO.setBusiItemId(csPayPlanBO.getBusiItemId());
		csPolicyAccountBO.setItemId(csPayPlanBO.getItemId());
		csPolicyAccountBO.setOldNew(Constants.NEW);
		csPolicyAccountBO.setAccountType(Constants.CS_POLICY_ACCOUNT_TYPE_ACCUMULATED);//@invalid  累计生息账户
		List<CsPolicyAccountPO> csPolicyAccountBOs = this.csPolicyAccountDao.findAllCsPolicyAccount(csPolicyAccountBO);
		CsPolicyAccountPO csPolicyAccount = csPolicyAccountBOs.get(0);
		
		/**2.判断累计声息账户是否注销*/
        if(csPolicyAccountBO.getPolicyAccountStatus()!=null && csPolicyAccountBO.getPolicyAccountStatus().equals(Constants.TWO_STRING)){
            isInterestCapital = Constants.INTERESTCAPITAL_ONE;
        }
        
        /**2.1如果累计声息账户注销   和需求确认暂按现金处理*/
        if(isInterestCapital.equals(Constants.NEW)){
            /**2.2产生补退费*/
			saveXYPremArap(acceptTime,premArap.getFeeAmount(),amountLi,notPremFee,premArap.getFeeAmount(),csEndorseRGPolicyVO, customerId,csPayDue,csPayPlanBO,Constants.OLD);
            
            csEndorseRGPolicyVO.setAmountLi(amountLi);
            csEndorseRGPolicyVO.setPremArapFeeAmount(amount);
            csEndorseRGPolicyVO.setItemId(csPayPlanBO.getItemId());
            return csEndorseRGPolicyVO;
        }
		
		/**
		 * 3.生存金累计声息账户
		 * 3.1更新表T_POLICY_ACCOUNT的本金利息和：
		 * 3.1.1 账户金额>追回金额，累计生息账户金额=追回前的账户金额-生存金追回金额（包括追回金额本金和利息）；
		 * 3.1.2账户金额<追回金额，累计生息账户金额=0；
		 * 3.1.3先扣利息，利息不够再扣本金
		 */
	    interestBalance = csPolicyAccount.getInterestBalance(); //@invalid  累计生息账户利息余额
	    capital_balance = csPolicyAccount.getCapitalBalance(); //@invalid  累计生息账户本金
	    interest_capital = csPolicyAccount.getInterestCapital(); //@invalid  累计生息账户本金利息和
	    
	    /**
         * 4.从该笔金额进入红利累积生息账户后至保险金追回申请提交日
         * 4.1合同中止日与账户注销日的较早者（不含）实际经过的结算日个数+1
         */
	    if(isCalInterest!=null && isCalInterest.equals(Constants.NEW)){
	        //@invalid 注销日
            if(csPolicyAccount.getPolicyAccountStatus()!=null&&csPolicyAccount.getPolicyAccountStatus().compareTo(POLICY_ACCOUNT_STATUS)==0){
                Date balanceDate = csPolicyAccount.getBalanceDate();
                if(acceptTime.getTime()<balanceDate.getTime()){
                    acceptTime = balanceDate;
                }
            }
            //@invalid 查询合同终止日期
            CsContractMasterPO csContractMasterPO = new CsContractMasterPO();
            csContractMasterPO.setPolicyCode(csPayPlanBO.getPolicyCode());
            csContractMasterPO.setOldNew(Constants.NEW);
            CsContractMasterPO findCsContractMaster = this.csContractMasterDao.findCsContractMaster(csContractMasterPO);
            if(findCsContractMaster.getLiabilityState().compareTo(Constants.LIABILITY_STATE__TERMINATED)==0){
                Date expiryDate = findCsContractMaster.getExpiryDate();
                if(acceptTime.getTime()<expiryDate.getTime()){
                    acceptTime = expiryDate;
                }
            }
        
            /**4.2查询生存金累计声息结算利息*/
            csPolicyAccount.setInterestBalance(premArap.getFeeAmount());
            BigDecimal partAmountLi = getRuturnAmountForAcc(csPolicyAccount, premArap.getDueTime());
            amountLi = amountLi.add(partAmountLi);
            
            if(amountLi!=null){
                amount = amount.add(amountLi);//@invalid 本息
            }
	    }
	    
		
	    /**4.3追回金额 >账户余额*/
		if (amount.compareTo(interest_capital) >= 0) {
			notPremFee = notPremFee.add(interest_capital); //@invalid  不可收付金额合计
			premArapFeeAmount = premArapFeeAmount.add(amount.subtract(interest_capital));  //@invalid  应向客户收取的金额
			transAmount = interest_capital; //@invalid  交易金额
			interestBalance = new BigDecimal(0); //@invalid  利息余额
			capital_balance = new BigDecimal(0); //@invalid  本金
			interest_capital = new BigDecimal(0); //@invalid  本息和
			
			if(Constants.NEW.equals(isCalInterest)){
				saveXYPremArap(acceptTime,premArap.getFeeAmount(),amountLi,notPremFee,premArapFeeAmount,csEndorseRGPolicyVO, customerId,csPayDue,csPayPlanBO,Constants.OLD);
			}
			
		}
		/**4.4追回金额<本金利息和*/
		else {  
			notPremFee = notPremFee.add(amount);
			transAmount = amount;
			/**
			 * @invalid 缺陷：8676，账户金额大于追回金额，只记账。
			 */
			premArapFeeAmount = Constants.LING;
			/**@invalid 追回金额>=利息*/
			if (amount.compareTo(interestBalance) >= 0) {
				capital_balance = capital_balance.subtract(amount.subtract(interestBalance)); //@invalid  本金
				interestBalance = new BigDecimal(0); //@invalid  利息余额
			} 
			/**@invalid 追回金额<利息*/
			else {
				interestBalance = interestBalance.subtract(amount); //@invalid  利息
			}
			interest_capital = interest_capital.subtract(amount); //@invalid  本息和
			
			if(Constants.NEW.equals(isCalInterest)){
				saveXYPremArap(acceptTime,premArap.getFeeAmount(),amountLi,notPremFee,premArapFeeAmount,csEndorseRGPolicyVO, customerId, csPayDue,csPayPlanBO,Constants.OLD);
			}
		}
		
		/**5.更新累积生息账户 并新增账户流水记录*/
		if(!Constants.FLAG_TWO.equals(isCalInterest)){
		CsPolicyAccountPO csPolicyAccountBO1 = csPolicyAccountBOs.get(0);
		csPolicyAccountBO1.setInterestBalance(interestBalance);
		csPolicyAccountBO1.setCapitalBalance(capital_balance);
		csPolicyAccountBO1.setInterestCapital(interest_capital);
		csPolicyAccountBO1.setOperationType(Constants.OPERATIONTYPE_UPDATE);
		updatePolicyAccount(csPolicyAccountBO1, transAmount, changeId, policyChgId,acceptTime);
		}
		
		csEndorseRGPolicyVO.setAmountLi(amountLi);//@invalid 利息
		csEndorseRGPolicyVO.setPremArapFeeAmount(premArapFeeAmount);//@invalid 应收金额
		csEndorseRGPolicyVO.setNotPremFee(notPremFee);//@invalid 不可收付金额
		csEndorseRGPolicyVO.setItemId(csPayPlanBO.getItemId());
        return csEndorseRGPolicyVO;
	}




/**
 * 
 * @description 保存应收应付信息
 * @version
 * @title
 * <AUTHOR> <EMAIL>
 * @param acceptTime acceptTime
 * @param amount amount
 * @param csEndorseRGPolicyVO csEndorseRGPolicyVO
 * @param customerId customerId
 * @param feeStatus feeStatus
 * @param csPayDue csPayDue
 * @param csPayPlanBO  csPayPlanBO
 * @param isReissueInterest isReissueInterest
 * @param isMasterItem isMasterItem
 */
private void saveRGPremArapCus(Date acceptTime,
		BigDecimal amount, CsEndorseRGPolicyVO csEndorseRGPolicyVO,
		BigDecimal customerId, String feeStatus,
		CsPayDueBO csPayDue, CsPayPlanPO csPayPlanBO, String isReissueInterest,String isMasterItem) {
	
	//1.查询投保人
	CsPolicyHolderPO csPolicyHolderBO= new CsPolicyHolderPO();
	csPolicyHolderBO.setChangeId(csEndorseRGPolicyVO.getChangeId());
	csPolicyHolderBO.setPolicyChgId(csEndorseRGPolicyVO.getPolicyChgId());
	csPolicyHolderBO.setOldNew(Constants.OLD);
	CsPolicyHolderPO findCsPolicyHolderBO = this.csPolicyHolderDao.findCsPolicyHolder(csPolicyHolderBO);
	
	
	CustomerPO customerBO = new CustomerPO();
	customerBO.setCustomerId(findCsPolicyHolderBO.getCustomerId());
	CustomerPO findCsCustomer = this.customerDao.findCustomer(customerBO);
	
	//2.获取应收应付对象
    CsPremArapBO csPremArapBO = getPremArapBO(csEndorseRGPolicyVO, customerId,csPayDue,csPayPlanBO,feeStatus,isReissueInterest,isMasterItem);
    csPremArapBO.setDueTime(acceptTime); //@invalid  应缴应付日
    csPremArapBO.setFeeStatus(feeStatus); //@invalid  收付状态
    csPremArapBO.setFeeAmount(amount); //@invalid  应付金额
//@invalid  csPremArapBO.setPayMode("90");//@invalid 记账是90  应收是10
    csPremArapBO.setPayeeName(findCsCustomer.getCustomerName());
    csPremArapBO.setPayLiabCode(csPayPlanBO.getLiabCode());
//@invalid  csPremArapBO.setProductCode(csPayDue.getProductCode());
    //3.区分现金红利累计声息账户、万能账户
    if(csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_1)){
        if(csPayDue.getSurvivalMode().compareTo(Constants.SURVIVAL_TYPE_2)==0){
            csPremArapBO.setRefeflag("JRLJSXZH");
        }
        if(csPayDue.getSurvivalMode().compareTo(Constants.FOUR)==0){
            csPremArapBO.setRefeflag("JRWNZH");
        }
    }
    //4.插入应收应付抄单表
    csPremArapBO.setPayMode(Constants.PAY_MODE_CODE_XJ);  //@invalid 10-现金  经与周旭沟通赋值为现金
    operatePremArapToolService.saveCsPremArap(csPremArapBO ,csPayPlanBO.getBusiItemId(),csPayPlanBO.getItemId());
}





/**
 * 
 * @description 获取应收应付对象
 * @version
 * @title
 * <AUTHOR> <EMAIL>
 * @param csEndorseRGPolicyVO csEndorseRGPolicyVO
 * @param customerId customerId
 * @param csPayDue csPayDue
 * @param csPayPlanBO csPayPlanBO
 * @param feeStatus feeStatus
 * @param isReissueInterest isReissueInterest
 * @param isMasterItem isMasterItem
 * @return
 */
private CsPremArapBO getPremArapBO(CsEndorseRGPolicyVO csEndorseRGPolicyVO,
		BigDecimal customerId, CsPayDueBO csPayDue, CsPayPlanPO csPayPlanBO,
		 String feeStatus, String isReissueInterest,String isMasterItem) {
	CsPremArapBO csPremArapBO = new CsPremArapBO();
	CsPremArapForQueryBO csPremArapForQueryBO = new CsPremArapForQueryBO();
	csPremArapForQueryBO.setChangeId(csEndorseRGPolicyVO.getChangeId()); //@invalid  保全变更id
	csPremArapForQueryBO.setAcceptId(csEndorseRGPolicyVO.getAcceptId()); //@invalid  受理id
	csPremArapForQueryBO.setCustomerId(customerId); //@invalid  客户id
	csPremArapForQueryBO.setPolicyChgId(csEndorseRGPolicyVO.getPolicyChgId()); //@invalid  保单变更id
	csPremArapForQueryBO.setMoneyCode(Constants.MONEY_CODE_CHINA); //@invalid  币种代码，现在一般都写成CNY即可
	csPremArapForQueryBO.setPayMode(Constants.PAYMODE_90);//@invalid 记账是90  应收是10
//@invalid 	csPremArapForQueryBO.setAccountType(Constants.FOUR); //@invalid  保单账户类型，你们要去t_policy_account_type表中查询，看看你们应该是多少

	csPremArapForQueryBO.setBusiItemId(csPayPlanBO.getBusiItemId()); //@invalid  责任组id
	csPremArapForQueryBO.setProductCode(csPayDue.getProductCode());
	csPremArapForQueryBO.setArapFlag(Constants.ARAP_FlAG_IN); //@invalid  应收应付类型，你们要去t_ar_ap表中查询  1应收 ，2应付
	if (csPayPlanBO.getBusiProdCode() != null) {
		csPremArapForQueryBO.setBusiProdCode(csPayPlanBO.getBusiProdCode()); //@invalid  险种代码
	}
	
	
	//@invalid 万能账户附加险
	if(Constants.OLD.equals(isMasterItem) && csPayDue.getSurvivalMode().compareTo(Constants.FOUR)==0){
		if (Constants.FEE_STATUS_16.equals(feeStatus)) {
			csPremArapForQueryBO.setArapFlag(Constants.ARAP_FlAG_OUT);//@invalid 应付
		}
		
		CsContractInvestPO csContractInvestBO = new CsContractInvestPO();
		csContractInvestBO.setInvestAccountType(Constants.INVEST_ACCOUNT_TYPE_2);//@invalid 915
		csContractInvestBO.setPolicyChgId(csPayDue.getPolicyChgId());
		csContractInvestBO.setOldNew(Constants.NEW);
		CsContractInvestPO csContractInvest = this.csContractInvestDao.findCsContractInvest(csContractInvestBO);
		
		ContractBusiProdPO contractBusiProd = new ContractBusiProdPO();
		contractBusiProd.setBusiItemId(csContractInvest.getBusiItemId());
		contractBusiProd.setPolicyId(csContractInvest.getPolicyId());
		ContractBusiProdPO findContractBusiProd = this.contractBusiProdDao.findContractBusiProd(contractBusiProd);
		
		ContractProductPO contractProductPO = new ContractProductPO();
		contractProductPO.setItemId(csContractInvest.getItemId());
		contractProductPO.setPolicyId(csContractInvest.getPolicyId());
		ContractProductPO findContractProduct = this.contractProductDao.findContractProduct(contractProductPO);
		
		csPremArapForQueryBO.setBusiProdCode(findContractBusiProd.getBusiProdCode());
		csPremArapForQueryBO.setBusiItemId(findContractBusiProd.getBusiItemId());
		csPremArapForQueryBO.setProductCode(findContractProduct.getProductCode());
		csPremArapForQueryBO.setPolicyCode(findContractProduct.getPolicyCode());
		
	}
	
	/**1.现金红利  记账类型*/
    if(csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_1) && feeStatus.equals(Constants.FEE_STATUS_16)){
		
		if(csPayDue.getSurvivalMode().compareTo(Constants.SURVIVAL_TYPE_2)==0 &&
				 csPayDue.getPayNum().compareTo(new BigDecimal(1))==0){
	         if(isReissueInterest.equals(Constants.NEW)){
	             csPremArapForQueryBO.setFeeType("G004680100");//@invalid 累计声息账户首期现金红利补发利息
	             csPremArapForQueryBO.setSceneCode("00442");
	         }else{
	             csPremArapForQueryBO.setFeeType("G004670100");//@invalid 累计声息账户首期现金红利记账
	             csPremArapForQueryBO.setSceneCode("00442");
	         }
		}
		else if(csPayDue.getSurvivalMode().compareTo(Constants.SURVIVAL_TYPE_2)==0 &&
			 		csPayDue.getPayNum().compareTo(new BigDecimal(1))>0){
	      	 if(isReissueInterest.equals(Constants.NEW)){
	            csPremArapForQueryBO.setFeeType("G004680200");//@invalid 累计声息账户续期现金红利补发利息
	            csPremArapForQueryBO.setSceneCode("00442");
	        }else{
	            csPremArapForQueryBO.setFeeType("G004670200");//@invalid 累计声息账户续期现金红利记账
	            csPremArapForQueryBO.setSceneCode("00442");
	        } 
	  	}
		else if(csPayDue.getSurvivalMode().compareTo(Constants.FOUR)==0 &&
		 		csPayDue.getPayNum().compareTo(new BigDecimal(1))==0){
			if(Constants.NEW.equals(isMasterItem)){
				if(isReissueInterest.equals(Constants.NEW)){
					csPremArapForQueryBO.setFeeType("G004680100");//@invalid 万能账户首期现金红利记账--主险记账--补发利息
			        csPremArapForQueryBO.setSceneCode("00443");
				}else{
					csPremArapForQueryBO.setFeeType("G004670100");//@invalid 万能账户首期现金红利记账--主险记账--本金
		            csPremArapForQueryBO.setSceneCode("00443");
				}
			}else{
				if(isReissueInterest.equals(Constants.NEW)){
					csPremArapForQueryBO.setFeeType("G004870100");//@invalid 万能账户首期现金红利记账--附加险记账--补发利息
			        csPremArapForQueryBO.setSceneCode("00443");
				}else{
					csPremArapForQueryBO.setFeeType("G004860100");//@invalid 万能账户首期现金红利记账--附加险记账--本金
		            csPremArapForQueryBO.setSceneCode("00443");
				}
			}
		}
		else if(csPayDue.getSurvivalMode().compareTo(Constants.FOUR)==0 &&
		 		csPayDue.getPayNum().compareTo(new BigDecimal(1))>0){
			if(Constants.NEW.equals(isMasterItem)){
				if(isReissueInterest.equals(Constants.NEW)){
					csPremArapForQueryBO.setFeeType("G004680200");//@invalid 万能账户续期现金红利记账--主险记账--补发利息
			        csPremArapForQueryBO.setSceneCode("00443");
				}else{
					csPremArapForQueryBO.setFeeType("G004670200");//@invalid 万能账户续期现金红利记账--主险记账--本金
			        csPremArapForQueryBO.setSceneCode("00443");
				}
			}else{
				if(isReissueInterest.equals(Constants.NEW)){
					csPremArapForQueryBO.setFeeType("G004870200");//@invalid 万能账户续期现金红利记账--附加险记账--补发利息
			        csPremArapForQueryBO.setSceneCode("00443");
				}else{
					csPremArapForQueryBO.setFeeType("G004860200");//@invalid 万能账户续期现金红利记账--附加险记账--本金
			        csPremArapForQueryBO.setSceneCode("00443");
				}
			}
		}
		
	}
    
    /**2.现金红利 收费类型 应领未领*/
    else if(csPayPlanBO.getPayPlanType().equals(Constants.PAY_PLAN_TYPE_1) && feeStatus.equals(Constants.FEE_STATUS_ZERO)){
    	csPremArapBO.setPayMode("10");//@invalid 记账是90  应收是10
        if(csPayDue.getSurvivalMode().compareTo(Constants.SURVIVAL_TYPE_2)==0){
            csPremArapForQueryBO.setFeeType("G004690000");//@invalid 累计声息余额不足
            csPremArapForQueryBO.setSceneCode("00442");
        }
        //@invalid #194955 注掉走else
        /*else if(csPayDue.getSurvivalMode().compareTo(Constants.FOUR)==0){
        	csPremArapForQueryBO.setFeeType("G004690000");//@invalid 转万能账户
            csPremArapForQueryBO.setSceneCode("00443");
        }*/
        else{//@invalid 现金
        	if(isReissueInterest.equals(Constants.NEW)){
        		csPremArapForQueryBO.setFeeType("G004680000");
                csPremArapForQueryBO.setSceneCode("00434");
        	}else{
        		csPremArapForQueryBO.setFeeType("G004670000");
                csPremArapForQueryBO.setSceneCode("00434");
        	}
        }
	}
	else{//@invalid 年金类型
		
		if(feeStatus.equals(Constants.FEE_STATUS_16)){
			
			if(csPayDue.getSurvivalMode().compareTo(Constants.SURVIVAL_MODE_ACCUMULATE)==0
					&& csPayDue.getPayNum().compareTo(new BigDecimal(1))==0){
				csPremArapForQueryBO.setFeeType("G004820100"); //@invalid 累计声息首期记账
				csPremArapForQueryBO.setSceneCode("00436");
			}
			else if(csPayDue.getSurvivalMode().compareTo(Constants.SURVIVAL_MODE_ACCUMULATE)==0
					&& csPayDue.getPayNum().compareTo(new BigDecimal(1))>0){
				csPremArapForQueryBO.setFeeType("G004820200"); //@invalid 累计声息续期记账
				csPremArapForQueryBO.setSceneCode("00436");
			}
			else if(csPayDue.getSurvivalMode().compareTo(Constants.FOUR)==0){
				if(isMasterItem!=null && isMasterItem.equals(Constants.NEW)){
					csPremArapForQueryBO.setFeeType("G004530400"); //@invalid 万能账户主险不区分首续期
					csPremArapForQueryBO.setSceneCode("00438");
				}else{
					if(csPayDue.getPayNum().compareTo(new BigDecimal(1))==0){
						csPremArapForQueryBO.setFeeType("G004820100"); //@invalid 万能账户首期记账
						csPremArapForQueryBO.setSceneCode("00438");
					}else{
						csPremArapForQueryBO.setFeeType("G004820200"); //@invalid 万能账户续期记账
						csPremArapForQueryBO.setSceneCode("00438");
					}
				}
			}
		}
		else{
			csPremArapBO.setPayMode("10");//@invalid 记账是90  应收是10
			if(csPayDue.getSurvivalMode().compareTo(Constants.SURVIVAL_MODE_ACCUMULATE)==0){
				csPremArapForQueryBO.setFeeType("G004690000"); //@invalid 累计声息账户不足补费
				csPremArapForQueryBO.setSceneCode("00436");
			}
			else if(csPayDue.getSurvivalMode().compareTo(Constants.FOUR)==0){
				//@invalid 196458 修改费用类型
				csPremArapForQueryBO.setFeeType("G004530400"); //@invalid 年金应收
				csPremArapForQueryBO.setSceneCode("00421");
			}
			else{
				csPremArapForQueryBO.setFeeType("G004530400"); //@invalid 年金现金领取
				csPremArapForQueryBO.setSceneCode("00421");
			}
			
		}
		
	}
	csPremArapBO = operatePremArapToolService.queryInfoForCsPremArap(csPremArapForQueryBO);
	return csPremArapBO;
}



/**
 * 
 * @description 更新累计生息账户信息  并增加流水信息
 * @version
 * @title
 * <AUTHOR> <EMAIL>
 * @param csPolicyAccountBO csPolicyAccountBO
 * @param transAmount transAmount
 * @param changeId  changeId
 * @param policyChgId policyChgId
 * @param acceptTime acceptTime
 * @return
 */
	private boolean updatePolicyAccount(CsPolicyAccountPO csPolicyAccountBO, BigDecimal transAmount,
			BigDecimal changeId, BigDecimal policyChgId,Date acceptTime) {
		//@invalid  更新累计生息账户信息  并增加流水信息
		boolean flag = false;
		if (csPolicyAccountBO != null&&transAmount.compareTo(new BigDecimal(0)) >= 0) {
			//@invalid  新增账户流水信息
			CsPolicyAccountTransListBO csPolicyAccountTransListBO = new CsPolicyAccountTransListBO();
			csPolicyAccountTransListBO.setChangeId(changeId);
			csPolicyAccountTransListBO.setPolicyChgId(policyChgId);
			csPolicyAccountTransListBO.setAccountId(csPolicyAccountBO.getAccountId());
			csPolicyAccountTransListBO.setTransAmount(transAmount);
			csPolicyAccountTransListBO.setOldNew(Constants.NEW);
			csPolicyAccountTransListBO.setListId(new BigDecimal(1));
			csPolicyAccountTransListBO.setTransCode(Constants.FIVE_STRING);//@invalid  保单账户t_policy_account_code
															//@invalid  表里面
															//@invalid  对应的的码值：存款取回(表中没有合适的值，暂用存款取回)
			csPolicyAccountTransListBO.setTransType("2");
			csPolicyAccountTransListBO.setOperationType(Constants.OPERATIONTYPE_ADD);
			if (acceptTime==null) {
                acceptTime=WorkDateUtil.getWorkDate();
            }
			csPolicyAccountTransListBO.setTransTime(acceptTime);
			csPolicyAccountTransListBO.setAccountBalance(csPolicyAccountBO.getInterestCapital());
			saveCsPolicyAccountTransList(csPolicyAccountTransListBO);
			
			//@invalid  新增账户流水信息  账户更新成功之后 
			flag = updateCsPolicyAccount(csPolicyAccountBO);
			
		}
		return flag;
	}
	

     /** 
     * @description 根据条件更新保单账户基本信息表信息
     * @version V1.0.0
     * <AUTHOR> <EMAIL>
     * @param    CsPolicyAccountPO 保单账户基本信息表PO
     * @return 
     */
	private boolean updateCsPolicyAccount(CsPolicyAccountPO csPolicyAccountBO) {
		csPolicyAccountDao.updateCsPolicyAccount(csPolicyAccountBO);
		return true;
	}


	/**
	 * 
	 * @description  约定年金追回处理
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPayPlanBO 险种生存给付计划表BO
	 * @param premArap premArap
	 * @param csEndorseRGPolicyVO csEndorseRGPolicyVO
	 * @param acceptTime acceptTime
	 * @param customerId customerId
	 * @param csPayDue csPayDue
	 * @param isCalInterest isCalInterest
	 * @return
	 */
private CsEndorseRGPolicyVO findPayPlanType8(CsPayPlanPO csPayPlanBO,
		PremArapPO premArap, CsEndorseRGPolicyVO csEndorseRGPolicyVO,
		Date acceptTime,BigDecimal customerId,
		CsPayDueBO csPayDue, String isCalInterest) {
			//@invalid 查询险种
			CsContractBusiProdPO csContractBusiProdBO = new CsContractBusiProdPO();
			csContractBusiProdBO.setBusiItemId(csPayPlanBO.getBusiItemId());
			csContractBusiProdBO.setOldNew(Constants.NEW);
			CsContractBusiProdPO  findCsContractBusiProdBO = this.csContractBusiProdDao.findCsContractBusiProd(csContractBusiProdBO);
			//@invalid 查询险种类型
			BusinessProductBO businessProduct = new BusinessProductBO();
			businessProduct.setBusinessPrdId(findCsContractBusiProdBO.getBusiPrdId());
			BusinessProductBO findBusinessProduct = this.pasIAS.findBusinessProduct(businessProduct);
			String businessType = findBusinessProduct.getProductCategory1(); //@invalid  获取险种类型(投连或万能)
			
			/**1.查询账户信息t_cs_contract_invest*/
    		 CsContractInvestPO csContractInvestBO = new CsContractInvestPO();
             csContractInvestBO.setBusiItemId(csPayPlanBO.getBusiItemId());
             csContractInvestBO.setOldNew(Constants.NEW);
             csContractInvestBO.setPolicyId(csPayPlanBO.getPolicyId());
             csContractInvestBO.setItemId(csPayPlanBO.getItemId());
             csContractInvestBO.setChangeId(csPayPlanBO.getChangeId());
             CsContractInvestPO csContractInvest = this.csContractInvestDao.findCsContractInvest(csContractInvestBO);
             
             /**2.查询此期发放交易记录表*/
             FundTransPO fundTrans = new FundTransPO();
             fundTrans.setUnitNumber(csPayDue.getUnitNumber());
             fundTrans.setPolicyId(csPayDue.getPolicyId());
//@invalid       fundTrans.setDealTime(csPayDue.getPayDueDate());
             FundTransPO findFundTrans = this.fundTransDao.findFundTrans(fundTrans);
			
			/**3.万能型约定年金*/
			if (Constants.PROD_BIZ_CATEGORY__20003.equals(businessType)) {
			   //@invalid 和批处理确认过  投连型约定年金是按金额进行发放
			    
			    BigDecimal interestCapital = csContractInvest.getInterestCapital();
			    
			    if(findFundTrans.getTransAmount()!=null){
			    	if(!"2".equals(isCalInterest)){
			        csContractInvest.setInterestCapital(interestCapital.add(findFundTrans.getTransAmount()));
	                csContractInvest.setOperationType(Constants.OPERATIONTYPE_UPDATE);
	                updateCsContractInvest(csContractInvest, null,acceptTime, findFundTrans.getTransAmount());
			    	}
	                if(Constants.NEW.equals(isCalInterest)){
		                saveRGPremArapCus(acceptTime,premArap.getFeeAmount(),csEndorseRGPolicyVO, customerId, Constants.FEE_STATUS_ZERO,csPayDue,csPayPlanBO,Constants.OLD,null);
	                }
			    }
			    
	              
	            csEndorseRGPolicyVO.setPremArapFeeAmount(csPayDue.getFeeAmount());
	            csEndorseRGPolicyVO.setItemId(csPayPlanBO.getItemId());
				return csEndorseRGPolicyVO;
			}
			
			/**4.投连型追回约定年金*/
			else if(Constants.PROD_BIZ_CATEGORY__20004.equals(businessType)){
				//@invalid 和批处理确认过  投连型约定年金是按比例进行发放
//@invalid 				BigDecimal instalmentProportion = csPayPlanBO.getInstalmentProportion();//@invalid 发放比例
//@invalid 				BigDecimal divide = csContractInvest.getAccumUnits().divide(instalmentProportion);
			   
			    if(findFundTrans.getTransUnits()!=null){
			        BigDecimal divide = csContractInvest.getAccumUnits().add(findFundTrans.getTransUnits());
			        csContractInvest.setAccumUnits(divide);
	                csContractInvest.setOperationType(Constants.OPERATIONTYPE_UPDATE);
	                if(!"2".equals(isCalInterest)){
	                updateCsContractInvest(csContractInvest, divide,acceptTime,null );
	                }
	                
	                if(Constants.NEW.equals(isCalInterest)){
		                saveRGPremArapCus(acceptTime,premArap.getFeeAmount(),csEndorseRGPolicyVO, customerId, Constants.FEE_STATUS_ZERO,csPayDue,csPayPlanBO,Constants.OLD,null);
	                }
			    }
				
			}
			
			csEndorseRGPolicyVO.setPremArapFeeAmount(csPayDue.getFeeAmount());
			csEndorseRGPolicyVO.setItemId(csPayPlanBO.getItemId()); 
			return csEndorseRGPolicyVO;
		}
  



/**
 * 
 * @description 保存投连账户  交易申请
 * @version
 * @title
 * <AUTHOR> <EMAIL>
 * @param csContractInvestBO csContractInvestBO
 * @param divide divide
 * @param acceptTime acceptTime
 * @param transAmount transAmount
 */
private void updateCsContractInvest(CsContractInvestPO csContractInvestBO,
		BigDecimal divide, Date acceptTime, BigDecimal transAmount) {
	 /** 1.保存账户交易记录t_cs_contract_busi_prod */
	CsFundServicePO csFundServicePO = new CsFundServicePO();
	csFundServicePO.setPolicyId(csContractInvestBO.getPolicyId()); //@invalid  保单id
	csFundServicePO.setBusiItemId(csContractInvestBO.getBusiItemId());
	if (csContractInvestBO.getBusiItemId() == null) {
		CsContractBusiProdPO csContractBusiProdPO = new CsContractBusiProdPO();
		csContractBusiProdPO.setChangeId(csContractInvestBO.getChangeId());
		csContractBusiProdPO.setBusiPrdId(csContractInvestBO.getProductId());
		
		List<CsContractBusiProdPO> csContractBusiProdPOs = csContractBusiProdDao.findAllCsContractBusiProd(csContractBusiProdPO);
		if (CollectionUtils.isNotEmpty(csContractBusiProdPOs)) {
			csFundServicePO.setBusiItemId(csContractBusiProdPOs.get(0).getBusiItemId()); //@invalid  险种id
		} else {
			csFundServicePO.setBusiItemId(new BigDecimal(0));
		}
	} 
	csFundServicePO.setItemId(csContractInvestBO.getItemId());  //@invalid  责任组id
	
	csFundServicePO.setProductId(csContractInvestBO.getProductId());  //@invalid  精算产品id
	csFundServicePO.setChangeId(csContractInvestBO.getChangeId()); //@invalid  保全变更id
	csFundServicePO.setPolicyChgId(csContractInvestBO.getPolicyChgId()); //@invalid  保单变更id
	csFundServicePO.setDistriType(Constants.DISTRI_TYPE_OUT); //@invalid  资金分配类型:转出
	csFundServicePO.setAccountCode(csContractInvestBO.getAccountCode()); //@invalid  投资基金账户代码
	csFundServicePO.setSwitchInCode(null); //@invalid  转换后基金账户代码
	csFundServicePO.setTargetId(null); //@invalid  转移/转换目标险种id
	csFundServicePO.setAssignUnit(null); //@invalid  By amount or unit
	csFundServicePO.setApplyAmount(transAmount); //@invalid  申请交易金额
	csFundServicePO.setApplyUnits(divide); //@invalid  申请交易单位数
	csFundServicePO.setApplyTime(acceptTime); //@invalid  交易申请时间
	csFundServicePO.setFundProcessStatus(
			Constants.FUND_PROCESS_STATUS_WAIT); //@invalid  交易申请处理状态 0:等待交易 1:交易完成 2:交易取消
	csFundServicePO.setMoneyCode(Constants.MONEY_CODE_CHINA); //@invalid  货币Id
	csFundServicePO.setSaFactor(null); //@invalid  费用因子
	csFundServicePO.setTransChargeFee(new BigDecimal(0)); //@invalid  基金交易费用
	csFundServicePO.setTransCode(Constants.TRANS_CODE_OUT); //@invalid  交易类型码
	csFundServicePO.setTerminalInterest(new BigDecimal(0)); //@invalid  terminal interest 
	csFundServicePO.setValidateFlag(Constants.YES_NO__NO); //@invalid  是否生效
	csFundServicePO.setTransType(new BigDecimal(1));
	
	 //@invalid 和songdf沟通，修改以下一行代码，原先是setAcceptId 2018-04-10 by tanyh
	CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
    csPolicyChangePO.setChangeId(csContractInvestBO.getChangeId());
    csPolicyChangePO.setPolicyId(csContractInvestBO.getPolicyId());
    csPolicyChangePO = csPolicyChangeDao.findCsPolicyChange(csPolicyChangePO);
    //@invalid 和songdf沟通增加以下一行代码，原先是直接csPolicyChangePO获取acceptCode 2018-04-10 by tanyh
    CsAcceptChangeBO csAcceptChangeBO=super.findCsAcceptChangeBOByAcceptId(csPolicyChangePO.getAcceptId());
    csFundServicePO.setAcceptCode(csAcceptChangeBO.getAcceptCode());
    
	csFundServiceDao.addFundService(csFundServicePO);
	csContractInvestDao.updateCsContractInvest(BeanUtils.copyProperties(CsContractInvestPO.class, csContractInvestBO));
}




   
/**
 * 
 * @description 追回计算万能账户利息
 * @version V1.0.0
 * @title
 * @<NAME_EMAIL>
 * @param param  param csContractInvestBO 保单投资连结表BO
 * @param param  param reitrieveDate
 * @param param  param acceptTime
 * @return
 */
    private BigDecimal sumInvest(CsContractInvestPO csContractInvestBO,
 Date reitrieveDate, Date acceptTime,String payPlanType,BigDecimal FeeAmount ) {

        /**1.万能险进入账户的记录*/
        FundTransPO fundTransPO = new FundTransPO();
        fundTransPO.setDataObject("start_date", reitrieveDate);
        fundTransPO.setListId(csContractInvestBO.getListId());
        if(payPlanType.equals(Constants.PAY_PLAN_TYPE_1)){
        	fundTransPO.setTransCode("44");//@invalid 现金红利
        }else{
            fundTransPO.setTransCode(Constants.TRANS_CODE__17);//@invalid 年金
        }
        //@invalid 解决一次发放两笔或多笔红利或者生存金（如20年进入一笔生存金的同时将19年和2o年的红利也发放了。这样流水就会有20年的3条记录，pay_due表里的数据为19年一条，20年2条）
        fundTransPO.setTransAmount(FeeAmount);
        List<FundTransPO> fundTransPOList = fundTransDao.findAllFundTrans(fundTransPO);

        /**2.查询交易记录表计息的记录*/
        FundSettlementPO fundSettlement = new FundSettlementPO();
        fundSettlement.setSettleDate(reitrieveDate);
        fundSettlement.setInvestId(csContractInvestBO.getListId());
        List<FundSettlementPO> FundSettlementList = this.fundSettlementDao.findFundSettlementDate(fundSettlement);

        Date endDate = null;
        BigDecimal feInitialValue = new BigDecimal(0);//@invalid  本息
        BigDecimal interest = new BigDecimal(0);//@invalid  总利息

        for (FundTransPO feFundTransPO : fundTransPOList) {
        	

            feInitialValue = feFundTransPO.getTransAmount();

            /**@invalid 查询结息记录 */
            for (FundSettlementPO feFundSettlementBO : FundSettlementList) {

                endDate = feFundSettlementBO.getSettleDate();//@invalid  结算时间
//@invalid          if (endDate.getTime() > acceptTime.getTime()) {
//@invalid              continue;
//@invalid          }
                
                if (feFundTransPO.getDealTime().getTime() > endDate.getTime()) {
                    continue;
                }
                
                BigDecimal intrestPartOne = new BigDecimal(0);//@invalid  本月利息
                BigDecimal dayOfMonth = new BigDecimal(0);
                if(DateUtilsEx.getYear(reitrieveDate)==DateUtilsEx.getYear(endDate) 
                		&& DateUtilsEx.getMonth(reitrieveDate)==DateUtilsEx.getMonth(endDate)){
                	dayOfMonth =  new BigDecimal(DateUtilsEx.getDayAmount(reitrieveDate, endDate));
                	dayOfMonth = dayOfMonth.add(Constants.ONEBig);
                }else{
                	dayOfMonth = new BigDecimal(DateUtilsEx.getMaxDayOfMonth(endDate));//@invalid  本月对总天数
                }
                BigDecimal dayOfyear = new BigDecimal(BatchDateUtil.getDayOfYear(endDate));//@invalid  此年天数
                BigDecimal interestRate = feFundSettlementBO.getInterestRate();//@invalid  计算利率
                

                /**
                 * @invalid 利息 = 本金*本月经过的天数*（年结算利率/本年天数）
                 */

                try {
                    BigDecimal divide = interestRate.divide(dayOfyear, Constants.NUM_8, RoundingMode.HALF_UP);//@invalid  年结算利率/年天数
                    intrestPartOne = feInitialValue.multiply(dayOfMonth).multiply(divide);
                    intrestPartOne = intrestPartOne.setScale(2, RoundingMode.HALF_UP);
                } catch (Exception e) {
                    //@invalid TODO Auto-generated catch block
                    e.printStackTrace();
                    throw new RuntimeException(e);
               }

                feInitialValue = feInitialValue.add(intrestPartOne);//@invalid  本息和
                interest = interest.add(intrestPartOne);//@invalid  利息和

            }
        }
        return interest;
    }
	
//@invalid 	/**
//@invalid  * 追回计算万能账户利息
//@invalid 	 * @param param  param acceptTime 
//@invalid  */
//@invalid 	private BigDecimal sumInvest(CsContractInvestPO csContractInvestBO,
//@invalid 			Date reitrieveDate, Date acceptTime) {
//@invalid 		
//@invalid 		//@invalid 查询交易记录表  计息的记录
//@invalid 		FundSettlementPO fundSettlement = new FundSettlementPO();
//@invalid 		fundSettlement.setSettleDate(reitrieveDate);
//@invalid 		fundSettlement.setInvestId(csContractInvestBO.getListId());
//@invalid 		List<FundSettlementPO> FundSettlementList = this.fundSettlementDao.findFundSettlementDate(fundSettlement);
//@invalid 		
//@invalid 		//@invalid 万能险进入账户的记录
//@invalid 		FundTransPO fundTransPO = new FundTransPO();
//@invalid 		fundTransPO.setDataObject("start_date", reitrieveDate);
//@invalid 		fundTransPO.setListId(csContractInvestBO.getListId());
//@invalid 		fundTransPO.setTransCode(Constants.TRANS_CODE__17);
//@invalid 		List<FundTransPO> fundTransPOList = fundTransDao.findAllFundTrans(fundTransPO);
//@invalid 		
//@invalid 		BigDecimal interest = new BigDecimal(0);
//@invalid 		
//@invalid 		FormulaBO feFormulaBO = new FormulaBO();
//@invalid 		/*BigDecimal feInterest = new BigDecimal(0);*/
//@invalid 		Date startDate = reitrieveDate;//@invalid 追回日期
//@invalid 		Date endDate = null;
//@invalid 		BigDecimal feInitialValue = new BigDecimal(0);
//@invalid 		for(FundTransPO feFundTransPO : fundTransPOList){
//@invalid 			feInitialValue = feFundTransPO.getTransAmount();
//@invalid 			startDate = feFundTransPO.getDealTime();//@invalid 进入账户时间
//@invalid 			for(FundSettlementPO feFundSettlementBO : FundSettlementList){
//@invalid 				if(startDate.getTime()<=feFundSettlementBO.getSettleDate().getTime()  ){
//@invalid 					feFormulaBO = new FormulaBO();
//@invalid 					/* 获取一年中的天数 */
//@invalid 					endDate = feFundSettlementBO.getSettleDate();//@invalid 结算时间
//@invalid 					BigDecimal dayOfyear = new BigDecimal(BatchDateUtil.getDayOfYear(endDate));
//@invalid 					feFormulaBO.setDayOfyear(dayOfyear);
//@invalid 					feFormulaBO.setInvestId(csContractInvestBO.getListId());
//@invalid 					feFormulaBO.setBusiItemId(csContractInvestBO.getBusiItemId());
//@invalid 					feFormulaBO.setStartDate(startDate);
//@invalid 					feFormulaBO.setInitialValue(feInitialValue);
//@invalid 					feFormulaBO.setEndDate(endDate);
//@invalid 					feFormulaBO.setInterestRate(feFundSettlementBO.getInterestRate());
//@invalid 					feFormulaBO.setInterestType("0");
//@invalid 					//@invalid 计算
//@invalid 					BigDecimal intrestPartOne = null;
//@invalid 					//@invalid intrestPartOne = calculateInvest(feFormulaBO);
//@invalid 					try {
//@invalid 						/* 1.公式的第一部分计算 */
//@invalid 						//@invalid  SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//@invalid 						/* （1-第i次领取比例）累乘 */
//@invalid 						FormulaPO tempSettlePO = new FormulaPO();
//@invalid 						tempSettlePO.setListId(csContractInvestBO.getListId());
//@invalid 						tempSettlePO.setStartDate(startDate); //@invalid  结算开始时间
//@invalid 						tempSettlePO.setEndDate(feFundSettlementBO.getSettleDate()); //@invalid  结算终止时间
//@invalid 						List<String> transCodeList = new ArrayList<String>();
//@invalid 						//@invalid TODO 查询交易类型
//@invalid 						transCodeList.add( com.nci.tunan.pa.common.Constants.TRANSACTION_CODE__22); //@invalid  保全-投连险账户部分领取
//@invalid 						transCodeList.add(com.nci.tunan.pa.common.Constants.TRANSACTION_CODE__24); //@invalid  满期/生存金/年金给付
//@invalid 						tempSettlePO.setTransCodeList(transCodeList); //@invalid  交易类型
//@invalid 						tempSettlePO = formulaDao.multiplycative(tempSettlePO);
//@invalid 						BigDecimal allMultiProportion = (BigDecimal) tempSettlePO.getData().get("all_multi_proportion");
//@invalid 						if (allMultiProportion == null) {
//@invalid 							allMultiProportion = new BigDecimal(1); //@invalid  累乘初始赋值
//@invalid 						}
//@invalid 
//@invalid 						/* 本结算期实际经过天数：是指上一结算日（不含）至当前结算日（含）或至账户注销日（不含）间实际经过的天数。 */
//@invalid 						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//@invalid 						feFormulaBO.setStartDate(sdf.parse(sdf.format(feFormulaBO.getStartDate())));
//@invalid 						feFormulaBO.setEndDate(sdf.parse(sdf.format(feFormulaBO.getEndDate())));
//@invalid 						BigDecimal daysSettlementPeriod = new BigDecimal(DateUtilsEx.getDayAmount(feFormulaBO.getStartDate(), feFormulaBO.getEndDate()))
//@invalid 								.add(BigDecimal.valueOf(1));
//@invalid 
//@invalid 
//@invalid 						/* 单利和复利公式中第一个有区别的地方 */
//@invalid 						BigDecimal changePartOne = new BigDecimal(1);
//@invalid 						
//@invalid 						changePartOne = daysSettlementPeriod.multiply(feFormulaBO.getInterestRate().divide(feFormulaBO.getDayOfyear(), Constants.NUM_8,
//@invalid 								RoundingMode.HALF_UP));
//@invalid 
//@invalid 						/*
//@invalid 						 * 期初账户（保证）价值× （1-第i次领取比例）× 本结算期实际经过天数×round（年结算利率/365，8） (或“round[(1+年保证利率)^（1/365 ）,8]^本结算期实际经过天数-1”)
//@invalid 						 */
//@invalid 						intrestPartOne = feFormulaBO.getInitialValue().multiply(allMultiProportion).multiply(changePartOne);
//@invalid 						intrestPartOne = intrestPartOne.setScale(2, RoundingMode.HALF_UP);
//@invalid 						if(feFormulaBO.getReceiveProportion()!=null){
//@invalid 							intrestPartOne = intrestPartOne.multiply(feFormulaBO.getReceiveProportion());
//@invalid 						}
//@invalid 					} catch (Exception e) {
//@invalid 						//@invalid TODO Auto-generated catch block
//@invalid 						/*feInterest = new BigDecimal(0);*/
//@invalid 						e.printStackTrace();
//@invalid 					}
//@invalid 					feInitialValue = feInitialValue.add(intrestPartOne);
//@invalid 					interest = interest.add(intrestPartOne);
//@invalid 					Calendar cal = Calendar.getInstance();
//@invalid 					cal.setTime(endDate);
//@invalid 					cal.add(Calendar.DAY_OF_YEAR, 1);
//@invalid 					startDate = cal.getTime();
//@invalid 				}
//@invalid 			}
//@invalid 			
//@invalid 			//@invalid 未跑结息的（暂定每月月底都会跑结息）
//@invalid      if (startDate.getTime() < acceptTime.getTime()) {
//@invalid 
//@invalid          int maxDayOfMonth = DateUtilsEx.getMaxDayOfMonth(startDate);//@invalid  本月对总天数
//@invalid          int day = DateUtilsEx.getDay(startDate);
//@invalid          int a = maxDayOfMonth - day;
//@invalid          endDate = DateUtilsEx.addDay(startDate, a);
//@invalid 
//@invalid          double monthAmount = DateUtilsEx.getMonthAmount(startDate, acceptTime);
//@invalid          int month = (new Double(monthAmount)).intValue() + 1;
//@invalid 
//@invalid          for (int i = 0; i < month; i++) {
//@invalid 
//@invalid              if (startDate.getTime() <= acceptTime.getTime()) {
//@invalid 
//@invalid                  //@invalid  本月利率 FormulaServiceImpl investRate
//@invalid                  FormulaBO formulaBO = new FormulaBO();
//@invalid                  formulaBO.setBusiItemId(csContractInvestBO.getBusiItemId());
//@invalid                  formulaBO.setAccountCode(csContractInvestBO.getAccountCode());
//@invalid                  formulaBO.setStartDate(startDate);
//@invalid                  formulaBO.setEndDate(endDate);
//@invalid                  BigDecimal investRate = investRate(formulaBO);
//@invalid 
//@invalid                  //@invalid  判断是否在一个月 不在一个月 就一个一个月计算
//@invalid                  if (endDate.getTime() >= acceptTime.getTime()) {
//@invalid                      endDate = acceptTime;
//@invalid                  }
//@invalid                  //@invalid  List<FormulaBO> investRates =
//@invalid                  //@invalid  formulaService.investRate(formulaBO);
//@invalid 
//@invalid                  feFormulaBO = new FormulaBO();
//@invalid                  /* 获取一年中的天数 */
//@invalid                  BigDecimal dayOfyear = new BigDecimal(BatchDateUtil.getDayOfYear(endDate));
//@invalid                  feFormulaBO.setDayOfyear(dayOfyear);
//@invalid                  feFormulaBO.setInvestId(csContractInvestBO.getListId());
//@invalid                  feFormulaBO.setBusiItemId(csContractInvestBO.getBusiItemId());
//@invalid                  feFormulaBO.setStartDate(startDate);
//@invalid                  feFormulaBO.setInitialValue(feInitialValue);
//@invalid                  feFormulaBO.setEndDate(endDate);
//@invalid                  feFormulaBO.setInterestRate(investRate);
//@invalid                  feFormulaBO.setInterestType("0");
//@invalid                  //@invalid  计算
//@invalid                  BigDecimal intrestPartOne = null;
//@invalid                  try {
//@invalid                      /* 1.公式的第一部分计算 */
//@invalid                      //@invalid  SimpleDateFormat sdf = new
//@invalid                      //@invalid  SimpleDateFormat("yyyy-MM-dd");
//@invalid                      /* （1-第i次领取比例）累乘 */
//@invalid                      FormulaPO tempSettlePO = new FormulaPO();
//@invalid                      tempSettlePO.setListId(csContractInvestBO.getListId());
//@invalid                      tempSettlePO.setStartDate(startDate); //@invalid  结算开始时间
//@invalid                      tempSettlePO.setEndDate(endDate); //@invalid  结算终止时间
//@invalid                      List<String> transCodeList = new ArrayList<String>();
//@invalid                      //@invalid TODO 查询交易类型
//@invalid                      transCodeList.add(com.nci.tunan.pa.common.Constants.TRANSACTION_CODE__22); //@invalid  保全-投连险账户部分领取
//@invalid                      transCodeList.add(com.nci.tunan.pa.common.Constants.TRANSACTION_CODE__24); //@invalid  满期/生存金/年金给付
//@invalid                      tempSettlePO.setTransCodeList(transCodeList); //@invalid  交易类型
//@invalid                      tempSettlePO = formulaDao.multiplycative(tempSettlePO);
//@invalid                      BigDecimal allMultiProportion = (BigDecimal) tempSettlePO.getData().get(
//@invalid                              "all_multi_proportion");
//@invalid                      if (allMultiProportion == null) {
//@invalid                          allMultiProportion = new BigDecimal(1); //@invalid  累乘初始赋值
//@invalid                      }
//@invalid 
//@invalid                      /*
//@invalid                       * 本结算期实际经过天数：是指上一结算日（不含）至当前结算日（含）或至账户注销日（不含）间实际经过的天数
//@invalid                       * 。
//@invalid                       */
//@invalid                      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//@invalid                      feFormulaBO.setStartDate(sdf.parse(sdf.format(feFormulaBO.getStartDate())));
//@invalid                      feFormulaBO.setEndDate(sdf.parse(sdf.format(feFormulaBO.getEndDate())));
//@invalid                      BigDecimal daysSettlementPeriod = new BigDecimal(DateUtilsEx.getDayAmount(
//@invalid                              feFormulaBO.getStartDate(), feFormulaBO.getEndDate())).add(BigDecimal.valueOf(1));
//@invalid 
//@invalid                      /* 单利和复利公式中第一个有区别的地方 */
//@invalid                      BigDecimal changePartOne = new BigDecimal(1);
//@invalid 
//@invalid                      changePartOne = daysSettlementPeriod.multiply(feFormulaBO.getInterestRate().divide(
//@invalid                              feFormulaBO.getDayOfyear(), Constants.NUM_8, RoundingMode.HALF_UP));
//@invalid 
//@invalid                      /*
//@invalid                       * 期初账户（保证）价值× （1-第i次领取比例）×
//@invalid                       * 本结算期实际经过天数×round（年结算利率/365，8）
//@invalid                       * (或“round[(1+年保证利率)^（1/365 ）,8]^本结算期实际经过天数-1”)
//@invalid                       */
//@invalid                      intrestPartOne = feFormulaBO.getInitialValue().multiply(allMultiProportion)
//@invalid                              .multiply(changePartOne);
//@invalid                      intrestPartOne = intrestPartOne.setScale(2, RoundingMode.HALF_UP);
//@invalid                      if (feFormulaBO.getReceiveProportion() != null) {
//@invalid                          intrestPartOne = intrestPartOne.multiply(feFormulaBO.getReceiveProportion());
//@invalid                      }
//@invalid                  } catch (Exception e) {
//@invalid                      //@invalid TODO Auto-generated catch block
//@invalid                      /* feInterest = new BigDecimal(0); */
//@invalid                      e.printStackTrace();
//@invalid                  }
//@invalid                  feInitialValue = feInitialValue.add(intrestPartOne);
//@invalid                  interest = interest.add(intrestPartOne);
//@invalid                  Calendar cal = Calendar.getInstance();
//@invalid                  cal.setTime(endDate);
//@invalid                  cal.add(Calendar.DAY_OF_YEAR, 1);
//@invalid                  startDate = cal.getTime();
//@invalid 
//@invalid                  maxDayOfMonth = DateUtilsEx.getMaxDayOfMonth(startDate);//@invalid  本月对总天数
//@invalid                  day = DateUtilsEx.getDay(startDate);
//@invalid                  endDate = DateUtilsEx.addDay(startDate, (maxDayOfMonth - day));
//@invalid              }//@invalid  if
//@invalid 
//@invalid          }//@invalid  for
//@invalid      }
//@invalid 		}
//@invalid 		
//@invalid 			return interest;
//@invalid 	}
	
	
//@invalid 	/**
//@invalid 	 * 万能账户对结算利率
//@invalid 	 * @param param  param formulaBO
//@invalid 	 * @return
//@invalid 	 */
//@invalid 	private BigDecimal investRate(FormulaBO formulaBO) {
//@invalid 		BigDecimal annualInterestRate = null;
//@invalid 		ContractBusiProdPO contractBusiProdPara = new ContractBusiProdPO();
//@invalid 		contractBusiProdPara.setBusiItemId(formulaBO.getBusiItemId());
//@invalid 		ContractBusiProdPO contractBusiProdReturn = contractBusiProdDao.findContractBusiProdByBusiItemId(contractBusiProdPara);
//@invalid 		//@invalid  调用营销接口查询公布的（保证利率/结算利率）
//@invalid 		MmsUniversalRateQueryReqVO mmsUniversalRateQueryReqVO = new MmsUniversalRateQueryReqVO();
//@invalid 		mmsUniversalRateQueryReqVO.setBusinessPrdId(contractBusiProdReturn.getBusiPrdId());
//@invalid 		mmsUniversalRateQueryReqVO.setFundCode(formulaBO.getAccountCode());
//@invalid 		mmsUniversalRateQueryReqVO.setStartDate(formulaBO.getStartDate());
//@invalid 		mmsUniversalRateQueryReqVO.setEndDate(formulaBO.getEndDate());
//@invalid 		mmsUniversalRateQueryReqVO.setInterestRateType("2"); //@invalid  利率类型为（结算/保证）利率
//@invalid 		logger.info(XmlHelper.classToXml(mmsUniversalRateQueryReqVO));
//@invalid 		MmsUniversalRateQueryResVO mmsUniversalRateQueryResVO = prdIAS
//@invalid 				.prdimmsuniversalratequeryuccuniversalRateQuery(mmsUniversalRateQueryReqVO);
//@invalid 		logger.info(XmlHelper.classToXml(mmsUniversalRateQueryResVO));
//@invalid 		List<MmsUniversalRateResVO> mmsUniversalRateResVOList = null;
//@invalid 		if (mmsUniversalRateQueryResVO.getMmsUniversalRateResVOList() != null) {
//@invalid 			mmsUniversalRateResVOList = mmsUniversalRateQueryResVO.getMmsUniversalRateResVOList();
//@invalid 			
//@invalid 		} else {
//@invalid 			logger.debug("---FormulaService---------万能保单结算批处理---营销查询万能公布利率接口---返回为空------");
//@invalid 		}
//@invalid 		
//@invalid 		/* 若果查询的公布的结算利率信息不为空，则给保（结算/保证）利率排序，按结算期间起始日期从大到小排序 */
//@invalid 		if (mmsUniversalRateResVOList != null && mmsUniversalRateResVOList.size() > 0) {
//@invalid 			annualInterestRate = mmsUniversalRateResVOList.get(0).getAnnualInterestRate();
//@invalid 		}
//@invalid 		return annualInterestRate;
//@invalid 		
//@invalid 	}

//@invalid 	/**
//@invalid 	 * @invalid TODO 追回累积生息
//@invalid 	 * @param param  param csPolicyAccountOld
//@invalid 	 * @param param  param retDate
//@invalid 	 * @param param  param appDate
//@invalid 	 * @return
//@invalid 	 */
//@invalid 
//@invalid private BigDecimal getRuturnAmountForAcc(CsPolicyAccountPO csPolicyAccount,
//@invalid 			Date retDate, Date appDate) {
//@invalid 		BigDecimal retAmount = new BigDecimal(0);
//@invalid 		SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
//@invalid 		int year = Integer.valueOf(sdf.format(retDate));
//@invalid 		int dayOfYear;
//@invalid 		if (year % Constants.FOURInt == 0 && year % Constants.CONSTANTS_100 != 0 || year % Constants.CONSTANTS_400 == 0) {
//@invalid 			dayOfYear = com.nci.tunan.pa.common.Constants.DAY_OF_YEAR__366;
//@invalid 		} else {
//@invalid 			dayOfYear = com.nci.tunan.pa.common.Constants.DAY_OF_YEAR__365;
//@invalid 		}
//@invalid 		
//@invalid 		ContractBusiProdPO contractBusiProdPO=new ContractBusiProdPO();
//@invalid  	contractBusiProdPO.setBusiItemId(csPolicyAccount.getBusiItemId());
//@invalid  	contractBusiProdPO=contractBusiProdDao.findContractBusiProd(contractBusiProdPO);
//@invalid  MmsInterestRateReqVO inputData=new MmsInterestRateReqVO();
//@invalid  if(csPolicyAccount.getAccountType().equals(com.nci.tunan.pa.common.Constants.POLICY_ACCOUNT_TYPE__CASH_BONUS)){
//@invalid      inputData.setInterestRateType("06");//@invalid 现金红利
//@invalid  }else{
//@invalid      inputData.setInterestRateType("22");//@invalid 累计生息
//@invalid  }
//@invalid  inputData.setStartDate(retDate);
//@invalid  inputData.setBusinessPrdId(contractBusiProdPO.getBusiPrdId());
//@invalid 		List<MmsInterestRateVO> msInterestRateVOs = prdIAS.prdimmsinterestratequeryuccinterestRateQuery(inputData).getMmsInterestRateVOList();
//@invalid 		BigDecimal rate = msInterestRateVOs.get(0).getInterestRate();
//@invalid 		double passDay = 0;
//@invalid 		if(retDate.getTime()==appDate.getTime()){
//@invalid 			passDay = DateUtilsEx.getDayAmount(retDate, appDate);
//@invalid 		}else{
//@invalid 			passDay = DateUtilsEx.getDayAmount(retDate, appDate);
//@invalid 		}
//@invalid 		
//@invalid 		
//@invalid 		double partInsterest = rate.doubleValue() / dayOfYear * passDay * csPolicyAccount.getInterestBalance().doubleValue();
//@invalid 		retAmount = new BigDecimal(partInsterest);
//@invalid 		return retAmount.setScale(2, RoundingMode.HALF_UP);
//@invalid 	}


    /**
     * 
     * @description 追回累积生息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csPolicyAccount v
     * @param retDate csPolicyAccount
     * @param appDate appDate
     * @return
     */
//private BigDecimal getRuturnAmountForAcc(CsPolicyAccountPO csPolicyAccount,
//			Date retDate, Date appDate) {
//	
//		BigDecimal InterestBalance=csPolicyAccount.getInterestBalance();
//	    BigDecimal amountLix = new BigDecimal(0);
//	    List<CsPolicyAccountTransListPO> findAllCsPolicyAccountTransList = new ArrayList<CsPolicyAccountTransListPO>();
//		CsPolicyAccountTransListPO csPolicyAccountTransList = new CsPolicyAccountTransListPO();
//		csPolicyAccountTransList.setAccountId(csPolicyAccount.getAccountId());
//		csPolicyAccountTransList.setChangeId(csPolicyAccount.getChangeId());
//		csPolicyAccountTransList.setPolicyChgId(csPolicyAccount.getPolicyChgId());
//		csPolicyAccountTransList.setTransType(Constants.TRANS_TYPE_IN);//@invalid 入账
//		csPolicyAccountTransList.setOldNew(Constants.OLD);
//		if(csPolicyAccount.getAccountType().equals(com.nci.tunan.pa.common.Constants.POLICY_ACCOUNT_TYPE__CASH_BONUS)){
//			findAllCsPolicyAccountTransList = this.csPolicyAccountTransListDao.findAllCsPolicyAccountTransList(csPolicyAccountTransList);
//	    }else{
//			findAllCsPolicyAccountTransList = this.csPolicyAccountTransListDao.findAllCsPolicyAccountTransList(csPolicyAccountTransList);  
//	    }
//		if(findAllCsPolicyAccountTransList.size()>0){
//			for(CsPolicyAccountTransListPO csPoAccTrs : findAllCsPolicyAccountTransList){
//				if(csPoAccTrs.getTransTime().getTime()>=retDate.getTime() 
//						&& csPoAccTrs.getTransCode().equals("6")){
//					double passDay = 0;
//					Date endDate = csPoAccTrs.getTransTime();
//					if(DateUtilsEx.getYear(retDate)==DateUtilsEx.getYear(endDate) 
//	                		&& DateUtilsEx.getMonth(retDate)==DateUtilsEx.getMonth(endDate)){
//	                	passDay = DateUtilsEx.getDayAmount(retDate, endDate);
//	                	passDay = passDay+1;
//	                }else{
//	                	passDay = DateUtilsEx.getMaxDayOfMonth(endDate);//@invalid  本月对总天数
//	                }
//					
//					BigDecimal retAmount = new BigDecimal(0);
//					SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
//					int year = Integer.valueOf(sdf.format(retDate));
//					int dayOfYear;
//					if (year % Constants.FOURInt == 0 && year % Constants.CONSTANTS_100 != 0 || year % Constants.CONSTANTS_400 == 0) {
//						dayOfYear = com.nci.tunan.pa.common.Constants.DAY_OF_YEAR__366;
//					} else {
//						dayOfYear = com.nci.tunan.pa.common.Constants.DAY_OF_YEAR__365;
//					}
//					
//					ContractBusiProdPO contractBusiProdPO=new ContractBusiProdPO();
//			    	contractBusiProdPO.setBusiItemId(csPolicyAccount.getBusiItemId());
//			    	contractBusiProdPO=contractBusiProdDao.findContractBusiProd(contractBusiProdPO);
//			        MmsInterestRateReqVO inputData=new MmsInterestRateReqVO();
//			        if(csPolicyAccount.getAccountType().equals(com.nci.tunan.pa.common.Constants.POLICY_ACCOUNT_TYPE__CASH_BONUS)){
//			            inputData.setInterestRateType(Constants.INTERESTRATETYPE_06);//@invalid 现金红利
//			        }else{
//			            inputData.setInterestRateType(Constants.INTERESTRATETYPE_22);//@invalid 累计生息
//			        }
//			        inputData.setStartDate(retDate);
//			        inputData.setBusinessPrdId(contractBusiProdPO.getBusiPrdId());
//					List<MmsInterestRateVO> msInterestRateVOs = prdIAS.prdimmsinterestratequeryuccinterestRateQuery(inputData).getMmsInterestRateVOList();
//					BigDecimal rate = msInterestRateVOs.get(0).getInterestRate();
//					
//					double partInsterest = rate.doubleValue() / dayOfYear * passDay * InterestBalance.doubleValue();
//					BigDecimal interest = new BigDecimal(partInsterest);
//					amountLix = amountLix.add(interest);
//					logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--rate" + rate);
//	                logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--dayOfyear:"+dayOfYear);
//	                logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--passDay:"+passDay);
//	                logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--partInsterest:"+interest);
//	                logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--InterestBalance--:"+InterestBalance);
//					InterestBalance=InterestBalance.add(interest);
//				}
//			}
//		}
//		
//		
//		return amountLix.setScale(2, RoundingMode.HALF_UP);
//	}


/**
 * 
 * @description 计算追回金额对应的利息
 * @version V1.0.0
 * @title
 * @<NAME_EMAIL>
 * @param csPolicyAccount
 * @param retDate 进入账户的日期
 * @return RG需要追回的利息
 */
private BigDecimal getRuturnAmountForAcc(CsPolicyAccountPO csPolicyAccount, Date retDate) {
    logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--start---");
    logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--policyChgId" + csPolicyAccount.getPolicyChgId());
    logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--accountId" + csPolicyAccount.getAccountId());
    logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--InterestBalance" + csPolicyAccount.getInterestBalance());
    
    BigDecimal backInterest = new BigDecimal(0);//追回利息
    List<CsPolicyAccountTransListPO> findAllCsPolicyAccountTransList = new ArrayList<CsPolicyAccountTransListPO>();
    CsPolicyAccountTransListPO csPolicyAccountTransList = new CsPolicyAccountTransListPO();
    csPolicyAccountTransList.setAccountId(csPolicyAccount.getAccountId());
    csPolicyAccountTransList.setChangeId(csPolicyAccount.getChangeId());
    csPolicyAccountTransList.setPolicyChgId(csPolicyAccount.getPolicyChgId());
    csPolicyAccountTransList.setTransType(Constants.TRANS_TYPE_IN);// 入账
    csPolicyAccountTransList.setOldNew(Constants.OLD);
    csPolicyAccountTransList.setTransCode(Constants.POLICY_ACCOUNT_CODE_6);// 账户结息
    csPolicyAccountTransList.set("start_date", retDate);
    findAllCsPolicyAccountTransList = this.csPolicyAccountTransListDao.findCsPolicyAccountTransListForRG(csPolicyAccountTransList);

    if (findAllCsPolicyAccountTransList.size() > 0) {
        for (CsPolicyAccountTransListPO csPoAccTrs : findAllCsPolicyAccountTransList) {

            BigDecimal passDay = BigDecimal.ZERO;
            Date endDate = csPoAccTrs.getTransTime();
            if (DateUtilsEx.getYear(retDate) == DateUtilsEx.getYear(endDate)
                    && DateUtilsEx.getMonth(retDate) == DateUtilsEx.getMonth(endDate)) {
                passDay = new BigDecimal(DateUtilsEx.getDayAmount(retDate, endDate)).add(BigDecimal.ONE);
            } else {
                passDay = new BigDecimal(DateUtilsEx.getMaxDayOfMonth(endDate));// 本月对总天数
            }

            /** 查询年结算利率 Start **/
            BigDecimal rate = BigDecimal.ZERO;
            CsQueryParamPO queryParamPO = new CsQueryParamPO();
            if (csPolicyAccount.getAccountType() != null
                    && csPolicyAccount.getAccountType().compareTo(Constants.POLICY_ACCOUNT_TYPE__CASH_BONUS) == 0) {
                queryParamPO.setInterestRateType("06");// 现金红利
            } else {
                queryParamPO.setInterestRateType("22");// 累计生息
            }
            queryParamPO.setEffectiveDate(endDate);
            queryParamPO.setEndDate(DateUtilsEx.addDay(endDate, -1));
            List<CsQueryParamPO> rateList = csEntryDao.findProductInterestRate(queryParamPO);
            if (rateList.size() > 0) {
                rate = rateList.get(0).getInterestRate();
            }
            logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--rate" + rate);
            /** 查询年结算利率 End **/

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
            int year = Integer.valueOf(sdf.format(endDate));
            BigDecimal dayOfYear = BigDecimal.ZERO;
            if (year % Constants.FOURInt == 0 && year % Constants.CONSTANTS_100 != 0 || year % Constants.CONSTANTS_400 == 0) {
                dayOfYear = new BigDecimal(com.nci.tunan.pa.common.Constants.DAY_OF_YEAR__366);
            } else {
                dayOfYear = new BigDecimal(com.nci.tunan.pa.common.Constants.DAY_OF_YEAR__365);
            }
            /*
             * 利息 = 本金*本月经过的天数*（年结算利率/本年天数）
             */
            BigDecimal retAmount = BigDecimal.ZERO;
            try {
                BigDecimal rateDivide = rate.divide(dayOfYear, Constants.NUM_8, RoundingMode.HALF_UP);
                retAmount = csPolicyAccount.getInterestBalance().add(backInterest).multiply(passDay).multiply(rateDivide);
                retAmount = retAmount.setScale(2, RoundingMode.HALF_UP);
            } catch (Exception e) {
                //@invalid TODO Auto-generated catch block
                e.printStackTrace();
                throw new RuntimeException(e);
            }
            logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--dayOfyear"+dayOfYear);
            logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--passDay"+passDay);
            logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--retAmount"+retAmount);
            backInterest = backInterest.add(retAmount);
        }
    }

    logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--追回利息：" + backInterest);
    logger.info("CsEndorseRGServiceImpl--getRuturnAmountForAcc--end---");
    return backInterest;
}



/**
 * 
 * @description 抄单 关联保单--投连险 账户相关 
 * @version
 * @title
 * <AUTHOR> <EMAIL>
 * @param changeId changeId
 * @param policyChgId policyChgId
 * @param policyCode policyCode
 * @return
 */
	private boolean copyToPrivateCsInvests(BigDecimal changeId,
			BigDecimal policyChgId, String policyCode) {
		//@invalid  1 查询变更主表获得 policyid
		ContractMasterPO contractMast = new ContractMasterPO();
		contractMast.setPolicyCode(policyCode);
		ContractMasterPO findContractMaster = this.contractMasterDao.findContractMaster(contractMast);
		
		//@invalid  查询 投连账户的表
		BigDecimal policyId = findContractMaster.getPolicyId();
		CsContractInvestBO csContractInvestBO = new CsContractInvestBO();
		csContractInvestBO.setPolicyId(policyId);
		List<CsContractInvestBO> contractInvestBOs = pasIAS.findCsContractInvests(csContractInvestBO);
		for (CsContractInvestBO csContractInvest : contractInvestBOs) {
			BigDecimal listId = csContractInvest.getListId();
			//@invalid  查询 保全的投连账户信息
			CsContractInvestPO csContractInvestPO = new CsContractInvestPO();
			csContractInvestPO.setChangeId(changeId);
			csContractInvestPO.setListId(listId);
			List<CsContractInvestPO> findAllCsContractInvest = this.csContractInvestDao.findAllCsContractInvest(csContractInvestPO);
			//@invalid  如果在保全中查询不到信息 则抄单
			if (CollectionUtils.isEmpty(findAllCsContractInvest)) {
				//@invalid  插入数据 为保全数据赋值
				//@invalid  新旧标记
				csContractInvest.setOldNew(Constants.OLD);
				//@invalid  操作类型
				csContractInvest.setOperationType(Constants.OPERATIONTYPE_NOCHANGE);
				//@invalid  changeid
				csContractInvest.setChangeId(changeId);
				//@invalid  policychgid
				csContractInvest.setPolicyChgId(policyChgId);
				//@invalid  保存标记为old的记录
				csContractInvestDao.addCsContractInvest(BeanUtils.copyProperties(CsContractInvestPO.class, csContractInvest));

				//@invalid  保存 标记为为new的记录
				csContractInvest.setOldNew(Constants.NEW);
				csContractInvestDao.addCsContractInvest(BeanUtils.copyProperties(CsContractInvestPO.class, csContractInvest));
			}
		}

		return true;
	}
	

   
	
	public IPayDueDao getPayDueDao() {
		return payDueDao;
	}

	public void setPayDueDao(IPayDueDao payDueDao) {
		this.payDueDao = payDueDao;
	}

	public ICsContractInvestDao getCsContractInvestDao() {
		return csContractInvestDao;
	}

	public void setCsContractInvestDao(ICsContractInvestDao csContractInvestDao) {
		this.csContractInvestDao = csContractInvestDao;
	}

	public ICsFundServiceDao getCsFundServiceDao() {
		return csFundServiceDao;
	}

	public void setCsFundServiceDao(ICsFundServiceDao csFundServiceDao) {
		this.csFundServiceDao = csFundServiceDao;
	}

	public IPremArapDao getPremArapDao() {
		return premArapDao;
	}

	public void setPremArapDao(IPremArapDao premArapDao) {
		this.premArapDao = premArapDao;
	}
	
	public ICsInsuredListDao getCsInsuredListDao() {
		return csInsuredListDao;
	}

	public void setCsInsuredListDao(ICsInsuredListDao csInsuredListDao) {
		this.csInsuredListDao = csInsuredListDao;
	}

	public IPolicyRetrieveDao getPolicyRetrieveDao() {
		return policyRetrieveDao;
	}

	public void setPolicyRetrieveDao(IPolicyRetrieveDao policyRetrieveDao) {
		this.policyRetrieveDao = policyRetrieveDao;
	}
	
	public ICustomerDao getCustomerDao() {
        return customerDao;
    }

    public void setCustomerDao(ICustomerDao customerDao) {
        this.customerDao = customerDao;
    }
	public IContractInvestDao getContractInvestDao() {
		return contractInvestDao;
	}
	public void setContractInvestDao(IContractInvestDao contractInvestDao) {
		this.contractInvestDao = contractInvestDao;
	}
	public IFundSettlementDao getFundSettlementDao() {
		return fundSettlementDao;
	}
	public void setFundSettlementDao(IFundSettlementDao fundSettlementDao) {
		this.fundSettlementDao = fundSettlementDao;
	}
	public IPolicyAccountTransListDao getPolicyAccountTransListDao() {
		return policyAccountTransListDao;
	}
	public void setPolicyAccountTransListDao(
			IPolicyAccountTransListDao policyAccountTransListDao) {
		this.policyAccountTransListDao = policyAccountTransListDao;
	}
	public IPolicyChangeDao getPolicyChangeDao() {
		return policyChangeDao;
	}
	public void setPolicyChangeDao(IPolicyChangeDao policyChangeDao) {
		this.policyChangeDao = policyChangeDao;
	}
	public PRDServiceImpl getPrdIAS() {
		return prdIAS;
	}
	public void setPrdIAS(PRDServiceImpl prdIAS) {
		this.prdIAS = prdIAS;
	}

	public IFormulaDao getFormulaDao() {
		return formulaDao;
	}

	public void setFormulaDao(IFormulaDao formulaDao) {
		this.formulaDao = formulaDao;
	}

	@Override
     /** 
     * @description 根据条件更新保单投资连结表信息
     * @version V1.0.0
     * <AUTHOR> <EMAIL>
     * @param    CsContractInvestBO 保单投资连结表BO
     * @return 
     */
	public void updateContractInvest(CsContractInvestBO contractInvest) {
		//@invalid TODO Auto-generated method stub
		CsContractInvestPO contractInvestPO = BeanUtils.copyProperties(CsContractInvestPO.class, contractInvest);
		this.csContractInvestDao.updateCsContractInvest(contractInvestPO);
	}

	/**
	 * Redmine:47068(关于累积生息与万能账户补结息需求)
	 * @description  根据现金红利发放时间，返回分红日最晚的分红日期
	 * @version  v1.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param    bonusAllocateList bonusAllocateList
	 * @return
	 * @throws ParseException 
	 */
	private Date sortCsBonusAllocate(List<CsBonusAllocatePO> bonusAllocateList){
		Date lastBonusDate = null;
		for (CsBonusAllocatePO csBonusAllocatePO : bonusAllocateList) {
//	@invalid		/*测试用
//	@invalid		 * SimpleDateFormat sdfs = new SimpleDateFormat("yyyy-MM-dd");
//	@invalid		csBonusAllocatePO.setAllocateDueDate(sdfs.parse("2021-03-26"));*/
			if(null == lastBonusDate){
				lastBonusDate = csBonusAllocatePO.getAllocateDueDate();
			}else{
				if(lastBonusDate.before(csBonusAllocatePO.getAllocateDueDate())){
					lastBonusDate = csBonusAllocatePO.getAllocateDueDate();
				}
			}
		}
		return lastBonusDate;
	}

	/**
	 * 
	 * @description  获取分红分配日期
	 * @version  v1.1
	 * @title
	 * <AUTHOR>
	 * @param  csPayDue
	 * @return
	 */
	private Date getCalDate(CsPayDueBO csPayDue) {
		Date allocateDate = null;
		CsBonusAllocatePO csBonusAllocatePO = new CsBonusAllocatePO();
		csBonusAllocatePO.setChangeId(csPayDue.getChangeId());
		csBonusAllocatePO.setPolicyChgId(csPayDue.getPolicyChgId());
		csBonusAllocatePO.setPolicyId(csPayDue.getPolicyId());
		csBonusAllocatePO.setBusiItemId(csPayDue.getBusiItemId());
		csBonusAllocatePO.setOldNew(Constants.OLD);
		csBonusAllocatePO.setAllocateDueDate(csPayDue.getPayDueDate());
		List<CsBonusAllocatePO> csBonusAllocatePOs = csBonusAllocateDao.findAllCsBonusAllocate(csBonusAllocatePO);
		for (CsBonusAllocatePO csBonAllPO : csBonusAllocatePOs) {
			if (null == csBonAllPO.getValidFlag() || BigDecimal.ZERO.compareTo(csBonAllPO.getValidFlag()) != 0) {
				allocateDate = csBonAllPO.getAllocateDate();
			}
		}
		return allocateDate;
	}
	
	/**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param survivalDeductionTaskPO 对象
     * @return List<SurvivalDeductionTaskPO> 查询结果List
     */
	@Override
	public List<SurvivalDeductionTaskPO> findAllSurvivalDeductionTask(SurvivalDeductionTaskPO survivalDeductionTaskPO){
		List<SurvivalDeductionTaskPO> survivalDeductionTaskPOs = survivalDeductionTaskDao.findAllSurvivalDeductionTask(survivalDeductionTaskPO);
		return survivalDeductionTaskPOs;
	}
	
	
	/**
	 * 
	 * @description 约定变更信息回退
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csPayDue
	 */
    private void saveRGForCsRepcontPay(CsPayDueBO csPayDue) {
        logger.info("CsEndorseRGServiceImpl.saveRGForCsRepcontPay-------start");
        Date payDueDate = csPayDue.getPayDueDate();
        CsPrecontPayPO csPrecontPayPO = new CsPrecontPayPO();
        csPrecontPayPO.setPolicyChgId(csPayDue.getPolicyChgId());
        csPrecontPayPO.setPlanId(csPayDue.getPlanId());
        csPrecontPayPO.setPrecontTime(payDueDate);
        csPrecontPayPO.setOldNew(Constants.NEW);
        csPrecontPayPO = csPrecontPayDao.findCsPrecontPayByPrecontTime(csPrecontPayPO);
        if (csPrecontPayPO != null && csPrecontPayPO.getLogId() != null) {
            csPrecontPayPO.setOperationType(Constants.OPERATIONTYPE_UPDATE);
            csPrecontPayPO.setPrecontStatus("0");// @invalid 待生效
            csPrecontPayDao.updateCsPrecontPay(csPrecontPayPO);

            CsPayPlanPO csPayPlanPO = new CsPayPlanPO();
            csPayPlanPO.setPolicyChgId(csPayDue.getPolicyChgId());
            csPayPlanPO.setPlanId(csPayDue.getPlanId());
            csPayPlanPO.setOldNew(Constants.NEW);
            csPayPlanPO = csPayPlanDao.findCsPayPlan(csPayPlanPO);
            if (csPayPlanPO != null && csPayPlanPO.getPlanId() != null) {
                logger.info("CsEndorseRGServiceImpl.saveRGForCsRepcontPay-------约定变更信息回退--PlanFreq={},InstalmentAmount={}",
                        csPrecontPayPO.getOldPlanFreq(), csPrecontPayPO.getOldInstalmentAmount());
                csPayPlanPO.setPlanFreq(csPrecontPayPO.getOldPlanFreq());
                csPayPlanPO.setInstalmentAmount(csPrecontPayPO.getOldInstalmentAmount());
                csPayPlanPO.setOperationType(Constants.OPERATIONTYPE_UPDATE);
                csPayPlanDao.updateCsPayPlan(csPayPlanPO);
            }
            
            /**@invalid 重新推算领取结束日期*/
            CsPayPlanBO planBO = BeanUtils.copyProperties(CsPayPlanBO.class, csPayPlanPO);
            PrdCalcAnnuTimeResVO calcNextPayDueDate = csCalcSAMService.calcNextPayDueDate(planBO);
             
            if (calcNextPayDueDate!=null&&calcNextPayDueDate.getPaymentEndTime() != null) {
                csPayPlanPO.setEndDate(calcNextPayDueDate.getPaymentEndTime());
                csPayPlanDao.updateCsPayPlan(csPayPlanPO);
            }
            
            CsContractProductPO csConProductPO = new CsContractProductPO();
            csConProductPO.setPolicyChgId(csPayDue.getPolicyChgId());
            csConProductPO.setPolicyId(csPayDue.getPolicyId());
            csConProductPO.setItemId(csPayDue.getItemId());
            csConProductPO.setOldNew(Constants.NEW);
            csConProductPO = csContractProductDao.findCsContractProduct(csConProductPO);

            if (csConProductPO.getLogId() != null) {
                csConProductPO.setOperationType(Constants.OPERATIONTYPE_UPDATE);
                csConProductPO.setPayFreq(csPrecontPayPO.getOldPlanFreq());
                csContractProductDao.updateCsContractProduct(csConProductPO);
            }
        }
    }
}
