package com.nci.tunan.cs.impl.csInforQuery.service;

import java.util.List;

import com.nci.tunan.cs.model.bo.CsAcceptChangeBO;
import com.nci.tunan.cs.model.bocomp.CsAcceptInformationBO;
/**
 * 
 * @description  (核保-保全系统)保全申请信息查询service 
 * @<NAME_EMAIL>
 * @date 2015-8-10 上午9:23:33 
 * @.belongToModule 保全子系统
 */
public interface ICSInformationQueryService {
	/**
	 * 
	 * @description 查询申请信息
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param bo 受理bo
	 * @return
	 */
	public CsAcceptInformationBO theInformationQueryService(CsAcceptChangeBO bo);
	/**
	 * 
	 * @description :flag为 2 ，传入List<String> 为保全受理号，查询保全受理表返回Map<String,String>
	 *              保全受理号，保全状态，存入Bo。
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param acceptList 申请list
	 * @return
	 */
	public CsAcceptInformationBO theAcceptChangeQueryService(List<String> acceptList);
	
}
