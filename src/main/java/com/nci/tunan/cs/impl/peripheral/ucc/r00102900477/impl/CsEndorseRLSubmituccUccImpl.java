package com.nci.tunan.cs.impl.peripheral.ucc.r00102900477.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.core.common.dao.IOrderServiceDao;
import com.nci.core.common.impl.util.CodeMapperUtils;
import com.nci.core.common.interfaces.model.po.OrderServicePO;
import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.common.SERVICE;
import com.nci.tunan.cs.dao.IBankCodeLengthDao;
import com.nci.tunan.cs.dao.ICsAcceptChangeDao;
import com.nci.tunan.cs.dao.ICsApplicationDao;
import com.nci.tunan.cs.dao.ICsContractBusiProdDao;
import com.nci.tunan.cs.dao.ICsPaymentAdjustDao;
import com.nci.tunan.cs.dao.ICsPolicyAccountStreamDao;
import com.nci.tunan.cs.dao.ICsPolicyChangeDao;
import com.nci.tunan.cs.dao.ICsPolicyStateDao;
import com.nci.tunan.cs.dao.ICsPremArapDao;
import com.nci.tunan.cs.dao.ILoanProductCfgDao;
import com.nci.tunan.cs.impl.commonFlow.service.ICusAcceptService;
import com.nci.tunan.cs.impl.commonFlow.ucc.ICusAcceptUCC;
import com.nci.tunan.cs.impl.commonPage.service.IBankAccountInfoService;
import com.nci.tunan.cs.impl.csItem.service.ICsEndorseLNService;
import com.nci.tunan.cs.impl.csItem.service.ICsEndorseRLService;
import com.nci.tunan.cs.impl.csItem.ucc.ICsEndorseRLUCC;
import com.nci.tunan.cs.impl.outterdealfa.ucc.IOutterDealUCC;
import com.nci.tunan.cs.impl.peripheral.service.common.ICsEndorseForOutterService;
import com.nci.tunan.cs.impl.peripheral.service.common.OutterCsEndorse;
import com.nci.tunan.cs.impl.peripheral.ucc.r00102900477.ICsEndorseRLSubmituccUcc;
import com.nci.tunan.cs.imports.IPRDService;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102900477.vo.InputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102900477.vo.NsRenew;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102900477.vo.OutputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102900477.vo.RenewList;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102900477.vo.VerifyInfoList;
import com.nci.tunan.cs.model.bo.CsAcceptChangeBO;
import com.nci.tunan.cs.model.bo.CsPremArapBO;
import com.nci.tunan.cs.model.bocomp.CsAcceptBO;
import com.nci.tunan.cs.model.bocomp.CsAcceptChangeCompBO;
import com.nci.tunan.cs.model.bocomp.PolicyRevivalBO;
import com.nci.tunan.cs.model.po.BankOfDepositPO;
import com.nci.tunan.cs.model.po.CsAcceptChangePO;
import com.nci.tunan.cs.model.po.CsPolicyAccountStreamPO;
import com.nci.tunan.cs.model.po.CsPolicyChangePO;
import com.nci.tunan.cs.model.vo.CsBankAccountVO;
import com.nci.tunan.cs.model.vo.NsRenewVO;
import com.nci.tunan.pa.dao.IBankAccountDao;
import com.nci.tunan.pa.dao.IBusinessProductDao;
import com.nci.tunan.pa.dao.IContractBusiProdDao;
import com.nci.tunan.pa.dao.IContractMasterDao;
import com.nci.tunan.pa.dao.ICsLoanReasonDao;
import com.nci.tunan.pa.dao.ICustomerDao;
import com.nci.tunan.pa.dao.IInsuredListDao;
import com.nci.tunan.pa.dao.IPolicyAccountStreamDao;
import com.nci.tunan.pa.dao.IPolicyHolderDao;
import com.nci.tunan.pa.impl.peripheral.service.common.IPolicyStatusService;
import com.nci.tunan.pa.interfaces.model.po.BankAccountPO;
import com.nci.tunan.pa.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.pa.interfaces.model.po.CsLoanReasonPO;
import com.nci.tunan.pa.interfaces.model.po.CustomerPO;
import com.nci.tunan.pa.interfaces.model.po.InsuredListPO;
import com.nci.tunan.pa.interfaces.model.po.PolicyHolderPO;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.xml.transform.XmlHelper;

public class CsEndorseRLSubmituccUccImpl implements ICsEndorseRLSubmituccUcc {
	/**
	 *  投保人
	 */
    @Autowired
    @Qualifier("PA_policyHolderDao")
    private IPolicyHolderDao policyHolderDao;
    /**141265
	 * 受理级别UCC
	 */
    @Autowired
    @Qualifier("PA_cusAcceptUCC")
    private ICusAcceptUCC cusAcceptUCC;
    /**
	 *  被保人
	 */
    @Autowired
    @Qualifier("PA_insuredListDao")
    private IInsuredListDao insuredListDao;
    /**
     * 续贷service
     */
    @Autowired
    @Qualifier("PA_csEndorseRLService")
    private ICsEndorseRLService csEndorseRLService;
    /**
     * 险种
     */
    @Autowired
    @Qualifier("PA_contractBusiProdDao")
    private IContractBusiProdDao contractBusiProdDao;
    /**
     * 客户dao
     */
    @Autowired
    @Qualifier("PA_customerDao")
    protected ICustomerDao customerDao;
    /**
     * 外围专用接口
     */
    @Autowired
    @Qualifier("PA_csEndorseForOutterService")
    private ICsEndorseForOutterService csEndorseForOutterService;
    /**
     * 受理service
     */
    @Autowired
    @Qualifier("PA_cusAcceptService")
    private ICusAcceptService cusAcceptService;
    /**
     * 保全收付费dao
     */
    @Autowired
    @Qualifier("PA_csPremArapDao")
    protected ICsPremArapDao csPremArapDao;
	/** 订单信息 **/
    @Autowired
    @Qualifier("orderServiceDao")
    private IOrderServiceDao orderServiceDao;
    /** 续贷UCC **/
	@Autowired
    @Qualifier("PA_csEndorseRLUCC")
    private ICsEndorseRLUCC csEndorseRLUCC;
	/**
     * @Fields contractMasterDao :t_contract_master 保单主表dao层定义
     */
    @Autowired
    @Qualifier("PA_contractMasterDao")
    private IContractMasterDao contractMasterDao;
    /**
     * @Fields policyStatusService:保单状态信息查询service服务
     */
    @Autowired
    @Qualifier("PA_IPolicyStatusServiceImpl")
    private IPolicyStatusService policyStatusService;
    /** 保全公共方法 **/
	@Autowired
    @Qualifier("PA_outterDealUcc")
    private IOutterDealUCC outterDealUCC;
	/**
     * 受理dao
     */
    @Autowired
    @Qualifier("PA_csAcceptChangeDao")
    private ICsAcceptChangeDao csAcceptChangeDao;
    /**
   	 * 银行账户Dao
   	*/
   	@Autowired
   	@Qualifier("PA_bankAccountDao")
    private IBankAccountDao bankAccountDao;
   	
    //#125198新核心-接口需求-移动保全2.0-险种层续贷功能调整需求-CUS-1/2
    //modify by cuiqi_wb
    //2022-11-28
    @Autowired
    @Qualifier("PA_csApplicationDao")
    private ICsApplicationDao csApplicationDao;   
    /**
     * 营销支持接口
     */
    @Autowired
    @Qualifier("PA_prdService")
    private IPRDService prdProductQueryService;
    
    @Autowired
    @Qualifier("PA_csContractBusiProdDao")
    private ICsContractBusiProdDao csContractBusiProdDao;
    
    @Autowired
    @Qualifier("PA_cs_loanProductCfgDao")
    private ILoanProductCfgDao loanProductCfgDao;
    
    @Autowired
    @Qualifier("PA_csPolicyStateDao")
    private ICsPolicyStateDao csPolicyStateDao;
    @Autowired
    @Qualifier("PA_businessProductDao")
    private IBusinessProductDao businessProductDao;
	/**
	 * 保单贷款Service
	 */
	@Autowired
	@Qualifier("PA_csEndorseLNService")
	private ICsEndorseLNService csEndorseLNService;
	
	 /**
     * 保全外围接口日志都记录到目录中：/logs/cus-outter-interface-%d{yyyy-M-d}.log
     */
    private static Logger logger = LoggerFactory.getLogger(Constants.CS_OUTTER_LOG_NAME);
    

  	
  	/**
	 * ICsPolicyChangeDao接口
	 */
	@Autowired
	@Qualifier("PA_csPolicyChangeDao")
	private ICsPolicyChangeDao csPolicyChangeDao;
	
	//141267 新核心-接口需求-移动保全2.0-贷款、续贷项目不支持非集中制返盘银行优化
    //modify by cuiqi_wb
    //2023-11-22
    //----------------------------------------------start-------------------------------------------------------------
	/**
	 * IcsPolicyAccountStreamDao接口
	 */
	@Autowired
	@Qualifier("PA_csPolicyAccountStreamDao")
	private ICsPolicyAccountStreamDao csPolicyAccountStreamDao;
	
	/**
	 * 银行信息显示 service 
	 */
	@Autowired
	@Qualifier("PA_bankAccountInfoService")
	private IBankAccountInfoService bankAccountInfoService;
	
	/**
	 * 保单账户分支表信息 
	 */
	@Autowired
	@Qualifier("PA_policyAccountStreamDao")
	private IPolicyAccountStreamDao policyAccountStreamDao;
	
	/**
	 *  银行号码长度配置dao 
	 */
	@Autowired
	@Qualifier("PA_bankCodeLengthDao")
	protected IBankCodeLengthDao bankCodeLengthDao;
	//----------------------------------------------end-------------------------------------------------------------
	
	//149958 新核心-接口需求-移动保全2.0-移动保全2.0-贷款及续贷项目贷款原因及客户告知内容修改需求
	//modify by cuiqi_wb
	//2023-12-07
	/**
	 *  贷款原因码表dao 
	 */
	@Autowired
	@Qualifier("PA_csLoanReasonDao")
	private ICsLoanReasonDao csLoanReasonDao;
	
	//#158308 需求变更 
	/**
	 * 收付费信息调整数据维护功能实现类Dao，主要使用这个Dao中已有的查询开户行信息方法
	 */
	@Autowired
	@Qualifier("PA_csPaymentAdjustDao")
	private ICsPaymentAdjustDao csPaymentAdjustDao;
	
	@Override
	public OutputData policyLoanSubmit(InputData inputData) {
		logger.info("------------------------P00002900480 保单续贷提交接口   start---------------------");
        logger.info("------------------------接口请求参数信息:");
        logger.info(XmlHelper.classToXml(inputData));
        long startTime = System.currentTimeMillis();

        String orderTransResultDesc = "";
        String orderStatus = Constants.ORDER_STATUS_0;// @invalid 订单状态：成功
        String orderTransResult = Constants.ORDER_TRANS_RESULT_0001;// @invalid 本次交易结果：失败
        BigDecimal acceptId = null;
        String acceptCode = "";
        SysHeader sysHeader = CommonHeaderDeal.getSYSHEADERTHREAD();
        //BizHeader bizheader = CommonHeaderDeal.getBIZHEADERTHREAD();
        //BizHeaderExA bizHeaderExA = CommonHeaderDeal.getBIZHEADERTHREAD_EXA();
        //BizHeaderExB bizHeaderExB = CommonHeaderDeal.getBIZHEADERTHREAD_EXB();
        sysHeader.setBizResCd(Constants.BIZ_RES_CD_1);// 失败
		
        String signIng = StringUtilsEx.nullToString(inputData.getSignIng().trim());//电子签名
        String reMark = StringUtilsEx.nullToString(inputData.getReMark().trim());//空中签名
        String checkMark = StringUtilsEx.nullToString(inputData.getCheckMark().trim());//是否需要验真
        String faceFlag = StringUtilsEx.nullToString(inputData.getFaceFlag().trim());//人脸识别标识
        String identityInfoFlag = StringUtilsEx.nullToString(inputData.getIdentityInfoFlag().trim());//简项信息标识
        String popSituation = StringUtilsEx.nullToString(inputData.getPopSituation().trim());//人口状态
        String msgCheckFlag = StringUtilsEx.nullToString(inputData.getMsgCheckFlag().trim());//短信验证识别标识
        String msgCheckMobile = StringUtilsEx.nullToString(inputData.getMsgCheckMobile().trim());//短信验证手机号码
        String msgCheckDate = StringUtilsEx.nullToString(inputData.getMsgCheckDate().trim());//短信验证通过时间
        String identityFlag = StringUtilsEx.nullToString(inputData.getIdentityFlag().trim());//简项标识
        String appComCode = StringUtilsEx.nullToString(inputData.getAppComCode().trim());//业务员号
		String otherEdorNo = StringUtilsEx.nullToString(inputData.getOtherEdorNo().trim());//订单号
		String contNo = StringUtilsEx.nullToString(inputData.getContNo().trim());//保单号
		String manageCom = StringUtilsEx.nullToString(inputData.getManageCom().trim());//保单管理机构代码
		String applicant = StringUtilsEx.nullToString(inputData.getApplicant().trim());//申请人姓名
		String applicantNo = StringUtilsEx.nullToString(inputData.getApplicantNo().trim());//申请人客户号
		String appType = StringUtilsEx.nullToString(inputData.getAppType().trim());//申请方式
		String payGetForm = StringUtilsEx.nullToString(inputData.getPayGetForm().trim());//收付费方式
		String bankCode = StringUtilsEx.nullToString(inputData.getBankCode()).trim();//开户银行
		String bankAccNo = StringUtilsEx.nullToString(inputData.getBankAccNo()).trim();//银行账户名
		String bankAccName = StringUtilsEx.nullToString(inputData.getBankAccName()).trim();//银行帐号
		String isCorporate = StringUtilsEx.nullToString(inputData.getIsCorporate()).trim();//#158308需求新增--对公标识，0否，1是
		String cipDestrictBankCode = StringUtilsEx.nullToString(inputData.getCipDestrictBankCode()).trim() ;//#158308需求新增--开户行联行号
		String payGetName = StringUtilsEx.nullToString(inputData.getPayGetName().trim());//补退费领取人
		String payGetIDType = StringUtilsEx.nullToString(inputData.getPayGetIDType().trim());//证件类型
		String payGetIDNO = StringUtilsEx.nullToString(inputData.getPayGetIDNO().trim());//证件号码
		String payGetPhone = StringUtilsEx.nullToString(inputData.getPayGetPhone().trim());//手机号
		String autoFlag = StringUtilsEx.nullToString(inputData.getAutoFlag().trim());//自动续贷或自动清偿
		List<RenewList> renewList = inputData.getRenewList();// 续贷险种信息		
		String autoLoanAccInfo = StringUtilsEx.nullToString(inputData.getAutoLoanAccInfo());//自动续贷/清偿使用本次贷款/续贷账户信息

		//#117347随信通_新增贷款续贷保全项接口需求-保全
		//modify by cuiqi_wb
		//2022-07-27
		String policyChapterNumIsPass ="";
		if(StringUtils.isNotBlank(appType) && "29".equals(appType)){
			policyChapterNumIsPass =inputData.getPolicyChapterNumIsPass();//保单红章号/黑章号是否查验通过
		}
		//#123344新核心-接口需求-移动保全2.0-线上E化平台贷款、续贷功能增加贷款原因需求-PART1-CUS
		//modify by cuiqi_wb
		//2022-09-26
		String loanReason = StringUtilsEx.nullToString(inputData.getLoanReason());//贷款原因
		String reasonDescription = StringUtilsEx.nullToString(inputData.getReasonDescription());//原因描述
		
		
		//默认返回失败
		OutputData outputData = new OutputData();
		outputData.setResultCode("");
		/** 1.订单信息初始化 **/
		OrderServicePO orderServicePO = new OrderServicePO();
		Date workTime = WorkDateUtil.getWorkDate();
		Date workDate = DateUtilsEx.formatDate(workTime, "yyyy-MM-dd");
		orderServicePO.setOrderMakeDate(workDate);
		orderServicePO.setOrderMakeTime(workTime);
		orderServicePO.setOrderStatus(Constants.ORDER_STATUS_2);
		orderServicePO.setOrderTransResult(Constants.ORDER_TRANS_RESULT_0000);
		orderServicePO.setOrderTransResultDesc("新建订单成功");
		orderServicePO.setBizCodeType(Constants.BIZ_CODE_TYPE_10);
		orderServicePO.setOrderTransType(Constants.ORDER_STATUS_1);
		orderServicePO.setOrderTransCode("R00102900477");
		orderServicePO.setServiceOrderId(otherEdorNo);
		String orderMsg = csEndorseForOutterService.createOrderServiceInfo(orderServicePO);
		if (!Constants.SUCCESS.equals(orderMsg)) {
			sysHeader.setBizResCd(Constants.BIZ_RES_CD_1);
			sysHeader.setBizResText(orderMsg);
			outputData.setResultCode(Constants.BIZ_RES_CD_1);
			outputData.setResultMsg(orderMsg);
			CommonHeaderDeal.setSYSHEADERTHREAD(sysHeader);
			return outputData;
		}
		try {

		//#117347随信通_新增贷款续贷保全项接口需求-保全-业务员号更改为非必填
		//modify by cuiqi_wb
		//2022-07-27
		if("29".equals(appType)){
			if(StringUtils.isBlank(policyChapterNumIsPass)){
				return failResult("申请方式为随信通，保单红章号/黑章号是否查验通过不可为空", sysHeader, outputData);
			}
		}
		
		String[] inputDatas = {otherEdorNo,contNo,manageCom,applicant,applicantNo,appType,payGetForm,
				bankAccNo,bankAccName,payGetName,payGetIDType,payGetIDNO,payGetPhone,payGetPhone};
		
		String[] hints = {"订单号为空","保单号为空","保单管理机构代码为空","申请人姓名为空","申请人客户号为空",
				"申请方式为空","收付费方式为空","银行账户名为空","银行帐号为空","补退费领取人为空",
				"证件类型为空","证件号码为空","手机号为空","自动续贷或自动清偿为空"};
		
		for (int i = 0; i < inputDatas.length; i++) {
			if ("".equals(inputDatas[i])) {
				return failResult(hints[i], sysHeader, outputData);
			}
		}
		
        if (CollectionUtilEx.isEmpty(renewList)) {
            return failResult("续贷险种信息不能为空！", sysHeader, outputData);
        }
        
        for (RenewList rlData : renewList) {
            List<NsRenew> nsRenewList = rlData.getNsRenewList();// 附加险单独续贷险种列表
            if (CollectionUtilEx.isNotEmpty(nsRenewList)) {
                for (NsRenew nsLoan : nsRenewList) {
                    if (!StringUtilsEx.isNullOrEmpty(nsLoan.getNsRiskCode())) {
                        if (nsLoan.getContinueMoney() == null || new BigDecimal(nsLoan.getContinueMoney()).compareTo(BigDecimal.ZERO) <= 0) {
                            return failResult(nsLoan.getNsRiskCode() + "贷款金额需大于0", sysHeader, outputData);
                        }
                    }
                }
            }
        }
		
		//签名方式	-‘0’电子签名,‘1’空中签名
		if(!StringUtilsEx.isNullOrEmpty(signIng) && !signIng.equals("N") && !signIng.equals(Constants.ZERO_STRING)){
			return failResult("签名方式有误！", sysHeader, outputData);
		}
		
		//空中签名	-‘0’：投保人；‘1’：被保人；‘2’投保人、被保人
		if(!StringUtilsEx.isNullOrEmpty(reMark) && 
				!reMark.equals(Constants.TWO_STRING) && !reMark.equals(Constants.ONE_STRING) && !reMark.equals(Constants.ZERO_STRING)){
			return failResult("空中签名有误！", sysHeader, outputData);
		}
		
		
		//人脸识别标识 Y-核心显示为“同一人”,N-核心显示“空”
		if(!StringUtilsEx.isNullOrEmpty(faceFlag) && !faceFlag.equals("Y") && !faceFlag.equals("N")){
			return failResult("人脸识别标识有误！", sysHeader, outputData);
		}
		
		//短信验证识别标识 -1-投保人；2-被保人；3-投保人、被保人
		if(!StringUtilsEx.isNullOrEmpty(msgCheckFlag) && !msgCheckFlag.equals(Constants.ONE_STRING)
				&& !msgCheckFlag.equals(Constants.TWO_STRING) && !msgCheckFlag.equals(Constants.THREE_STRING)){
			return failResult("短信验证识别标识有误！", sysHeader, outputData);
		}
		
		//短信验证通过时间 -仅支持为一个时间传入 yyyy-MM-dd hh:mm:ss
		if(!StringUtilsEx.isNullOrEmpty(msgCheckDate)){
			try {
				DateUtilsEx.formatToDate(msgCheckDate, "yyyy-MM-dd hh:mm:ss");
			} catch (Exception e) {
				return failResult("短信验证通过时间有误！", sysHeader, outputData);
			}
		}
		
		//简项标识 如果涉及验真则传Y； 否则传N，传N时核心显示为空。 返回“Y”或 “N”；
		if(!StringUtilsEx.isNullOrEmpty(identityFlag) && !identityFlag.equals("Y") && !identityFlag.equals("N")){
			return failResult("简项标识标识有误！", sysHeader, outputData);
		}
		
		//长度为27位，例：X20190523117419539234291712
		if (otherEdorNo.length()!=27) {
			return failResult("订单号有误！", sysHeader, outputData);
		}
		
		//自动续贷或自动清偿 -Y-自动续贷 N-自动清偿
		if(!StringUtilsEx.isNullOrEmpty(autoFlag) && !autoFlag.equals("Y") && !autoFlag.equals("N")){
			return failResult("自动续贷或自动清偿标识有误！", sysHeader, outputData);
		} else {
			if (autoFlag.equals("Y")) autoFlag = Constants.IS_AUTO_RENEW_ONE.toString();
			if (autoFlag.equals("N")) autoFlag = Constants.IS_AUTO_RENEW_ZERO.toString();
		}
		
		//128129随信通-线上E化平台贷款、续贷功能增加贷款原因的需求意向单（新核心）- 保全
		if(!StringUtilsEx.isNullOrEmpty(loanReason) && loanReason.length()>2){
			return failResult("贷款原因码值过长！", sysHeader, outputData);
		}
		
		//#123344新核心-接口需求-移动保全2.0-线上E化平台贷款、续贷功能增加贷款原因需求-PART1-CUS
		//modify by cuiqi_wb
		//2022-09-26 校验贷款原因码值
		if(!StringUtilsEx.isNullOrEmpty(loanReason)	&& loanReason.length()<=2){
			//149958 新核心-接口需求-移动保全2.0-移动保全2.0-贷款及续贷项目贷款原因及客户告知内容修改需求
			//modify by cuiqi_wb
			//2023-12-07
			CsLoanReasonPO csLoanReasonPO = new CsLoanReasonPO();
			csLoanReasonPO.setCode(loanReason);
			csLoanReasonPO =  csLoanReasonDao.findLoanReasonByCode(csLoanReasonPO);
			if(StringUtilsEx.isNullOrEmpty(csLoanReasonPO.getName())){
				return failResult("贷款原因码值有误！", sysHeader, outputData);
			}		
		}
		
		
		//申请方式码值转换
        if (!appType.equals("")){
        	appType = CodeMapperUtils.getNewCodeByOldCode("SERVICE_TYPE", appType, "CUS");
        }
        
		if (Constants.SERVICE_TYPE_20.equals(appType) && StringUtilsEx.isEmpty(autoLoanAccInfo)) {
			return failResult("申请方式为移动保全2.0时自动续贷/清偿使用本次贷款/续贷账户信息节点不能为空！", sysHeader, outputData);
		}
		
		//141267 新核心-接口需求-移动保全2.0-贷款、续贷项目不支持非集中制返盘银行优化
        //modify by cuiqi_wb
        //2023-10-31
        //----------------------------------------------start-------------------------------------------------------------
        String autoPayAccountBank = "";//自动续贷/清偿开户银行
        String autoPayAccoName = "";//自动续贷/清偿银行账户名
        String autoPayBankAccount = "";//自动续贷/清偿银行帐号
        if (!StringUtilsEx.isNullOrEmpty(inputData.getAutoPayAccountBank())) {
        	autoPayAccountBank = StringUtilsEx.nullToString(inputData.getAutoPayAccountBank().trim());// 自动续贷/清偿开户银行
        }
        if (!StringUtilsEx.isNullOrEmpty(inputData.getAutoPayAccoName())) {
        	autoPayAccoName = StringUtilsEx.nullToString(inputData.getAutoPayAccoName().trim());// 自动续贷/清偿开户银行
        }
        if (!StringUtilsEx.isNullOrEmpty(inputData.getAutoPayBankAccount())) {
        	autoPayBankAccount = StringUtilsEx.nullToString(inputData.getAutoPayBankAccount().trim());// 自动续贷/清偿开户银行
        }
        if (Constants.SERVICE_TYPE_20.equals(appType) && (StringUtilsEx.isEmpty(inputData.getAutoPayAccountBank()) || 
        		StringUtilsEx.isEmpty(inputData.getAutoPayAccoName())||StringUtilsEx.isEmpty(inputData.getAutoPayBankAccount()))) {
			return failResult("申请方式为移动保全2.0时自动续贷/清偿开户银行,自动续贷/清偿银行账户名,自动续贷/清偿银行帐号必传！", sysHeader, outputData);
		}
        //-----------------------------------------------end-------------------------------------------------------------------------
		
        //收付费方式码值转换
      //#158308 34的码值也不能被转换，否则的话payMode转换后结果为空
        if(!("32".equals(payGetForm) || "34".equals(payGetForm)) ){
        	payGetForm = CodeMapperUtils.getNewCodeByOldCode("PAY_MODE", payGetForm, "PAS");// 本次领取方式
        }
        
        /**
		 * #158308 需求变更：
		 * 		1、收付费方式为"34-网上银行时，对公标识和开户行联行号不能为空"
		 * 		2、传入的开户行联行号应在核心开户行联行号数据范围内，否则受理失败
		 * 		3、开户银行改为非必传：收付费方式为"32-银行转账（制返盘）"时必传
		 */
        //---------------------------------------需求变更校验 start-------------------------------------
		if(StringUtils.isNotBlank(payGetForm) && payGetForm.equals(Constants.PAY_MODE_CODE_34)){
			if(StringUtils.isBlank(isCorporate) || StringUtils.isBlank(cipDestrictBankCode) ){
				return failResult("对公标识、开户行联行号不能为空！", sysHeader, outputData);
			}
		}else{
			//收付费方式目前只有：34-网上银行、32-银行转账（制返盘），所以，当收付费方式不满足等于34时，需要判断开户银行是否为空
			if(StringUtils.isBlank(bankCode)){
				return failResult("开户银行不能为空！", sysHeader, outputData);
			}
		}
		
		BankOfDepositPO bankOfDepositPO = new BankOfDepositPO();
		if(StringUtils.isNotBlank(cipDestrictBankCode)){
			bankOfDepositPO = new BankOfDepositPO();
			bankOfDepositPO.setCorrespondentNo(inputData.getCipDestrictBankCode());
			bankOfDepositPO = csPaymentAdjustDao.findBankOfDepositByCorrespondentNo(bankOfDepositPO);
			if(bankOfDepositPO == null || StringUtils.isBlank(bankOfDepositPO.getBankCode()) ){
				return failResult("开户行联行号错误！", sysHeader, outputData);
			}
		}
		//---------------------------------------需求变更校验 end------------------------------------------
			/**2.初始化登录用户 **/
            csEndorseForOutterService.initOutterUser(applicant);
            
            Date appDate = WorkDateUtil.getWorkDate();

            PolicyHolderPO policyHolderPO = new PolicyHolderPO();
            policyHolderPO.setPolicyCode(contNo);
    		policyHolderPO = policyHolderDao.findPolicyHolder(policyHolderPO);
    		if (policyHolderPO == null || policyHolderPO.getCustomerId() == null) {
    		  return failResult(contNo + "保单下未找到对应投保人信息，请确认！", sysHeader, outputData);
    		}   		
    		// 客户ID 申请人客户号
    		BigDecimal customerId = policyHolderPO.getCustomerId();
    		CustomerPO customerPO = new CustomerPO();
    		customerPO.setCustomerId(customerId);
    		customerPO = customerDao.findCustomer(customerPO);// 投保人信息
    		
    		InsuredListPO insuredListPO =new InsuredListPO();
    		insuredListPO.setPolicyCode(contNo);
    		List<InsuredListPO> insuredListPOs= insuredListDao.findAllInsuredList(insuredListPO);
    		List<CustomerPO> customerPOs = new ArrayList<CustomerPO>();
    		for (InsuredListPO insuredListPO1 : insuredListPOs) {
    			BigDecimal customerId1 = insuredListPO1.getCustomerId();       		
        		CustomerPO customerPO1 =new  CustomerPO();
        		customerPO1.setCustomerId(customerId1);
        		customerPO1 = customerDao.findCustomer(customerPO1);// 被保人信息
        		customerPOs.add(customerPO1);
			}
    		
    		//置灰码值校验
    		ContractMasterPO contractMasterPO = new ContractMasterPO();
        	contractMasterPO.setPolicyCode(contNo);
        	String initUnavailableCode = initUnavailableCode(contractMasterPO,appType,inputData);
        	if (!"".equals(initUnavailableCode)) {
        		return failResult(initUnavailableCode.substring(0, initUnavailableCode.length()-1), sysHeader, outputData);
    		}
        	
        	if((null != inputData.getVerifyInfo() && null != inputData.getVerifyInfo().getVerifyInfoList()) || !StringUtilsEx.isNullOrEmpty(inputData.getMsgCheckFlag())){
        		for(VerifyInfoList verifyInfoList : inputData.getVerifyInfo().getVerifyInfoList()){
        			Map<String, String> verifyMap = new HashMap<String, String>();
            		verifyMap.put("verifType", verifyInfoList.getVerifType());
            		verifyMap.put("customCheckDate", verifyInfoList.getCustomCheckDate());
            		verifyMap.put("checkName", verifyInfoList.getCheckName());
            		verifyMap.put("checkIDNo", verifyInfoList.getCheckIDNo());
            		verifyMap.put("similar", verifyInfoList.getSimilar());
            		String YZsaveMsg = this.saveIdentityCheck(inputData, customerPO,customerPOs,verifyMap);
                    if (!Constants.SUCCESS.equals(YZsaveMsg)) {
                        return failResult("保单续贷提交接口-保存验真信息失败", sysHeader, outputData);
                    }
        		}
        		
        	}
            
            if(manageCom!=null && "20".equals(appType)){
            	csEndorseForOutterService.initOutterUser("ydbq2.0", manageCom);
            	//#117347随信通_新增贷款续贷保全项接口需求-保全
            	//modify by cuiqi_wb
            	//2022-07-27
            } else if("23".equals(appType)){
            	csEndorseForOutterService.initOutterUser("suixt");
            }else {
            	csEndorseForOutterService.initOutterUser("ydbq2.0");
            }
            

            /***** 3保存申请、受理信息 ************/
            OutterCsEndorse outterCsEndorse = new OutterCsEndorse();
            outterCsEndorse.setApplyTime(appDate);
            outterCsEndorse.setServiceType(appType);
            outterCsEndorse.setCustomerId(customerId);
            outterCsEndorse.setApplyName(customerPO.getCustomerName());
            outterCsEndorse.setPolicyCode(policyHolderPO.getPolicyCode());
            outterCsEndorse.setPolicyId(policyHolderPO.getPolicyId());
            outterCsEndorse.setServiceCode(SERVICE.CsEndorseRL);
            outterCsEndorse.setOrganCode(manageCom);
            outterCsEndorse.setIsElecSign("0".equals(signIng) ? BigDecimal.ONE : BigDecimal.ZERO);
            outterCsEndorse.setMeritGrade(inputData.getMeritGrade());//绩优等级
            /** 145266 个险新核心支持外围平台申请强制流转保全人工复核流程优化   START  **/
			// 外围系统传入"是否强制进入人工复核"节点标识
			if("Y".equals(inputData.getIsManuallyReview())){
				outterCsEndorse.setManualReviewFlag(BigDecimal.ONE);
			}else if("N".equals(inputData.getIsManuallyReview())){
				outterCsEndorse.setManualReviewFlag(BigDecimal.ZERO);
			}
			/** 145266 个险新核心支持外围平台申请强制流转保全人工复核流程优化  END  **/
            
            CsAcceptBO csAcceptBO = csEndorseForOutterService.saveApplyAcceptInfo(outterCsEndorse);
            if (CollectionUtilEx.isEmpty(csAcceptBO.getCsAcceptChangeCompBOs())) {
                sysHeader.setBizResText("保单续贷提交接口-保存受理信息失败！"+csAcceptBO.getResultMsg());
                logger.info("----------移动保全2.0 P00002900480 保单续贷提交接口-保存受理信息失败！"+csAcceptBO.getResultMsg());
                return failResult("保单续贷提交接口-保存受理信息失败！"+csAcceptBO.getResultMsg(), sysHeader, outputData);
            }
            
            CsAcceptChangeCompBO csAcceptChangeCompBO = csAcceptBO.getCsAcceptChangeCompBOs().get(0);
            CsAcceptChangeBO csAcceptChangeBO = BeanUtils.copyProperties(CsAcceptChangeBO.class, csAcceptChangeCompBO);
            acceptId = csAcceptChangeBO.getAcceptId();
            BigDecimal changeId = csAcceptChangeBO.getChangeId();
            acceptCode = csAcceptChangeBO.getAcceptCode();
            orderServicePO.setBizCode(acceptCode);// 受理号
            /** 更新订单信息（保存“订单号”与“受理号”关联关系） **/
            orderServicePO.setOrderStatus(Constants.ORDER_STATUS_2);
            orderServicePO.setBizCode(acceptCode);
            orderServicePO.setOrderTransResultDesc("处理中");
            csEndorseForOutterService.updateOrderServiceInfo(orderServicePO);
            //人脸识别结果 0通过 1不通过
			//入参【是否需要验真】为1时；核心默认存储为“通过”
			if (checkMark.equals("1")) {
				csAcceptChangeBO.setFaceRecoResult("0");
			}
			//是否需要验真 1-人像比对;2-注销验真；3-人像比对， 注销验真；N-否。
			csAcceptChangeBO.setIsIdentityCheck(checkMark);
            //空中签名 0投保人;1被保人;2投被保人
			if (!StringUtilsEx.isNullOrEmpty(reMark)) {
	            csAcceptChangeBO.setSkySign(reMark);
			}
            //人脸识别标识
            if(Constants.FLAG_Y.equals(faceFlag)){
            	csAcceptChangeBO.setFaceFlag(Constants.YES_NO__ONE);
            }
            if(Constants.FLAG_N.equals(faceFlag)){
            	csAcceptChangeBO.setFaceFlag(Constants.YES_NO__ZERO);
            }
            //简项标识
            if (!StringUtilsEx.isNullOrEmpty(identityFlag)) {
            	csAcceptChangeBO.setIdentityFlag(identityFlag);
			}
            //回访电话1
            if (!StringUtilsEx.isNullOrEmpty(payGetPhone)) {
            	csAcceptChangeBO.setCallPhone1(payGetPhone);
			}
            //@invalid 87204 start 新核心-接口需求-移动保全2.0-保全业务明细清单优化
            if (StringUtils.isNotEmpty(appComCode)) {
            	csAcceptChangeBO.setAcceptAgentCode(appComCode);
            }
            //受理机构
            if (StringUtils.isNotEmpty(manageCom)) {
                csAcceptChangeBO.setOrganCode(manageCom);
            }
            //修改受理信息
            csAcceptChangeDao.updateCsAcceptChangeFlag(BeanUtils.copyProperties(CsAcceptChangePO.class, csAcceptChangeBO));
           
            /***** 3.保存保全项:RL贷款续贷信息 ************/
            BigDecimal isAutoRenew=Constants.IS_AUTO_RENEW_TWO;
            if (autoFlag != null && autoFlag.equals("1")) {
                isAutoRenew = BigDecimal.ONE;
            }else if (autoFlag != null && autoFlag.equals("0")){
            	isAutoRenew = BigDecimal.ZERO;
            }
            PolicyRevivalBO policyRLBO = new PolicyRevivalBO();
            policyRLBO.setChangeId(changeId);
            policyRLBO.setAcceptId(acceptId);
            policyRLBO.setCustomerId(customerId);
            policyRLBO.setBankAccount(bankAccName);
            policyRLBO.setBankCode(bankCode);
            policyRLBO.setBankName(bankAccNo);
            policyRLBO.setLoanReason(loanReason);
            policyRLBO.setReasonDescription(reasonDescription);
            policyRLBO.setIsAutoRenew(isAutoRenew);
            policyRLBO.setAutoLoanAccInfo(autoLoanAccInfo);
          //141267 ALM25489 start
            if(Constants.SERVICE_TYPE_20.equals(appType) && !"Y".equals(autoLoanAccInfo)){
            	policyRLBO.setBankAccount(autoPayBankAccount);
                policyRLBO.setBankCode(autoPayAccountBank);
                policyRLBO.setBankName(autoPayAccoName);
            }
          //141267 ALM25489 end
            
            
            //172487 新核心-接口需求-移动保全2.0-贷款状态下生存金自动抵扣流程需求  start
            if(StringUtils.isNotBlank(inputData.getDeduLoanFlag())){
            	policyRLBO.setDeduLoanFlag(new BigDecimal(inputData.getDeduLoanFlag()));
            }
            //172487 end
            
            String saveMsg = this.saveRLLoanInfo(policyRLBO, renewList,appType);
            if (!Constants.SUCCESS.equals(saveMsg)) {
                csEndorseForOutterService.cancelCsendorse(acceptId, Constants.ACCEPT_STATUS__CANCEL);
                return failResult("录入信息保存失败:" + saveMsg, sysHeader, outputData);
            }
            //141265 start
            String checkAppLoanStatus = cusAcceptUCC.checkAppLoanStatus(csAcceptChangeBO);
            if(!"".equals(checkAppLoanStatus)){
				csEndorseForOutterService.cancelCsendorse(acceptId, Constants.ACCEPT_STATUS__CANCEL);
            	return failResult(checkAppLoanStatus, sysHeader, outputData);
            }
            //141265 end
            /** 5.更新t_cs_prem_arap表中补退费信息 **/
            CsPremArapBO csPremArapBO = new CsPremArapBO();
            csPremArapBO.setBusinessCode(acceptCode);
            csPremArapBO.setPayMode(payGetForm);           	
    	    csPremArapBO.setBankAccount(bankAccName);
            csPremArapBO.setBankUserName(bankAccNo);              
            csPremArapBO.setPayeeName(bankAccNo);
            csPremArapBO.setPayeePhone(payGetPhone);
            csPremArapBO.setCertiType(payGetIDType);
            csPremArapBO.setCertiCode(payGetIDNO);
            /**
    		 * #158308 需求变更：
    		 * 		4、收付费方式为"34-网上银行"时，开户银行代码对应的值应该是根据开户行联行号查询出来的银行代码
    		 * 		5、将对公标识和开户行联行号保存到应收应付表中
    		 */
        	//--------------------------------------- start-------------------------------------------
            if(payGetForm.equals(Constants.PAY_MODE_CODE_34)){
            	csPremArapBO.setBankCode(bankOfDepositPO.getBankCode());
            }else{
            	csPremArapBO.setBankCode(bankCode);
            }
            csPremArapBO.setIsCorporate(isCorporate); //对公标识
            csPremArapBO.setCipDistrictBankCode(cipDestrictBankCode); //开户行联行号
			//--------------------------------------- end-------------------------------------------
            
            csEndorseForOutterService.updateCsPremArap(csPremArapBO);
            
            /**6.执行保全项录入完成之后的操作 **/
            //#117347随信通_新增贷款续贷保全项接口需求-保全
            //modify by cuiqi_wb
            //2022-07-28
            //====================================================start==========================================================
            String ilogMsg ="";
          //强制流转人工复核标识
		    if((!StringUtilsEx.isNullOrEmpty(inputData.getIsManuallyReview())&&"Y".equals(inputData.getIsManuallyReview()))
		    		||(!StringUtilsEx.isBlank(policyChapterNumIsPass) && "N".equals(policyChapterNumIsPass) && "23".equals(appType))){
            	 ilogMsg = csEndorseForOutterService.executeAfterCsEntryThreadBPMNew(csAcceptChangeBO,"","Y");
            }else{
            	 ilogMsg = csEndorseForOutterService.executeAfterCsEntryThreadBPM(csAcceptChangeBO);	
            }
		    //====================================================end=============================================================
	        if (!Constants.SUCCESS.equals(ilogMsg) && !Constants.BPM_CREATE_FLAG_2.equals(ilogMsg)
                    && !Constants.BPM_CREATE_FLAG_1.equals(ilogMsg)) {
	        	if(null!=acceptId){
                	csEndorseForOutterService.cancelCsendorse(acceptId, Constants.ACCEPT_STATUS__CANCEL);
            	}
	            return failResult("提交失败:" + ilogMsg.replace("<br/>", ""), sysHeader, outputData);
	        }

			if (Constants.BPM_CREATE_FLAG_1.equals(ilogMsg) || Constants.BPM_CREATE_FLAG_2.equals(ilogMsg)) {
				String msg = csEndorseForOutterService.submitToIBM(csAcceptChangeBO, ilogMsg);
	            if (!Constants.SUCCESS.equals(msg)) {
	                /* 保全项撤销 */
	            	if(null!=acceptId){
	                	csEndorseForOutterService.cancelCsendorse(acceptId, Constants.ACCEPT_STATUS__CANCEL);
	            	}
	                return failResult("创建工作流任务失败", sysHeader, outputData);
	            }
			}
           
            if (ilogMsg.equals(Constants.BPM_CREATE_FLAG_2)) {
            	sysHeader.setBizResCd(Constants.BIZ_RES_CD_1);// 报文返回1为接口失败返回
        		sysHeader.setBizResText("保全自动核保不通过，进入人工核保");//设置返回失败提示语
        		outputData.setResultMsg("保全自动核保不通过，进入人工核保");//失败原因
            } else if (ilogMsg.equals(Constants.BPM_CREATE_FLAG_1)) {
            	sysHeader.setBizResCd(Constants.BIZ_RES_CD_1);// 报文返回1为接口失败返回
        		sysHeader.setBizResText("保全自动复核不通过，进入人工复核");//设置返回失败提示语
        		outputData.setResultMsg("保全自动复核不通过，进入人工复核");//失败原因
            } else {
            	sysHeader.setBizResCd(Constants.BIZ_RES_CD_0);
            	sysHeader.setBizResText("贷款续贷提交成功");
            	outputData.setResultCode(Constants.BIZ_RES_CD_0);
            	orderTransResultDesc = "贷款续贷提交成功";
            }
            // @invalid 仅接口提交成功场景，赋值成功。后续如调整程序，须关注此字段赋值处理。
            orderTransResult = Constants.ORDER_TRANS_RESULT_0000;// @invalid 本次交易结果：成功
		} catch (Exception e) {
            e.printStackTrace();
            logger.info("---------- 移动保全2.0 P00002900480 保单续贷提交接口  保存异常:" + e.getMessage(), e);
            /* ---执行撤销操作 ---- */
            if (acceptId != null) {
                csEndorseForOutterService.cancelCsendorse(acceptId, Constants.ACCEPT_STATUS__CANCEL);
            }
            orderStatus = Constants.ORDER_STATUS_1;// @invalid 订单状态：失败
            orderTransResultDesc = "保单续贷提交接口 -保存异常！";
            sysHeader.setBizResText("移动保全2.0 P00002900480 保单续贷提交接口 -保存异常！");
            sysHeader.setBizResCd(Constants.BIZ_RES_CD_1);// 失败
            throw new RuntimeException(e);
        } finally {
        	if (acceptId != null) {
                /*** 执行解锁 ***/
                csEndorseForOutterService.releaseLockByAcceptId(acceptId);
				CsAcceptChangeBO acceptChangeBO = csEndorseForOutterService.queryCsAcceptChange(acceptId);
				outputData.setEdorState(csEndorseForOutterService.getAcceptStatus(acceptChangeBO.getAcceptStatus()));
                outputData.setEdorAcceptNo(acceptCode);
                /** 设置接口返回信息 */
            }
            /** 更新订单信息 **/
            if (orderServicePO.getOrderListId() != null) {
                orderServicePO.setOrderStatus(orderStatus);
                orderServicePO.setOrderTransResult(orderTransResult);
                orderServicePO.setOrderTransResultDesc(StringUtils.isNotBlank(orderTransResultDesc) ? orderTransResultDesc : outputData.getResultMsg());
                csEndorseForOutterService.updateOrderServiceInfo(orderServicePO);
            }
            CommonHeaderDeal.setSYSHEADERTHREAD(sysHeader);
            logger.info("---------- 移动保全2.0 P00002900480 保单续贷提交接口  用时:" + (System.currentTimeMillis()-startTime));
        }
		return outputData;
	}


	//失败方法返回
	private OutputData failResult(String str, SysHeader sysHeader, OutputData outputData) {
		sysHeader.setBizResCd(Constants.BIZ_RES_CD_1);// 报文返回1为接口失败返回
		sysHeader.setBizResText(str);//设置返回失败提示语
		outputData.setResultCode(Constants.BIZ_RES_CD_1);//1-失败
		outputData.setResultMsg(str);//失败原因
		return outputData;
	}
	
	/**
	 * 
	 * @description 封装保单贷款续贷 保存保全项目逻辑
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param appType 141267 add
	 * @param policyRLBO
	 * @param csBankAccountVO
	 * @return
     */
    public String saveRLLoanInfo(PolicyRevivalBO policyRevBO, List<RenewList> renewList, String appType) {
        BigDecimal changeId = policyRevBO.getChangeId();
        BigDecimal acceptId = policyRevBO.getAcceptId();
        BigDecimal customerId = policyRevBO.getCustomerId();

        String isNewAccount = Constants.YES_NO__ZERO;// 是否有新增账户       
        String isAutoRenew = policyRevBO.getIsAutoRenew().toString();// 0：自动清偿1：自动续贷
        String loanReason=policyRevBO.getLoanReason();//贷款原因
        String reasonDescription=policyRevBO.getReasonDescription();//原因描述
        
		// N 核心自动续贷/清偿时使用本次贷款/续贷账户信息为不勾选状态
//		if (!"Y".equals(StringUtilsEx.nullToString(policyRevBO.getAutoLoanAccInfo()))) {
//			BankAccountPO bankAccountPO = new BankAccountPO();
//			bankAccountPO.setCustomerId(customerId);
//			bankAccountPO.setBankAccount(policyRevBO.getBankAccount());
//			bankAccountPO.setBankCode(policyRevBO.getBankCode());
//			bankAccountPO.setAccoName(policyRevBO.getAccountName());
//			List<BankAccountPO> bankAccountPOs = bankAccountDao.findAllBankAccount(bankAccountPO);
//			if (CollectionUtilEx.isEmpty(bankAccountPOs)) {
//				isNewAccount = Constants.YES_NO__ONE;
//			}
//		}
        
		BankAccountPO bankAccountPO = new BankAccountPO();
        bankAccountPO.setCustomerId(customerId);
        bankAccountPO.setBankAccount(policyRevBO.getBankAccount());
        bankAccountPO.setBankCode(policyRevBO.getBankCode());
        bankAccountPO.setAccoName(policyRevBO.getAccountName());
        List<BankAccountPO> bankAccountPOs = bankAccountDao.findAllBankAccount(bankAccountPO);
        if (CollectionUtilEx.isEmpty(bankAccountPOs)) {
            isNewAccount = Constants.YES_NO__ONE;
        }
      //141267 ALM25489 start
        if(Constants.SERVICE_TYPE_20.equals(appType) && !"Y".equals(policyRevBO.getAutoLoanAccInfo())){
        	isNewAccount = Constants.YES_NO__ONE;
        }
      //141267 ALM25489 end
        BigDecimal autoAccountId=null;
        if ("1".equals(isNewAccount)) {
            CsBankAccountVO csBankAccountVO = new CsBankAccountVO();     
            csBankAccountVO.setAccoName(policyRevBO.getBankName());
            csBankAccountVO.setBankAccount(policyRevBO.getBankAccount());
            csBankAccountVO.setBankCode(policyRevBO.getBankCode());
            csBankAccountVO.setCustomerId(customerId);
            csBankAccountVO.setChangeId(changeId);
            
            // 1.调用账户存储方法，并返回账户号，组织接收新增字段 // 使用新录入的账户
            String remsg = csEndorseLNService.saveBankAccount(customerId, changeId, acceptId, csBankAccountVO);
            if (remsg != null && remsg.contains("accountId")) {
                // @invalid 如果返回accountId，进行赋值处理
                autoAccountId = new BigDecimal(remsg.substring(Constants.NINE_INT));                
            } else {
                return remsg;
            }
        }

        List<PolicyRevivalBO> policyRLDataList = new ArrayList<PolicyRevivalBO>();
        for (RenewList rlData : renewList) {
            String mainRiskCode = rlData.getMainRiskCode();// 主险代码
            BigDecimal rlAmount =   BigDecimal.ZERO;// 续贷金额
            if (!StringUtilsEx.isNullOrEmpty(rlData.getContinueMoney())) {
				rlAmount = new BigDecimal(rlData.getContinueMoney());
			}
            List<NsRenew> nsRenewList = rlData.getNsRenewList();// 附加险单独续贷险种列表

            PolicyRevivalBO policyRLBO = new PolicyRevivalBO();
            policyRLBO.setPolicyLoanLtAmount(rlAmount);
            policyRLBO.setMasterBusiPrdCode(mainRiskCode);
            policyRLBO.setIsAutoRenew(new BigDecimal(isAutoRenew));            
            policyRLBO.setIsNewAccount(new BigDecimal(isNewAccount));
            policyRLBO.setLoanReason(loanReason);
            policyRLBO.setReasonDescription(reasonDescription);
            if (autoAccountId!=null) {
                policyRLBO.setAutoAccountId(autoAccountId);
            }
            if (CollectionUtils.isNotEmpty(nsRenewList) && rlAmount.compareTo(BigDecimal.ZERO) == 0) {//附加险单独贷款
                List<NsRenewVO> nsRenewVOList = new ArrayList<NsRenewVO>();
                for (NsRenew nsRenew : nsRenewList) {
                    NsRenewVO nsRenewVO = new NsRenewVO(nsRenew.getNsRiskCode(), nsRenew.getContinueMoney());
                    nsRenewVOList.add(nsRenewVO);
                }
                policyRLBO.setNsRenewList(nsRenewVOList);
            }
            
            //172487 当传递的  年金/生存金/满期金优先抵扣贷款标识  不为空时，赋值
            if(policyRevBO.getDeduLoanFlag() != null){
            	policyRLBO.setDeduLoanFlag(policyRevBO.getDeduLoanFlag());
            }
            
            policyRLDataList.add(policyRLBO);
        }

        logger.info("CsEndorseRLSubmituccUccImpl===policyRLDataList={}", XmlHelper.classToXml(policyRLDataList));

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("changeId", changeId);
        map.put("acceptId", acceptId);
        map.put("customerId", customerId);
        map.put("policyRLDataList", policyRLDataList);
        //return csEndorseRLService.saveRLDataForOut(map);
        String res = csEndorseRLService.saveRLDataForOut(map);
        //141267 ALM25489 start 通用保存方法最后会将外围提交接口是否新增账户置为0。需要修改
        if(Constants.SERVICE_TYPE_20.equals(appType) && !"Y".equals(policyRevBO.getAutoLoanAccInfo())){
        	CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
        	csPolicyChangePO.setAcceptId(acceptId);
        	List<CsPolicyChangePO> csPolicyChangePOs = csPolicyChangeDao.findAllCsPolicyChange(csPolicyChangePO);
        	for (CsPolicyChangePO csPolChgPO : csPolicyChangePOs) {
        		CsPolicyAccountStreamPO csPolicyAccountStreamPO = new CsPolicyAccountStreamPO();
        		csPolicyAccountStreamPO.setPolicyId(csPolChgPO.getPolicyId());
        		csPolicyAccountStreamPO.setChangeId(changeId);
        		csPolicyAccountStreamPO.setPolicyChgId(csPolChgPO.getPolicyChgId());
        		csPolicyAccountStreamPO.setAccountType(Constants.CS_POLICY_ACCOUNT_TYPE_LOAN);
        		csPolicyAccountStreamPO.setOldNew(Constants.NEW);
        		List<CsPolicyAccountStreamPO> csPolicyAccountStreamPOList = csPolicyAccountStreamDao.findAllCsPolicyAccountStream(csPolicyAccountStreamPO);
        		if (!CollectionUtils.isEmpty(csPolicyAccountStreamPOList)) {
        			for (CsPolicyAccountStreamPO csPolAccountStreamPO : csPolicyAccountStreamPOList) {
        				csPolAccountStreamPO.setIsNewAccount(BigDecimal.ONE);
        				if (autoAccountId!=null) {
        					csPolAccountStreamPO.setAutoAccountId(autoAccountId);
        				}
						csPolicyAccountStreamDao.updateCsPolicyAccountStream(csPolAccountStreamPO);
        			}
        		}
        	}
        }
        return res;
        //141267 ALM25489 end
    }
    
    /**
	 * 置灰码值
	 * @param conMasterPO
	 * @return
	 */
    private String initUnavailableCode(ContractMasterPO conMasterPO,String appType,InputData inputData) {
    	String flag = "";
    	
    	ContractMasterPO contractMasterPO = new ContractMasterPO();
    	contractMasterPO.setPolicyCode(conMasterPO.getPolicyCode());
    	List<ContractMasterPO> contractMaters = contractMasterDao.findAllContractMaster(contractMasterPO);
    	logger.info("CsEndorseRLSubmituccUccImpl.initUnavailableCode置灰码校验====start====保单号："+conMasterPO.getPolicyCode());
    	flag += policyStatusService.policyWQCDK(contractMaters.get(0),"保单"+conMasterPO.getPolicyCode()+"下不存在未清偿的贷款");
    	if (flag.isEmpty()) {
			for(ContractMasterPO conPO : contractMaters){
	    		if(Constants.LIABILITY_STATE__LAPSED.equals(conPO.getLiabilityState().toString())){
	    			flag += "保单"+conMasterPO.getPolicyCode()+"未生效，暂不允许申请续贷。";
	    		}
	    		if(Constants.LIABILITY_STATE__TERMINATED.equals(conPO.getLiabilityState().toString())){
	    			flag += "保单"+conMasterPO.getPolicyCode()+"终止保单，不允许申请续贷。";
	    		}
	    	}
	    	flag += policyStatusService.policyLPWJ(conMasterPO,"保单"+conMasterPO.getPolicyCode()+"存在未决理赔，暂不允许申请续贷。");
	    	flag += policyStatusService.policyGQDJGS(conMasterPO,conMasterPO.getPolicyCode()+"保单处于挂起状态，暂不允许申请续贷。",null,null,null);
	    	flag += policyStatusService.policyZJZF(conMasterPO, "“保单"+conMasterPO.getPolicyCode()+"保单质押第三方止付状态，不能受理保单续贷，请确认。”");
	    	flag += policyStatusService.policyTBRSCBS(conMasterPO,"保单"+conMasterPO.getPolicyCode()+"投保人死亡，不能受理保单贷款，请续贷。");
	    	flag += policyStatusService.policyBBRSCBS(conMasterPO,"保单"+conMasterPO.getPolicyCode()+"被保人死亡，不能受理保单贷款，请确认。");
	    	flag += policyStatusService.policyBDCYYHHKQJ(conMasterPO,"保单"+conMasterPO.getPolicyCode()+"处于收付款银行划款期间，不能受理保单续贷，请确认。");
	    	//flag += policyStatusService.policyBFHM(conMasterPO,"保单"+conMasterPO.getPolicyCode()+"处于保费豁免状态，不支持保单续贷");
		}
    	logger.info("CsEndorseRLSubmituccUccImpl.initUnavailableCode置灰码校验====end====保单号："+conMasterPO.getPolicyCode()+"==flag:"+flag);
    	return flag;
    }
	
    /**
	 * 
	 * @description 保存短信验真信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param inputData
	 * @param customerPO
	 * @return
	 */
	private String saveIdentityCheck(InputData inputData, CustomerPO customerPO,List<CustomerPO> customerPOs,Map<String, String> verifyMap) {
		String isVerify = verifyMap.get("verifType").trim(); //是否需要验真
		String verifyDate = verifyMap.get("customCheckDate").trim(); //身份验真时间
		String checkName = verifyMap.get("checkName").trim(); //被验真人姓名
		String checkIDNo = verifyMap.get("checkIDNo").trim(); //被验真人身份证号码
		String similar = verifyMap.get("similar").trim(); //相似度		
		String msgCheckMobile =""; 
		String msgCheckDate = ""; 
		String msgCheckFlag = ""; 
		List<String> names =new ArrayList<String>();
		for (CustomerPO customerPO1 : customerPOs) {
			String name= customerPO1.getCustomerName();
			names.add(name);
		}
		if((customerPO.getCustomerName().equals(checkName)&&inputData.getMsgCheckFlag().trim().equals("1"))
				||(names.contains(checkName)&&inputData.getMsgCheckFlag().trim().equals("2"))
				||((customerPO.getCustomerName().equals(checkName)||names.contains(checkName))&&inputData.getMsgCheckFlag().trim().equals("3"))){
		 msgCheckMobile = inputData.getMsgCheckMobile().trim(); //短信验证手机号码
		 msgCheckDate = inputData.getMsgCheckDate().trim(); //短信验证通过时间		
		 msgCheckFlag = inputData.getMsgCheckFlag().trim(); //短信验证识别标识
		}
		String orderId = inputData.getOtherEdorNo().trim();
		List<Map<String, String>> listmap = new ArrayList<Map<String, String>>();
		Map<String, String> map = new HashMap<String, String>();
		
		// @invalid 客户姓名，因为方法里 取值为 CustomerName 所以put时 也需要传输CustomerName
		map.put("CustomerName", checkName);
		// @invalid 客户证件号，因为方法里 取值为 CustomerIdCode 所以put时 也需要传输CustomerIdCode
		map.put("CustomerIdCode", checkIDNo);
		// @invalid 短信验证类别，因为方法里 取值为 MsgCheckFlag 所以put时 也需要传输MsgCheckFlag
		map.put("MsgCheckFlag", msgCheckFlag);
		// @invalid 短信验证手机号码，因为方法里 取值为 MsgCheckMobile 所以put时 也需要传输MsgCheckMobile
		map.put("MsgCheckMobile", msgCheckMobile);
		// @invalid 短信验证日期，因为方法里 取值为 MsgCheckDate 所以put时 也需要传输MsgCheckDate
		map.put("MsgCheckDate", msgCheckDate);
		// @invalid 相似度，因为方法里 取值为 Similar 所以put时 也需要传输Similar
		map.put("Similar", similar.equals("N")?"":similar);
		// @invalid 流水号，因为方法里 取值为 ServiceOrderId 所以put时 也需要传输ServiceOrderId
		map.put("ServiceOrderId", orderId);
		// @invalid 验真类别，因为方法里 取值为 CheckType 所以put时 也需要传输CheckType
		map.put("CheckType", isVerify);
		// @invalid 验真时间，因为方法里 取值为 YzDate 所以put时 也需要传输YzDate
		map.put("YzDate", verifyDate);

		if ("3".equals(isVerify)) {
            /** 3-人像比对、人口状态 保存两条验真信息 */
            map.put("CheckType", "1");// 人像对比

            Map<String, String> mapTwo = new HashMap<String, String>();
            mapTwo.putAll(map);
            mapTwo.put("CheckType", "2");// 人口状态

            String[] arr = verifyDate.split("、");
            String[] nameArray = checkName.split("、");
            String[] idArray = checkIDNo.split("、");
            if (arr.length >= 2) {
            	map.put("CustomerName", nameArray[0]);
            	map.put("CustomerIdCode", idArray[0]);
                map.put("YzDate", arr[0]);
                mapTwo.put("CustomerName", nameArray[1]);
                mapTwo.put("CustomerIdCode", idArray[1]);
                mapTwo.put("YzDate", arr[1]);
                
            }
            /** 添加到验真集合中 */
            listmap.add(map);
            listmap.add(mapTwo);
        } else {
            map.put("CheckType", isVerify);
//            // 客户姓名，因为方法里 取值为 CustomerName 所以put时 也需要传输CustomerName
//            if (null != checkName && StringUtils.isNotBlank(checkName)) {
//                map.put("CustomerName", checkName);
//            } else {
//                map.put("CustomerName", customerPO.getCustomerName());
//            }
//            // 客户证件号，因为方法里 取值为 CustomerIdCode 所以put时 也需要传输CustomerIdCode
//            if (null != checkIDNo && StringUtils.isNotBlank(checkIDNo)) {
//                map.put("CustomerIdCode", checkIDNo);
//            } else {
//                map.put("CustomerIdCode", customerPO.getCustomerIdCode());
//            }
            // 保存短信验真需要用到
            listmap.add(map);
        }

		if (!outterDealUCC.addValidationInfo2(listmap)) {
			logger.info("基本资料变更移动保全2.0接口信息保存验真信息保存失败-----------------------------------------------------------");
			return "保存验真信息保存失败！";
		}

		return Constants.SUCCESS;
    }

}
