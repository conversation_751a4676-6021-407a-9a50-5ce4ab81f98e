package com.nci.tunan.cs.impl.csEffect.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.core.common.impl.util.CommonBizKeyUtil;
import com.nci.core.common.impl.util.bean.BizkeyParam;
import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.dao.ICsSpecialAccountInfoDao;
import com.nci.tunan.cs.model.bo.CsAcceptChangeBO;
import com.nci.tunan.cs.model.bo.CsBankAccountBO;
import com.nci.tunan.cs.model.bo.CsContractMasterBO;
import com.nci.tunan.cs.model.bo.CsEffectBO;
import com.nci.tunan.cs.model.bo.CsPayerAccountBO;
import com.nci.tunan.cs.model.po.CsContractBusiProdPO;
import com.nci.tunan.cs.model.po.CsContractProductPO;
import com.nci.tunan.cs.model.po.CsPayerAccountPO;
import com.nci.tunan.cs.model.po.CsPolicyChangePO;
import com.nci.tunan.cs.model.po.CsSpecialAccountInfoPO;
import com.nci.tunan.cs.model.po.CsSpecialAccountRelationPO;
import com.nci.tunan.pa.dao.IPlatInvestAccBindTraceDao;
import com.nci.tunan.pa.dao.ISpecialAccountInfoDao;
import com.nci.tunan.pa.dao.ISpecialAccountRelationDao;
import com.nci.tunan.pa.impl.peripheral.service.r06402900875.IAccountBindingConfirmationService;
import com.nci.tunan.pa.interfaces.model.bo.SpecialAccountInfoBO;
import com.nci.tunan.pa.interfaces.model.bo.SpecialAccountRelationBO;
import com.nci.tunan.pa.interfaces.model.po.BankAccountPO;
import com.nci.tunan.pa.interfaces.model.po.BankPO;
import com.nci.tunan.pa.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.pa.interfaces.model.po.PlatInvestAccBindTracePO;
import com.nci.tunan.pa.interfaces.model.po.SpecialAccountInfoPO;
import com.nci.tunan.pa.interfaces.model.po.SpecialAccountRelationPO;
import com.nci.udmp.framework.tag.i18n.CodeTable;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;

/**
 * 
 * @description  关联银行卡保全项生效Service实现
 * @<NAME_EMAIL> 
 * @date 2015-05-15 
 * @.belongToModule 保全子系统  关联银行卡保全项生效Service实现
 */
public class CsEffectCDServiceImpl extends CsEffectServiceImpl {
	@Autowired
	@Qualifier("PA_CsSpecialAccountInfoDao")
    private ICsSpecialAccountInfoDao CsSpecialAccountInfoDao;

	/**
	 * 中银保账户与保单关联表
	 */
	@Autowired
	@Qualifier("PA_specialAccountRelationDao")
	private ISpecialAccountRelationDao specialAccountRelationDao;
	/**
	 * 中银保账户信息表
	 */
	@Autowired
	@Qualifier("PA_specialAccountInfoDao")
	private ISpecialAccountInfoDao specialAccountInfoDao;
	
	
	@Autowired
	@Qualifier("PA_accountBindingConfirmationService")
	private IAccountBindingConfirmationService accountBindingConfirmationService;
	
	
	@Autowired
	@Qualifier("PA_platInvestAccBindTraceDao")
	private IPlatInvestAccBindTraceDao platInvestAccBindTraceDao;
	/**
	 * 
	 * @description 生效方法
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csEffect.service.impl.CsEffectServiceImpl#csEffectService(com.nci.tunan.cs.model.bo.CsAcceptChangeBO)
	 * @param csAcceptChangeBO  保全受理信息BO对象
	 * @return
	 */
	@Override
	public CsEffectBO csEffectService(CsAcceptChangeBO csAcceptChangeBO) {
		CsEffectBO csEffectBO = new CsEffectBO();
		
		//@invalid /************************** 3.4 银行账户信息表的回写 start ********************************/
		// 1.查询银行账户信息表（新旧标识为new） T_CS_BANK_ACCOUNT 银行账户信息
		CsBankAccountBO csBankAccountBO = new CsBankAccountBO();
		csBankAccountBO.setChangeId(csAcceptChangeBO.getChangeId());
		csBankAccountBO.setAcceptId(csAcceptChangeBO.getAcceptId());
		csBankAccountBO.setOldNew(Constants.NEW);
		List<CsBankAccountBO> csBankAccountBOs = csCustomerService.findCsBankAccountBO(csBankAccountBO);

		if (CollectionUtils.isNotEmpty(csBankAccountBOs)) {

			for (CsBankAccountBO csBankAccountForPABO : csBankAccountBOs) {
				
				//2.复核报错,重复生效
				BankAccountPO bankPO = new BankAccountPO();
				bankPO.setBankAccount(csBankAccountForPABO.getBankAccount());
				bankPO.setCustomerId(csBankAccountForPABO.getCustomerId());
				bankPO.setBankCode(csBankAccountForPABO.getBankCode());
				//3.查询保单银行账户信息 T_CS_BANK_ACCOUNT 银行账户信息表
				List<BankAccountPO> bankList = bankAccountDao.findAllBankAccount(bankPO);
				
				/*if(null != bankList && bankList.size() > 0){
					continue;
				}*/
				
				if (Constants.OPERATIONTYPE_ADD.equals(csBankAccountForPABO.getOperationType())) {
					csBankAccountForPABO.setAccountId(null);
					//4.添加银行账户 T_CS_BANK_ACCOUNT 银行账户信息表
					CsBankAccountBO bankAccountBO=paCustomerService.addBankAccount(csBankAccountForPABO);
					csBankAccountForPABO.setAccountId(bankAccountBO.getAccountId());
					//5.修改客户银行账户信息 T_CS_BANK_ACCOUNT 银行账户信息表
					csCustomerService.updateCsBankAccountBO(csBankAccountForPABO);
				}
				if (Constants.OPERATIONTYPE_UPDATE.equals(csBankAccountForPABO.getOperationType())) {
					CsBankAccountBO accountBO = new CsBankAccountBO();
					accountBO.setAccountId(csBankAccountForPABO.getAccountId());
					//6.查询客户银行账户信息根据AccountId T_CS_BANK_ACCOUNT 银行账户信息表
					List<CsBankAccountBO> accountBOs = paCustomerService.findBankAccountList(accountBO);
					csBankAccountBO.setAccountId(csBankAccountForPABO.getAccountId());
					csBankAccountBO.setOldNew(Constants.OLD);
					//7.查询保全抄单客户银行账户为old的信息根据AccountId T_CS_BANK_ACCOUNT 银行账户信息表
					List<CsBankAccountBO> bankAccountBOs = csCustomerService.findCsBankAccountBO(csBankAccountBO);
					if (CollectionUtils.isNotEmpty(bankAccountBOs) & CollectionUtils.isNotEmpty(accountBOs)) {
						CsBankAccountBO bankAccountBO = bankAccountBOs.get(0);
						accountBO = accountBOs.get(0);

						if (bankAccountBO.getBankCode() != csBankAccountForPABO.getBankCode()) {
							accountBO.setBankCode(csBankAccountForPABO.getBankCode());
						}
						if (bankAccountBO.getBankAccount() != csBankAccountForPABO.getBankAccount()) {
							accountBO.setBankAccount(csBankAccountForPABO.getBankAccount());
						}
						if (bankAccountBO.getAccoName() != csBankAccountForPABO.getAccoName()) {
							accountBO.setAccoName(csBankAccountForPABO.getAccoName());
						}
						if (bankAccountBO.getIssueBankName() != csBankAccountForPABO.getIssueBankName()) {
							accountBO.setIssueBankName(csBankAccountForPABO.getIssueBankName());
						}
						//8.修改保单表银行账户信息 T_CS_BANK_ACCOUNT 银行账户信息表
						paCustomerService.updateBankAccount(accountBO);
					}
				}
				if (Constants.OPERATIONTYPE_DELETE.equals(csBankAccountForPABO.getOperationType())) {
					//9.删除客户下银行账户信息 T_CS_BANK_ACCOUNT 银行账户信息表
					paCustomerService.deleteBankAccount(csBankAccountForPABO);
				}
				
				String policy_code="";
				CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
				csPolicyChangePO.setAcceptId(csAcceptChangeBO.getAcceptId());
				csPolicyChangePO = csPolicyChangeDao.findCsPolicyChange(csPolicyChangePO);
				policy_code=csPolicyChangePO.getPolicyCode();
				
				CsSpecialAccountInfoPO csspecialaccountinfopos = new CsSpecialAccountInfoPO();
				csspecialaccountinfopos.setChangeId(csAcceptChangeBO.getChangeId());
				csspecialaccountinfopos.setPolicyChgId(csPolicyChangePO.getPolicyChgId());
				csspecialaccountinfopos.setOldNew("1");
				List<CsSpecialAccountInfoPO> csspecialaccountinfopoList=CsSpecialAccountInfoDao.findAllCsSpecialAccountInfo(csspecialaccountinfopos);
				if(csspecialaccountinfopoList.size()>0){
					for(CsSpecialAccountInfoPO csspecialaccountinfopo:csspecialaccountinfopoList){
						BankPO bankPO1 = new BankPO();
						bankPO1.setBankCode(csspecialaccountinfopo.getAccountBank());
						bankPO1 = bankDao.findBankByBankCode(bankPO1);
						String depositBankn1 = bankPO1.getBankName(); // 开户银行
						
						
						
						Document csPrintXmlDocument = csPrintEndorseService.initPrintElement(csAcceptChangeBO.getAcceptCode(),
								policy_code);
						 Element eltRoot = csPrintXmlDocument.getRootElement();
						 Element policyDataElement = eltRoot.element("DATASET").element("DATA");
						// 拼接自己的业务数据 开始
				            Element cUSItemNCodeElement = policyDataElement.addElement("CUSItemCode");
				            cUSItemNCodeElement.setText("CD"); // ServiceCode
				            Element cUSItemNameElement = policyDataElement.addElement("CUSItemName");
				            cUSItemNameElement.setText("账户维护");
				            Element cUSEdorInfoElement = policyDataElement.addElement("CUSEdorInfo");
				            Element EdorList61Element = cUSEdorInfoElement.addElement("EdorList61");
				            Element specialInsPol = EdorList61Element.addElement("SpecialInsPol");//是否专户投保保单
							Element customerName = EdorList61Element.addElement("CustomerName");//客户姓名
							Element iDType = EdorList61Element.addElement("IDType");//证件类型
							Element iDNo = EdorList61Element.addElement("IDNo");//证件号码
							Element depositBank = EdorList61Element.addElement("DepositBank");//开户银行
							Element accountName = EdorList61Element.addElement("AccountName");//账户名称
							Element accountNo = EdorList61Element.addElement("AccountNo");//账户号码
							Element telePhone = EdorList61Element.addElement("TelePhone");//预留手机号
							Element authorInfo = EdorList61Element.addElement("AuthorInfo");//授权信息
							
							// 2.1.1 查询保单主表（新旧标识为new）
			    			CsContractMasterBO csContractMasterBO = new CsContractMasterBO();
			    			csContractMasterBO.setChangeId(csAcceptChangeBO.getChangeId());
			    			csContractMasterBO.setPolicyChgId(csAcceptChangeBO.getPolicgChgId());
			    			csContractMasterBO.setOldNew(Constants.NEW);
			    			List<CsContractMasterBO> csContractMasterBOs = csGeneralInfoService.findCsContractMasters(csContractMasterBO);
				            if(csContractMasterBOs.size()>0){
				            	if("1".equals(csContractMasterBOs.get(0).getSpecialAccountFlag())){
				            		specialInsPol.setText("Y");//是否专户投保保单Y是N否
				            		authorInfo.setText("您同意授权本公司委托银行通过代扣方式从您指定的银行账户按照保险合同及本次保全申请约定的方式、金额收取应交保险费及以转账方式将保险金、退保金、退费等给付转入指定账户。");
				            	}else{
				            		specialInsPol.setText("N");//是否专户投保保单Y是N否
				            		authorInfo.setText("");
				            	}
				            }
				            customerName.setText(csspecialaccountinfopo.getCustomerName());//客户姓名
				            String clientIDType = CodeTable.getCodeDesc("APP___PAS__DBUSER.T_CERTI_TYPE", csspecialaccountinfopo.getCustomerCertType());
				            iDType.setText(clientIDType);//证件类型
				            iDNo.setText(csspecialaccountinfopo.getCustomerCertiCode());//证件号码
				            depositBank.setText(csspecialaccountinfopo.getAccountBank()+"-"+depositBankn1);//开户银行
				            accountName.setText(csspecialaccountinfopo.getAccountName());//账户名称
				            accountNo.setText(csspecialaccountinfopo.getAccount());//账户号码
				            //预留手机号
				            telePhone.setText(csspecialaccountinfopo.getBankPhone());
				            if (!csPrintEndorseService.callPrintInterface(csPrintXmlDocument ,csAcceptChangeBO.getAcceptCode(),policy_code)) { // 自测时设置为true
				                csEffectBO.setSucceed(false);
				                return csEffectBO;
				            }
					}
				}
				
		            
			}
		}
		//@invalid /************************** 3.4 银行账户信息表的回写 end ********************************/
		
		
		/***************专户保单生效走此逻辑*************/
		CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
		csPolicyChangePO.setAcceptId(csAcceptChangeBO.getAcceptId());
		csPolicyChangePO = csPolicyChangeDao.findCsPolicyChange(csPolicyChangePO);
		ContractMasterPO contractMasterPO = new ContractMasterPO();
		contractMasterPO.setPolicyId(csPolicyChangePO.getPolicyId());
		contractMasterPO = contractMasterDao.findContractMasterByPolicyId(contractMasterPO);
		if(contractMasterPO.getSpecialAccountFlag() != null && contractMasterPO.getSpecialAccountFlag().equals(Constants.ONE_STRING)){
			CsSpecialAccountRelationPO csSpecialAccountRelationPO = new CsSpecialAccountRelationPO();
			csSpecialAccountRelationPO.setChangeId(csAcceptChangeBO.getChangeId());
			csSpecialAccountRelationPO.setPolicyChgId(csPolicyChangePO.getPolicyChgId());
			csSpecialAccountRelationPO.setOldNew(Constants.NEW);
			csSpecialAccountRelationPO = csSpecialAccountRelationDao.findCsSpecialAccountRelation(csSpecialAccountRelationPO);
			CsSpecialAccountInfoPO csSpecialAccountInfoPO = new CsSpecialAccountInfoPO();
			csSpecialAccountInfoPO.setChangeId(csAcceptChangeBO.getChangeId());
			csSpecialAccountInfoPO.setPolicyChgId(csPolicyChangePO.getPolicyChgId());
			csSpecialAccountInfoPO.setOldNew(Constants.NEW);
			csSpecialAccountInfoPO.setOperationType(Constants.OPERATIONTYPE_ADD);
			csSpecialAccountInfoPO = csSpecialAccountInfoDao.findCsSpecialAccountInfo(csSpecialAccountInfoPO);
			
			SpecialAccountInfoPO specialAccountInfoPO = new SpecialAccountInfoPO();
			specialAccountInfoPO.setListId(csSpecialAccountRelationPO.getBankPolicyId());
			SpecialAccountInfoPO findSpecialAccountInfo = specialAccountInfoDao.findSpecialAccountInfo(specialAccountInfoPO);
			
			
			
			//如果抄单的新的那条没有数据，说明走的是CD的另一个页面，这里不做生效T表处理
			if (csSpecialAccountRelationPO.getListId() != null && findSpecialAccountInfo.getListId() == null) {
				logger.info("-----------专户投保保单生效开始，受理号:" + csAcceptChangeBO.getAcceptCode() + "---------------------");
				SpecialAccountInfoBO specialAccountInfoBO = BeanUtils.copyProperties(SpecialAccountInfoBO.class, csSpecialAccountInfoPO);
				specialAccountInfoPO = BeanUtils.copyProperties(SpecialAccountInfoPO.class, specialAccountInfoBO);
				specialAccountInfoDao.addSpecialAccountInfoForCS(specialAccountInfoPO);
				
				SpecialAccountRelationPO specialAccountRelationPO = new SpecialAccountRelationPO();
				SpecialAccountRelationBO specialAccountRelationBO = BeanUtils.copyProperties(SpecialAccountRelationBO.class, csSpecialAccountRelationPO);
				specialAccountRelationPO = BeanUtils.copyProperties(SpecialAccountRelationPO.class, specialAccountRelationBO);
				specialAccountRelationDao.updateSpecialAccountRelation(specialAccountRelationPO);
				
				//1.查询出所有的付款帐户表
				CsPayerAccountPO csPayerAccountPO = new CsPayerAccountPO();
				csPayerAccountPO.setChangeId(csAcceptChangeBO.getChangeId());
				csPayerAccountPO.setPolicyChgId(csSpecialAccountRelationPO.getPolicyChgId());
				csPayerAccountPO.setOldNew(Constants.NEW);
				List<CsPayerAccountPO> csPayerAccountPOList = new ArrayList<CsPayerAccountPO>();
				csPayerAccountPOList = csPayerAccountDao.findAllCsPayerAccount(csPayerAccountPO);
				for (CsPayerAccountPO csPayerAccountPO2 : csPayerAccountPOList) {			
					//@invalid 更新
					paGeneralInfoService.updatePayerAccount(BeanUtils.copyProperties(CsPayerAccountBO.class, csPayerAccountPO2));
				}
				
				
				logger.info("-----------专户投保保单生效结束，受理号:" + csAcceptChangeBO.getAcceptCode() + "---------------------");
			}
		}
		/***************专户保单生效走此逻辑*************/
		csEffectBO.setSucceed(true);
		return csEffectBO;
	}
}
