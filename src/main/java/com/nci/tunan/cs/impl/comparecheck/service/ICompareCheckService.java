package com.nci.tunan.cs.impl.comparecheck.service;

import com.nci.tunan.cs.impl.comparecheck.bo.CompareCheckBO;
import com.nci.tunan.cs.impl.comparecheck.bo.IDentityCheckMainBO;


/**
 * 
 * @description 验真查询
 * <AUTHOR> <EMAIL> 
 * @date 2020年12月31日 下午4:17:02 
 * @.belongToModule 保全子系统
 */
public interface ICompareCheckService {
	/**
	 * 
	 * @description 增加验真信息
	 * @version  V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param compareCheckBO compareCheckBO
	 * @return
	 */
	public CompareCheckBO addCompareCheck(CompareCheckBO compareCheckBO);
	/**
	 * 
	 * @description 增加验真信息
	 * @version  V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param iDentityCheckMainBO iDentityCheckMainBO
	 * @return
	 */
	public IDentityCheckMainBO addIDentityCheckMain(IDentityCheckMainBO iDentityCheckMainBO);
}
