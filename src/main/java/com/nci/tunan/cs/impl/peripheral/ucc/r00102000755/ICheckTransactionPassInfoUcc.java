package com.nci.tunan.cs.impl.peripheral.ucc.r00102000755;

import com.nci.tunan.cs.interfaces.peripheral.exports.r00102000755.vo.InputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102000755.vo.OutputData;
/**
 * 
 * @description 交易密码验证接口Ucc
 * <AUTHOR> <EMAIL> 
 * @date 2015年05月15日
 * @.belongToModule 保全子系统-交易密码验证接口Ucc
 */
public interface ICheckTransactionPassInfoUcc {
	public OutputData getCheckPassMgs (InputData inputVO);
}
