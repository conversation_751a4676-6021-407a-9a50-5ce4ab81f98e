package com.nci.tunan.cs.impl.csItem.service;

import java.math.BigDecimal;
import java.util.List;

import com.nci.tunan.cs.model.bo.CsContractBusiProdBO;
import com.nci.tunan.cs.model.bo.CsContractExtendBO;
import com.nci.tunan.cs.model.bo.CsContractProductBO;
import com.nci.tunan.cs.model.bo.CsEndorseDCBO;
import com.nci.tunan.cs.model.bo.CsPrecontProductBO;

/**
 * 
 * @description 万能险基本保额约定变更service
 * <AUTHOR> <EMAIL>
 * @date 2015年7月13日 下午4:18:01
 * @.belongToModule 保全系统
 */
public interface ICsEndorseDCService extends ICSItemBaseService {

	/**
	 * 
	 * @description 险种信息查询
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param changeId 变更id
	 * @param policyChgId 保单变更id
	 * @param oldNew 新旧标识
	 * @return
	 */
	public List<CsContractBusiProdBO> queryContractBusiProdBO(CsContractBusiProdBO csContractBusiProdBO);

	/**
	 * 
	 * @description 查询责任组信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractProductBO bo入参
	 * @return
	 */
	public List<CsContractProductBO> queryContractProduct(CsContractProductBO contractProductBO);

	/**
	 * 
	 * @description 查询缴费计划
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param contractExtendBO bo入参
	 * @return
	 */
	public CsContractExtendBO queryContractExtend(CsContractExtendBO contractExtendBO);

	/**
	 * 
	 * @description 查询险种保障预约信息 单条
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPrecontProductBO bo入参
	 * @return
	 */
	public CsPrecontProductBO queryPrecontProduct(CsPrecontProductBO csPrecontProductBO);

	/**
	 * 
	 * @description 查询险种保障预约信息集合
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPrecontProductBO bo入参
	 * @return
	 */
	public List<CsPrecontProductBO> queryPrecontProductList(CsPrecontProductBO csPrecontProductBO);

	/**
	 * 
	 * @description 修改险种保障预约信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPrecontProductBO bo入参
	 * @return
	 */
	public CsPrecontProductBO updateCsPrecontProduct(CsPrecontProductBO csPrecontProductBO);

	/**
	 * 
	 * @description 新增险种保障预约信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPrecontProductBO bo入参
	 */
	public void insertPrecontProduct(CsPrecontProductBO csPrecontProductBO);
	/**
	 * 
	 * @description 将新增险种保障预约信息取消
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPrecontProductBO bo入参
	 */
	public void deletePrecontProduct(CsPrecontProductBO csPrecontProductBO);
	/**
	 * 
	 * @description 查询被保人出生日期
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param policyCode 保单号
	 * @return
	 */
	public CsEndorseDCBO findInsureBirthday(BigDecimal policyId);
	
	/**
	 * 
	 * @description 查询变更前信息
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.impl.csItem.ucc.ICsEndorseDCUCC#loadBeforChangeInfo(java.math.BigDecimal,
	 *      java.math.BigDecimal, java.math.BigDecimal, java.lang.String)
	 * @param changeId 变更id
	 * @param acceptId 受理id
	 * @param customerId 客户id
	 * @param oldNew   新旧标识
	 * @return
	 */
	public List<CsEndorseDCBO> loadBeforChangeInfo(BigDecimal changeId, BigDecimal acceptId,
			BigDecimal customerId, String oldNew);
	
	/**
	 * 
	 * @description 变更后信息
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.impl.csItem.ucc.ICsEndorseDCUCC#loadAfterChangeInfo(java.util.List)
	 * @param csEndorseDCVOs 入参
	 * @return
	 */
	public List<CsEndorseDCBO> loadAfterChangeInfo(List<CsEndorseDCBO> csEndorseDCBOs);
	
	/**
	 * 
	 * @description 新增险种保障预约信息
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.impl.csItem.ucc.ICsEndorseDCUCC#insertPrecontProduct(java.util.List)
	 * @param csEndorseDCVOs 入参 
	 */
	public void insertPrecontProduct(List<CsEndorseDCBO> csEndorseDCBOs);
	
	/**
	 * 
	 * @description 删除险种保障预约信息
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.impl.csItem.ucc.ICsEndorseDCUCC#deletePrecontProduct(java.lang.String)
	 * @param jsonMsg json入参
	 * @return
	 */
	public List<CsEndorseDCBO> deletePrecontProduct(String jsonMsg);
	
	/**
	 * 
	 * @description 变更后信息(保全查询)
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.impl.csItem.ucc.ICsEndorseDCUCC#afterChangeInfo(java.math.BigDecimal,
	 *      java.math.BigDecimal, java.math.BigDecimal, java.lang.String)
	 * @param changeId 变更id
	 * @param acceptId 受理id
	 * @param customerId 客户id
	 * @param oldNew 新旧标识 
	 * @return
	 */
	public List<CsEndorseDCBO> afterChangeInfo(BigDecimal changeId, BigDecimal acceptId,
			BigDecimal customerId, String oldNew);
	
	/**
	 * @description	获取变更前的记录
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL>
	 * @param changeId 变更id
	 * @param acceptId 受理id
	 * @param customerId 客户id
	 * @param oldNew 新旧标识 
	 * @return 
	*/
	public List<CsEndorseDCBO> loadChangerBefor(BigDecimal changeId, BigDecimal acceptId, BigDecimal customerId, String oldNew);
}
