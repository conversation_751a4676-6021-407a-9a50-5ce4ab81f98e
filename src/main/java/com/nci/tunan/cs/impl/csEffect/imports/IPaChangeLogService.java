package com.nci.tunan.cs.impl.csEffect.imports;

import java.util.List;

import com.nci.tunan.cs.model.bo.CsPayDueBO;
import com.nci.tunan.cs.model.bo.CsPayPlanBO;
import com.nci.tunan.cs.model.bo.CsPayPlanPayeeBO;
import com.nci.tunan.cs.model.bo.CsPolicyChangeLogBO;


/**
 * 
 * @description 变更履历表
 * @<NAME_EMAIL> 
 * @date 2015-10-13 上午10:52:44 
 * @.belongToModule 保全系统-变更履历表
 */
public interface IPaChangeLogService
{
	/**
	 * 
	 * @description 查询保单变更履历表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param policyChangeBO 保单受理变更管理信息学
	 * @return
	 */
	public List<CsPolicyChangeLogBO> findPolicyChangeList(CsPolicyChangeLogBO csPolicyChangeLogBO);
	
	/**
	 * 
	 * @description 添加保单变更履历表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param policyChangeBO 保单受理变更管理信息学
	 * @return
	 */
	public CsPolicyChangeLogBO addPolicyChange(CsPolicyChangeLogBO csPolicyChangeLogBO);
}
