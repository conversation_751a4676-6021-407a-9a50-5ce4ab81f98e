package com.nci.tunan.cs.impl.peripheral.ucc.r00101000036.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONArray;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.dao.IBusinessProductDao;
import com.nci.tunan.cs.dao.ICsAcceptChangeDao;
import com.nci.tunan.cs.impl.commonFlow.service.ICusApplicationService;
import com.nci.tunan.cs.impl.commonFlow.ucc.ICusAcceptUCC;
import com.nci.tunan.cs.impl.commonFlow.ucc.ICusApplicationUCC;
import com.nci.tunan.cs.impl.csItem.ucc.ICsEndorseCTUCC;
import com.nci.tunan.cs.impl.csManual.ucc.ISurrenderQuotAppUCC;
import com.nci.tunan.cs.impl.outterdealfa.constants.IntegralOldNewConvert;
import com.nci.tunan.cs.impl.outterdealfa.ucc.IOutterDealUCC;
import com.nci.tunan.cs.impl.peripheral.service.r00101000036.IBusiDetailSurrendTryQuotService;
import com.nci.tunan.cs.impl.peripheral.ucc.r00101000036.IBusiDetailSurrendTryQuotUcc;
import com.nci.tunan.cs.imports.IPASImportsAdapterService;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00101000036.vo.InputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00101000036.vo.OutputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00101000036.vo.Result;
import com.nci.tunan.cs.model.bo.CsAcceptChangeBO;
import com.nci.tunan.cs.model.po.BusinessProductPO;
import com.nci.tunan.cs.model.po.CsAcceptChangePO;
import com.nci.tunan.cs.model.vo.ChangeVO;
import com.nci.tunan.cs.model.vo.CsAcceptInfoEntryVO;
import com.nci.tunan.cs.model.vo.CsAcceptPolicyInfoVO;
import com.nci.tunan.cs.model.vo.CsAcceptVO;
import com.nci.tunan.cs.model.vo.CsApplicationVO;
import com.nci.tunan.cs.model.vo.CsEndorseCTVO;
import com.nci.tunan.cs.model.vo.SurrenderMoneyVO;
import com.nci.tunan.cs.model.vo.SurrenderQuotAppVO;
import com.nci.tunan.cs.model.vo.SurrenderVO;
import com.nci.tunan.pa.dao.IContractBusiProdDao;
import com.nci.tunan.pa.dao.IContractMasterDao;
import com.nci.tunan.pa.dao.IContractProductDao;
import com.nci.tunan.pa.dao.ICustomerDao;
import com.nci.tunan.pa.dao.IPayPlanDao;
import com.nci.tunan.pa.dao.IPolicyHolderDao;
import com.nci.tunan.pa.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.pa.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.pa.interfaces.model.po.ContractProductPO;
import com.nci.tunan.pa.interfaces.model.po.CustomerPO;
import com.nci.tunan.pa.interfaces.model.po.PayPlanPO;
import com.nci.tunan.pa.interfaces.model.po.PolicyHolderPO;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.framework.context.AppUser;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.xml.transform.XmlHelper;

/**
 * 
 * @description  退保试算险种退费明细信息查询UCC实现  服务编码：P00001000296
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2018年3月26日 上午11:09:41
 */
public class BusiDetailSurrendTryQuotUccImpl implements IBusiDetailSurrendTryQuotUcc {
	/**
	 * 日志记录公共
	 */
	private static Logger logger = LoggerFactory.getLogger("testOutterdeal");
	/**
	 * 退保试算险种退费明细信息查询Service
	 */
	private IBusiDetailSurrendTryQuotService busiDetailSurrendTryQuotService;
	/**
	 * 保全申请受理信息服务Service
	 */
	protected ICusApplicationService cusApplicationService;
	/**
	 * 退保试算服务UCC
	 */
	private ISurrenderQuotAppUCC surrenderQuotAppUCC;
	/**
	 * 产品服务Dao
	 */
	private IBusinessProductDao iBusinessProductDao;
	/**
	 * 保单险种服务Dao
	 */
	private IContractBusiProdDao iContractBusiProdDao;
	/**
	 * 保单管理服务Dao
	 */
	private IContractMasterDao contractMasterDao;
	/**
	 * 保单投保人服务Dao
	 */
	private IPolicyHolderDao policyHolderDao;
	/**
	 * 保单责任组服务Dao
	 */
	private IContractProductDao contractProductDao;
	/**
	 * 客户服务Dao
	 */
	private ICustomerDao customerDao;
	/**
	 * 保全申请信息UCC
	 */
	private ICusApplicationUCC cusApplicationUCC;
	/**
	 * 外围服务UCC服务
	 */
	private IOutterDealUCC outterDealUCC;
	/**
	 * 保单给付计划服Dao
	 */
	@Autowired
	@Qualifier("PA_payPlanDao")
	private IPayPlanDao payPlanDao;
	public ICsAcceptChangeDao getCsAcceptChangeDao() {
		return csAcceptChangeDao;
	}

	public void setCsAcceptChangeDao(ICsAcceptChangeDao csAcceptChangeDao) {
		this.csAcceptChangeDao = csAcceptChangeDao;
	}

	/**
	 * 保全退保UCC服务
	 */
	private ICsEndorseCTUCC csEndorseCTUCC;
	/**
	 * 保全受理服务UCC
	 */
	private ICusAcceptUCC cusAcceptUCC;
	/**
	 * 保全服务受理服务Fao
	 */
	private ICsAcceptChangeDao csAcceptChangeDao;
	public void setCusAcceptUCC(ICusAcceptUCC cusAcceptUCC) {
		this.cusAcceptUCC = cusAcceptUCC;
	}

	public void setCsEndorseCTUCC(ICsEndorseCTUCC csEndorseCTUCC) {
		this.csEndorseCTUCC = csEndorseCTUCC;
	}

	public IContractProductDao getContractProductDao() {
		return contractProductDao;
	}

	public void setContractProductDao(IContractProductDao contractProductDao) {
		this.contractProductDao = contractProductDao;
	}

	public void setOutterDealUCC(IOutterDealUCC outterDealUCC) {
		this.outterDealUCC = outterDealUCC;
	}

	public void setCusApplicationUCC(ICusApplicationUCC cusApplicationUCC) {
		this.cusApplicationUCC = cusApplicationUCC;
	}

	public void setCustomerDao(ICustomerDao customerDao) {
		this.customerDao = customerDao;
	}

	public void setPolicyHolderDao(IPolicyHolderDao policyHolderDao) {
		this.policyHolderDao = policyHolderDao;
	}

	

	public IContractMasterDao getContractMasterDao() {
		return contractMasterDao;
	}

	public void setContractMasterDao(IContractMasterDao contractMasterDao) {
		this.contractMasterDao = contractMasterDao;
	}

	public IContractBusiProdDao getiContractBusiProdDao() {
		return iContractBusiProdDao;
	}

	public void setiContractBusiProdDao(IContractBusiProdDao iContractBusiProdDao) {
		this.iContractBusiProdDao = iContractBusiProdDao;
	}

	public void setiBusinessProductDao(IBusinessProductDao iBusinessProductDao) {
		this.iBusinessProductDao = iBusinessProductDao;
	}

	public void setSurrenderQuotAppUCC(ISurrenderQuotAppUCC surrenderQuotAppUCC) {
		this.surrenderQuotAppUCC = surrenderQuotAppUCC;
	}

	public void setCusApplicationService(ICusApplicationService cusApplicationService) {
		this.cusApplicationService = cusApplicationService;
	}

	public IBusiDetailSurrendTryQuotService getBusiDetailSurrendTryQuotService() {
		return busiDetailSurrendTryQuotService;
	}

	public void setBusiDetailSurrendTryQuotService(IBusiDetailSurrendTryQuotService busiDetailSurrendTryQuotService) {
		this.busiDetailSurrendTryQuotService = busiDetailSurrendTryQuotService;
	}

	/**
	 * 公共保單服務Service
	 */
	private IPASImportsAdapterService pasIAS;// 调用接口查询保单相关信息

	/**
	 * 
	 * @description  退保试算险种退费明细信息查询
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.peripheral.ucc.r00101000036.IBusiDetailSurrendTryQuotUcc#busiDetailSurrendTryQuot(com.nci.tunan.cs.interfaces.peripheral.exports.r00101000036.vo.InputData)
	 * @param inputData  接口入参
	 * @return OutputData 出参
	 */
	@Override
	public OutputData busiDetailSurrendTryQuot(InputData inputData) {
		OutputData outputData = new OutputData();
		SysHeader sysHeader = CommonHeaderDeal.getSYSHEADERTHREAD();
		BigDecimal changeId = null;
		BigDecimal acceptId = null;	
		try{
		sysHeader.setBizResCd("0");//@invalid  业务响应码赋值成功
		sysHeader.setBizResText("");//@invalid  成功描述
		Date applyTimeDate = null;
		if (inputData.getContNo() != null && !("").equals(inputData.getContNo())) {
			String sysdateStr = null;
			if (inputData.getCTTestDate() != null && !("").equals(inputData.getCTTestDate())) {
				try {
					applyTimeDate = DateUtilsEx.toDate(inputData.getCTTestDate(), "yyyy-MM-dd");
				} catch (Exception e) {
					e.printStackTrace();
					sysHeader.setBizResCd("1");//@invalid  业务响应码赋值失败
					sysHeader.setBizResText("输入的日期格式不正确！");// @invalid 失败描述
					return outputData;
				}
			} else { //@invalid  暂时这样;bug 33708获取（zsxh用户）申请时间 报文对比用;
				AppUser user = new AppUser();
				user.setUserName("zsxh");
				try {
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
					sysdateStr = sdf.format(WorkDateUtil.getWorkDate(user));
					applyTimeDate = sdf.parse(sysdateStr);
				} catch (Exception e) {
					e.printStackTrace();
				}

			}

			ContractMasterPO contractMasterPO = new ContractMasterPO();
			contractMasterPO.setPolicyCode(inputData.getContNo());
			//1查询保单信息
			contractMasterPO = contractMasterDao.findContractMaster(contractMasterPO);
			if (null == contractMasterPO || contractMasterPO.getData().size() == 0) {
				sysHeader.setBizResCd("1");
				sysHeader.setBizResText("保单查询为空");
				return outputData;
			}
			if(Constants.LIABILITY_STATE__TERMINATED.compareTo(contractMasterPO.getLiabilityState()) == 0){
				sysHeader.setBizResCd("1");
				sysHeader.setBizResText("保单已终止，不能受理退保试算，请确认");
				return outputData;
			}
			PayPlanPO payPlanPO = new PayPlanPO();
			payPlanPO.setPolicyCode(inputData.getContNo());
			//2查询给付计划
			List<PayPlanPO> payPlanPOList = payPlanDao.queryUnclaimLiGold(payPlanPO);
			if(!payPlanPOList.isEmpty() && payPlanPOList.size()>0){
				sysHeader.setBizResCd("1");
				sysHeader.setBizResText("有应领未领生存金，不能受理退保试算，请确认");
				return outputData;
			}
			AppUser appUser = AppUserContext.getCurrentUser();
			appUser.setOrganCode(contractMasterPO.getOrganCode());
			logger.info(appUser + "用户信息====================");
			// @invalid 保单id;
			BigDecimal policyId = contractMasterPO.getPolicyId();
			String policyCode = inputData.getContNo();
			ContractProductPO contractProductPO = new ContractProductPO();
			contractProductPO.setPolicyId(contractMasterPO.getPolicyId());
			//3查询责任信息
			List<ContractProductPO> contractProductList = contractProductDao
					.findContractProductByPolicyId(contractProductPO);
			int size = contractProductList.size();
			String[] jsonArray = new String[size];
			for (int i = 0; i < size; i++) {
				BigDecimal itemId = contractProductList.get(i).getItemId();
				BigDecimal busiItemId = contractProductList.get(i).getBusiItemId();
				if (sysdateStr != null) {
					jsonArray[i] = sysdateStr + "," + itemId + "," + policyId + "," + busiItemId + "," + policyCode;
				} else {
					jsonArray[i] = applyTimeDate + "," + itemId + "," + policyId + "," + busiItemId + "," + policyCode;
				}
			}
			Date date = new Date();
			Long ilogTime1 = date.getTime();
			logger.info("surrenderQuotAppUCC.calCTInfo退保试算险种退费明细信息查询）开始进入时间" + ilogTime1);
			SurrenderQuotAppVO calCTInfo = surrenderQuotAppUCC.calCTInfo(jsonArray, applyTimeDate,
					contractMasterPO.getPolicyId());
			Date date2 = new Date();
			Long ilogTime2 = date2.getTime();

			logger.info("surrenderQuotAppUCC.calCTInfo退保试算险种退费明细信息查询）耗时:" + (ilogTime2 - ilogTime1));
			List<SurrenderMoneyVO> queryDetail = new ArrayList<SurrenderMoneyVO>();
			Date date3 = new Date();
			Long ilogTime3 = date3.getTime();

			logger.info("surrenderQuotAppUCC.queryDetail退保试算险种退费明细信息查询）开始进入时间" + ilogTime3);
			if (calCTInfo.isCheckRule()) {
				queryDetail = surrenderQuotAppUCC.queryDetail(jsonArray, applyTimeDate, contractMasterPO.getPolicyId());
			}
			System.out.println("queryDetail=="+XmlHelper.classToXml(queryDetail));
			Date date4 = new Date();
			Long ilogTime4 = date4.getTime();
			logger.info("surrenderQuotAppUCC.queryDetail退保试算险种退费明细信息查询）耗时:" + (ilogTime4 - ilogTime3));
			ContractBusiProdPO busiProdPO = new ContractBusiProdPO();
			busiProdPO.setPolicyCode(inputData.getContNo());
			BusinessProductPO businessProductPO = new BusinessProductPO();
			if (null != queryDetail && queryDetail.size() > 0) {
				List<Result> results = new ArrayList<Result>();
				//@invalid  bug 33708报文对比;
				for (SurrenderMoneyVO po : queryDetail) {
					//1 获取BUSINESS_PRD_ID
					BigDecimal busiPrdId = po.getBusiPrdId();
					//2 根据BUSINESS_PRD_ID查询险种号码;
					ContractBusiProdPO contractbusiprodpo = new ContractBusiProdPO();
					contractbusiprodpo.setBusiPrdId(po.getBusiPrdId());
					contractbusiprodpo.setBusiItemId(po.getBusiItemId());
					contractbusiprodpo = iContractBusiProdDao.findContractBusiProd(contractbusiprodpo);
					//3 查询险种名称;
					businessProductPO.setBusinessPrdId(busiPrdId);
					BusinessProductPO BusinessProduct = iBusinessProductDao.findBusinessProduct(businessProductPO);
					//@invalid /** 犹豫期退保费 */
					if(po.getHesitateFlag()!=null && Constants.YES_NO__YES.compareTo(po.getHesitateFlag())==0){
						if(null != po.getActualReturnPremium() && !"".equals(po.getActualReturnPremium().toString())
								&& po.getActualReturnPremium().compareTo(new BigDecimal(0)) == 1) { 
						Result result = new Result();
						result.setFee(null==po.getBasicPremCashValue()?"0":po.getActualReturnPremium().negate().toString());
						result.setFeeType("退保金");
						result.setFeeName("犹豫期保费应退金额");
						result.setRiskCode(po.getBusiProdCode());				  
						result.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						result.setRiskName(BusinessProduct.getProductNameSys());
						results.add(result);
						}
					}else{
					
				    //4判断是否是万能险  万能险取万能险现价
					if (BusinessProduct != null
							&& (Constants.PROD_BIZ_CATEGORY__20003.compareTo(BusinessProduct.getProductCategory1()) == 0 || 
								Constants.PROD_BIZ_CATEGORY__20004.compareTo(BusinessProduct.getProductCategory1()) == 0) ) {
						if (null != po.getUniversallCashValue() && !"".equals(po.getUniversallCashValue().toString())
								&& po.getUniversallCashValue().compareTo(new BigDecimal(0)) == 1) { 
						Result result = new Result();
						result.setFee(null==po.getUniversallCashValue()?"0":po.getUniversallCashValue().negate().toString());
						result.setFeeType("退保金");
						result.setFeeName("万能险现价应退金额 ");
						result.setRiskCode(po.getBusiProdCode());				  
						result.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						result.setRiskName(BusinessProduct.getProductNameSys());
						results.add(result);
						}
						// 5、修改接口现有的“万能险退保手续费”费用金额不显示负号，只显示具体金额。 RM121392
						if (null != po.getCtCashFee() && !"".equals(po.getCtCashFee().toString()) && po.getCtCashFee().compareTo(new BigDecimal(0)) == 1) { 
						Result result = new Result();
						result.setFee(null==po.getCtCashFee()?"0":po.getCtCashFee().toString());
						result.setFeeType("退保金");
						result.setFeeName("万能险退保手续费");
						result.setRiskCode(po.getBusiProdCode());				  
						result.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						result.setRiskName(BusinessProduct.getProductNameSys());
						results.add(result);
						}
					}else{
						//5获取普通险种取基本保额现价
						// 3、修改接口现有的“基本保额应退金额”数据取值为“基本保额现价”（与核心系统保持一致）。 RM121392
						if (null != po.getStandAmount() && !"".equals(po.getStandAmount().toString())
								&& po.getStandAmount().compareTo(new BigDecimal(0)) == 1) { 
						Result result = new Result();
						result.setFee(null==po.getStandAmount()?"0":po.getStandAmount().negate().toString());
						result.setFeeType("退保金");
						result.setFeeName("基本保额应退金额");
						result.setRiskCode(po.getBusiProdCode());				  
						result.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						result.setRiskName(BusinessProduct.getProductNameSys());
						results.add(result);
						}
					}				
					if (null != po.getLoanCapital() && !"".equals(po.getLoanCapital().toString())
							&& po.getLoanCapital().compareTo(new BigDecimal(0)) == 1) { // @invalid 费用为0时不返回;
						Result result1 = new Result();
						result1.setFee(po.getLoanCapital().toString());//@invalid 和保全确认LoanCapital指本金
						result1.setFeeType("质押贷款还款");
						result1.setFeeName("贷款本金清偿");
						result1.setRiskCode(po.getBusiProdCode());
						result1.setRiskName(BusinessProduct.getProductNameSys());
						result1.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result1);
					}
					if (null != po.getLoanInterest() && !"".equals(po.getLoanInterest())
							&& po.getLoanInterest().compareTo(new BigDecimal(0)) == 1) {
						Result result2 = new Result();
						result2.setFee(po.getLoanInterest().toString());
						result2.setFeeType("利息收入");
						result2.setFeeName("贷款利息清偿");
						result2.setRiskCode(po.getBusiProdCode());
						result2.setRiskName(BusinessProduct.getProductNameSys());
						result2.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result2);
					}
					// TODO 4、修改接口现有的“累计红利保额应退金额”数据取值为“红利保额现价”（与核心系统保持一致）。 RM121392
					if (null != po.getBonusSaCashValue() && !"".equals(po.getBonusSaCashValue().toString())
							&& po.getBonusSaCashValue().compareTo(new BigDecimal(0)) == 1) {
						Result result4 = new Result();
						result4.setFee(po.getBonusAmount().negate().toString());
						result4.setFeeType("退保金");
						result4.setFeeName("累计红利保额应退金额");
						result4.setRiskCode(po.getBusiProdCode());
						result4.setRiskName(BusinessProduct.getProductNameSys());
						result4.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result4);
					}
					//返回补发红利利息 生产缺陷29181 
					if(null != po.getReissuedInterest() && !"".equals(po.getReissuedInterest().toString())
						&& po.getReissuedInterest().compareTo(new BigDecimal(0)) == 1){
						Result result5 = new Result();
						result5.setFee(po.getReissuedInterest().negate().toString());
						result5.setFeeType("退保金");
						result5.setFeeName("现金补发红利利息");
						result5.setRiskCode(po.getBusiProdCode());
						result5.setRiskName(BusinessProduct.getProductNameSys());
						result5.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result5);
					}
					if (null != po.getEndBonus() && !"".equals(po.getEndBonus().toString())
							&& po.getEndBonus().compareTo(new BigDecimal(0)) == 1) {
						Result result3 = new Result();
						result3.setFee(po.getEndBonus().negate().toString());
						result3.setFeeType("退保金");
						result3.setFeeName("终了红利");
						result3.setRiskCode(po.getBusiProdCode());
						result3.setRiskName(BusinessProduct.getProductNameSys());
						result3.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result3);
					}
					//@invalid 返回现金红利;
					if(null != po.getCashBonus() && !"".equals(po.getCashBonus().toString())
						&& po.getCashBonus().compareTo(new BigDecimal(0)) == 1){
						Result result5 = new Result();
						result5.setFee(po.getCashBonus().negate().toString());
						result5.setFeeType("退保金");
						result5.setFeeName("现金红利");
						result5.setRiskCode(po.getBusiProdCode());
						result5.setRiskName(BusinessProduct.getProductNameSys());
						result5.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result5);
					}
					//@invalid 返回健康职业加费;
					if(null != po.getExtraPremAf() && !"".equals(po.getExtraPremAf().toString())
						&& po.getExtraPremAf().compareTo(new BigDecimal(0)) == 1){
						Result result5 = new Result();
						result5.setFee(po.getExtraPremAf().negate().toString());
						result5.setFeeType("退保金");
						result5.setFeeName("健康职业加费");
						result5.setRiskCode(po.getBusiProdCode());
						result5.setRiskName(BusinessProduct.getProductNameSys());
						result5.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result5);
					}
					//@invalid 返回累积生息;  2、删除接口现有的“累计生息账户本息”的数据。RM121392
					/*if(null != po.getCalProfit() && !"".equals(po.getCalProfit().toString())
						&& po.getCalProfit().compareTo(new BigDecimal(0)) == 1){
						Result result5 = new Result();
						result5.setFee(po.getCalProfit().negate().toString());
						result5.setFeeType("退保金");
						result5.setFeeName("累积生息账户本息");
						result5.setRiskCode(po.getBusiProdCode());
						result5.setRiskName(BusinessProduct.getProductNameSys());
						result5.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result5);
					}*/
					//@invalid 返回红利账户;  1、删除接口现有的“现金红利账户本息”的数据。RM121392
					/*if(null != po.getCashBonusAccount() && !"".equals(po.getCashBonusAccount().toString())
						&& po.getCashBonusAccount().compareTo(new BigDecimal(0)) == 1){
						Result result5 = new Result();
						result5.setFee(po.getCashBonusAccount().negate().toString());
						result5.setFeeType("退保金");
						result5.setFeeName("现金红利账户本息");
						result5.setRiskCode(po.getBusiProdCode());
						result5.setRiskName(BusinessProduct.getProductNameSys());
						result5.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result5);
					}*/
					//@invalid 返回应未领生存金;
					if(null != po.getRevivilFee() && !"".equals(po.getRevivilFee().toString())
						&& po.getRevivilFee().compareTo(new BigDecimal(0)) == 1){
						Result result5 = new Result();
						result5.setFee(po.getRevivilFee().negate().toString());
						result5.setFeeType("退保金");
						result5.setFeeName("应领未领生存金");
						result5.setRiskCode(po.getBusiProdCode());
						result5.setRiskName(BusinessProduct.getProductNameSys());
						result5.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result5);
					}
					//@invalid 返回终了结算利息
					if(null != po.getEndInterest() && !"".equals(po.getEndInterest().toString())
							&& po.getEndInterest().compareTo(new BigDecimal(0)) == 1){
						Result result6 = new Result();
						result6.setFee(po.getEndInterest().negate().toString());
						result6.setFeeType("退保金");
						result6.setFeeName("终了结算利息");
						result6.setRiskCode(po.getBusiProdCode());
						result6.setRiskName(BusinessProduct.getProductNameSys());
						result6.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result6);
					}
					//@invalid 应领未领生存金
					if(null != po.getRevivilFee() && !"".equals(po.getRevivilFee().toString())
							&& po.getRevivilFee().compareTo(new BigDecimal(0)) == 1){
						Result result7 = new Result();
						result7.setFee(po.getRevivilFee().negate().toString());
						result7.setFeeType("退保金");
						result7.setFeeName("应领未领生存金");
						result7.setRiskCode(po.getBusiProdCode());
						result7.setRiskName(BusinessProduct.getProductNameSys());
						result7.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result7);
					}
					//@invalid 工本费;
					if(null != po.getNominalFee() && !"".equals(po.getNominalFee().toString())
							&& po.getNominalFee().compareTo(new BigDecimal(0)) == 1){
						Result result8 = new Result();
						result8.setFee(po.getNominalFee().toString());
						result8.setFeeType("工本费");
						result8.setFeeName("工本费");
						result8.setRiskCode(po.getBusiProdCode());
						result8.setRiskName(BusinessProduct.getProductNameSys());
						result8.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result8);
					}
					//@invalid 自垫本息合计 advanced
					if(null != po.getAdvanced() && !"".equals(po.getAdvanced().toString())
							&& po.getAdvanced().compareTo(new BigDecimal(0)) == 1){
						Result result9 = new Result();
						result9.setFee(po.getAdvanced().toString());
						result9.setFeeType("自垫本息合计");
						result9.setFeeName("自垫本息合计");
						result9.setRiskCode(po.getBusiProdCode());
						result9.setRiskName(BusinessProduct.getProductNameSys());
						result9.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result9);
					}
					//@invalid 利差账户金额
					if(null != po.getProfitGap() && !"".equals(po.getProfitGap().toString())
							&& po.getProfitGap().compareTo(new BigDecimal(0)) == 1){
						Result result9 = new Result();
						result9.setFee(po.getProfitGap().negate().toString());
						result9.setFeeType("自垫本息合计");
						result9.setFeeName("自垫本息合计");
						result9.setRiskCode(po.getBusiProdCode());
						result9.setRiskName(BusinessProduct.getProductNameSys());
						result9.setPolNo(null==contractbusiprodpo.getOldPolNo()?contractbusiprodpo.getBusiItemId().toString():contractbusiprodpo.getOldPolNo());
						results.add(result9);
					}
 				  }
					changeId = po.getChangeId();
					
				}

				CommonHeaderDeal.setSYSHEADERTHREAD(sysHeader);
				outputData.setResults(results);
				// @invalid 返回分页条数;
				BizHeaderExB bizheader = CommonHeaderDeal.getBIZHEADERTHREAD_EXB();
				bizheader.setTotalRowNum(String.valueOf(outputData.getResults().size()));
			} else {
				sysHeader.setBizResCd("1");// @invalid 业务响应码赋值失败
				sysHeader.setBizResText("查询险种退费信息失败！");//@invalid  失败描述
				return outputData;
			}
		}
		if(changeId != null){
			CsAcceptChangePO csAcceptChangePO = new CsAcceptChangePO();
			csAcceptChangePO.setChangeId(changeId);
			CsAcceptChangePO findCsAcceptChange = csAcceptChangeDao.findCsAcceptChange(csAcceptChangePO);
			acceptId = findCsAcceptChange.getAcceptId();
		}		
		return outputData;
		}catch(Exception e){
			sysHeader.setBizResCd("1");
        	sysHeader.setBizResText("调用接口过程中产生异常!"+e.getMessage());
        	logger.error("调用接口过程中产生异常!"+sysHeader.getMsgId(),e);
        	if(acceptId!=null){
        	outterDealUCC.releaseLockByAcceptId(acceptId);
        	}
        	return outputData;
		}finally{
			//@invalid 调用删除所有保全数据的方法
			if(acceptId!=null){
				boolean flag = outterDealUCC.deleteChangeInfo(acceptId);
				if(flag){
					logger.info("删除保全生成的数据调用成功-----------------------------------------");
				}else{
					logger.info("删除保全生成的数据调用失败-----------------------------------------");
				}
			}
 
		}
	}
	/**
	 * 
	 * @description  判断终止原因的保单
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.peripheral.ucc.r00101000036.IBusiDetailSurrendTryQuotUcc#busiDetailSurrendTryQuot(com.nci.tunan.cs.interfaces.peripheral.exports.r00101000036.vo.InputData)
	 * @param csContractProductPO  接口入参
	 * @return csContractProductPO 出参
	 */
	private boolean prodTrueOrFalse(ContractProductPO csContractProductPO) {
		if (!Constants.LIABILITY_STATE__TERMINATED.equals(csContractProductPO.getLiabilityState())
				|| "06".equals(csContractProductPO.getEndCause()) || "08".equals(csContractProductPO.getEndCause())
				|| "11".equals(csContractProductPO.getEndCause())) {
			// 3.1. 以下终止原因的保单除外（可以退保）：贷款终止06，自垫终止08或永久失效11；
			return true;
		}
		return false;
	}


	public IPASImportsAdapterService getPasIAS() {
		return pasIAS;
	}

	public void setPasIAS(IPASImportsAdapterService pasIAS) {
		this.pasIAS = pasIAS;
	}

}
