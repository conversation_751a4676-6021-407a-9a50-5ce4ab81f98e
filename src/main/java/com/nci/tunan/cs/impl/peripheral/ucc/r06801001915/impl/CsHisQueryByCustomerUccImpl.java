package com.nci.tunan.cs.impl.peripheral.ucc.r06801001915.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.impl.peripheral.service.r06801001915.ICsHisQueryByCustomerService;
import com.nci.tunan.cs.impl.peripheral.ucc.r06801001915.ICsHisQueryByCustomerUcc;
import com.nci.tunan.cs.interfaces.peripheral.exports.r06801001915.vo.InputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r06801001915.vo.OutputData;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;

/**
 * 
 * @description  既往保全查询
 * <AUTHOR> 
 * @date 2020年12月23日 下午7:38:18 
 * @.belongToModule  保全子系统
 */
public class CsHisQueryByCustomerUccImpl implements ICsHisQueryByCustomerUcc{
	/**
	 * 既往保全查询service 
	 */
	private ICsHisQueryByCustomerService csHisQueryByCustomerService;

	public ICsHisQueryByCustomerService getCsHisQueryByCustomerService() {
		return csHisQueryByCustomerService;
	}

	public void setCsHisQueryByCustomerService(
			ICsHisQueryByCustomerService csHisQueryByCustomerService) {
		this.csHisQueryByCustomerService = csHisQueryByCustomerService;
	}
	
	/**
     * @Fields logger : Logger (Constants.CS_LOG_NAME_ILOG) 类私有日志打印器
     */
	private static Logger logger = LoggerFactory.getLogger(Constants.CS_LOG_NAME_ILOG);
	
	/**
	 * 
	 * @description 既往保全查询
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.impl.peripheral.ucc.r06801001915.ICsHisQueryByCustomerUcc#queryCsHisQueryByCustomer(com.nci.tunan.cs.interfaces.peripheral.exports.r06801001915.vo.InputData)
	 * @param inputData 入参
	 * @return
	 */
	@Override
	public OutputData queryCsHisQueryByCustomer(InputData inputData) {
		logger.info(inputData.getAppntNo()+"~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
		SysHeader sysHeader = CommonHeaderDeal.getSYSHEADERTHREAD();
		OutputData outputData = new OutputData();
		sysHeader.setBizResCd("0000");// @invalid 业务响应码赋值成功
		sysHeader.setBizResText("调用接口成功");// @invalid 成功描述	
		try{
			//#126623新个险临分客户相关查询接口参数及逻辑调整-CUS-5/5
			//modify by cuiqi_wb
			//2022-12-15
			//============================================start=======================================================
			if(inputData.getAppntNo() == null || "".equals(inputData.getAppntNo())
					|| inputData.getCusFlag() == null || "".equals(inputData.getCusFlag()) 
					 || inputData.getIDType() == null || "".equals(inputData.getIDType())){
			    sysHeader.setBizResCd("0001");//@invalid 业务响应码赋值失败
			    sysHeader.setBizResText("证件号码、类型、投被保人标志均不能为空，请检查！");// @invalid 失败描述
			    return outputData;
			}			
			if(!"0".equals(inputData.getCusFlag()) && !"1".equals(inputData.getCusFlag())){
			    sysHeader.setBizResCd("0001");// @invalid 业务响应码赋值失败
			    sysHeader.setBizResText("投被保人标志错误，请检查！");// @invalid 失败描述
			    return outputData;
			}
			if(inputData.getIDType() != null && !"0".equals(inputData.getIDType())){
				if(inputData.getName() == null || "".equals(inputData.getName())
						|| inputData.getSex() == null || "".equals(inputData.getSex()) 
						 || inputData.getBirthday() == null || "".equals(inputData.getBirthday())){
					sysHeader.setBizResCd("0001");// @invalid 业务响应码赋值失败
				    sysHeader.setBizResText("当证件类型不为身份证时，客户姓名、性别、生日均不可为空，请检查！");// @invalid 失败描述
				    return outputData;
				}
			}
			//================================================end=========================================================
			//调用既往保全查询
			return csHisQueryByCustomerService.queryCsHisQueryByCustomer(inputData);

		}
		catch(Exception e){
			e.printStackTrace();
		    sysHeader.setBizResCd("0001");// @invalid 业务响应码赋值失败
		    sysHeader.setBizResText("调用接口失败");// @invalid 失败描述	
		    return outputData;
		}		
		
	}

}
