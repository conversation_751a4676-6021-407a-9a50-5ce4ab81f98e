package com.nci.tunan.cs.impl.csItem.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.nci.tunan.cs.model.bo.CsPolicyAccountBO;
import com.nci.tunan.cs.model.bo.CsPolicyAccountStreamBO;
import com.nci.tunan.cs.model.bo.CsPolicyAccountTransListBO;
import com.nci.tunan.cs.model.bo.CsPolicyChangeBO;
import com.nci.tunan.cs.model.vo.CsEndorseTRVO;
/**
 * 
 * @description   自垫清偿 Service接口
 * <AUTHOR> 
 * @date 2015-05-15
 * @.belongToModule 保全子系统
  */
public interface ICsEndorseTRService extends ICSItemBaseService {
	/**
	 * 
	 * @description 查询保单账户基本信息表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 
	 * @return
	 */
	public List<CsPolicyAccountBO>  findCsPolicyAccountInfo(CsPolicyAccountBO csPolicyAccountBO); 
	
	/**
	 * @description 更新保单账户基本信息表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param csPolicyAccountBO  保单账户基本信息表BO
	 * @return 
	*/
	public CsPolicyAccountBO updateCsPolicyAccountInfo(CsPolicyAccountBO csPolicyAccountBO); 
	
	
	/**
	
	 * @description 更新保单账户分支表
		 * @version
		 * @title
		 * @<NAME_EMAIL>
		 * @param csPolicyAccountStreamListBO 保单账户基本信息分支表，记录贷款和自垫信息BO
		 * @return 
		*/
	public CsPolicyAccountStreamBO updateCspolicyAccountStreamInfoList(
				CsPolicyAccountStreamBO csPolicyAccountStreamBO); 
	/**
	 * @description 保存保单账户分支表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param csPolicyAccountStreamBO 保单账户基本信息分支表，记录贷款和自垫信息BO
	 * @return 
	*/
	public CsPolicyAccountStreamBO saveCsPolicyAccountStreamInfo(CsPolicyAccountStreamBO csPolicyAccountStreamListBO);
	/**
	 * @description 批量删除保单账户分支表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param csPolicyAccountStreamListBO  保单账户基本信息分支表，记录贷款和自垫信息BO
	 * @return 
	*/
	public boolean deleteCspolicyAccountStreamInfoList(CsPolicyAccountStreamBO  csPolicyAccountStreamListBO);
	/**
	 * @description 查询保单分支表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param csPolicyAccountStreamListBO 保单账户基本信息分支表，记录贷款和自垫信息BO
	 * @return 
	*/
	public List<CsPolicyAccountStreamBO> findCsPolicyAccountStreamBO (CsPolicyAccountStreamBO csPolicyAccountStreamListBO);
	
	
	/**
     * @description 批量删除保单账户流水表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param csPolicyAccountTransListBO 保单投资连结交易ListBO
	 * @return 
	*/
	public boolean deleteCsPolicyAccountTransListInfo(CsPolicyAccountTransListBO csPolicyAccountTransListBO);

	/**
	 * @description 查询保单账户流水表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param csPolicyAccountTransListBO 保单投资连结交易ListBO
	 * @return 
	*/
	public CsPolicyAccountTransListBO findCsPolicyAccountTransListInfo(CsPolicyAccountTransListBO csPolicyAccountTransListBO);
	/**
	 * @description 保存保单账户流水表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param csPolicyAccountTransListBO 保单投资连结交易ListBO
	 * @return 
	*/
	public CsPolicyAccountTransListBO saveCsPolicyAccountTransListInfo(CsPolicyAccountTransListBO csPolicyAccountTransListBO);

	/**
	 * 
	 * @description 保存保单账户基本信息表
	 * @version
	 * @title
	 * @<NAME_EMAIL> 
	 * @param csPolicyAccountBO 保单账户基本信息表BO
	 * @return
	 */
	public boolean saveCsPolicyAccount(CsPolicyAccountBO csPolicyAccountBO);

	/**
	 * 
	 * @description 查询保单变更管理表
	 * @version
	 * @title
	 * @<NAME_EMAIL> 
	 * @param csPolicyChangeBO 保单变更管理BO
	 * @return
	 */
	public List<CsPolicyChangeBO> findCsPolicyChanges(CsPolicyChangeBO csPolicyChangeBO);
	/**
	 * 
	 * @description 查询保单账户基本信息表
	 * @version
	 * @title
	 * @<NAME_EMAIL> 
	 * @param csPolicyAccountStreamQueryBO 保单账户基本信息分支表，记录贷款和自垫信息BO
	 * @return
	 */
	public List<CsPolicyAccountStreamBO> findAllCsPolicyAccount1(CsPolicyAccountStreamBO csPolicyAccountStreamQueryBO);
	/**
	 * 
	 * @description 保存清偿信息
	 * @version
	 * @title
	 * @<NAME_EMAIL> 
	 * @param csPolicyAccountStreamBO  保单账户基本信息分支表，记录贷款和自垫信息BO
	 * @return
	 */
	public boolean saveCleanOffInfo(CsPolicyAccountStreamBO csPolicyAccountStreamBO);

	/**
     * @description 查询自垫信息
     * @version
     * @title
     * <AUTHOR> 加载自垫清偿保全页面
     * @param acceptId 受理id
     * @param changeId 保全变更id
     * @param customerId 客户id
     * @return
     */
	List<CsEndorseTRVO> queryTRInfo(BigDecimal acceptId, BigDecimal changeId,
			BigDecimal customerId, String queryFlag);
	/**
     * @description 保存保全保单账户、分支表信息 新增流水，保存
     * @version
     * @title
     * @<NAME_EMAIL>
     * @param csPremArapForQueryBO  保费自垫清偿
     * @return
     */
	void savePolicyAccountInfo(List<CsEndorseTRVO> endorseTRVOs);
	 /** 
     * @description  查询保存后详细信息
     * <AUTHOR>
     * @param endorseTRVOs 保费自垫清偿VO对象
     * @return
     * @throws 
     */
	List<CsEndorseTRVO> findChangeTRInfo(List<CsEndorseTRVO> endorseTRVOs,
			boolean tQueryFlag);
	/** 
     * @description  查询自垫信息
     * @param acceptId 受理id
     * @param changeId 保全变更id
     * @param customerId 客户id
     * @param queryFlag "1"则不超单
     * @param repayDueDate 如果为空则使用受理时间
     * @return 
     * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseTRService#queryTRInfo(java.math.BigDecimal, java.math.BigDecimal, java.math.BigDecimal, java.lang.String, java.util.Date) 
     */
	List<CsEndorseTRVO> queryTRInfo(BigDecimal acceptId, BigDecimal changeId,
			BigDecimal customerId, String queryFlag, Date repayDueDate);

}
