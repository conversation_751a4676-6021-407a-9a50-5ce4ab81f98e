package com.nci.tunan.cs.impl.csEffect.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.tunan.clm.interfaces.model.po.ConstantsInfoPO;
import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.constants.ConstantsBusiProdCode;
import com.nci.tunan.cs.model.bo.CsAcceptChangeBO;
import com.nci.tunan.cs.model.bo.CsEffectBO;
import com.nci.tunan.cs.model.bo.CsPolicyChangeBO;
import com.nci.tunan.cs.model.po.BusinessProductPO;
import com.nci.tunan.cs.model.po.CsContractMasterPO;
import com.nci.tunan.cs.model.po.CsContractProductPO;
import com.nci.tunan.cs.model.po.CsContractRelationPO;
import com.nci.tunan.cs.model.po.CsPayPlanPO;
import com.nci.tunan.cs.model.po.CsPolicyChangePO;
import com.nci.tunan.pa.dao.IPayPlanDao;
import com.nci.tunan.pa.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.pa.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.pa.interfaces.model.po.ContractProductPO;
import com.nci.tunan.pa.interfaces.model.po.ContractRelationPO;
import com.nci.tunan.pa.interfaces.model.po.PayPlanPO;
import com.nci.udmp.framework.tag.i18n.CodeTable;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.StringUtilsEx;


/**
 * 
 * @description  保单关联 生效service实现类
 * @<NAME_EMAIL> 
 * @version V1.0.0
 * @.belongToModule  保全子系统
 * @date 2017年12月18日 下午2:51:08
 */
public class CsEffectRSServicempl extends CsEffectServiceImpl{
    /**
     * 险种生存给付计划表Dao
     */
    @Autowired
    @Qualifier("PA_payPlanDao")
    protected IPayPlanDao payPlanDao;
	/**
	 * 
	 * @description 子保全项生效方法
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csEffect.service.impl.CsEffectServiceImpl#csEffectService(com.nci.tunan.cs.model.bo.CsAcceptChangeBO)
	 * @param csAcceptChangeBO 生效方法
	 * @return
	 */
	@Override
	public CsEffectBO csEffectService(CsAcceptChangeBO csAcceptChangeBO) {
		CsEffectBO csEffectBO = new CsEffectBO();
		boolean flag = true;
		// @invalid TODO Auto-generated method stub
		// 第一步：查询保单变更管理列表，查询该保全项所涉及的保单
		CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
        csPolicyChangePO.setAcceptId(csAcceptChangeBO.getAcceptId());
        List<CsPolicyChangePO> csPolicyChangePOs = csPolicyChangeDao.findAllCsPolicyChange(csPolicyChangePO);
		if (!CollectionUtils.isEmpty(csPolicyChangePOs)) {
		    for (CsPolicyChangePO csPolicyChgPO : csPolicyChangePOs) {
		        BigDecimal policyChgId = csPolicyChgPO.getPolicyChgId();
	            BigDecimal policyId = csPolicyChgPO.getPolicyId();
	            CsContractMasterPO csContractMasterPO = new CsContractMasterPO();
	            csContractMasterPO.setPolicyChgId(policyChgId);
	            csContractMasterPO.setPolicyId(policyId);
	            csContractMasterPO.setOldNew(Constants.NEW);
	            csContractMasterPO = csContractMasterDao.findCsContractMaster(csContractMasterPO);
	            if (Constants.OPERATIONTYPE_UPDATE.equals(csContractMasterPO.getOperationType())) {
	                ContractMasterPO conMasterPO = BeanUtils.copyProperties(ContractMasterPO.class, csContractMasterPO);
	                ContractMasterPO organCodePO = new ContractMasterPO();
	    	        organCodePO.setPolicyId(conMasterPO.getPolicyId());
	    	        organCodePO = contractMasterDao.findContractMaster(organCodePO);
	    	        conMasterPO.setOrganCode(organCodePO.getOrganCode());
	                contractMasterDao.updateContractMaster(conMasterPO);
	            }
	            //113326 start 如果单主险保单关联了多主险保单也走多主险流程
	            CsContractRelationPO csContractRelaNewPO = new CsContractRelationPO();
	            csContractRelaNewPO.setChangeId(csAcceptChangeBO.getChangeId());
	            csContractRelaNewPO.setPolicyChgId(policyChgId);
	            csContractRelaNewPO.setOldNew(Constants.NEW);
	            List <CsContractRelationPO> csContractRelationNewPOList = csContractRelationDao.findAllCsContractRelation(csContractRelaNewPO);
	            //113326 end
	            if(csContractMasterPO.getMultiMainriskFlag()!=null && csContractMasterPO.getMultiMainriskFlag().compareTo(Constants.MULTI_MAINRISK_FLAG1)==0
	            		||CollectionUtils.isNotEmpty(csContractRelationNewPOList)){
	            	CsContractRelationPO csContractRelationPO = new CsContractRelationPO();
		            csContractRelationPO.setChangeId(csAcceptChangeBO.getCancelId());
		            csContractRelationPO.setMasterPolicyId(policyId);
		            csContractRelationPO.setPolicyChgId(policyChgId);
		            csContractRelationPO.setOldNew(Constants.NEW);
//		            csContractRelationPO.setOperationType(Constants.OPERATIONTYPE_ADD);
		            List<CsContractRelationPO> csContractRelationPOs = csContractRelationDao.findExistCsContractRelation(csContractRelationPO);
		            if(CollectionUtils.isNotEmpty(csContractRelationPOs)){
		            	//先去除保单表中原有的关联关系，再建立新的关联关系
		            	ContractRelationPO contractRelationPO = new ContractRelationPO();
		            	contractRelationPO.setMasterPolicyId(policyId);
		            	List<ContractRelationPO> conRelations = paContractRelationDao.findAllContractRelation(contractRelationPO);
		            	if(CollectionUtils.isNotEmpty(conRelations)){
		            		for(ContractRelationPO conRelationPO : conRelations){
		            			paContractRelationDao.deleteContractRelationByListId(conRelationPO);
		            		}
		            	}
		            	for(CsContractRelationPO csRelationPO : csContractRelationPOs){
		            		ContractRelationPO conRelationPO = new ContractRelationPO();
		            		conRelationPO.setMasterPolicyCode(csRelationPO.getMasterPolicyCode());
		            		conRelationPO.setMasterPolicyId(csRelationPO.getMasterPolicyId());
		            		conRelationPO.setMasterBusiItemId(csRelationPO.getMasterBusiItemId());
		            		conRelationPO.setMasterBusiProdCode(csRelationPO.getMasterBusiProdCode());
		            		conRelationPO.setSubPolicyCode(csRelationPO.getSubPolicyCode());
		            		conRelationPO.setSubPolicyId(csRelationPO.getSubPolicyId());
		            		conRelationPO.setSubBusiItemId(csRelationPO.getSubBusiItemId());
		            		conRelationPO.setSubBusiProdCode(csRelationPO.getSubBusiProdCode());
							conRelationPO.setRelationType(csRelationPO.getRelationType());
							conRelationPO.setListId(csRelationPO.getListId());
		            		paContractRelationDao.addContractRelationWhenHavePrimaryKeyValue(conRelationPO);
		            	}
		            }else{
		            	ContractRelationPO contractRelationPO = new ContractRelationPO();
		            	contractRelationPO.setMasterPolicyId(policyId);
		            	List<ContractRelationPO> conRelations = paContractRelationDao.findAllContractRelation(contractRelationPO);
		            	if(CollectionUtils.isNotEmpty(conRelations)){
		            		for(ContractRelationPO conRelationPO : conRelations){
		            			paContractRelationDao.deleteContractRelationByListId(conRelationPO);
		            		}
		            	}
		            }
	            }
	            CsPayPlanPO queryCsPayPlanBO = new CsPayPlanPO();
				queryCsPayPlanBO.setPolicyChgId(csPolicyChgPO.getPolicyChgId());
				queryCsPayPlanBO.setPolicyId(csPolicyChgPO.getPolicyId());
				queryCsPayPlanBO.setOperationType(Constants.OPERATIONTYPE_UPDATE);
				queryCsPayPlanBO.setPayPlanType(Constants.PAY_PLAN_TYPE_1);
				queryCsPayPlanBO.setOldNew(Constants.NEW);
				List<CsPayPlanPO> findAllCsPayPlan = csPayPlanDao.findAllCsPayPlan(queryCsPayPlanBO);
				for(CsPayPlanPO csPayPlanBO:findAllCsPayPlan){
					 /**@invalid 更改t_pay_plan***/
					 PayPlanPO planPO = BeanUtils.copyProperties(PayPlanPO.class, csPayPlanBO);
					 payPlanDao.updatePayPlan(planPO);
					 /**@invalid 更改责任组*/
		    		 CsContractProductPO csContractProductPO = new CsContractProductPO();
		    		 csContractProductPO.setPolicyChgId(csPayPlanBO.getPolicyChgId());
		    		 csContractProductPO.setOldNew(Constants.NEW);
		    		 csContractProductPO.setItemId(csPayPlanBO.getItemId());
		    		 csContractProductPO = csContractProductDao.findCsContractProduct(csContractProductPO);
		    		 ContractProductPO contractProductPO = BeanUtils.copyProperties(ContractProductPO.class, csContractProductPO);
		    		 contractProductDao.updateContractProduct(contractProductPO);
				}
	            super.addCsTrustSubmitTask(csAcceptChangeBO, BeanUtils.copyProperties(CsPolicyChangeBO.class,csPolicyChgPO) );
            }
		}else{
		    flag = false;
		}
		csEffectBO.setSucceed(flag);
		return csEffectBO;
	
	}
	
	/**
	 *
	 * @description 批单打印
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csEffect.service.impl.CsEffectServiceImpl#printCsEndorsement(com.nci.tunan.cs.model.bo.CsAcceptChangeBO)
	 * @param csAcceptChangeBO 受理信息
	 * @return
	 */
	@SuppressWarnings("deprecation")
    public CsEffectBO printCsEndorsement(CsAcceptChangeBO csAcceptChangeBO) {
		CsEffectBO retBo = new CsEffectBO();
        boolean flag = true;
        // @invalid TODO Auto-generated method stub
       // 第一步：查询保单变更管理列表，查询该保全项所涉及的保单
        List<CsPolicyChangeBO> csList = csMainStreamService.findCsPolicyChanges(csAcceptChangeBO);
        Document csPrintXmlDocument = csPrintEndorseService.initPrintElement(csAcceptChangeBO.getAcceptCode(),
                null);
        Element eltRoot = csPrintXmlDocument.getRootElement();
        Element policyDataElement = eltRoot.element("DATASET").element("DATA");
        /**取消关联 false  建立关联 true*/
        boolean isYesOrNO=false;
        
        if (!CollectionUtils.isEmpty(csList)) {
            // 循环保单变更主表的信息，
    		for (CsPolicyChangeBO csPolicyChangeBO : csList) {
    			String tRelationPolicyCode ="";//被关联的保单号
    	        String oldPolicyCode = "";
    	        String newRelationPolicyCode = "";
    	        String oldRelationPolicyCode = "";
    	        BigDecimal newPolicyRelationType = null;
    	        BigDecimal oldPolicyRelationType = null;
    	        String oldApplyCode = "";
                /************************** 打印批单 start ********************************/
                // 调用打印公共类的初始化报文方法（生成公共的报文数据）
               

                // 拼接自己的业务数据 开始
				Element cUSItemNCodeElement = policyDataElement.addElement("CUSItemCode");
                cUSItemNCodeElement.setText("RS"); // ServiceCode
                Element cUSItemNameElement = policyDataElement.addElement("CUSItemName");
                cUSItemNameElement.setText("保单关联"); // 保全项名称
                Element cUSEdorInfoElement = policyDataElement.addElement("CUSEdorInfo");
                Element edorList53Element = cUSEdorInfoElement.addElement("EdorList53");
                Element multiMainNewFlag = edorList53Element.addElement("MultiMainNewFlag");//多主险标识
                Element establishOrCancel = edorList53Element.addElement("Establish");//关联或则取消
                Element policyNo = edorList53Element.addElement("PolicyNo");//年金保单号
                Element number = edorList53Element.addElement("Number");//万能险保单号
                CsContractMasterPO csContractMasterPONew = new CsContractMasterPO();
                CsContractMasterPO csContractMasterPOOld= new CsContractMasterPO();
                CsContractRelationPO csContractRelationOldPO = new CsContractRelationPO();
                CsContractRelationPO csContractRelationNewPO = new CsContractRelationPO();
                List<CsContractRelationPO> csContractRelationOldPOs = new ArrayList<CsContractRelationPO>();
        		List<CsContractRelationPO> csContractRelationNewPOs = new ArrayList<CsContractRelationPO>(); 		
                //查询是否多主险 
                ContractMasterPO contractMasterPO = new ContractMasterPO();
		  		contractMasterPO.setPolicyCode(csPolicyChangeBO.getPolicyCode());
		  		contractMasterPO = contractMasterDao.findContractMaster(contractMasterPO);
		  		if(contractMasterPO.getMultiMainriskFlag() == null){
		  			contractMasterPO.setMultiMainriskFlag(BigDecimal.ZERO);
		  		}
		  		csContractMasterPONew.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
                csContractMasterPONew.setPolicyId(csPolicyChangeBO.getPolicyId());
                csContractMasterPONew.setOldNew(Constants.NEW);
                csContractMasterPONew = csContractMasterDao.findCsContractMaster(csContractMasterPONew);
                csContractMasterPOOld.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
                csContractMasterPOOld.setPolicyId(csPolicyChangeBO.getPolicyId());
                csContractMasterPOOld.setOldNew(Constants.OLD);
                csContractMasterPOOld = csContractMasterDao.findCsContractMaster(csContractMasterPOOld);
                //113326 start 如果单主险保单关联了多主险保单也走多主险流程
                CsContractRelationPO csContractRelaNewPO = new CsContractRelationPO();
                csContractRelaNewPO.setChangeId(csPolicyChangeBO.getChangeId());
                csContractRelaNewPO.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
                csContractRelaNewPO.setOldNew(Constants.NEW);
                List <CsContractRelationPO> csContractRelationNewPOList = csContractRelationDao.findAllCsContractRelation(csContractRelaNewPO);
                //113326 end
		  		//#85366若是多主险
		  		if(contractMasterPO.getMultiMainriskFlag()!=null&&contractMasterPO.getMultiMainriskFlag().compareTo(Constants.MULTI_MAINRISK_FLAG1)==0
		  				||CollectionUtils.isNotEmpty(csContractRelationNewPOList)) {
		  			multiMainNewFlag.setText("1");
		  			//113326 end
	                csContractRelationNewPO.setChangeId(csPolicyChangeBO.getChangeId());
	                csContractRelationNewPO.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
	                csContractRelationNewPO.setOldNew(Constants.NEW);
	                csContractRelationNewPOs = csContractRelationDao.findExistCsContractRelation(csContractRelationNewPO);
	                csContractRelationOldPO.setChangeId(csPolicyChangeBO.getChangeId());
	                csContractRelationOldPO.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
	                csContractRelationOldPO.setOldNew(Constants.OLD);
	                csContractRelationOldPOs = csContractRelationDao.findAllCsContractRelation(csContractRelationOldPO);
	                
	                if(CollectionUtils.isEmpty(csContractRelationOldPOs)){
		                policyNo.setText(csContractMasterPOOld.getPolicyCode());    
		                oldPolicyCode = csContractMasterPOOld.getPolicyCode();
		                oldRelationPolicyCode = csContractMasterPOOld.getRelationPolicyCode();
		    	        oldPolicyRelationType =csContractMasterPOOld.getPolicyRelationType();
		    	        oldApplyCode = csContractMasterPOOld.getApplyCode();
	                }else{
	                	policyNo.setText(csContractRelationOldPOs.get(0).getMasterPolicyCode());    
	                	oldPolicyCode = csContractRelationOldPOs.get(0).getMasterPolicyCode();
		    	        oldRelationPolicyCode = csContractRelationOldPOs.get(0).getSubPolicyCode();
		    	        oldPolicyRelationType =new BigDecimal(csContractRelationOldPOs.get(0).getRelationType());
		    	        ContractMasterPO conMasterPO = new ContractMasterPO();
		    	        conMasterPO.setPolicyCode(oldPolicyCode);
		    	        conMasterPO = contractMasterDao.findContractMaster(conMasterPO);
		    	        oldApplyCode = conMasterPO.getApplyCode();
	                }
	                if(CollectionUtils.isNotEmpty(csContractRelationNewPOs)){
	                	newRelationPolicyCode = csContractRelationNewPOs.get(0).getSubPolicyCode();
		    	        newPolicyRelationType = new BigDecimal(csContractRelationNewPOs.get(0).getRelationType());
		                policyNo.setText(csContractRelationNewPOs.get(0).getMasterPolicyCode());
	                }else{
	                	newRelationPolicyCode = csContractRelationOldPOs.get(0).getSubPolicyCode();
		    	        newPolicyRelationType = new BigDecimal(csContractRelationOldPOs.get(0).getRelationType());
		                policyNo.setText(csContractRelationOldPOs.get(0).getMasterPolicyCode());
	                }
		  			//113326 start
		  			if(contractMasterPO.getMultiMainriskFlag()==null||contractMasterPO.getMultiMainriskFlag().compareTo(Constants.MULTI_MAINRISK_FLAG0)==0) {
		  				multiMainNewFlag.setText("0");
		            	if(newPolicyRelationType != null){
		            		if("2".equals(newPolicyRelationType.toString())){
			            		newPolicyRelationType = new BigDecimal("0");
			            	}else if("3".equals(newPolicyRelationType.toString())){
			            		newPolicyRelationType = new BigDecimal("1");
			            	}else if("4".equals(newPolicyRelationType.toString())){
			            		newPolicyRelationType = new BigDecimal("2");
			            	}
		            	}
		            	if(oldPolicyRelationType != null){
			            	if("2".equals(oldPolicyRelationType.toString())){
			            		oldPolicyRelationType = new BigDecimal("0");
			            	}else if("3".equals(oldPolicyRelationType.toString())){
			            		oldPolicyRelationType = new BigDecimal("1");
			            	}else if("4".equals(oldPolicyRelationType.toString())){
			            		oldPolicyRelationType = new BigDecimal("2");
			            	}
		            	}
		  			}
	                if(CollectionUtils.isNotEmpty(csContractRelationOldPOs)){
	                	if(CollectionUtils.isEmpty(csContractRelationNewPOs)){
	                		tRelationPolicyCode=csContractRelationOldPOs.get(0).getSubPolicyCode();
	                        establishOrCancel.setText("0"); //取消关联
	                        number.setText(csContractRelationOldPOs.get(0).getSubPolicyCode());
	                        isYesOrNO=false;
		                }else{
		                	tRelationPolicyCode=csContractRelationNewPOs.get(0).getSubPolicyCode();
	                        establishOrCancel.setText("1"); //建立关联
	                        number.setText(csContractRelationNewPOs.get(0).getSubPolicyCode());
	                        isYesOrNO=true;
		                }
	                }else{
	                	if(CollectionUtils.isNotEmpty(csContractRelationNewPOs)){
	                		tRelationPolicyCode=csContractRelationNewPOs.get(0).getSubPolicyCode();
	                        establishOrCancel.setText("1"); //建立关联
	                        number.setText(csContractRelationNewPOs.get(0).getSubPolicyCode());
	                        isYesOrNO=true;
	                	}
	                }
	                //113326 start
	                CsContractRelationPO csContractRelaDocument = new CsContractRelationPO();
	                csContractRelaDocument.setChangeId(csPolicyChangeBO.getChangeId());
	                csContractRelaDocument.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
	                csContractRelaDocument.setOldNew(Constants.NEW);
	                List<CsContractRelationPO> newCsContractRelationForDocument = csContractRelationDao.findAllCsContractRelationForDocument(csContractRelaDocument);
	                Element multiInfo = edorList53Element.addElement("MultiInfo");
	                List<Integer> contNum = new ArrayList<Integer>();
	                for(CsContractRelationPO po : newCsContractRelationForDocument){
	                	csContractRelaDocument = new CsContractRelationPO();
	                	csContractRelaDocument.setChangeId(csPolicyChangeBO.getChangeId());
	                	csContractRelaDocument.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
	                	csContractRelaDocument.setMasterBusiItemId(new BigDecimal(po.getData().get("master_busi_item_id")==null?"":po.getData().get("master_busi_item_id").toString()));
	                	csContractRelaDocument.setOldNew(Constants.OLD);
	                	List<CsContractRelationPO> oldCsContractRelationForDocument = csContractRelationDao.findAllCsContractRelationForDocument(csContractRelaDocument);
	                	if("0".equals(po.getData().get("operation_type"))){
	                		continue;
	                	}else if("3".equals(po.getData().get("operation_type"))){
	    	                if(CollectionUtils.isNotEmpty(oldCsContractRelationForDocument)&&oldCsContractRelationForDocument.size()>0){//如果操作类型是删除，取原关系
	    	                	Element multiInfoList = multiInfo.addElement("MultiInfoList");
		                		Element relPolicyNo = multiInfoList.addElement("RelPolicyNo");//申请变更关联关系险种的保单号
		                		relPolicyNo.setText(oldCsContractRelationForDocument.get(0).getData().get("relpolicyno")==null?"":oldCsContractRelationForDocument.get(0).getData().get("relpolicyno").toString());
		                		Element relMRiskFlag = multiInfoList.addElement("RelMRiskFlag");//申请变更关联关系险种是否为主险
		                		relMRiskFlag.setText(oldCsContractRelationForDocument.get(0).getData().get("relmriskflag")==null?"":oldCsContractRelationForDocument.get(0).getData().get("relmriskflag").toString());
		                		Element relRiskName = multiInfoList.addElement("RelRiskName");//申请变更关联关系险种及其非终止状态的附加险的简称
		                		String riskNames = oldCsContractRelationForDocument.get(0).getData().get("relriskname")==null?"":oldCsContractRelationForDocument.get(0).getData().get("relriskname").toString();
		                		if (riskNames.contains("，")) {
									String[] riskName = riskNames.split("，");
									for (int i = 0; i < riskName.length; i++) {
										//附加险需要再循环传值
										if(i==0){
											relRiskName.setText(riskName[i]);
										}
									} 
								}else{
									relRiskName.setText(riskNames);
								}
		                		Element relPolicyNo2 = multiInfoList.addElement("RelPolicyNo2");//万能险保单号
		                		relPolicyNo2.setText(oldCsContractRelationForDocument.get(0).getData().get("relpolicyno2")==null?"":oldCsContractRelationForDocument.get(0).getData().get("relpolicyno2").toString());
		                		Element relRiskName2 = multiInfoList.addElement("RelRiskName2");//万能险险种简称
		                		relRiskName2.setText(oldCsContractRelationForDocument.get(0).getData().get("relriskname2")==null?"":oldCsContractRelationForDocument.get(0).getData().get("relriskname2").toString());
		                		Element relType = multiInfoList.addElement("RelType");//关联类型
		                		relType.setText(oldCsContractRelationForDocument.get(0).getData().get("reltype")==null?"":oldCsContractRelationForDocument.get(0).getData().get("reltype").toString());
		                		Element relChoice = multiInfoList.addElement("RelChoice");//关联选项
		                		relChoice.setText("取消关联");//取消关联
		                		if("0".equals(oldCsContractRelationForDocument.get(0).getData().get("reltypecode")) && !contNum.contains(3)){
		                			contNum.add(3);
		                		}else if("1".equals(oldCsContractRelationForDocument.get(0).getData().get("reltypecode")) && !contNum.contains(4)){
		                			contNum.add(4);
		                		}else if("2".equals(oldCsContractRelationForDocument.get(0).getData().get("reltypecode"))){
		                			if(!contNum.contains(3)){
		                				contNum.add(3);
		                			}
		                			if(!contNum.contains(4)){
		                				contNum.add(4);
		                			}
		                		}
		                		if (riskNames.contains("，")) {
									String[] riskName = riskNames.split("，");
									for (int i = 0; i < riskName.length; i++) {
										//附加险需要再循环传值
										if(i>0){
											Element multiInfoList1 = multiInfo.addElement("MultiInfoList");
					                		Element relPolicyNo1 = multiInfoList1.addElement("RelPolicyNo");//申请变更关联关系险种的保单号
					                		relPolicyNo1.setText("");
					                		Element relMRiskFlag1 = multiInfoList1.addElement("RelMRiskFlag");//申请变更关联关系险种是否为主险
					                		relMRiskFlag1.setText("0");
					                		Element relRiskName1 = multiInfoList1.addElement("RelRiskName");//申请变更关联关系险种及其非终止状态的附加险的简称
					                		relRiskName1.setText(riskName[i]);
					                		Element relPolicyNo21 = multiInfoList1.addElement("RelPolicyNo2");//万能险保单号
					                		relPolicyNo21.setText("");
					                		Element relRiskName21 = multiInfoList1.addElement("RelRiskName2");//万能险险种简称
					                		relRiskName21.setText("");
					                		Element relType1 = multiInfoList1.addElement("RelType");//关联类型
					                		relType1.setText("");
					                		Element relChoice1 = multiInfoList1.addElement("RelChoice");//关联选项
					                		relChoice1.setText("");//取消关联
										}
									} 
								}
	    	                }
	                	}else{
	                		if(CollectionUtils.isNotEmpty(oldCsContractRelationForDocument)&&oldCsContractRelationForDocument.size()>0){
	                			//关联关系改变，先插入一条旧的，再插新的
	                			Element multiInfoList = multiInfo.addElement("MultiInfoList");
		                		Element relPolicyNo = multiInfoList.addElement("RelPolicyNo");//申请变更关联关系险种的保单号
		                		relPolicyNo.setText(oldCsContractRelationForDocument.get(0).getData().get("relpolicyno")==null?"":oldCsContractRelationForDocument.get(0).getData().get("relpolicyno").toString());
		                		Element relMRiskFlag = multiInfoList.addElement("RelMRiskFlag");//申请变更关联关系险种是否为主险
		                		relMRiskFlag.setText(oldCsContractRelationForDocument.get(0).getData().get("relmriskflag")==null?"":oldCsContractRelationForDocument.get(0).getData().get("relmriskflag").toString());
		                		Element relRiskName = multiInfoList.addElement("RelRiskName");//申请变更关联关系险种及其非终止状态的附加险的简称
		                		String riskNames = oldCsContractRelationForDocument.get(0).getData().get("relriskname")==null?"":oldCsContractRelationForDocument.get(0).getData().get("relriskname").toString();
		                		if (riskNames.contains("，")) {
									String[] riskName = riskNames.split("，");
									for (int i = 0; i < riskName.length; i++) {
										//附加险需要再循环传值
										if(i==0){
											relRiskName.setText(riskName[i]);
										}
									} 
								}else{
									relRiskName.setText(riskNames);
								}
		                		Element relPolicyNo2 = multiInfoList.addElement("RelPolicyNo2");//万能险保单号
		                		relPolicyNo2.setText(oldCsContractRelationForDocument.get(0).getData().get("relpolicyno2")==null?"":oldCsContractRelationForDocument.get(0).getData().get("relpolicyno2").toString());
		                		Element relRiskName2 = multiInfoList.addElement("RelRiskName2");//万能险险种简称
		                		relRiskName2.setText(oldCsContractRelationForDocument.get(0).getData().get("relriskname2")==null?"":oldCsContractRelationForDocument.get(0).getData().get("relriskname2").toString());
		                		Element relType = multiInfoList.addElement("RelType");//关联类型
		                		relType.setText(oldCsContractRelationForDocument.get(0).getData().get("reltype")==null?"":oldCsContractRelationForDocument.get(0).getData().get("reltype").toString());
		                		Element relChoice = multiInfoList.addElement("RelChoice");//关联选项
		                		relChoice.setText("取消关联");//取消关联
		                		if("0".equals(oldCsContractRelationForDocument.get(0).getData().get("reltypecode")) && !contNum.contains(3)){
		                			contNum.add(3);
		                		}else if("1".equals(oldCsContractRelationForDocument.get(0).getData().get("reltypecode")) && !contNum.contains(4)){
		                			contNum.add(4);
		                		}else if("2".equals(oldCsContractRelationForDocument.get(0).getData().get("reltypecode"))){
		                			if(!contNum.contains(3)){
		                				contNum.add(3);
		                			}
		                			if(!contNum.contains(4)){
		                				contNum.add(4);
		                			}
		                		}
		                		if (riskNames.contains("，")) {
									String[] riskName = riskNames.split("，");
									for (int i = 0; i < riskName.length; i++) {
										//附加险需要再循环传值
										if(i>0){
											Element multiInfoList1 = multiInfo.addElement("MultiInfoList");
					                		Element relPolicyNo1 = multiInfoList1.addElement("RelPolicyNo");//申请变更关联关系险种的保单号
					                		relPolicyNo1.setText("");
					                		Element relMRiskFlag1 = multiInfoList1.addElement("RelMRiskFlag");//申请变更关联关系险种是否为主险
					                		relMRiskFlag1.setText("0");
					                		Element relRiskName1 = multiInfoList1.addElement("RelRiskName");//申请变更关联关系险种及其非终止状态的附加险的简称
					                		relRiskName1.setText(riskName[i]);
					                		Element relPolicyNo21 = multiInfoList1.addElement("RelPolicyNo2");//万能险保单号
					                		relPolicyNo21.setText("");
					                		Element relRiskName21 = multiInfoList1.addElement("RelRiskName2");//万能险险种简称
					                		relRiskName21.setText("");
					                		Element relType1 = multiInfoList1.addElement("RelType");//关联类型
					                		relType1.setText("");
					                		Element relChoice1 = multiInfoList1.addElement("RelChoice");//关联选项
					                		relChoice1.setText("");//取消关联
										}
									} 
								}
	                		}
	                		Element multiInfoList = multiInfo.addElement("MultiInfoList");
	                		Element relPolicyNo = multiInfoList.addElement("RelPolicyNo");//申请变更关联关系险种的保单号
	                		relPolicyNo.setText(po.getData().get("relpolicyno")==null?"":po.getData().get("relpolicyno").toString());
	                		Element relMRiskFlag = multiInfoList.addElement("RelMRiskFlag");//申请变更关联关系险种是否为主险
	                		relMRiskFlag.setText(po.getData().get("relmriskflag")==null?"":po.getData().get("relmriskflag").toString());
	                		Element relRiskName = multiInfoList.addElement("RelRiskName");//申请变更关联关系险种及其非终止状态的附加险的简称
	                		//relRiskName.setText(po.getData().get("relriskname")==null?"":po.getData().get("relriskname").toString());
	                		String riskNames = po.getData().get("relriskname")==null?"":po.getData().get("relriskname").toString();
	                		if (riskNames.contains("，")) {
								String[] riskName = riskNames.split("，");
								for (int i = 0; i < riskName.length; i++) {
									//附加险需要再循环传值
									if(i==0){
										relRiskName.setText(riskName[i]);
									}
								} 
							}else{
								relRiskName.setText(riskNames);
							}
	                		Element relPolicyNo2 = multiInfoList.addElement("RelPolicyNo2");//万能险保单号
	                		relPolicyNo2.setText(po.getData().get("relpolicyno2")==null?"":po.getData().get("relpolicyno2").toString());
	                		Element relRiskName2 = multiInfoList.addElement("RelRiskName2");//万能险险种简称
	                		relRiskName2.setText(po.getData().get("relriskname2")==null?"":po.getData().get("relriskname2").toString());
	                		Element relType = multiInfoList.addElement("RelType");//关联类型
	                		relType.setText(po.getData().get("reltype")==null?"":po.getData().get("reltype").toString());
	                		Element relChoice = multiInfoList.addElement("RelChoice");//关联选项
	                		relChoice.setText("建立关联");//建立关联
	                		if("0".equals(po.getData().get("reltypecode")) && !contNum.contains(1)){
	                			contNum.add(1);
	                		}else if("1".equals(po.getData().get("reltypecode")) && !contNum.contains(2)){
	                			contNum.add(2);
	                		}else if("2".equals(po.getData().get("reltypecode"))){
	                			if(!contNum.contains(1)){
	                				contNum.add(1);
	                			}
	                			if(!contNum.contains(2)){
	                				contNum.add(2);
	                			}
	                		}
	                		if (riskNames.contains("，")) {
								String[] riskName = riskNames.split("，");
								for (int i = 0; i < riskName.length; i++) {
									//附加险需要再循环传值
									if(i>0){
										Element multiInfoList1 = multiInfo.addElement("MultiInfoList");
				                		Element relPolicyNo1 = multiInfoList1.addElement("RelPolicyNo");//申请变更关联关系险种的保单号
				                		relPolicyNo1.setText("");
				                		Element relMRiskFlag1 = multiInfoList1.addElement("RelMRiskFlag");//申请变更关联关系险种是否为主险
				                		relMRiskFlag1.setText("0");
				                		Element relRiskName1 = multiInfoList1.addElement("RelRiskName");//申请变更关联关系险种及其非终止状态的附加险的简称
				                		relRiskName1.setText(riskName[i]);
				                		Element relPolicyNo21 = multiInfoList1.addElement("RelPolicyNo2");//万能险保单号
				                		relPolicyNo21.setText("");
				                		Element relRiskName21 = multiInfoList1.addElement("RelRiskName2");//万能险险种简称
				                		relRiskName21.setText("");
				                		Element relType1 = multiInfoList1.addElement("RelType");//关联类型
				                		relType1.setText("");
				                		Element relChoice1 = multiInfoList1.addElement("RelChoice");//关联选项
				                		relChoice1.setText("");//取消关联
									}
								} 
							}
	                	}
	                }
	                Collections.sort(contNum);
	                for(int i : contNum){
	                	if(i==1){
	                		Element multiInfoList2 = multiInfo.addElement("MultiInfoList2");//多主险提示语列表
		                	Element multiCont = multiInfoList2.addElement("MultiCont");//多主险提示语内容
		                	multiCont.setText("建立关联、生存类保险金转入万能险合同：在万能险合同有效的情况下，经投保人和被保险人同意，将年金险合同有效期内的生存类保险金及现金红利（如有）转为万能险合同的保险费，由投保人予以交纳。");
	                	}else if(i==2){
	                		Element multiInfoList2 = multiInfo.addElement("MultiInfoList2");//多主险提示语列表
		                	Element multiCont = multiInfoList2.addElement("MultiCont");//多主险提示语内容
		                	multiCont.setText("建立关联、万能险合同账户价值交纳续期或续保保险费：经投保人和被保险人同意，万能险合同部分领取保单账户价值用于交纳健康险/年金险及其附加险保险合同的续期保险费或续保保险费，当未及时交纳健康险/年金险及其附加险保险合同全部当期保险费（除另有约定外，全部当期保险费指贵公司认可的年金险/健康险及其附加保险合同当期应交纳保险费之和）时，将于约定期间内通过部分领取万能险合同保单账户价值予以交纳（部分领取时的费用收取情况以万能险合同条款约定为准）予以交纳，保单账户价值相应减少。部分领取后的保单账户价值余额低于本公司规定的最低金额时不能进行部分领取，保单贷款期间不能进行部分领取。");
	                	}else if(i==3){
	                		Element multiInfoList2 = multiInfo.addElement("MultiInfoList2");//多主险提示语列表
		                	Element multiCont = multiInfoList2.addElement("MultiCont");//多主险提示语内容
		                	multiCont.setText("取消关联、生存类保险金转入万能险合同：经投保人和被保险人同意，在万能险合同有效的情况下：将保单年金险合同有效期内的生存类保险金及现金红利（如有）不再转为保单万能险合同的保险费。");
	                	}else if(i==4){
	                		Element multiInfoList2 = multiInfo.addElement("MultiInfoList2");//多主险提示语列表
		                	Element multiCont = multiInfoList2.addElement("MultiCont");//多主险提示语内容
	                		multiCont.setText("取消关联、万能险合同账户价值交纳续期或续保保险费：经投保人和被保险人同意，万能险合同账户价值不再用于交纳健康险/年金险及其附加险保险合同的续期保险费或续保保险费，健康险/年金险及其附加险保单号保险合同全部当期保险费由投保人予以交纳。");
	                	}
	                }
	                //113326 end
		  		}else{
		  			//非多主险沿用原来的关联方式
		  			multiMainNewFlag.setText("0");
		  			
	                
	                oldPolicyCode = csContractMasterPOOld.getPolicyCode();
	    	        newRelationPolicyCode = csContractMasterPONew.getRelationPolicyCode() ;
	    	        oldRelationPolicyCode = csContractMasterPOOld.getRelationPolicyCode();
	    	        newPolicyRelationType = csContractMasterPONew.getPolicyRelationType();
	    	        oldPolicyRelationType = csContractMasterPOOld.getPolicyRelationType();
	    	        oldApplyCode = csContractMasterPONew.getApplyCode();
	                policyNo.setText(csContractMasterPOOld.getPolicyCode());
		  			if(StringUtils.isNotEmpty(csContractMasterPOOld.getRelationPolicyCode())){//修改之前存在关联关系
	                    if(StringUtils.isEmpty(csContractMasterPONew.getRelationPolicyCode())){
	                    	tRelationPolicyCode=csContractMasterPOOld.getRelationPolicyCode();
	                        establishOrCancel.setText("0"); //取消关联
	                        number.setText(csContractMasterPOOld.getRelationPolicyCode());
	                        isYesOrNO=false;
	                    }else{
	                    	tRelationPolicyCode=csContractMasterPONew.getRelationPolicyCode();
	                        establishOrCancel.setText("1"); //建立关联
	                        number.setText(csContractMasterPONew.getRelationPolicyCode());
	                        isYesOrNO=true;
	                    }
	                }else{
	                    if(StringUtils.isNotEmpty(csContractMasterPONew.getRelationPolicyCode())){
	                    	tRelationPolicyCode=csContractMasterPONew.getRelationPolicyCode();
	                        establishOrCancel.setText("1"); //建立关联
	                        number.setText(csContractMasterPONew.getRelationPolicyCode());
	                        isYesOrNO=true;
	                    }
	                }
		  		}
				String tProductAbbrName="";//万能险简称
				String tProductNameStd="";//万能险标准名称
				String tProductCode = "";//万能险代码
				String tProductCodeSTD = "";//@invalid 万能险代码简称
				ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
				BusinessProductPO businessProductPO = null;
				contractBusiProdPO.setPolicyCode(tRelationPolicyCode);
				List<ContractBusiProdPO> contractBusiProdPOWNList = contractBusiProdDao.findAllContractBusiProd(contractBusiProdPO );
				 for(ContractBusiProdPO tContractBusiProdPO:contractBusiProdPOWNList){
					 if(tContractBusiProdPO.getMasterBusiItemId()==null){
						businessProductPO = new BusinessProductPO();
						businessProductPO.setBusinessPrdId(tContractBusiProdPO.getBusiPrdId());
						businessProductPO=businessProductDao.findBusinessProduct(businessProductPO);
						if(Constants.PROD_BIZ_CATEGORY__20003.equals(businessProductPO.getProductCategory1())){
							tProductAbbrName = businessProductPO.getProductAbbrName();
							tProductNameStd=businessProductPO.getProductNameStd();
							tProductCode = businessProductPO.getProductCodeSys();
							tProductCodeSTD = businessProductPO.getProductCodeStd();
						}
					 }
				 }
				 String tProductAbbrNameN="";//年金险简称
				 String tProductNameStdN="";//年金险标准名称
				 String tProductCodeN = "";//年金险代码
				 contractBusiProdPO.setPolicyCode(oldPolicyCode);
				 List<ContractBusiProdPO> contractBusiProdPONJList = contractBusiProdDao.findAllContractBusiProd(contractBusiProdPO );
				 List<String> tProductNameStdNs = new ArrayList<String>();
				 for(ContractBusiProdPO tContractBusiProdPO:contractBusiProdPONJList){
				 	 if(tContractBusiProdPO.getMasterBusiItemId()==null){
				 		businessProductPO = new BusinessProductPO();
				 		businessProductPO.setBusinessPrdId(tContractBusiProdPO.getBusiPrdId());
				 		businessProductPO=businessProductDao.findBusinessProduct(businessProductPO);
				 		tProductAbbrNameN = businessProductPO.getProductAbbrName();
				 		tProductNameStdN=businessProductPO.getProductNameStd();
				 		if(CollectionUtils.isEmpty(csContractRelationNewPOs)){
				 			for(CsContractRelationPO ccr : csContractRelationOldPOs){
				 				if(tContractBusiProdPO.getBusiItemId().compareTo(ccr.getMasterBusiItemId())==0){
				 					tProductNameStdNs.add(tProductNameStdN);
				 				}
				 			}
				 		}else {
				 			for(CsContractRelationPO ccr : csContractRelationNewPOs){
				 				if(tContractBusiProdPO.getBusiItemId().compareTo(ccr.getMasterBusiItemId())==0){
				 					tProductNameStdNs.add(tProductNameStdN);
				 				}
				 			}
						}
				 		tProductCodeN = businessProductPO.getProductCodeSys();
				 	 }
				}
                Element productName = edorList53Element.addElement("ProductName");//险种名称
                productName.setText("《"+tProductAbbrName+"》");
                Element productNo = edorList53Element.addElement("ProductNo");//险种代码
                productNo.setText(tProductCodeSTD);
                Element tNote = edorList53Element.addElement("Note");//险种名称 
                StringBuffer tNoteText=new StringBuffer(); 
                List<String> remarkList=new ArrayList<String>();
                List<String> contentList = new ArrayList<String>();
                if(contractMasterPO.getMultiMainriskFlag()!=null&&contractMasterPO.getMultiMainriskFlag().compareTo(Constants.MULTI_MAINRISK_FLAG1)==0) {
                	//多主险要求所有的年金险均要与能关联的万能险输出如下条款。
                	for(int i=0;i<tProductNameStdNs.size();i++){
                		String njProductName = tProductNameStdNs.get(i);
                		if(i+1 ==tProductNameStdNs.size()){
                			tNoteText.append("《"+njProductName+"》");        							
                		}else{
                			tNoteText.append("《"+njProductName+"》、");
                		}
                	}
                	if (isYesOrNO) {
                		//如果是年金险或是具有年金责任，且含有被关联的万能险，则输出条款，否则不用输出。
                		contentList.add("建立"+ oldPolicyCode +"号保单与"+ tRelationPolicyCode +"号保单的关联关系。");
                		if (newPolicyRelationType.intValue() == 2) {
                			contentList.add("建立关联后，经投保人和被保险人同意，在保单"+ tRelationPolicyCode +"下《"+tProductNameStd+"》合同有效的情况下，" +
                					"保单"+oldPolicyCode+"下" + tNoteText + "合同有效期内的生存类保险金（如有）、现金红利（如有）转为保单" +
                					newRelationPolicyCode+"下《"+tProductNameStd+"》的保险费，由投保人予以交纳。");
                		}
                		if (newPolicyRelationType.intValue() == 3) {
                			contentList.add("建立关联后，经投保人和被保险人同意，在保单"+ tRelationPolicyCode +"下《"+tProductNameStd+"》合同有效的情况下，保单" + tRelationPolicyCode +"《"+tProductNameStd+
                					"合同部分领取保单账户价值用于交纳保单"+oldPolicyCode+"下" + tNoteText + "及各自所附附加险的续期或者续保保险费。^当未及时交纳保单" +oldPolicyCode+"下全部当期保险费（除另有约定外，全部当期保险费指前述贵公司认可的产品当期应交纳保险费之和）时，将于约定期间内通过部分领取保单"
                					+newRelationPolicyCode+"下《"+tProductNameStd+"》合同保单账户价值（部分领取时的费用收取情况以保单"+newRelationPolicyCode+"下《"+tProductNameStd+"》合同条款约定为准）予以交纳，保单账户价值相应减少。");
                			contentList.add("部分领取后的保单账户价值余额低于本公司规定的最低金额时不能进行部分领取，保单贷款期间不能进行部分领取。");
                		}
                		if (newPolicyRelationType.intValue() == 4) {
                			contentList.add("建立关联后，经投保人和被保险人同意，在保单"+ tRelationPolicyCode +"下《"+tProductNameStd+"》合同有效的情况下，" +
                					"保单"+oldPolicyCode+"下" + tNoteText + "合同有效期内的生存类保险金（如有）、现金红利（如有）转为保单" + newRelationPolicyCode+"下《"+tProductNameStd+"》合同的保险费，由投保人予以交纳。");
                			contentList.add("在保单"+ tRelationPolicyCode +"下《"+tProductNameStd+"》合同有效的情况下，保单" + tRelationPolicyCode +"下《"+tProductNameStd+
                					"合同部分领取保单账户价值用于交纳保单"+oldPolicyCode+"下"
                					 + tNoteText + "及各自所附附加险的续期或者续保保险费。^当未及时交纳保单"+oldPolicyCode+"下全部当期保险费（除另有约定外，全部当期保险费指前述贵公司认可的产品当期应交纳保险费之和）时，将于约定期间内通过部分领取保单"
                 					+ tRelationPolicyCode +"下《"+tProductNameStd+"合同保单账户价值（部分领取时的费用收取情况以保单"+ tRelationPolicyCode +"下《"+tProductNameStd+"合同条款约定为准）予以交纳，保单账户价值相应减少。");
                			contentList.add("部分领取后的保单账户价值余额低于本公司规定的最低金额时不能进行部分领取，保单贷款期间不能进行部分领取。");
                		}
                	}else{
                		contentList.add("取消"+ oldPolicyCode +"号保单与"+ tRelationPolicyCode +"号保单的关联关系。");
                		if (newPolicyRelationType.intValue() == 2) {
                			contentList.add("取消关联后，经投保人和被保险人同意，在保单"+ tRelationPolicyCode +"下《"+tProductNameStd+"》合同有效的情况下，" +
                					"保单"+oldPolicyCode+"下" + tNoteText + "合同有效期内的生存类保险金（如有）、现金红利（如有）不再转为保单" +
                					newRelationPolicyCode+"下《"+tProductNameStd+"》的保险费。");
                		}
                		if (newPolicyRelationType.intValue() == 3) {
                			contentList.add("取消关联后，经投保人和被保险人同意，在保单"+ tRelationPolicyCode +"下《"+tProductNameStd+"》合同有效的情况下，保单" + tRelationPolicyCode +"下《"+tProductNameStd+
                					"合同部分领取保单账户价值不再用于交纳保单"+oldPolicyCode+"下" + tNoteText + "及各自所附附加险的续期或者续保保险费。");
                		}
                		if (newPolicyRelationType.intValue() == 4) {
                			contentList.add("取消关联后，经投保人和被保险人同意，在保单"+ tRelationPolicyCode +"下《"+tProductNameStd+"》合同有效的情况下，" +
                					"保单"+oldPolicyCode+"下" + tNoteText + "合同有效期内的生存类保险金（如有）、现金红利（如有）不再转为保单" + newRelationPolicyCode+"下《"+tProductNameStd+"》合同的保险费。");
                			contentList.add("在保单"+ tRelationPolicyCode +"下《"+tProductNameStd+"》合同有效的情况下，保单" + tRelationPolicyCode +"下《"+tProductNameStd+
                					"合同部分领取保单账户价值不再用于交纳保单"+oldPolicyCode+"下" + tNoteText + "及各自所附附加险的续期或者续保保险费。");
                		}
                	}
                }else{
                	if (isYesOrNO) {
                		if ((Constants.BUSIPROD_CODE_429.equals(tProductCodeN)/*新增429华实人生终身年金保险*/ || ConstantsBusiProdCode.BUSI_PROD_CODE_00457000.equals(tProductCodeN) 
                				|| Constants.BUSIPROD_CODE_450.equals(tProductCodeN) || Constants.BUSIPROD_CODE_451.equals(tProductCodeN) ||
                				Constants.BUSIPROD_CODE_452.equals(tProductCodeN) || Constants.BUSIPROD_CODE_453.equals(tProductCodeN)
                				|| Constants.BUSIPROD_CODE_4291.equals(tProductCodeN)
                				|| Constants.BUSIPROD_CODE_458.equals(tProductCodeN))
                				&& (Constants.BUSIPROD_CODE_926.equals(tProductCode) || Constants.BUSIPROD_CODE_924.equals(tProductCode))) {
                			tNoteText.append("建立关联后，");
                			tNoteText.append("经投保人和被保险人同意，在《"+tProductNameStd+"》合同有效的情况下，" +
                					"保单"+oldPolicyCode+"下《"+tProductNameStdN+"》合同有效期内的生存类保险金及现金红利（如有）" +
                					"转为保单"+newRelationPolicyCode+"下《"+tProductNameStd+"》合同的保险费，由投保人予以交纳。");
                		}else if ((Constants.BUSIPROD_CODE_443.equals(tProductCodeN) && (Constants.BUSIPROD_CODE_924.equals(tProductCode) || Constants.BUSIPROD_CODE_926.equals(tProductCode)))
                				|| (Constants.BUSIPROD_CODE_446.equals(tProductCodeN) && Constants.BUSIPROD_CODE_926.equals(tProductCode))
                				|| ("00464000".equals(tProductCodeN) && Constants.BUSIPROD_CODE_924.equals(tProductCode))) {
                			tNoteText.append("建立关联后，");
                			tNoteText.append("经投保人和被保险人同意，在《"+tProductNameStd+"》合同有效的情况下，" +
                					"保单"+oldPolicyCode+"下"+tProductNameStdN+"合同的生存类保险金及现金红利（如有）" +
                					"转为保单"+newRelationPolicyCode+"下《"+tProductNameStd+"》合同的保险费，由投保人予以交纳。");
                		}
                	}else {
                		if ((Constants.BUSIPROD_CODE_429.equals(tProductCodeN)/*新增429华实人生终身年金保险*/ || ConstantsBusiProdCode.BUSI_PROD_CODE_00457000.equals(tProductCodeN) 
                				||Constants.BUSIPROD_CODE_450.equals(tProductCodeN) || Constants.BUSIPROD_CODE_451.equals(tProductCodeN) ||
                				Constants.BUSIPROD_CODE_452.equals(tProductCodeN) || Constants.BUSIPROD_CODE_453.equals(tProductCodeN)
                				|| Constants.BUSIPROD_CODE_4291.equals(tProductCodeN)
                				|| Constants.BUSIPROD_CODE_458.equals(tProductCodeN))
                				&& (Constants.BUSIPROD_CODE_926.equals(tProductCode) || Constants.BUSIPROD_CODE_924.equals(tProductCode))) {
                			tNoteText.append("取消关联后，");
                			tNoteText.append("经投保人和被保险人同意，在《"+tProductNameStd+"》合同有效的情况下，" +
                					"保单"+oldPolicyCode+"下《"+tProductNameStdN+"》合同有效期内的生存类保险金及现金红利（如有）" +
                					"不再转为保单"+oldRelationPolicyCode+"下《"+tProductNameStd+"》合同的保险费。");
                		}else if ((Constants.BUSIPROD_CODE_443.equals(tProductCodeN) && (Constants.BUSIPROD_CODE_924.equals(tProductCode) || Constants.BUSIPROD_CODE_926.equals(tProductCode)))
                				|| (Constants.BUSIPROD_CODE_446.equals(tProductCodeN) && Constants.BUSIPROD_CODE_926.equals(tProductCode)) 
                				|| ("00464000".equals(tProductCodeN) && Constants.BUSIPROD_CODE_924.equals(tProductCode))) {
                			tNoteText.append("取消关联后，");
                			tNoteText.append("经投保人和被保险人同意，在《"+tProductNameStd+"》合同有效的情况下，" +
                					"保单"+oldPolicyCode+"下"+tProductNameStdN+"合同的生存类保险金及现金红利（如有）" +
                					"不再转为保单"+oldRelationPolicyCode+"下《"+tProductNameStd+"》合同的保险费，由投保人予以交纳。");
                		}
                	}
                }
                
                
                
                
                
                
                if(contractMasterPO.getMultiMainriskFlag() != null  && contractMasterPO.getMultiMainriskFlag().compareTo(Constants.MULTI_MAINRISK_FLAG1)==0){
                	remarkList.addAll(remarkList.size(),contentList);
                }else{
                	tNote.setText(tNoteText.toString());
                }
                
                Element RelationsElement = edorList53Element.addElement("Relations");
                String Relations="";
                BigDecimal relationType = newPolicyRelationType;
               
                if (ConstantsBusiProdCode.BUSI_PROD_CODE_00927000.equals(tProductCode)) {
                    if (relationType != null) {
                        if (relationType.intValue() == 0) {
                            Relations = "1";
                        } else if (relationType.intValue() == 1) {
                            Relations = "2";
                        } else if (relationType.intValue() == 2) {
                            Relations = "3";
                        }
                    } else {
                        Relations = "4";// 取消关联
                    }
                }
                RelationsElement.setText(Relations); // 关联927关系
                
                Element RemarkElement = edorList53Element.addElement("Remark");    
                Element relPrdInfoElement = edorList53Element.addElement("RelPrdInfo");
                   
                
                ContractMasterPO relationConMaster = new ContractMasterPO();              
                relationConMaster.setPolicyCode(tRelationPolicyCode);
                relationConMaster = contractMasterDao.findContractMaster(relationConMaster);
                BigDecimal policyRelationType=null;
                if (isYesOrNO) {
                    // 建立关联
                    policyRelationType = newPolicyRelationType;
                } else {
                    // 取消关联
                    policyRelationType = oldPolicyRelationType;
                }
                
                
                Map<String,Object> data=new HashMap<String, Object>();                
                data.put("policyCode", oldPolicyCode);
                data.put("applyCode", oldApplyCode);
                data.put("relationPCode", tRelationPolicyCode);
                data.put("relationApplyCode",relationConMaster.getApplyCode());
                data.put("relationType",policyRelationType);
                data.put("isYesOrNO", isYesOrNO);
                data.put("busiProdName",tProductNameStdN );
                data.put("relationBusiName",tProductNameStd);
                data.put("relationApplyDate", relationConMaster.getApplyDate());
                data.put("tProductCode", tProductCode);
              
                if (tProductCode != null && ConstantsBusiProdCode.BUSI_PROD_CODE_RS_LIST.contains(tProductCode)&&
                		contractMasterPO.getMultiMainriskFlag() != null && contractMasterPO.getMultiMainriskFlag().compareTo(Constants.MULTI_MAINRISK_FLAG1)!=0) {
                    String oldRelationPCode = oldRelationPolicyCode;
                    String newRelationPCode = newRelationPolicyCode;
                    if (isYesOrNO && !StringUtilsEx.isNullOrEmpty(oldRelationPCode) && !oldRelationPCode.equals(newRelationPCode)) {
                        remarkList.add("取消" + oldPolicyCode + "号保单与" + oldRelationPCode + "号保单的关联关系。");
                    }
                    remarkList.addAll(remarkList.size(), initRemarkData(data));
                }
                
                if (CollectionUtilEx.isNotEmpty(remarkList)) {
                    for (int i = 0; i < remarkList.size(); i++) {
                        Element RemarkInfoElement = RemarkElement.addElement("RemarkInfo");
                        RemarkInfoElement.setAttributeValue("id", (i + 1 + ""));
                        Element contentElement = RemarkInfoElement.addElement("content");
                        String content = remarkList.get(i);
                        contentElement.setText(content);
                        Element relPrdInfoListElement = relPrdInfoElement.addElement("RelPrdInfoList"); 
                        Element relPrdContentElement = relPrdInfoListElement.addElement("RelPrdContent");
                        String relPrdContent = remarkList.get(i);
                        relPrdContentElement.setText(relPrdContent);
                    }
                }
                
				// rm:192285 start
				Element bonusRcvWayInfoElement = edorList53Element.addElement("BonusRcvWayInfo");
				
				CsPayPlanPO csPayPlanPONew = new CsPayPlanPO();
				csPayPlanPONew.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
				csPayPlanPONew.setOldNew(Constants.NEW);
				csPayPlanPONew.setOperationType(Constants.OPERATE_FLAG_UPDATE);
				csPayPlanPONew.setPayPlanType(Constants.PAY_PLAN_TYPE_1);
				List<CsPayPlanPO> findAllCsPayPlan = csPayPlanDao.findAllCsPayPlan(csPayPlanPONew);
				
				boolean freendlyFlag = false;
				for (CsPayPlanPO csPayPlanPO : findAllCsPayPlan) {
					// 温馨提示话术只有在变更后的领取形式为转万能的时候才需要显示，多个变更中存在多个转万能的只需要添加一次温馨提示即可
					if(csPayPlanPO.getSurvivalMode() != null && !freendlyFlag && csPayPlanPO.getSurvivalMode().compareTo(new BigDecimal(4)) == 0 ){
						// 温馨提示话术
						Element freendlyTipsElement = edorList53Element.addElement("FreendlyTips");
						freendlyTipsElement.setText(Constants.FreendlyTips);
						freendlyFlag = true;
					}
					
					Element bonusRcvWayListElement = bonusRcvWayInfoElement.addElement("BonusRcvWayList");
					Element productNoElement = bonusRcvWayListElement.addElement("ProductNo");
					Element productNameElement = bonusRcvWayListElement.addElement("ProductName");
					Element bonusRcvWayElement = bonusRcvWayListElement.addElement("BonusRcvWay");
					productNoElement.setText(csPayPlanPO.getBusiProdCode());
					bonusRcvWayElement.setText(CodeTable.getCodeDesc("APP___PAS__DBUSER.T_SURVIVAL_MODE",
							csPayPlanPO.getSurvivalMode().toString()));
					BusinessProductPO businessProductPOForBonusRcvWay = new BusinessProductPO();
					businessProductPOForBonusRcvWay.setProductCodeSys(csPayPlanPO.getBusiProdCode());
					businessProductPOForBonusRcvWay = businessProductDao
							.findBusinessProduct(businessProductPOForBonusRcvWay);
					productNameElement.setText(businessProductPOForBonusRcvWay.getProductAbbrName());
				}
				// rm:192285 end
                
                // 调用打印系统打印批单 
                if (!csPrintEndorseService.callPrintInterface(csPrintXmlDocument ,csAcceptChangeBO.getAcceptCode(),csPolicyChangeBO.getPolicyCode())) {
                    flag = false;
                }

            }
        }
        retBo.setSucceed(flag);
        return retBo;
    }
	
	private List<String> initRemarkData(Map<String,Object> map) {		
	    List<String> contentList = new ArrayList<String>();
	    //年金险
	    String policyCode=(String) map.get("policyCode");
	    String applyCode=(String) map.get("applyCode");
	    String busiProdName=(String) map.get("busiProdName");
	    //万能险
	    String relationPCode=(String) map.get("relationPCode");
	    String relationApplyCode=(String) map.get("relationApplyCode");
	    String relationBusiName=(String) map.get("relationBusiName");
	    String tProductCode=(String) map.get("tProductCode");

	    BigDecimal relationType=(BigDecimal) map.get("relationType");
	    boolean isYesOrNO=(boolean) map.get("isYesOrNO");
	    
//	    Date relationApplyDate=(Date)map.get("relationApplyDate");
//	    
//	    
//	    CsQueryParamPO queryPO = new CsQueryParamPO();
//        queryPO.setConstantsKey("'APPLY_DATE_927'");
//        queryPO = csEntryDao.queryConstantsInfo(queryPO);
//        String date = queryPO.getConstantsValue();
//		Date pZDate = new Date();
//        if(!StringUtilsEx.isNullOrEmpty(date)){
//        	pZDate = DateUtilsEx.formatToDate(date, "yyyy-MM-dd");
//        }
	    
       /* if(relationApplyDate.compareTo(pZDate)>=0){*/

            if (isYesOrNO) {            
                contentList.add("建立"+policyCode+"号保单与"+relationPCode+"号保单的关联关系。");
                
                /** 建立关系 */
                if (relationType.intValue()==0) {
                	if("00930000".equals(tProductCode)){// 由于930产品部分内容需要加粗,和打印约定加粗内容之前加"^"做特殊判断
                		// 0-生存类保险金转入万能险合同              
                		contentList.add("建立关联后，经投保人和被保险人同意，在《" + relationBusiName + "》合同有效的情况下，保单" + policyCode + "下" + "《" + busiProdName + "》合同有效期内的生存类保险金（如有）、现金红利（如有）转为保单" + relationPCode
                				+ "下《" + relationBusiName + "》合同的保险费，由投保人予以交纳。");
                	}else {
                		// 0-生存类保险金转入万能险合同              
                		contentList.add("建立关联后，经投保人和被保险人同意，在《" + relationBusiName + "》合同有效的情况下，保单" + policyCode + "下" + "《" + busiProdName + "》合同有效期内的生存类保险金及现金红利（如有）转为保单" + relationPCode
                				+ "下《" + relationBusiName + "》合同的保险费，由投保人予以交纳。");
					}
                }
                if (relationType.intValue()==1) {
                	if("00930000".equals(tProductCode)){// 由于930产品部分内容需要加粗,和打印约定加粗内容之前加"^"做特殊判断
	            		 // 1-万能险合同账户价值交纳续期或续保保险费时
                		// 1-万能险合同账户价值交纳续期或续保保险费时
                        contentList.add("建立关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下，保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值"
                        		+ "用于交纳"+policyCode+"保险合同的续期保险费或续保保险费，^当"+policyCode+"保险合同未及时交纳全部当期保险费（除另有约定外，全部当期保险费指保险合同下"
                                + "所有产品当期应交纳保险费之和）时，将于约定期间内通过部分领取保单"+relationPCode+"下《"+relationBusiName+"》合同保单账户价值（部分领取时的费用收取情况以上述"
                                + "《"+relationBusiName+"》合同条款约定为准）予以交纳，保单账户价值相应减少。");
		                contentList.add("部分领取后的保单账户价值余额低于本公司规定的最低金额时不能进行部分领取，保单贷款期间不能进行部分领取。");
                	}else {
                		// 1-万能险合同账户价值交纳续期或续保保险费时
                        contentList.add("建立关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下，保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值"
                        		+ "用于交纳"+policyCode+"保险合同的续期保险费或续保保险费，当"+policyCode+"保险合同未及时交纳全部当期保险费（除另有约定外，全部当期保险费指保险合同下"
                                + "所有产品当期应交纳保险费之和）时，将于约定期间内通过部分领取保单"+relationPCode+"下《"+relationBusiName+"》合同保单账户价值，在扣除退保费用后予以交纳（退保费用收取及对应比例以所签订的"
                                + "《"+relationBusiName+"》合同利益条款约定为准），保单账户价值相应减少。部分领取后的保单账户价值余额低于本公司规定的最低金额时不能进行部分领取，保单贷款期间不能进行部分领取。");
					}
                }

                if (relationType.intValue()==2) {
                	if("00930000".equals(tProductCode)){// 由于930产品部分内容需要加粗,和打印约定加粗内容之前加"^"做特殊判断
                		 // 2-生存类保险金转入万能险合同&万能险合同账户价值交纳续期或续保保险费
                        contentList.add("建立关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下：将保单"+policyCode+"下《"+busiProdName+"》合同有效期内的生存类保险金（如有）、现金红利（如有）"
                                + "转为保单"+relationPCode+"下《"+relationBusiName+"》合同的保险费，由投保人予以交纳；保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值用于交纳"
                                + ""+policyCode+"保险合同的续期保险费或续保保险费，^当"+policyCode+"保险合同未及时交纳全部当期保险费（除另有约定外，全部当期保险费指保险合同下所有产品当期应交纳保险费之和）时，"
                                + "将于约定期间内通过部分领取保单"+relationPCode+"下《"+relationBusiName+"》合同保单账户价值（部分领取时的费用收取情况以上述《"+relationBusiName+"》"
                                + "合同条款约定为准）予以交纳，保单账户价值相应减少。");
                        contentList.add("部分领取后的保单账户价值余额低于本公司规定的最低金额时不能进行部分领取，保单贷款期间不能进行部分领取。");
                	}else{
                		 // 2-生存类保险金转入万能险合同&万能险合同账户价值交纳续期或续保保险费
                        contentList.add("建立关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下：将保单"+policyCode+"项下《"+busiProdName+"》合同有效期内的生存类保险金及现金红利"
                                + "（如有）转为保单"+relationPCode+"下《"+relationBusiName+"》合同的保险费，由投保人予以交纳；保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值用于交纳"
                                + ""+policyCode+"保险合同的续期保险费或续保保险费，当"+policyCode+"保险合同未及时交纳全部当期保险费（除另有约定外，全部当期保险费指保险合同下所有产品当期应交纳保险费之和）时，"
                                + "将于约定期间内通过部分领取保单"+relationPCode+"下《"+relationBusiName+"》合同保单账户价值予以交纳，在扣除退保费用后予以交纳（退保费用收取及对应比例以所签订的《"+relationBusiName+"》"
                                + "合同利益条款约定为准），保单账户价值相应减少。部分领取后的保单账户价值余额低于本公司规定的最低金额时不能进行部分领取，保单贷款期间不能进行部分领取。");
                	}
                }
            } else {
                /** 取消关系 */
            	contentList.add("取消"+policyCode+"号保单与"+relationPCode+"号保单的关联关系。");
                
                if (relationType.intValue()==0) {
                	if("00930000".equals(tProductCode)){// 由于930产品部分内容需要加粗,和打印约定加粗内容之前加"^"做特殊判断
                		// 0-生存类保险金转入万能险合同                
                    	contentList.add("取消关联后，经投保人和被保险人同意，在《" + relationBusiName + "》合同有效的情况下，保单" + policyCode + "下" + "《" + busiProdName + "》合同有效期内的生存类保险金（如有）、现金红利（如有）不再转为保单" + relationPCode
                                + "下《" + relationBusiName + "》合同的保险费。");
                	}else {
                		// 0-生存类保险金转入万能险合同                
                    	contentList.add("取消关联后，经投保人和被保险人同意，在《" + relationBusiName + "》合同有效的情况下，保单" + policyCode + "下" + "《" + busiProdName + "》合同有效期内的生存类保险金及现金红利（如有）不再转为保单" + relationPCode
                                + "下《" + relationBusiName + "》合同的保险费，由投保人予以交纳。");
					}
                    
                }
                if (relationType.intValue()==1) {
                	if("00930000".equals(tProductCode)){// 由于930产品部分内容需要加粗,和打印约定加粗内容之前加"^"做特殊判断
                		// 1-万能险合同账户价值交纳续期或续保保险费时
                        contentList.add("取消关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下，保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值不再用于"
                                + "交纳"+policyCode+"保险合同的续期保险费或续保保险费。");
                	}else{
                		// 1-万能险合同账户价值交纳续期或续保保险费时
                        contentList.add("取消关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下，保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值不再用于"
                                + "交纳"+policyCode+"保险合同的续期保险费或续保保险费。"+policyCode+"保险合同全部当期保险费（除另有约定外，全部当期保险费指保险合同下所有产品当期未交纳保险费之和）"
                                + "由投保人予以交纳。");
                	}
         
                }

                if (relationType.intValue()==2) {
                	if("00930000".equals(tProductCode)){// 由于930产品部分内容需要加粗,和打印约定加粗内容之前加"^"做特殊判断
                		// 2-生存类保险金转入万能险合同&万能险合同账户价值交纳续期或续保保险费
                        contentList.add("取消关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下：将保单"+policyCode+"下《"+busiProdName+"》合同有效期内的生存类保险金（如有）、现金红利（如有）"
                                + "不再转为保单"+relationPCode+"下《"+relationBusiName+"》合同的保险费；保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值不再用于交纳"+policyCode+""
                                + "保险合同的续期保险费或续保保险费。");
                	}else{
                		// 2-生存类保险金转入万能险合同&万能险合同账户价值交纳续期或续保保险费
                        contentList.add("取消关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下：将保单"+policyCode+"项下《"+busiProdName+"》合同有效期内的生存类保险金及现金红利（如有）"
                                + "不再转为保单"+relationPCode+"下《"+relationBusiName+"》合同的保险费；保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值用于交纳"+policyCode+""
                                + "保险合同的续期保险费或续保保险费，"+policyCode+"保险合同全部当期保险费由投保人予以交纳。");
                	}
                }
            }
            
        	
        	
        /*}else{
        	
        
        if (isYesOrNO) {            
            contentList.add("建立"+policyCode+"号保单与"+relationPCode+"号保单的关联关系。");
            
            *//** 建立关系 *//*
            if (relationType.intValue()==0) {
                // 0-生存类保险金转入万能险合同              
                contentList.add("建立关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下，保单"+policyCode+"、"+policyCode+"下《"+busiProdName+"》、"+policyCode+"下"
                        + "《"+busiProdName+"》合同有效期内的生存类保险金及现金红利（如有）转为保单"+relationPCode+"下《"+relationBusiName+"》合同的保险费，由投保人予以交纳。");
            }
            if (relationType.intValue()==1) {
                // 1-万能险合同账户价值交纳续期或续保保险费时
                contentList.add("建立关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下，保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值"
                		+ "用于交纳"+policyCode+"保险合同的续期保险费或续保保险费，当"+policyCode+"保险合同未及时交纳全部当期保险费（除另有约定外，全部当期保险费指保险合同下"
                        + "所有产品当期未交纳保险费之和）时，将于约定日期起通过部分领取"+relationPCode+"下《"+relationBusiName+"》合同保单账户价值予以交纳（不收取《"+relationBusiName+"》"
                        + "合同利益条款第八条规定的退保费用），保单账户价值相应减少。部分领取后的保单账户价值余额低于本公司规定的最低金额时不能进行部分领取，保单贷款期间不能进行部分领取。");
            }

            if (relationType.intValue()==2) {
                // 2-生存类保险金转入万能险合同&万能险合同账户价值交纳续期或续保保险费
                contentList.add("建立关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下：将保单"+policyCode+"项下《"+busiProdName+"》合同有效期内的生存类保险金及现金红利"
                        + "（如有）转为保单"+relationPCode+"下《"+relationBusiName+"》合同的保险费，由投保人予以交纳；保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值用于交纳"
                        + ""+policyCode+"保险合同的续期保险费或续保保险费，当"+policyCode+"保险合同未及时交纳全部当期保险费（除另有约定外，全部当期保险费指保险合同下所有产品当期未交纳保险费之和）时，"
                        + "将于约定日期起通过部分领取保单"+relationApplyCode+"下《"+relationBusiName+"》合同保单账户价值予以交纳（不收取《"+relationBusiName+"》合同利益条款第八条规定的退保费用），保单"
                        + "账户价值相应减少。部分领取后的保单账户价值余额低于本公司规定的最低金额时不能进行部分领取，保单贷款期间不能进行部分领取。");
            }
        } else {
            *//** 取消关系 *//*
            contentList.add("取消"+policyCode+"号保单与"+relationPCode+"号保单的关联关系。");
            
            if (relationType.intValue()==0) {
                // 0-生存类保险金转入万能险合同                
                contentList.add("取消关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下，保单"+policyCode+"、"+policyCode+"下《"+busiProdName+"》、"+policyCode+"下"
                		+ "《"+busiProdName+"》合同有效期内的生存类保险金及现金红利（如有）不再转为保单"+relationPCode+"下《"+relationBusiName+"》合同的保险费，由投保人予以交纳。");
            }
            if (relationType.intValue()==1) {
                // 1-万能险合同账户价值交纳续期或续保保险费时
                contentList.add("取消关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下，保单"+relationPCode+"下《"+relationBusiName+"》合同部分领取保单账户价值不再用于"
                        + "交纳"+policyCode+"保险合同的续期保险费或续保保险费。"+policyCode+"保险合同全部当期保险费（除另有约定外，全部当期保险费指保险合同下所有产品当期未交纳保险费之和）"
                        + "由投保人予以交纳。");
     
            }

            if (relationType.intValue()==2) {
                // 2-生存类保险金转入万能险合同&万能险合同账户价值交纳续期或续保保险费
                contentList.add("取消关联后，经投保人和被保险人同意，在《"+relationBusiName+"》合同有效的情况下：将保单"+policyCode+"项下《"+busiProdName+"》合同有效期内的生存类保险金及现金红利（如有）"
                        + "不再转为保单"+relationPCode+"下《"+relationBusiName+"》合同的保险费；保单"+relationPCode+"号下《"+relationBusiName+"》合同部分领取保单账户价值用于交纳"+policyCode+""
                        + "保险合同的续期保险费或续保保险费，"+policyCode+"保险合同全部当期保险费由投保人予以交纳。");
            }
        }
        }*/
        return contentList;
	}


}
