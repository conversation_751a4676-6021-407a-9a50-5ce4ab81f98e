package com.nci.tunan.cs.impl.peripheral.ucc.r06801003101;

import com.nci.tunan.cs.interfaces.peripheral.exports.r06801003101.vo.OutputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r06801003101.vo.InputData;
/**
 * 
 * @description 订单号查询受理号及状态
 * <AUTHOR> <EMAIL> 
 * @date 2015年05月15日 下午1:39:53 
 * @.belongToModule 保全子系统
 */
public interface IQueryOrderNumberUcc {
	/**
	 * 
	 * @description 订单号查询受理号及状态
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param input 入参
	 * @return
	 */
	public OutputData queryOrderNumber(InputData input);
}
