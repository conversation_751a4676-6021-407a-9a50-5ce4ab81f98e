package com.nci.tunan.cs.impl.csConfig.ucc.impl;



import java.util.ArrayList;
import java.util.List;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.tunan.cs.impl.csConfig.service.ICsPostCodeCfgService;
import com.nci.tunan.cs.impl.csConfig.ucc.ICsPostCodeCfgUCC;
import com.nci.tunan.cs.model.bo.CsPostCodeCfgBO;
import com.nci.tunan.cs.model.vo.CsReviewResultCfgVO;
import com.nci.tunan.pa.interfaces.model.vo.CsPostCodeCfgVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.StringUtilsEx;
/**
 * @description 复核结论配置 UCC接口实现类
 * @date 2021-03-03
 *ICsPostcodeCfgUCC
 */
public class CsPostCodeCfgUCCImpl implements ICsPostCodeCfgUCC{
	/**
	 * 复核结论配置 Service
	 */
    private ICsPostCodeCfgService csPostcodeCfgService;

    public ICsPostCodeCfgService getCsPostcodeCfgService() {
		return csPostcodeCfgService;
	}
	public void setCsPostcodeCfgService(ICsPostCodeCfgService csPostcodeCfgService) {
		this.csPostcodeCfgService = csPostcodeCfgService;
	}

	public CsPostCodeCfgVO addReviewResultCfg(CsPostCodeCfgVO csPostCodeCfgVO) {
		CsPostCodeCfgVO csPostCodeCfgVO1 = new CsPostCodeCfgVO();
		// TODO Auto-generated method stub
		return csPostCodeCfgVO1;
	}
	public List<CsPostCodeCfgVO> findAllCsPostCodeCfg(CsPostCodeCfgVO csPostCodeCfgVO) {
		// TODO Auto-generated method stub
		CsPostCodeCfgBO csPostCodeCfgBO = BeanUtils.copyProperties(CsPostCodeCfgBO.class, csPostCodeCfgVO);
		List<CsPostCodeCfgBO> csPostCodeCfgBOlist = csPostcodeCfgService.findAllCsPostCodeCfg(csPostCodeCfgBO);
		
		if(CollectionUtilEx.isNotEmpty(csPostCodeCfgBOlist)){
			for(CsPostCodeCfgBO csPostCodeCfgBOnew :csPostCodeCfgBOlist){
				if(csPostCodeCfgVO != null && !StringUtilsEx.isNullOrEmpty(csPostCodeCfgVO.getState())){
					csPostCodeCfgBOnew.setState(csPostCodeCfgVO.getState());
				}
				if(csPostCodeCfgVO != null && !StringUtilsEx.isNullOrEmpty(csPostCodeCfgVO.getDistrict())){
					csPostCodeCfgBOnew.setDistrict(csPostCodeCfgVO.getDistrict());
				}
			}
		}

		return BeanUtils.copyList(CsPostCodeCfgVO.class, csPostCodeCfgBOlist);
	}
	public CsPostCodeCfgVO findCsPostCodeCfg(CsPostCodeCfgVO csPostCodeCfgVO) {

		CsPostCodeCfgBO csPostCodeCfgBO = BeanUtils.copyProperties(CsPostCodeCfgBO.class, csPostCodeCfgVO);
		
		csPostCodeCfgBO = csPostcodeCfgService.findCsPostCodeCfg(csPostCodeCfgBO);		
		
		return BeanUtils.copyProperties(CsPostCodeCfgVO.class, csPostCodeCfgBO);
	}
	@Override
	public boolean deleteReviewResultCfg(CsPostCodeCfgVO csPostCodeCfgVO) {		
		return false;
	}
	@Override
	public CsPostCodeCfgVO updateCsPostCodeCfg(CsPostCodeCfgVO csPostCodeCfgVO) {
		CsPostCodeCfgVO csPostCodeCfgVO1 = new CsPostCodeCfgVO();
		return csPostCodeCfgVO1;
	}
	@Override
	public CurrentPage queryReviewResultCfg(CsReviewResultCfgVO csReviewResultCfgVO,
			CurrentPage<CsReviewResultCfgVO> currentPage) {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public boolean batchUpdateCsPostCodeCfg(List<CsPostCodeCfgVO> csPostCodeCfgVOlist) {
		
		boolean isChange = false;
		List<CsPostCodeCfgBO> csPostCodeCfgBOlist = BeanUtils.copyList(CsPostCodeCfgBO.class, csPostCodeCfgVOlist);	
		List<CsPostCodeCfgBO> csPostCodeCfgBOUpdatelist = new ArrayList<CsPostCodeCfgBO>();
		CsPostCodeCfgBO csPostCodeCfgBOforADD = null;
		for(CsPostCodeCfgBO csPostCodeCfgBO : csPostCodeCfgBOlist){
			try{
				if(csPostCodeCfgBO != null && csPostCodeCfgBO.getCode() != null ){
					csPostCodeCfgBOforADD = new CsPostCodeCfgBO();
					csPostCodeCfgBOforADD = csPostcodeCfgService.findCsPostCodeCfg(csPostCodeCfgBO);
					if(csPostCodeCfgBOforADD != null && StringUtilsEx.isBlank(csPostCodeCfgBOforADD.getPostCode()) ){
						csPostcodeCfgService.addCsPostCodeCfg(csPostCodeCfgBO);
						isChange = true;
						continue;
					}else{
						csPostCodeCfgBOUpdatelist.add(csPostCodeCfgBO);
					}
				}
			}catch (Exception e) {
				e.printStackTrace();
			}	
		}
		if(CollectionUtilEx.isNotEmpty(csPostCodeCfgBOUpdatelist)){
			return csPostcodeCfgService.batchUpdateCsPostCodeCfg(csPostCodeCfgBOUpdatelist);
		}
		return isChange;
	}
		/**
	 * 
	 * @description 查询复核结论配置信息 - 分页查询
	 * <AUTHOR>
		 * @param csReviewResultCfgService 
	 * @return
	 */
	public CurrentPage<CsPostCodeCfgVO> findAllCsPostCodeCfgLX(CsPostCodeCfgVO csPostCodeCfgVO,CurrentPage<CsPostCodeCfgVO> currentPage) {
		CurrentPage<CsPostCodeCfgBO> boPage = BeanUtils.copyCurrentPage(CsPostCodeCfgBO.class, currentPage);
		CsPostCodeCfgBO csPostCodeCfgBO = BeanUtils.copyProperties(CsPostCodeCfgBO.class, csPostCodeCfgVO);	
		CurrentPage<CsPostCodeCfgBO> csPostCodeCfgBOpage = csPostcodeCfgService.findAllCsPostCodeCfgLxService(csPostCodeCfgBO, boPage);
		CurrentPage<CsPostCodeCfgVO> voPage = BeanUtils.copyCurrentPage(CsPostCodeCfgVO.class,csPostCodeCfgBOpage);		 
		 return voPage;
	}
	
}
