package com.nci.tunan.cs.impl.csEffect.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.constants.ConstantsBusiProdCode;
import com.nci.tunan.cs.impl.csItem.service.ICSItemBaseService;
import com.nci.tunan.cs.model.bo.CsAcceptChangeBO;
import com.nci.tunan.cs.model.bo.CsApplicationBO;
import com.nci.tunan.cs.model.bo.CsContractExtendBO;
import com.nci.tunan.cs.model.bo.CsContractInvestBO;
import com.nci.tunan.cs.model.bo.CsContractMasterBO;
import com.nci.tunan.cs.model.bo.CsEffectBO;
import com.nci.tunan.cs.model.bo.CsFundServiceBO;
import com.nci.tunan.cs.model.bo.CsPolicyChangeBO;
import com.nci.tunan.cs.model.bo.CsPolicyChangeLogBO;
import com.nci.tunan.cs.model.po.CsContractBusiProdPO;
import com.nci.tunan.cs.model.po.CsContractExtendPO;
import com.nci.tunan.cs.model.po.CsContractInvestPO;
import com.nci.tunan.cs.model.po.CsContractMasterPO;
import com.nci.tunan.cs.model.po.CsContractProductPO;
import com.nci.tunan.cs.model.po.CsFundServicePO;
import com.nci.tunan.cs.model.po.CsPremArapPO;
import com.nci.tunan.pa.dao.IContractBusiProdDao;
import com.nci.tunan.pa.dao.IContractInvestRateDao;
import com.nci.tunan.pa.dao.IFundGroupTransDao;
import com.nci.tunan.pa.interfaces.model.bo.ContractMasterBO;
import com.nci.tunan.pa.interfaces.model.bo.PremArapBO;
import com.nci.tunan.pa.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.pa.interfaces.model.po.ContractInvestPO;
import com.nci.tunan.pa.interfaces.model.po.ContractInvestRatePO;
import com.nci.tunan.pa.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.pa.interfaces.model.po.ContractProductPO;
import com.nci.tunan.pa.interfaces.model.po.FundTransPO;
import com.nci.tunan.pa.interfaces.model.po.PremArapPO;
import com.nci.tunan.pa.interfaces.model.po.SaChangePO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;

/**
 * 
 * @description 保全生效：追加保费
 * <AUTHOR> <EMAIL>
 * @date 2017-12-14 下午7:31:12
 * @.belongToModule 保全子系统
 */
public class CsEffectAMServiceImpl extends CsEffectServiceImpl {
	/**
	 * 投资账户组合交易表接口
	 */
    @Autowired
    private IFundGroupTransDao fundGroupTransDao;

    @Autowired
    @Qualifier("PA_contractInvestRateDao")
    private IContractInvestRateDao contractInvestRateDao;
    @Autowired
    @Qualifier("PA_contractBusiProdDao")
    private IContractBusiProdDao contractBusiProdDao;
    @Autowired
	@Qualifier("PA_baseCsItemService")
	private ICSItemBaseService baseCsItemService;
    /**
     * 
     * @description 保全生效：追加保费
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.cs.impl.csEffect.service.impl.CsEffectServiceImpl#csEffectService(com.nci.tunan.cs.model.bo.CsAcceptChangeBO)
     * @param csAcceptChangeBO
     *            受理号
     * @return
     */
    @Override
    public CsEffectBO csEffectService(CsAcceptChangeBO csAcceptChangeBO) {
        CsEffectBO csEffectBO = new CsEffectBO();

        Boolean isBonus = false;//  @invalid 判断是否是万能险种,用于判断是否执行持续奖金
        BigDecimal isAddDelay = null == csAcceptChangeBO.getIsAddDelay() ? BigDecimal.ZERO : csAcceptChangeBO.getIsAddDelay(); // @invalid  是否勾选
                                                                                                                               //  @invalid 0 否 1是
        String acceptCode = csAcceptChangeBO.getAcceptCode();
        // 1. 查询变更主表
        CsApplicationBO csApplicationBO = csEndorseXXService.findCsApplicationBO(csAcceptChangeBO.getChangeId());
        List<CsPolicyChangeBO> csPolicyChangeBOs = csMainStreamService.findCsPolicyChanges(csAcceptChangeBO);

        List<CsPolicyChangeLogBO> chgLogs = csAcceptChangeBO.getPaPolicyChangeBOs();// @invalid  保单变更履历表
        PremArapPO premArapPO = new PremArapPO();
        premArapPO.setBusinessCode(csAcceptChangeBO.getAcceptCode());
        List<PremArapPO> premArapPOs = premArapDao.findAllPremArap(premArapPO);
        if(CollectionUtils.isNotEmpty(premArapPOs)){
        	for (PremArapPO cpp : premArapPOs) {
        		if(("01".equals(cpp.getFeeStatus()) || "19".equals(cpp.getFeeStatus())) && cpp.getFinishTime() != null){
        			csAcceptChangeBO.setLiabValidateTime(DateUtilsEx.addDay(cpp.getFinishTime(),1));
        			break;
        		}
        	}
        }
        // 2. 循环查询保全 变更的责任组保额
        for (CsPolicyChangeBO csPolicyChangeBO : csPolicyChangeBOs) {
            isBonus = false;

            BigDecimal changeId = csPolicyChangeBO.getChangeId();
            BigDecimal policyChgId = csPolicyChangeBO.getPolicyChgId();
            BigDecimal policyId = csPolicyChangeBO.getPolicyId();

            //  @invalid 保单变更履历ID t_policy_change
            BigDecimal paPolicyChId = null;
            // @invalid  查询保单变更履历ID
            if (CollectionUtils.isNotEmpty(chgLogs)) {
                for (CsPolicyChangeLogBO logBO : chgLogs) {
                    if (logBO.getPolicyId() != null && logBO.getPolicyId().compareTo(csPolicyChangeBO.getPolicyId()) == 0) {
                        paPolicyChId = logBO.getPolicyChgId();// @invalid  关联到保单变更履历表ID
                        break;
                    }
                }
            }
            // 3.查询获得 险种列表信息
            CsContractBusiProdPO csContractBusiProdPO = new CsContractBusiProdPO();
            csContractBusiProdPO.setChangeId(changeId);
            csContractBusiProdPO.setPolicyChgId(policyChgId);
            csContractBusiProdPO.setPolicyId(policyId);
            csContractBusiProdPO.setOldNew(Constants.NEW);
            List<CsContractBusiProdPO> csContractBusiProdList = csContractBusiProdDao.findAllCsContractBusiProd(csContractBusiProdPO);
            for (CsContractBusiProdPO contractBusiProdPO : csContractBusiProdList) {
            	
                // @invalid 经产品余远丽确认追加保费时仅907险种生成持续奖金发放计划
                if ("00907000".equals(contractBusiProdPO.getBusiProdCode())) {
                    isBonus = true;
                }

                CsContractProductPO csConProductPO = new CsContractProductPO();
                csConProductPO.setPolicyId(policyId);
                csConProductPO.setOldNew(Constants.NEW);
                csConProductPO.setChangeId(csPolicyChangeBO.getChangeId());
                csConProductPO.setPolicyChgId(policyChgId);
                csConProductPO.setBusiItemId(contractBusiProdPO.getBusiItemId());
                csConProductPO.setOperationType(Constants.OPERATIONTYPE_UPDATE);
                List<CsContractProductPO> csContractProductPOs = csContractProductDao.findAllCsContractProduct(csConProductPO);
                for (CsContractProductPO csContractProductPO : csContractProductPOs) {
                    ContractProductPO contractProductPO = BeanUtils.copyProperties(ContractProductPO.class, csContractProductPO);
                    contractProductPO = contractProductDao.updateContractProduct(contractProductPO);
                    
                    //@invalid 如果保额变化，需要添加保额变更轨迹
                    this.addSaChange(csContractProductPO,paPolicyChId,csApplicationBO.getApplyTime());
                }

                // @invalid ************************** 险种下期缴费计划表回写 start ********************************/
                // 4. 查询险种下期缴费计划表
                CsContractExtendBO csContractExtendBO = new CsContractExtendBO();
                csContractExtendBO.setChangeId(csPolicyChangeBO.getChangeId());
                csContractExtendBO.setPolicyChgId(csPolicyChangeBO.getPolicyChgId());
                csContractExtendBO.setPolicyId(csPolicyChangeBO.getPolicyId());
                csContractExtendBO.setBusiItemId(contractBusiProdPO.getBusiItemId());
                csContractExtendBO.setOldNew(Constants.NEW);
                csContractExtendBO.setOperationType(Constants.OPERATIONTYPE_UPDATE);
                List<CsContractExtendBO> csContractExtendBOLists = csGeneralInfoService.findCsContractExtends(csContractExtendBO);
                for (CsContractExtendBO csContractExtBOUp : csContractExtendBOLists) {
                    paGeneralInfoService.updateContractExtend(csContractExtBOUp);
                }
                // @invalid ************************** 险种下期缴费计划表回写 end **********************************/

                CsFundServiceBO csFundServiceBO = new CsFundServiceBO();
                csFundServiceBO.setChangeId(changeId);
                csFundServiceBO.setPolicyChgId(policyChgId);
                csFundServiceBO.setPolicyId(policyId);
                csFundServiceBO.setBusiItemId(contractBusiProdPO.getBusiItemId());
                List<CsFundServiceBO> csFundServiceBOsWN = csContractInvestService.findCsFundService(csFundServiceBO);
               
                    boolean flagWN;//  @invalid 如果把数据回写到保单表里则更新
                    for (CsFundServiceBO csFSUpdate : csFundServiceBOsWN) {
                        Date applyTime = csApplicationBO.getApplyTime();
                    	// TC30621 T_FUND_TRANS中deal_time字段应取值为保费到账日的次日零时
                    	PremArapPO premArap=new PremArapPO();
                    	premArap.setBusinessCode(csFSUpdate.getAcceptCode());
                    	premArap.setPolicyCode(contractBusiProdPO.getPolicyCode());
                    	premArap.setBusiItemId(csFSUpdate.getBusiItemId());
                    	List<PremArapPO> findAllPremArapList = premArapDao.findAllPremArap(premArap);
                    	
                    	if(CollectionUtilEx.isNotEmpty(findAllPremArapList)){
                    		for (PremArapPO premArapPO2 : findAllPremArapList) {
                    			if(premArapPO2.getFinishTime()!=null){
                    				applyTime = DateUtilsEx.addDay(premArapPO2.getFinishTime(), 1);
                    			}
							}
                    	}
                    	logger.info("{}==此受理号==进入交易账户时间为===={}",acceptCode,DateUtilsEx.formatToString(applyTime, "yyyy-MM-dd"));
                        
                        if (isAddDelay.compareTo(BigDecimal.ONE) == 0) {
                            HashMap<String, Object> map = new HashMap<String, Object>();
                            map.put("accept_Id", csAcceptChangeBO.getAcceptId());
                            HashMap<String, Object> mapp = csEndorseAMService.queryRBLogHistory(map);
                            if (mapp.get("FINISH_TIME") != null) {
                                applyTime = DateUtilsEx.formatToDate((String) mapp.get("FINISH_TIME"), "yyyy-MM-dd");
                            }
                        }

                        csFSUpdate.setApplyTime(applyTime);
                        csFSUpdate.setAcceptCode(csAcceptChangeBO.getAcceptCode());
                        flagWN = pasIAS.saveContractInvestInfo(csFSUpdate, paPolicyChId);

                        if (flagWN) {
                            csFSUpdate.setValidateFlag(Constants.YES_NO__YES);
                            csContractInvestService.updateCsFundService(csFSUpdate);
                        }
                    }
                
                // @invalid ************************** 保单投资连结表回写 start ********************************/
                    CsContractInvestBO csContractInvestBO = new CsContractInvestBO();
                    csContractInvestBO.setChangeId(changeId);
                    csContractInvestBO.setPolicyChgId(policyChgId);
                    csContractInvestBO.setPolicyId(policyId);
                    csContractInvestBO.setBusiItemId(contractBusiProdPO.getBusiItemId());
                    csContractInvestBO.setOldNew(Constants.NEW);
                    List<CsContractInvestBO> csContractInvestBOs = csContractInvestService.findCsContractInvestBOs(csContractInvestBO);
    				for (CsContractInvestBO tmpBO : csContractInvestBOs) {
    					CsContractInvestPO csContractInvestOldPO = new CsContractInvestPO();
    					csContractInvestOldPO.setPolicyId(policyId);
    					csContractInvestOldPO.setPolicyChgId(policyChgId);
    					csContractInvestOldPO.setBusiItemId(tmpBO.getBusiItemId());
    					csContractInvestOldPO.setListId(tmpBO.getListId());
    					csContractInvestOldPO.setOldNew(Constants.OLD);
    					csContractInvestOldPO = csContractInvestDao.findCsContractInvest(csContractInvestOldPO);
    					ContractInvestPO temPO = BeanUtils.copyProperties(ContractInvestPO.class, tmpBO);
    					Boolean flag = false;
    					//特殊情况处理，针对追加保费之前做过投资账户转换，导致账户金额为0情况处理
    					FundTransPO fundTransPO = new FundTransPO();
    					fundTransPO.setListId(tmpBO.getListId());
    					fundTransPO.setTransType(new BigDecimal(2));
    					int total = fundTransDao.findFundTransTotal(fundTransPO);	
    					if ((ConstantsBusiProdCode.FUND_CODE_LIST_WJ.contains(tmpBO.getAccountCode())
    							|| ConstantsBusiProdCode.FUND_CODE_LIST_JJ.contains(tmpBO.getAccountCode()))
    							&& csContractInvestOldPO.getInterestCapital().compareTo(BigDecimal.ZERO) == 0
    							&& tmpBO.getInterestCapital().compareTo(BigDecimal.ZERO) == 1 && total==0) {
    						flag = true;
    					}
    					if (Constants.OPERATIONTYPE_UPDATE.equals(tmpBO.getOperationType())) {
    						if (flag) {
    							temPO.setCreateDate(csAcceptChangeBO.getValidateTime());
    						}
    						contractInvestDao.updateContractInvest(temPO);
    					}
    				}
                // @invalid ************************** 保单投资连结表回写 end **********************************/
                // 险种表回写
                ContractBusiProdPO contractBusiProd = BeanUtils.copyProperties(ContractBusiProdPO.class, contractBusiProdPO);
                contractBusiProd.setDueLapseDate(null);
                contractBusiProdDao.updateContractBusiProd(contractBusiProd);
            }

            if (isBonus) {
                csEndorseAMService.createBonus(csAcceptChangeBO.getChangeId());
            }

            if (CollectionUtils.isNotEmpty(csPolicyChangeBOs)) {
                PremArapBO premArapBO = new PremArapBO();
                premArapBO.setPolicyCode(csPolicyChangeBO.getPolicyCode());
                premArapBO.setBusinessCode(csAcceptChangeBO.getAcceptCode());
                premArapBO.setDerivType("003");// @invalid  类型为续期
                premArapBO.setBusinessType("4003");
                List<PremArapBO> premArapList = paPremArapService.queryAllPaPremArap(premArapBO);
                List<String> unitNumbers = new ArrayList<String>();
                for (PremArapBO premArap : premArapList) {
                    if (!"01".equals(premArap.getFeeStatus())) {
                        unitNumbers.add(premArap.getUnitNumber());
                    }
                }

                // 5.查询PremArap里的数据用保全的受理号
                PremArapBO csPremArapBO = new PremArapBO();
                csPremArapBO.setBusinessCode(csAcceptChangeBO.getAcceptCode());
                csPremArapBO.setDerivType("003");//  @invalid 类型为续期
                List<PremArapBO> csPremArapList = paPremArapService.queryAllPaPremArap(csPremArapBO);
                if (csPremArapList != null && csPremArapList.size() > 0) {
                    pasIAS.deletePremArap(unitNumbers, csPremArapList.get(0).getUnitNumber(), true, csPolicyChangeBO.getPolicyCode());
                }
            }
            super.addCsTrustSubmitTask(csAcceptChangeBO, csPolicyChangeBO);
        }
        for (CsPolicyChangeBO csPolicyChangeBO : csPolicyChangeBOs) {
        	BigDecimal policyChgId = csPolicyChangeBO.getPolicyChgId();
            BigDecimal policyId = csPolicyChangeBO.getPolicyId();
        	/************************** 回写保全修改数据到保单表 start 1212********************************/
            logger.info("开始更新保单主表中的营销自互保件，参数 policyCode:{},acceptId:{}",csPolicyChangeBO.getPolicyCode(),csPolicyChangeBO.getAcceptId());
			// 1.查询保单主表信息(抄单)
			CsContractMasterBO csContractMasterBO = new CsContractMasterBO();
			csContractMasterBO.setOldNew(Constants.NEW);
			csContractMasterBO.setOperationType(Constants.OPERATIONTYPE_UPDATE);
			csContractMasterBO.setPolicyChgId(policyChgId);
			CsContractMasterBO csContractMasterBONew = csGeneralInfoService.findCsContractMaster(csContractMasterBO);
			if(null != csContractMasterBONew.getPolicyId()){
				logger.info("更新保单主表中的营销自互保件，参数 policyCode:{},acceptId:{},isChannelSelfInsured:{},isChannelMutualInsured:{}",csPolicyChangeBO.getPolicyCode(),csPolicyChangeBO.getAcceptId(),csContractMasterBONew.getIsChannelSelfInsured(),csContractMasterBONew.getIsChannelMutualInsured());
				paGeneralInfoService.updateContractMaster(csContractMasterBONew);
			}
			/************************** 回写保全修改数据到保单表 end 1212********************************/
        }

        csEffectBO.setSucceed(true);
        return csEffectBO;
    }

    /**
     * 
     * @description 查询投资组合账户比例
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @see com.nci.tunan.cs.impl.csEffect.service.impl.CsEffectServiceImpl#printCsEndorsement(com.nci.tunan.cs.model.bo.CsAcceptChangeBO)
     * @param policyId 保单id
     * @param busiItemId 险种id
     * @return
     */
    private Map<String, BigDecimal> queryAssignRate(BigDecimal policyId,BigDecimal busiItemId) {        
        ContractInvestRatePO conInvestRatePO=new ContractInvestRatePO();
        conInvestRatePO.setPolicyId(policyId);
        conInvestRatePO.setBusiItemId(busiItemId);
        List<ContractInvestRatePO> csContractInvestRatePOs=contractInvestRateDao.findAllContractInvestRate(conInvestRatePO);
        BigDecimal assignRate1=BigDecimal.ZERO;
        BigDecimal assignRate2=BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(csContractInvestRatePOs)) {
            for (ContractInvestRatePO conInvestRate : csContractInvestRatePOs) {
                if (ConstantsBusiProdCode.FUND_CODE_LIST_WJ.contains(conInvestRate.getAccountCode())) {
                    assignRate1 = conInvestRate.getAssignRate();//稳健回报型投资组合账户 
                    continue;
                }
                if (ConstantsBusiProdCode.FUND_CODE_LIST_JJ.contains(conInvestRate.getAccountCode())) {
                    assignRate2 = conInvestRate.getAssignRate();// 积极进取型投资组合账户
                    continue;
                }
            }
        }
        Map<String,BigDecimal> rateMap=new HashMap<String, BigDecimal>();
        rateMap.put("assignRate1", assignRate1);
        rateMap.put("assignRate2", assignRate2);
        return rateMap;
    }

    /**
     * 
     * @description 打印批单
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @see com.nci.tunan.cs.impl.csEffect.service.impl.CsEffectServiceImpl#printCsEndorsement(com.nci.tunan.cs.model.bo.CsAcceptChangeBO)
     * @param csAcceptChangeBO 受理信息
     * @return
     */
    public CsEffectBO printCsEndorsement(CsAcceptChangeBO csAcceptChangeBO) {
        CsEffectBO csEffectBO = new CsEffectBO();

        List<CsPolicyChangeBO> csPolicyChangeBOs = csMainStreamService.findCsPolicyChanges(csAcceptChangeBO);
        // 1. 循环查询保全 变更的责任组保额
        for (CsPolicyChangeBO csPolicyChangeBO : csPolicyChangeBOs) {

            // 2. 调用打印公共类的初始化报文方法（生成公共的报文数据）
            Document csPrintXmlDocument = csPrintEndorseService.initPrintElement(csAcceptChangeBO.getAcceptCode(),
                    csPolicyChangeBO.getPolicyCode());
            Element eltRoot = csPrintXmlDocument.getRootElement();
            Element policyDataElement = eltRoot.element("DATASET").element("DATA");
            // 3.拼接自己的业务数据 开始
            Element cUSItemNCodeElement = policyDataElement.addElement("CUSItemCode");
            cUSItemNCodeElement.setText("AM"); //  @invalid ServiceCode
            Element cUSItemNameElement = policyDataElement.addElement("CUSItemName");
            cUSItemNameElement.setText("追加保费"); //  @invalid 保全项名称
            Element cUSEdorInfoElement = policyDataElement.addElement("CUSEdorInfo");
            Element edorList39Element = cUSEdorInfoElement.addElement("EdorList39");
            Element addPremiumInfoElement = edorList39Element.addElement("AddPremiumInfo");
            
            BigDecimal changeId = csPolicyChangeBO.getChangeId();
            BigDecimal policyChgId = csPolicyChangeBO.getPolicyChgId();
            BigDecimal policyId = csPolicyChangeBO.getPolicyId();

            
            // 4.查询获得 险种列表信息
            CsContractBusiProdPO csContractBusiProdPO = new CsContractBusiProdPO();
            csContractBusiProdPO.setChangeId(changeId);
            csContractBusiProdPO.setPolicyChgId(policyChgId);
            csContractBusiProdPO.setPolicyId(policyId);
            csContractBusiProdPO.setOldNew(Constants.NEW);
            List<CsContractBusiProdPO> csContractBusiProdList = csContractBusiProdDao.findAMCsContractBusiProd(csContractBusiProdPO);
            for (CsContractBusiProdPO contractBusiProdPO : csContractBusiProdList) {

                BigDecimal newAddPrem = BigDecimal.ZERO;
                BigDecimal oldAddPrem = BigDecimal.ZERO;
                BigDecimal stdPremAf = BigDecimal.ZERO;

                CsContractProductPO csConProductBO = new CsContractProductPO();
                csConProductBO.setPolicyId(policyId);                
                csConProductBO.setChangeId(csPolicyChangeBO.getChangeId());
                csConProductBO.setPolicyChgId(policyChgId);
                csConProductBO.setBusiItemId(contractBusiProdPO.getBusiItemId());
                List<CsContractProductPO> csConProductNewList = csContractProductDao.findAllCsContractProduct(csConProductBO);
                for (CsContractProductPO csConProduct : csConProductNewList) {
                    if (Constants.OLD.equals(csConProduct.getOldNew())) {
                        oldAddPrem = csConProduct.getAppendPremAf();//  @invalid 追加保费合计
                    } else if (Constants.NEW.equals(csConProduct.getOldNew())) {
                        newAddPrem = csConProduct.getAppendPremAf();// @invalid  追加保费合计
                        stdPremAf = csConProduct.getStdPremAf();
                    }
                }

                // @invalid ** 查询下期应缴日 */
                CsContractExtendPO csConExtendPO = new CsContractExtendPO();
                csConExtendPO.setPolicyChgId(policyChgId);
                csConExtendPO.setPolicyId(policyId);
                csConExtendPO.setBusiItemId(contractBusiProdPO.getBusiItemId());
                csConExtendPO.setOldNew(Constants.NEW);
                csConExtendPO = csContractExtendDao.queryNextPayDueDate(csConExtendPO);
                Date payDueDate = csConExtendPO.getPayDueDate();

                //  @invalid 险种名称
                String productName = findProductName(contractBusiProdPO.getBusiPrdId());
                BigDecimal curAddPrem = BigDecimal.ZERO;
                if(newAddPrem!=null){
                    curAddPrem= newAddPrem.subtract(oldAddPrem==null?BigDecimal.ZERO:oldAddPrem);//  @invalid 本次追加保费
                }
                
                Element addPremiumListElement = addPremiumInfoElement.addElement("AddPremiumList");

                Element productNameElement = addPremiumListElement.addElement("ProductName");
                productNameElement.setText(productName == null ? "" : productName);

                Element addPremiumMoneyElement = addPremiumListElement.addElement("AddPremiumMoney");
                addPremiumMoneyElement.setText(curAddPrem.toString()); //  @invalid 本次追加保费
                // 930产品验收发现基线中批单如果没有续期保费,批单中续期保险费不应显示
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                boolean flag = false;
                try {
					Date date1 = sdf.parse("9999-12-31");
					if(payDueDate != null && date1.compareTo(payDueDate) == 0){
						flag = true;
					}
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
					throw new BizException(e.getMessage());
				}
                if(flag){
                	Element RNPreminumElement = addPremiumListElement.addElement("RNPremium");//  @invalid 续期保费
                	RNPreminumElement.setText("");
                }else {
                	Element RNPreminumElement = addPremiumListElement.addElement("RNPremium");//  @invalid 续期保费
                	RNPreminumElement.setText(stdPremAf.toString());
				}

                Element nextPayDateElement = addPremiumListElement.addElement("NextPayDate");//  @invalid 下期应缴日
                nextPayDateElement.setText(DateUtilsEx.formatToString(payDueDate, "yyyy年MM月dd日"));

                if (ConstantsBusiProdCode.BUSI_CODE_LIST_928.contains(contractBusiProdPO.getBusiProdCode()) 
                		&& !ConstantsBusiProdCode.BUSI_CODE_LIST_Z01.contains(contractBusiProdPO.getBusiProdCode())) {
                    
                    CsFundServicePO csFundServicePO = new CsFundServicePO();
                    csFundServicePO.setPolicyChgId(contractBusiProdPO.getPolicyChgId());
                    csFundServicePO.setPolicyId(contractBusiProdPO.getPolicyId());
                    csFundServicePO.setBusiItemId(contractBusiProdPO.getBusiItemId());
                    csFundServicePO = csFundServiceDao.queryFundServiceFor928(csFundServicePO);

                    Element TypeOfPaymentElement = addPremiumInfoElement.addElement("TypeOfPayment");//  @invalid 续期保费
                    int pay_times = csFundServicePO.get("pay_times") == null ? 0 : Integer.parseInt(csFundServicePO.get("pay_times")
                            .toString());
                    if (pay_times > 0) {
                        TypeOfPaymentElement.setText("1");// @invalid  补交
                    } else {
                        TypeOfPaymentElement.setText("2");//  @invalid 不定期交
                    }
                    
                    //  @invalid 初始费用
                    String InitialCost = csFundServicePO.get("cost_fee") == null ? "" : csFundServicePO.get("cost_fee").toString();
                    //  @invalid 补交保费期数
                    String MakeupPremiumNo = csFundServicePO.get("pay_times") == null ? "" : csFundServicePO.get("pay_times").toString();
                    //  @invalid 累计交费期数
                    String AddPayMoneyNo = csFundServicePO.get("policy_period") == null ? "" : csFundServicePO.get("policy_period")
                            .toString();
                    //  @invalid 补交保费金额
                    String MakeupPremium = csFundServicePO.get("prem_total") == null ? "" : csFundServicePO.get("prem_total").toString();
                    //  @invalid 不定期交保费金额
                    String IrregularPremium = csFundServicePO.get("irregular_prem") == null ? "" : csFundServicePO.get("irregular_prem")
                            .toString();
                    //  @invalid 进入稳健回报型投资组合的保险费
                    String ReturnPremium = csFundServicePO.get("add_prem1") == null ? "" : csFundServicePO.get("add_prem1").toString();
                    //  @invalid 进入积极进取型投资组合时保险费
                    String PositivePremium = csFundServicePO.get("add_prem2") == null ? "" : csFundServicePO.get("add_prem2").toString();

                    BigDecimal PayAmount = BigDecimal.ZERO; //  @invalid 不定期交与补交保费总额
                    if (!StringUtilsEx.isNullOrEmpty(MakeupPremium)) {
                        PayAmount = PayAmount.add(new BigDecimal(MakeupPremium));
                    }
                    if (!StringUtilsEx.isNullOrEmpty(IrregularPremium)) {
                        PayAmount = PayAmount.add(new BigDecimal(IrregularPremium));
                    }

                    cUSItemNameElement.setText("不定期交与补交保费"); //  @invalid 保全项名称
                    Element PayAmountElement = addPremiumListElement.addElement("PayAmount");
                    PayAmountElement.setText(PayAmount.toString());

                    Element InitialCostElement = addPremiumListElement.addElement("InitialCost");
                    InitialCostElement.setText(InitialCost);

                    Element MakeupPremiumNoElement = addPremiumListElement.addElement("MakeupPremiumNo");
                    MakeupPremiumNoElement.setText(MakeupPremiumNo);

                    Element AddPayMoneyNoElement = addPremiumListElement.addElement("AddPayMoneyNo");
                    AddPayMoneyNoElement.setText(AddPayMoneyNo);

                    Element MakeupPremiumElement = addPremiumListElement.addElement("MakeupPremium");
                    MakeupPremiumElement.setText(MakeupPremium);

                    Element IrregularPremiumElement = addPremiumListElement.addElement("IrregularPremium");
                    IrregularPremiumElement.setText(IrregularPremium);

                    Element ReturnPremiumElement = addPremiumListElement.addElement("ReturnPremium");
                    ReturnPremiumElement.setText(ReturnPremium);

                    Element PositivePremiumElement = addPremiumListElement.addElement("PositivePremium");
                    PositivePremiumElement.setText(PositivePremium);
                }else if(ConstantsBusiProdCode.BUSI_CODE_LIST_Z01.contains(contractBusiProdPO.getBusiProdCode())){
                    CsFundServicePO csFundServicePO = new CsFundServicePO();
                    csFundServicePO.setPolicyChgId(contractBusiProdPO.getPolicyChgId());
                    csFundServicePO.setPolicyId(contractBusiProdPO.getPolicyId());
                    csFundServicePO.setBusiItemId(contractBusiProdPO.getBusiItemId());
                    csFundServicePO = csFundServiceDao.queryFundServiceForZ01(csFundServicePO);
                    
                    //进入稳健回报型投资组合的保险费
                    BigDecimal ReturnPremium = BigDecimal.ZERO;
                    //进入积极进取型投资组合时保险费
                    BigDecimal PositivePremium = BigDecimal.ZERO;
                    
                    //@invalid 初始费用
                    if(csFundServicePO.get("cost_fee") != null){
                        Element InitialCostElement = addPremiumListElement.addElement("InitialCost");
                        InitialCostElement.setText(csFundServicePO.get("cost_fee").toString());
                    }
                    //@invalid 进入稳健回报型投资组合的保险费
					if(csFundServicePO.get("add_prem1") != null){
						ReturnPremium = (BigDecimal)csFundServicePO.get("add_prem1");
	                    Element ReturnPremiumElement = addPremiumListElement.addElement("ReturnPremium");
	                    ReturnPremiumElement.setText(ReturnPremium.toString());
                    }
					//@invalid 进入积极进取型投资组合时保险费
					if(csFundServicePO.get("add_prem2") != null){
						PositivePremium = (BigDecimal)csFundServicePO.get("add_prem2");
	                    Element PositivePremiumElement = addPremiumListElement.addElement("PositivePremium");
	                    PositivePremiumElement.setText(PositivePremium.toString());
					}
                }
                
                
            }

            //  @invalid 调用打印系统打印批单
            if (!csPrintEndorseService.callPrintInterface(csPrintXmlDocument, csAcceptChangeBO.getAcceptCode(),
                    csPolicyChangeBO.getPolicyCode())) {
                csEffectBO.setSucceed(false);
                return csEffectBO;
            }
        }

        csEffectBO.setSucceed(true);
        return csEffectBO;
    }
    
    
    /**
     * 
     * @description 添加保额变更记录
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param newCsConProduct csConProduct_new
     * @param paPolicyChId paPolicyChId
     * @param applyDate applyDate
     */
    private void addSaChange(CsContractProductPO newCsConProduct,BigDecimal paPolicyChId,Date applyDate) {
        CsContractProductPO csContractProductOld = new CsContractProductPO();
        csContractProductOld.setChangeId(newCsConProduct.getChangeId());
        csContractProductOld.setItemId(newCsConProduct.getItemId());
        csContractProductOld.setPolicyChgId(newCsConProduct.getPolicyChgId());
        csContractProductOld.setOldNew(Constants.OLD);
        csContractProductOld = csContractProductDao.findCsContractProduct(csContractProductOld);
        if(newCsConProduct.getAmount().compareTo(csContractProductOld.getAmount()) != Constants.ZERO_INT){         
            SaChangePO addSaChangePO = new SaChangePO();
            addSaChangePO.setItemId(newCsConProduct.getItemId());
            addSaChangePO.setPolicyId(newCsConProduct.getPolicyId());
            addSaChangePO.setPolicyChgId(paPolicyChId);
            addSaChangePO.setSa(newCsConProduct.getAmount().subtract(csContractProductOld.getAmount()));
            addSaChangePO.setStartDate(applyDate);
            saChangeDao.addSaChange(addSaChangePO);
        }

    }
}
