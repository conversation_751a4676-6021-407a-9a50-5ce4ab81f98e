package com.nci.tunan.cs.impl.peripheral.ucc.r00102001669;

import com.nci.tunan.cs.interfaces.peripheral.exports.r00102001669.vo.Input;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102001669.vo.Output;

/**
 * 
 * @description   万能险、投连险部分领取Action
 * <AUTHOR>  <EMAIL>
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2018年3月22日 下午5:00:11
 */
public interface IEdorTypePGSubmitUcc {

	/**
	 * 
	 * @description 提交PG
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>  <EMAIL> 
	 * @param input input
	 * @return Output
	 */
	public Output submitEdorTypePG(Input input);
}
