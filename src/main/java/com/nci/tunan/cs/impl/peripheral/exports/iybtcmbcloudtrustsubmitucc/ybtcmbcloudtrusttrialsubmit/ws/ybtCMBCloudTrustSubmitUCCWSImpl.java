package com.nci.tunan.cs.impl.peripheral.exports.iybtcmbcloudtrustsubmitucc.ybtcmbcloudtrusttrialsubmit.ws;

import com.nci.tunan.cs.impl.ybtcmbcloudtrustsubmit.ucc.IybtCMBCloudTrustSubmitUCC;
import com.nci.tunan.cs.interfaces.ybtcmbcloudtrustsubmit.exports.iybtcmbcloudtrustsubmitucc.ybtcmbcloudtrusttrialsubmit.SrvReqBody;
import com.nci.tunan.cs.interfaces.ybtcmbcloudtrustsubmit.exports.iybtcmbcloudtrustsubmitucc.ybtcmbcloudtrusttrialsubmit.SrvResBizBody;
import com.nci.tunan.cs.interfaces.ybtcmbcloudtrustsubmit.exports.iybtcmbcloudtrustsubmitucc.ybtcmbcloudtrusttrialsubmit.SrvResBody;
import com.nci.tunan.cs.interfaces.ybtcmbcloudtrustsubmit.exports.iybtcmbcloudtrustsubmitucc.ybtcmbcloudtrusttrialsubmit.ws.IybtCMBCloudTrustSubmitUCCWS;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import com.nci.udmp.component.aop.TablePrevThreadLocal;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import org.slf4j.Logger;

import javax.jws.WebService;
import javax.xml.ws.Holder;
import java.util.Map;

@WebService(endpointInterface = "com.nci.tunan.cs.interfaces.ybtcmbcloudtrustsubmit.exports.iybtcmbcloudtrustsubmitucc.ybtcmbcloudtrusttrialsubmit.ws.IybtCMBCloudTrustSubmitUCCWS", serviceName = "ybtCMBCloudTrustSubmitUCCWSImplybtCMBCloudTrustTrialsubmit")
public class ybtCMBCloudTrustSubmitUCCWSImpl implements IybtCMBCloudTrustSubmitUCCWS {

    /** 
     * @Fields logger : 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();
    
    private IybtCMBCloudTrustSubmitUCC ucc = null;
    
    public IybtCMBCloudTrustSubmitUCC getUcc() {
        return ucc;
    }
    public void setUcc(IybtCMBCloudTrustSubmitUCC ucc) {
        this.ucc = ucc;
    }
    
    @Override
    public void ybtCMBCloudTrustTrialsubmit(SysHeader parametersReqHeader, SrvReqBody parametersReqBody, 
            Holder<SysHeader> parametersResHeader, Holder<SrvResBody> parametersResBody) {
        TablePrevThreadLocal.setTABLEPREV("$tablePrev");
        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        String dealTime = DateUtilsEx.getTodayTime();
        String systemName = StringUtilsEx.getStr("com.nci.tunan.cs.impl.ybtcmbcloudtrustsubmit.exports.iybtcmbcloudtrustsubmitucc.ybtcmbcloudtrusttrialsubmit.ws", 4, ".");
        String dealNo = systemName + "_" + "IybtCMBCloudTrustSubmitUCC" + "_" + "ybtCMBCloudTrustTrialsubmit";
        if (dealSwitch) {
            logger.debug("开始记录交易请求日志");
//            CommonDealManagement.beforeCommonDealManagement(parametersReqHeader, parametersReqBody.getBizHeader(),
//                parametersReqBody.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime);
        }
        CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
                CommonHeaderDeal.setBIZHEADERTHREAD(parametersReqBody.getBizHeader());
                com.nci.tunan.cs.interfaces.ybtcmbcloudtrustsubmit.vo.inputData inputVO = parametersReqBody.getBizBody().getInputData();
        try {
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(parametersReqHeader));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersReqBody.getBizHeader()));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersReqBody.getBizBody()));
        } catch (Exception e1) {
            e1.printStackTrace();
            logger.debug("解析调用前报文的JSON字符串发生异常");
            throw new RuntimeException(e1);
        }
        SrvResBody srvResBody = new SrvResBody();
        SysHeader sysHeader = new SysHeader();
        String dealStatus = "2";
        try {
            com.nci.tunan.cs.interfaces.ybtcmbcloudtrustsubmit.vo.outputData output = ucc.ybtCMBCloudTrustTrialsubmit(inputVO);
            sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
             BizHeader  bizHeader = CommonHeaderDeal.dealBizHeader(parametersReqBody.getBizHeader());
            SrvResBizBody bizResBody = new SrvResBizBody();
            bizResBody.setOutputData(output);
            srvResBody.setBizBody(bizResBody);
            srvResBody.setBizHeader(bizHeader);
            parametersResHeader.value = sysHeader;
            parametersResBody.value = srvResBody;
        } catch (Exception e2) {
            dealStatus = "3";
        	logger.error("调用接口过程中产生异常!",e2);
            throw new RuntimeException(e2);
        } finally{
            if (dealSwitch) {
            	logger.debug("开始记录交易响应日志");
//                CommonDealManagement.afterCommonDealManagement(sysHeader,
//                        srvResBody.getBizHeader(), srvResBody.getBizBody(), DealTrigger.WEBSERVICE, dealNo, dealTime,dealStatus);
            }    
        }
        try {
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(parametersResHeader.value));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersResBody.value.getBizHeader()));
            logger.debug(" WebService返回结果的消息id：" + parametersResHeader.value.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersResBody.value.getBizBody()));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析调用后报文的JSON字符串发生异常");
            throw new RuntimeException(e3);
        } 
    }
}
 

