package com.nci.tunan.cs.impl.csItem.service;

import java.math.BigDecimal;
import java.util.List;

import com.nci.tunan.cs.model.bo.CsBankAccountBO;
import com.nci.tunan.cs.model.bo.CsContractExtendBO;
import com.nci.tunan.cs.model.bo.CsContractMasterBO;
import com.nci.tunan.cs.model.bo.CsContractProductBO;
import com.nci.tunan.cs.model.bo.CsCustomerBO;
import com.nci.tunan.cs.model.bo.CsInsuredListBO;
import com.nci.tunan.cs.model.bo.CsPayerAccountBO;
import com.nci.tunan.cs.model.bo.CsPayerBO;
import com.nci.tunan.cs.model.bo.CsPolicyChangeBO;
import com.nci.tunan.cs.model.po.CsBankAccountPO;
import com.nci.tunan.cs.model.vo.CsPayerAccountVO;
import com.nci.tunan.pa.interfaces.model.bo.BankBO;

/**
 * 
 * @description 交费信息变更Service
 * <AUTHOR> <EMAIL> 
 * @date 2015年05月15日 下午4:39:40 
 * @.belongToModule 保全 保全子系统
 */
public interface ICsEndorsePCService extends ICSItemBaseService{

	/**
	 * 
	 * @description 查询客户下的账户信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param param csBankAccountBO
	 * @return
	 */
	public List<CsBankAccountBO> findCsBankAccountBOs(
			CsBankAccountBO csBankAccountBO);

	/**
	 * 
	 * @description 查询银行信息单条
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param param csBankAccountBO
	 * @return
	 */
	public CsBankAccountPO findCsBankAccount(CsBankAccountBO csBankAccountBO );
	/**
	 * 
	 * @description 保存新增账户信息
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param param csBankAccountBOs
	 * @return
	 */
	public boolean saveCsBankAccountBaseInfo(List<CsBankAccountBO> csBankAccountBOs);
	
	/**
	 * 
	 * @description 查询保单付款人表
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param param csPayerBO
	 * @return
	 */
	public CsPayerBO findCsPayerBO(
			CsPayerBO csPayerBO);
	/**
	 * 
	 * @description 查询付款账户信息
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param param csPayerAccountBO
	 * @return
	 */
	public List<CsPayerAccountBO> findCsPayerAccountBOs(
			CsPayerAccountBO csPayerAccountBO);
	
	/**
	 * 
	 * @description 查询险种责任组信息
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param param csContractProductBO
	 * @return
	 */
	public List<CsContractProductBO> findCsContractProductBOs(
			CsContractProductBO csContractProductBO);
	
	/**
	 * 
	 * @description 查询险种下期缴费计划信息
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param param csContractExtendBO
	 * @return
	 */
	public List<CsContractExtendBO> findCsContractExtendBOs(
			CsContractExtendBO csContractExtendBO);
	
	
	/**
	 * 
	 * @description	保存变更的交费信息
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param param csPayerAccountBOs
	 */
	public boolean saveChange(List<CsPayerAccountBO> csPayerAccountBOs);
	/**
	 * 
	 * @description 修改保单账户信息
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param param csPayerAccountBO
	 */
	public void updateCsPayAccount(CsPayerAccountBO csPayerAccountBO);
	/**
	 * 
	 * @description 查询新的银行信息表信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param param csBankAccountVO
	 * @return
	 */
	public List<CsBankAccountBO> queryNewCsBankAccount(CsBankAccountBO csBankAccountVO);
	/**
	 * 
	 * @description 校验银行信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param param newCsBankAccountVOList
	 * @return
	 */
	public String checkBankCodeInfo(List<CsBankAccountBO> newCsBankAccountVOList);
	/**
	 * 
	 * @description 保存银行账户信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param param customerId
	 * @param param changeId
	 * @param param acceptId
	 * @param param new1
	 * @param param newCsBankAccountVOList
	 * @param param deleteAccountIds
	 * @return
	 */
	public List<CsBankAccountBO> saveAndDeleteBankAccount(
			BigDecimal customerId, BigDecimal changeId, BigDecimal acceptId,
			String new1, List<CsBankAccountBO> newCsBankAccountVOList,
			String deleteAccountIds);
	/**
	 * 
	 * @description 查询银行账户
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param param csBankAccountVO
	 * @return
	 */
	public List<CsBankAccountBO> queryAllCsBankAccount(
			CsBankAccountBO csBankAccountVO);
	/**
	 * 
	 * @description 根据条件更新银行信息表信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param param csBankAccountVO
	 */
	public void updateCsBankAccount(CsBankAccountBO csBankAccountVO);
	/**
	 * 
	 * @description 查询保单管理机构下银行
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param param acceptId
	 * @return
	 */
	public List<BankBO> queryOrgBankCode(BigDecimal acceptId);
	/**
	 * 
	 * @description 给外围集成接口提供方法 
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param param csBankAccountBO
	 * @return
	 */
	public String savePCDataForOutter(CsBankAccountBO csBankAccountBO);
	
	public String CheckMedicaPolicy(CsPolicyChangeBO csPolicyChangeBO,CsPayerAccountVO csPayerAccountVO);

}
