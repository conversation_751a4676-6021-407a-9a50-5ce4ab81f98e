package com.nci.tunan.cs.impl.bonusdoc.ucc.impl;

import java.io.IOException;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;

import com.nci.tunan.cs.impl.bonusdoc.service.IBonusDocumentListService;
import com.nci.tunan.cs.impl.bonusdoc.ucc.IBonusDocumentListUCC;
import com.nci.tunan.cs.model.bo.CsUnivRepFileBO;
import com.nci.tunan.cs.model.vo.CsUnivRepFileVO;
import com.nci.tunan.pa.batch.bonusdoclist.service.IBonusDocListService;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.logging.LoggerFactory;
/**
 * 
 * @description  分红险红利通知书清单
 * <AUTHOR> <EMAIL> 
 * @date 2022-8-9 下午5:25:47 
 * @.belongToModule 保全子系统-分红险红利通知书清单
 */
public class BonusDocumentListUCCImpl implements IBonusDocumentListUCC {

	/**
	 * 分红险红利通知书清单Service接口
	 */
	private IBonusDocumentListService bonusDocumentListService;
	
	/** 分红险红利通知书清单Service接口 **/
	private IBonusDocListService bonusDocListService;
	
	/**
	 * 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	/**
	 * @description 分页查询分红险红利通知书清单
	 * @version
	 * @title
	 * @param currentPage 分页信息
	 * @param univRepFileVO 检索条件VO对象
	 * @return
	 */
	@Override
	public CurrentPage<CsUnivRepFileVO> queryList(CsUnivRepFileVO univRepFileVO,CurrentPage<?> currentPage) {
		logger.info("BonusDocumentListUCCImpl");
		CsUnivRepFileBO univRepFileBO =BeanUtils.copyProperties(CsUnivRepFileBO.class, univRepFileVO);
		logger.info("测试---->"+univRepFileBO.getOrganCode());
		return BeanUtils.copyCurrentPage(CsUnivRepFileVO.class, bonusDocumentListService.queryList(univRepFileBO, currentPage));
	}

	/**
	 * @description 查询单条分红险红利通知书文件数据
	 * @version
	 * @title
	 * @param vo
	 * @return 
	*/
	@Override
	public CsUnivRepFileVO querySingleBonusDocFile(CsUnivRepFileVO csUnivRepFileVO) {
		CsUnivRepFileBO csUnivRepFileBO = BeanUtils.copyProperties(CsUnivRepFileBO.class, csUnivRepFileVO);
		return BeanUtils.copyProperties(CsUnivRepFileVO.class, bonusDocumentListService.querySingleBonusDocFile(csUnivRepFileBO));
	}
	
	/**
	 * 
	 * @description 下载
	 * @version
	 * @title
	 * @param response Http输出
	 * @param filePath 文件路径
	 * @param fileName 文件名
	 * @throws IOException 
	 */
	@Override
	public void exportExcel(HttpServletResponse response, String filePath, String fileName) {
		
		ServletOutputStream fos = null;
		try {
			String newFileName = "";
			if(fileName.endsWith(".zip")){
				newFileName = fileName.split("SYS")[0]+ ".zip";
			}else{
				newFileName = fileName.split("SYS")[0].replace(".xlsx", "") + ".xlsx";
			}
			response.setContentType("text/x-msdownload");
			response.addHeader("Content-Disposition", "attachment; filename=\"" +new String(newFileName.getBytes("gb2312"),"ISO8859-1") + "\"");
			fos = response.getOutputStream(); 
			bonusDocListService.downloadFile(filePath, fileName, fos);
		} catch (Exception e) {
			logger.debug("-----【分红险红利通知书】下载失败，报错信息如下： " + e.getMessage() + "-----");
			e.printStackTrace();
			throw new RuntimeException(e);
		} finally {
			try {
				if (fos != null) {
					fos.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
				throw new RuntimeException(e);
			}
		}
	}





	public IBonusDocumentListService getBonusDocumentListService() {
		return bonusDocumentListService;
	}

	public void setBonusDocumentListService(
			IBonusDocumentListService bonusDocumentListService) {
		this.bonusDocumentListService = bonusDocumentListService;
	}

	public IBonusDocListService getBonusDocListService() {
		return bonusDocListService;
	}

	public void setBonusDocListService(IBonusDocListService bonusDocListService) {
		this.bonusDocListService = bonusDocListService;
	}



}
