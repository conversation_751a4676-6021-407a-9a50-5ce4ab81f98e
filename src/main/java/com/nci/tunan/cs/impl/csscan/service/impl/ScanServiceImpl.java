package com.nci.tunan.cs.impl.csscan.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.git.ecss.sysobject.system.BaseBusiObject;
import com.git.ecss.sysobject.system.BaseDocObject;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.nci.tunan.clm.dao.IUserDao;
import com.nci.tunan.clm.interfaces.model.po.UserPO;
import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.common.EVENT_CODE;
import com.nci.tunan.cs.dao.ICsAcceptChangeDao;
import com.nci.tunan.cs.dao.ICsApplicationDao;
import com.nci.tunan.cs.dao.IImageDao;
import com.nci.tunan.cs.dao.IImageScanDao;
import com.nci.tunan.cs.dao.IImageScanInfoDao;
import com.nci.tunan.cs.impl.csscan.service.IScanService;
import com.nci.tunan.cs.impl.priorScan.service.IPriorScanService;
import com.nci.tunan.cs.model.bo.ImageBO;
import com.nci.tunan.cs.model.bo.ImageScanBO;
import com.nci.tunan.cs.model.bo.ImageScanInfoBO;
import com.nci.tunan.cs.model.bo.ImageScanListBO;
import com.nci.tunan.cs.model.po.CsAcceptChangePO;
import com.nci.tunan.cs.model.po.CsApplicationPO;
import com.nci.tunan.cs.model.po.ImagePO;
import com.nci.tunan.cs.model.po.ImageScanInfoPO;
import com.nci.tunan.cs.model.po.ImageScanPO;
import com.nci.tunan.cs.util.CusAddOperationUtil;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.StringUtilsEx;

/**
 * 
 * @description 扫描
 * <AUTHOR> <EMAIL> 
 * @date 2015年05月15日 上午9:53:16 
 * @.belongToModule 保全子系统
 */
public class ScanServiceImpl implements IScanService {
	
	private static Logger logger=LoggerFactory.getLogger(ScanServiceImpl.class);
	/**
	 * 影像资料关联表Dao接口
	 */
	private IImageDao imageDao;
	/**
	 * 影响扫描Dao接口
	 */
	private IImageScanDao imageScanDao;
	/**
	 * 影像扫描信息表
	 */
	private IImageScanInfoDao imageScanInfoDao;
	/**
	 * ICsApplicationDao接口
	 */
	private ICsApplicationDao csApplicationDao;
	/**
	 * 事前扫描ServiceImpl
	 */
	@Autowired
	private IPriorScanService priorScanService;
	/**
	 * ICsAcceptChangeDao接口
	 */
	@Autowired
	private ICsAcceptChangeDao csAcceptChangeDao; //@invalid 受理信息
	/**
	 * IUserDao接口
	 */
	@Autowired
	private IUserDao userDao;


	/**
	 * 影像回调数据处理（每次回调传回的数据 都是 申请号下的图片全集）
	 * 数据分为3级	
	 * 	1：申请号挂单证	<T_IMAGE_SCAN> 	
	 * 	2：单证挂图片	<T_IMAGE_SCAN_INFO>
	 * 	3：受理号挂单证及图片	<T_IMAGE> 
	 * 
	 * 	受理号不为空，则调用 updateToBQ 接口  回传 图片属性 oprationStatus = 1 的数据，该属性代表图片是新增的，不支持直接受理号删除，所以此处不考虑受理号删除情况
	 * 对回调方法数据集处理
		1、根据申请号+单证类型 验证是否有值
			有：获取对象（主要是id）
			没有：新增，后获取对象（主要是id）
		2、根据operatorStatus =1 判断是否需要新增图片
		
		3、根据受理号 来判断是否 把新增图片和受理号关联，并组装 updateForBq 接口的数据集合中图片结构
	 */
	/**
	 * 
	 * @description 保存影像扫描结果
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csscan.service.IScanService#saveImageScanResult(java.util.List)
	 * @param imageScanResultBOList 影像扫描结果BOList
	 * @throws BizException
	 */
	public void saveImageScanResult(List<ImageScanListBO> imageScanResultBOList) throws BizException {
		logger.info("保存返回数据imageScanResultBOList.size()========="+imageScanResultBOList.size());
		if (imageScanResultBOList != null && imageScanResultBOList.size() > 0) {
			// 1. 通过图片名称修改保全受理号
			BaseBusiObject<BaseDocObject> o = new BaseBusiObject<BaseDocObject>();
			o.setEcmBusiCode(imageScanResultBOList.get(0).getBussCode());// @invalid
																			// 业务码
																			// --申请号
			o.setEcmBusiType(Constants.SCAN_SYSTEM_FLAG);// @invalid 业务名称
			o.setBusiOrgCode(Constants.SCAN_SYSTEM_NUM);// @invalid 组织机构代码
			// 2. 组装单证类型级别
			for (ImageScanListBO imageScanListBO : imageScanResultBOList) {
				logger.info("影像扫描结果返回数据========="+imageScanListBO.getBussCode()+"===用户："+imageScanListBO.getScanUserCode());
				imageScanListBO.setApplyCode(imageScanListBO.getBussCode());// @invalid
																			// 申请号
																			// 业务码
				// @invalid 文档对象 包含多个文档页对象（BasePageObject）
				BaseDocObject docObject = new BaseDocObject();
				docObject.setEcmCatalogCode(imageScanListBO.getDocType());// @invalid
																			// 单证类型
				// 3. 根据申请号+单证类型 验证是否有值--有：获取对象（主要是id）；没有：新增，后获取对象（主要是id）
				ImageScanBO imageScanBO = new ImageScanBO();
				// @invalid 获取事前扫描
				String IsBeforScan = String.valueOf(imageScanListBO.getResultBackMap().get("isBeforScan"));
				logger.info("影像扫描结果返回数据======="+imageScanListBO.getBussCode()+"==IsBeforScan:"+IsBeforScan);
				if ("2".equals(IsBeforScan)) {
					// @invalid 柜面互联
					imageScanListBO.setApplyCode(String.valueOf(imageScanListBO.getResultBackMap().get("applyCode")));
				}
				// @invalid 根据是否事前扫描 获取申请、受理信息
				CsAcceptChangePO csAcceptChangePO = new CsAcceptChangePO();
				CsApplicationPO application = new CsApplicationPO();
				// @invalid 事前扫描、图片扫描
				if ("1.0".equals(IsBeforScan) || "0.0".equals(IsBeforScan)) {
					if ("1.0".equals(IsBeforScan)) {
						if (null == imageScanListBO.getApplyCode() || "".equals(imageScanListBO.getApplyCode())) {
							return;
						}

						// @invalid 事前扫描 只有申请号没有受理号
						application = new CsApplicationPO();
						application.setApplyCode(imageScanListBO.getApplyCode());
						application = csApplicationDao.findCsApplication(application);
						imageScanBO.setApplyCode(application.getApplyCode());// @invalid
																				// 申请号

					} else if ("0.0".equals(IsBeforScan)) {
						// @invalid 扫描影像
						if (null == imageScanListBO.getBussCode() || "".equals(imageScanListBO.getBussCode())) {
							return;
						}
						csAcceptChangePO.setAcceptCode(imageScanListBO.getBussCode());
						csAcceptChangePO = csAcceptChangeDao.findCsAcceptChange(csAcceptChangePO);
						application.setChangeId(csAcceptChangePO.getChangeId());
						application = csApplicationDao.findCsApplicationByChangeId(application);
						imageScanBO.setApplyCode(application.getApplyCode());
					}

					imageScanBO.setDocType(imageScanListBO.getDocType());// @invalid
																			// 应备资料类型
					ImageScanPO imageScanPO = new ImageScanPO();
					Map resultBackMap = imageScanListBO.getResultBackMap();
					// @invalid 添加changeID字段，后续  保全扫描清单查询   的时候使用。
					if(null != application.getChangeId()){
						imageScanListBO.setChangeId(application.getChangeId());
					}
					imageScanPO = updateImageScan(imageScanListBO, resultBackMap);// @invalid
																					// 更新imageScan信息
					imageScanBO = BeanUtils.copyProperties(ImageScanBO.class, imageScanPO);
					// @invalid resultBackMap处理

					String imagePageInfos = String.valueOf(resultBackMap.get("imagePageInfos"));
					Gson gson = new GsonBuilder().setFieldNamingPolicy(FieldNamingPolicy.UPPER_CAMEL_CASE).create();
					List imagePageInfoList = gson.fromJson(imagePageInfos, ArrayList.class);

					// @invalid 组装图片级别
					for (int i = 0; i < imagePageInfoList.size(); i++) {
						Map imagePageInfo = new HashMap();
						imagePageInfo = (Map) imagePageInfoList.get(i);
						// 4.
						// 影像文件操作类型，保全受理号补扫时使用，1--新增，0--其他，保全根据该字段判断当前新增的影像，再调用保全受理号修改接口
						String oprationStatus = String.valueOf(imagePageInfo.get("oprationStatus"));
						logger.info("影像扫描结果返回数据========="+imageScanListBO.getBussCode()+"==oprationStatus:"+oprationStatus+"==IsBeforScan:"+IsBeforScan);
						ImageScanInfoPO imageScanInfoPO = updateImageScanInfo(imagePageInfo, imageScanListBO,imageScanPO);// 处理imageScanInfo表中的数据
						// @invalid 新增图片
						if ("1.0".equals(oprationStatus) || "1".equals(oprationStatus)) {
							ImageBO imageBO = new ImageBO();
							BigDecimal userId = BigDecimal.ZERO;
							// @invalid RM:46090获取扫描用户的用户ID Start
							if(!StringUtilsEx.isBlank(imageScanListBO.getScanUserCode())){
								UserPO tUser =new UserPO();
								tUser.setUserName(imageScanListBO.getScanUserCode());
								tUser=userDao.findUser(tUser);
								userId=tUser.getUserId();
							}
							// @invalid 根据事前扫描判断是受理号还是申请号
							if ("1.0".equals(IsBeforScan)) {
								imageBO.setApplyCode(application.getApplyCode());
								CusAddOperationUtil.setCusAddOperationUtil().addbizOperation(application.getApplyCode(),
										EVENT_CODE.EVENT_CODE_10303, null, userId);
							} else if ("0.0".equals(IsBeforScan)) {
								imageBO.setAcceptCode(csAcceptChangePO.getAcceptCode());
								imageBO.setApplyCode(application.getApplyCode());
								CusAddOperationUtil.setCusAddOperationUtil().addbizOperation(
										csAcceptChangePO.getAcceptCode(), EVENT_CODE.EVENT_CODE_10303, null, userId);
							}

							imageBO.setImageScanId(imageScanBO.getImageScanId());
							imageBO.setChangeId(application.getChangeId());

							imageBO.setImageScanInfoId(imageScanInfoPO.getImageScanInfoId());// @invalid
																								// 主键
							ImagePO imagePO = BeanUtils.copyProperties(ImagePO.class, imageBO);
							// @invalid 录入 <T_IMAGE> 数据,关联受理关系
							imagePO = imageDao.addImage(imagePO);
						}
					}
					// 5. 互动联勤 申请号下影响拷贝到受理号下
				} else if ("2".equals(IsBeforScan)) { // @invalid 申请、受理信息
					csAcceptChangePO.setAcceptCode(imageScanListBO.getBussCode());
					csAcceptChangePO = csAcceptChangeDao.findCsAcceptChange(csAcceptChangePO);
					application.setChangeId(csAcceptChangePO.getChangeId());
					application = csApplicationDao.findCsApplicationByChangeId(application);
					// @invalid 图片扫描
					ImageScanPO imageScanPO = new ImageScanPO();
					imageScanPO.setApplyCode(application.getApplyCode());
					imageScanPO.setDocType(imageScanListBO.getDocType());
					imageScanPO = imageScanDao.findImageScan(imageScanPO);

					// @invalid 图片扫描信息
					ImageScanInfoPO imageScanInfoPO = new ImageScanInfoPO();
					imageScanInfoPO.setApplyCode(imageScanListBO.getApplyCode());
					List<ImageScanInfoPO> imageScanInfoPOList = imageScanInfoDao.findAllImageScanInfo(imageScanInfoPO);
					for (ImageScanInfoPO isinfoPO : imageScanInfoPOList) {
						ImageBO imageBO = new ImageBO();
						imageBO.setAcceptCode(csAcceptChangePO.getAcceptCode());
						imageBO.setApplyCode(application.getApplyCode());
						imageBO.setImageScanId(imageScanPO.getImageScanId());
						imageBO.setChangeId(application.getChangeId());

						imageBO.setImageScanInfoId(isinfoPO.getImageScanInfoId());// @invalid
																					// 主键
						ImagePO imagePO = BeanUtils.copyProperties(ImagePO.class, imageBO);
						// @invalid 录入 <T_IMAGE> 数据,关联受理关系
						imagePO = imageDao.addImage(imagePO);
					}

				} else {
					// 6. 扫描影像
					if (null == imageScanListBO.getBussCode() || "".equals(imageScanListBO.getBussCode())) {
						return;
					}
					csAcceptChangePO.setAcceptCode(imageScanListBO.getBussCode());
					csAcceptChangePO = csAcceptChangeDao.findCsAcceptChange(csAcceptChangePO);
					application.setChangeId(csAcceptChangePO.getChangeId());
					application = csApplicationDao.findCsApplicationByChangeId(application);

					ImageScanPO imageScanPO = new ImageScanPO();
					Map resultBackMap = imageScanListBO.getResultBackMap();
					imageScanPO = updateImageScan(imageScanListBO, resultBackMap);// @invalid
																					// 更新imageScan信息
					imageScanBO = BeanUtils.copyProperties(ImageScanBO.class, imageScanPO);

					// @invalid resultBackMap处理
					String imagePageInfos = String.valueOf(resultBackMap.get("imagePageInfos"));
					Gson gson = new GsonBuilder().setFieldNamingPolicy(FieldNamingPolicy.UPPER_CAMEL_CASE).create();
					List imagePageInfoList = gson.fromJson(imagePageInfos, ArrayList.class);

					// @invalid 组装图片级别
					for (int i = 0; i < imagePageInfoList.size(); i++) {
						Map imagePageInfo = new HashMap();
						imagePageInfo = (Map) imagePageInfoList.get(i);
						// 7.
						// 影像文件操作类型，保全受理号补扫时使用，1--新增，0--其他，保全根据该字段判断当前新增的影像，再调用保全受理号修改接口
						String oprationStatus = String.valueOf(imagePageInfo.get("oprationStatus"));
						// @invalid 影响图片名称
						String pageId = String.valueOf(imagePageInfo.get("ecmPageId"));

						ImageScanInfoPO imageScanInfoPO = updateImageScanInfo(imagePageInfo, imageScanListBO,
								imageScanPO);// @invalid 更新图片信息
						// @invalid 新增图片
						if ("1.0".equals(oprationStatus) || "1".equals(oprationStatus)) {
							ImageBO imageBO = new ImageBO();
							BigDecimal userId = BigDecimal.ZERO;
							// @invalid RM:46090获取扫描用户的用户ID Start
							if(!StringUtilsEx.isBlank(imageScanListBO.getScanUserCode())){
								UserPO tUser =new UserPO();
								tUser.setUserName(imageScanListBO.getScanUserCode());
								tUser=userDao.findUser(tUser);
								userId=tUser.getUserId();
							}
							// @invalid RM:46090获取扫描用户的用户ID  end

							// 8. 根据事前扫描判断是受理号还是申请号
							if ("1.0".equals(IsBeforScan)) {
								imageBO.setApplyCode(application.getApplyCode());
								// 扫描回传的UserId与实际操作扫描人员不一致，这里初始化用户
								// RM:46090获取扫描用户的用户ID
								CusAddOperationUtil.setCusAddOperationUtil().addbizOperation(application.getApplyCode(),EVENT_CODE.EVENT_CODE_10303, null,userId);
							} else if ("0.0".equals(IsBeforScan)) {
								imageBO.setAcceptCode(csAcceptChangePO.getAcceptCode());
								imageBO.setApplyCode(application.getApplyCode());
								// 扫描回传的UserId与实际操作扫描人员不一致，这里初始化用户
								// RM:46090获取扫描用户的用户ID
								CusAddOperationUtil.setCusAddOperationUtil().addbizOperation(csAcceptChangePO.getAcceptCode(), EVENT_CODE.EVENT_CODE_10303, null,userId);
							} else{
								imageBO.setAcceptCode(csAcceptChangePO.getAcceptCode());
								imageBO.setApplyCode(application.getApplyCode());
								CusAddOperationUtil.setCusAddOperationUtil().addbizOperation(csAcceptChangePO.getAcceptCode(), EVENT_CODE.EVENT_CODE_10303, null, userId);
							}
							imageBO.setImageScanId(imageScanBO.getImageScanId());
							imageBO.setChangeId(application.getChangeId());
							imageBO.setImageScanInfoId(imageScanInfoPO.getImageScanInfoId());// @invalid
																								// 主键
							ImagePO imagePO = BeanUtils.copyProperties(ImagePO.class, imageBO);
							// 9. 录入 <T_IMAGE> 数据,关联受理关系
							imagePO = imageDao.addImage(imagePO);

						}
					}
				}
				//再查一遍imageScanInfo如果没有说明本次全删除就删除对应得imageScan，如果还有数据，更新imageScan中得页数
				ImageScanInfoPO imageScanInfoPOs = new ImageScanInfoPO();
				imageScanInfoPOs.setImageScanId(imageScanListBO.getImageScanId());
				List<ImageScanInfoPO> imageScanInfoPOlist = imageScanInfoDao.findAllImageScanInfo(imageScanInfoPOs);
				if(CollectionUtilEx.isEmpty(imageScanInfoPOlist)){	
					ImageScanPO deImageScanPO = new ImageScanPO();
					deImageScanPO.setImageScanId(imageScanListBO.getImageScanId());
					
					imageScanDao.deleteImageScan(deImageScanPO);
				}else{
					ImageScanPO imageScanPO = new ImageScanPO();
					imageScanPO.setApplyCode(imageScanListBO.getBussCode());
					// @invalid 生产缺陷#104_30881 P00002002393接口响应失败  代码未同步，本次进行同步修改。
					imageScanPO.setDocType(imageScanListBO.getDocType());
					imageScanDao.updateImageScanPagesByApplyCode(imageScanPO);
				}
			}

		}
	}

	/**
	 * 
	 * @description 更新人脸识别标识
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csscan.service.IScanService#saveFaceFlag(com.nci.tunan.cs.model.po.CsAcceptChangePO)
	 * @param updateCACPO PO对象
	 * @return
	 */
//	@Override
//	public String saveFaceFlag(CsAcceptChangePO updateCACPO) {
//		String returnMsg = "success";
//		//1. 第一步:入参非空判断
//		if(null == updateCACPO || StringUtils.isEmpty(updateCACPO.getAcceptCode())){
//			returnMsg = "入参为空，终止修改'人脸识别标识'";
//			logger.info("'saveFaceFlag' 终止处理，入参为空 -- {}", updateCACPO);
//			return returnMsg;
//		}
//		//2. 第二步:根据受理号查询原始受理信息(1、用于判断原受理信息是否存在；2、尝试条件使用原更新人代码)
//		CsAcceptChangePO cacQueryPO = new CsAcceptChangePO();
//		cacQueryPO.setAcceptCode(updateCACPO.getAcceptCode());
//		CsAcceptChangePO returnCacPO = csAcceptChangeDao.findCsAcceptChangeByAcceptCode(cacQueryPO);
//		//3. 第三步:原始受理信息非空判断
//		if(null == returnCacPO || null == returnCacPO.getAcceptId()){
//			returnMsg = "原始受理信息不存在，修改失败";
//			logger.info("'saveFaceFlag' 终止处理，根据受理号查询到原受理信息不存在!");
//			return returnMsg;
//		}
//		
//		//4. 第四步：人脸识别标识对比，如果表中数据和要修改的数据一致，则无需再修改
//		if(StringUtils.equals(updateCACPO.getFaceFlag(), returnCacPO.getFaceFlag())){
//			returnMsg = "要修改的和现有的'人脸识别标识'一致，无需修改！";
//			return returnMsg;
//		}
//		
//		updateCACPO.setAcceptId(returnCacPO.getAcceptId());
//		
//		//5. 第五步:判断 (是否传递了更新人代码  && 更新人代码是否合法)
//		if(null == updateCACPO.get("update_by") || !(updateCACPO.get("update_by") instanceof BigDecimal)){
//			//@invalid 设置更新人代码(接口请求，没有更新人，置为0)
//			updateCACPO.set("update_by", new BigDecimal(0));
//		}
//		//6. 第六步:更新受理标记
//		csAcceptChangeDao.updateCsAcceptChangeFlag(updateCACPO);
//		return returnMsg;
//	}
	
	

	
	/**
	 * 
	 * @description 查询事前扫描件事
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.impl.csscan.service.IScanService#findImageScanAppliccationTotal(java.lang.String)
	 * @param applicyCode applicyCode
	 * @return
	 */
	public int findImageScanAppliccationTotal(String applicyCode) {
		ImageScanPO po=new ImageScanPO();
		po.setApplyCode(applicyCode);
		return imageScanDao.findImageScanAppliccationTotal(po);
	}


	
	/**
	 * 
	 * @description 更新imageScan表中的数据 。如果同一业务同一单证类型下只能有一条数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param imageScanListBO 影像bo对象
	 * @param resultBackMap   map结果集
	 * @return
	 */
	private ImageScanPO updateImageScan(ImageScanListBO imageScanListBO,Map resultBackMap){
		ImageScanPO imageScanPO = new ImageScanPO();
		ImageScanPO imageScanQuery = new ImageScanPO();
		imageScanQuery.setApplyCode(imageScanListBO.getApplyCode());
		imageScanQuery.setDocType(imageScanListBO.getDocType());//1. 应备资料类型
		List<ImageScanPO> imageScanList = imageScanDao.findAllImageScan(imageScanQuery);//2.查询历史数据，如果有则全部删除后新增。
		//116569 start
		Map<String, Object> imageMap = new HashMap<String, Object>();
		//116569 end
		String creatUsCode = "";
		if(null != imageScanList && imageScanList.size() > 0){
			imageScanDao.batchDeleteImageScan(imageScanList);
			for(ImageScanPO imageScanPO2:imageScanList){
				ImageScanInfoPO imageScanInfoPO = new ImageScanInfoPO();
				imageScanInfoPO.setImageScanId(imageScanPO2.getImageScanId());
				List<ImageScanInfoPO> ImageScanInfoPOList = imageScanInfoDao.findAllImageScanInfo(imageScanInfoPO);
				if(null != ImageScanInfoPOList && ImageScanInfoPOList.size() > 0){
					//116569 start
					for(ImageScanInfoPO p : ImageScanInfoPOList){
						if(p.getData().get("image_id")!=null && p.getUploadTime() != null){
							creatUsCode = p.getScanUserCode();
							imageMap.put(p.getData().get("image_id").toString(), p.getUploadTime());
						}
					}
					//116569 end
					imageScanInfoDao.batchDeleteImageScanInfo(ImageScanInfoPOList);
				}
			}
			imageScanPO= BeanUtils.copyProperties(ImageScanPO.class, imageScanListBO);
			if(null != resultBackMap && null != resultBackMap.get("batchId")){
				imageScanPO.setScanNo(resultBackMap.get("batchId").toString());//@invalid 
			}
			/**
			 * #106_180310	保全扫描清单优化  t_image_scan 表新增字段，赋值处理。create_user_code（创建人员编码）、update_user_code（更新人员编码）。
			 * 经过与影像组同事确认，create_user_code、update_user_code 字段层级与 batchId 为同一层级。
			 */
			
			logger.info("updateImageScan方法billCardCreateUserCode字段赋值："+resultBackMap.get("billCardCreateUserCode")+"=========保全业务号："+imageScanListBO.getBussCode());
			if(null != resultBackMap && null != resultBackMap.get("billCardCreateUserCode")){
				imageScanPO.setCreateUserCode(resultBackMap.get("billCardCreateUserCode").toString());
			}
			logger.info("updateImageScan方法billCardUpdateUserCode字段赋值："+resultBackMap.get("billCardUpdateUserCode")+"=========保全业务号："+imageScanListBO.getBussCode());
			if(null != resultBackMap && null != resultBackMap.get("billCardUpdateUserCode")){
				imageScanPO.setUpdateUserCode(resultBackMap.get("billCardUpdateUserCode").toString());
			}
			logger.info("updateImageScan方法isForOutter字段赋值："+resultBackMap.get("isForOutter")+"==isForOutterNew:"+resultBackMap.get("isForOutterNew")+"==保全业务号："+imageScanListBO.getBussCode());
			// 兼容外围、梧桐树。（经与影像组同事确认，本次调整只涉及核心，外围端不参与变更。但是外围端部分字段不进行存储，核心进行兼容处理，字段取值为  t_image_scan_info 表  SCAN_USER_CODE 字段）
			if(null != resultBackMap && ("YES".equals(resultBackMap.get("isForOutter")) || "YES".equals(resultBackMap.get("isForOutterNew")))){
				imageScanPO.setCreateUserCode(imageScanListBO.getScanUserCode());
				imageScanPO.setUpdateUserCode(imageScanListBO.getScanUserCode());
			}
			logger.info("updateImageScan方法billCardParentCode字段赋值："+resultBackMap.get("billCardParentCode")+"=========保全业务号："+imageScanListBO.getBussCode());
			if(null != resultBackMap && null != resultBackMap.get("billCardParentCode")){
				imageScanPO.setCardType(resultBackMap.get("billCardParentCode").toString());
			}
			logger.info("UpdateUserCode字段赋值："+imageScanPO.getUpdateUserCode()+"==CreateUserCode"+imageScanPO.getCreateUserCode()+"=========保全业务号："+imageScanListBO.getBussCode());
			imageScanPO = imageScanDao.addImageScan(imageScanPO);
			//116569 start
			imageScanPO.getData().putAll(imageMap);
			//116569 end
		}else{
			imageScanPO= BeanUtils.copyProperties(ImageScanPO.class, imageScanListBO);
			if(null != resultBackMap && null != resultBackMap.get("batchId")){
				imageScanPO.setScanNo(resultBackMap.get("batchId").toString());//@invalid 
			}
			/**
			 * #106_180310	保全扫描清单优化  t_image_scan 表新增字段，赋值处理。create_user_code（创建人员编码）、update_user_code（更新人员编码）。
			 * 经过与影像组同事确认，create_user_code、update_user_code 字段层级与 batchId 为同一层级。
			 */
			logger.info("updateImageScan方法billCardCreateUserCode字段赋值："+resultBackMap.get("billCardCreateUserCode")+"=========保全业务号："+imageScanListBO.getBussCode());
			if(null != resultBackMap && null != resultBackMap.get("billCardCreateUserCode")){
				imageScanPO.setCreateUserCode(resultBackMap.get("billCardCreateUserCode").toString());
			}
			logger.info("updateImageScan方法billCardUpdateUserCode字段赋值："+resultBackMap.get("billCardUpdateUserCode")+"=========保全业务号："+imageScanListBO.getBussCode());
			if(null != resultBackMap && null != resultBackMap.get("billCardUpdateUserCode")){
				imageScanPO.setUpdateUserCode(resultBackMap.get("billCardUpdateUserCode").toString());
			}
			logger.info("updateImageScan方法isForOutter字段赋值："+resultBackMap.get("isForOutter")+"==isForOutterNew:"+resultBackMap.get("isForOutterNew")+"==保全业务号："+imageScanListBO.getBussCode());
			// 兼容外围、梧桐树。（经与影像组同事确认，本次调整只涉及核心，外围端不参与变更。但是外围端部分字段不进行存储，核心进行兼容处理，字段取值为  t_image_scan_info 表  SCAN_USER_CODE 字段）
			if(null != resultBackMap && ("YES".equals(resultBackMap.get("isForOutter")) || "YES".equals(resultBackMap.get("isForOutterNew")))){
				imageScanPO.setCreateUserCode(imageScanListBO.getScanUserCode());
				imageScanPO.setUpdateUserCode(imageScanListBO.getScanUserCode());
			}
			logger.info("updateImageScan方法billCardParentCode字段赋值："+resultBackMap.get("billCardParentCode")+"=========保全业务号："+imageScanListBO.getBussCode());
			if(null != resultBackMap && null != resultBackMap.get("billCardParentCode")){
				imageScanPO.setCardType(resultBackMap.get("billCardParentCode").toString());
			}
			
			logger.info("UpdateUserCode字段赋值："+imageScanPO.getUpdateUserCode()+"==CreateUserCode"+imageScanPO.getCreateUserCode()+"=========保全业务号："+imageScanListBO.getBussCode());
			imageScanPO = imageScanDao.addImageScan(imageScanPO);
		}
		
		return imageScanPO;
	}
	

	/**
	 * 
	 * @description 更新imageScanInfo表中的数据。
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param imagePageInfo 影像信息集合
	 * @param imageScanListBO 影像bo集合对象
	 * @param imageScanPO imageScanpo对象
	 * @return
	 */
	private ImageScanInfoPO updateImageScanInfo(Map imagePageInfo,ImageScanListBO imageScanListBO,ImageScanPO imageScanPO){
		ImageScanInfoPO result = null;
		//1. 影像文件操作类型，保全受理号补扫时使用，1--新增，0--其他，保全根据该字段判断当前新增的影像，再调用保全受理号修改接口
		String oprationStatus = String.valueOf(imagePageInfo.get("oprationStatus"));// @invalid1新增、2删除、0未操作（历史新增，本次未做修改的）、3更新
		//@invalid 影响图片名称
		String pageName = String.valueOf(imagePageInfo.get("pageName"));
		//@invalid 影响图片名称
		String pageId = String.valueOf(imagePageInfo.get("ecmPageId"));
		//116569 start
		String fileFormat = String.valueOf(imagePageInfo.get("fileFormat"));
		String isLocalUpload = String.valueOf(imagePageInfo.get("isLocalUpload"));
		String uploadReason = String.valueOf(imagePageInfo.get("uploadReason"));
		String uploadDetailReason = String.valueOf(imagePageInfo.get("uploadDetailReason"));
		// @invalid 扫描人员编码
		String pageCreateUserCode = null ;
		// @invalid 更新扫描人员编码
		String pageUpdateUserCode = null ;
		logger.info("updateImageScanInfo方法pageCreateUserCode字段赋值："+imagePageInfo.get("pageCreateUserCode")+"=========保全业务号："+imageScanListBO.getBussCode());
		if(null != imagePageInfo.get("pageCreateUserCode")){
			pageCreateUserCode = String.valueOf(imagePageInfo.get("pageCreateUserCode"));
		}
		logger.info("updateImageScanInfo方法pageUpdateUserCode字段赋值："+imagePageInfo.get("pageUpdateUserCode")+"=========保全业务号："+imageScanListBO.getBussCode());
		if(null != imagePageInfo.get("pageUpdateUserCode")){
			pageUpdateUserCode = String.valueOf(imagePageInfo.get("pageUpdateUserCode"));
		}
		//116569 end
		//@invalid 受理号
		BigDecimal userId =null;
		//@invalid 新增图片
		if("1.0".equals(oprationStatus)||"1".equals(oprationStatus)|| "0".equals(oprationStatus)|| "3".equals(oprationStatus) || "-1".equals(oprationStatus)){
			ImageScanInfoBO imageScanInfoBO = new ImageScanInfoBO();
			// @invalid ImageScanBO->ImageScanInfoBO*****7个属性******
			try{
				imageScanInfoBO.setScanTime(imageScanListBO.getScanTime());//@invalid 扫描时间
				imageScanInfoBO.setUploadTime(imageScanListBO.getUploadTime());//@invalid 上载时间
				imageScanInfoBO.setRescanCause(imageScanListBO.getRescanCause());//@invalid 重扫原因
				imageScanInfoBO.setScanUserCode(imageScanListBO.getScanUserCode());//@invalid 扫描人员编码
				imageScanInfoBO.setScanUserType(imageScanListBO.getScanUserType());//@invalid 扫描人员类型
				imageScanInfoBO.setScanOperationType(imageScanListBO.getScanOperationType());//@invalid 补扫或新扫描
				imageScanInfoBO.setImageScanStatus(imageScanListBO.getImageScanStatus());//@invalid 扫描审核状态
				// @invalid RM:46090获取扫描用户的用户ID Start
				if(!"".equals(imageScanListBO.getScanUserCode())  && imageScanListBO.getScanUserCode()!=null){
					UserPO tUser =new UserPO();
					tUser.setUserName(imageScanListBO.getScanUserCode());
					tUser=userDao.findUser(tUser);
					userId=tUser.getUserId();
				}
				// @invalid  RM:46090获取扫描用户的用户ID  end
			}catch(Exception e){
				throw new RuntimeException(e);
			}
			imageScanInfoBO = BeanUtils.copyProperties(ImageScanInfoBO.class,imageScanListBO);
			//116569 start
			if("0".equals(oprationStatus)){
				try {
					Date uploadtime = (Date)imageScanPO.getData().get(pageId);
					if(uploadtime != null){
						imageScanInfoBO.setUploadTime(uploadtime);
					}
				} catch (Exception e) {
					logger.info("pageId:"+pageId+",上传时间取值报错:"+imageScanPO.getData().get(pageId));
					throw new RuntimeException(e);
				}
			}
			//116569 end
			// @invalid ImageScanBO->ImageScanInfoBO
			imageScanInfoBO.setImageScanId(imageScanPO.getImageScanId());//@invalid 主键
			imageScanInfoBO.setImageName(pageName+","+pageId);
			imageScanInfoBO.setImageID(pageId);
			//116569 start
			if(fileFormat!=null && !"".equals(fileFormat) && !"null".equals(fileFormat)){
				imageScanInfoBO.setFileFormat(fileFormat);
			}
			if(isLocalUpload!=null && !"".equals(isLocalUpload) && !"null".equals(isLocalUpload)){
				imageScanInfoBO.setIsLocalUpload(new BigDecimal(isLocalUpload));
			}
			if(uploadReason!=null && !"".equals(uploadReason) && !"null".equals(uploadReason)){
				imageScanInfoBO.setUploadReason(uploadReason);
			}
			if(uploadDetailReason!=null && !"".equals(uploadDetailReason) && !"null".equals(uploadDetailReason)){
				imageScanInfoBO.setUploadDetailReason(uploadDetailReason);
			}
			// @invalid t_image_scan_info 表  扫描人员编码  字段赋值。
			if(!StringUtilsEx.isNullOrEmpty(pageCreateUserCode)){
				imageScanInfoBO.setScanUserCode(pageCreateUserCode); 
			}
			// @invalid t_image_scan_info 表   更新扫描人员编码   字段赋值。
			if(!StringUtilsEx.isNullOrEmpty(pageUpdateUserCode)){
				imageScanInfoBO.setUpdateUserCode(pageUpdateUserCode);
			}
			logger.info("UpdateUserCode字段赋值："+imageScanInfoBO.getUpdateUserCode()+"==scanusercode"+imageScanInfoBO.getScanUserCode()+"=========保全业务号："+imageScanListBO.getBussCode());
			//116569 end
			ImageScanInfoPO imageScanInfoPO = BeanUtils.copyProperties(ImageScanInfoPO.class,imageScanInfoBO);
			logger.info("UpdateUserCode字段赋值："+imageScanInfoPO.getUpdateUserCode()+"==scanusercode"+imageScanInfoPO.getScanUserCode()+"=========保全业务号："+imageScanListBO.getBussCode());
			//2.录入 <T_IMAGE_SCAN_INFO> 数据
			result = imageScanInfoDao.addImageScanInfo(imageScanInfoPO);
		}
		
		return result;
	}
	
	public IImageScanDao getImageScanDao() {
		return imageScanDao;
	}



	public void setImageScanDao(IImageScanDao imageScanDao) {
		this.imageScanDao = imageScanDao;
	}



	public IImageDao getImageDao() {
		return imageDao;
	}



	public void setImageDao(IImageDao imageDao) {
		this.imageDao = imageDao;
	}



	public IImageScanInfoDao getImageScanInfoDao() {
		return imageScanInfoDao;
	}



	public void setImageScanInfoDao(IImageScanInfoDao imageScanInfoDao) {
		this.imageScanInfoDao = imageScanInfoDao;
	}



	public ICsApplicationDao getCsApplicationDao() {
		return csApplicationDao;
	}



	public void setCsApplicationDao(ICsApplicationDao csApplicationDao) {
		this.csApplicationDao = csApplicationDao;
	}



	public IPriorScanService getPriorScanService() {
		return priorScanService;
	}



	public void setPriorScanService(IPriorScanService priorScanService) {
		this.priorScanService = priorScanService;
	}

    public ICsAcceptChangeDao getCsAcceptChangeDao() {
        return csAcceptChangeDao;
    }

    public void setCsAcceptChangeDao(ICsAcceptChangeDao csAcceptChangeDao) {
        this.csAcceptChangeDao = csAcceptChangeDao;
    }
}
