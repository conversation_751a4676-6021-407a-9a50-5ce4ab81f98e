package com.nci.tunan.cs.impl.peripheral.ucc.r00102001135;

import com.nci.tunan.cs.interfaces.peripheral.exports.r00102001135.vo.InputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102001135.vo.OutputData;
import com.nci.udmp.framework.exception.app.BizException;

/**
 * 
 * @description 保单挂起服务 
 * <AUTHOR> <EMAIL> 
 * @date 2020-12-15 下午8:04:38 
 * @.belongToModule CS-保全子系统
 */
public interface ICSPendingPolicyUcc  {
	
	/**
	 * 
	 * @description 保单挂起服务提交接口
	 * @version 1.0
	 * @title 保单挂起服务提交接口
	 * <AUTHOR> <EMAIL>
	 * @param inputData 入参
	 * @return 返回挂起结果
	 * @throws BizException 异常信息
	 */
	public OutputData pendingPolicy(InputData inputData) throws BizException;

}
