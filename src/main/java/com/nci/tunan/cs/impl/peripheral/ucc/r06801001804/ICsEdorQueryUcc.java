package com.nci.tunan.cs.impl.peripheral.ucc.r06801001804;


import com.nci.tunan.cs.interfaces.peripheral.exports.r06801001804.vo.InputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r06801001804.vo.OutputData;
/**
 * 
 * @description  保全批单查询
 * <AUTHOR> 
 * @version V1.0.0
 * @.belongToModule 保全子系统
 * @date 2018年3月22日 下午2:31:45
 */
public interface ICsEdorQueryUcc{

	/**
	 * 
	 * @description 条件查询数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param inputData 接口入参
	 * @return OutputData 出参
	 */
	public OutputData queryCsEdor(InputData inputData);
}
