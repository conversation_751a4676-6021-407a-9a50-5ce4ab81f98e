package com.nci.tunan.cs.impl.csItem.service;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.model.bo.CusPhRelaChgBO;
import com.nci.tunan.cs.model.bo.CustomerBO;
import com.nci.tunan.cs.model.vo.CsAcceptChangeVO;
import com.nci.tunan.cs.model.vo.CsCustomerVO;
import com.nci.tunan.cs.model.vo.CsEndorseCCVO;
import com.nci.tunan.cs.model.vo.CusPhRelaChgVo;
import com.nci.tunan.cs.model.vo.CustPolicyAddressChgVO;
import com.nci.tunan.cs.model.vo.CustomerBaseInfoVO;
import com.nci.tunan.cs.model.vo.CustomerVO;
import com.nci.tunan.pa.interfaces.model.bo.PolicyHolderBO;


/**
 * 
 * @description 快捷客户基本资料变更Service接口 
 * <AUTHOR>
 * @version V1.0.0
 * @.belongToModule CS-保全子系统
 * @date 2020年12月24日 下午1:47:48
 */
public interface ICsEndorseCCHKService{

	
	
/**
 * 
 * @description 查询客户基本资料快捷键 客户层 保单层信息
 * @version V1.0.0
 * @title
 * <AUTHOR>
 * @param customerVO 客户信息
 * @param queryFlag 查询标记
 * @return
 */
    CsEndorseCCVO calcCustPolicyMsg(CustomerBaseInfoVO customerVO, String queryFlag);

 
    /**
     * 
     * @description 修改CChK
     * @version V1.0.0
     * @title
     * @<NAME_EMAIL>
     * @param checkCustomerVO
     * @param applicantCustomerVO 将申请customerId和changeId存放进去
     * @param custPolicyAdrChgVOs
     * @return
     * @throws Exception 
     */
    /**54754  @invalid  关于新核心系统生成唯一收付费号码的需求  start**/
    List<CsEndorseCCVO> updateCustPolicy(List<CsCustomerVO> checkCustomerVOList, CsCustomerVO applicantCustomerVO, List<CustPolicyAddressChgVO> custPolicyAdrChgVOs,List<CusPhRelaChgVo> cusPhRelaChgVos, List<CsAcceptChangeVO> csAcceptChangeVOList) throws Exception;
    /**54754  @invalid 关于新核心系统生成唯一收付费号码的需求  end**/

    /**
     * 
     * @description 查询页面展示信息
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param customerVO 客户信息
     * @return
     */
CsEndorseCCVO calcCustOrPolicyMsg(CustomerBaseInfoVO customerVO);

/**
 * 
 * @description 查询明细
 * @version V1.0.0
 * @title
 * <AUTHOR>
 * @param checkCustomerVO checkCustomerVO
 * @return
 */
CsEndorseCCVO calcCustPolicy(CsCustomerVO checkCustomerVO);

/**
 * 
 * @description 非自保件投保人移动电话与全系统在职业务员电话号码不能一致
 * @version V1.0.0
 * @title
 * <AUTHOR>
 * @param csCustomerVO csCustomerVO
 * @return
 */
public boolean checkPolicyHolderMobile(CsCustomerVO csCustomerVO);

/**
 * 
 * @description  非自保件投保人移动电话与全系统在职业务员电话号码不能一致
 * @version V1.0.0
 * @title
 * <AUTHOR>
 * @param policyHolderBO 投保人信息
 * @param customerVO 客户信息
 * @param changeId 保全变更ID
 * @return
 */
public boolean queryHKHolderUpdatePhone(PolicyHolderBO policyHolderBO, CustomerVO customerVO,BigDecimal changeId);

/**
 * 
 * @description 校验投被保人关系
 * @version V1.0.0
 * @title
 * <AUTHOR>
 * @param cusPhRelaChgBo cusPhRelaChgBo
 * @return
 */
public String checkPhRelation(CusPhRelaChgBO cusPhRelaChgBo);


/**
 * 
 * @description 校验客户职业类别
 * @version V1.0.0
 * @title
 * <AUTHOR>
 * @param csCustomerVO csCustomerVO
 * @return
 */
public String checkCustomerJob(CsCustomerVO csCustomerVO);
/**
 * 
 * @description 判断是否发生变化
 * @version V1.0.0
 * @title
 * <AUTHOR>
 * @param customerList 客户信息
 * @param custPolicyAdrChgVOs custPolicyAdrChgVOs
 * @param changeId 保全变更ID
 * @return
 */
public String checkCustomerAndPolicy(List<CsCustomerVO> customerList,List<CustPolicyAddressChgVO> custPolicyAdrChgVOs,List<CusPhRelaChgVo> cusPhRelaChgVos,BigDecimal changeId);

/**
 * 
 * @description 自保件投保人固定电话与全系统在职业务员电话号码不能一致
 * @version V1.0.0
 * @title
 * <AUTHOR>
 * @param customerVO 客户信息
 * @param changeId 保全变更ID
 * @return
 */
public String queryHKHolderUpdateFixphone(CustomerBO customerVO, BigDecimal changeId);

/**
 * 
 * @description  查询第二投保人及其相关保单
 * @version V1.0.0
 * @title
 * <AUTHOR> <EMAIL>
 * @param param
 * @return
 */
public Map<BigDecimal, String> findSecondPolicyHolderInfo(HashSet<BigDecimal> customerIdSet);

}
