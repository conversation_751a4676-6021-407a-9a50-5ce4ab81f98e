package com.nci.tunan.cs.impl.csItem.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.model.bo.BusinessProductBO;
import com.nci.tunan.cs.model.bo.CsAddProductInfoBO;
import com.nci.tunan.cs.model.bo.CsBenefitInsuredBO;
import com.nci.tunan.cs.model.bo.CsContractBusiProdBO;
import com.nci.tunan.cs.model.bo.CsContractExtendBO;
import com.nci.tunan.cs.model.bo.CsContractProductBO;
import com.nci.tunan.cs.model.bo.CsContractProductOtherBO;
import com.nci.tunan.cs.model.bo.CsInsuredListBO;
import com.nci.tunan.cs.model.bo.CsPolicyHolderBO;
import com.nci.tunan.cs.model.bo.CsPremArapBO;
import com.nci.tunan.cs.model.bo.PagecfgElementValueBO;
import com.nci.tunan.cs.model.bocomp.PApremiumAddCompBO;
import com.nci.tunan.cs.model.po.CsAcceptChangePO;
import com.nci.tunan.cs.model.po.CsContractBusiProdPO;
import com.nci.tunan.cs.model.po.CsPolicyChangePO;
import com.nci.tunan.cs.model.po.CsPrecontProductPO;
import com.nci.tunan.cs.model.po.CsRiskAmountPO;
import com.nci.tunan.cs.model.po.PagecfgPrdCateRelaPO;
import com.nci.tunan.cs.model.po.PagecfgPrdElementPO;
import com.nci.tunan.cs.model.vo.AddBusiPremVO;
import com.nci.tunan.cs.model.vo.CsAddSubBusiProdVO;
import com.nci.tunan.cs.model.vo.CsCustomerSocialSecuVO;
import com.nci.tunan.cs.model.vo.CsPaymentTypeVO;
import com.nci.tunan.cs.model.vo.ImageVO;
import com.nci.tunan.cs.model.vo.PolicyHolderAndInsurdMsgVO;
import com.nci.tunan.mms.interfaces.query.exports.productbaseinfoquery.vo.ProductLifeResVO;
import com.nci.tunan.mms.interfaces.query.exports.riderproductquery.vo.MmsRiderProductQueryVO;
import com.nci.tunan.pa.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.prd.interfaces.query.exports.queryobservationperiod.vo.PrdQueryObservationPeriodResVO;
import com.nci.udmp.framework.signature.IService;

/**
 * 
 * @description ICsEndorseNSService
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @.belongToModule cs-保全子系统
 * @date 2020年12月30日  下午8:20:59
 */
 
public interface ICsEndorseNSService extends IService, ICSItemBaseService {

  

    /**
     * 查询险种下 责任组信息
     * 
     * @description 查询险种下 责任组信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csAddSubBusiProd
     *            入参 所属产品id
     * @return
     */
    public List<ProductLifeResVO> queryProductLife(PApremiumAddCompBO csAddSubBusiProd);

    /**151299 判断是否485双被保人保单
	 * @return
	 */
	public boolean is485TwoInsured(BigDecimal policyChgId);
    /**
     *
     * 
     * @description  新增责任组信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param csContractProductBO
     * @return
     */
    public CsContractProductBO saveContractProduct(CsContractProductBO csContractProductBO);

    /**
     * 
     * @description 保存险种下期缴费计划
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param csContractExtendBO 险种下期缴费计划BO对象 
     * @return
     */
    public CsContractExtendBO saveCsContractExtendPO(CsContractExtendBO csContractExtendBO);

    /**
     *
     * 
     * @description  查询保费接口
     * @version 
     * @title
     * <AUTHOR> <EMAIL>
     * @param csTotalPremAfCompBO
     *            changeId，policyId，policyChgId，busiItemId，所属产品责任组id，amount，unit
     * @return
     */
//    public PrdPremCalByMMSResVO queryProductAmount(PApremiumAddCompBO csTotalPremAfCompBO);

    
    /**
     * 
     * @description 删除
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param extendBOs 险种下期缴费计划BO对象列表
     */
    public void deletecsContractExtend(List<CsContractExtendBO> extendBOs);

    /**
     * 
     * @description 删除险种被保人
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param bInsuredBOs 保全险种被保人表BO对象列表
     */
    public void deleteCsBenefitInsuredBO(List<CsBenefitInsuredBO> bInsuredBOs);

    /**
     * 
     * @description 删除险种
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param busiBOs 险种表BO
     */
    public void deleteCsContractBusiProdBO(List<CsContractBusiProdBO> busiBOs);

    /**
     * 
     * @description 删除保单被保人
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param insured 保单被保人列表BO
     */
    public void deleteCsInsuredListBO(CsInsuredListBO insured);
    /**
     * @description 查询单条数据
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param businessProductPO 对象
     * @return CsBusinessProductPO 查询结果对象
     */
	 public BusinessProductBO findBusinessProduct(BusinessProductBO businessProductBO);
	 
	 /**
	  * @description 查询单条数据。
	  * @version V1.0.0
	  * @param newProduct 保单险种责任组表BO
	  * @return
	  */
    public CsContractProductBO findCsContractProduct(CsContractProductBO newProduct);

    /**
     * @description 查询保障计划
     * @version V1.0.0
     * @param businessProductBO 保障计划要素值组合BO对象
     * @return List<PagecfgElementValueBO>
     */
    public List<PagecfgElementValueBO> queryGuaranteeProject(PagecfgElementValueBO pagecfgElementValueBO);
    
    /**
     * @description 查询保障计划页面展示
     * @version V1.0.0
     * @param pagecfgPrdCateRelaPO PagecfgPrdCateRelaPO对象
     * @return PagecfgPrdCateRelaPO
     */
    public PagecfgPrdCateRelaPO findPagecfgPrdElement(PagecfgPrdCateRelaPO pagecfgPrdCateRelaPO);

    /**
     * 
     * @description 查询所有数据
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param pagecfgPrdElementPO PagecfgPrdElementPO对象
     * @return List<PagecfgPrdElementPO>
     */
    public List<PagecfgPrdElementPO> findAllPagecfgPrdElementS(PagecfgPrdElementPO pagecfgPrdElementPO);
    
    /**
     * @description 新增保单责任组备用表
     * @version V1.0.0
     * @param csContractProductOtherBO 保单责任组备用表BO
     * @return CsContractProductOtherBO
     */
    public CsContractProductOtherBO addCsContractProductOther(CsContractProductOtherBO csContractProductOtherBO);
    
    /**
     * @description 查询保单责任组备用表
     * @version V1.0.0
     * @param csContractProductOther 保单责任组备用表BO
     * @return List<CsContractProductOtherBO>
     */
    public List<CsContractProductOtherBO> findAllCsContractProductOther(CsContractProductOtherBO csContractProductOther);
    /**
     * 
     * @description 更新保单责任组备用表
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.cs.impl.csItem.service.ICSItemBaseService#updateCsContractProductOther(com.nci.tunan.cs.model.bo.CsContractProductOtherBO)
     * @param csContractProductOtherBO 保单责任组备用表BO
     * @return Boolean 成功与否
     */
    public Boolean updateCsContractProductOther(CsContractProductOtherBO csContractProductOtherBO);

    /**
     * 
     * @description 删除健康告知
     * @version V1.0.0
     * @title
     * <AUTHOR> <EMAIL>
     * @param tAcceptId
     */
	public void clearNewCsQuestionaireCustParam(BigDecimal tAcceptId);

	/**
	 * 
	 * @description 删除新添加的附加险
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param newCsContractBusiProdPO CsContractBusiProdPO对象
	 * @param acceptId 受理Id
	 */
	public void deleteNewBusiProd(CsContractBusiProdPO newCsContractBusiProdPO,BigDecimal acceptId);

	/**
	 * 
	 * @description 加保--保单相关险种信息 组合查询
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csAddSubBusiProdVO
	 * @return List<CsAddSubBusiProdVO>
	 */
	public List<CsAddSubBusiProdVO> queryAddSubBusiProd(CsAddSubBusiProdVO csAddSubBusiProdVO);

	/**
	 * 
	 * @description 查询附加险列表
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csAddSubBusiProd
	 * @return List<BusinessProductBO>
	 */
	public List<BusinessProductBO> queryCsContractBusiProdList(CsAddSubBusiProdVO csAddSubBusiProd);
	
	/**
	 * 新增附加险
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csAddSubBusiProd
	 * @param customerId 
	 * @return
	 * @throws Exception 
	 */
	public String saveBusiProd(CsAddSubBusiProdVO csAddSubBusiProd,BigDecimal customerId) throws Exception;

	/**
	 * 删除新添加的附加险
	 * @param changeId
	 * @param acceptId
	 * @param delBusiItemId
	 */
	public void deleteBusiProd(BigDecimal changeId, BigDecimal acceptId,BigDecimal busiItemId, String policyCode);

	/**
	 * 
	 * @description 根据险种号查询所有数据
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param businessProductBO
	 * @return
	 */
	public List<Map<String, Object>> findPagecfgPrdElementByProductCodeSys(BusinessProductBO businessProductBO);

	/**
	 * 
	 * @description 计算风险保额
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param changeId 申请id
	 * @param policyCode 保单号
	 * @param WaiverFlag
	 * @return boolean
	 */
	boolean csRiskSave(BigDecimal changeId,String policyCode,BigDecimal WaiverFlag);
	
	/**
	 * 
	 * @description 调用产品工厂接口 查询附加险
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csAddSubBusiProd
	 * @return MmsRiderProductQueryVO
	 */
	public MmsRiderProductQueryVO querySupplementalInsurance(CsAddSubBusiProdVO csAddSubBusiProd);

	/**
	 * 
	 * @description 1、查询新增险种是否为豁免险  2、查询新增豁免险的豁免附加险种列表+主险  3、求出可豁免险种的每期总保费
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param acceptId 受理id
	 * @param addBusPrdId 附加险id
	 * @return BigDecimal
	 */
	public BigDecimal calWaivedStdAll(BigDecimal acceptId,CsContractBusiProdBO csNewConBusiProdBO);
	/**
	 * 
	 * @description 新增附加险改变社保状态的验证
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csAddSubBusiProdVO CsAddSubBusiProdVO
	 * @param newSocialSecu
	 * @param checkFlag
	 * @param jsonSocialSecuVOList 151299 add
	 * @return
	 * @throws Exception
	 */
	public String  checkSocialSecuInfo(CsAddSubBusiProdVO csAddSubBusiProdVO, BigDecimal newSocialSecu,String checkFlag,String jsonSocialSecuVOList) throws Exception;
	/**
	 * 
	 * @description 新增附加险保存社保状态
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csCustomerSocialSecuVO 社保标识变更
	 * @return CsCustomerSocialSecuVO 社保标识变更
	 */
	public CsCustomerSocialSecuVO saveSocialSecu(CsCustomerSocialSecuVO csCustomerSocialSecuVO);
	
	/**
	 * 
	 * @description 初始化页面 查询险种社保信息
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csAddSubBusiProd CsAddSubBusiProdVO
	 * @return CsCustomerSocialSecuVO 社保标识变更
	 */
	public CsCustomerSocialSecuVO querySocialSecuInfo(CsAddSubBusiProdVO csAddSubBusiProd);
	/**
	 * @description 生成保全收付费,只给预约长期险用。别人别用
	 * @version V1.0.0
	 * @param csNewConBusiProdBO 险种表BO
	 * @param policyHolder 保单被保人信息BO对象
	 * @param csPrecontProductPO
	 * @param csPremArap 应收应付表BO
	 */
	public void initCsPremArapBOForExtract(CsContractBusiProdBO csNewConBusiProdBO,CsPolicyHolderBO policyHolder,CsPrecontProductPO csPrecontProductPO,CsPremArapBO csPremArap);


	/**
	 * 
	 * @description 预约长期附加险重算风险保费
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPrecontProductPO CsPrecontProductPO对象
	 * @return CsRiskAmountPO CsRiskAmountPO对象
	 */
	public CsRiskAmountPO csRiskSavePRECNS(CsPrecontProductPO csPrecontProductPO);
	
	/**
	 * 
	 * @description 新增附加险查询产品是否社保相关
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csContractProductBO 保单险种责任组表BO
	 * @return BigDecimal
	 */
	public BigDecimal querySocialInsuranceIndi(CsContractProductBO csContractProductBO);
	
	/**
	 * 
	 * @description 查询产品的社保状态
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csContractBusiProdBO 险种表BO
	 * @return BigDecimal
	 */
	public BigDecimal querySocialStatusForCheck(CsContractBusiProdBO csContractBusiProdBO) ;
	/**
	 * @description 更改社保状态,默认保存61版告知
	 * @version V1.0.0
	 * @param newAddSubBusiProdVO 主险
	 * @param newSociSecu 最新的社保状态
	 */
	public void updateNotificBySociSecu(CsAddSubBusiProdVO newAddSubBusiProdVO,BigDecimal newSociSecu) ;

	/**
	 * 
	 * @description 校验投保规则
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param acceptId 受理id
	 * @return String
	 */
    public String checkApplyRule(BigDecimal acceptId);
    
	/**
	 * @description 查询险种配置信息
	 * @version V1.0.0
	 * @param productCodeSys
	 * @param policyId 保单Id
	 * @return
	 */
	public CsAddProductInfoBO queryBusinessInfo(CsAddSubBusiProdVO csAddSubBusiProdVO);
	 
	/*********************************** 给外围集成接口提供方法 start *********************************************/
	/**
     * 
     * @description 保存新增附加险信息外围接口调用
     * @version
     * @title
     * <AUTHOR>
     * @param changeId
     * @param acceptId
     * @param customerId
     * @param policyCode
     * @param csAddSubBusiProdVOs
     * @param tryFlag
     * @return
     * @throws Exception
     */
    public String saveNSDataForOutter(BigDecimal changeId, BigDecimal acceptId, BigDecimal customerId, String policyCode,
            List<CsAddSubBusiProdVO> csAddSubBusiProdVOs,boolean tryFlag) throws Exception;
    /**
     * 
     * @description 新增长期附加险的申请提交日期校验外围接口调用
     * @version
     * @title
     * <AUTHOR>
     * @param changeId
     * @param policyCode
     * @param csAddSubBusiProdVOs 对象
     * @return String
     */
    public String checkApplyDateForOutter(BigDecimal changeId, String policyCode, List<CsAddSubBusiProdVO> csAddSubBusiProdVOs);
    /**
     * 
     * @description 移动保全2.0 新增附加险校验社保状态
     * @version
     * @title
     * <AUTHOR>
     * @param changeId
     * @param policyId
     * @param newSocialState
     * @return
     */
    public Map<String, String> checkSociSecu(BigDecimal changeId, BigDecimal policyId, BigDecimal newSocialState);
    /*********************************** 给外围集成接口提供方法 end *********************************************/
    /**
     * 
     * @description 判断是否是长期附加险
	 * @version
	 * @title
	 * <AUTHOR>
     * @param busiProdCode  险种编码
     * @return
     */
    public boolean isLongBusiProdCode(String busiProdCode);
    /**
	 * 
	 * @description 加载长期险的缴费方法
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param changeId 保全变更id
	 * @param acceptId 受理id
	 * @param policyChgId 保单变更id
	 * @param oldNew 标识
	 * @return
	 */
	public List<CsPaymentTypeVO> loadPaymentTypeInfo(BigDecimal changeId, BigDecimal acceptId, BigDecimal policyChgId,
			String oldNew,String acceptCode,PolicyHolderAndInsurdMsgVO policyHolderVO,boolean isLongBusi);
	
	/**
	 * 
	 * @description  查询新增险种是否为长期险
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param changeId
	 * @param policyChgId
	 * @param policyCode
	 * @return
	 */
	public boolean queryAddBusiProd(BigDecimal changeId,BigDecimal policyChgId,String policyCode);
	
	/**
	 * 
	 * @description 新增长期险保存对应的长期险的缴费银行信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPaymentTypeVO
	 * @return
	 */
	public String saveNewPaymentType(CsPaymentTypeVO csPaymentTypeVO,BigDecimal policyId);

	/**
	 * @Description: 移动保全 2.0 新增附加险校验_核心业务校验逻辑_指定保单是否有未责任生效的长期附加险
	 * <AUTHOR>  
	 * @date 2020年8月11日 上午9:21:28
	 * @version 1.0
	 */
	public String mobileCsNsCoreValidate(BigDecimal acceptId, String contNo);
	
	/**
	 * 
	 * @description 长期险老核心数据走此方法(抽档批处理)
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csAddSubBusiProd
	 * @param customerId
	 * @throws Exception 
	 */
	public Map<String,Object> saveOldDateMethod(CsAddSubBusiProdVO csAddSubBusiProd, BigDecimal customerId, CsContractBusiProdPO csContractBusiProdPOMaster) throws Exception ;
	
	/**
	 * @description 判断是否是可保证续保
	 * @version
	 * @title 如产品的保证续保标识t. RENEW_OPTION为“2”（2-可保证续保）则本产品为“保证续保附加险”
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsEndorseCTService#toTransCapStatus(java.lang.String)
	 * @param 参数  busiProdCode
	 * @return
	 */
	public boolean initDRFlag(BigDecimal policyId,String productCodeSys,BigDecimal productCodeSysId,BigDecimal changeId,BigDecimal customerId);


	  /**
     * 
     * @description 录入完成
     * @version 1.0
     * @title 录入完成 20210330
     * <AUTHOR> <EMAIL>
     * @return 返回保存结果，是否保存成功。
     */
	public ImageVO overDRFlag(BigDecimal policyId,BigDecimal acceptId);
	 /**
     * 
     * @description 录入完成(调影像接口)
     * @version 1.0
     * @title 录入完成 20210330
     * <AUTHOR> <EMAIL>
     * @return 返回保存结果，是否保存成功。
     */
	public ImageVO overDRFlags(String riskCodeOutPut,BigDecimal policyId,String productCodeSys,BigDecimal productCodeSysId,BigDecimal changeId,BigDecimal customerId,BigDecimal acceptId);

	
	/**
     * 
     * @description 校验是短期健康险。
     * @version 1.0
     * @title 新增附加险保存
     * <AUTHOR> <EMAIL>
     * @return 返回保存结果，是否保存成功。
     */
	public boolean checkShortChangeRisk(BigDecimal acceptId);
	
    /**
     * 校验是否需要双录
     * @param applyCode 投保单号
     * @param policyCode 保单号
     * @param busiProdCodes 险种信息 List<String>
     * @return 返回险种信息是否需要双录
     */
	public Map<String,Boolean> checkCsDr(ContractMasterPO masterPO,List<String> busiProdCodes);

	/**
	 * “双录险种标识”为“是”（即，险种代码前显示红色“*”的险种）且该险种双录系统不存在双录影像（保单号+险种，查询是否存在当日上传的双录影像）,根据以下规则进行阻断或非阻断提示：
	 * 本次受理是否已上传双录影音判断规则：根据“保单号+险种”查询是否有双录影音资料（标识红色“*”的险种代码及险种相同，且个数相同，不能多也不能少，必须一一对应），且该双录影音文件上传日期≥【录入完成】日期-30。

	 * @param csAcceptChangePO
	 * @return
	 */
	boolean checkAcceptChangeCsDrVideo(CsAcceptChangePO csAcceptChangePO);

	public void saveOrUpdateCsDrInfo(CsAcceptChangePO csAcceptChangePO,BigDecimal policyChgId,List<String> busiProdCodes);
	public Map<String,String> checkCsDrForNs(CsPolicyChangePO cpcp,List<String> newBusiProds);
	/**
	 * 计算保费
	 * @param parameterMap
	 */
	public void calculationPremium(Map<String,Object> parameterMap);


	/**
	 * 判断新增产品是否是停止新单产品配置表数据且申请日期晚于或等于“停止新单日期（含）
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAddSubBusiProdVOList 新增产品信息
	 * @return 阻断提示信息
	 */
	public String checkStopBusiProd(List<CsAddSubBusiProdVO> csAddSubBusiProdVOList) throws Exception;


	/**
	 * 查询特殊险种页面信息
	 * @param countWay
	 * @param addBusPrdId
	 * @param masterBusiItemId
	 * @return
	 */
	public AddBusiPremVO queryBusiPrem(AddBusiPremVO addBusiPremVO);
	
	/**
	 * 新增附加险 42584需求新增批单节点
	 * @return 拼接完成字符串 
	 */
	public String hesitationObservation(List<PrdQueryObservationPeriodResVO> listRes,List<Map<String,String>> freeLookPeridList);
}
