package com.nci.tunan.cs.impl.csConfig.service.impl; 
import java.util.ArrayList;

import com.nci.tunan.cs.dao.IAutoVisitCfgDao;
import com.nci.tunan.cs.impl.csConfig.service.IAutoVisitCfgService;
import com.nci.tunan.cs.model.bo.AutoVisitCfgBO;
import com.nci.tunan.cs.model.po.AutoVisitCfgPO;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;
 
/**
 * 
 * @description  保全自动发起回访配置Service实现类
 * <AUTHOR>  <EMAIL> 
 * @date 2015-06-12 10:54:55 
 * @.belongToModule 保全子系统
 */
 public class AutoVisitCfgServiceImpl   implements IAutoVisitCfgService  {
 	/** 
	 *  日志工具
 	 */
 	private static Logger logger = LoggerFactory.getLogger();

	/** 
     * 保全自动发起回访配置dao 
     */
 	private IAutoVisitCfgDao autoVisitCfgDao;
 	
 	/**
     * @description DAO-getter方法
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @return IAutoVisitCfgDao dao接口对象
     */
 	public IAutoVisitCfgDao getAutoVisitCfgDao() {
 		return autoVisitCfgDao;
 	}
 	
 	 /**
     * @description DAO-setter方法
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param autoVisitCfgDao dao对象
     */
 	public void setAutoVisitCfgDao(IAutoVisitCfgDao autoVisitCfgDao) {
 		this.autoVisitCfgDao = autoVisitCfgDao;
 	}
 	
 	/**
 	 * 
 	 * @description 增加数据
 	 * @version V1.0.0
 	 * @title
 	 * <AUTHOR> <EMAIL>
 	 * @see com.nci.tunan.cs.impl.csConfig.service.IAutoVisitCfgService#addAutoVisitCfg(java.util.List)
 	 * @param autoVisitCfgBO 对象
 	 */
	public void addAutoVisitCfg(List<AutoVisitCfgBO> autoVisitCfgBO) {
		logger.debug("<======AutoVisitCfgServiceImpl--addAutoVisitCfg======>");
		for(AutoVisitCfgBO feAutoVisitCfgBO:autoVisitCfgBO){
			//@invalid 增加数据
			autoVisitCfgDao.addAutoVisitCfg(BeanUtils.copyProperties(AutoVisitCfgPO.class, feAutoVisitCfgBO));	
		}
	} 	
 	
 	 /**
     * @description 修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param autoVisitCfgBO 对象
     * @return AutoVisitCfgBO 修改结果
     */
	public AutoVisitCfgBO updateAutoVisitCfg(AutoVisitCfgBO autoVisitCfgBO) {
		logger.debug("<======AutoVisitCfgServiceImpl--updateAutoVisitCfg======>");
		//@invalid 修改数据
		AutoVisitCfgPO autoVisitCfgPO = autoVisitCfgDao.updateAutoVisitCfg(BeanUtils.copyProperties(AutoVisitCfgPO.class, autoVisitCfgBO));
		return BeanUtils.copyProperties(AutoVisitCfgBO.class, autoVisitCfgPO);
	}

	 /**
     * @description 删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param autoVisitCfgBO 对象
     * @return boolean 删除是否成功
     */
	public boolean deleteAutoVisitCfg(AutoVisitCfgBO autoVisitCfgBO) {
		logger.debug("<======AutoVisitCfgServiceImpl--deleteAutoVisitCfg======>");
		//@invalid 删除数据
		return autoVisitCfgDao.deleteAutoVisitCfg(BeanUtils.copyProperties(AutoVisitCfgPO.class, autoVisitCfgBO));
	}
	
	 /**
     * @description 查询单条数据
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param autoVisitCfgBO 对象
     * @return AutoVisitCfgBO 查询结果对象
     */
	public AutoVisitCfgBO findAutoVisitCfg(AutoVisitCfgBO autoVisitCfgBO) {
		logger.debug("<======AutoVisitCfgServiceImpl--findAutoVisitCfg======>");
		//@invalid 查询单条数据
		AutoVisitCfgPO autoVisitCfgBackPO = autoVisitCfgDao.findAutoVisitCfg(BeanUtils.copyProperties(AutoVisitCfgPO.class, autoVisitCfgBO));
		AutoVisitCfgBO autoVisitCfgBackBO = BeanUtils.copyProperties(AutoVisitCfgBO.class, autoVisitCfgBackPO);
		return autoVisitCfgBackBO;
	}
	
	/**
     * @description 查询所有数据
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param autoVisitCfgBO 对象
     * @return List<AutoVisitCfgBO> 查询结果List
     */
	public List<AutoVisitCfgBO> findAllAutoVisitCfg(AutoVisitCfgBO  autoVisitCfgBO) {
		logger.debug("<======AutoVisitCfgServiceImpl--findAllAutoVisitCfg======>");
		AutoVisitCfgPO autoVisitCfgPO = BeanUtils.copyProperties(AutoVisitCfgPO.class, autoVisitCfgBO);
		//@invalid 查询所有数据
		return BeanUtils.copyList(AutoVisitCfgBO.class, autoVisitCfgDao.findAllAutoVisitCfg(autoVisitCfgPO));
	}
	
	 /**
     * @description 查询数据条数
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param autoVisitCfgBO 对象
     * @return int 查询结果条数
     */
	public int findAutoVisitCfgTotal(AutoVisitCfgBO autoVisitCfgBO) {
		logger.debug("<======AutoVisitCfgServiceImpl--findAutoVisitCfgTotal======>");
		//@invalid 查询数据条数
		AutoVisitCfgPO autoVisitCfgPO = BeanUtils.copyProperties(AutoVisitCfgPO.class, autoVisitCfgBO);
		return autoVisitCfgDao.findAutoVisitCfgTotal(autoVisitCfgPO);
	}
	
	 /**
	  * 
	  * @description 分页查询数据
	  * @version V1.0.0
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @see com.nci.tunan.cs.impl.csConfig.service.IAutoVisitCfgService#queryAutoVisitCfgForPage(com.nci.tunan.cs.model.bo.AutoVisitCfgBO, com.nci.udmp.framework.model.CurrentPage)
	  * @param autoVisitCfgBO 对象
	  * @param currentPage 分页对象
	  * @return CurrentPage<AutoVisitCfgBO>
	  */
	public CurrentPage<AutoVisitCfgBO> queryAutoVisitCfgForPage(AutoVisitCfgBO autoVisitCfgBO, CurrentPage<AutoVisitCfgBO> currentPage) {
		logger.debug("<======AutoVisitCfgServiceImpl--queryAutoVisitCfgForPage======>");
		AutoVisitCfgPO autoVisitCfgPO = BeanUtils.copyProperties(AutoVisitCfgPO.class, autoVisitCfgBO);
		autoVisitCfgPO.getData().put("busi_prd_id_list",autoVisitCfgBO.getBusiPrdIdList());
		autoVisitCfgPO.getData().put("service_code_list",autoVisitCfgBO.getServiceCodeList());
		//@invalid 分页查询数据
		CurrentPage<AutoVisitCfgBO> currentPageBO = BeanUtils.copyCurrentPage(AutoVisitCfgBO.class, autoVisitCfgDao.queryAutoVisitCfgForPage(autoVisitCfgPO, BeanUtils.copyCurrentPage(AutoVisitCfgPO.class, currentPage)));
		return currentPageBO;
	}
	
	 /**
     * @description 批量增加数据
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param autoVisitCfgBOList 对象列表
     * @return boolean 批量添加是否成功
     */
	public boolean batchSaveAutoVisitCfg(List<AutoVisitCfgBO> autoVisitCfgBOList) {
		logger.debug("<======AutoVisitCfgServiceImpl--batchSaveAutoVisitCfg======>");
		List<AutoVisitCfgPO> autoVisitCfgPOList = BeanUtils.copyList(AutoVisitCfgPO.class, autoVisitCfgBOList);
		//@invalid 批量增加数据
		return autoVisitCfgDao.batchSaveAutoVisitCfg(autoVisitCfgPOList);
	}	
	
	/**
     * @description 批量修改数据
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param autoVisitCfgBOList 对象列表
     * @return boolean 批量修改是否成功
     */
	public boolean batchUpdateAutoVisitCfg(List<AutoVisitCfgBO> autoVisitCfgBOList) {
		logger.debug("<======AutoVisitCfgServiceImpl--batchUpdateAutoVisitCfg======>");
		//@invalid 批量修改数据
		List<AutoVisitCfgPO> autoVisitCfgPOList = BeanUtils.copyList(AutoVisitCfgPO.class, autoVisitCfgBOList);
		return autoVisitCfgDao.batchUpdateAutoVisitCfg(autoVisitCfgPOList);
	}
	
	/**
     * @description 批量删除数据
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param autoVisitCfgBOList 对象列表
     * @return boolean 批量删除是否成功
     */
	public boolean batchDeleteAutoVisitCfg(List<AutoVisitCfgBO> autoVisitCfgBOList) {
		logger.debug("<======AutoVisitCfgServiceImpl--batchDeleteAutoVisitCfg======>");
		//@invalid 批量删除数据
		List<AutoVisitCfgPO> autoVisitCfgPOList = BeanUtils.copyList(AutoVisitCfgPO.class, autoVisitCfgBOList);
		return autoVisitCfgDao.batchDeleteAutoVisitCfg(autoVisitCfgPOList);
	}
	
	/**
     * @description 查询所有数据 ，重新组装为map
     * @version V1.0.0
     * @title
     * <AUTHOR>
     * @param autoVisitCfgBO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	public List<Map<String, Object>> findAllMapAutoVisitCfg(AutoVisitCfgBO autoVisitCfgBO) {
		logger.debug("<======AutoVisitCfgServiceImpl--findAllMapAutoVisitCfg======>");
		AutoVisitCfgPO autoVisitCfgPO = BeanUtils.copyProperties(AutoVisitCfgPO.class, autoVisitCfgBO);
		//@invalid 查询所有数据 ，重新组装为map
		return autoVisitCfgDao.findAllMapAutoVisitCfg(autoVisitCfgPO);
	}
	
	/**
	 * 
	 * @description 校验当前的操作是否符合自动发起回访的配置
	 * @version 1.0
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.impl.csConfig.service.IAutoVisitCfgService#checkAutoVisitCfg(com.nci.tunan.cs.model.bo.AutoVisitCfgBO)
	 * @param autoVisitCfgBO 待校验信息
	 * @return
	 */
	public List<AutoVisitCfgBO> checkAutoVisitCfg(AutoVisitCfgBO autoVisitCfgBO){
		AutoVisitCfgPO autoVisitCfgPO = BeanUtils.copyProperties(AutoVisitCfgPO.class,autoVisitCfgBO);
		autoVisitCfgPO.setBusiPrdIdList(autoVisitCfgBO.getBusiPrdIdList());
		//@invalid 校验当前的操作是否符合自动发起回访的配置
		List<AutoVisitCfgPO> autoVisitCfgPOs = autoVisitCfgDao.checkAutoVisitCfg(autoVisitCfgPO);
		return BeanUtils.copyList(AutoVisitCfgBO.class,autoVisitCfgPOs);
	}
 }
 