package com.nci.tunan.cs.impl.csConfig.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.dao.ICsOptionCfgDao;
import com.nci.tunan.cs.dao.impl.CsOptionCfgDaoImpl;
import com.nci.tunan.cs.impl.csConfig.service.ICsOptionCfgService;
import com.nci.tunan.cs.model.bo.CsBpoPrintOrgCfgBO;
import com.nci.tunan.cs.model.bo.OptionConfigBO;
import com.nci.tunan.cs.model.po.CsBpoPrintOrgCfgPO;
import com.nci.tunan.cs.model.po.OptionConfigPO;
import com.nci.tunan.cs.model.vo.OptionConfigVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.CollectionUtilEx;

public class CsOptionCfgServiceImpl implements ICsOptionCfgService{
	
    private static Logger logger = LoggerFactory.getLogger(Constants.CS_LOG_NAME_PRINT);
    
    @Autowired
    @Qualifier("PA_cs_csOptionCfgDao")
    private ICsOptionCfgDao csOptionCfgDao;
    
    
	@Override
	public CurrentPage<OptionConfigBO> queryCfg(OptionConfigBO optionConfigBO ,CurrentPage currentPage) {
		 logger.debug("保全选项配置分页查询【service开始】");
		 OptionConfigPO optionConfigPO = new OptionConfigPO();
		 optionConfigPO = BeanUtils.copyProperties(OptionConfigPO.class, optionConfigBO);
	     CurrentPage<OptionConfigPO> currentPo = new CurrentPage<OptionConfigPO>();
	     currentPo = BeanUtils.copyCurrentPage(OptionConfigPO.class, currentPage);
	     CurrentPage<OptionConfigPO> currentPo1 = new CurrentPage<OptionConfigPO>();
	     //查询配置信息   
	     currentPo1 = csOptionCfgDao.queryCfg(optionConfigPO, currentPo);
	     CurrentPage<OptionConfigBO> currentBo = new CurrentPage<OptionConfigBO>();
	     currentBo = BeanUtils.copyCurrentPage(OptionConfigBO.class, currentPo1);
	     logger.debug("保全选项配置分页查询【service结束】");
	     return currentBo;
	}

	@Override
	public String saveCfgStatus(String cfgListId ,OptionConfigBO optionConfigBO) {
		logger.debug("保全选项配置修改【service开始】");
		String[] cfgIds = cfgListId.split(",");
		int resultNum = 0;
		for(String cfgId : cfgIds){
			optionConfigBO.setListId(new BigDecimal(cfgId));
			OptionConfigPO optionConfigPO = new OptionConfigPO();
			optionConfigPO = BeanUtils.copyProperties(OptionConfigPO.class, optionConfigBO);
			resultNum = csOptionCfgDao.updateOptionCfg(optionConfigPO);
		}
		if(resultNum == 0){
			return "修改失败";
		}
        logger.debug("保全选项配置修改【service结束】");
        return  "";
	}

	@Override
	public OptionConfigBO saveCfg(OptionConfigBO optionConfigBO) {
		logger.debug("保全选项配置保存【service开始】");
		OptionConfigPO optionConfigPO = new OptionConfigPO();
		optionConfigPO = BeanUtils.copyProperties(OptionConfigPO.class, optionConfigBO);
		OptionConfigPO optionConfigPO2 = csOptionCfgDao.addOptionCfg(optionConfigPO);
        OptionConfigBO optionConfigBO2 = new OptionConfigBO(); 
        //optionConfigBO2 = BeanUtils.copyProperties(OptionConfigBO.class, optionConfigPO2);
        logger.debug("保全选项配置保存【service结束】");
        return  optionConfigBO2;
	}

	@Override
	public OptionConfigBO querySignalCfg(OptionConfigBO optionConfigBO) {
		 logger.debug("保全选项配置查询【service开始】");
		 OptionConfigPO optionConfigPO = new OptionConfigPO();
		 optionConfigPO = BeanUtils.copyProperties(OptionConfigPO.class, optionConfigBO);
	     //查询配置信息   
		 optionConfigPO = csOptionCfgDao.queryCfg(optionConfigPO);
	     OptionConfigBO optionConfigBO2 = null;
	     if(!optionConfigPO.getData().isEmpty()){
	    	 optionConfigBO2 = BeanUtils.copyProperties(OptionConfigBO.class, optionConfigPO);
		     logger.debug("保全选项配置查询【service结束】");
	     }
		 return optionConfigBO2;
	}
	
	@Override
	public List<OptionConfigBO> queryListCfg(OptionConfigBO optionConfigBO) {
		 logger.debug("保全选项配置查询【service开始】");
		 OptionConfigPO optionConfigPO = new OptionConfigPO();
		 optionConfigPO = BeanUtils.copyProperties(OptionConfigPO.class, optionConfigBO);
	     //查询配置信息   
		 List<OptionConfigPO> optionConfigPOs = csOptionCfgDao.queryCfgList(optionConfigPO);
		 List<OptionConfigBO> optionConfigBO2 = new ArrayList<OptionConfigBO>();
	     if(CollectionUtilEx.isNotEmpty(optionConfigPOs)){
	    	 optionConfigBO2 = BeanUtils.copyList(OptionConfigBO.class, optionConfigPOs);
		     logger.debug("保全选项配置查询【service结束】");
	     }
		 return optionConfigBO2;
	}

}
