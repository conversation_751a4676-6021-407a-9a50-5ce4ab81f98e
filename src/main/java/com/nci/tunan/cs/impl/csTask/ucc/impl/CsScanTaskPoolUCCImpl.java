package com.nci.tunan.cs.impl.csTask.ucc.impl;

import com.nci.tunan.cs.impl.csTask.service.ICsScanTaskPoolService;
import com.nci.tunan.cs.impl.csTask.ucc.ICsScanTaskPoolUCC;
import com.nci.tunan.cs.model.bo.CsScanTaskPoolBO;
import com.nci.tunan.cs.model.vo.CsScanTaskPoolVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
/**
 * 
 * @description 查询保全待扫描任务
 * <AUTHOR> <EMAIL> 
 * @date 2015-05-15 下午3:44:33 
 * @.belongToModule 保全子系统
 */
public class CsScanTaskPoolUCCImpl implements ICsScanTaskPoolUCC {
	/**
	 * ICsScanTaskPoolService
	 */
	private ICsScanTaskPoolService csScanTaskPoolService;
	/**
	 * 
	 * @description 查询保全待扫描任务
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL> 
	 * @see com.nci.tunan.cs.impl.csTask.ucc.ICsScanTaskPoolUCC#queryScanTaskPool(com.nci.tunan.cs.model.vo.CsScanTaskPoolVO, com.nci.udmp.framework.model.CurrentPage)
	 * @param csScanTaskPoolVO csScanTaskPoolVO对象
	 * @param currentPage 分页对象
	 * @return CsScanTaskPoolBO
	 */
	@Override
	public CurrentPage<CsScanTaskPoolVO> queryScanTaskPool(CsScanTaskPoolVO csScanTaskPoolVO,
			CurrentPage<CsScanTaskPoolVO> currentPage) {
		CurrentPage<CsScanTaskPoolBO> boPage = BeanUtils.copyCurrentPage(CsScanTaskPoolBO.class, currentPage);
		CsScanTaskPoolBO csScanTaskPoolBO = BeanUtils.copyProperties(CsScanTaskPoolBO.class, csScanTaskPoolVO);
		//1.根据调价查询保全待扫描任务
		boPage = csScanTaskPoolService.queryScanTaskPool(csScanTaskPoolBO, boPage);		
		// 2.将保全待扫描任务返回
		currentPage = BeanUtils.copyCurrentPage(CsScanTaskPoolVO.class, boPage);
		return currentPage;
	}
	public ICsScanTaskPoolService getCsScanTaskPoolService() {
		return csScanTaskPoolService;
	}
	public void setCsScanTaskPoolService(
			ICsScanTaskPoolService csScanTaskPoolService) {
		this.csScanTaskPoolService = csScanTaskPoolService;
	}
	
}
