package com.nci.tunan.cs.impl.peripheral.ucc.r00101000853.impl;

import com.nci.tunan.cs.impl.peripheral.service.r00101000853.IWXPEdorITTestService;
import com.nci.tunan.cs.impl.peripheral.ucc.r00101000853.IWXPEdorITTestUcc;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00101000853.vo.Input;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00101000853.vo.Output;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;

/**
 * 
 * @description 保全试算-投连退保接口 服务编码：P00001000854
 * <AUTHOR> <EMAIL> 
 * @date 2021-11-16 下午8:24:39 
 * @.belongToModule cs-保全子系统
 */
public class WXPEdorITTestUccImpl implements IWXPEdorITTestUcc{
	/**
	 * 保全试算-投连退保接口Service类
	 */
	private IWXPEdorITTestService wXPEdorITTestService;
	
	public IWXPEdorITTestService getwXPEdorITTestService() {
		return wXPEdorITTestService;
	}

	public void setwXPEdorITTestService(IWXPEdorITTestService wXPEdorITTestService) {
		this.wXPEdorITTestService = wXPEdorITTestService;
	}
    /**
     * 
     * @description 保全试算-投连退保
     * @version v1.0.0
     * @title
     * <AUTHOR> <EMAIL> 
     * @see com.nci.tunan.cs.impl.peripheral.ucc.r00101000853.IWXPEdorITTestUcc#exitEdor(com.nci.tunan.cs.interfaces.peripheral.exports.r00101000853.vo.Input)
     * @param inputData Input对象
     * @return Output
     */
	@Override
	public Output exitEdor(Input inputData) {
		//1.处理响应码
		SysHeader sysHeader=CommonHeaderDeal.getSYSHEADERTHREAD();
		Output outputData = new Output();
		try {
			//2.投连退保试算t_surrender/t_cs_accept_change/t_policy_holder
			outputData = wXPEdorITTestService.wXPEdorExit(inputData);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return outputData;
	}

}
