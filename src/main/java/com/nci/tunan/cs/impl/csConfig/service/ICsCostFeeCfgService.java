package com.nci.tunan.cs.impl.csConfig.service;

import java.util.ArrayList;
import java.util.List;

import com.nci.tunan.cs.model.bo.CostFeeCfgBO;
import com.nci.tunan.cs.model.vo.CostFeeCfgVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.signature.IService;
/**
 * 
 * @description  工本费配置
 * @<NAME_EMAIL> 
 * @date 2017-3-16  下午5:21:54
 * @.belongToModule 保全系统-工本费配置
 */
public interface ICsCostFeeCfgService extends IService{
	/**
	 * 
	 * @description 查询所有工本费信息
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param costFeeCfgBO 工本费配置信息
	 * @return
	 */
	public List<CostFeeCfgBO> queryCostFeeCfgList(CostFeeCfgBO costFeeCfgBO);
	/**
	 * 
	 * @description 分页查询
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param currentPage 分页内容
	 * @param costFeeCfgBO 工本费配置信息
	 * @return
	 */
	public CurrentPage<CostFeeCfgBO> queryCostFeeCfgForPages(CurrentPage<CostFeeCfgBO> currentPage,CostFeeCfgBO costFeeCfgBO);
	/**
	 * 
	 * @description 删除
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param costFeeBO 工本费配置信息
	 * @return
	 */
	public boolean deleteCostFeeCfg(CostFeeCfgBO costFeeBO);
	/**
	 * 
	 * @description 修改
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param costFeeList 工本费配置列表
	 * @return
	 */
	public boolean updateCostFeeCfg(ArrayList<CostFeeCfgBO> costFeeList);
	
	/**
	 * 
	 * @description 批量添加工本费
	 * @version
	 * @title
	 * @<NAME_EMAIL> 
	 * @param copyList 复制批量添加工本费集合
	 */
	public boolean batchSaveCsCostFeeCfg(List<CostFeeCfgBO> copyList);
	/**
	 * 
	 * @description 删除
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param costFeeBO  工本费配置信息
	 */
	public void   updateCostFeeCfg(CostFeeCfgBO costFeeBO);
	/**
	 * 
	 * @description 单条语句查询
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param costFeeBO 工本费配置信息
	 * @return
	 */

	public CostFeeCfgBO  findCostFeeCfgByCfgId(CostFeeCfgBO costFeeBO);
}
