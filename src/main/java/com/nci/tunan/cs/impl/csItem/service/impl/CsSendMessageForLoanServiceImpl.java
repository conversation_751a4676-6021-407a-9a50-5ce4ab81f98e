package com.nci.tunan.cs.impl.csItem.service.impl;

import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.nci.core.common.factory.BOServiceFactory;
import com.nci.core.common.interfaces.vo.BizNoticeMessageVO;
import com.nci.core.common.interfaces.vo.MultiFileInfoVO;
import com.nci.core.common.interfaces.vo.system.feeconfirm.PremArapVO;
import com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO;
import com.nci.tunan.cs.dao.IChangeDao;
import com.nci.tunan.cs.dao.ICsContractBusiProdDao;
import com.nci.tunan.cs.dao.ICsPolicyChangeDao;
import com.nci.tunan.cs.impl.csItem.service.ICsEndorseLNService;
import com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService;
import com.nci.tunan.cs.model.po.ChangePO;
import com.nci.tunan.cs.model.po.CsContractBusiProdPO;
import com.nci.tunan.cs.model.po.CsPolicyChangePO;
import com.nci.tunan.cs.model.vo.PolicyLoanSetMsgVO;
import com.nci.tunan.pa.dao.INoticeDao;
import com.nci.tunan.pa.dao.IPolicyHolderDao;
import com.nci.tunan.pa.dao.IPremArapDao;
import com.nci.tunan.pa.interfaces.model.po.LoadNoticeTerminationPO;
import com.nci.tunan.pa.interfaces.model.po.PolicyHolderInfoPO;
import com.nci.tunan.pa.interfaces.model.po.PremArapPO;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.xml.transform.XmlHelper;

/**
 * 为贷款相关业务提供发送短信的方法
 * @description 
 * @<NAME_EMAIL> 
 * @date 2015-05-15
 * @.belongToModule 保全系统
 */
public class CsSendMessageForLoanServiceImpl implements ICsSendMessageForLoanService{
	/**
	 * 日志工具
	 */
	protected Logger logger = LoggerFactory.getLogger();
	/**
	 * 保单投保人表Dao
	 */
	@Autowired
	private IPolicyHolderDao policyHolderDao;
	/**
	 * 应收应付表Dao接口
	 */
	@Autowired
	private IPremArapDao premArapDao;
	/**
	 * 保单变更表
	 */
	@Autowired
	private IChangeDao changeDao;
	/**
	 * 保单变更履历表Dao
	 */
	@Autowired
	private ICsPolicyChangeDao csPolicyChangeDao;
	/**
	 * 保单货款Service
	 */
	@Autowired
	private ICsEndorseLNService csEndorseLNService;
	/**
	 * 险种DAO
	 */
	@Autowired
	/**险种表Dao*/
	private ICsContractBusiProdDao csContractBusiProdDao;
	/**
	 * 通知
	 */
	@Autowired
	private INoticeDao noticeDao;
	/**
	 * 1、针对自动清偿业务（未选自动续贷），贷款到期前7天发送。发送给投保人
	 * 尊敬的客户，您尾号POLICYCODESORT的保单于LOANAPPLYDATE申请的贷款将于LOANENDDATE到期，系统将自动为您办理清偿业务，贷款本息和MONEY元，
	 * 请您在到期日之前将以上金额存入尾号为BANKSORTCODE的BANKNAME账户，若未能划款成功，贷款将按照逾期处理。如有问题请致电新华保险客服热线95567【新华保险】
	 * @description
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService#sendMessagePerPaymentForRF(com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO)
	 * @param csPolicyAccountStreamBO 货款Bo
	 * @return
	 */
	@Override
	public boolean sendMessagePerPaymentForRF(CsPolicyAccountStreamBO csPolicyAccountStreamBO){
		BizNoticeMessageVO bizNoticeMessageVO = new BizNoticeMessageVO();
		bizNoticeMessageVO.setNoticeCode(new BigDecimal(122)); // @invalid   @invalid 通知编码
		bizNoticeMessageVO.setOrgId(new BigDecimal(86)); // @invalid   @invalid 机构id
		bizNoticeMessageVO.setReceiveObjName(csPolicyAccountStreamBO.getPolicyHolderName()==null?"":csPolicyAccountStreamBO.getPolicyHolderName()); // @invalid   @invalid 通知对象姓名
		bizNoticeMessageVO.setReceiveObj(csPolicyAccountStreamBO.getPolicyHolderId()==null?"":csPolicyAccountStreamBO.getPolicyHolderId().toString()); // @invalid   @invalid 通知对象编码
		bizNoticeMessageVO.setSendForm(new BigDecimal(1)); // @invalid   @invalid 发送形式 (必输项)1 短信2 邮件
		bizNoticeMessageVO.setMobiles(Arrays.asList(csPolicyAccountStreamBO.getPolicyHolderMobileTel()==null?"":csPolicyAccountStreamBO.getPolicyHolderMobileTel())); // @invalid  对象手机号(发送形式为“短信”时必输项，可录入多个手机号)
		bizNoticeMessageVO.setBusiCode(csPolicyAccountStreamBO.getPolicyCode());
		 // @invalid  @invalid 通知内容对象： 按照各业务通知类型模板，上送参数名与参数值
		Map<String, String> template = new HashMap<String, String>();
		List<Map<String, String>> templateList = new ArrayList<Map<String, String>>();
		template.put("POLICYCODESORT",csPolicyAccountStreamBO.getPolicyCode()==null?"":getLastFourChart(csPolicyAccountStreamBO.getPolicyCode())); // @invalid  @invalid 处理后四位
		template.put("LOANAPPLYDATE",csPolicyAccountStreamBO.getLoanStartDate()==null?"":dateFormate(csPolicyAccountStreamBO.getLoanStartDate()));
		template.put("LOANENDDATE",csPolicyAccountStreamBO.getRepayDueDate()==null?"":dateFormate(csPolicyAccountStreamBO.getRepayDueDate()));
		template.put("MONEY",csPolicyAccountStreamBO.getInterestCapital()==null?"0":csPolicyAccountStreamBO.getInterestCapital().toString());
		template.put("BANKSORTCODE",csPolicyAccountStreamBO.getBankAccountForRF()==null?"":getLastFourChart(csPolicyAccountStreamBO.getBankAccountForRF())); // @invalid  @invalid 处理后四位
		template.put("BANKNAME",csPolicyAccountStreamBO.getBankNameForRF()==null?"":csPolicyAccountStreamBO.getBankNameForRF()); // @invalid  @invalid 银行名称
		templateList.add(template);
		bizNoticeMessageVO.setBusiCode(csPolicyAccountStreamBO.getPolicyCode());
		bizNoticeMessageVO.setTemplateList(templateList);
		bizNoticeMessageVO.setSmsmultiFlg("1"); // @invalid  @invalid  短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setMultiFlg("1"); // @invalid   @invalid 短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setTaskid("messageperpaymentforrf"); // @invalid   @invalid 任务ID(必输项)
		
		boolean out = false;
		try {
			logger.info("短信入参报文"+XmlHelper.classToXml(bizNoticeMessageVO));
			out = BOServiceFactory.getBizNoticeUCC().sendBizNotice(bizNoticeMessageVO);
		} catch (Exception e) {
			out = false;
			logger.warn("<======CsSendMessageForLoanImpl--sendMessagePerPaymentForRF--短信发送异常======>"+e.getMessage());
			throw new RuntimeException(e);
		}
		logger.info("短信发送结果为 =" + out);
		return out;
	}
	
	/**
	 * @description 2、针对自动续贷业务（选自动续贷），贷款到期前7天发送。发送给投保人
	 	尊敬的客户，您尾号POLICYCODESORT的保单于LOANAPPLYDATE申请贷款并约定自动续贷，贷款将于LOANENDDATE到期，自动续贷本金MONEY元，续贷利率为RATE。
	 	本次续贷您需清偿利息及本金差额共PAIDACOUNT元，请您在到期日之前将以上金额存入尾号为BANKSORTCODE的BANKNAME账户，
	 	若未能划款成功，贷款将按照逾期处理。如有问题请致电新华保险客服热线95567【新华保险】
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService#sendMessagePerPaymentForRF(com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO)
	 * @param csPolicyAccountStreamBO 货款Bo
	 * @return 
	 */
	@Override
	public boolean sendMessagePerPaymentForRL(CsPolicyAccountStreamBO csPolicyAccountStreamBO){
		BizNoticeMessageVO bizNoticeMessageVO = new BizNoticeMessageVO();
		bizNoticeMessageVO.setNoticeCode(new BigDecimal(123)); // @invalid   @invalid 通知编码
		bizNoticeMessageVO.setOrgId(new BigDecimal(86)); // @invalid  @invalid 机构id
		bizNoticeMessageVO.setReceiveObjName(csPolicyAccountStreamBO.getPolicyHolderName()==null?"":csPolicyAccountStreamBO.getPolicyHolderName()); // @invalid   @invalid 通知对象姓名
		bizNoticeMessageVO.setReceiveObj(csPolicyAccountStreamBO.getPolicyHolderId()==null?"":csPolicyAccountStreamBO.getPolicyHolderId().toString()); // @invalid   @invalid 通知对象编码
		bizNoticeMessageVO.setSendForm(new BigDecimal(1)); // @invalid   @invalid 发送形式 (必输项)1 短信2 邮件
		bizNoticeMessageVO.setBusiCode(csPolicyAccountStreamBO.getPolicyCode());
		bizNoticeMessageVO.setMobiles(Arrays.asList(csPolicyAccountStreamBO.getPolicyHolderMobileTel()==null?"":csPolicyAccountStreamBO.getPolicyHolderMobileTel())); // @invalid  对象手机号(发送形式为“短信”时必输项，可录入多个手机号)
		 // @invalid  @invalid 通知内容对象： 按照各业务通知类型模板，上送参数名与参数值
		Map<String, String> template = new HashMap<String, String>();
		List<Map<String, String>> templateList = new ArrayList<Map<String, String>>();
		template.put("POLICYCODESORT",csPolicyAccountStreamBO.getPolicyCode()==null?"":getLastFourChart(csPolicyAccountStreamBO.getPolicyCode())); // @invalid  @invalid 处理后四位
		template.put("LOANAPPLYDATE",csPolicyAccountStreamBO.getLoanStartDate()==null?"":dateFormate(csPolicyAccountStreamBO.getLoanStartDate()));
		template.put("LOANENDDATE",csPolicyAccountStreamBO.getRepayDueDate()==null?"":dateFormate(csPolicyAccountStreamBO.getRepayDueDate()));
		template.put("MONEY",csPolicyAccountStreamBO.getCapitalBalance()==null?"0":csPolicyAccountStreamBO.getCapitalBalance().toString());
		template.put("RATE",csPolicyAccountStreamBO.getRateForRL()==null?"0":csPolicyAccountStreamBO.getRateForRL().toString());
		template.put("PAIDACOUNT",csPolicyAccountStreamBO.getInterestBalance()==null?"0":csPolicyAccountStreamBO.getInterestBalance().toString()); // @invalid  @invalid 利息及本金差额，在这里，实际上只是到期利息而已
		template.put("BANKSORTCODE",csPolicyAccountStreamBO.getBankAccountForRF()==null?"":getLastFourChart(csPolicyAccountStreamBO.getBankAccountForRF())); // @invalid  @invalid 处理后四位
		template.put("BANKNAME",csPolicyAccountStreamBO.getBankNameForRF()==null?"":csPolicyAccountStreamBO.getBankNameForRF()); // @invalid  @invalid 银行名称
		templateList.add(template);
		bizNoticeMessageVO.setBusiCode(csPolicyAccountStreamBO.getPolicyCode());
		bizNoticeMessageVO.setTemplateList(templateList);
		bizNoticeMessageVO.setSmsmultiFlg("1"); // @invalid   @invalid 短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setMultiFlg("1"); // @invalid   @invalid 短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setTaskid("messageperpaymentforrl"); // @invalid   @invalid 任务ID(必输项)
		
		boolean out = false;
		try {
			logger.info("短信入参报文"+XmlHelper.classToXml(bizNoticeMessageVO));
			out = BOServiceFactory.getBizNoticeUCC().sendBizNotice(bizNoticeMessageVO);
		} catch (Exception e) {
			out = false;
			logger.warn("<======CsSendMessageForLoanImpl--sendMessagePerPaymentForRL--短信发送异常======>"+e.getMessage());
			throw new RuntimeException(e);
		}
		logger.info("短信发送结果为 =" + out);
		return out;
	}
	
	/**
	 * @description 3、自动清偿成功短信提醒，自动清偿生效后次日发送。发送给投保人
	 * 尊敬的客户，您尾号POLICYCODESORT的保单已自动清偿贷款成功，清偿金额MONEY元。感谢您的支持【新华保险】
	  * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService#sendMessagePerPaymentForRF(com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO)
	 * @param csPolicyAccountStreamBO 货款Bo
	 * @return
	 */
	@Override
	public boolean sendMessageSuccessForRF(CsPolicyAccountStreamBO csPolicyAccountStreamBO){
		BizNoticeMessageVO bizNoticeMessageVO = new BizNoticeMessageVO();
		bizNoticeMessageVO.setNoticeCode(new BigDecimal(124)); // @invalid   @invalid 通知编码
		bizNoticeMessageVO.setOrgId(new BigDecimal(86)); // @invalid   @invalid 机构id
		bizNoticeMessageVO.setReceiveObjName(csPolicyAccountStreamBO.getPolicyHolderName()==null?"":csPolicyAccountStreamBO.getPolicyHolderName()); // @invalid   @invalid 通知对象姓名
		bizNoticeMessageVO.setReceiveObj(csPolicyAccountStreamBO.getPolicyHolderId()==null?"":csPolicyAccountStreamBO.getPolicyHolderId().toString()); // @invalid   @invalid 通知对象编码
		bizNoticeMessageVO.setSendForm(new BigDecimal(1)); // @invalid   @invalid 发送形式 (必输项)1 短信2 邮件
		bizNoticeMessageVO.setMobiles(Arrays.asList(csPolicyAccountStreamBO.getPolicyHolderMobileTel()==null?"":csPolicyAccountStreamBO.getPolicyHolderMobileTel())); // @invalid  @invalid  对象手机号(发送形式为“短信”时必输项，可录入多个手机号)
		bizNoticeMessageVO.setBusiCode(csPolicyAccountStreamBO.getAcceptCode());
		 // @invalid  @invalid 通知内容对象： 按照各业务通知类型模板，上送参数名与参数值
		Map<String, String> template = new HashMap<String, String>();
		List<Map<String, String>> templateList = new ArrayList<Map<String, String>>();
		template.put("POLICYCODESORT",csPolicyAccountStreamBO.getPolicyCode()==null?"":getLastFourChart(csPolicyAccountStreamBO.getPolicyCode())); // @invalid  @invalid 处理后四位
		template.put("MONEY",csPolicyAccountStreamBO.getInterestCapital()==null?"0":csPolicyAccountStreamBO.getInterestCapital().toString()); // @invalid  @invalid 续贷本金、清偿金额
		templateList.add(template);
		bizNoticeMessageVO.setTemplateList(templateList);
		bizNoticeMessageVO.setSmsmultiFlg("1"); // @invalid   @invalid 短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setMultiFlg("1"); // @invalid   @invalid 短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setTaskid("messagesuccessforrf"); // @invalid   @invalid 任务ID(必输项)
		
		boolean out = false;
		try {
			logger.info("短信入参报文"+XmlHelper.classToXml(bizNoticeMessageVO));
			out = BOServiceFactory.getBizNoticeUCC().sendBizNotice(bizNoticeMessageVO);
		} catch (Exception e) {
			out = false;
			logger.warn("<======CsSendMessageForLoanImpl--sendMessageSuccessForRF--短信发送异常======>"+e.getMessage());
			throw new RuntimeException(e);
		}
		logger.info("短信发送结果为 =" + out);
		return out;
	}
	
	/**
	 * @description 4、贷款续贷成功短信提醒，自动续贷生效后次日发送。发送给投保人
	 * 	尊敬的客户，您尾号POLICYCODESORT的保单已自动续贷成功，续贷本金MONEY元，续贷利率为RATE，续贷到期将自动为您办理清偿业务，
	 * 	请您在到期日前将PAIDACOUNT存入尾号为BANKSORTCODE的BANKNAME账户。感谢您的支持【新华保险】
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService#sendMessagePerPaymentForRF(com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO)
	 * @param csPolicyAccountStreamBO 货款Bo
	 * @return
	 */
	@Override
	public boolean sendMessageSuccessForRL(CsPolicyAccountStreamBO csPolicyAccountStreamBO){
		BizNoticeMessageVO bizNoticeMessageVO = new BizNoticeMessageVO();
		bizNoticeMessageVO.setNoticeCode(new BigDecimal(125)); // @invalid   @invalid 通知编码
		bizNoticeMessageVO.setOrgId(new BigDecimal(86)); // @invalid   @invalid 机构id
		bizNoticeMessageVO.setReceiveObjName(csPolicyAccountStreamBO.getPolicyHolderName()==null?"":csPolicyAccountStreamBO.getPolicyHolderName()); // @invalid  @invalid 通知对象姓名
		bizNoticeMessageVO.setReceiveObj(csPolicyAccountStreamBO.getPolicyHolderId()==null?"":csPolicyAccountStreamBO.getPolicyHolderId().toString()); // @invalid   @invalid 通知对象编码
		bizNoticeMessageVO.setSendForm(new BigDecimal(1)); // @invalid  @invalid  发送形式 (必输项)1 短信2 邮件
		bizNoticeMessageVO.setBusiCode(csPolicyAccountStreamBO.getAcceptCode());
		bizNoticeMessageVO.setMobiles(Arrays.asList(csPolicyAccountStreamBO.getPolicyHolderMobileTel()==null?"":csPolicyAccountStreamBO.getPolicyHolderMobileTel())); // @invalid   @invalid 对象手机号(发送形式为“短信”时必输项，可录入多个手机号)
		 // @invalid  @invalid 通知内容对象： 按照各业务通知类型模板，上送参数名与参数值
		Map<String, String> template = new HashMap<String, String>();
		List<Map<String, String>> templateList = new ArrayList<Map<String, String>>();
		template.put("POLICYCODESORT",csPolicyAccountStreamBO.getPolicyCode()==null?"":getLastFourChart(csPolicyAccountStreamBO.getPolicyCode())); // @invalid  @invalid 处理后四位
		template.put("MONEY",csPolicyAccountStreamBO.getCapitalBalance()==null?"0":csPolicyAccountStreamBO.getCapitalBalance().toString());
		template.put("RATE",csPolicyAccountStreamBO.getRateForRL()==null?"0":csPolicyAccountStreamBO.getRateForRL().toString());
		template.put("PAIDACOUNT",csPolicyAccountStreamBO.getInterestCapital()==null?"0":csPolicyAccountStreamBO.getInterestCapital().toString()); // @invalid  @invalid 利息及本金差额，在这里，实际上只是到期利息而已
		template.put("BANKSORTCODE",csPolicyAccountStreamBO.getBankAccountForRF()==null?"":getLastFourChart(csPolicyAccountStreamBO.getBankAccountForRF())); // @invalid  @invalid 处理后四位
		template.put("BANKNAME",csPolicyAccountStreamBO.getBankNameForRF()==null?"":csPolicyAccountStreamBO.getBankNameForRF()); // @invalid  @invalid 银行名称
		templateList.add(template);
		bizNoticeMessageVO.setTemplateList(templateList);
		bizNoticeMessageVO.setSmsmultiFlg("1"); // @invalid   @invalid 短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setMultiFlg("1"); // @invalid  @invalid  短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setTaskid("messagesuccessforrl"); // @invalid   @invalid 任务ID(必输项)
		
		boolean out = false;
		try {
			logger.info("短信入参报文"+XmlHelper.classToXml(bizNoticeMessageVO));
			out = BOServiceFactory.getBizNoticeUCC().sendBizNotice(bizNoticeMessageVO);
		} catch (Exception e) {
			out = false;
			logger.warn("<======CsSendMessageForLoanImpl--sendMessageSuccessForRL--短信发送异常======>"+e.getMessage());
			throw new RuntimeException(e);
		}
		logger.info("短信发送结果为 =" + out);
		return out;
	}
	
	/**
	 * @description 5、自动清偿首次划款失败且原因为余额不足的短信提醒，首次划款失败的次日发送：发送给投保人
	 * 尊敬的客户您好，您尾号POLICYCODESORT保单已自动清偿，余额不足划款失败，
	 * 请您尽快将本次需清偿金额MONEY元存入尾号为BANKSORTCODE的BANKNAME账户。若未能划款成功，原贷款将逾期处理，利率将上浮。
	  * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService#sendMessagePerPaymentForRF(com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO)
	 * @param csPolicyAccountStreamBO 货款Bo
	 * @return
	 */
	@Override
	public boolean sendMessageFailForRF(CsPolicyAccountStreamBO csPolicyAccountStreamBO){
		BizNoticeMessageVO bizNoticeMessageVO = new BizNoticeMessageVO();
		bizNoticeMessageVO.setNoticeCode(new BigDecimal(128)); // @invalid   @invalid 通知编码
		bizNoticeMessageVO.setOrgId(new BigDecimal(86)); // @invalid  @invalid  机构id
		bizNoticeMessageVO.setReceiveObjName(csPolicyAccountStreamBO.getPolicyHolderName()==null?"":csPolicyAccountStreamBO.getPolicyHolderName()); // @invalid  @invalid  通知对象姓名
		bizNoticeMessageVO.setReceiveObj(csPolicyAccountStreamBO.getPolicyHolderId()==null?"":csPolicyAccountStreamBO.getPolicyHolderId().toString()); // @invalid  通知对象编码
		bizNoticeMessageVO.setSendForm(new BigDecimal(1)); // @invalid  发送形式 (必输项)1 短信2 邮件
		bizNoticeMessageVO.setBusiCode(csPolicyAccountStreamBO.getAcceptCode());
		bizNoticeMessageVO.setMobiles(Arrays.asList(csPolicyAccountStreamBO.getPolicyHolderMobileTel()==null?"":csPolicyAccountStreamBO.getPolicyHolderMobileTel())); // @invalid  对象手机号(发送形式为“短信”时必输项，可录入多个手机号)
		 // @invalid 通知内容对象： 按照各业务通知类型模板，上送参数名与参数值
		Map<String, String> template = new HashMap<String, String>();
		List<Map<String, String>> templateList = new ArrayList<Map<String, String>>();
		template.put("POLICYCODESORT",csPolicyAccountStreamBO.getPolicyCode()==null?"":getLastFourChart(csPolicyAccountStreamBO.getPolicyCode())); // @invalid 处理后四位
		template.put("MONEY",csPolicyAccountStreamBO.getInterestCapital()==null?"0":csPolicyAccountStreamBO.getInterestCapital().toString()); // @invalid 本息和
		template.put("BANKSORTCODE",csPolicyAccountStreamBO.getBankAccountForRF()==null?"":getLastFourChart(csPolicyAccountStreamBO.getBankAccountForRF())); // @invalid 处理后四位
		template.put("BANKNAME",csPolicyAccountStreamBO.getBankNameForRF()==null?"":csPolicyAccountStreamBO.getBankNameForRF()); // @invalid 银行名称
		templateList.add(template);
		bizNoticeMessageVO.setTemplateList(templateList);
		bizNoticeMessageVO.setSmsmultiFlg("1"); // @invalid  短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setMultiFlg("1"); // @invalid  短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setTaskid("messagefailforrf"); // @invalid  任务ID(必输项)
		
		boolean out = false;
		try {
			logger.info("短信入参报文"+XmlHelper.classToXml(bizNoticeMessageVO));
			out = BOServiceFactory.getBizNoticeUCC().sendBizNotice(bizNoticeMessageVO);
		} catch (Exception e) {
			out = false;
			logger.warn("<======CsSendMessageForLoanImpl--sendMessageFailForRF--短信发送异常======>"+e.getMessage());
			throw new RuntimeException(e);
		}
		logger.info("短信发送结果为 =" + out);
		return out;
	}
	
	/**
	 * @description 6、自动续贷首次划款失败且原因为余额不足的短信提醒，首次划款失败的次日发送：发送给投保人：
	 * 尊敬的客户您好，您尾号POLICYCODESORT保单已自动续贷，余额不足划款失败，
	 * 请您尽快将本次需清偿金额MONEY元存入尾号为BANKSORTCODE的BANKNAME账户。若未能划款成功，原贷款将逾期处理，利率将上浮。
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService#sendMessagePerPaymentForRF(com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO)
	 * @param csPolicyAccountStreamBO 货款Bo
	 * @return
	 */
	@Override
	public boolean sendMessageFailForRL(CsPolicyAccountStreamBO csPolicyAccountStreamBO){
		/******************** 批量短信公共部分************************/
		BizNoticeMessageVO bizNoticeMessageVO = new BizNoticeMessageVO();
		bizNoticeMessageVO.setMultiFlg("3"); // @invalid  批量
    	bizNoticeMessageVO.setSendForm(new BigDecimal("1"));
    	bizNoticeMessageVO.setOrgId(new BigDecimal("86"));
    	bizNoticeMessageVO.setNoticeCode(new BigDecimal("127"));
		InetAddress addr;
		String ip = "";
		try {
			addr = InetAddress.getLocalHost();
			ip = addr.getHostAddress().toString();  // @invalid 获取本机ip  
		} catch (UnknownHostException e1) {
			e1.printStackTrace();
			logger.info("--------获取ip 报错-------");
			throw new RuntimeException(e1);
		}  
    	bizNoticeMessageVO.setLocalIp(ip);
    	List<MultiFileInfoVO> mulElementBOList = new ArrayList<MultiFileInfoVO>();
    	/******************** 批量短信公共部分************************/
     	MultiFileInfoVO mulElementVO = new MultiFileInfoVO();
		Date finishTime = WorkDateUtil.getWorkDate();
		mulElementVO.setSendSMSDate(DateUtilsEx.formatToString(DateUtilsEx.addDay(finishTime, 1), "yyyy-MM-dd"));
		if(null == csPolicyAccountStreamBO.getPolicyCode()){
			return false;
		}
		LoadNoticeTerminationPO fullOrganNamePO = new LoadNoticeTerminationPO();
		fullOrganNamePO.setPolicyCode(csPolicyAccountStreamBO.getPolicyCode());
		List<LoadNoticeTerminationPO> regList = noticeDao.findorgOrgRelInfo(fullOrganNamePO);
		String supportOrgCode = "";
		String branchOrgCode = "";
		for (LoadNoticeTerminationPO regRel : regList) {
			 // @invalid  分公司
			if (regRel.getOrganGrade().equals("02")) {
				branchOrgCode = regRel.getOrganName();
			}
			 // @invalid  支公司
			if (regRel.getOrganGrade().equals("03")) {
				supportOrgCode = regRel.getOrganName();
			}
		}
		mulElementVO.setSupportOrgCode(branchOrgCode); // @invalid 支公司机构代码（6位机构代码）
		mulElementVO.setBranchOrgCode(supportOrgCode); // @invalid  分公司机构代码（4位机构代码）
		mulElementVO.setBusiCode(csPolicyAccountStreamBO.getAcceptCode());
		mulElementVO.setSendSMSPhone(csPolicyAccountStreamBO.getPolicyHolderMobileTel()==null?"":csPolicyAccountStreamBO.getPolicyHolderMobileTel());
		
		List<Map<String, String>> templateList = new ArrayList<Map<String, String>>();
		Map<String, String> template = new HashMap<String, String>();
		template.put("POLICYCODESORT",csPolicyAccountStreamBO.getPolicyCode()==null?"":getLastFourChart(csPolicyAccountStreamBO.getPolicyCode())); // @invalid 处理后四位
		template.put("MONEY",csPolicyAccountStreamBO.getInterestBalance()==null?"0":csPolicyAccountStreamBO.getInterestBalance().toString()); // @invalid 续贷本金、清偿金额
		template.put("BANKSORTCODE",csPolicyAccountStreamBO.getBankAccountForRF()==null?"":getLastFourChart(csPolicyAccountStreamBO.getBankAccountForRF())); // @invalid 处理后四位
		template.put("BANKNAME",csPolicyAccountStreamBO.getBankNameForRF()==null?"":csPolicyAccountStreamBO.getBankNameForRF()); // @invalid 银行名称
		mulElementVO.setTemplateMap(template);
		mulElementBOList.add(mulElementVO);
		 // @invalid 通知内容对象： 按照各业务通知类型模板，上送参数名与参数值
		bizNoticeMessageVO.setMultiFileInfo(mulElementBOList);
		boolean out = false;
		try {
			logger.info("短信入参报文"+XmlHelper.classToXml(bizNoticeMessageVO));
			out = BOServiceFactory.getBizNoticeUCC().sendBizNotice(bizNoticeMessageVO);
		} catch (Exception e) {
			out = false;
			logger.warn("<======CsSendMessageForLoanImpl--sendMessageFailForRL--短信发送异常======>"+e.getMessage());
			throw new RuntimeException(e);
		}
		logger.info("短信发送结果为 =" + out);
		return out;
	}
	/**
	 * @description 7、自动清偿/续贷失败短信提醒，自动清偿/自动续贷逾期终止后次日发送。发送给投保人：
	 * 尊敬的用户,您尾号POLICYCODESORT的保单在进行POLICYTYPE过程中,因划款失败,原货代将按照逾期处理（逾期利率上浮X%），
	 * 请您尽快至公司柜台办理续贷或清偿手续。如有问题请及时致电新华保险客服热线95567【新华保险】
	  * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService#sendMessagePerPaymentForRF(com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO)
	 * @param csPolicyAccountStreamBO 货款Bo
	 * @return
	 */
	@Override
	public boolean sendMessageOverTimeForRFAndRL(CsPolicyAccountStreamBO csPolicyAccountStreamBO){
		BizNoticeMessageVO bizNoticeMessageVO = new BizNoticeMessageVO();
		bizNoticeMessageVO.setNoticeCode(new BigDecimal(126)); // @invalid  通知编码
		bizNoticeMessageVO.setOrgId(new BigDecimal(86)); // @invalid  机构id
		bizNoticeMessageVO.setReceiveObjName(csPolicyAccountStreamBO.getPolicyHolderName()==null?"":csPolicyAccountStreamBO.getPolicyHolderName()); // @invalid  通知对象姓名
		bizNoticeMessageVO.setReceiveObj(csPolicyAccountStreamBO.getPolicyHolderId()==null?"":csPolicyAccountStreamBO.getPolicyHolderId().toString()); // @invalid  通知对象编码
		bizNoticeMessageVO.setSendForm(new BigDecimal(1)); // @invalid  发送形式 (必输项)1 短信2 邮件
		bizNoticeMessageVO.setBusiCode(csPolicyAccountStreamBO.getAcceptCode());
		bizNoticeMessageVO.setMobiles(Arrays.asList(csPolicyAccountStreamBO.getPolicyHolderMobileTel()==null?"":csPolicyAccountStreamBO.getPolicyHolderMobileTel())); // @invalid  对象手机号(发送形式为“短信”时必输项，可录入多个手机号)
		 // @invalid 通知内容对象： 按照各业务通知类型模板，上送参数名与参数值
		Map<String, String> template = new HashMap<String, String>();
		List<Map<String, String>> templateList = new ArrayList<Map<String, String>>();
		template.put("POLICYCODESORT",csPolicyAccountStreamBO.getPolicyCode()==null?"":getLastFourChart(csPolicyAccountStreamBO.getPolicyCode())); // @invalid 处理后四位
		template.put("POLICYTYPE",csPolicyAccountStreamBO.getOverRate()==null?"0":csPolicyAccountStreamBO.getOverRate().toString()); // @invalid 上浮利率
		templateList.add(template);
		bizNoticeMessageVO.setTemplateList(templateList);
		bizNoticeMessageVO.setSmsmultiFlg("1"); // @invalid  短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setMultiFlg("1"); // @invalid  短信条数：(必输项)1（单条），2（多条）
		bizNoticeMessageVO.setTaskid("sendmessagefailforrfandrl"); // @invalid  任务ID(必输项)
		
		boolean out = false;
		try {
			logger.info("短信入参报文"+XmlHelper.classToXml(bizNoticeMessageVO));
			out = BOServiceFactory.getBizNoticeUCC().sendBizNotice(bizNoticeMessageVO);
		} catch (Exception e) {
			out = false;
			logger.warn("<======CsSendMessageForLoanImpl--sendMessageFailForRFandRL--短信发送异常======>"+e.getMessage());
			throw new RuntimeException(e);
		}
		logger.info("短信发送结果为 =" + out);
		return out;
	}
	/**
	 * @description 对外提供调用方法，逾期终止处理流程，逾期终止，不再进入下次自动清偿或续贷对象，修改自动清偿状态为清偿失败。等数据库字段添加之后再说
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService#sendMessagePerPaymentForRF(com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO)
	 * @param acceptCode 受理号
	 * @return
	 */
	@Override
	public boolean sendMessageOverTimeForRFAndRL(String acceptCode){
		boolean flag = true;
		 // 1. 查询受理号所属类型
		ChangePO changePO = new ChangePO();
		changePO.setAcceptCode(acceptCode);
		List<ChangePO> allChanges = changeDao.findAllChangeByAcceptCode(changePO);
		if(CollectionUtilEx.isNotEmpty(allChanges)){ // @invalid 如果不为空
			ChangePO changePO2 = allChanges.get(0);
			String serviceCode = changePO2.getServiceCode();
			if("RF".equals(serviceCode)||"RL".equals(serviceCode)){ // @invalid 如果是清偿或者续贷
				BigDecimal acceptId = changePO2.getAcceptId();
				 // 2.查询POLICYCODESORT和OVERRATE
				CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
				csPolicyChangePO.setAcceptId(acceptId);
				List<CsPolicyChangePO> findAllCsPolicyChange = csPolicyChangeDao.findAllCsPolicyChange(csPolicyChangePO);
				if(CollectionUtilEx.isNotEmpty(findAllCsPolicyChange)){
					List<String> policyList = new ArrayList<String>();
					for (CsPolicyChangePO csPolicyChangePO2 : findAllCsPolicyChange) {
						String policyCode = csPolicyChangePO2.getPolicyCode();
						BigDecimal policyId = csPolicyChangePO2.getPolicyId();
						BigDecimal changeId = csPolicyChangePO2.getChangeId();
						BigDecimal policyChgId = csPolicyChangePO2.getPolicyChgId();
						if(policyList.contains(policyCode)){ // @invalid 如果包含保单号就跳过循环不再发送短信了
							continue;
						}
						CsPolicyAccountStreamBO csPolicyAccountStreamBO = new CsPolicyAccountStreamBO();
						csPolicyAccountStreamBO.setPolicyCode(policyCode);
						 // 3.查询投保人信息
						PolicyHolderInfoPO policyHolderInfoPO = new PolicyHolderInfoPO();
						policyHolderInfoPO.setPolicyCode(policyCode);
						List<PolicyHolderInfoPO> policyHolderInfoList = policyHolderDao.findPolicyHolderInfoByPolicyCode(policyHolderInfoPO);
						if(CollectionUtilEx.isNotEmpty(policyHolderInfoList)){
							String policyHolderName = policyHolderInfoList.get(0).getCustomerName();
							BigDecimal policyHolderId = (BigDecimal)policyHolderInfoList.get(0).getCustomerId();
							String policyHolderMobileTel = policyHolderInfoList.get(0).getMobileTel();
							csPolicyAccountStreamBO.setPolicyHolderName(policyHolderName);
							csPolicyAccountStreamBO.setPolicyHolderId(policyHolderId);
							csPolicyAccountStreamBO.setPolicyHolderMobileTel(policyHolderMobileTel);
						}
						 //4.查询主险险种
						CsContractBusiProdPO csContractBusiProdPO = new CsContractBusiProdPO();
						csContractBusiProdPO.setChangeId(changeId);
						csContractBusiProdPO.setPolicyChgId(policyChgId);
						csContractBusiProdPO.setPolicyId(policyId);
						csContractBusiProdPO.setOldNew("0");
						List<CsContractBusiProdPO> csContractBusiProdPOs = csContractBusiProdDao.findAllCsContractBusiProdMaster(csContractBusiProdPO);
						if(CollectionUtilEx.isNotEmpty(csContractBusiProdPOs)){
							for(CsContractBusiProdPO csContractBusiProdPO2:csContractBusiProdPOs){
								BigDecimal busiItemId = csContractBusiProdPO2.getBusiItemId();
								BigDecimal busiProdId = csContractBusiProdPO2.getBusiPrdId();
								 // 5.查询超期利息
								PolicyLoanSetMsgVO queryLoanSetMsg = csEndorseLNService.queryLoanSetMsg(policyId, busiItemId, busiProdId, policyChgId, changeId);
								// @invalid 多主险保单，其中只有一个主险有贷款场景，查询贷款信息时查询结果返回为空，导致程序出现空指针。
								if(queryLoanSetMsg!=null && (queryLoanSetMsg.getOverDueRateValue() != null || queryLoanSetMsg.getSecondStageRateVal() != null) ){
									 // @invalid BigDecimal initRate = queryLoanSetMsg.getRateValue();
									BigDecimal overDueRateValue = queryLoanSetMsg.getOverDueRateValue();
									BigDecimal secondStageRateVal = queryLoanSetMsg.getSecondStageRateVal();
									String isCarefullyChoose = queryLoanSetMsg.getIsCarefullyChoose();
									if("1".equals(isCarefullyChoose) && secondStageRateVal != null){ // @invalid 如果是精选，超期利息为二利息
										secondStageRateVal = secondStageRateVal.multiply(new BigDecimal(100));
										csPolicyAccountStreamBO.setOverRate(secondStageRateVal);
									}else if (null != overDueRateValue){
										overDueRateValue = overDueRateValue.multiply(new BigDecimal(100));
										csPolicyAccountStreamBO.setOverRate(overDueRateValue);
									}
									policyList.add(policyCode);
									csPolicyAccountStreamBO.setAcceptCode(acceptCode);
									boolean nowFlag = this.sendMessageOverTimeForRFAndRL(csPolicyAccountStreamBO);
									if(nowFlag == false){
										flag = false;
									}
								}
							}
							
						}
						
					}
				}
			}
		}
		return flag;
	}
	/**
	 * @description 对外提供调用，续贷、清偿余额不足导致划款失败短信提醒
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService#sendMessagePerPaymentForRF(com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO)
	 * @param premArapVO 应收应付
	 */
	@Override
	public void sendMessageFailForRFOrRL(PremArapVO premArapVO){
		String businessType = premArapVO.getBusinessType(); // @invalid 2051清偿2053续贷
		String feeStatus = premArapVO.getFeeStatus(); // @invalid 状态03失败
		BigDecimal failTimes = premArapVO.getFailTimes(); // @invalid 失败次数，1
		String fundsRtnCode = premArapVO.getFundsRtnCode(); // @invalid 失败原因：E1004
		
		if("03".equals(feeStatus)&&BigDecimal.ONE.equals(failTimes)&&"E1004".equals(fundsRtnCode)){ // @invalid 如果失败、并且第一次失败、原因为余额不足
			if("2051".equals(businessType)||"2053".equals(businessType)){
				String uninumber = premArapVO.getUnitNumber();
				PremArapPO premArapPO = new PremArapPO();
				premArapPO.setUnitNumber(uninumber);
				List<PremArapPO> findAllPremArap = premArapDao.findAllPremArap(premArapPO);
				if(CollectionUtilEx.isNotEmpty(findAllPremArap)){
					List<String> policyList = new ArrayList<String>();
					for (PremArapPO premArapPO2 : findAllPremArap) {
						String policyCode = premArapPO2.getPolicyCode();
						if(policyList.contains(policyCode)){ // @invalid 如果包含保单号就跳过循环不再发送短信了
							continue;
						}
						 // @invalid 查询投保人信息
						PolicyHolderInfoPO policyHolderInfoPO = new PolicyHolderInfoPO();
						policyHolderInfoPO.setPolicyCode(policyCode);
						List<PolicyHolderInfoPO> policyHolderInfoList = policyHolderDao.findPolicyHolderInfoByPolicyCode(policyHolderInfoPO);
						if(CollectionUtilEx.isNotEmpty(policyHolderInfoList)){
							String bankAccountForRF = premArapVO.getBankAccount();
							String BankCodeForRF = premArapVO.getBankCode();
							BigDecimal feeAmount = premArapVO.getFeeAmount();
							String policyHolderName = policyHolderInfoList.get(0).getCustomerName();
							BigDecimal policyHolderId = (BigDecimal)policyHolderInfoList.get(0).getCustomerId();
							String policyHolderMobileTel = policyHolderInfoList.get(0).getMobileTel();
							if(feeAmount==null||BigDecimal.ZERO.equals(feeAmount)){
								 // @invalid @invalid TODO 无有效值则查询报单表获取金额
								feeAmount = BigDecimal.ZERO;
							}
							String bankNameForRF = 
									com.nci.udmp.framework.tag.i18n.CodeTable.getCodeDesc("APP___PAS__DBUSER.T_BANK", BankCodeForRF);
							CsPolicyAccountStreamBO csPolicyAccountStreamBO = new CsPolicyAccountStreamBO();
							
							csPolicyAccountStreamBO.setPolicyHolderName(policyHolderName);
							csPolicyAccountStreamBO.setPolicyHolderId(policyHolderId);
							csPolicyAccountStreamBO.setPolicyHolderMobileTel(policyHolderMobileTel);
							csPolicyAccountStreamBO.setPolicyCode(policyCode);
							csPolicyAccountStreamBO.setBankAccountForRF(bankAccountForRF);
							csPolicyAccountStreamBO.setBankNameForRF(bankNameForRF);
							if("2051".equals(businessType)){ // @invalid 清偿
								csPolicyAccountStreamBO.setInterestCapital(feeAmount);
								csPolicyAccountStreamBO.setAcceptCode(premArapVO.getBusinessCode());
								sendMessageFailForRF(csPolicyAccountStreamBO);
								policyList.add(policyCode);
							}else if("2053".equals(businessType)){ // @invalid 续贷
								csPolicyAccountStreamBO.setAcceptCode(premArapVO.getBusinessCode());
								csPolicyAccountStreamBO.setInterestBalance(feeAmount);
								sendMessageFailForRL(csPolicyAccountStreamBO);
								policyList.add(policyCode);
							}
						}
					}
					
				}
			}
		}
		
	}
	
	/**
	 * @description 获取后四位
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.impl.csItem.service.ICsSendMessageForLoanService#sendMessagePerPaymentForRF(com.nci.tunan.cs.batch.autoRFandRL.bo.CsPolicyAccountStreamBO)
	 * @param resourse 入参
	 */
	private String getLastFourChart(String resourse){
		int length = resourse.length();
		return resourse.substring(length-4);
	}
	/**
	 *  日期格式转换
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param date 日期
	 * @return
	 */
	private String dateFormate(Date date){
		return DateUtilsEx.formatToString(date, "yyyy年MM月dd日");
	}

}
