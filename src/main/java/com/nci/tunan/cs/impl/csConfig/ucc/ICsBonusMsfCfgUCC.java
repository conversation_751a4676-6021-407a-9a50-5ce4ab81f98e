package com.nci.tunan.cs.impl.csConfig.ucc;

import java.util.List;

import com.nci.tunan.cs.model.vo.BonusMsfCfgVO;
import com.nci.tunan.cs.model.vo.CsBasiBonusMsfCfgVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.signature.IUCC;
/**
 * 
 * @description  红利短信区域配置
 * <AUTHOR> <EMAIL> 
 * @date 2017-12-04  
 * @.belongToModule 保全子系统
 */
public interface ICsBonusMsfCfgUCC extends IUCC{
	
	/**
	 * 
	 * @description 保存红利短息范围配置信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bonusMsfCfgVo  保全红利短信范围配置基础VO
	 * @return
	 */
	public BonusMsfCfgVO saveBonusMsfCfg(BonusMsfCfgVO bonusMsfCfgVo);
	
	
	/**
	 * 
	 * @description 删除红利短息范围配置信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param rangeIds 主键id
	 * @return
	 */
	public boolean detBonusMsfCfg(String rangeIds);
	
	
	/**
	 * 
	 * @description 分页加载红利短息范围配置信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bonusMsfCfgVo 保全红利短信范围配置基础VO
	 * @param currentPage  分页对象
	 * @return
	 */
	public CurrentPage<BonusMsfCfgVO> loadBonusMsfCfgList(BonusMsfCfgVO bonusMsfCfgVo, CurrentPage<BonusMsfCfgVO>  currentPage);

	
	
	/**
	 * 
	 * @description 根据条件查询数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bonusMsfCfgVo 保全红利短信范围配置基础VO
	 * @return
	 */
	public List<BonusMsfCfgVO> findAllBounsMsfCfgList(BonusMsfCfgVO bonusMsfCfgVo);
	
	/**
	 * 
	 * @param bonusMsfCfgVo
	 * @param currentPage
	 * @return
	 */
	/**
	 *  
	 * @description 分页加载红利短息范围配置历史信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bonusMsfCfgHisVo  保全红利短信范围配置基础VO
	 * @param currentPage 分页对象
	 * @return
	 */
	public CurrentPage<BonusMsfCfgVO> loadBonusMsfCfgHisList(BonusMsfCfgVO bonusMsfCfgHisVo, CurrentPage<BonusMsfCfgVO>  currentPage);
	
	/**
	 * 
	 * @description  新增配置信息
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL>
	 * @param bonusMsfCfgVo 保全红利短信范围配置基础VO
	 * @return
	 */
	public CsBasiBonusMsfCfgVO addBonusMsfCfg(BonusMsfCfgVO bonusMsfCfgVo);

}
