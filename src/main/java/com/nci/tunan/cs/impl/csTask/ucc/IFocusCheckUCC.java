package com.nci.tunan.cs.impl.csTask.ucc;

import java.util.Map;

import com.nci.tunan.cs.model.vo.AutoworkTaskListVO;
import com.nci.tunan.cs.model.vo.CsFocusCheckVO;
import com.nci.udmp.framework.model.CurrentPage;

public interface IFocusCheckUCC {
	/**
	 * 在任务池中获取任务到个人任务池
	 * @param csFocusCheckVO
	 * @return
	 */
	public Map<String,String> getFocusCheckTask(CsFocusCheckVO csFocusCheckVO);
	
	/**
	 * 将个人池任务返回共享池
	 * @param csFocusCheckVO
	 * @return
	 */
	public boolean backToPublicPool(CsFocusCheckVO csFocusCheckVO);
	/**
	 * 查询个人任务池
	 * @param csFocusCheckVO
	 * @param currentPage
	 * @param IsPageFlag 默认1：分页  ；0 不分页
	 * @return
	 */
	public CurrentPage queryCsFocusCheckTask(CsFocusCheckVO csFocusCheckVO, CurrentPage<CsFocusCheckVO> currentPage,String IsPageFlag);
	/**
	 * 根据保单受理号获取任务号
	 * @description
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @return
	 */
	public String getTaskId(String acceptCode);
	
	/**
	 * 校验用户是否具有复核权限
	 * @description
	 * @version
	 * @return true 有 false 没有
	 */
	public boolean checkUserPower();

	/**
	 * 保单进能否进入保全复合
	 * @description
	 * @version
	 * @return true 可以 false 不可以
	 */
	public boolean focusORreview(CsFocusCheckVO csFocusCheckVO);
	
	/**
	 * 集中复核任务池 
	 * 申请任务
	 * 从共享池申请任务到个人池
	 * @return 提示信息
	 */
	public String getCheckTaskinFocus(AutoworkTaskListVO autoworkTaskListVO);
	
	 /***RM:46089 集中复核任务池任务筛选
	  *  <EMAIL>	
	  */
	public CurrentPage queryCurrReviewData (CsFocusCheckVO csFocusCheckVO, CurrentPage<CsFocusCheckVO> currentPage);

	/**
	 * 
	 * @description 校验是否可以退回共享池
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param csFocusCheckVO
	 * @return
	 */
    public String checkBackRule(CsFocusCheckVO csFocusCheckVO);
	
}
