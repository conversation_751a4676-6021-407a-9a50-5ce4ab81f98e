package com.nci.tunan.cs.impl.csItem.service;

import java.util.List;

import com.nci.tunan.cs.model.bo.CsEndorseCBCABO;
import com.nci.tunan.cs.model.bo.CsPrecontProductBO;
import com.nci.tunan.cs.model.vo.CsEndorseCBCAVO;
import com.nci.udmp.framework.signature.IService;
/**
 * 
 * @description  万能险 基本保额减少 service接口
 * <AUTHOR> <EMAIL>
 * @date 2015年10月30日 下午3:15:47
 * @.belongToModule 保全子系统
 */
public interface ICsEndorseCBService extends IService, ICSItemBaseService {

	/**
	 * 
	* @description 查询 约定变更计划
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPrecontProductBO  预约险种信息变更表BO
	 * @return
	 */
	public List<CsPrecontProductBO> queryCsPrecontProducts(CsPrecontProductBO csPrecontProductBO);

	/**
	 *  
	 * @description 更新预约变更计划
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csPrecontProductBO 预约险种信息变更表BO
	 * @return
	 */
	public boolean updateCsPrecontProducts(CsPrecontProductBO csPrecontProductBO);
	
	/**
	 * 
	 * @description 查询 变更前 信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csEndorseCBCAVO  入参
	 * @return
	 */
	public List<CsEndorseCBCABO> queryCsEndorsecCBBefore(CsEndorseCBCABO csEndorseCBCABO);
	
	/**
	 * 
	 * @description 查询预订 变更计划
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csEndorseCBCABO  入参
	 * @return
	 */
	public List<CsEndorseCBCABO> queryPrecontProduct(CsEndorseCBCABO csEndorseCBCABO);
	
	/**
	    * 
	    * @description 保存 减保信息
	    * @version
	    * @title
	    * <AUTHOR> <EMAIL>
	    * @see com.nci.tunan.cs.impl.csItem.ucc.ICsEndorseCBUCC#saveCsEndorseCBVOs(java.util.List, java.util.List)
	    * @param csEndorseCBCAVOs   减保的vo
	    * @param csEndorseCBPrecontVOs 更新的预约变更计划的的vo
	    * @return
	    */
	public boolean saveCsEndorseCBVOs(List<CsEndorseCBCABO> csEndorseCBCABOs,List<CsEndorseCBCABO> csEndorseCBPrecontBOs);
	
}
