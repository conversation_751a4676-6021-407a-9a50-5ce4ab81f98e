package com.nci.tunan.cs.impl.csConfig.ucc.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.ObjectUtils;

import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.impl.csConfig.service.ICsOptionCfgService;
import com.nci.tunan.cs.impl.csConfig.ucc.ICsOptionCfgUCC;
import com.nci.tunan.cs.model.bo.CsBpoPrintOrgCfgBO;
import com.nci.tunan.cs.model.bo.OptionConfigBO;
import com.nci.tunan.cs.model.vo.CsBpoPrintOrgCfgVO;
import com.nci.tunan.cs.model.vo.OptionConfigVO;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.bean.BeanUtils;

public class CsOptionCfgUCCImpl implements ICsOptionCfgUCC{

	@Autowired
	@Qualifier("PA_csOptionCfgService")
	private ICsOptionCfgService csOptionCfgService; 
	
    private static Logger logger = LoggerFactory.getLogger(Constants.CS_LOG_NAME_PRINT);
	
	@Override
	public CurrentPage<OptionConfigVO> queryCfg(OptionConfigVO optionConfigVO , CurrentPage currentPage) {
		OptionConfigBO optionConfigBO = new OptionConfigBO();
		optionConfigBO = BeanUtils.copyProperties(OptionConfigBO.class, optionConfigVO);
        CurrentPage<OptionConfigBO> currentPageBO = new CurrentPage<OptionConfigBO>();
        //查询相关配置信息
        currentPageBO = csOptionCfgService.queryCfg(optionConfigBO, currentPage);
        CurrentPage<OptionConfigVO> opCurrentPage = new CurrentPage<OptionConfigVO>();
        opCurrentPage = BeanUtils.copyCurrentPage(OptionConfigVO.class, currentPageBO);
        return opCurrentPage;
	}

	@Override
	public String saveCfgStatus(String cfgListId , OptionConfigVO optionConfigVO) {
		Date date = WorkDateUtil.getWorkDate(AppUserContext.getCurrentUser());
		OptionConfigBO optionConfigBO = new OptionConfigBO();
		optionConfigBO = BeanUtils.copyProperties(OptionConfigBO.class, optionConfigVO);
		//修改相关配置信息	
		String optionConfigBO2 = csOptionCfgService.saveCfgStatus(cfgListId,optionConfigBO);
		return optionConfigBO2;
	}

	@Override
	public String saveCfg(OptionConfigVO optionConfigVO) {
		OptionConfigBO optionConfigBO = new OptionConfigBO();
		optionConfigBO = BeanUtils.copyProperties(OptionConfigBO.class, optionConfigVO);
		//保存相关配置信息
		OptionConfigBO optionConfigBO2 = csOptionCfgService.querySignalCfg(optionConfigBO);
		OptionConfigVO optionConfigVO2 = new OptionConfigVO();
		if (ObjectUtils.isEmpty(optionConfigBO2)) {
			logger.debug("保全选项配置增加【当前配置数据不存在已配置数据，开始添加】");
			optionConfigBO2 = csOptionCfgService.saveCfg(optionConfigBO);
			return "";
		}
		optionConfigVO2 = BeanUtils.copyProperties(OptionConfigVO.class, optionConfigBO2);
		return "此选项已存在，不支持重复新增。";
	}

}
