package com.nci.tunan.cs.impl.peripheral.ucc.r00101000054;

import com.nci.tunan.cs.interfaces.peripheral.exports.r00101000054.vo.ReceiveResultReqDataVO;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00101000054.vo.ReceiveResultResDataVO;

/**
 * 
 * @description 领取形式变更（初始化）UCC接口
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @.belongToModule 保单管理系统-接收形式
 * @date 2015年5月15日 下午2:20:36
 */
public interface IReceiveFormUCC {
	
	/**
	 * 
	 * @description 查询结果
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param receiveResultReqDataVO 对象
	 * @return
	 */
	public ReceiveResultResDataVO queryRefoResultInfo(ReceiveResultReqDataVO receiveResultReqDataVO);


}
