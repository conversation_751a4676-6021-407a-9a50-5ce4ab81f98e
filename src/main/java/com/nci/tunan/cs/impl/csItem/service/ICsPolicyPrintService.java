package com.nci.tunan.cs.impl.csItem.service;

import com.nci.tunan.cs.model.bo.ContractSignBO;
import com.nci.tunan.cs.model.bo.CsAcceptChangeBO;
import com.nci.tunan.cs.model.bo.CsPolicyPrintFeeBO;
import com.nci.tunan.cs.model.bo.CsPolicyPrintTaskBO;
import com.nci.tunan.cs.model.bo.CsPolicyPrintSignBO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.framework.signature.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 
 * @description 保险合同打印 service接口
 * @<NAME_EMAIL>
 * @version V1.0.0
 * @.belongToModule 保全子系统
 * @date 2021年10月9日 上午10:15:58
 */
public interface ICsPolicyPrintService extends IService{

	/**
	 * 
	 * @description 查询符合条件的保单打印任务信息列表
	 * @version v1.0
	 * @title
	 * @<NAME_EMAIL> 
	 * @param csPolicyQueryPrintBO 保险合同打印页面查询BO
	 * @param currentPage 
	 */
	@SuppressWarnings("rawtypes")
	public CurrentPage<CsPolicyPrintTaskBO> queryTask(CsPolicyPrintTaskBO csPolicyPrintTaskBO, CurrentPage currentPage);

	/**
	 * 
	 * @description 创建保险合同待打印任务
	 * @version v1.0
	 * @title
	 * @<NAME_EMAIL> 
	 * @param csAcceptChangeBO 受理变更管理BO
	 * @param policyCode 保单号
	 */
	public void addCsPolicyPrintTask(CsAcceptChangeBO csAcceptChangeBO, String policyCode);

	/**
	 * 
	 * @description 推送/禁止外包打印功能
	 * @version v1.0
	 * @title
	 * <AUTHOR> 
	 * @param csPolicyPrintTaskBOList
	 * @return 
	 */
	public void csChangePrint(List<CsPolicyPrintTaskBO> csPolicyPrintTaskBOList);

	/**
	 * 
	 * @description 柜面打印
	 * @version v1.0
	 * @title
	 * @<NAME_EMAIL> 
	 * @param csPolicyPrintTaskBO 保险合同打印页面查询BO
	 * @return 返回保单打印任务BO
	 */
	public CsPolicyPrintTaskBO policyPrint(CsPolicyPrintTaskBO csPolicyPrintTaskBO);

	/**
	 * 
	 * @description 制单费用结算
	 * @version v1.0
	 * @title
	 * @<NAME_EMAIL> 
	 * @param csPolicyPrintTaskBO 保险合同制单费用查询BO
	 * @return 返回制单费用列表currentPage
	 */
	public CurrentPage<CsPolicyPrintFeeBO> queryMakeFeeInfoForPage(CurrentPage<CsPolicyPrintFeeBO> currentPage);
	/**
	 * @description 制单费用明细清单下载 mode 0-全部 1-合格 2-不合格
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param csPolicyPrintFeeBO
	 * @param mode
	 * @return 
	 */
	public List<CsPolicyPrintFeeBO> queryMakeFeeInfoForDownloadList(CsPolicyPrintFeeBO csPolicyPrintFeeBO, String mode);
	/**
	 * @description 查询打印份数 printCounts
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param bpoWageBO 结算明细对象
	 * @return
	 * @throws BizException
	 */
	public BigDecimal findBpoWagePrintCounts(CsPolicyPrintFeeBO bpoWageBO) throws BizException;
	/**
	 * @description 查询打印印数 questionCounts
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param bpoWageBO 结算明细对象
	 * @return
	 * @throws BizException
	 */
	public BigDecimal findBpoWageQuestionCounts(CsPolicyPrintFeeBO bpoWageBO) throws BizException;
	/**
	 * @description 查询不合格份数printNum
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param bpoWageBO 结算明细对象
	 * @return
	 * @throws BizException
	 */
	public BigDecimal findBpoWagePrintNum(CsPolicyPrintFeeBO bpoWageBO) throws BizException;
	/**
	 * @description 查询不合格印数printNum
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param bpoWageBO 结算明细对象
	 * @return
	 * @throws BizException
	 */
	public BigDecimal findBpoWagePrintNotNum(CsPolicyPrintFeeBO bpoWageBO) throws BizException;
	/**
	 * @description 查询需付费印数 num
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param bpoWageBO 结算明细对象
	 * @return
	 * @throws BizException
	 */
	public BigDecimal findBpoWageNum(CsPolicyPrintFeeBO bpoWageBO) throws BizException;
	/**
	 * @description 制单费用明细清单下载
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param bpoWageBO 结算明细对象
	 * @return
	 * @throws BizException
	 */
	public List<CsPolicyPrintFeeBO> downloadMakeFeeCalLists(CsPolicyPrintFeeBO bpoWageBO) throws BizException;
	/**
	 * @description 提取外包商回传不成功清单
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param bpoWageBO 结算明细对象
	 * @return
	 * @throws BizException
	 */
	public List<CsPolicyPrintFeeBO> downLoadMakeFeeUnQualifyDate(CsPolicyPrintFeeBO bpoWageBO) throws BizException;

	/**
	 * @description 根据操作人员ID 获取用户保全权限
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param userId
	 * @return 
	 */
	public String queryCusPermissionFlag(long userId);
	
	
	/**
	 * 根据保单号和受理号查询出来保全保单打印任务，以及和保全保单打印任务相关联的外包商名称
	 */
	public CsPolicyPrintTaskBO findAllCsPolicyPrintTaskByPolicyAndAccountCode(String acceptCode , String policyCode);

}
