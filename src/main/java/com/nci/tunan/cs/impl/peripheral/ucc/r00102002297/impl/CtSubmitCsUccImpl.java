package com.nci.tunan.cs.impl.peripheral.ucc.r00102002297.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.nci.core.common.impl.util.CodeMapperUtils;
import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.dao.ICsPremArapDao;
import com.nci.tunan.cs.impl.commonFlow.ucc.ICsPremArapUCC;
import com.nci.tunan.cs.impl.commonPage.ucc.IBankAccountInfoUCC;
import com.nci.tunan.cs.impl.csItem.ucc.ICsEndorseCTUCC;
import com.nci.tunan.cs.impl.outterdealfa.ucc.IOutterDealUCC;
import com.nci.tunan.cs.impl.outterdealfa.ucc.impl.OutterDealUCCImpl;
import com.nci.tunan.cs.impl.peripheral.service.r00102002297.ICtSubmitCsService;
import com.nci.tunan.cs.impl.peripheral.ucc.common.impl.CommonOutterUccImpl;
import com.nci.tunan.cs.impl.peripheral.ucc.r00102002297.ICtSubmitCsUcc;
import com.nci.tunan.cs.interfaces.peripheral.exports.common.OutterEndorseVO;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102002297.vo.InputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102002297.vo.OutputData;
import com.nci.tunan.cs.interfaces.peripheral.exports.r00102002297.vo.Result;
import com.nci.tunan.cs.model.po.CsPremArapPO;
import com.nci.tunan.cs.model.vo.CsAcceptChangeVO;
import com.nci.tunan.cs.model.vo.CsAcceptInfoVO;
import com.nci.tunan.cs.model.vo.CsBankAccountVO;
import com.nci.tunan.cs.model.vo.CsEndorseCTVO;
import com.nci.tunan.cs.model.vo.CsPremArapVO;
import com.nci.tunan.cs.model.vo.SurrenderVO;
import com.nci.tunan.pa.interfaces.model.bo.PolicyHolderBO;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;

/**
 * 
 * @description 退保提交接口
 * <AUTHOR> <EMAIL> 
 * @date 2021-11-15 下午8:39:11 
 * @.belongToModule cs-保全子系统
 */
public class CtSubmitCsUccImpl extends CommonOutterUccImpl implements ICtSubmitCsUcc {
    /**退保service*/
	private ICtSubmitCsService ctSubmitCsService;
	/**退保ucc*/
	private ICsEndorseCTUCC csEndorseCTUCC;	//@invalid		 // 退保ucc
	/**查询银行信息ucc*/
	private IBankAccountInfoUCC bankAccountInfoUCC;
	/**查询收付费信息ucc*/
	private ICsPremArapUCC csPremArapUCC;//@invalid		 // 收付费
	/**外围接口处理ucc*/
	@Autowired
	@Qualifier("PA_outterDealUcc")
	private IOutterDealUCC outterDealUCC;//@invalid		 // 解锁
	/**查询保全补退费信息dao*/
	private ICsPremArapDao csPremArapDao;

	/**
	 * 
	 * @description 退保提交
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.impl.peripheral.ucc.r00102002297.ICtSubmitCsUcc#ctSubmitCs(com.nci.tunan.cs.interfaces.peripheral.exports.r00102002297.vo.InputData)
	 * @param inputData 入参
	 * @return OutputData 处理结果
	 */
	@Override
	public OutputData ctSubmitCs(InputData inputData) {
String item="请求参数初始化及校验";
outterLog(item,null);
Date d=new Date();
		OutputData outputData = new OutputData();
		OutterEndorseVO outterEndorseVO=new OutterEndorseVO();
		outterEndorseVO.setEdorApp(inputData.getApplyer());
		super.init(outterEndorseVO);
		SysHeader sysHeader = outterEndorseVO.getSysHeader();
		sysHeader.setBizResCd("1");
		//1.入参校验
		String checkRes=checkData(inputData);
		if(!StringUtilsEx.isBlank(checkRes)){
			sysHeader.setBizResText(checkRes);
			return outputData;
		}
outterLog(item,d);
		try{
		    //2.退保提交
			outputData=ctSubmitCs(inputData,outterEndorseVO);
			if(outputData==null){
			    //3.解锁
				super.releaseLockByAcceptId(outterEndorseVO.getAcceptId());
			}
		}catch(Exception e){ 
			e.printStackTrace();
			sysHeader.setBizResCd("1");
			sysHeader.setBizResText("退保失败");
			//@invalid		 //如果出现异常，进行解锁操作
			super.releaseLockByAcceptId(outterEndorseVO.getAcceptId());
		}
		CommonHeaderDeal.setSYSHEADERTHREAD(outterEndorseVO.getSysHeader());
		return outputData;
	}
	/**
	 * 
	 * @description  校验请求报文
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param inputData 入参
	 * @return String 校验结果
	 */
	private String checkData(InputData inputData){
		if(StringUtilsEx.isBlank(inputData.getContno())){
			return "保单号为空";
		}
		if(StringUtilsEx.isBlank(inputData.getGetMode())){
			return "本领取方式为空";
		}
		if(StringUtilsEx.isBlank(inputData.getAppType())){
			return "申请方式为空";
		}
		if(StringUtilsEx.isBlank(inputData.getApplyer())){
			return "申请人为空";
		}
		if(StringUtilsEx.isBlank(inputData.getAcceptDate())){
			return "受理日期为空";
		}
		if(StringUtilsEx.isBlank(inputData.getEdorType())){
			return "批改类型编码为空";
		}
		if(StringUtilsEx.isBlank(inputData.getEdorAppDate())){
			return "批改类型保全申请提交日期为空";
		}
		if(StringUtilsEx.isBlank(inputData.getSurrReason())){
			return "退保原因为空";
		}
		if(StringUtilsEx.isBlank(inputData.getRelationToAppnt())){
			return "投保人与业务员关系为空";
		}
		if(StringUtilsEx.isBlank(inputData.getBankCode())){
			return "开户银行为空";
		}
		if(StringUtilsEx.isBlank(inputData.getBankAccNo())){
			return "银行账户为空";
		}
		if(StringUtilsEx.isBlank(inputData.getAccName())){
			return "银行户名为空";
		}
		return null;
	}
	
	/**
	 * 
	 * @description 执行退保操作
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param inputData 入参
	 * @param outterEndorseVO 处理结果
	 * @return  OutputData 处理结果
	 */
	private OutputData ctSubmitCs(InputData inputData, OutterEndorseVO outterEndorseVO) {
		SysHeader sysHeader=outterEndorseVO.getSysHeader();
		OutputData outputData = new OutputData();
		List<Result> results=new ArrayList<Result>();
		//1.获取入参所需要的字段
		//@invalid		 // 保单号
		String contno=inputData.getContno();
		//@invalid		 // 本次领取方式
		String getMode=CodeMapperUtils.getNewCodeByOldCode("GET_MODE", inputData.getGetMode(), "BCP");
		//@invalid		 // 申请方式
		String appType=CodeMapperUtils.getNewCodeByOldCode("SERVICE_TYPE", inputData.getAppType(), "CUS");
		//@invalid		 // 申请人
		String applyer=inputData.getApplyer();
		//@invalid		 // 受理日期
		String acceptDate=inputData.getAcceptDate();
		//@invalid		 // 批改类型编码
		String edorType=CodeMapperUtils.getNewCodeByOldCode("SERVICE_CODE", inputData.getEdorType(), "CUS");
		//@invalid		 // 批改类型保全申请提交日期
		String edorAppDate=inputData.getEdorAppDate();
		//@invalid		 // 退保原因
		String surrReason=inputData.getSurrReason();
		//@invalid		 // 投保人与业务员关系
		String relationToAppnt=inputData.getRelationToAppnt();
		//@invalid		 // 回访电话
		String callBack1=inputData.getCallBack1();
		//@invalid		 // 开户银行
		String bankCode=inputData.getBankCode();
		//@invalid		 // 银行账户
		String bankAccNo=inputData.getBankAccNo();
		//@invalid		 // 银行户名
		String accName=inputData.getAccName();
		
		PolicyHolderBO policyHolderBO=new PolicyHolderBO();
		policyHolderBO.setPolicyCode(contno);
		policyHolderBO = getPolicyHolderService().findPolicyHolder(policyHolderBO);
		if(policyHolderBO!=null&&policyHolderBO.getCustomerId()!=null){
			outterEndorseVO.setCustomerId(policyHolderBO.getCustomerId());
		}else{
			sysHeader.setBizResText("未找到相应客户");
			return outputData;
		}
		
		outterEndorseVO.setAppType(appType);	//@invalid		 //申请方式	12移动保全
		outterEndorseVO.setContNo(contno);
		outterEndorseVO.setEdorType(edorType);
		outterEndorseVO.setEdor_service_name("退保");
		//@invalid		 //保全申请提交日期
		Date applyTime = DateUtilsEx.formatToDate(edorAppDate, null);
		applyTime = (null==applyTime?WorkDateUtil.getWorkDate():applyTime);
		outterEndorseVO.setEdorAppDate(applyTime);
		//@invalid		 //申请、受理、加锁
		if(!executeBigOutterRingBefore(outterEndorseVO)){
			return null;
		}
		
		BigDecimal changeId=outterEndorseVO.getChangeId();
		BigDecimal acceptId=outterEndorseVO.getAcceptId();
		BigDecimal customerId=outterEndorseVO.getCustomerId();
		
		//@invalid		 /*----------保存保全项---开始-------------------------------------------------*/
outterLog("保存CT退保", new Date());
		// 2.查询变更前保单信息
		CsEndorseCTVO csEndorseCTVO = csEndorseCTUCC.queryCTMessage(changeId, acceptId, customerId,"");
		List<SurrenderVO> listBfSurrender = csEndorseCTVO.getListBfSurrender();
		if(listBfSurrender!=null&&!listBfSurrender.isEmpty()){
			for (SurrenderVO surrVO : listBfSurrender)
			{	//@invalid		 //变更保单信息
				surrVO.setChangeId(changeId);
				surrVO.setAcceptId(acceptId);
				surrVO.setCustomerId(customerId);
				surrVO.setSurrenderCause(new BigDecimal(surrReason));
				surrVO.setAgentHolderRelation(relationToAppnt);
				surrVO.setIsPCancel(new BigDecimal(1));
			}
		}
		//@invalid		 //计算
		List<SurrenderVO> surrenderSaveVOs = csEndorseCTUCC.calCTFee(listBfSurrender);
		//3.保存退保信息
		csEndorseCTUCC.saveCTMassage(changeId, acceptId, customerId, surrenderSaveVOs);
		//@invalid		 //查询变更后的退保信息
		//@invalid		 //		List<SurrenderVO> listSurrenderNew = csEndorseCTUCC.queryPolicyInfo(changeId, acceptId, customerId, com.nci.tunan.cs.common.Constants.NEW);
		
		//4.保全受理信息初始化
		CsAcceptChangeVO csAcceptChangeVO1 = new CsAcceptChangeVO();
		csAcceptChangeVO1.setChangeId(changeId);
		List<CsAcceptInfoVO> csAcceptInfoVOList= getCusApplicationUCC().loadAllAcceptInfo(csAcceptChangeVO1);
		//@invalid		 // 用于存储收付费的集合，默认补退费信息取第一个受理下的
		List<List<CsPremArapVO>> csPremArapVOList1 = new ArrayList<List<CsPremArapVO>>();
		List<CsPremArapVO> csPremArapVOList=new ArrayList<CsPremArapVO>();
		for (CsAcceptInfoVO changeVO1 : csAcceptInfoVOList) {
			CsPremArapVO csPremArapVO = new CsPremArapVO();
			csPremArapVO.setAcceptId(changeVO1.getAcceptId());
			csPremArapVO.setChangeId(changeId);
			csPremArapVO.setCustomerId(customerId);
			csPremArapVO.setCustomerName(applyer);
			csPremArapVOList = getCsPremArapUCC().findAllCsPremArapVO(csPremArapVO);//@invalid		 //补退费信息
			if (csPremArapVOList != null && csPremArapVOList.size() > 0) {
				changeVO1.setPremArap("1");
			}
			csPremArapVOList1.add(csPremArapVOList);
		}
		//@invalid		 // 受理环节点击录入按钮时，如果多个受理下均有补退费信息，则显示第一个受理对应的补退费信息(保单补退费形式为该受理相对应的保单-以前为所有受理下保单的补退费信息)
		List<CsPremArapVO> csArapVOs=new ArrayList<CsPremArapVO>();
		if (csPremArapVOList1.get(0) != null && csPremArapVOList1.get(0).size() > 0) {
			String policyCodeString = "";//@invalid		 // 保单号
			BigDecimal amountBigDecimal = new BigDecimal("0");//@invalid		 // 金额
			BigDecimal amountBigDecimalPay = new BigDecimal("0");//@invalid		 // 金额
			BigDecimal amountMany = new BigDecimal("0");//@invalid		 // 金额
			policyCodeString = csPremArapVOList1.get(0).get(0).getPolicyCode();
			policyCodeString = inputData.getContno();
			CsPremArapVO csPolicyPremArapVO=new CsPremArapVO();
			for (int a = 0; a < csPremArapVOList1.get(0).size(); a++) {
				List<CsPremArapVO> csPremArapVOss = csPremArapVOList1.get(0).get(a).getCsPremArapVOs();
				for (int i = 0; i < csPremArapVOss.size(); i++) {
				
				if (policyCodeString.equals(csPremArapVOss.get(i).getPolicyCode())) {
					
					if ("1".equals(csPremArapVOss.get(i).getArapFlag())) {
						amountBigDecimal = amountBigDecimal.add(csPremArapVOList1.get(0).get(i).getCsPremArapVOs().get(0).getAmountMany());
					} else if ("2".equals(csPremArapVOss.get(i).getArapFlag())) {
						if (csPremArapVOList1.get(0).get(i).getCsPremArapVOs().get(0).getAmountMany()!= null) {
							amountBigDecimalPay = amountBigDecimalPay.add(csPremArapVOss.get(i).getAmountMany());
						}
					}

				}  else {
					//5. 存储保单层应收应付信息
					csPolicyPremArapVO = csPremArapVOss.get(i - 1);

					amountMany = amountBigDecimal.abs().subtract(amountBigDecimalPay.abs());
					if (amountMany.compareTo(new BigDecimal("0")) > 0) {
						csPolicyPremArapVO.setArapAndPrem("应收");
					} else if (amountMany.compareTo(new BigDecimal("0")) < 0) {
						csPolicyPremArapVO.setArapAndPrem("应付");
					} else if (amountMany.compareTo(new BigDecimal("0")) == 0) {
						csPolicyPremArapVO.setArapAndPrem("非应付/应付");
					}
					csPolicyPremArapVO.setAmountMany(amountMany);
					csArapVOs.add(csPolicyPremArapVO);
					amountBigDecimal = new BigDecimal("0");
					amountBigDecimalPay = new BigDecimal("0");
					if ("1".equals(csPremArapVOss.get(i).getArapFlag())) { //@invalid		 // 1代表应收
						amountBigDecimal = csPremArapVOss.get(i).getFeeAmount();
						amountMany = new BigDecimal("0");
					} else if ("2".equals(csPremArapVOss.get(i).getArapFlag())) {
						amountBigDecimalPay = csPremArapVOss.get(i).getFeeAmount();
						amountMany = new BigDecimal("0");
					}
					policyCodeString = csPremArapVOss.get(i).getPolicyCode();
				}
				if (i == (csPremArapVOList1.get(0).size() - 1)) {
					csPolicyPremArapVO = csPremArapVOss.get(i);
					amountMany = amountBigDecimal.abs().subtract(amountBigDecimalPay.abs());
					if (amountMany.compareTo(new BigDecimal("0")) > 0) {
						csPolicyPremArapVO.setArapAndPrem("应收");
					} else if (amountMany.compareTo(new BigDecimal("0")) < 0) {
						csPolicyPremArapVO.setArapAndPrem("应付");
					} else if (amountMany.compareTo(new BigDecimal("0")) == 0) {
						csPolicyPremArapVO.setArapAndPrem("非应付/应付");
					}
					csPolicyPremArapVO.setAmountMany(amountMany);
					csArapVOs.add(csPolicyPremArapVO);
				}
			}
			}
		}
		
		//6.新添加的银行账户集合
		List<CsBankAccountVO> newCsBankAccountVOList=new ArrayList<CsBankAccountVO>();
		//7. 配置新账户信息
		CsBankAccountVO oneCsBankAccountVO = new CsBankAccountVO();
		oneCsBankAccountVO.setBankCode(bankCode);
		oneCsBankAccountVO.setAccoName(accName);
		oneCsBankAccountVO.setBankAccount(bankAccNo);
		oneCsBankAccountVO.setAccountStatus(Constants.CS_ACCOUNT_STATUS_EFFECT);
		oneCsBankAccountVO.setAccountType(Constants.CS_ACCOUNT_TYPE_BANK_RETURN);
		oneCsBankAccountVO.setOldNew(Constants.NEW);
		oneCsBankAccountVO.setOperationType(Constants.OPERATIONTYPE_ADD);
		oneCsBankAccountVO.setConfirmed(Constants.YES_NO__YES);
		oneCsBankAccountVO.setAcceptId(acceptId);
		oneCsBankAccountVO.setChangeId(changeId);
		oneCsBankAccountVO.setCustomerId(customerId);
		newCsBankAccountVOList.add(oneCsBankAccountVO);
		//@invalid		 //判断银行账号是否存在账户表中，如果不存在就添加
		String message = bankAccountInfoUCC.checkBankCodeInfo(newCsBankAccountVOList);
		if(message == null || "".equals(message)){
			//@invalid		 //保存账号信息
			bankAccountInfoUCC.saveAndDeleteBankAccount(customerId, changeId, acceptId,Constants.NEW, newCsBankAccountVOList,"");
		}
	
		//8.保存补退费信息
		CsPremArapPO csPremArapPO=new CsPremArapPO();
		csPremArapPO.setBusinessCode(outterEndorseVO.getAcceptCode());
		List<CsPremArapPO>premList=csPremArapDao.findAllCsPremArap(csPremArapPO);
		if(premList != null && !premList.isEmpty()){
			for (CsPremArapPO csPremArapPO2 : premList) {
				
				csPremArapPO2.setPayMode(getMode);
				csPremArapPO2.setBankAccount(bankAccNo);
				csPremArapPO2.setBankUserName(accName);
				csPremArapPO2.setPayMode(getMode);
				csPremArapPO2.setBankCode(bankCode);
				if(StringUtils.isBlank(csPremArapPO2.getPayeeName())){
					csPremArapPO2.setPayeeName(accName);
				}
			}
			csPremArapDao.batchUpdateCsPremArap(premList);
		}
		
outterLog("保存CT退保", null);
//@invalid		 /*----------保存保全项---结束---------------------------------------------------*/
		
		if(!executeBigOutterRingAfter(outterEndorseVO)){
			return null;
		}
		
		String succFlag = (String)outterEndorseVO.getData().get("succFlag");
		if(succFlag.equals("0")||succFlag.equals("1")){
			for(CsPremArapVO csArapVO:csArapVOs){
				Result result = new Result();
				result.setEdorAcceptNo(outterEndorseVO.getAcceptCode());
				result.setBackFeeGetMoney(csArapVO.getAmountMany().toString());
				results.add(result);
			}
			outputData.setResults(results);
			sysHeader.setBizResCd("0");
			if(succFlag.equals("0")){
				//@invalid		 //返回成功
				sysHeader.setBizResText("成功");
			}else if(succFlag.equals("1")){
				outterDealUCC.releaseLockByAcceptId(acceptId);
				sysHeader.setBizResCd("1");
				sysHeader.setBizResText(OutterDealUCCImpl.explainOutterdealRes(succFlag));
			}
		}else{
			outterDealUCC.releaseLockByAcceptId(acceptId);
			sysHeader.setBizResCd("1");
			sysHeader.setBizResText(OutterDealUCCImpl.explainOutterdealRes(succFlag));
			return null;
		}
		return outputData;
	
	}

	public ICtSubmitCsService getCtSubmitCsService() {
		return ctSubmitCsService;
	}

	public void setCtSubmitCsService(ICtSubmitCsService ctSubmitCsService) {
		this.ctSubmitCsService = ctSubmitCsService;
	}
	
	public ICsEndorseCTUCC getCsEndorseCTUCC() {
		return csEndorseCTUCC;
	}

	public void setCsEndorseCTUCC(ICsEndorseCTUCC csEndorseCTUCC) {
		this.csEndorseCTUCC = csEndorseCTUCC;
	}

	public IBankAccountInfoUCC getBankAccountInfoUCC() {
		return bankAccountInfoUCC;
	}

	public void setBankAccountInfoUCC(IBankAccountInfoUCC bankAccountInfoUCC) {
		this.bankAccountInfoUCC = bankAccountInfoUCC;
	}

	public ICsPremArapUCC getCsPremArapUCC() {
		return csPremArapUCC;
	}

	public void setCsPremArapUCC(ICsPremArapUCC csPremArapUCC) {
		this.csPremArapUCC = csPremArapUCC;
	}
	public ICsPremArapDao getCsPremArapDao() {
		return csPremArapDao;
	}
	public void setCsPremArapDao(ICsPremArapDao csPremArapDao) {
		this.csPremArapDao = csPremArapDao;
	}
	
}
