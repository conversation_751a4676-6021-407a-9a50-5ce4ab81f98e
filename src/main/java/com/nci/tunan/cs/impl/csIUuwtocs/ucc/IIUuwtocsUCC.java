package com.nci.tunan.cs.impl.csIUuwtocs.ucc;

import java.math.BigDecimal;

import com.nci.tunan.cs.interfaces.exports.vo.uwproposal.UwProposalReqVO;
import com.nci.tunan.cs.interfaces.exports.vo.uwproposal.UwProposalResVO;
import com.nci.tunan.cs.interfaces.serviceData.iucsuw.IUuwtocsReqDataVO;
import com.nci.tunan.cs.interfaces.serviceData.iucsuw.IUuwtocsResDataVO;
import com.nci.udmp.framework.signature.IUCC;

/**
 * 
 * @description 核保方法
 * <AUTHOR> <EMAIL> 
 * @date 2015年05月15日
 * @.belongToModule 保全子系统-核保方法
 */
public interface IIUuwtocsUCC extends IUCC{
	
	/**
	 * 
	 * @description 核保给保全推数据，下核保结论
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL> 
	 * @param vo 入参
	 * @return
	 * @throws Exception
	 */
	public IUuwtocsResDataVO insertforupdateUCC(IUuwtocsReqDataVO vo) throws Exception;
	
	/**
	 * 核保系统调保全系统的投保规则
	 * @description
	 * @version V1.0.0
	 * @title
	 * @<NAME_EMAIL> 
	 * @param inputData 入参
	 * @return
	 * @throws Exception 
	 */
	public UwProposalResVO queryCsProposal(UwProposalReqVO inputData) throws Exception;
	 /**
     * 
     * @description 查询判断险种在当前保证续保期间内是否操作保全项“续保意愿变更保全项”且核保结论为“拒保/延期”
     */
	Boolean queryIsUwFlag(BigDecimal busiItemId, String policyCode);
	

}
