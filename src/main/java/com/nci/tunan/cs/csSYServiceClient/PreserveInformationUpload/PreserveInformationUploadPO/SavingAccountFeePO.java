package com.nci.tunan.cs.csSYServiceClient.PreserveInformationUpload.PreserveInformationUploadPO;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

/** 
 * #69356 需求取消-43419-个人税收递延型养老年金保险理赔系统需求
 * @description 税延产品-保全信息上传接口(END001)
 * <AUTHOR> <EMAIL> 
 * @date 2020年6月22日 下午1:44:25 
 * @.belongToModule  CS-保全系统/税延产品
*/
public class SavingAccountFeePO extends BasePO {

	private static final long serialVersionUID = 1L;

	/*public String getPolicyCode() {
		return getString("policy_code");
	}
	
	public void setPolicyCode(String policyCode) {
		setString("policy_code", policyCode);
	}
	public String getCoveragePackageCode() {
		return getString("coverage_package_code");
	}
	public void setCoveragePackageCode(String coveragePackageCode) {
		setString("coverage_package_code", coveragePackageCode);
	}
	public String getComCoverageCode() {
		return getString("com_coverage_code");
	}
	public void setComCoverageCode(String comCoverageCode) {
		setString("com_coverage_code", comCoverageCode);
	}
	public BigDecimal getNetWorth() {
		return getBigDecimal("net_worth");
	}
	public void setNetWorth(BigDecimal netWorth) {
		setBigDecimal("net_worth", netWorth);
	}
	public String getPerValue() {
		return getString("per_value");
	}
	public void setPerValue(String perValue) {
		setString("per_value", perValue);
	}
	public String getComFeeId() {
		return getString("com_fee_id");
	}
	public void setComFeeId(String comFeeId) {
		setString("com_fee_id", comFeeId);
	}
	public String getFeeDate() {
		return getString("fee_date");
	}
	public void setFeeDate(String feeDate) {
		setString("fee_date", feeDate);
	}
	public String getAccFeeType() {
		return getString("acc_fee_type");
	}
	public void setAccFeeType(String accFeeType) {
		setString("acc_fee_type", accFeeType);
	}
	public BigDecimal getFeeAmount() {
		return getBigDecimal("fee_amount");
	}
	public void setFeeAmount(BigDecimal feeAmount) {
		setBigDecimal("fee_amount", feeAmount);
	}
	public Date getRiskCoverageStartDate() {
		return getUtilDate("risk_coverage_start_date");
	}
	public void setRiskCoverageStartDate(Date riskCoverageStartDate) {
		setUtilDate("risk_coverage_start_date", riskCoverageStartDate);
	}
	public Date getRiskCoverageEndDate() {
		return getUtilDate("risk_coverage_end_date");
	}
	public void setRiskCoverageEndDate(Date riskCoverageEndDate) {
		setUtilDate("risk_coverage_end_date", riskCoverageEndDate);
	}
	public BigDecimal getInterestRate() {
		return getBigDecimal("interest_rate");
	}
	public void setInterestRate(BigDecimal interestRate) {
		setBigDecimal("interest_rate", interestRate);
	}*/

}
