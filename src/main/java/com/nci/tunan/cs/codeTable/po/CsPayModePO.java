package com.nci.tunan.cs.codeTable.po;

import com.nci.udmp.framework.model.BasePO;

/** 
 * @description PayModePO对象
 * <AUTHOR> 
 * @date 2015-10-13 14:32:56  
 */
public class CsPayModePO extends BasePO {
 	/** 
	* @Fields serialVersionUID : @invalid TODO(用一句话描述这个变量表示什么) 
	*/ 
	
	private static final long serialVersionUID = 1L;

	/** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
	// name  ---  String  ---  VARCHAR2_100_0_支付方式名称;
		// code  ---  String  ---  CHAR_2_0_支付方式代码;
	
		public void setName(String name){
		setString("name", name);
	}
	
	public String getName(){
		return getString("name");
	}
		public void setCode(String code){
		setString("code", code);
	}
	
	public String getCode(){
		return getString("code");
	}
		
	@Override
    public String toString() {
        return "CsPayModePO [" +
				"name="+getName()+","+
"code="+getCode()+"]";
    }	
 }
