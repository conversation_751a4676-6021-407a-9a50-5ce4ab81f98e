package com.nci.tunan.cs.codeTable.bo;

import com.nci.udmp.framework.model.BaseBO;

/**
 * 
 * @description 质押对象表BO对象
 * @.belongToModule 保全系统  质押对象表BO对象
 * <AUTHOR> <EMAIL> 
 * @date 2015年10月13日 上午11:47:39
 */
public class CsPledgeOrgTypeBO extends BaseBO{

	 /** 
	* @Fields pledgeOrgTypeName : 质押对象名称
	*/ 
	private String pledgeOrgTypeName;
	 /** 
	* @Fields pledgeOrgType :  质押对象代码
	*/ 
	private String pledgeOrgType;
	
	
	public String getPledgeOrgTypeName() {
		return pledgeOrgTypeName;
	}


	public void setPledgeOrgTypeName(String pledgeOrgTypeName) {
		this.pledgeOrgTypeName = pledgeOrgTypeName;
	}


	public String getPledgeOrgType() {
		return pledgeOrgType;
	}


	public void setPledgeOrgType(String pledgeOrgType) {
		this.pledgeOrgType = pledgeOrgType;
	}


	@Override
	public String getBizId() {
		// @invalid TODO Auto-generated method stub
		return null;
	}

}
