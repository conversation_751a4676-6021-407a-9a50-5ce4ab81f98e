package com.nci.tunan.cs.codeTable.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.cs.codeTable.dao.ICsLicenseTypeDao;
import com.nci.tunan.cs.codeTable.po.CsLicenseTypePO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * @description 体检项目类型Dao实现类
 * @.belongToModule 保全系统  体检项目类型Dao实现类
 * <AUTHOR> <EMAIL>
 * @date 2015-10-13 14:28:24
 */
public class CsLicenseTypeDaoImpl extends BaseDaoImpl implements ICsLicenseTypeDao {
	/**
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	/**
	 * @description 增加数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csLicenseTypePO 体检项目类型PO
	 *            对象
	 * @return CsLicenseTypePO 添加结果
	 */
	public CsLicenseTypePO addLicenseType(CsLicenseTypePO csLicenseTypePO) {
		logger.debug("<======CsLicenseTypeDaoImpl--addLicenseType======>");
		return createObject("addLicenseType", csLicenseTypePO);
	}

	/**
	 * @description 删除数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csLicenseTypePO 体检项目类型PO
	 *            对象
	 * @return boolean 删除是否成功
	 */
	public boolean deleteLicenseType(CsLicenseTypePO csLicenseTypePO) {
		logger.debug("<======CsLicenseTypeDaoImpl--deleteLicenseType======>");
		return deleteObject("deleteLicenseType", csLicenseTypePO);
	}

	/**
	 * @description 修改数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csLicenseTypePO 体检项目类型PO
	 *            对象
	 * @return CsLicenseTypePO 修改结果
	 */
	public CsLicenseTypePO updateLicenseType(CsLicenseTypePO csLicenseTypePO) {
		logger.debug("<======CsLicenseTypeDaoImpl--updateLicenseType======>");
		return updateObject("updateLicenseType", csLicenseTypePO);
	}

	/**
	 * @description 查询单条数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csLicenseTypePO 体检项目类型PO
	 *            对象
	 * @return CsLicenseTypePO 查询结果对象
	 */
	public CsLicenseTypePO findLicenseType(CsLicenseTypePO csLicenseTypePO) {
		logger.debug("<======CsLicenseTypeDaoImpl--findLicenseType======>");
		return findObject("CS_findLicenseTypeByLicenseType", csLicenseTypePO);
	}

	/**
	 * @description 查询所有数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csLicenseTypePO 体检项目类型PO
	 *            对象
	 * @return List<CsLicenseTypePO> 查询结果List
	 */
	public List<CsLicenseTypePO> findAllLicenseType(CsLicenseTypePO csLicenseTypePO) {
		logger.debug("<======CsLicenseTypeDaoImpl--findAllLicenseType======>");
		return findAll("findAllLicenseType", csLicenseTypePO);
	}

	/**
	 * @description 查询数据条数
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csLicenseTypePO 体检项目类型PO
	 *            对象
	 * @return int 查询结果条数
	 */
	public int findLicenseTypeTotal(CsLicenseTypePO csLicenseTypePO) {
		logger.debug("<======CsLicenseTypeDaoImpl--findLicenseTypeTotal======>");
		return findCount("findLicenseTypeTotal", csLicenseTypePO);
	}

	/**
	 * @description 分页查询数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param currentPage
	 *            当前页对象
	 * @return CurrentPage<CsLicenseTypePO> 查询结果的当前页对象
	 */
	public CurrentPage<CsLicenseTypePO> queryLicenseTypeForPage(CsLicenseTypePO csLicenseTypePO,
			CurrentPage<CsLicenseTypePO> currentPage) {
		logger.debug("<======CsLicenseTypeDaoImpl--queryLicenseTypeForPage======>");
		currentPage.setParamObject(csLicenseTypePO);
		return queryForPage("findLicenseTypeTotal", "queryLicenseTypeForPage", currentPage);
	}

	/**
	 * @description 批量增加数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param licenseTypePOList 体检项目类型PO
	 *            对象列表
	 * @return boolean 批量添加是否成功
	 */
	public boolean batchSaveLicenseType(List<CsLicenseTypePO> licenseTypePOList) {
		logger.debug("<======CsLicenseTypeDaoImpl--batchSaveLicenseType======>");
		return batchSave("addLicenseType", licenseTypePOList);
	}

	/**
	 * @description 批量修改数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param licenseTypePOList 体检项目类型PO
	 *            对象列表
	 * @return boolean 批量修改是否成功
	 */
	public boolean batchUpdateLicenseType(List<CsLicenseTypePO> licenseTypePOList) {
		logger.debug("<======CsLicenseTypeDaoImpl--batchUpdateLicenseType======>");
		return batchUpdate("updateLicenseType", licenseTypePOList);
	}

	/**
	 * @description 批量删除数据
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param licenseTypePOList 体检项目类型PO
	 *            对象列表
	 * @return boolean 批量删除是否成功
	 */
	public boolean batchDeleteLicenseType(List<CsLicenseTypePO> licenseTypePOList) {
		logger.debug("<======CsLicenseTypeDaoImpl--batchDeleteLicenseType======>");
		return batchDelete("deleteLicenseType", licenseTypePOList);
	}

	/**
	 * @description 查询所有数据 ，重新组装为map
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param csLicenseTypePO 体检项目类型PO
	 *            对象
	 * @return List<Map<String, Object>> 查询结果存放到map中
	 */
	public List<Map<String, Object>> findAllMapLicenseType(CsLicenseTypePO csLicenseTypePO) {
		logger.debug("<======CsLicenseTypeDaoImpl--findAllMapLicenseType======>");
		return findAllMap("findAllMapLicenseType", csLicenseTypePO);
	}
}
