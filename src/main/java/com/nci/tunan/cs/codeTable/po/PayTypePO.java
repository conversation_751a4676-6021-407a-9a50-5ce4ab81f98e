package com.nci.tunan.cs.codeTable.po;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.math.BigDecimal;
import java.lang.String;

/** 
 * @description PayTypePO对象
 * <AUTHOR> 
 * @date 2015-10-13 15:39:59  
 */
public class PayTypePO extends BasePO {
 	/** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
	// payName  ---  String  ---  VARCHAR2_20_0_Description of the Type;
		// payType  ---  String  ---  CHAR_1_0_Type of the Payment;
		// payRate  ---  BigDecimal  ---  NUMBER_8_6_Conversion Rate to Monthly Payment;
	
		public void setPayName(String payName){
		setString("pay_name", payName);
	}
	
	public String getPayName(){
		return getString("pay_name");
	}
		public void setPayType(String payType){
		setString("pay_type", payType);
	}
	
	public String getPayType(){
		return getString("pay_type");
	}
		public void setPayRate(BigDecimal payRate){
		setBigDecimal("pay_rate", payRate);
	}
	
	public BigDecimal getPayRate(){
		return getBigDecimal("pay_rate");
	}
		
	@Override
    public String toString() {
        return "PayTypePO [" +
				"payName="+getPayName()+","+
"payType="+getPayType()+","+
"payRate="+getPayRate()+"]";
    }	
 }
