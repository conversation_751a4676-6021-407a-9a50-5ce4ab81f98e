package com.nci.tunan.cs.codeTable.po;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.math.BigDecimal;
import java.lang.String;

/** 
 * @description QuestionaireSurveyVersionPO对象
 * <AUTHOR> 
 * @date 2016-04-03 14:24:59  
 */
public class QuestionaireSurveyVersionPO extends BasePO {
 	/** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
	// surveyVersionType  ---  String  ---  VARCHAR2_1_0_版别分类;
		// surveyVersionName  ---  String  ---  VARCHAR2_120_0_版别名称;
		// surveyVersionCode  ---  String  ---  VARCHAR2_5_0_版别编码;
		// oldSysFlag  ---  BigDecimal  ---  NUMBER_1_0_老核心数据标志;
	
		public void setSurveyVersionType(String surveyVersionType){
		setString("survey_version_type", surveyVersionType);
	}
	
	public String getSurveyVersionType(){
		return getString("survey_version_type");
	}
		public void setSurveyVersionName(String surveyVersionName){
		setString("survey_version_name", surveyVersionName);
	}
	
	public String getSurveyVersionName(){
		return getString("survey_version_name");
	}
		public void setSurveyVersionCode(String surveyVersionCode){
		setString("survey_version_code", surveyVersionCode);
	}
	
	public String getSurveyVersionCode(){
		return getString("survey_version_code");
	}
		public void setOldSysFlag(BigDecimal oldSysFlag){
		setBigDecimal("old_sys_flag", oldSysFlag);
	}
	
	public BigDecimal getOldSysFlag(){
		return getBigDecimal("old_sys_flag");
	}
		
	@Override
    public String toString() {
        return "QuestionaireSurveyVersionPO [" +
				"surveyVersionType="+getSurveyVersionType()+","+
"surveyVersionName="+getSurveyVersionName()+","+
"surveyVersionCode="+getSurveyVersionCode()+","+
"oldSysFlag="+getOldSysFlag()+"]";
    }	
 }
