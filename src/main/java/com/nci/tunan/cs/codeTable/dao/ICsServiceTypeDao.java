package com.nci.tunan.cs.codeTable.dao;

import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.core.common.interfaces.model.po.ServiceTypePO;
import com.nci.tunan.cs.codeTable.po.ServicePO;



/**
 * 
 * @description 服务人员类型
 * @<NAME_EMAIL> 
 * @date 2015-10-19 下午17:27:26 
 * @.belongToModule 保全系统-服务人员类型
 */
 public interface ICsServiceTypeDao {
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param serviceTypePO 对象 服务人员类型表PO
     * @return serviceTypePO 查询结果对象
     */
	 public ServiceTypePO findServiceType(ServiceTypePO serviceTypePO);
	 
	 
 }
 
