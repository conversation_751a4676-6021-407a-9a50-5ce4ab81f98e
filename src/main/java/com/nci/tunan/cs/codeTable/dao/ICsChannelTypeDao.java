package com.nci.tunan.cs.codeTable.dao;

import java.util.List;

import com.nci.tunan.cs.codeTable.po.CsChannelTypePO;
/**
 * 
 * @description   码表（T_CHANNEL_TYPE）dao接口
 * @<NAME_EMAIL> 
 * @date 2015-05-15
 * @.belongToModule 保全子系统   码表（T_CHANNEL_TYPE）dao接口
 */
public interface ICsChannelTypeDao {
	/**
	 * 
	 * @description 查询所有数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csChannelTypePO  PO对象
	 * @return  List<CsChannelTypePO>  PO对象集合
	 */
	public List<CsChannelTypePO> findAllCsChannelType(CsChannelTypePO csChannelTypePO);
}
