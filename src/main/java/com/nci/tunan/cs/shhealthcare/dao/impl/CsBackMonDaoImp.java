package com.nci.tunan.cs.shhealthcare.dao.impl;

import java.util.List;

import com.nci.tunan.cs.shhealthcare.dao.ICsBackMonDao;
import com.nci.tunan.cs.shhealthcare.po.CsBackMonParamPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;

/**
 * 
 * @description 上海医保--保全计算退保金上传接口
 * <AUTHOR> <EMAIL> 
 * @date 2015年05月15日 下午7:57:44 
 * @.belongToModule 保全子系统
 */
public class CsBackMonDaoImp extends BaseDaoImpl implements ICsBackMonDao{

	/**
	 * 
	 * @description 通过保单编码PolicySequenceNo查询保单号policyCode
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.shhealthcare.dao.ICsBackMonDao#getPolicyCode(com.nci.tunan.cs.shhealthcare.po.CsBackMonParamPO)
	 * @param csBackMonParamPO
	 * @return
	 */
	@Override
	public CsBackMonParamPO getPolicyCode(CsBackMonParamPO csBackMonParamPO) {
		logger.debug("<======CsBackMonDaoImp--getPolicyCode======>");
		return findObject("CS_findPolicyCode",csBackMonParamPO);
	}	

	/**
	 * 
	 * @description 查询计算退保金接口参数
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.shhealthcare.dao.ICsBackMonDao#getBackMonParam(com.nci.tunan.cs.shhealthcare.po.CsBackMonParamPO)
	 * @param csBackMonParamPO
	 * @return
	 */
	@Override
	public List<CsBackMonParamPO> getBackMonParam(CsBackMonParamPO csBackMonParamPO) {
		logger.debug("<======CsBackMonDaoImp--getBackMonParam======>");
		return findAll("CS_findAllBackParam", csBackMonParamPO);
	}

}

