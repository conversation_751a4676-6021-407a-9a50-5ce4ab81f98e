package com.nci.tunan.cs.shhealthcare.csPro;

import com.nci.udmp.framework.model.BaseBO;
import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * 
 * @description 上海医保--保全接口（服务方）
 * <AUTHOR> <EMAIL>
 * @version V1.0.0
 * @.belongToModule cs-保全子系统
 * @date 2020年12月23日  上午9:48:38
 */
@XStreamAlias("BODY")
public class CsProBodyBO extends BaseBO{


/**
 * serialVersionUID
 */
	private static final long serialVersionUID = 1L;
	/**
	 * succinfo 对象
	 */
	@XStreamAlias("SUCCESS_INFO")
    private  CsProEndorsementInfoBO  succinfo;
    
	public CsProEndorsementInfoBO getSuccinfo() {
		return succinfo;
	}

	public void setSuccinfo(CsProEndorsementInfoBO succinfo) {
		this.succinfo = succinfo;
	}

	/**
	 * 
	 * @description 业务ID
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @return
	 */
	public String getBizId() {
		return null;
	}

}
