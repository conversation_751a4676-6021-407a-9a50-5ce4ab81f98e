package com.nci.tunan.cs.batch.holderMobileSync;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.batch.holderMobileSync.service.IHolderMobileSyncService;
import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.model.bo.HolderMobileSyncBO;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.util.WorkDateUtil;
import com.nci.udmp.util.lang.CollectionUtilEx;
import com.nci.udmp.util.lang.DateUtilsEx;

/**
 * 
 * @description 投保人手机号同步批处理
 * <AUTHOR> <EMAIL>
 * @date 2020-9-2 下午6:37:50
 * @.belongToModule CS-保全子系统
 */
public class HolderMobileSyncBatchJob extends AbstractBatchJobForMod {

	/**
	 * 投保人手机号同步批处理
	 */
	private IHolderMobileSyncService holderMobileSyncService;

	public IHolderMobileSyncService getHolderMobileSyncService() {
		return holderMobileSyncService;
	}

	public void setHolderMobileSyncService(
			IHolderMobileSyncService holderMobileSyncService) {
		this.holderMobileSyncService = holderMobileSyncService;
	}

	/**
	 * 
	 * @description 查询总条数
	 * @version 1.0
	 * @title 查询总条数
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#queryCounts(com.nci.udmp.component.batch.context.JobSessionContext)
	 * @param jobSessionContext
	 *            上下文
	 * @return 返回查询总条数
	 */
	@Override
	public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
		jobSessionContext.setModNum(Constants.THREEInt);
		return jobSessionContext;
	}

	/**
	 * 
	 * @description 查询批处理明细数据
	 * @version 1.0
	 * @title 查询批处理明细数据
	 * <AUTHOR> <EMAIL>
	 * @param jobSessionContext
	 *            上下文
	 * @param start
	 *            开始
	 * @param counts
	 *            MOD
	 * @return 返回明细数据
	 */
	@Override
	public List<JobData> query(JobSessionContext jobSessionContext, long arg1,
			int arg2) {
		
		/**
		 * 根据开始时间、结束时间参数获取需要同步的投保人电话
		 * 1.开始日期与结束日期之间最大间隔不超过一个月
		 * 2.若开始时间、结束时间都为空，那么开始时间、结束时间获取当前的系统时间
		 * 3.若开始时间不为空，结束时间为空，那么结束时间与开始时间保持相同
		 * 4.若开始时间为空，结束时间不为空，那么结束时间与开始时间保持相同
		 * 5.若开始日期和结束日期均未录入，则默认推送系统当前日期前一天的数据。开始日期与结束日期之间最大间隔不超过一个月。
		 */ 
		List<JobData> jobDataList = new ArrayList<JobData>();

		//@invalid 1、 获取开始时间、结束时间参数
		String startDate = jobSessionContext.getParams().get(0).getParamValue();
		String endDate = jobSessionContext.getParams().get(1).getParamValue();

		//@invalid 2、开始日期与结束日期之间最大间隔不超过一个月
		if ((startDate != null && !"".equals(startDate))
				&& (endDate != null && !"".equals(endDate))) {
			int month = (int) DateUtilsEx.getMonthAmount(
					DateUtilsEx.toDate(startDate), DateUtilsEx.toDate(endDate));
			if (month >= Constants.ONE_INT) {
				logger.info("***********************开始日期与结束日期之间最大间隔不超过一个月。*************************");
				return jobDataList;
			}
		}

		//@invalid 3、 若开始时间、结束时间都为空，那么开始时间、结束时间获取当前的系统时间
		if ((startDate == null || "".equals(startDate))
				&& (endDate == null || "".equals(endDate))) {
			startDate = DateUtilsEx.date2String(WorkDateUtil.getWorkDate(),
					"yyyy-MM-dd");
			endDate = DateUtilsEx.date2String(WorkDateUtil.getWorkDate(),
					"yyyy-MM-dd");
		}
		//@invalid 4、 若开始时间不为空，结束时间为空，那么结束时间与开始时间保持相同
		if ((startDate != null && !"".equals(startDate))
				&& (endDate == null || "".equals(endDate))) {
			endDate = startDate;
		}
		//@invalid 5、 若开始时间为空，结束时间不为空，那么结束时间与开始时间保持相同
		if ((startDate == null || "".equals(startDate))
				&& (endDate != null && !"".equals(endDate))) {
			startDate = endDate;
		}
		//@invalid 5、 若开始日期和结束日期均未录入，则默认推送系统当前日期前一天的数据。开始日期与结束日期之间最大间隔不超过一个月。
		//@invalid （如录入开始日期为2018-10-22，结束日期为2018-10-27，则将保全确认生效日期为2018-10-21至2018-10-26之间的数据推送至统一消息平台数据库表中）
		Date starttime = null;
		Date endtime = null;
		try {
			starttime = DateUtilsEx.toDate(startDate, "yyyy-MM-dd");
			endtime = DateUtilsEx.toDate(endDate, "yyyy-MM-dd");
		} catch (Exception e) {
			e.printStackTrace();
			throw new BizException(e.getMessage());
		}

		//@invalid （如录入开始日期为2018-10-22，结束日期为2018-10-27，则将保全确认生效日期为2018-10-21至2018-10-26之间的数据推送至统一消息平台数据库表中）
		//@invalid 那么sql查询的具体时间条件是2018-10-21 00:00:00 至 2018-10-26 23:59:59
		starttime = DateUtilsEx.addDay(starttime, -1);

		//@invalid 查询的截止结束时间为录入时间的前一秒，
		Calendar cal = Calendar.getInstance();
		cal.setTime(endtime);
		cal.add(Calendar.SECOND, -1);
		endtime = cal.getTime();

		HolderMobileSyncBO holderMobileSyncBO = new HolderMobileSyncBO();
		holderMobileSyncBO.setStartDate(starttime);
		holderMobileSyncBO.setEndDate(endtime);
		logger.info("投保人手机号同步批处理:"+starttime+" "+endtime);
		List<HolderMobileSyncBO> resultList = holderMobileSyncService
				.queryChangeHolderMobile(holderMobileSyncBO);
		if (CollectionUtilEx.isNotEmpty(resultList)) {
			for (HolderMobileSyncBO resultBO : resultList) {
				Map<String, Object> data = new HashMap<String, Object>();
				data.put("HolderMobileSyncBO", resultBO);
				JobData jobData = new JobData();
				jobData.setData(data);
				jobDataList.add(jobData);
			}
		} else {
			logger.debug("***********************不存在需要同步的投保人电话*************************");
		}
		return jobDataList;
	}

	/**
	 * 
	 * @description 批处理明细查询
	 * @version 1.0
	 * @title 批处理明细查询
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#execute(com.nci.udmp.component.batch.context.JobSessionContext,
	 *      java.util.List, int)
	 * @param arg0
	 *            上下文
	 * @param arg1
	 *            开始
	 * @param arg2
	 *            MOD数据
	 * @return 返回执行结果
	 */
	@Override
	public List<JobData> execute(JobSessionContext arg0, List<JobData> arg1, int arg2) {
		logger.info("投保人手机号同步批处理执行开始 ");
		if (CollectionUtilEx.isNotEmpty(arg1)
				&& arg1.size() > Constants.ZERO_INT) {
			List<HolderMobileSyncBO> holderMobileSyncBOList = new ArrayList<HolderMobileSyncBO>();
			for (JobData jobData : arg1) {
				holderMobileSyncBOList.add((HolderMobileSyncBO) jobData
						.getData().get("HolderMobileSyncBO"));
			}
			holderMobileSyncService
					.syncChangeHolderMobile(holderMobileSyncBOList);
		}
		return arg1;
	}

	/**
	 * 
	 * @description 回写
	 * @version 1.0
	 * @title 回写
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#write(com.nci.udmp.component.batch.context.JobSessionContext,
	 *      com.nci.udmp.component.batch.core.agent.job.JobData)
	 * @param arg0
	 *            上下文
	 * @param arg1
	 *            回写数据
	 */
	@Override
	public void write(JobSessionContext arg0, JobData arg1) {

	}

	/**
	 * 
	 * @description 返回IDname
	 * @version 1.0
	 * @title 返回IDname
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#getIdName()
	 * @return 返回返回IDname
	 */
	@Override
	public String getIdName() {
		return null;
	}

	/**
	 * 
	 * @description 前置条件
	 * @version 1.0
	 * @title 前置条件
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#isCanBeRun()
	 * @return 返回前置条件
	 */
	@Override
	public boolean isCanBeRun() {
		return true;
	}

	/**
	 * 
	 * @description 异常处理
	 * @version 1.0
	 * @title 异常处理
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobErrorAudit(com.nci.udmp.component.batch.context.JobSessionContext,
	 *      com.nci.udmp.component.batch.core.agent.job.JobData)
	 * @param arg0 上下文
	 * @param arg1 批处理数据
	 */
	@Override
	public void jobErrorAudit(JobSessionContext arg0, JobData arg1) {
	}

	/**
	 * 
	 * @description 停批处理
	 * @version 1.0
	 * @title 停批处理
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobStop()
	 */
	@Override
	public void jobStop() {
	}

}
