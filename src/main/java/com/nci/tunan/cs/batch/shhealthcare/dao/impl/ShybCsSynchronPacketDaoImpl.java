package com.nci.tunan.cs.batch.shhealthcare.dao.impl;

import java.util.List;

import com.nci.tunan.cs.batch.shhealthcare.dao.IShybCsSynchronPacketDao;
import com.nci.tunan.cs.batch.shhealthcare.po.ShybCsSynchronPacketPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.util.xml.transform.XmlHelper;
/**
 * 上海医保保全信息同步上传批处理异步Dao实现
 * @description 
 * @<NAME_EMAIL> 
 * @date 2021年6月25日 下午3:52:37 
 * @.belongToModule 保全-上海医保批处理
 */
public class ShybCsSynchronPacketDaoImpl extends BaseDaoImpl implements IShybCsSynchronPacketDao {

	/**
	 * 上海医保保全信息同步上传批处理查询
	 * @description
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.batch.shhealthcare.dao.IShybCsSynchronPacketDao#queryShybCsSynchronPacketInfo(com.nci.tunan.cs.batch.shhealthcare.po.ShybCsSynchronPacketPO)
	 * @param shybCsSynchronPacketPO
	 * @return
	 */
	@Override
	public List<ShybCsSynchronPacketPO> queryShybCsSynchronPacketInfo(ShybCsSynchronPacketPO shybCsSynchronPacketPO) {
		logger.debug("<======ShybCsSynchronPacketDaoImpl--queryShybCsSynchronPacketInfoList======>");
		logger.info("上海医保保全信息同步上传批处理入参" +XmlHelper.classToXml(shybCsSynchronPacketPO));
		shybCsSynchronPacketPO.setString("SkipLimit", "");
		return findAll("queryShybCsSynchronPacketInfoList", shybCsSynchronPacketPO);
	}

}
