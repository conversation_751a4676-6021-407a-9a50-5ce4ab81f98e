package com.nci.tunan.cs.batch.shhealthcare.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

/**
 * 上海医保保全信息同步上传批处理-异步PO对象
 * 
 * @description
 * @<NAME_EMAIL>
 * @date 2021年6月25日 下午3:10:41
 * @.belongToModule 保全-上海医保批处理
 */
public class ShybCsSynchronPacketPO extends BasePO {

	private static final long serialVersionUID = 1L;

	public void setModNum(BigDecimal modNum) {
		setBigDecimal("modNum", modNum);
	}

	public BigDecimal getModNum() {
		return getBigDecimal("modNum");
	}

	public void setStart(BigDecimal start) {
		setBigDecimal("start", start);
	}

	public BigDecimal getStart() {
		return getBigDecimal("start");
	}

	// 保单号
	public void setPolicyCode(String policyCode) {
		setString("policy_code", policyCode);
	}

	public String getPolicyCode() {
		return getString("policy_code");
	}

	public String getServiceCode() {
		return getString("service_code");
	}

	public void setServiceCode(String serviceCode) {
		setString("service_code", serviceCode);
	}

	public void setChangeId(BigDecimal changeId) {
		setBigDecimal("change_id", changeId);
	}

	public BigDecimal getChangeId() {
		return getBigDecimal("change_id");
	}

	public void setAcceptId(BigDecimal acceptId) {
		setBigDecimal("accept_id", acceptId);
	}

	public BigDecimal getAcceptId() {
		return getBigDecimal("accept_id");
	}

	public void setValidateTime(Date validateTime) {
		setUtilDate("validate_time", validateTime);
	}

	public Date getValidateTime() {
		return getUtilDate("validate_time");
	}

	@Override
	public String toString() {
		return "ShybCsSynchronPO [getModNum()=" + getModNum() + ", getStart()=" + getStart() + ", getPolicyCode()="
				+ getPolicyCode() + ", getServiceCode()=" + getServiceCode() + ", getChangeId()=" + getChangeId()
				+ ", getAcceptId()=" + getAcceptId() + ", getValidateTime()=" + getValidateTime() + "]";

	}
}
