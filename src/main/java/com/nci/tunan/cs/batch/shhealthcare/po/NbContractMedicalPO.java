package com.nci.tunan.cs.batch.shhealthcare.po;

import java.math.BigDecimal;

import com.nci.udmp.framework.model.BasePO;
/** 
 * @description 上海医保主表PO
 * <AUTHOR> 
 * @date 2020年3月10日 下午5:25:51 
 * @.belongToModule 保全-上海医保主表
*/
public class NbContractMedicalPO extends BasePO{

	/** 
	* @Fields serialVersionUID : 版本序列号
	*/ 
	private static final long serialVersionUID = 1L;
	public BigDecimal getMedicalId() {
		return getBigDecimal("medical_id");
	}

	public void setMedicalId(BigDecimal medicalId) {
		setBigDecimal("medical_id", medicalId);
	}
	public String getApplyCode() {
		return getString("apply_code");
	}

	public void setApplyCode(String applyCode) {
		setString("apply_code",applyCode);
	}

	public String getPolicyFormerNo() {
		return getString("policy_former_no");
	}

	public void setPolicyFormerNo(String policyFormerNo) {
		setString("policy_former_no",policyFormerNo);
	}

	public String getPolicySequenceNo() {
		return getString("policy_sequence_no");
	}

	public void setPolicySequenceNo(String policySequenceNo) {
		setString("policy_sequence_no",policySequenceNo);
	}

	public String getCustomerSequenceNo() {
		return getString("customer_sequence_no");
	}

	public void setCustomerSequenceNo(String customerSequenceNo) {
		setString("customer_sequence_no",customerSequenceNo);
	}

	public String getUnderwritSequenceNo() {
		return getString("underwrit_sequence_no");
	}

	public void setUnderwritSequenceNo(String underwritSequenceNo) {
		setString("underwrit_sequence_no",underwritSequenceNo);
	}

	public String getMedicalNo() {
		return getString("medical_no");
	}

	public void setMedicalNo(String medicalNo) {
		setString("medical_no",medicalNo);
	}

	public String getValidateMode() {
		return getString("validate_mode");
	}

	public void setValidateMode(String validateMode) {
		setString("validate_mode",validateMode);
	}
	public String getMedicalPayOrder() {
		return getString("medical_pay_order");
	}

	public void setMedicalPayOrder(String medicalPayOrder) {
		setString("medical_pay_order",medicalPayOrder);
	}
	public String getInsureScene() {
		return getString("insure_scene");
	}

	public void setInsureScene(String insureScene) {
		setString("insure_scene",insureScene);
	}
	public String getShJobCode() {
		return getString("sh_job_code");
	}

	public void setShJobCode(String shJobCode) {
		setString("sh_job_code",shJobCode);
	}
}
