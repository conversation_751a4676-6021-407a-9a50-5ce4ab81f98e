package com.nci.tunan.cs.batch.autoassignwork.dao.impl;

import java.util.List;

import com.nci.tunan.cs.batch.autoassignwork.dao.IAutoAssignWorkDao;
import com.nci.tunan.cs.batch.autoassignwork.po.AutoAssignWorkPO;
import com.nci.tunan.cs.model.po.CsAcceptChangePO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;

/**
 * 
 * @description 保全复核任务自动分配任务 
 * <AUTHOR> <EMAIL> 
 * @date 2020-9-3 下午9:13:45 
 * @.belongToModule CS-保全子系统-保全复核任务自动分配任务 
 */
public class AutoAssignWorkDaoImpl extends BaseDaoImpl implements IAutoAssignWorkDao {

	/**
	 * 
	 * @description 查询复核用户的信息
	 * @version 1.0
	 * @title 查询复核用户的信息
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.batch.autoassignwork.dao.IAutoAssignWorkDao#queryAutoAssignWork(com.nci.tunan.cs.batch.autoassignwork.po.AutoAssignWorkPO)
	 * @param tAutoAssignWorkPO 复核用户的信息的查询条件
	 * @return 返回结果
	 */
	@Override
	public List<AutoAssignWorkPO> queryAutoAssignWork(AutoAssignWorkPO tAutoAssignWorkPO) {
		return findAll("query_AutoAssignWork", tAutoAssignWorkPO);
	}
	/**
	 *
	 * @description 查询复核权限的信息
	 * @version 1.0
	 * @title 查询复核的信息
	 * <AUTHOR> <EMAIL>
	 * @param tAutoAssignWorkPO 权限入参
	 * @return 权限集合
	 */
	public List<AutoAssignWorkPO> queryBQ01_12(AutoAssignWorkPO tAutoAssignWorkPO){
		return findAll("query_BQ01_12", tAutoAssignWorkPO);
	}
	
	/**
	 * @description 查询该用户下的复核权限集合
	 * @version 1.0
	 * @param tAutoAssignWorkPO 用户ID
	 * @return 权限集合
	 */
	@Override
	public List<AutoAssignWorkPO> findUserRolePermission(AutoAssignWorkPO tAutoAssignWorkPO) {
		
		return findAll("findUserRolePermission", tAutoAssignWorkPO);
	}
	
	/**
	 * 
	 * @description 查询复核用户的信息
	 * @version 1.0
	 * @title 查询复核用户的信息
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.batch.autoassignwork.dao.IAutoAssignWorkDao#queryAutoAssignWork(com.nci.tunan.cs.batch.autoassignwork.po.AutoAssignWorkPO)
	 * @param tAutoAssignWorkPO 复核用户的信息的查询条件
	 * @return 返回结果
	 */
	@Override
	public List<AutoAssignWorkPO> queryAutoAssignWorkForDoubleAudit(AutoAssignWorkPO tAutoAssignWorkPO) {
		return findAll("query_AutoAssignWorkForDoubleAudit", tAutoAssignWorkPO);
	}
	
	
	/**
	 * 
	 * @description 查询受理相关信息，用于判断是否属于上收业务
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.batch.autoassignwork.dao.IAutoAssignWorkDao#queryAcceptChangeData(com.nci.tunan.cs.model.po.CsAcceptChangePO)
	 * @param csAcceptChangePO
	 * @return
	 */
    @Override
    public CsAcceptChangePO queryAcceptChangeData(CsAcceptChangePO csAcceptChangePO) {
        return findObject("queryAcceptChangeData", csAcceptChangePO);
    }


}
