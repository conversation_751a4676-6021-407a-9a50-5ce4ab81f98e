package com.nci.tunan.cs.batch.shhealthcare.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.CopyUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;

import com.git.common.string.StringUtil;
import com.nci.tunan.cap.shhealthcare.interfaces.model.bo.PremArapMedicalBO;
import com.nci.tunan.cap.util.HealthCareXmlUtil;
import com.nci.tunan.cs.batch.shhealthcare.bo.BodyCoverageBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.CoveragePolicyBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.CoveragePolicyBeneficialBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.CoveragePolicyInsuredBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.CoveragePolicyInsuredInfoBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.CoveragePolicyInsuredRiskBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.CoveragePolicyInsuredRiskResultBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.CoveragePolicyRiskBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.CoveragePolicyRiskResultBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.CoverageSinglePolicyBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.CoverageSinglePolicyHolderBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.HeadBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.NbContractMedicalBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.PacketCoverageBO;
import com.nci.tunan.cs.batch.shhealthcare.bo.SHReneworSurrenderBO;
import com.nci.tunan.cs.batch.shhealthcare.dao.IShybXQCoverageInfoDao;
import com.nci.tunan.cs.batch.shhealthcare.po.CoveragePolicyBeneficiaPO;
import com.nci.tunan.cs.batch.shhealthcare.po.CoveragePolicyInsuredPO;
import com.nci.tunan.cs.batch.shhealthcare.po.CoveragePolicyInsuredRiskPO;
import com.nci.tunan.cs.batch.shhealthcare.po.CoveragePolicyPO;
import com.nci.tunan.cs.batch.shhealthcare.po.CoveragePolicyRiskPO;
import com.nci.tunan.cs.batch.shhealthcare.po.CoverageSinglePolicyPO;
import com.nci.tunan.cs.batch.shhealthcare.po.NbPolicyHolderPO;
import com.nci.tunan.cs.batch.shhealthcare.po.SHReneworSurrenderPO;
import com.nci.tunan.cs.batch.shhealthcare.service.ISHReneworSurrenderService;
import com.nci.tunan.cs.imports.ICPAService;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.properties.PropertiesUtil;

/**
 * 
 * @description 上海医保续保退保接口批处理
 * <AUTHOR> <EMAIL> 
 * @date 2015年05月15日 下午3:26:51 
 * @.belongToModule 保全子系统
 */
public class SHReneworSurrenderServiceImpl implements ISHReneworSurrenderService{
	private  static  Logger logger = LoggerFactory.getLogger();
	/**
	 * 收付费Service
	 */
	private ICPAService capServiceSaveArap;
	/**
	 * 上海医保续期承保接口Dao
	 */
	private IShybXQCoverageInfoDao ShybXQCoverageInfoDao;

	/**
	 * 
	 * @description 上海医保续期承保上传接口查询方法
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.batch.shhealthcare.service.ISHReneworSurrenderService#queryReneworSurrenderInfo(com.nci.tunan.cs.batch.shhealthcare.bo.SHReneworSurrenderBO)
	 * @param shreneworsurrenderBO 入参BO
	 * @return List<SHReneworSurrenderBO>
	 */
	@Override
	public List<SHReneworSurrenderBO> querySHYBCapInfo(SHReneworSurrenderBO shreneworsurrenderBO) {
		com.nci.tunan.cap.shhealthcare.interfaces.model.bo.ShHealthcareBatchBO ShHealthcareBatchBO=new com.nci.tunan.cap.shhealthcare.interfaces.model.bo.ShHealthcareBatchBO(); 
		ShHealthcareBatchBO.setArapflag(shreneworsurrenderBO.getArapflag());
		ShHealthcareBatchBO.setPolicyCode(shreneworsurrenderBO.getPolicyCode());
		ShHealthcareBatchBO.setPolicyformerNo(shreneworsurrenderBO.getPolicyformerNo());
		ShHealthcareBatchBO.setPolicySequenceNo(shreneworsurrenderBO.getPolicySequenceNo());
		List<com.nci.tunan.cap.shhealthcare.interfaces.model.bo.ShHealthcareBatchBO> shReneworSurrenderBO = capServiceSaveArap.capQuerySHReneworSurrenderInfo(ShHealthcareBatchBO);
		List<SHReneworSurrenderBO>  shreneworSurrenderBOList = BeanUtils.copyList(SHReneworSurrenderBO.class, shReneworSurrenderBO);

		return shreneworSurrenderBOList;
	}

	/**
	 * 
	 * @description 上海医保续保退保接口保存退保流水号
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.batch.shhealthcare.service.ISHReneworSurrenderService#saveSHYBcsLmNo(com.nci.tunan.cs.batch.shhealthcare.bo.SHReneworSurrenderBO)
	 * @param shreneworsurrenderBO 入参BO
	 * @return SHReneworSurrenderBO
	 */
	@Override
	public SHReneworSurrenderBO saveSHYBcsLmNo(SHReneworSurrenderBO shreneworsurrenderBO) {
		com.nci.tunan.cap.shhealthcare.interfaces.model.bo.ShHealthcareBatchBO ShHealthcareBatchBO=new com.nci.tunan.cap.shhealthcare.interfaces.model.bo.ShHealthcareBatchBO(); 
		ShHealthcareBatchBO.setApplyCode(shreneworsurrenderBO.getApplyCode());
		ShHealthcareBatchBO.setCsLmNo(shreneworsurrenderBO.getCsLmNo());
		ShHealthcareBatchBO = capServiceSaveArap.capsaveSHYBcsLmNoInfo(ShHealthcareBatchBO);
		shreneworsurrenderBO=BeanUtils.copyProperties(SHReneworSurrenderBO.class,ShHealthcareBatchBO);
		return shreneworsurrenderBO;
	}

	/**
	 * 
	 * @description 上海医保续保退保接口保存退保流水号
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.batch.shhealthcare.service.ISHReneworSurrenderService#saveAddSHYBcsLmNoInfo(com.nci.tunan.cs.batch.shhealthcare.bo.SHReneworSurrenderBO)
	 * @param shreneworsurrenderBO 入参BO
	 * @return SHReneworSurrenderBO
	 */
	@Override
	public SHReneworSurrenderBO saveAddSHYBcsLmNoInfo(SHReneworSurrenderBO shreneworsurrenderBO) {
		com.nci.tunan.cap.shhealthcare.interfaces.model.bo.ShHealthcareBatchBO ShHealthcareBatchBO=new com.nci.tunan.cap.shhealthcare.interfaces.model.bo.ShHealthcareBatchBO(); 
		ShHealthcareBatchBO.setApplyCode(shreneworsurrenderBO.getApplyCode());
		ShHealthcareBatchBO.setCsLmNo(shreneworsurrenderBO.getCsLmNo());
		ShHealthcareBatchBO.setPayOrder(shreneworsurrenderBO.getPayOrder());
		ShHealthcareBatchBO = capServiceSaveArap.addLineSHYBcsLmNoInfo(ShHealthcareBatchBO);
		shreneworsurrenderBO=BeanUtils.copyProperties(SHReneworSurrenderBO.class,ShHealthcareBatchBO);
		return shreneworsurrenderBO;
	}
	/**
	 * 
	 * @description 上海医保续期承保上传批处理-查询续期保单
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.batch.shhealthcare.service.ISHReneworSurrenderService#queryReneworSurrenderInfo(com.nci.tunan.cs.batch.shhealthcare.bo.SHReneworSurrenderBO)
	 * @param shreneworsurrenderBO 入参BO
	 * @return List<SHReneworSurrenderBO>
	 */
	@Override
	public List<SHReneworSurrenderBO> queryXQpolicyInfo( SHReneworSurrenderBO shreneworsurrenderBO) {
		SHReneworSurrenderPO shReneworSurrenderPO = BeanUtils.copyProperties(SHReneworSurrenderPO.class, shreneworsurrenderBO);
		List<SHReneworSurrenderPO> shReneworSurrenderPOList=ShybXQCoverageInfoDao.queryXQpolicyInfo(shReneworSurrenderPO);
		List<SHReneworSurrenderBO>  shreneworSurrenderBOList = BeanUtils.copyList(SHReneworSurrenderBO.class, shReneworSurrenderPOList);

		return shreneworSurrenderBOList;
	}
	/**
	 * 
	 * @description 上海医保续期承保上传批处理-保存保单编码
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.cs.batch.shhealthcare.service.ISHReneworSurrenderService#queryReneworSurrenderInfo(com.nci.tunan.cs.batch.shhealthcare.bo.SHReneworSurrenderBO)
	 * @param shreneworsurrenderBO 入参BO
	 * @return String
	 */
	@Override
	public String saveCoverageSequenceNo( SHReneworSurrenderBO shreneworsurrenderBO) {
		//查询保单信息
		CoveragePolicyPO uwPO=new CoveragePolicyPO();
		uwPO.setPolicyCode(shreneworsurrenderBO.getPolicyCode());
		uwPO = ShybXQCoverageInfoDao.findCoveragePolicyInfo(uwPO);
		//
		/*if(uwPO.getMasterExpireDate().getTime() != uwPO.getExpireDate().getTime()){
			CoveragePolicyPO updateExpireUwPO=new CoveragePolicyPO();
			updateExpireUwPO.setPolicyCode(shreneworsurrenderBO.getPolicyCode());
			updateExpireUwPO.setExpireDate(uwPO.getExpireDate());
			ShybXQCoverageInfoDao.updateMasterExpireDate(updateExpireUwPO);
			
		}*/
		//
		CoveragePolicyBO reUwBO = BeanUtils.copyProperties(CoveragePolicyBO.class,uwPO);
		reUwBO.setDerivation("04");//续期
		transType(reUwBO);
		//日期格式转换为yyyyMMdd	
		if (uwPO.getExpireDate()!=null) {
			String strDate = transToDate(DateUtilsEx.addDay(uwPO.getExpireDate(),-1));
			reUwBO.setExpireDate(strDate);//保单满期日期	
		}			
		String validateDate = transToDate(uwPO.getValidateDate());
		reUwBO.setValidateDate(validateDate);//保单起保日期		
		String applyDate = transToDate(uwPO.getApplyDate());
		reUwBO.setApplyDate(applyDate);	
		String acceptDate = transToDate(uwPO.getAcceptDate());
		reUwBO.setAcceptDate(acceptDate);	
		String regularClearingDate = transToDate(uwPO.getRegularClearingDate());
		reUwBO.setRegularClearingDate(regularClearingDate);		
		String premiumdueDate = transToDate(uwPO.getPremiumdueDate());
		reUwBO.setPremiumdueDate(premiumdueDate);
		//电子保单链接
		Map<String, String> proMap = PropertiesUtil.getMapFromPropertiesPath(HealthCareXmlUtil.NB_SH_PATH);
		String eleUrl = (String) proMap.get(HealthCareXmlUtil.NB_SH_ELEURL);
		reUwBO.setElePolicyUrl(eleUrl);
		//调用api查询收付费医保保费来源和缴费结果确认编码
		shreneworsurrenderBO.setArapflag("0");//查询
		List<SHReneworSurrenderBO> returnBO = querySHYBCapInfo(shreneworsurrenderBO);
		if (StringUtil.isNotBlank(returnBO) && StringUtil.isNotBlank(returnBO.get(0).getPaymentResultSequenceNo())) {
			reUwBO.setPayResSequenceNo(returnBO.get(0).getPaymentResultSequenceNo());
		}else{
			reUwBO.setPayResSequenceNo("");
		}
		if (StringUtil.isNotBlank(returnBO) && StringUtil.isNotBlank(returnBO.get(0).getPayOrder())) {
	         if("2".equals(returnBO.get(0).getPayOrder())){
	        	 reUwBO.setMedicalPremiumSource("1");
	         }else if("3".equals(returnBO.get(0).getPayOrder())){
	        	 reUwBO.setMedicalPremiumSource("2");
	         }else{
	        	 if (StringUtil.isBlank(reUwBO.getPayResSequenceNo())) {
	        		 reUwBO.setMedicalPremiumSource("1");
				 }else{
					 reUwBO.setMedicalPremiumSource("2");
				 }
	         }		
		}
		else{
			reUwBO.setMedicalPremiumSource("");
		}
		//调用api结束
		//查询保单险种信息
		CoveragePolicyRiskPO uwPolicyRiskPO=new CoveragePolicyRiskPO();
		uwPolicyRiskPO.setPolicyCode(shreneworsurrenderBO.getPolicyCode());
		List<CoveragePolicyRiskPO> reUwPolicyRiskPO = ShybXQCoverageInfoDao.findCgPolicyRiskInfo(uwPolicyRiskPO);
		//缴费类型码值转换
		paymodeTransType(reUwPolicyRiskPO);	
		//保单险种核定信息
		List<CoveragePolicyRiskResultBO> reUwPolicyRiskResultBOList = BeanUtils.copyList(CoveragePolicyRiskResultBO.class, reUwPolicyRiskPO);
		List<CoveragePolicyRiskBO> reUwPolicyRiskBOList=BeanUtils.copyList(CoveragePolicyRiskBO.class,reUwPolicyRiskPO);
		for (int i = 0; i < reUwPolicyRiskBOList.size(); i++) {
			CoveragePolicyRiskBO reUwPolicyRiskBO = reUwPolicyRiskBOList.get(i);
			for (int j = i; j < reUwPolicyRiskResultBOList.size(); j++) {
				CoveragePolicyRiskResultBO reUwPolicyRiskResultBO = reUwPolicyRiskResultBOList.get(j);
				String undatestr = transToDateHMS(reUwPolicyRiskResultBO.getUnderwritingDate());
				reUwPolicyRiskResultBO.setUnderwritingDatestr(undatestr);
				reUwPolicyRiskResultBO.setUnderwritingDate(null);				
				reUwPolicyRiskBO.setUwPolicyRiskResultBO(reUwPolicyRiskResultBO);
				reUwPolicyRiskBO.setRiskEffectiveDatestr(transToDate(reUwPolicyRiskBO.getRiskEffectiveDate()));
				reUwPolicyRiskBO.setRiskEffectiveDate(null);
				if (reUwPolicyRiskBO.getRiskExpireDate()!=null) {
					reUwPolicyRiskBO.setRiskExpireDatestr(transToDate(DateUtilsEx.addDay(reUwPolicyRiskBO.getRiskExpireDate(),-1)));					
				}else{
					reUwPolicyRiskBO.setRiskExpireDatestr(null);
				}
				reUwPolicyRiskBO.setRiskExpireDate(null);			
				break;
			}
		}
		//查询保单个人数据
		CoverageSinglePolicyPO uwSinglePolicyPO=new CoverageSinglePolicyPO();
		uwSinglePolicyPO.setPolicyCode(shreneworsurrenderBO.getPolicyCode());
		CoverageSinglePolicyPO reUwSinglePolicyPO = ShybXQCoverageInfoDao.findCgSinglePolicyInfo(uwSinglePolicyPO);
		CoverageSinglePolicyBO reUwSinglePolicyBO = BeanUtils.copyProperties(CoverageSinglePolicyBO.class,reUwSinglePolicyPO);
		if(null != reUwBO.getBankCode()){
			reUwSinglePolicyBO.setBankName(reUwBO.getBankCode());
		}
		if(StringUtilsEx.isNullOrEmpty(reUwSinglePolicyBO.getBankNo())){
			reUwSinglePolicyBO.setBankNo("9999");
		}
		if(StringUtilsEx.isNullOrEmpty(reUwSinglePolicyBO.getBankName())){
			reUwSinglePolicyBO.setBankName("未指定银行");
		}
		
		//查询被保人数据
		CoveragePolicyInsuredPO uwInsuredPolicyPO =new CoveragePolicyInsuredPO();
		uwInsuredPolicyPO.setPolicyCode(shreneworsurrenderBO.getPolicyCode());
		List<CoveragePolicyInsuredPO> reUwInsuredPolicyPO = ShybXQCoverageInfoDao.findCgInsuredPolicyInfo(uwInsuredPolicyPO);
		List<CoveragePolicyInsuredBO> reUwInsuredPolicyBOList = BeanUtils.copyList(CoveragePolicyInsuredBO.class,reUwInsuredPolicyPO);
		for (int i = 0; i < reUwInsuredPolicyBOList.size(); i++) {
			CoveragePolicyInsuredBO underwritepolicyinsuredbo = reUwInsuredPolicyBOList.get(i);
			String insuredeffectivedate = transToDate(underwritepolicyinsuredbo.getInsuredEffectiveDate());
			underwritepolicyinsuredbo.setInsuredEffectiveDatestr(insuredeffectivedate);
			if (underwritepolicyinsuredbo.getInsuredExpireDate()!=null) {
				underwritepolicyinsuredbo.setInsuredExpireDatestr(transToDate(DateUtilsEx.addDay(underwritepolicyinsuredbo.getInsuredExpireDate(),-1)));							
			}else{
				underwritepolicyinsuredbo.setInsuredExpireDatestr(null);
			}
			underwritepolicyinsuredbo.setInsuredEffectiveDate(null);
			underwritepolicyinsuredbo.setInsuredExpireDate(null);
			//钱保留2位
			if (underwritepolicyinsuredbo.getLifeSumInsured()!=null&&(underwritepolicyinsuredbo.getLifeSumInsured().compareTo(BigDecimal.ZERO)==1)) {//寿险风险保额
				underwritepolicyinsuredbo.setLifeSumInsured(transToScale(underwritepolicyinsuredbo.getLifeSumInsured()));
			}
			if (underwritepolicyinsuredbo.getAccidentSumInsured()!=null&&(underwritepolicyinsuredbo.getAccidentSumInsured().compareTo(BigDecimal.ZERO)==1)) {//意外险风险保额
				underwritepolicyinsuredbo.setAccidentSumInsured(transToScale(underwritepolicyinsuredbo.getAccidentSumInsured()));
			}
			if (underwritepolicyinsuredbo.getCriticalIllnessSumInsured()!=null&&(underwritepolicyinsuredbo.getCriticalIllnessSumInsured().compareTo(BigDecimal.ZERO)==1)) {//重大疾病险风险保额
				underwritepolicyinsuredbo.setCriticalIllnessSumInsured(transToScale(underwritepolicyinsuredbo.getCriticalIllnessSumInsured()));
			}
			if (underwritepolicyinsuredbo.getHospitalAllowance()!=null&&(underwritepolicyinsuredbo.getHospitalAllowance().compareTo(BigDecimal.ZERO)==1)) {//住院日额
				underwritepolicyinsuredbo.setHospitalAllowance(transToScale(underwritepolicyinsuredbo.getHospitalAllowance()));
			}
		}
		//查询被保人险种数据
		CoveragePolicyInsuredRiskPO uwInsuredRiskPolicyPO=new CoveragePolicyInsuredRiskPO();
		uwInsuredRiskPolicyPO.setPolicyCode(shreneworsurrenderBO.getPolicyCode());
		List<CoveragePolicyInsuredRiskPO> reUwInsuredRiskPolicyPOList = ShybXQCoverageInfoDao.findCgInsuredRiskPolicyInfo(uwInsuredRiskPolicyPO);
		//被保险人险种核定
		List<CoveragePolicyInsuredRiskResultBO> reUwInsuredRiskResultBOList = BeanUtils.copyList(CoveragePolicyInsuredRiskResultBO.class,reUwInsuredRiskPolicyPOList);
		List<CoveragePolicyInsuredRiskBO> reUwInsuredRiskPolicyBOList = BeanUtils.copyList(CoveragePolicyInsuredRiskBO.class,reUwInsuredRiskPolicyPOList);
		for (int i = 0; i < reUwInsuredRiskPolicyBOList.size(); i++) {
			CoveragePolicyInsuredRiskBO reUwInsuredRiskBO = reUwInsuredRiskPolicyBOList.get(i);
			//钱保留2位
			if (reUwInsuredRiskBO.getCoveragePremium()!=null&&(reUwInsuredRiskBO.getCoveragePremium().compareTo(BigDecimal.ZERO)==1)) {//险种保费
				reUwInsuredRiskBO.setCoveragePremium(transToScale(reUwInsuredRiskBO.getCoveragePremium()));
			}
			if (reUwInsuredRiskBO.getCoveragEpremium()!=null&&(reUwInsuredRiskBO.getCoveragEpremium().compareTo(BigDecimal.ZERO)==1)) {//险种期初保费
				reUwInsuredRiskBO.setCoveragEpremium(transToScale(reUwInsuredRiskBO.getCoveragEpremium()));
			}
			if (reUwInsuredRiskBO.getCoverageCurrentPremium()!=null&&(reUwInsuredRiskBO.getCoverageCurrentPremium().compareTo(BigDecimal.ZERO)==1)) {//险种当前保费
				reUwInsuredRiskBO.setCoverageCurrentPremium(transToScale(reUwInsuredRiskBO.getCoverageCurrentPremium()));
			}
			if (reUwInsuredRiskBO.getCoverageSumInsured()!=null&&(reUwInsuredRiskBO.getCoverageSumInsured().compareTo(BigDecimal.ZERO)==1)) {//险种保额
				reUwInsuredRiskBO.setCoverageSumInsured(transToScale(reUwInsuredRiskBO.getCoverageSumInsured()));
			}
			if (reUwInsuredRiskBO.getCoverageEffectiveSuminsured()!=null&&(reUwInsuredRiskBO.getCoverageEffectiveSuminsured().compareTo(BigDecimal.ZERO)==1)) {//险种有效保额
				reUwInsuredRiskBO.setCoverageEffectiveSuminsured(transToScale(reUwInsuredRiskBO.getCoverageEffectiveSuminsured()));
			}
			if (reUwInsuredRiskBO.getCoverageDayAmount()!=null&&(reUwInsuredRiskBO.getCoverageDayAmount().compareTo(BigDecimal.ZERO)==1)) {//险种期初日额保险金
				reUwInsuredRiskBO.setCoverageDayAmount(transToScale(reUwInsuredRiskBO.getCoverageDayAmount()));
			}
			if (reUwInsuredRiskBO.getCoverageDayeffectiveAmount()!=null&&(reUwInsuredRiskBO.getCoverageDayeffectiveAmount().compareTo(BigDecimal.ZERO)==1)) {//险种有效日额保险金
				reUwInsuredRiskBO.setCoverageDayeffectiveAmount(transToScale(reUwInsuredRiskBO.getCoverageDayeffectiveAmount()));
			}
			if (reUwInsuredRiskBO.getCoverageYearPremium()!=null&&(reUwInsuredRiskBO.getCoverageYearPremium().compareTo(BigDecimal.ZERO)==1)) {//险种当前年化保费
				reUwInsuredRiskBO.setCoverageYearPremium(transToScale(reUwInsuredRiskBO.getCoverageYearPremium()));
			}
			for (int j = i; j < reUwInsuredRiskResultBOList.size(); j++) {
				CoveragePolicyInsuredRiskResultBO reUwInsuredRiskResultBO = reUwInsuredRiskResultBOList.get(j);
				String uwdatestr = transToDateHMS(reUwInsuredRiskResultBO.getUnderwritingDate());
				reUwInsuredRiskResultBO.setUnderwritingDatestr(uwdatestr);
				reUwInsuredRiskResultBO.setUnderwritingDate(null);			 
				reUwInsuredRiskBO.setUwPolicyInsuredRiskResultBO(reUwInsuredRiskResultBO);
				reUwInsuredRiskBO.setCoverageEffectiveDatestr(transToDate(reUwInsuredRiskBO.getCoverageEffectiveDate()));
				reUwInsuredRiskBO.setCoverageEffectiveDate(null);
				if (reUwInsuredRiskBO.getCoveragEexpireDate()!=null) {
					reUwInsuredRiskBO.setCoveragEexpireDatestr(transToDate(DateUtilsEx.addDay(reUwInsuredRiskBO.getCoveragEexpireDate(),-1)));						
				}else{
					reUwInsuredRiskBO.setCoveragEexpireDatestr(null);
				}
				reUwInsuredRiskBO.setCoveragEexpireDate(null);	
				break;
			}
		}
		for (int i = 0; i < reUwInsuredPolicyBOList.size(); i++) {
			CoveragePolicyInsuredBO reInsuredPolicyBO = reUwInsuredPolicyBOList.get(i);
			for (int j = 0; j < reUwInsuredRiskPolicyBOList.size(); j++) {
				CoveragePolicyInsuredRiskBO reInsuredRiskPolicyBO = reUwInsuredRiskPolicyBOList.get(j);
				if (reInsuredPolicyBO.getListId().equals(reInsuredRiskPolicyBO.getInsuredId())) {
					CoveragePolicyInsuredInfoBO coPolicyInsuredInfoBO=new CoveragePolicyInsuredInfoBO();
					coPolicyInsuredInfoBO.setCoPolicyInsuredRiskBO(reInsuredRiskPolicyBO);
					reInsuredPolicyBO.setUwPolicyInsuredInfoBO(coPolicyInsuredInfoBO);					
					reInsuredPolicyBO.setListId(null);
					reInsuredRiskPolicyBO.setInsuredId(null);					


				}	
			}
		}
		//查询受益人数据
		CoveragePolicyBeneficiaPO uwBeneficyPolicyPO=new CoveragePolicyBeneficiaPO();
		uwBeneficyPolicyPO.setPolicyCode(shreneworsurrenderBO.getPolicyCode());
		List<CoveragePolicyBeneficiaPO> reUwBeneficyPolicyPO = ShybXQCoverageInfoDao.findCgBeneficyPolicyInfo(uwBeneficyPolicyPO);
		List<CoveragePolicyBeneficialBO> reUwBeneficyPolicyBOList = BeanUtils.copyList(CoveragePolicyBeneficialBO.class,reUwBeneficyPolicyPO);
		
		PacketCoverageBO pkBO=new PacketCoverageBO();
		BodyCoverageBO bodyBO=new BodyCoverageBO();
		CoverageSinglePolicyHolderBO cgSinglePolicyHolderBO=new CoverageSinglePolicyHolderBO();
		cgSinglePolicyHolderBO.setCgSinglePolicyHolderBO(reUwSinglePolicyBO);
		cgSinglePolicyHolderBO.setUwPolicyInsuredBO(reUwInsuredPolicyBOList);
		cgSinglePolicyHolderBO.setUwPolicyBeneficialBO(reUwBeneficyPolicyBOList);
		reUwBO.setUwPolicyRiskBO(reUwPolicyRiskBOList);
		reUwBO.setSinglePolicyInfo(cgSinglePolicyHolderBO);
		reUwBO.setBankCode(null);//此码值未映射、传空
		bodyBO.setPolicyInfo(reUwBO);
		HeadBO headBO=new HeadBO();
		String user = (String) proMap.get(HealthCareXmlUtil.NB_SH_USER);
		String password = (String) proMap.get(HealthCareXmlUtil.NB_SH_PASSWORD);
		headBO.setUser(user);
		headBO.setPassword(password);
		headBO.setRequestType("N01");
		pkBO.setHead(headBO);
		pkBO.setBody(bodyBO);
		String policySeqNo = PostXQcoverageXML(pkBO,shreneworsurrenderBO);
		return policySeqNo;
	}
	/**
	 * 渠道转换
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param reUwBO 渠道信息
	 */
	public void transType(CoveragePolicyBO reUwBO){
		//销售渠道转换
		if (reUwBO.getChannelType()!=null && !"".equals(reUwBO.getChannelType())) {
			if ("0".equals(reUwBO.getChannelType())) {//无关或不确定
				reUwBO.setChannelType("99");
			}else if ("1".equals(reUwBO.getChannelType())) {//个险
				reUwBO.setChannelType("99");
			}else if ("2".equals(reUwBO.getChannelType())) {//团险
				reUwBO.setChannelType("17");
			}else if ("3".equals(reUwBO.getChannelType())) {//银行代理
				reUwBO.setChannelType("24");
			}else if ("4".equals(reUwBO.getChannelType())) {//续期
				reUwBO.setChannelType("99");
			}else if ("8".equals(reUwBO.getChannelType())) {//网络销售
				reUwBO.setChannelType("12");
			}
		}
		//保单状态转换
		if (reUwBO.getPolicyStatus()!=null && !"".equals(reUwBO.getPolicyStatus())) {
			if ((reUwBO.getPolicyStatus().compareTo(BigDecimal.ZERO)==0 ) || (reUwBO.getPolicyStatus().compareTo(new BigDecimal("4"))==0)) {//0 未生效 4失效
				reUwBO.setPolicyStatus(new BigDecimal ("9"));//其他
			}
		}
		//缴费方式-缴费频率
		if (reUwBO.getPayLocation()!=null && !"".equals(reUwBO.getPayLocation())) {
			if ("1".equals(reUwBO.getPayLocation())) {//趸交
				reUwBO.setPayLocation("01");
			}else if ("2".equals(reUwBO.getPayLocation())) {//月缴
				reUwBO.setPayLocation("02");
			}else if ("3".equals(reUwBO.getPayLocation())) {//季缴
				reUwBO.setPayLocation("03");
			}else if ("4".equals(reUwBO.getPayLocation())) {//半年缴
				reUwBO.setPayLocation("04");
			}else if ("5".equals(reUwBO.getPayLocation())) {//年缴
				reUwBO.setPayLocation("05");
			}else if ("6".equals(reUwBO.getPayLocation()) || "9".equals(reUwBO.getPayLocation())) {//6不定期缴 9其他
				reUwBO.setPayLocation("09");
			}
		}
		//钱保留2位小数
		if (reUwBO.getCurrentPremium()!=null&&(reUwBO.getCurrentPremium().compareTo(BigDecimal.ZERO)==1)) {//当前保费
			reUwBO.setCurrentPremium(transToScale(reUwBO.getCurrentPremium()));
		}
		if (reUwBO.getPremium()!=null&&(reUwBO.getPremium().compareTo(BigDecimal.ZERO)==1)) {//期初保费
			reUwBO.setPremium(transToScale(reUwBO.getPremium()));
		}
		if (reUwBO.getYearPremium()!=null&&(reUwBO.getYearPremium().compareTo(BigDecimal.ZERO)==1)) {//当前年化保费
			reUwBO.setYearPremium(transToScale(reUwBO.getYearPremium()));
		}
		if (reUwBO.getAmount()!=null&&(reUwBO.getAmount().compareTo(BigDecimal.ZERO)==1)) {//期初保额
			reUwBO.setAmount(transToScale(reUwBO.getAmount()));
		}
		if (reUwBO.getEffectiveAmount()!=null&&(reUwBO.getEffectiveAmount().compareTo(BigDecimal.ZERO)==1)) {//有效保额
			reUwBO.setEffectiveAmount(transToScale(reUwBO.getEffectiveAmount()));
		}
		if (reUwBO.getPolicyPremium()!=null&&(reUwBO.getPolicyPremium().compareTo(BigDecimal.ZERO)==1)) {//医保保单保费
			reUwBO.setPolicyPremium(transToScale(reUwBO.getPolicyPremium()));
		}
		//银行编码
		if (reUwBO.getBankCode()!=null && !"".equals(reUwBO.getBankCode())) {
			String newBankCode="";
			switch (reUwBO.getBankCode()) {
			case "8600030":
				newBankCode="北京银行";//1000
				break;
			case "8600460":
				newBankCode="东莞银行";//1002
				break;
			case "8600180":
				newBankCode="广发银行";//1003
				break;
			case "8600450":
				newBankCode="广州银行";//1004
				break;
			case "8600160":
				newBankCode="华夏银行";//1005
				break;
			case "8600470":
				newBankCode="江苏银行";//1006
				break;
			case "8600120":
				newBankCode="交通银行";//1007
				break;
			case "8600060":
				newBankCode="中国民生银行";//1008
				break;
			case "8600500":
				newBankCode="宁波银行";//1009
				break;
			case "8600110":
				newBankCode="农村信用联合银行";//1010
				break;
			case "8600490":
				newBankCode="平安银行";//1011
				break;
			case "8600130":
				newBankCode="上海浦东发展银行";//1013
				break;
			case "8600290":
				newBankCode="上海银行";//1014
				break;
			case "8600080":
				newBankCode="兴业银行";//1016
				break;
			case "8600070":
				newBankCode="招商银行";//1017
				break;
			case "8600010":
				newBankCode="中国工商银行";//1018
				break;
			case "8600150":
				newBankCode="中国光大银行";//1019
				break;
			case "8600020":
				newBankCode="中国建设银行";//1020
				break;
			case "8600050":
				newBankCode="中国农业银行";//1021
				break;
			case "8600090":
				newBankCode="中国银行";//1022
				break;
			case "8600040":
				newBankCode="中国邮政储蓄银行";//1023
				break;
			case "8600100":
				newBankCode="中信银行";//1024
				break;
			case "9999":
				newBankCode="未指定银行";//9999
				break;
			default :
				newBankCode="未指定银行";
			}
			reUwBO.setBankCode(newBankCode);
			reUwBO.setDebitBank(newBankCode);
		}
	}
	/**
	 * BigDecimal转换
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param bigdecimal  字符
	 * @return
	 */
	public BigDecimal transToScale(BigDecimal bigdecimal){
		BigDecimal bigDecimal=BigDecimal.ZERO;
		if (bigdecimal.compareTo(BigDecimal.ZERO)==1) {
			bigDecimal=bigdecimal.setScale(2);
		}
		return bigDecimal;		
	}
	/**
	 * 日期转换
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param strDate 日期
	 * @return
	 * @throws ParseException
	 */
	public String transToDate(Date strDate) {
		String strTime ="";
		if (strDate!=null) {
			SimpleDateFormat format0 = new SimpleDateFormat("yyyyMMdd");
			strTime = format0.format(strDate.getTime());//这个就是把时间戳经过处理得到期望格式的时间	       
		}
		return strTime;

	}
	/**
	 * 险种信息转换
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param reUwPolicyRiskPOs 险种信息
	 */
	public void paymodeTransType(List<CoveragePolicyRiskPO> reUwPolicyRiskPOs){
		if (reUwPolicyRiskPOs!=null && reUwPolicyRiskPOs.size()>0) {
			for (CoveragePolicyRiskPO reUwPolicyRiskPO:reUwPolicyRiskPOs) {
				//				String paymode = reUwPolicyRiskPO.getPayMode();
				//				String newpaymode="";
				//				if ("1".equals(paymode)) {//趸交
				//					newpaymode="01";
				//				} else if ("2".equals(paymode)){//月交
				//					newpaymode="02";
				//				}else if ("3".equals(paymode)){//季交
				//					newpaymode="03";
				//				}else if ("4".equals(paymode)){//半年缴
				//					newpaymode="04";
				//				}else if ("5".equals(paymode)){//年缴
				//					newpaymode="05";
				//				}else if ("9".equals(paymode)){//其他
				//					newpaymode="09";
				//				}
				//				reUwPolicyRiskPO.setPayMode(newpaymode);
				//钱保留2位
				if (reUwPolicyRiskPO.getFeeAmount()!=null&&(reUwPolicyRiskPO.getFeeAmount().compareTo(BigDecimal.ZERO)==1)) {//险种保费
					reUwPolicyRiskPO.setFeeAmount(transToScale(reUwPolicyRiskPO.getFeeAmount()));
				}
				if (reUwPolicyRiskPO.getRiskPremium()!=null&&(reUwPolicyRiskPO.getRiskPremium().compareTo(BigDecimal.ZERO)==1)) {//险种期初保费
					reUwPolicyRiskPO.setRiskPremium(transToScale(reUwPolicyRiskPO.getRiskPremium()));
				}
				if (reUwPolicyRiskPO.getRiskCurrentPremium()!=null&&(reUwPolicyRiskPO.getRiskCurrentPremium().compareTo(BigDecimal.ZERO)==1)) {//险种当前保费
					reUwPolicyRiskPO.setRiskCurrentPremium(transToScale(reUwPolicyRiskPO.getRiskCurrentPremium()));
				}
				if (reUwPolicyRiskPO.getRiskAmount()!=null&&(reUwPolicyRiskPO.getRiskAmount().compareTo(BigDecimal.ZERO)==1)) {//险种保额
					reUwPolicyRiskPO.setRiskAmount(transToScale(reUwPolicyRiskPO.getRiskAmount()));
				}
				if (reUwPolicyRiskPO.getRiskEffectiveAmount()!=null&&(reUwPolicyRiskPO.getRiskEffectiveAmount().compareTo(BigDecimal.ZERO)==1)) {//险种有效保额
					reUwPolicyRiskPO.setRiskEffectiveAmount(transToScale(reUwPolicyRiskPO.getRiskEffectiveAmount()));
				}
				if (reUwPolicyRiskPO.getRiskDayAmount()!=null && (reUwPolicyRiskPO.getRiskDayAmount().compareTo(BigDecimal.ZERO)==1)) {//险种期初日额保险金
					reUwPolicyRiskPO.setRiskDayAmount(transToScale(reUwPolicyRiskPO.getRiskDayAmount()));
				}
				if (reUwPolicyRiskPO.getRiskDayEffectiveAmount()!=null&&(reUwPolicyRiskPO.getRiskDayEffectiveAmount().compareTo(BigDecimal.ZERO)==1)) {//险种有效日额保险金
					reUwPolicyRiskPO.setRiskDayEffectiveAmount(transToScale(reUwPolicyRiskPO.getRiskDayEffectiveAmount()));
				}
				if (reUwPolicyRiskPO.getRiskYearPremium()!=null&&(reUwPolicyRiskPO.getRiskYearPremium().compareTo(BigDecimal.ZERO)==1)) {//险种当前年化保费
					reUwPolicyRiskPO.setRiskYearPremium(transToScale(reUwPolicyRiskPO.getRiskYearPremium()));
				}

			}

		}
	}
	/**
	 * 日期转换
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param strDate 日期
	 * @return
	 * @throws ParseException
	 */
	public String transToDateHMS(Date strDate) {
		String strTime ="";
		if (strDate!=null) {
			SimpleDateFormat format0 = new SimpleDateFormat("yyyyMMddHHmmss");
			strTime = format0.format(strDate.getTime());//这个就是把时间戳经过处理得到期望格式的时间	       
		}
		return strTime;

	}
	public String PostXQcoverageXML(PacketCoverageBO pcBO, SHReneworSurrenderBO shreneworsurrenderBO){
		String policySequenceNo = null;
		if(null == pcBO){
			return null;  
		} else {
			//上海医保承保信息同步上传增加挡板 add by yangzd1_wb
			ParaDefBO paraDefBO = (ParaDefBO) ParaDefInitConst.getGlobalConst().get("mode");
			if(paraDefBO != null && paraDefBO.getParaValue() != null && "PE".equals(paraDefBO.getParaValue())){
				//默认成功处理
				policySequenceNo = "POL" + DateUtilsEx.longDateTime();//接口返回定义时间戳
				NbContractMedicalBO nbContractMedicalBO = new NbContractMedicalBO();
				String policyFormerNo = pcBO.getBody().getPolicyInfo().getPolicyFormerNo();
				nbContractMedicalBO.setPolicyFormerNo(policyFormerNo);
				nbContractMedicalBO.setPolicySequenceNo(policySequenceNo);
				ShybXQCoverageInfoDao.updatePySequenceNo(nbContractMedicalBO);
				logger.info("保单保存上海医保续期承保保单编码成功");
				// 调用api推送上海医保保单编码到收付费--start
				shreneworsurrenderBO.setArapflag("1");//更新
				shreneworsurrenderBO.setPolicySequenceNo(policySequenceNo);
				List<SHReneworSurrenderBO> returnBO = querySHYBCapInfo(shreneworsurrenderBO);
				if (returnBO.get(0).isFlag()) {
					logger.info("收付费保存上海医保续期承保保单编码成功");
				}
				// 调用api推送上海医保保单编码到收付费--end	
			}
			//上海医保承保信息同步上传增加挡板 add by yangzd1_wb
			
			Map<String, String> proMap = PropertiesUtil
					.getMapFromPropertiesPath(HealthCareXmlUtil.NB_SH_PATH);
			String xmlUrl = (String) proMap.get(HealthCareXmlUtil.NB_SH_URL);

			String xmlInfo = HealthCareXmlUtil.toXml(pcBO);
			logger.error("上海医保续期承保信息上传接口请求报文：" + xmlInfo);
			// 发送请求报文
			String returnXml = HealthCareXmlUtil.xmlPost(xmlUrl, xmlInfo);
			logger.error("上海医保续期承保信息上传接口返回报文："+returnXml);
			// DOM4J解析返回报文，获取核保确认编码字段
			Document document;
			try {
				document = DocumentHelper.parseText(returnXml);
				// 获取根节点
				Element root = document.getRootElement();
				// 获取head节点
				Element element = root.element("HEAD");
				// 获取返回类型代码文本
				String responseCode = element.elementText("RESPONSE_CODE");			
				if ("1".equals(responseCode)) {// 输入数据正确，返回正常值
					// 获取保单编码
					Element body = root.element("BODY");
					Element successInfo = body.element("SUCCESS_INFO");
					policySequenceNo = successInfo.elementText("POLICY_SEQUENCE_NO");
					NbContractMedicalBO nbContractMedicalBO = new NbContractMedicalBO();
					String policyFormerNo = pcBO.getBody().getPolicyInfo()
							.getPolicyFormerNo();
					nbContractMedicalBO.setPolicyFormerNo(policyFormerNo);
					nbContractMedicalBO.setPolicySequenceNo(policySequenceNo);
					ShybXQCoverageInfoDao.updatePySequenceNo(nbContractMedicalBO);
					logger.info("保单保存上海医保续期承保保单编码成功");
					// 调用api推送上海医保保单编码到收付费--start
					shreneworsurrenderBO.setArapflag("1");//更新
					shreneworsurrenderBO.setPolicySequenceNo(policySequenceNo);
					List<SHReneworSurrenderBO> returnBO = querySHYBCapInfo(shreneworsurrenderBO);
					if (returnBO.get(0).isFlag()) {
						logger.info("收付费保存上海医保续期承保保单编码成功");
					}
					// 调用api推送上海医保保单编码到收付费--end		
				} else if ("0".equals(responseCode)) {
					logger.info("输入数据有误，无法返回正常值");
				} else if ("2".equals(responseCode)) {
					logger.info("输入数据部分成功");
				} else if ("3".equals(responseCode)) {
					logger.info("输入数据对象已存在");
					// 获取保单编码
					Element body = root.element("BODY");
					Element successInfo = body.element("SUCCESS_INFO");
					policySequenceNo = successInfo.elementText("POLICY_SEQUENCE_NO");
					NbContractMedicalBO nbContractMedicalBO = new NbContractMedicalBO();
					String policyFormerNo = pcBO.getBody().getPolicyInfo()
							.getPolicyFormerNo();
					nbContractMedicalBO.setPolicyFormerNo(policyFormerNo);
					nbContractMedicalBO.setPolicySequenceNo(policySequenceNo);
					ShybXQCoverageInfoDao.updatePySequenceNo(nbContractMedicalBO);
					logger.info("保单保存上海医保续期承保保单编码成功");
					// 调用api推送上海医保保单编码到收付费--start
					shreneworsurrenderBO.setArapflag("1");//更新
					shreneworsurrenderBO.setPolicySequenceNo(policySequenceNo);
					List<SHReneworSurrenderBO> returnBO = querySHYBCapInfo(shreneworsurrenderBO);
					if (returnBO.get(0).isFlag()) {
						logger.info("收付费保存上海医保续期承保保单编码成功");
					}
					// 调用api推送上海医保保单编码到收付费--end		
				} else if ("6".equals(responseCode)) {
					logger.info("报文数据超过/低于限制");
				} else if ("E".equals(responseCode)) {
					logger.info("系统未知错误");
				}
			} catch (DocumentException e) {
				logger.info("上海医保续期更新保单编码字段失败");
				e.printStackTrace();
			}
			
			return policySequenceNo;
		} 
		
	}
	public ICPAService getCapServiceSaveArap() {
		return capServiceSaveArap;
	}
	public void setCapServiceSaveArap(ICPAService capServiceSaveArap) {
		this.capServiceSaveArap = capServiceSaveArap;
	}

	public IShybXQCoverageInfoDao getShybXQCoverageInfoDao() {
		return ShybXQCoverageInfoDao;
	}

	public void setShybXQCoverageInfoDao(
			IShybXQCoverageInfoDao shybXQCoverageInfoDao) {
		ShybXQCoverageInfoDao = shybXQCoverageInfoDao;
	}

	

	

	

	
}
