package com.nci.tunan.cs.batch.autoCSCTEffect.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.nci.tunan.cs.batch.autoCSCTEffect.service.IAutoCSCTEffectService;
import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.impl.csEffect.ucc.ICsEffectUCC;
import com.nci.tunan.cs.interfaces.exports.vo.csEffect.CsEffectReqData;
import com.nci.tunan.cs.interfaces.exports.vo.csEffect.CsEffectResData;



/**
 * 
 * @description 预退保生效批处理
 * @<NAME_EMAIL> 
 * @date 2017-3-16 下午5:21:54 
 * @.belongToModule 保全系统-预退保生效批处理
 */
public class AutoCSCTEffectServiceImpl implements IAutoCSCTEffectService {
	/**
	 * 日志文件
	 */
	private static Logger logger = LoggerFactory.getLogger(Constants.CS_BATCH_LOG_NAME);
	/**
	 * 生效UCC
	 */
	private ICsEffectUCC csEffectUCC;

	/**
	 * 
	 * @description 工作流接口
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @see com.nci.tunan.cs.batch.autoCSCTEffect.service.IAutoCSCTEffectService#startJob(java.lang.String)
	 * @param acceptCode 受理号
	 * @return
	 * @throws Exception 
	 */
	@Override
	/** @invalid 54754 关于新核心系统生成唯一收付费号码的需求  start**/
	public boolean startJob(String acceptCode) throws Exception {
		/** @invalid 54754 关于新核心系统生成唯一收付费号码的需求  end**/
		CsEffectReqData effectInput = new CsEffectReqData();
		// 1.获取预退保生效批处理,执行条件
		effectInput.setAcceptCode(acceptCode);
		effectInput.setFlag("1");
		// 2.执行预退保生效批处理
		CsEffectResData csEffectResData = csEffectUCC.saveCsEffect(effectInput);
		String ResultCode = csEffectResData.getResultCode();
		if (!"0".equals(ResultCode)){
			logger.info(csEffectResData.getResultMsg());
			return false;
		}
		
		return true;
	}
	public ICsEffectUCC getCsEffectUCC() {
		return csEffectUCC;
	}
	public void setCsEffectUCC(ICsEffectUCC csEffectUCC) {
		this.csEffectUCC = csEffectUCC;
	}
	
}
