package com.nci.tunan.cs.batch.trialCancalJob.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.nci.core.common.factory.BOServiceFactory;
import com.nci.core.common.interfaces.vo.LockResultVO;
import com.nci.core.common.interfaces.vo.LockServiceSetVO;
import com.nci.tunan.cs.batch.trialCancalJob.bo.TrialCancalBO;
import com.nci.tunan.cs.batch.trialCancalJob.dao.ITrialCancalDao;
import com.nci.tunan.cs.batch.trialCancalJob.po.TrialCancalPO;
import com.nci.tunan.cs.batch.trialCancalJob.service.ITrialCancalService;
import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.dao.ICsAcceptChangeDao;
import com.nci.tunan.cs.dao.ICsApplicationDao;
import com.nci.tunan.cs.dao.ICsPolicyChangeDao;
import com.nci.tunan.cs.impl.csBusinessLock.service.ICsBusinessLockService;
import com.nci.tunan.cs.model.po.CsAcceptChangePO;
import com.nci.tunan.cs.model.po.CsApplicationPO;
import com.nci.tunan.cs.model.po.CsPolicyChangePO;
import com.nci.udmp.component.aop.TablePrevThreadLocal;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.CollectionUtilEx;

/**
 * 
 * @description  试算解挂的service 实现类 
 * <AUTHOR> <EMAIL> 
 * @date 2015年05月15日 下午3:31:31 
 * @.belongToModule 保全子系统
 */
public class TriakCancalServiceImpl implements ITrialCancalService {

	/**
     * @Fields logger : 日志工具
     */
    private static Logger logger = LoggerFactory.getLogger(com.nci.tunan.cs.common.Constants.CS_LOG_NAME_UDMPINT);

    /**
     * 试算解挂的dao
     */
	private ITrialCancalDao trialCancalDao;// @invalid 试算解挂的dao
	/**
	 * 受理管理表dao
	 */
	private ICsAcceptChangeDao csAcceptChangeDao;//@invalid 受理管理表dao
	/**
	 * 业务锁
	 */
	private ICsBusinessLockService csBusinessLockService;
	/**
	 * 保单变更表DAO
	 */
	@Autowired
	private ICsPolicyChangeDao csPolicyChangeDao;
	/**
	 * 保全申请表信息DAO
	 */
	@Autowired
	private ICsApplicationDao csApplicationDao;

	public ICsBusinessLockService getCsBusinessLockService() {
		return csBusinessLockService;
	}
	
	public void setCsBusinessLockService(
			ICsBusinessLockService csBusinessLockService) {
		this.csBusinessLockService = csBusinessLockService;
	}
	
	public ICsAcceptChangeDao getCsAcceptChangeDao() {
		return csAcceptChangeDao;
	}

	public void setCsAcceptChangeDao(ICsAcceptChangeDao csAcceptChangeDao) {
		this.csAcceptChangeDao = csAcceptChangeDao;
	}

	public ITrialCancalDao getTrialCancalDao() {
		return trialCancalDao;
	}

	public void setTrialCancalDao(ITrialCancalDao trialCancalDao) {
		this.trialCancalDao = trialCancalDao;
	}
	
	public ICsPolicyChangeDao getCsPolicyChangeDao() {
		return csPolicyChangeDao;
	}

	public void setCsPolicyChangeDao(ICsPolicyChangeDao csPolicyChangeDao) {
		this.csPolicyChangeDao = csPolicyChangeDao;
	}


	/**
	 * 
	 * @description 试算解挂 -查询符合条件的最大最小acceptid
	 * @version 1.0
	 * @title 试算解挂 -查询符合条件的最大最小acceptid
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.batch.trialCancalJob.service.ITrialCancalService#queryTrialCancalConuts(com.nci.tunan.cs.batch.trialCancalJob.bo.TrialCancalBO)
	 * @param trialCancalBO 查询条件 
	 * @return 返回试算解挂 -查询符合条件的最大最小acceptid
	 * @throws Exception 异常信息
	 */
	@Override
	public int queryTrialCancalConuts(TrialCancalBO trialCancalBO) throws Exception {
		TrialCancalPO trialCancalPO = BeanUtils.copyProperties(TrialCancalPO.class, trialCancalBO);
		int sum = trialCancalDao.queryTrialCancalConuts(trialCancalPO);
		return sum;
	}

	/**
	 * 
	 * @description 试算解挂-按启动参数查询符合条件的保单
	 * @version 1.0
	 * @title 试算解挂-按启动参数查询符合条件的保单
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.batch.trialCancalJob.service.ITrialCancalService#queryTrialCancalpolicy(com.nci.tunan.cs.batch.trialCancalJob.bo.TrialCancalBO)
	 * @param trialCancalBO 查询条件
	 * @return 返回试算解挂-按启动参数查询符合条件的保单 
	 * @throws Exception 异常
	 */

	@Override
	public List<TrialCancalBO> queryTrialCancalpolicy(TrialCancalBO trialCancalBO) throws Exception {
		TrialCancalPO trialCancalPO = BeanUtils.copyProperties(TrialCancalPO.class, trialCancalBO);
		List<TrialCancalPO> list = trialCancalDao.queryTrialCancalpolicy(trialCancalPO);

		return BeanUtils.copyList(TrialCancalBO.class, list);
	}

	/**
	 * 
	 * @description 试算解挂的条件
	 * @version 1.0
	 * @title 试算解挂的条件
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.batch.trialCancalJob.service.ITrialCancalService#isCanBeRun(com.nci.tunan.cs.batch.trialCancalJob.bo.TrialCancalBO)
	 * @param trialCancalBO 查询条件
	 * @return 是否可以解挂
	 * @throws Exception 异常
	 */
	@Override
	public Boolean isCanBeRun(TrialCancalBO trialCancalBO) throws Exception {

		// @invalid TODO 保单处于保全挂起状态。调用接口

		return true;
	}

	/**
	 * 
	 * @description 开始解挂
	 * @version 1.0
	 * @title 开始解挂
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.cs.batch.trialCancalJob.service.ITrialCancalService#startJob(com.nci.tunan.cs.batch.trialCancalJob.bo.TrialCancalBO)
	 * @param trialCancalBO 条件
	 * @throws Exception 异常
	 */
	@Override
	public void startJob(TrialCancalBO trialCancalBO) throws Exception {
		BigDecimal acceptId = trialCancalBO.getAcceptId();
		CsAcceptChangePO csAcceptChangePO = new CsAcceptChangePO();
		csAcceptChangePO.setAcceptId(acceptId);
		//@invalid 已经通过id查出数据，不需要在获取这个状态
		//@invalid /* csAcceptChangePO.setAcceptStatus(Constants.ACCEPT_STATUS_TRIAL); */
		csAcceptChangePO = csAcceptChangeDao.findCsAcceptChangeByAcceptId(csAcceptChangePO);
		//@invalid TrialCancalPO trialCancalPOs = new TrialCancalPO();

		csAcceptChangePO.setAcceptStatus(Constants.ACCEPT_STATUS_CANCEL);
		csAcceptChangePO = trialCancalDao.updateTrialCancal(csAcceptChangePO);
		/** 业务锁解锁 --开始 */
		//@invalid csBusinessLockService.releaseLockByAcceptId(acceptId);
		
		logger.info("试算解挂业务锁解锁开始， acceptId=" + acceptId);
		
		logger.info("试算解挂,申请表业务状态维护开始， ChangeId=" + csAcceptChangePO.getChangeId());
		CsApplicationPO csApplicationPO = new CsApplicationPO();
		csApplicationPO.setChangeId(csAcceptChangePO.getChangeId());
		csApplicationPO = csApplicationDao.findCsApplication(csApplicationPO);
		csApplicationPO.setAppStatus(Constants.APP_STATUS__CANCEL);		
		csApplicationDao.updateApplyStatusByChangeId(csApplicationPO);
		logger.info("试算解挂,申请表业务状态维护结束， ChangeId=" + csAcceptChangePO.getChangeId());						
		
		 //@invalid 按照业务号进行解锁  查找保单变更信息
        CsPolicyChangePO csPolicyChangePO = new CsPolicyChangePO();
        csPolicyChangePO.setAcceptId(acceptId);
        List<CsPolicyChangePO> csPolicyChangePOList = csPolicyChangeDao.findAllCsPolicyChange(csPolicyChangePO);

        List<LockServiceSetVO> setList = new ArrayList<LockServiceSetVO>();
        if (CollectionUtilEx.isNotEmpty(csPolicyChangePOList) && csPolicyChangePOList.size() > Constants.ZERO_INT) {
        	
        	BigDecimal changeId = csPolicyChangePOList.get(Constants.ZERO_INT).getChangeId();
        	
        	CsApplicationPO csApplication = new CsApplicationPO();
        	csApplication.setChangeId(changeId);
        	
        	CsApplicationPO csapplicationResult = csApplicationDao.findCsApplication(csApplication);
        	
        	if (csapplicationResult != null && csapplicationResult.getTryCalcNo() != null
        			&& !"".equals(csapplicationResult.getTryCalcNo())) {
        		String tryCalcNo = csapplicationResult.getTryCalcNo();
        		BigDecimal customerId = csapplicationResult.getCustomerId();
        		
        		if (tryCalcNo != null && !"".equals(tryCalcNo) && customerId != null) {
        			for (CsPolicyChangePO csPolicyChange : csPolicyChangePOList) {
                		logger.info("试算解挂业务锁解锁开始， PolicyCode=" + csPolicyChange.getPolicyCode());
                        LockServiceSetVO setVO = new LockServiceSetVO();
                        setVO.setPolicyId(csPolicyChange.getPolicyId());
                        setVO.setPolicyCode(csPolicyChange.getPolicyCode());
                        setVO.setBusinessCode(tryCalcNo);
                        setVO.setServiceCode(csPolicyChange.getServiceCode());
                        setVO.setLockProcessCode("3002");
                        setList.add(setVO);
                    }
        		}
        	}
        	
        }
        
        logger.info("试算解挂业务锁解锁调用接口开始， 请求参数：" + DataSerialJSon.fromObject(setList));
        
        if (CollectionUtilEx.isNotEmpty(setList) && setList.size() > Constants.ZERO_INT) {
        	TablePrevThreadLocal.setTABLEPREV("APP___PAS__DBUSER.");
            LockResultVO res = BOServiceFactory.setLockOneForAPI().setLockList(setList);
            logger.info("试算解挂业务锁解锁调用接口结束， 返回参数" + DataSerialJSon.fromObject(res));
        }
		        
		/** 业务锁解锁--结束 */
		//@invalid  try {
		//@invalid  trialCancalPOs = trialCancalDao.findstatusdesc(trialCancalPOs);
		//@invalid  } catch (Exception e) {
		//@invalid  e.printStackTrace();
		//@invalid  throw new BatchBizException("更新试算解挂状态为取消");
		//@invalid  }
		//@invalid  CsAcceptChangePO csAcceptChange = new CsAcceptChangePO();
		//@invalid  csAcceptChange.setAcceptId(acceptId);
		//@invalid  csAcceptChange.setAcceptStatus(trialCancalPOs.getAcceptStatus());

	}

}
