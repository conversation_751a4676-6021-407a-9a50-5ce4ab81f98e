package com.nci.tunan.cs.batch.loanIntersest;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.nci.tunan.cs.batch.loanIntersest.bo.CsPolicyAccountStreamBO;
import com.nci.tunan.cs.batch.loanIntersest.service.IloanIntersestService;
import com.nci.tunan.pa.interfaces.model.bo.ContractMasterBO;
import com.nci.tunan.pa.util.BatchDateUtil;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.exception.BatchBizException;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.util.lang.DateUtilsEx;
/**
 * 
 * @description 保单贷款利息批处理 
 * <AUTHOR> <EMAIL> 
 * @date 2020-9-2 下午9:44:51 
 * @.belongToModule CS-保全子系统
 */
public class LoanIntersestJob extends AbstractBatchJobForMod{

	public IloanIntersestService getIloanIntersestService() {
		return iloanIntersestService;
	}

	public void setIloanIntersestService(IloanIntersestService iloanIntersestService) {
		this.iloanIntersestService = iloanIntersestService;
	}
	
	/**
	 * 保单贷款利息批处理 
	 */
	private IloanIntersestService iloanIntersestService;
	
	/**
	 * 
	 * @description 回写
	 * @version 1.0
	 * @title 回写
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#write(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
	 * @param jobSessionContext 上下文
	 * @param jobData 回写数据
	 */
	@Override
	public void write(JobSessionContext jobSessionContext, JobData jobData) {
		
		
	}

	/**
	 * 
	 * @description 返回IdName
	 * @version 1.0
	 * @title 返回IdName
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#getIdName()
	 * @return 返回IdName
	 */
	@Override
	public String getIdName() {
		
		return null;
	}

	/**
	 * 
	 * @description 前置条件
	 * @version 1.0
	 * @title 前置条件
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#isCanBeRun()
	 * @return 返回前置条件
	 */
	@Override
	public boolean isCanBeRun() {
		
		return true;
	}

	/**
	 * 
	 * @description 总条数
	 * @version 1.0
	 * @title 总条数
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#queryCounts(com.nci.udmp.component.batch.context.JobSessionContext)
	 * @param jobSessionContext 上下文
	 * @return 返回总条数
	 */
	@Override
	public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {
		//CsPolicyAccountBO csPolicyAccountBO = new CsPolicyAccountBO();
		CsPolicyAccountStreamBO csPolicyAccountStreamBO = new CsPolicyAccountStreamBO();
		 List<JobParam> jobParams = jobSessionContext.getParams();
		/* 查询所有符合保单贷款未还款的数据条目 */
		try {
		    String policyCode = "";// 保单号
            Date batchDate = null;// 批处理运行时间
            for (JobParam jobParam : jobParams) {
               if (jobParam.getParamName().trim().equals("policyCode") && jobParam.getParamType().trim().equalsIgnoreCase("string")) {
                    // 保单号
                    policyCode = jobParam.getParamValue();
                    //根据保单号得到保单id
                    ContractMasterBO masterBO = new ContractMasterBO();
                    masterBO.setPolicyCode(policyCode);
                    masterBO= iloanIntersestService.findMaster(masterBO);
                    csPolicyAccountStreamBO.setPolicyId(masterBO.getPolicyId());
                } else if (jobParam.getParamName().equals("batchDate") && jobParam.getParamType().equalsIgnoreCase("Date")) {
                    // 批处理运行时间
                    batchDate = DateUtilsEx.formatToDate(jobParam.getParamValue(), "yyyy-MM-dd");
                    if (batchDate == null) {
                        batchDate = DateUtilsEx.formatDate(BatchDateUtil.getCurrentDate(), "yyyy-MM-dd");
                    }
                    csPolicyAccountStreamBO.setBatchDate(batchDate);
                }
            }
               csPolicyAccountStreamBO= iloanIntersestService.findPolicyAccountStreamBO(csPolicyAccountStreamBO);
 			long startNum = 0;
	        long endNum = 0;
			
			/* 设置起始位置和终止位置并返回 */
	        if(null != csPolicyAccountStreamBO && null!= csPolicyAccountStreamBO.getEndnum() && csPolicyAccountStreamBO.getEndnum().longValue() > 0){
	            jobSessionContext.setStartNum(1);
	            jobSessionContext.setEndNum(csPolicyAccountStreamBO.getEndnum().longValue());
	        }else{
	            jobSessionContext.setStartNum(0);
	            jobSessionContext.setEndNum(0);
	        }
		
            } catch (BatchBizException e) {
			throw new BatchBizException("保单贷款利息计算-查询行数异常!");
		}
		return jobSessionContext;
	}

	/**
	 * 
	 * @description 查询待执行数据明细
	 * @version 1.0
	 * @title 查询待执行数据明细
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#query(com.nci.udmp.component.batch.context.JobSessionContext, long, int)
	 * @param jobSessionContext 上下文
	 * @param start 开始
	 * @param counts MDO数
	 * @return 返回查询待执行数据明细
	 */
	@Override
	public List<JobData> query(JobSessionContext jobSessionContext, long start,
			int counts) {
		/* 查询批处理数据信息集合 */
		CsPolicyAccountStreamBO csPolicyAccountStreamBO=new CsPolicyAccountStreamBO();
		List<JobParam> jpList = jobSessionContext.getParams();
		Date batchDate = null;
		if(jpList.size()<1){
            throw new BatchBizException("缺少运行参数");
        }else{
            String policyCode = "";
            for(JobParam jp : jpList){
                if (jp.getParamName().equals("policyCode")&&jp.getParamType().equals("string")) {
                    policyCode = jp.getParamValue();
                    ContractMasterBO masterBO = new ContractMasterBO();
                    masterBO.setPolicyCode(policyCode);
                    masterBO= iloanIntersestService.findMaster(masterBO);
                    csPolicyAccountStreamBO.setPolicyId(masterBO.getPolicyId());
                }
                if(jp.getParamName().equals("batchDate")&&jp.getParamType().equals("date")){
                    try {
                        batchDate = new SimpleDateFormat("yyyy-MM-dd").parse(jp.getParamValue());
                        csPolicyAccountStreamBO.setBatchDate(batchDate);
                    } catch (ParseException e) {
                        throw new BatchBizException("运行参数-应收日期格式错误，yyyy-MM-dd");
                    }
                    
                }
            }
            
        }
		//返回结果
        List<JobData> jobDataList = new ArrayList<JobData>();
        csPolicyAccountStreamBO.setCounts(BigDecimal.valueOf((counts+start)));
        csPolicyAccountStreamBO.setStart(BigDecimal.valueOf(start));
        List<CsPolicyAccountStreamBO> csPolicyAccountStreamBOList = iloanIntersestService.findLoanIntersestJob(csPolicyAccountStreamBO);
		
		if (csPolicyAccountStreamBOList.size() > 0) {
			for (CsPolicyAccountStreamBO arpl : csPolicyAccountStreamBOList) {
				System.out.println("查询集合数据---- ："+arpl.getPolicyId());
				//将查出的数据拼成map放入集合
				Map<String, Object> mapValue = new HashMap<String, Object>();
				arpl.setBatchDate(batchDate);
				mapValue.put("CsPolicyAccountStreamBO", arpl);
				JobData jd = new JobData();
				jd.setData(mapValue);
				jobDataList.add(jd);
			}
		}
		return jobDataList;
	}

	/**
	 * 
	 * @description 停止批处理
	 * @version 1.0
	 * @title 停止批处理
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobStop()
	 */
	@Override
	public void jobStop() {
		
	}

	/**
	 * 
	 * @description 执行明细数据
	 * @version 1.0
	 * @title 执行明细数据
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#execute(com.nci.udmp.component.batch.context.JobSessionContext, java.util.List, int)
	 * @param arg0 上下文
	 * @param arg1 明细数据
	 * @param arg2 MOD数
	 * @return 返回执行结果
	 */
    @Override
    public List<JobData> execute(JobSessionContext arg0, List<JobData> arg1, int arg2) {
        try {

            for (JobData jobData : arg1) {
                CsPolicyAccountStreamBO csPolicyAccountStreamBO = (CsPolicyAccountStreamBO) jobData.getData().get(
                        "CsPolicyAccountStreamBO");
                iloanIntersestService.startJob(csPolicyAccountStreamBO);
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new BatchBizException("贷款/自垫预终止-业务处理异常!");
        }
        return arg1;
    }

    /**
     * 
     * @description 异常处理
     * @version 1.0
     * @title 异常处理
     * <AUTHOR> <EMAIL>
     * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobErrorAudit(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
     * @param arg0 上下文
     * @param arg1 异常信息
     */
    @Override
    public void jobErrorAudit(JobSessionContext arg0, JobData arg1) {
        
    }

}
