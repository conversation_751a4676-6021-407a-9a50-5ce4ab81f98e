package com.nci.tunan.cs.batch.premArapFeedBack;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import com.nci.tunan.cs.impl.csEffect.ucc.ICsEffectUCC;
import com.nci.tunan.cs.interfaces.exports.vo.csEffect.CsEffectReqData;
import com.nci.tunan.cs.interfaces.exports.vo.csEffect.CsEffectResData;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.nci.tunan.cs.batch.premArapFeedBack.bo.CsEffectJobBO;
import com.nci.tunan.cs.batch.premArapFeedBack.service.IPremArapFeedBackService;
import com.nci.tunan.cs.batch.premArapFeedBack.ucc.IPremArapFeedBackUCC;
import com.nci.tunan.cs.common.Constants;
import com.nci.udmp.component.batch.context.JobSessionContext;
import com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod;
import com.nci.udmp.component.batch.core.agent.job.JobData;
import com.nci.udmp.component.batch.exception.BatchBizException;
import com.nci.udmp.component.batch.model.JobParam;
import com.nci.udmp.framework.context.AppUser;
import com.nci.udmp.framework.context.AppUserContext;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.exception.support.ExceptionCode;
import com.nci.udmp.framework.util.BatchConstants;

import org.springframework.beans.factory.annotation.Autowired;

/**
 * 
 * @description 收付费结果信息回写批处理
 * <AUTHOR> <EMAIL> 
 * @date 2020-9-2 下午5:01:51 
 * @.belongToModule CS-保全子系统
 */
public class PremArapFeedBackJob extends AbstractBatchJobForMod {
	/**
	 * 日志
	 */
	private static Logger logger = LoggerFactory.getLogger(Constants.CS_BATCH_LOG_NAME);
	/**
	 * 收付费业务类
	 */
	private IPremArapFeedBackService premArapFeedBackService;
    /**
     * 收付费事物层
     */
    private IPremArapFeedBackUCC premArapFeedBackUCC;
	/**
	 * 保全生效类
	 */
	@Autowired
    private ICsEffectUCC csEffectUCC;
	
    /**
	 * 批处理监控优化  记录当前线程要处理的总数 add by suhui_wb
	 */
	private int totalCnt = 0;
	/**
	 * 批处理监控优化  记录当前线程已处理过的数  add by suhui_wb
	 */
	private int executedCnt = 0;
	/**
	 * 批处理监控优化 收集异常信息 add by suhui_wb
	 */
	private StringBuffer errorMsg = new StringBuffer();
	/**
	 * 批处理监控优化  收集异常保单 add by suhui_wb
	 */
	private List<Object> errorList = new ArrayList<Object>();
	
	public IPremArapFeedBackUCC getPremArapFeedBackUCC() {
		return premArapFeedBackUCC;
	}

	public void setPremArapFeedBackUCC(IPremArapFeedBackUCC premArapFeedBackUCC) {
		this.premArapFeedBackUCC = premArapFeedBackUCC;
	}

	public IPremArapFeedBackService getPremArapFeedBackService() {
		return premArapFeedBackService;
	}

	public void setPremArapFeedBackService(
			IPremArapFeedBackService premArapFeedBackService) {
		this.premArapFeedBackService = premArapFeedBackService;
	}
	/**
	 * 
	 * @description 停批处理
	 * @version 1.0
	 * @title 停批处理
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobStop()
	 */
	@Override
	public void jobStop() {

	}
	/**
	 * 
	 * @description 批处理回写
	 * @version 1.0
	 * @title 批处理回写
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#write(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
	 * @param arg0 上下文
	 * @param arg1 回写数据
	 */
	public void write(JobSessionContext arg0, JobData arg1) {
		
	}
	/**
	 * 
	 * @description 获取IDName
	 * @version 1.0
	 * @title 获取IDName
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#getIdName()
	 * @return 获取IDName
	 */
	@Override
	public String getIdName() {
		return "csTaskId";
	}
	/**
	 * 
	 * @description 返回前置条件是否满足
	 * @version 1.0
	 * @title 返回前置条件是否满足
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#isCanBeRun()
	 * @return 返回前置条件是否满足
	 */
	@Override
	public boolean isCanBeRun() {
		return true;
	}
	/**
	 * 
	 * @description 查询批处理明细
	 * @version 1.0
	 * @title 查询批处理明细
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#query(com.nci.udmp.component.batch.context.JobSessionContext, long, int)
	 * @param jobSessionContext 上下文
	 * @param start 开始
	 * @param modNum MOD数
	 * @return 返回查询批处理明细
	 */
	@Override
	public List<JobData> query(JobSessionContext jobSessionContext, long start, int modNum) {
	    logger.info("===========保全生效批处理  query  start================");		
		logger.info("任务查询，start:" + start +", modNum" + modNum);
		
		CsEffectJobBO csEffectJobBO = new CsEffectJobBO();
		csEffectJobBO.setModNum(new BigDecimal(modNum));
		csEffectJobBO.setStart(new BigDecimal(start));
		List<JobParam> paramlist = jobSessionContext.getParams();
		if(CollectionUtils.isNotEmpty(paramlist)){
			for (JobParam jobParam : paramlist) {
				if (jobParam.getParamName().equals("acceptCode")&& jobParam.getParamType().equals("string")) {
					String acceptCode = jobParam.getParamValue();
					csEffectJobBO.setAcceptCode(acceptCode);
				}
			}
		}
		
		List<JobData> resultList = new ArrayList<JobData>();//@invalid 本次处理的任务
		List<CsEffectJobBO> csEffectJobList = premArapFeedBackService.queryCsAcceptChangeBO( csEffectJobBO);
		if(CollectionUtils.isNotEmpty(csEffectJobList)){
		    
		    for(CsEffectJobBO feCsEffectJobBO : csEffectJobList){
		        Map<String,Object> data= new HashMap<String,Object>();
		        JobData jobData = new JobData();
		        data.put("acceptCode", feCsEffectJobBO.getAcceptCode());
		        jobData.setData(data);
		        resultList.add(jobData);
		    }
		}
		//批处理监控优化 add by suhui_wb
		totalCnt += resultList.size();
		jobSessionContext.setTotalCnt(csEffectJobList.size()-resultList.size());
		logger.info("===========保全生效批处理  query  end================");
		return resultList;
	}
	/**
	 * 
	 * @description 执行总条数据
	 * @version 1.0
	 * @title 查询批处理明细
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#queryCounts(com.nci.udmp.component.batch.context.JobSessionContext)
	 * @param jobSessionContext 上下文
	 * @return 返回上下文
	 */
	@Override
	public JobSessionContext queryCounts(JobSessionContext jobSessionContext) {		
		jobSessionContext.setModNum(3);
		return jobSessionContext;
	}
	/**
	 * 
	 * @description 批处理明细数据处理
	 * @version 1.0
	 * @title 批处理明细数据处理
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJobForMod#execute(com.nci.udmp.component.batch.context.JobSessionContext, java.util.List, int)
	 * @param jobSessionContext 上下文
	 * @param jobData 明细数据
	 * @param modNum MOD数
	 * @return 返回处理情况
	 */
	@Override
	public List<JobData> execute(JobSessionContext jobSessionContext, List<JobData> jobData,int modNum) {
		logger.info("===========保全生效批处理  execute  end================");
		List<JobData> returnSuccessDatas = new ArrayList<JobData>();
		//@invalid  获取机构参数
		List<JobParam> params = jobSessionContext.getParams();
		logger.info("机构参数：" + params);
		//@invalid 设置批处理用户，否则调外部接口设置报文头时报错
		AppUser appUser = new AppUser();
		appUser.setUserId(20980);//@invalid  设置为指定的批处理用户
		AppUserContext.setCurrentUser(appUser);
		CsEffectReqData inputVO = null;
		//@invalid 调保单接口查询收付费结果待处理任务
		for(JobData feJobData : jobData){
			if(feJobData.get("acceptCode") != null){
				inputVO = new CsEffectReqData();
				String acceptCode = (String) feJobData.get("acceptCode");

				inputVO.setAcceptCode(acceptCode);
				inputVO.setFlag("1");
				/**54754 关于新核心系统生成唯一收付费号码的需求  start**/
				try {
					CsEffectResData resultData = csEffectUCC.saveCsEffect(inputVO);
					if("0".equals(resultData.getResultCode())){
						returnSuccessDatas.add(feJobData);
					}
				} catch (Exception e) {
					if(jobSessionContext.getIsAllowSingleRollback().equals(BatchConstants.JOB_SINGLE_ROLLBACK_YES)){
						if(!errorList.contains(acceptCode)){
							errorList.add(acceptCode);
							errorMsg.append("\r\n保全受理号"+acceptCode+"执行"+e.getMessage()+"处理逻辑异常;");
							executedCnt++;
						}
					}else{
						errorMsg.append("\r\n保全受理号"+acceptCode+"执行"+e.getMessage()+"处理逻辑异常;");
						executedCnt += jobData.size();
					}
		        	if(executedCnt >= totalCnt){
						jobSessionContext.addExceptionList(new BatchBizException(ExceptionCode.UDMP_BATCH_LOG_0000, errorMsg.toString(), "2"));
						errorMsg.setLength(0);
						executedCnt = 0;
					}
					throw new BizException("生效报错"+inputVO.getAcceptCode());
				}
				/**54754 关于新核心系统生成唯一收付费号码的需求  start**/
			}
		}
		executedCnt += jobData.size();
	  	if(executedCnt >= totalCnt && errorMsg.toString() != null && !"".equals(errorMsg.toString())){
	  		jobSessionContext.addExceptionList(new BatchBizException(ExceptionCode.UDMP_BATCH_LOG_0000, errorMsg.toString(), "2"));
	  		errorMsg.setLength(0);
	  		executedCnt = 0;
	  	}
		logger.info("===========保全生效批处理  execute  end================");
		return returnSuccessDatas;
	}
	/**
	 * 
	 * @description 异常处理
	 * @version 1.0
	 * @title 异常处理
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.udmp.component.batch.core.agent.job.AbstractBatchJob#jobErrorAudit(com.nci.udmp.component.batch.context.JobSessionContext, com.nci.udmp.component.batch.core.agent.job.JobData)
	 * @param arg0 上下文
	 * @param arg1 异常数据
	 */
	@Override
	public void jobErrorAudit(JobSessionContext arg0, JobData arg1) {
		
		
	}
}
