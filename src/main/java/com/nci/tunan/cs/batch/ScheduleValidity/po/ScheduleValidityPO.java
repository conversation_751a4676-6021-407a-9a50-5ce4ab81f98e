package com.nci.tunan.cs.batch.ScheduleValidity.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;
/**
 * 
 * @description 
 * <AUTHOR> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2018年3月24日 下午2:10:40
 */
public class ScheduleValidityPO extends BasePO {
	
	/** 
	* @Fields serialVersionUID : @invalid TODO(用一句话描述这个变量表示什么) 
	*/ 
	private static final long serialVersionUID = 1L;

	/** 属性 --- java类型 --- oracle类型_数据长度_小数位长度_注释信息 */
	// precPolicyChgId --- BigDecimal --- NUMBER_16_0_null;
	// precontStatus --- String --- VARCHAR2_1_0_null;
	// newUnit --- BigDecimal --- NUMBER_18_2_null;
	// newAccountId --- BigDecimal --- NUMBER_16_0_null;
	// newPrem --- BigDecimal --- NUMBER_18_2_null;
	// newPlanId --- String --- CHAR_2_0_null;
	// newAmount --- BigDecimal --- NUMBER_18_2_null;
	// triggerModule --- String --- VARCHAR2_5_0_null;
	// oldAmount --- BigDecimal --- NUMBER_18_2_null;
	// itemId --- BigDecimal --- NUMBER_16_0_null;
	// oldChargeMode --- BigDecimal --- NUMBER_1_0_null;
	// oldPlanId --- String --- CHAR_2_0_null;
	// oldAccountId --- BigDecimal --- NUMBER_16_0_null;
	// precontSaInc --- BigDecimal --- NUMBER_18_2_null;
	// processTime --- Date --- DATE_7_0_null;
	// precontTime --- Date --- DATE_7_0_null;
	// oldPrem --- BigDecimal --- NUMBER_18_2_null;
	// policyId --- BigDecimal --- NUMBER_16_0_null;
	// newPayMode --- String --- CHAR_2_0_null;
	// oldPayMode --- String --- CHAR_2_0_null;
	// oldStandAmount --- BigDecimal --- NUMBER_18_2_null;
	// newStandAmount --- BigDecimal --- NUMBER_18_2_null;
	// newCountWay --- String --- CHAR_1_0_null;
	// oldUnit --- BigDecimal --- NUMBER_18_2_null;
	// newChargeMode --- BigDecimal --- NUMBER_1_0_null;
	// endCause --- String --- CHAR_2_0_null;
	// precontId --- BigDecimal --- NUMBER_16_0_null;
	// oldBenefitLevel --- String --- VARCHAR2_20_0_null;
	// newBenefitLevel --- String --- VARCHAR2_20_0_null;
	// oldCountWay --- String --- CHAR_1_0_null;
	// busiItemId --- BigDecimal --- NUMBER_16_0_险种ID;
	
	
	public BigDecimal getStartnum() {
        return this.getBigDecimal("startnum");
    }

    public void setStartnum(BigDecimal startnum) {
        setBigDecimal("startnum", startnum);
    }
    
    public BigDecimal getEndnum() {
        return this.getBigDecimal("endnum");
    }
    
    public void setEndnum(BigDecimal endnum) {
        setBigDecimal("endnum", endnum);
    }
    
    public BigDecimal getStart() {
        return this.getBigDecimal("start");
    }

    public void setStart(BigDecimal start) {
        setBigDecimal("start", start);
    }

    public BigDecimal getCounts() {
        return this.getBigDecimal("counts");
    }

    public void setCounts(BigDecimal counts) {
        setBigDecimal("counts", counts);
    }

	public void setPrecPolicyChgId(BigDecimal precPolicyChgId) {
		setBigDecimal("prec_policy_chg_id", precPolicyChgId);
	}

	public BigDecimal getPrecPolicyChgId() {
		return getBigDecimal("prec_policy_chg_id");
	}

	public void setPrecontStatus(String precontStatus) {
		setString("precont_status", precontStatus);
	}

	public String getPrecontStatus() {
		return getString("precont_status");
	}

	public void setNewUnit(BigDecimal newUnit) {
		setBigDecimal("new_unit", newUnit);
	}

	public BigDecimal getNewUnit() {
		return getBigDecimal("new_unit");
	}

	public void setNewAccountId(BigDecimal newAccountId) {
		setBigDecimal("new_account_id", newAccountId);
	}

	public BigDecimal getNewAccountId() {
		return getBigDecimal("new_account_id");
	}

	public void setNewPrem(BigDecimal newPrem) {
		setBigDecimal("new_prem", newPrem);
	}

	public BigDecimal getNewPrem() {
		return getBigDecimal("new_prem");
	}

	public void setNewPlanId(String newPlanId) {
		setString("new_plan_id", newPlanId);
	}

	public String getNewPlanId() {
		return getString("new_plan_id");
	}

	public void setNewAmount(BigDecimal newAmount) {
		setBigDecimal("new_amount", newAmount);
	}

	public BigDecimal getNewAmount() {
		return getBigDecimal("new_amount");
	}

	public void setTriggerModule(String triggerModule) {
		setString("trigger_module", triggerModule);
	}

	public String getTriggerModule() {
		return getString("trigger_module");
	}

	public void setOldAmount(BigDecimal oldAmount) {
		setBigDecimal("old_amount", oldAmount);
	}

	public BigDecimal getOldAmount() {
		return getBigDecimal("old_amount");
	}

	public void setItemId(BigDecimal itemId) {
		setBigDecimal("item_id", itemId);
	}

	public BigDecimal getItemId() {
		return getBigDecimal("item_id");
	}

	public void setOldChargeMode(BigDecimal oldChargeMode) {
		setBigDecimal("old_charge_mode", oldChargeMode);
	}

	public BigDecimal getOldChargeMode() {
		return getBigDecimal("old_charge_mode");
	}

	public void setOldPlanId(String oldPlanId) {
		setString("old_plan_id", oldPlanId);
	}

	public String getOldPlanId() {
		return getString("old_plan_id");
	}

	public void setOldAccountId(BigDecimal oldAccountId) {
		setBigDecimal("old_account_id", oldAccountId);
	}

	public BigDecimal getOldAccountId() {
		return getBigDecimal("old_account_id");
	}

	public void setPrecontSaInc(BigDecimal precontSaInc) {
		setBigDecimal("precont_sa_inc", precontSaInc);
	}

	public BigDecimal getPrecontSaInc() {
		return getBigDecimal("precont_sa_inc");
	}

	public void setProcessTime(Date processTime) {
		setUtilDate("process_time", processTime);
	}

	public Date getProcessTime() {
		return getUtilDate("process_time");
	}

	public void setPrecontTime(Date precontTime) {
		setUtilDate("precont_time", precontTime);
	}

	public Date getPrecontTime() {
		return getUtilDate("precont_time");
	}

	public void setOldPrem(BigDecimal oldPrem) {
		setBigDecimal("old_prem", oldPrem);
	}

	public BigDecimal getOldPrem() {
		return getBigDecimal("old_prem");
	}

	public void setPolicyId(BigDecimal policyId) {
		setBigDecimal("policy_id", policyId);
	}

	public BigDecimal getPolicyId() {
		return getBigDecimal("policy_id");
	}

	public void setNewPayMode(String newPayMode) {
		setString("new_pay_mode", newPayMode);
	}

	public String getNewPayMode() {
		return getString("new_pay_mode");
	}

	public void setOldPayMode(String oldPayMode) {
		setString("old_pay_mode", oldPayMode);
	}

	public String getOldPayMode() {
		return getString("old_pay_mode");
	}

	public void setOldStandAmount(BigDecimal oldStandAmount) {
		setBigDecimal("old_stand_amount", oldStandAmount);
	}

	public BigDecimal getOldStandAmount() {
		return getBigDecimal("old_stand_amount");
	}

	public void setNewStandAmount(BigDecimal newStandAmount) {
		setBigDecimal("new_stand_amount", newStandAmount);
	}

	public BigDecimal getNewStandAmount() {
		return getBigDecimal("new_stand_amount");
	}

	public void setNewCountWay(String newCountWay) {
		setString("new_count_way", newCountWay);
	}

	public String getNewCountWay() {
		return getString("new_count_way");
	}

	public void setOldUnit(BigDecimal oldUnit) {
		setBigDecimal("old_unit", oldUnit);
	}

	public BigDecimal getOldUnit() {
		return getBigDecimal("old_unit");
	}

	public void setNewChargeMode(BigDecimal newChargeMode) {
		setBigDecimal("new_charge_mode", newChargeMode);
	}

	public BigDecimal getNewChargeMode() {
		return getBigDecimal("new_charge_mode");
	}

	public void setEndCause(String endCause) {
		setString("end_cause", endCause);
	}

	public String getEndCause() {
		return getString("end_cause");
	}

	public void setPrecontId(BigDecimal precontId) {
		setBigDecimal("precont_id", precontId);
	}

	public BigDecimal getPrecontId() {
		return getBigDecimal("precont_id");
	}

	public void setOldBenefitLevel(String oldBenefitLevel) {
		setString("old_benefit_level", oldBenefitLevel);
	}

	public String getOldBenefitLevel() {
		return getString("old_benefit_level");
	}

	public void setNewBenefitLevel(String newBenefitLevel) {
		setString("new_benefit_level", newBenefitLevel);
	}

	public String getNewBenefitLevel() {
		return getString("new_benefit_level");
	}

	public void setOldCountWay(String oldCountWay) {
		setString("old_count_way", oldCountWay);
	}

	public String getOldCountWay() {
		return getString("old_count_way");
	}

	public BigDecimal getStartNum() {
		return getBigDecimal("start_num");
	}

	public void setStartNum(BigDecimal startNum) {
		setBigDecimal("start_num", startNum);
	}

	public BigDecimal getEndNum() {
		return getBigDecimal("end_num");
	}

	public void setEndNum(BigDecimal endNum) {
		setBigDecimal("end_num", endNum);
	}

	public void setBusiItemId(BigDecimal busiItemId) {
		setBigDecimal("busi_item_id", busiItemId);
	}

	public BigDecimal getBusiItemId() {
		return getBigDecimal("busi_item_id");
	}

	@Override
	public String toString() {
		return "ScheduleValidityPO [" + "precPolicyChgId=" + getPrecPolicyChgId() + "," + "precontStatus="
				+ getPrecontStatus() + "," + "newUnit=" + getNewUnit() + "," + "newAccountId=" + getNewAccountId()
				+ "," + "newPrem=" + getNewPrem() + "," + "newPlanId=" + getNewPlanId() + "," + "newAmount="
				+ getNewAmount() + "," + "triggerModule=" + getTriggerModule() + "," + "oldAmount=" + getOldAmount()
				+ "," + "itemId=" + getItemId() + "," + "oldChargeMode=" + getOldChargeMode() + "," + "oldPlanId="
				+ getOldPlanId() + "," + "oldAccountId=" + getOldAccountId() + "," + "precontSaInc="
				+ getPrecontSaInc() + "," + "processTime=" + getProcessTime() + "," + "precontTime=" + getPrecontTime()
				+ "," + "oldPrem=" + getOldPrem() + "," + "policyId=" + getPolicyId() + "," + "newPayMode="
				+ getNewPayMode() + "," + "oldPayMode=" + getOldPayMode() + "," + "oldStandAmount="
				+ getOldStandAmount() + "," + "newStandAmount=" + getNewStandAmount() + "," + "newCountWay="
				+ getNewCountWay() + "," + "oldUnit=" + getOldUnit() + "," + "newChargeMode=" + getNewChargeMode()
				+ "," + "endCause=" + getEndCause() + "," + "precontId=" + getPrecontId() + "," + "oldBenefitLevel="
				+ getOldBenefitLevel() + "," + "newBenefitLevel=" + getNewBenefitLevel() + "," + "oldCountWay="
				+ getOldCountWay() + "," + "getStartNum()=" + getStartNum() + "," + "getEndNum()=" + getEndNum() + ","
				+ "getBusiItemId()=" + getBusiItemId() + "]";
	}
}
