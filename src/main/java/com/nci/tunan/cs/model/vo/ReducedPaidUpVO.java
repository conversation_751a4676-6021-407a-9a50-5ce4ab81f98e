package com.nci.tunan.cs.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/***
 * @description  减额交清/险种转换VO
 * <AUTHOR> <EMAIL> 
 * @date 2017-11-23
 * @.belongToModule 保全系统
 */
public class ReducedPaidUpVO extends BaseVO{
	/**serialVersionUID*/
	private static final long serialVersionUID = -537695238819474905L;

	/**
	 * policyCode as 保单号, productId as 险种序号,productItem as 险种代码, productName as 险种名称
	 * subProduct as 附属险种,productCost as 保额,productFee as 保费, prodStatus as 险种状态,
	 * premiumStatus as　保费状态，validateDay　as 生效日期, nextPayDay 下次交费日期，anniversary as 周年日
	 * payMod as 交费方式, payTerm as 交费期限, newPayMod as 新交费方式, newPayTerm as 新交费期限
	 */
	// 客户信息
	/**客户姓名*/
	private String cusName;// 客户姓名
	/**性别*/
	private Integer cusSex;// 性别
	/**生日*/
	private String birthDay;// 生日
	/** 证件类型*/
	private Integer ceriType;// 证件类型
	/** 证件号码*/
	private String ceriCode;// 证件号码
	/** 保单号*/
	private String policyCode; // 保单号
	/** 险种代码*/
	private BigDecimal productItem; // 险种代码
	/** 险种名称*/
	private String productName; // 险种名称
	/** 保额*/
	private BigDecimal productCost; // 保额
	/** 每份保额*/
	private BigDecimal perSA; // 每份保额
	/** 保费*/
	private BigDecimal productFee; // 保费
	/** 下次交费日期*/
	private Date nextPayDay; // 下次交费日期
	/** 险种状态*/
	private Integer prodStatus; // 险种状态
	/** 现价*/
	private BigDecimal currentPrice; // 现价
	/** 终了红利*/
	private BigDecimal finalBonus; // 终了红利
	
	//险种转换信息
	/** 保障年期类型*/
	private String gurntPerdType ; // 保障年期类型
	/** 保障年期*/
	private Integer guartPeriod ; // 保障年期
	/** 约定年金领取年龄*/
	private Integer receiveAge ; // 约定年金领取年龄
	/** 年金领取方式*/
	private String receiveMode ; // 年金领取方式


	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public BigDecimal getProductItem() {
		return productItem;
	}

	public void setProductItem(BigDecimal productItem) {
		this.productItem = productItem;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public BigDecimal getProductCost() {
		return productCost;
	}

	public void setProductCost(BigDecimal productCost) {
		this.productCost = productCost;
	}

	public BigDecimal getPerSA() {
		return perSA;
	}

	public void setPerSA(BigDecimal perSA) {
		this.perSA = perSA;
	}

	public BigDecimal getProductFee() {
		return productFee;
	}

	public void setProductFee(BigDecimal productFee) {
		this.productFee = productFee;
	}


	public Date getNextPayDay() {
		return nextPayDay;
	}

	public void setNextPayDay(Date nextPayDay) {
		this.nextPayDay = nextPayDay;
	}

	public Integer getProdStatus() {
		return prodStatus;
	}

	public void setProdStatus(Integer prodStatus) {
		this.prodStatus = prodStatus;
	}

	public BigDecimal getCurrentPrice() {
		return currentPrice;
	}

	public void setCurrentPrice(BigDecimal currentPrice) {
		this.currentPrice = currentPrice;
	}

	public BigDecimal getFinalBonus() {
		return finalBonus;
	}

	public void setFinalBonus(BigDecimal finalBonus) {
		this.finalBonus = finalBonus;
	}

	public String getGurntPerdType() {
		return gurntPerdType;
	}

	public void setGurntPerdType(String gurntPerdType) {
		this.gurntPerdType = gurntPerdType;
	}

	public Integer getGuartPeriod() {
		return guartPeriod;
	}

	public void setGuartPeriod(Integer guartPeriod) {
		this.guartPeriod = guartPeriod;
	}

	public Integer getReceiveAge() {
		return receiveAge;
	}

	public void setReceiveAge(Integer receiveAge) {
		this.receiveAge = receiveAge;
	}

	public String getReceiveMode() {
		return receiveMode;
	}

	public void setReceiveMode(String receiveMode) {
		this.receiveMode = receiveMode;
	}

	public String getCusName() {
		return cusName;
	}

	public void setCusName(String cusName) {
		this.cusName = cusName;
	}

	public Integer getCusSex() {
		return cusSex;
	}

	public void setCusSex(Integer cusSex) {
		this.cusSex = cusSex;
	}

	public String getBirthDay() {
		return birthDay;
	}

	public void setBirthDay(String birthDay) {
		this.birthDay = birthDay;
	}

	public Integer getCeriType() {
		return ceriType;
	}

	public void setCeriType(Integer ceriType) {
		this.ceriType = ceriType;
	}

	public String getCeriCode() {
		return ceriCode;
	}

	public void setCeriCode(String ceriCode) {
		this.ceriCode = ceriCode;
	}

	@Override
	public String getBizId() {
		// @invalid TODO Auto-generated method stub
		return null;
	}

}