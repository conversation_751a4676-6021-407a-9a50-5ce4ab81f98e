package com.nci.tunan.cs.model.vo;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/***
 *
 * @description IlpSurrenderVO对象
 * <AUTHOR> <EMAIL> 
 * @date 2017-03-16
 * @.belongToModule 保全系统
 */
public class IlpSurrenderVO extends BaseVO {	
			 /** 
	* @Fields itemId :  险种责任组ID
 	*/ 
	private BigDecimal itemId;
	 /** 
	* @Fields agentHolderRelation :  投保人与业务员关系
 	*/ 
	private String agentHolderRelation;
	 /** 
	* @Fields acceptCode :  保全受理号
 	*/ 
	private String acceptCode;
			 /** 
	* @Fields changeId :  保全变更ID
 	*/ 
	private BigDecimal changeId;
	 /** 
	* @Fields listId :  记录ID
 	*/ 
	private BigDecimal listId;
		 /** 
	* @Fields policyChgId :  保单变更ID
 	*/ 
	private BigDecimal policyChgId;
	 /** 
	* @Fields busiItemId :  险种ID
 	*/ 
	private BigDecimal busiItemId;
		 /** 
	* @Fields ilpSurrenderCause :  投连退保原因
 	*/ 
	private String ilpSurrenderCause;
	 /** 
	* @Fields delayCause :  延迟原因
 	*/ 
	private String delayCause;
	/**
	 * @Fields hesitateFlag : 判断是否在犹豫期
	 */
	private BigDecimal hesitateFlag;
		
			 public void setItemId(BigDecimal itemId) {
		this.itemId = itemId;
	}
	
	public BigDecimal getItemId() {
		return itemId;
	}
	 public void setAgentHolderRelation(String agentHolderRelation) {
		this.agentHolderRelation = agentHolderRelation;
	}
	
	public String getAgentHolderRelation() {
		return agentHolderRelation;
	}
	 public void setAcceptCode(String acceptCode) {
		this.acceptCode = acceptCode;
	}
	
	public String getAcceptCode() {
		return acceptCode;
	}
			 public void setChangeId(BigDecimal changeId) {
		this.changeId = changeId;
	}
	
	public BigDecimal getChangeId() {
		return changeId;
	}
	 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
		 public void setPolicyChgId(BigDecimal policyChgId) {
		this.policyChgId = policyChgId;
	}
	
	public BigDecimal getPolicyChgId() {
		return policyChgId;
	}
	 public void setBusiItemId(BigDecimal busiItemId) {
		this.busiItemId = busiItemId;
	}
	
	public BigDecimal getBusiItemId() {
		return busiItemId;
	}
		 public void setIlpSurrenderCause(String ilpSurrenderCause) {
		this.ilpSurrenderCause = ilpSurrenderCause;
	}
	
	public String getIlpSurrenderCause() {
		return ilpSurrenderCause;
	}
	 public void setDelayCause(String delayCause) {
		this.delayCause = delayCause;
	}
	
	public String getDelayCause() {
		return delayCause;
	}
		
	public BigDecimal getHesitateFlag() {
		return hesitateFlag;
	}

	public void setHesitateFlag(BigDecimal hesitateFlag) {
		this.hesitateFlag = hesitateFlag;
	}

	@Override
    public String getBizId() {
        return null;
    }
    
    @Override
    public String toString() {
        return "IlpSurrenderVO [" +
				"itemId="+itemId+","+
"agentHolderRelation="+agentHolderRelation+","+
"acceptCode="+acceptCode+","+
"changeId="+changeId+","+
"listId="+listId+","+
"policyChgId="+policyChgId+","+
"busiItemId="+busiItemId+","+
"ilpSurrenderCause="+ilpSurrenderCause+","+
"delayCause="+delayCause+"]";
    }
}
