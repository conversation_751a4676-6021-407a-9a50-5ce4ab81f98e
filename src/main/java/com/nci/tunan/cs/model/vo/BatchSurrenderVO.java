package com.nci.tunan.cs.model.vo;

import com.nci.udmp.framework.model.*;

import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @description 批量退保
 * <AUTHOR> <EMAIL>
 * @date  2015年11月11日
 * @.belongToModule 保全子系统
 */
public class BatchSurrenderVO extends BaseVO {
	/** 
	* @Fields serialVersionUID : @invalid TODO(用一句话描述这个变量表示什么) 
	*/ 
	
	private static final long serialVersionUID = 1L;
	/**
	 * 事件ID
	 */
	private BigDecimal eventId;
    /**
     * 事件号码
     */
    private String eventCode;
    /**
     * 事件日期
     */
    private Date eventTime;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 事件描述
     */
    private String eventDesc;
    /**
     * 是否已实付
     */
    private String realPayFlag;
    /**
     * 申请方式
     */
    private String serviceType;
    /**
     * 业务员代码
     */
    private BigDecimal agentId;
    /**
     * 新业务员代码
     */
    private BigDecimal agentIdNew;
    /**
     * 业务员代码
     */
    private String agentCode;
    /**
     * 新业务员代码
     */
    private String agentCodeNew;
    /**
     * 绩优等级
     */
    private String agentLevel;
    /**
     * 代办人姓名
     */
    private String agentName;
    /**
     * 代办人证件类型
     */
    private String agentCeritType;
    /**
     * 代办人证件号码
     */
    private String agentCeritCode;
    /**
     * 代办人联系电话
     */
    private String agentTel;
    /**
     * 序号
     */
    private String sequenceNumber;
    /**
     * 保单号
     */
    private String policyCode;
    /**
     * 投保人
     */
    private String holderName;
    /**
     * 保单终止日期
     */
    private Date expiryDate;
    /**
     * 保单终止原因
     */
    private String endCause;
    /**
     * 保单生效日
     */
    private Date validateDate;
    /**
     * 保全申请提交日期
     */
    private Date applyTime;
    /**
     * 退保生效日期
     */
    private Date validateDateSurrender;
    /**
     * 付费标识
     */
    private String realPayAmountFlag;
    /**
     * 退费金额
     */
    private BigDecimal payAmount;
    /**
     * 已经支付金额
     */
    private BigDecimal realPayAmount;
    /**
     * 生存金是否额外付给
     */
    private String survivalExtraFlag;
    /**
     * 退费方式
     */
    private String payMode;
    /**
     * 开户银行
     */
    private String bankCode;
    /**
     * 开户银行名称
     */
    private String bankName;
    /**
     * 银行账号
     */
    private String bankAccount;
    /**
     * 银行账号名
     */
    private String bankAccountName;
    /**
     * 应付金额
     */
    private BigDecimal actualReturnPremium;
    /**
     * 客户号
     */
    private BigDecimal customerId;
    /**
     * 险种号（所属业务产品ID）
     */
    private BigDecimal busiPrdId;
    /**
     * 责任组Id 所属的精算产品ID
     */
    private BigDecimal productId;
    /**
     * 差额
     */
    private BigDecimal reapayAmount;
    /**
     * 保全受理id
     */
    private BigDecimal changeId;
    
	/**
	 * 投保人
	 */
	private String policyHolderName;
	/**
	 * 保单生效日期
	 */
	private Date policyValidateDate;
	/**
	 * 保全申请提交日期
	 */
	private Date surApplyDate;
	/**
	 * 退保生效日期
	 */
	private Date surEffectDate;
	
	
	
	 public String getPolicyHolderName() {
		return policyHolderName;
	}

	public void setPolicyHolderName(String policyHolderName) {
		this.policyHolderName = policyHolderName;
	}

	public Date getPolicyValidateDate() {
		return policyValidateDate;
	}

	public void setPolicyValidateDate(Date policyValidateDate) {
		this.policyValidateDate = policyValidateDate;
	}

	public Date getSurApplyDate() {
		return surApplyDate;
	}

	public void setSurApplyDate(Date surApplyDate) {
		this.surApplyDate = surApplyDate;
	}

	public Date getSurEffectDate() {
		return surEffectDate;
	}

	public void setSurEffectDate(Date surEffectDate) {
		this.surEffectDate = surEffectDate;
	}

	public String getRealPayAmountFlag() {
		return realPayAmountFlag;
	}

	public void setRealPayAmountFlag(String realPayAmountFlag) {
		this.realPayAmountFlag = realPayAmountFlag;
	}

	public BigDecimal getChangeId() {
		return changeId;
	}

	public void setChangeId(BigDecimal changeId) {
		this.changeId = changeId;
	}

	public BigDecimal getEventId() {
		return eventId;
	}

	public void setEventId(BigDecimal eventId) {
		this.eventId = eventId;
	}

	public BigDecimal getReapayAmount() {
		return reapayAmount;
	}

	public void setReapayAmount(BigDecimal reapayAmount) {
		this.reapayAmount = reapayAmount;
	}

	public BigDecimal getActualReturnPremium() {
		return actualReturnPremium;
	}

	public void setActualReturnPremium(BigDecimal actualReturnPremium) {
		this.actualReturnPremium = actualReturnPremium;
	}

	public BigDecimal getProductId() {
		return productId;
	}

	public void setProductId(BigDecimal productId) {
		this.productId = productId;
	}

	public BigDecimal getBusiPrdId() {
		return busiPrdId;
	}

	public void setBusiPrdId(BigDecimal busiPrdId) {
		this.busiPrdId = busiPrdId;
	}

	public String getEndCause() {
		return endCause;
	}

	public void setEndCause(String endCause) {
		this.endCause = endCause;
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public BigDecimal getCustomerId() {
		return customerId;
	}

	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}

	public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public Date getEventTime() {
        return eventTime;
    }

    public void setEventTime(Date eventTime) {
        this.eventTime = eventTime;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventDesc() {
        return eventDesc;
    }

    public void setEventDesc(String eventDesc) {
        this.eventDesc = eventDesc;
    }

    public String getRealPayFlag() {
        return realPayFlag;
    }

    public void setRealPayFlag(String realPayFlag) {
        this.realPayFlag = realPayFlag;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public BigDecimal getAgentId() {
        return agentId;
    }

    public void setAgentId(BigDecimal agentId) {
        this.agentId = agentId;
    }

    public BigDecimal getAgentIdNew() {
        return agentIdNew;
    }

    public void setAgentIdNew(BigDecimal agentIdNew) {
        this.agentIdNew = agentIdNew;
    }


    public String getAgentLevel() {
		return agentLevel;
	}

	public void setAgentLevel(String agentLevel) {
		this.agentLevel = agentLevel;
	}

	public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentCeritType() {
        return agentCeritType;
    }

    public void setAgentCeritType(String agentCeritType) {
        this.agentCeritType = agentCeritType;
    }

    public String getAgentCeritCode() {
        return agentCeritCode;
    }

    public void setAgentCeritCode(String agentCeritCode) {
        this.agentCeritCode = agentCeritCode;
    }

    public String getAgentTel() {
        return agentTel;
    }

    public void setAgentTel(String agentTel) {
        this.agentTel = agentTel;
    }

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public Date getValidateDate() {
        return validateDate;
    }

    public void setValidateDate(Date validateDate) {
        this.validateDate = validateDate;
    }

    public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public Date getValidateDateSurrender() {
        return validateDateSurrender;
    }

    public void setValidateDateSurrender(Date validateDateSurrender) {
        this.validateDateSurrender = validateDateSurrender;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public BigDecimal getRealPayAmount() {
        return realPayAmount;
    }

    public void setRealPayAmount(BigDecimal realPayAmount) {
        this.realPayAmount = realPayAmount;
    }

  

    public String getSurvivalExtraFlag() {
		return survivalExtraFlag;
	}

	public void setSurvivalExtraFlag(String survivalExtraFlag) {
		this.survivalExtraFlag = survivalExtraFlag;
	}

	public String getPayMode() {
        return payMode;
    }

    public void setPayMode(String payMode) {
        this.payMode = payMode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getSequenceNumber() {
        return sequenceNumber;
    }

    public void setSequenceNumber(String sequenceNumber) {
        this.sequenceNumber = sequenceNumber;
    }

    public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getAgentCodeNew() {
		return agentCodeNew;
	}

	public void setAgentCodeNew(String agentCodeNew) {
		this.agentCodeNew = agentCodeNew;
	}
    
	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	/**
   	 * 
   	 * @description 无
   	 * @version
   	 * @title
   	 * <AUTHOR> <EMAIL>
   	 * @see com.nci.udmp.framework.model.BaseVO#getBizId()
   	 * @return
   	 */	
	@Override
    public String getBizId() {
        // @invalid TODO Auto-generated method stub
        return null;
    }
    
}
