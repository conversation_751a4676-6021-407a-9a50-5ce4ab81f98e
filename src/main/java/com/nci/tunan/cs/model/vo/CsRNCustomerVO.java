package com.nci.tunan.cs.model.vo;

import java.math.BigDecimal;
import java.util.Date;
/**
 * 
 * @description 重新出单客户信息
 * <AUTHOR> 
 * @date 2015-05-15	
 * @.belongToModule 保全子系统
 */
public class CsRNCustomerVO{

	/** 
	 * @Fields policyCode :  保单号
	 */ 
	private String policyCode;
	/** 
	 * @Fields policyRole :  保单角色
	 */ 
	private String policyRole;
	/** 
	 * @Fields customerName :  客户姓名
	 */ 
	private String customerName;
	/** 
	 * @Fields customerCertType :  客户证件类型
	 */ 
	private String customerCertType;
	/** 
	 * @Fields customerCertiCode :  客户证件号码
	 */ 
	private String customerCertiCode;
	/** 
	 * @Fields customerGender :  客户性别，关联到性别字典表
	 */ 
	private BigDecimal customerGender;
	
	/** 
	 * @Fields customerGender :  客户性别 汉字
	 */ 
	private String csGender;
	/** 
	 * @Fields customerBirthday :  客户出生日期
	 */ 
	private Date customerBirthday;
	/** 
	 * @Fields relationToPh :  与投保人关系
	 */ 
	private String relationToPh;

    /**
     * 定义 customerid 客户id
     */
    private BigDecimal customerId;

	/** 
	 * @Fields acceptId :  受理号
	 */ 
	private String acceptId;
    
	public String getAcceptId() {
		return acceptId;
	}
	public void setAcceptId(String acceptId) {
		this.acceptId = acceptId;
	}

	/** 
	 * @Fields customerSeaId :  客户号查询
	 */ 
	private String customerSeaId;
    
	public String getCustomerSeaId() {
		return customerSeaId;
	}
	public void setCustomerSeaId(String customerSeaId) {
		this.customerSeaId = customerSeaId;
	}
	public BigDecimal getCustomerId() {
		return customerId;
	}
	public void setCustomerId(BigDecimal customerId) {
		this.customerId = customerId;
	}
	public String getPolicyCode() {
		return policyCode;
	}
	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	public String getPolicyRole() {
		return policyRole;
	}
	public void setPolicyRole(String policyRole) {
		this.policyRole = policyRole;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getCustomerCertType() {
		return customerCertType;
	}
	public void setCustomerCertType(String customerCertType) {
		this.customerCertType = customerCertType;
	}
	public String getCustomerCertiCode() {
		return customerCertiCode;
	}
	public void setCustomerCertiCode(String customerCertiCode) {
		this.customerCertiCode = customerCertiCode;
	}
	public BigDecimal getCustomerGender() {
		return customerGender;
	}
	public void setCustomerGender(BigDecimal customerGender) {
		this.customerGender = customerGender;
	}
	public String getCsGender() {
		return csGender;
	}
	public void setCsGender(String csGender) {
		this.csGender = csGender;
	}
	public Date getCustomerBirthday() {
		return customerBirthday;
	}
	public void setCustomerBirthday(Date customerBirthday) {
		this.customerBirthday = customerBirthday;
	}
	public String getRelationToPh() {
		return relationToPh;
	}
	public void setRelationToPh(String relationToPh) {
		this.relationToPh = relationToPh;
	}

}
