package com.nci.tunan.cs.model.vo;

import java.math.BigDecimal;
import java.util.List;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description CsUwInfoQueryVO 核保信息查询VO
 * <AUTHOR> <EMAIL> 
 * @.belongToModule 保全系统-核保信息查询VO
 * @date 2015-05-15
 */
public class UwInfoQueryVO extends BaseVO {
	/** 
	* serialVersionUID
	*/ 
	
	private static final long serialVersionUID = 1L;
	/**受理号*/
	private String acceptCode;
    /** 保单号*/
    private String policyCode;
    /** 险种*/
    private BigDecimal busiProdId;
    /**险种名称*/
    private String productName;
    /** 保额*/
    private BigDecimal amount;
    /** 保费*/
    private BigDecimal totalPremAf;
    /** 受理层级核保决定*/
    private BigDecimal uwDecision;
    /** 保单层级核保决定*/
    private String policyDecision;
    /** 险种层级核保决定*/
    private String busiDecision;
    /** 责任组层级核保决定*/
    private String prodDecision;
    /** 加费信息*/
    private String extraPremInfo;
    /** 特约信息*/
    private String conditionInfo;
    /** 限额信息*/
    private String limitPremInfo;
    /**查询加费信息*/
    private List<UwExtraPremVO> uwExtraPremVOs;
    /**查询限额信息*/
    private	List<UwLimitVO> uwLimitVOs;
    /**查询特约信息*/
    private List<UwConditionVO> uwConditionVOs;
   

    public String getPolicyCode() {
        return policyCode;
    }

    public void setPolicyCode(String policyCode) {
        this.policyCode = policyCode;
    }

    public BigDecimal getBusiProdId() {
        return busiProdId;
    }

    public void setBusiProdId(BigDecimal busiProdId) {
        this.busiProdId = busiProdId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getTotalPremAf() {
        return totalPremAf;
    }

    public void setTotalPremAf(BigDecimal totalPremAf) {
        this.totalPremAf = totalPremAf;
    }

    public BigDecimal getUwDecision() {
        return uwDecision;
    }

    public void setUwDecision(BigDecimal uwDecision) {
        this.uwDecision = uwDecision;
    }

    public String getPolicyDecision() {
        return policyDecision;
    }

    public void setPolicyDecision(String policyDecision) {
        this.policyDecision = policyDecision;
    }

    public String getBusiDecision() {
        return busiDecision;
    }

    public void setBusiDecision(String busiDecision) {
        this.busiDecision = busiDecision;
    }

    public String getProdDecision() {
        return prodDecision;
    }

    public void setProdDecision(String prodDecision) {
        this.prodDecision = prodDecision;
    }

    public String getExtraPremInfo() {
        return extraPremInfo;
    }

    public void setExtraPremInfo(String extraPremInfo) {
        this.extraPremInfo = extraPremInfo;
    }

    public String getConditionInfo() {
        return conditionInfo;
    }

    public void setConditionInfo(String conditionInfo) {
        this.conditionInfo = conditionInfo;
    }

    public String getLimitPremInfo() {
        return limitPremInfo;
    }

    public void setLimitPremInfo(String limitPremInfo) {
        this.limitPremInfo = limitPremInfo;
    }

    public String getAcceptCode() {
		return acceptCode;
	}

	public void setAcceptCode(String acceptCode) {
		this.acceptCode = acceptCode;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}


	public List<UwExtraPremVO> getUwExtraPremVOs() {
		return uwExtraPremVOs;
	}

	public void setUwExtraPremVOs(List<UwExtraPremVO> uwExtraPremVOs) {
		this.uwExtraPremVOs = uwExtraPremVOs;
	}

	public List<UwLimitVO> getUwLimitVOs() {
		return uwLimitVOs;
	}

	public void setUwLimitVOs(List<UwLimitVO> uwLimitVOs) {
		this.uwLimitVOs = uwLimitVOs;
	}

	public List<UwConditionVO> getUwConditionVOs() {
		return uwConditionVOs;
	}

	public void setUwConditionVOs(List<UwConditionVO> uwConditionVOs) {
		this.uwConditionVOs = uwConditionVOs;
	}

	@Override
    public String getBizId() {
        // @invalid TODO Auto-generated method stub
        return null;
    }

}
