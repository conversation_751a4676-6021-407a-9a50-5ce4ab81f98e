package com.nci.tunan.cs.model.vo;

import com.nci.udmp.framework.model.*;
import org.slf4j.Logger;
import java.math.BigDecimal;
import java.lang.String;
import java.util.Date;

/** 
 * @description CsDrqTaskTraceVO对象
 * <AUTHOR> 
 * @date 2021-03-11 21:12:55  
 */
public class CsDrqTaskTraceVO extends BaseVO {	
	 /** 
	* @Fields videoName :  双录影音资料文件名
 	*/ 
	private String videoName;
		 /** 
	* @Fields remark :  备注
 	*/ 
	private String remark;
		 /** 
	* @Fields operatorName :  操作员姓名
 	*/ 
	private String operatorName;
	 /** 
	* @Fields operateTime :  操作时间
 	*/ 
	private Date operateTime;
	 /** 
	* @Fields acceptCode :  受理号
 	*/ 
	private String acceptCode;
	 /** 
	* @Fields operatorCode :  操作员编码
 	*/ 
	private String operatorCode;
		 /** 
	* @Fields taskStatus :  操作后的状态
 	*/ 
	private BigDecimal taskStatus;
		 /** 
	* @Fields listId :  主键序列
 	*/ 
	private BigDecimal listId;
	
	/** 
	  * @Fields otherReason :  其他原因
	*/ 
	private String otherReason;
	/** 
	* @Fields rejectReason :  质检不通过原因
	*/ 
	private String rejectReason;
				
	 public void setVideoName(String videoName) {
		this.videoName = videoName;
	}
	
	public String getVideoName() {
		return videoName;
	}
		 public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRemark() {
		return remark;
	}
		 public void setOperatorName(String operatorName) {
		this.operatorName = operatorName;
	}
	
	public String getOperatorName() {
		return operatorName;
	}
	 public void setOperateTime(Date operateTime) {
		this.operateTime = operateTime;
	}
	
	public Date getOperateTime() {
		return operateTime;
	}
	 public void setAcceptCode(String acceptCode) {
		this.acceptCode = acceptCode;
	}
	
	public String getAcceptCode() {
		return acceptCode;
	}
	 public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}
	
	public String getOperatorCode() {
		return operatorCode;
	}
		 public void setTaskStatus(BigDecimal taskStatus) {
		this.taskStatus = taskStatus;
	}
	
	public BigDecimal getTaskStatus() {
		return taskStatus;
	}
		 public void setListId(BigDecimal listId) {
		this.listId = listId;
	}
	
	public BigDecimal getListId() {
		return listId;
	}
				
	@Override
    public String getBizId() {
        return null;
    }

	public String getOtherReason() {
		return otherReason;
	}

	public void setOtherReason(String otherReason) {
		this.otherReason = otherReason;
	}

	public String getRejectReason() {
		return rejectReason;
	}

	public void setRejectReason(String rejectReason) {
		this.rejectReason = rejectReason;
	}

	/**
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see java.lang.Object#toString()
	 * @return 
	*/
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CsDrqTaskTraceVO [getVideoName()=").append(getVideoName()).append(", getRemark()=").append(getRemark()).append(", getOperatorName()=")
				.append(getOperatorName()).append(", getOperateTime()=").append(getOperateTime()).append(", getAcceptCode()=").append(getAcceptCode())
				.append(", getOperatorCode()=").append(getOperatorCode()).append(", getTaskStatus()=").append(getTaskStatus()).append(", getListId()=").append(getListId())
				.append(", getBizId()=").append(getBizId()).append(", getOtherReason()=").append(getOtherReason()).append(", getRejectReason()=").append(getRejectReason())
				.append("]");
		return builder.toString();
	}
    
   
}
