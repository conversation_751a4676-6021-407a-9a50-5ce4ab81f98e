package com.nci.tunan.cs.model.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/**
 * 
 * @description 转增养老金用
 * @<NAME_EMAIL> 
 * @date 2015-10-14 上午9:04:36 
 * @.belongToModule 保全系统-转增养老金用
 */
public class CsEndorseTALXLVO extends BaseVO{
	/**
	 * 付费id
	 */
	private BigDecimal payId;
	
	/**
	 * 本次增加标准
	 */
	private BigDecimal pensionAmount;
	/**
	 * 生存给付计划表ID
	 */
	private BigDecimal payPlanId;
	/**
	 * 生存给付应领表ID
	 */
    private BigDecimal logIds;
    /** 
    * @Fields applyTime :  申请时间
    */ 
    private Date applyTime;
   
    /** 
    * @Fields accountType :  账号类型
    */ 
    private BigDecimal accountType;
    /**
     * 受理号
     */
    private String acceptCode;
    /**
     * 保单号
     */
    private String policyCode;
    /**
     * 保障责任ID
     */
    private BigDecimal liabId;
    /**
     * 应领日期
     */
    private Date payDueDate;
    /**
     * 保单变更ID
     */
    private BigDecimal policyChgId;
    /**
     * 保单ID
     */
    private BigDecimal policyId;
    /**
     * 险种ID
     */
    private BigDecimal busiItemId;
    /**
     * 所属业务产品代码
     */
    private String busiProdCode;
    /**
     * 所属业务产品ID
     */
    private BigDecimal busiPrdId;
    /**
     * 险种状态
     */
    private BigDecimal liabilityState;
    /**
     * 责任组ID
     */
    private BigDecimal itemId;
    /**
     * 所属的精算产品ID
     */
    private BigDecimal productId;
    /**
     * 应领年龄
     */
    private BigDecimal payDueAge;
    /**
     * 养老金领取标准
     */
    private BigDecimal instalmentAmount;
    /**
     * 养老金增加标准    
     */
    private BigDecimal totalAddAmount;
    /**
     * 给付金额
     */
    private BigDecimal feeAmount;
    /**
     * 转增后的领取标准
     */
    private BigDecimal totalAmount;
    /**
     * 转增后的领取标准
     */
    private Date acceptTime;
    /**
     * 是否做过转增养老金
     * @return
     */
    private String flag; 
    
    
    public BigDecimal getPayId() {
		return payId;
	}

	public void setPayId(BigDecimal payId) {
		this.payId = payId;
	}

	public String getFlag() {
		return flag;
	}

	public void setFlag(String flag) {
		this.flag = flag;
	}

	public Date getAcceptTime() {
		return acceptTime;
	}

	public void setAcceptTime(Date acceptTime) {
		this.acceptTime = acceptTime;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public BigDecimal getPensionAmount() {
		return pensionAmount;
	}

	public void setPensionAmount(BigDecimal pensionAmount) {
		this.pensionAmount = pensionAmount;
	}

	public BigDecimal getPayPlanId() {
		return payPlanId;
	}

	public void setPayPlanId(BigDecimal payPlanId) {
		this.payPlanId = payPlanId;
	}

	public BigDecimal getLogIds() {
		return logIds;
	}

	public void setLogIds(BigDecimal logIds) {
		this.logIds = logIds;
	}

	public BigDecimal getFeeAmount() {
		return feeAmount;
	}

	public void setFeeAmount(BigDecimal feeAmount) {
		this.feeAmount = feeAmount;
	}

	public String getBusiProdCode() {
		return busiProdCode;
	}

	public void setBusiProdCode(String busiProdCode) {
		this.busiProdCode = busiProdCode;
	}

	public BigDecimal getLiabilityState() {
		return liabilityState;
	}

	public void setLiabilityState(BigDecimal liabilityState) {
		this.liabilityState = liabilityState;
	}

	public BigDecimal getProductId() {
		return productId;
	}

	public void setProductId(BigDecimal productId) {
		this.productId = productId;
	}

	public BigDecimal getBusiItemId() {
		return busiItemId;
	}

	public void setBusiItemId(BigDecimal busiItemId) {
		this.busiItemId = busiItemId;
	}

	public String getAcceptCode() {
		return acceptCode;
	}

	public void setAcceptCode(String acceptCode) {
		this.acceptCode = acceptCode;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public BigDecimal getLiabId() {
		return liabId;
	}

	public void setLiabId(BigDecimal liabId) {
		this.liabId = liabId;
	}

	public Date getPayDueDate() {
		return payDueDate;
	}

	public void setPayDueDate(Date payDueDate) {
		this.payDueDate = payDueDate;
	}

	public BigDecimal getPolicyChgId() {
		return policyChgId;
	}

	public void setPolicyChgId(BigDecimal policyChgId) {
		this.policyChgId = policyChgId;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}

	public BigDecimal getBusiPrdId() {
		return busiPrdId;
	}

	public void setBusiPrdId(BigDecimal busiPrdId) {
		this.busiPrdId = busiPrdId;
	}

	public BigDecimal getItemId() {
		return itemId;
	}

	public void setItemId(BigDecimal itemId) {
		this.itemId = itemId;
	}

	public BigDecimal getPayDueAge() {
		return payDueAge;
	}

	public void setPayDueAge(BigDecimal payDueAge) {
		this.payDueAge = payDueAge;
	}

	public BigDecimal getInstalmentAmount() {
		return instalmentAmount;
	}

	public void setInstalmentAmount(BigDecimal instalmentAmount) {
		this.instalmentAmount = instalmentAmount;
	}

	public BigDecimal getTotalAddAmount() {
		return totalAddAmount;
	}

	public void setTotalAddAmount(BigDecimal totalAddAmount) {
		this.totalAddAmount = totalAddAmount;
	}

	public Date getApplyTime() {
		return applyTime;
	}

	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	public BigDecimal getAccountType() {
		return accountType;
	}

	public void setAccountType(BigDecimal accountType) {
		this.accountType = accountType;
	}

	@Override
    public String getBizId() {
        return null;
    }
}
