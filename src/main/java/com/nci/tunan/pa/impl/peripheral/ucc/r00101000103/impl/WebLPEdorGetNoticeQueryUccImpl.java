package com.nci.tunan.pa.impl.peripheral.ucc.r00101000103.impl;

import com.nci.tunan.pa.impl.peripheral.service.r00101000103.IWebLPEdorGetNoticeQueryService;
import com.nci.tunan.pa.impl.peripheral.ucc.r00101000103.IWebLPEdorGetNoticeQueryUcc;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000103.vo.InputData;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000103.vo.OutputData;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;

/**
 * @description  满期给付通知书查询接口实现类
 * <AUTHOR> 
 * @.belongToModule PA-保单管理系统-满期给付通知书查询接口
 * @date 2017年12月4日 下午3:19:12
 */
public class WebLPEdorGetNoticeQueryUccImpl implements IWebLPEdorGetNoticeQueryUcc {
	/**
	 * @Fields webLPEdorGetNoticeQueryService ： 满期给付通知书查询Service接口
	 */
	private IWebLPEdorGetNoticeQueryService webLPEdorGetNoticeQueryService;
	
	/**
	 * 
	 * @description 满期给付通知书查询
	 * @version
	 * @title
	 * <AUTHOR> 
	 * @see com.nci.tunan.pa.impl.peripheral.ucc.r00101000103.IWebLPEdorGetNoticeQueryUcc#queryWebLPEdorGetNotice(com.nci.tunan.pa.interfaces.peripheral.exports.r00101000103.vo.InputData)
	 * @param inputData 满期给付通知书查询接口入参对象
	 * @return
	 */
	@Override
	public OutputData queryWebLPEdorGetNotice(InputData inputData) {
		SysHeader sysHeader=CommonHeaderDeal.getSYSHEADERTHREAD();
		OutputData outputData= new OutputData();
		sysHeader.setBizResCd("1");
		try {
			String contNo = inputData.getContNo();
			if(null==contNo||"".equals(contNo)){
				sysHeader.setBizResText("输入保单号为空！");
				return new OutputData();
			}
			//1、保单号不能为空
			//2、调用满期给付通知书查询Service接口
			outputData=webLPEdorGetNoticeQueryService.queryWebLPEdorGetNotice(inputData);
			if(null!=outputData&&null!=outputData.getResultList()&&outputData.getResultList().size()>0){
				sysHeader.setBizResCd("0");
				sysHeader.setBizResText("");
				return outputData;
			}else{
				sysHeader.setBizResText("没有查询到任何信息，请检查输入条件");
				return new OutputData();
			}
		} catch (Exception e) {
			e.printStackTrace();
			sysHeader.setBizResCd("1");
			sysHeader.setBizResText("没有查询到任何信息，请检查输入条件");
			return outputData;
		}
	}
	
	public IWebLPEdorGetNoticeQueryService getWebLPEdorGetNoticeQueryService() {
		return webLPEdorGetNoticeQueryService;
	}
	public void setWebLPEdorGetNoticeQueryService(
			IWebLPEdorGetNoticeQueryService webLPEdorGetNoticeQueryService) {
		this.webLPEdorGetNoticeQueryService = webLPEdorGetNoticeQueryService;
	}

}
