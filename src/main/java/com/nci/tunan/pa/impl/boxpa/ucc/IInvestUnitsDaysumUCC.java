package com.nci.tunan.pa.impl.boxpa.ucc;

import com.nci.tunan.pa.interfaces.vo.InvestUnitsDaysumBoxInputVO;
import com.nci.tunan.pa.interfaces.vo.InvestUnitsDaysumBoxOutVO;
/**
 * 
 * @description 根据客户投资代码和计算日期查询单位数总数UCC接口类
 * <AUTHOR>
 * @date 2017年5月18日 下午4:50:49
 * @.belongToModule PA-保单管理系统
 */
public interface IInvestUnitsDaysumUCC {
    /**
     * 根据客户投资代码和计算日期查询单位数总数
     * @description
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitsDaysumBoxVO 方法入参VO
     * @return
     */
   public  InvestUnitsDaysumBoxOutVO findInvestUnitsDaysum(InvestUnitsDaysumBoxInputVO investUnitsDaysumBoxInputVO );
}
