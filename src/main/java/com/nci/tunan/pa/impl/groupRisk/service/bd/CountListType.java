package com.nci.tunan.pa.impl.groupRisk.service.bd;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>CountListType complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�����ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="CountListType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="CountParam" type="{http://www.newchinalife.com/service/bd}CountParamType" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CountListType", propOrder = {
    "countParam"
})
public class CountListType {

    @XmlElement(name = "CountParam", required = true)
    protected List<CountParamType> countParam;

    /**
     * Gets the value of the countParam property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the countParam property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getCountParam().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CountParamType }
     * 
     * 
     */
    public List<CountParamType> getCountParam() {
        if (countParam == null) {
            countParam = new ArrayList<CountParamType>();
        }
        return this.countParam;
    }

	public void setCountParam(List<CountParamType> countParam) {
		this.countParam = countParam;
	}

}
