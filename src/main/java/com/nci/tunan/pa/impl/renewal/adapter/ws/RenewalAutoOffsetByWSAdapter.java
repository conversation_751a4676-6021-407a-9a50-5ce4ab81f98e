package com.nci.tunan.pa.impl.renewal.adapter.ws;

import java.util.List;

import javax.jws.WebService;
import javax.xml.ws.Holder;

import org.slf4j.Logger;

import com.nci.tunan.pa.impl.renewal.service.IRenewCollectionService;
import com.nci.tunan.pa.interfaces.model.bo.ContractMasterBO;
import com.nci.tunan.pa.interfaces.model.bo.PremArapBO;
import com.nci.tunan.pa.interfaces.model.vo.RenewCollectionVO;
import com.nci.tunan.pa.interfaces.renewalAutoOffset.exports.SrvReqBody;
import com.nci.tunan.pa.interfaces.renewalAutoOffset.exports.SrvResBizBody;
import com.nci.tunan.pa.interfaces.renewalAutoOffset.exports.SrvResBody;
import com.nci.tunan.pa.interfaces.renewalAutoOffset.exports.vo.RenewalAutoOffsetReqVO;
import com.nci.tunan.pa.interfaces.renewalAutoOffset.exports.vo.RenewalAutoOffsetResVO;
import com.nci.tunan.pa.interfaces.renewalAutoOffset.exports.ws.IRenewalAutoOffsetByWS;
import com.nci.udmp.component.serviceinvoke.message.body.hd.SRVResHead;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * @description 自核接口适配器WS
 * <AUTHOR> <EMAIL>
 * @date 2014-12-10 下午2:12:50
 */
@WebService(endpointInterface = "com.nci.tunan.pa.interfaces.renewalAutoOffset.exports.ws.IRenewalAutoOffsetByWS", serviceName = "renewalAutoOffset")
public class RenewalAutoOffsetByWSAdapter implements IRenewalAutoOffsetByWS {

	/** 
	 * @Fields logger :  日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();
	private IRenewCollectionService renewCollectionService;
	public IRenewCollectionService getRenewCollectionService() {
		return renewCollectionService;
	}
	public void setRenewCollectionService(
			IRenewCollectionService renewCollectionService) {
		this.renewCollectionService = renewCollectionService;
	}
	/**
	 * @description 自核
	 * @version 0.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @date 2014-12-10 下午2:12:59
	 * @see com.nci.tunan.pa.interfaces.renewalAutoOffset.exports.ws.IRenewalAutoOffsetByWS#renewalAutoOffset(com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader,
	 *      com.nci.tunan.pa.interfaces.renewalAutoOffset.exports.SrvReqBody,
	 *      javax.xml.ws.Holder, javax.xml.ws.Holder)
	 * @param parametersReqHeader
	 * @param parametersReqBody
	 * @param parametersResHeader
	 * @param parametersResBody
	 */
	public void renewalAutoOffset(SysHeader parametersReqHeader,
			SrvReqBody parametersReqBody,
			Holder<SysHeader> parametersResHeader,
			Holder<SrvResBody> parametersResBody) {
		logger.debug("<======RenewalAutoOffsetByWSAdapter--renewalAutoOffset======>");
		// 业务报文体赋值
		RenewalAutoOffsetResVO outputData = new RenewalAutoOffsetResVO();
		RenewalAutoOffsetReqVO inputData = parametersReqBody.getBizBody()
				.getInputData();

		RenewCollectionVO renewCollectionVO = new RenewCollectionVO();
		renewCollectionVO.setPolicyCode(inputData.getPolicyCode().toString());

		try {
			Boolean result = manulBatchExt(renewCollectionVO);
			outputData.setRenewalAutoOffsetResult(result);
		} catch (BizException e) {
			outputData.setRenewalAutoOffsetResult(false);
			parametersReqHeader.setResText(e.getMessage());
			throw new RuntimeException(e);
		} catch (Exception e) {
			outputData.setRenewalAutoOffsetResult(false);
			parametersReqHeader.setResText(e.getMessage());
			throw new RuntimeException(e);
		}

		// 业务报文头赋值
		SRVResHead header = new SRVResHead();
		BeanUtils.copyProperties(header, parametersReqBody.getBizHeader());// 属性拷贝
		// 业务包报文体
		SrvResBizBody srvResBizBody = new SrvResBizBody();
		srvResBizBody.setOutputData(outputData);

		// 系统报文头赋值
		parametersResHeader.value = parametersReqHeader;
		// 系统报文体赋值
		SrvResBody srvResBody = new SrvResBody();
		parametersResBody.value = srvResBody;
		srvResBody.setBizHeader(header);
		srvResBody.setBizBody(srvResBizBody);
	}
	/**
	 * @description 
	 *              1.renewCollectionAction通过RenewCollectionUCC调用manulBatchExt中参数设定
	 * @version 0.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @date 2014-12-10 下午1:49:06
	 * @see com.nci.tunan.pa.impl.renewal.ucc.IRenewCollectionUCC#manulBatchExt(com.nci.tunan.pa.interfaces.model.vo.RenewCollectionVO)
	 * @param renewCollectionVO
	 * @return
	 * @throws Exception
	 */
	public Boolean manulBatchExt(RenewCollectionVO renewCollectionVO)
			throws Exception {
		logger.info("<======RenewalAutoOffsetByWSAdapter--manulBatchExt======>");
		// 2. 根据用户输入的保单号或管理机构作为系统处理参数设定，提供人工核销处理操作；
		// 判断核销类型，是保单核销，还是机构核销
		if (renewCollectionVO.getPolicyCode() != null
				&& !renewCollectionVO.getPolicyCode().trim().equals("null")
				&& !renewCollectionVO.getPolicyCode().trim().equals("")) {
			// 根据保单号筛选保单进行核销
			return this.renewalCollection(renewCollectionVO);
		} else {
			// 根据管理机构筛选保单进行核销
			return this.renewalCollectionByOrganCode(renewCollectionVO);
		}
	}
	/**
	 * @description 根据保单号筛选保单进行核销
	 * @version 0.1
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @date 2014-12-10 下午1:50:12
	 * @param renewCollectionVO
	 * @return
	 * @throws Exception
	 */
	private Boolean renewalCollection(RenewCollectionVO renewCollectionVO)
			throws Exception {

		logger.info("<======RenewCollectionUCCImpl--renewalCollection======>");
		// 3. 系统根据用户输入的保单号/管理机构下查询保单产生的应收记录；
		//当保单号和机构号同时存在时，校验两者是否匹配
		if (null != renewCollectionVO.getOrganCode() && !"".equals(renewCollectionVO.getOrganCode())) {
			boolean flag = checkPolicyAndOrgan(renewCollectionVO);
			if (!flag) {
				logger.info("errCode", "保单不在该管理机构下!");
				throw new BizException("errCode", "保单不在该管理机构下!");
			}
		}
		
		// 验证保单是否存在
		ContractMasterBO contractMasterBO = this
				.queryPolicyReceivable(renewCollectionVO);
		if (contractMasterBO.getPolicyId() == null) {
			logger.info("errCode", "保单不存在!");
			throw new BizException("errCode", "保单不存在!");
		}

		//@invalid TODO 加锁 4.调用保单锁服务接口PolicyLockService的applyKey方法申请保单锁，如果处理失败，处理下一保单；
		// policyLockService.applyKey(contractMasterBO);

		// 保单下的收付费信息进行核销
		Boolean result = false;
		long startTime = System.currentTimeMillis();
		if (null != renewCollectionVO.getUnitNumber()) {
			contractMasterBO.setUnitNumber(renewCollectionVO.getUnitNumber());
		}
		result = renewCollectionService.manulBatchExt(contractMasterBO);
		logger.info("<-----------本次根据保单核销耗时  :"+(System.currentTimeMillis() - startTime)+"--------->");
		
		//@invalid TODO 解锁 9.调用保单锁服务接口PolicyLockService的releaseLock方法申请保单锁；若还有未处理保单，转第4步；
		// policyLockService.releaseLock(contractMasterBO);

		return result;
	}
	/**
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param renewCollectionVO
	 * @return
	 */
		public Boolean checkPolicyAndOrgan(RenewCollectionVO renewCollectionVO){
			boolean flag = true ;
			ContractMasterBO contractMasterBO = new ContractMasterBO();
			contractMasterBO.setPolicyCode(renewCollectionVO.getPolicyCode());
			contractMasterBO.setOrganCode(renewCollectionVO.getOrganCode());
			contractMasterBO = renewCollectionService.findContractMasterByPolicyCode(contractMasterBO);
			
			if (null == contractMasterBO.getPolicyId()) {
				flag = false;
				return flag;
			}
			return flag;
		}
		/**
		 * @description 根据保单号查找保单
		 * @version 0.1
		 * @title
		 * <AUTHOR> <EMAIL>
		 * @date 2014-12-10 下午1:50:46
		 * @param renewCollectionVO
		 * @return
		 */
		private ContractMasterBO queryPolicyReceivable(
				RenewCollectionVO renewCollectionVO) {

			logger.info("<======--queryPolicyReceivable======>");
			ContractMasterBO contractMasterBO = BeanUtils.copyProperties(
					ContractMasterBO.class, renewCollectionVO);

			ContractMasterBO bo = renewCollectionService
					.findContractMasterByPolicyCode(contractMasterBO);

			return bo;
		}
		/**
		 * @description 根据管理机构筛选保单进行核销
		 * @version 0.1
		 * @title
		 * <AUTHOR> <EMAIL>
		 * @date 2014-12-10 下午1:51:03
		 * @param contractMasterVO
		 * @return
		 * @throws Exception
		 */
		private Boolean renewalCollectionByOrganCode(
				RenewCollectionVO contractMasterVO) throws Exception {

			logger.info("<======--renewalCollectionByOrganCode======>");
			// 查找机构下的所有保单
			long startTime = System.currentTimeMillis();
			PremArapBO premArapBO = new PremArapBO();
			premArapBO.setOrganCode(contractMasterVO.getOrganCode());
			List<PremArapBO> premArapBOs = renewCollectionService.findAllPremArapByOrganCode(premArapBO);
			
			// 如果机构下保单为空返回错误
			if (premArapBOs.size() <= 0) {
				logger.info("errCode", "该机构下没有要核销的保单!");
				throw new BizException("errCode", "该机构下没有要核销的保单!");
			}

			List<RenewCollectionVO> renewCollectionVOs = BeanUtils.copyList(
					RenewCollectionVO.class, premArapBOs);
			Boolean result = false;
			for (RenewCollectionVO cmvo : renewCollectionVOs) {
				result = this.renewalCollection(cmvo);
				if (!result) {
					return false;
				}
			}
			logger.info("<-----------本次根据机构核销耗时  :"+(System.currentTimeMillis() - startTime)+"--------->");
			return true;
		}
}
