package com.nci.tunan.pa.impl.peripheral.ucc.r00101000100;

import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000100.vo.InputData;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000100.vo.OutputData;

/**
 * @description 保单相关影像文件查询接口
 * <AUTHOR> 
 * @.belongToModule PA-保单管理系统-保单相关影像文件查询接口
 * @date 2017年12月6日 下午5:33:02
 */
public interface IQueryImageFileUcc {
	/**
	 * 
	 * @description 查询投保单影像件
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param inputData 保单相关影像文件查询接口入参对象
	 * @return
	 */
	public OutputData queryImageFile(InputData inputData);
}
