package com.nci.tunan.pa.impl.nbdatadeal.service.impl;

import org.slf4j.Logger;

import com.nci.tunan.pa.impl.nbdatadeal.bo.PolicyPrintListBO;
import com.nci.tunan.pa.impl.nbdatadeal.dao.IPostLostDealDao;
import com.nci.tunan.pa.impl.nbdatadeal.po.PolicyPrintListPO;
import com.nci.tunan.pa.impl.nbdatadeal.service.IPostLostDealService;
import com.nci.tunan.pa.interfaces.serviceData.postlostdeal.PostLostDealReqVO;
import com.nci.tunan.pa.interfaces.serviceData.postlostdeal.PostLostDealResVO;
import com.nci.udmp.util.bean.BeanUtils;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * 物流丢失保单标记service实现类
 * @description 
 * <AUTHOR> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2017年7月18日14:50:35
 */
public class PostLostDealServiceImpl implements IPostLostDealService {

	/**
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();
	
	/**
	 * @Fields postLostDealDao : 物流丢失保单标记dao
	 */
	private IPostLostDealDao postLostDealDao;

	/**
	 * @description 物流丢失保单标记service方法
	 * <AUTHOR>
	 * @param postLostDealReqVO 物流丢失保单标记入参VO
	 * @return PostLostDealResVO 物流丢失保单标记出参VO
	 */
	@Override
	public PostLostDealResVO insertPostLost(PostLostDealReqVO postLostDealReqVO) {
		// 返回结果
		PostLostDealResVO postLostDealResVO = new PostLostDealResVO();
		
		// 保单补发记录赋值
		PolicyPrintListBO policyPrintListBO = new PolicyPrintListBO();
		policyPrintListBO.setIsReissue(postLostDealReqVO.getIsReissue());
		policyPrintListBO.setPolicyCode(postLostDealReqVO.getPolicyCode());
		policyPrintListBO.setPrintCause(postLostDealReqVO.getReissueCause());
		policyPrintListBO.setPrintDate(postLostDealReqVO.getPrintDate());
		policyPrintListBO.setPrintTime(postLostDealReqVO.getPrintTime());
		try {
			// 添加数据
			PolicyPrintListPO policyPrintListPO = postLostDealDao.insertPostLost(BeanUtils.copyProperties(PolicyPrintListPO.class, policyPrintListBO));
			if (policyPrintListPO != null) {
				logger.info("-----------------添加成功------");
				postLostDealResVO.setResultFlag("1");
			} else {
				postLostDealResVO.setResultFlag("0");
				postLostDealResVO.setResultMsg("添加失败");
			}
		} catch (Exception e) {
			e.printStackTrace();
			postLostDealResVO.setResultFlag("0");
			postLostDealResVO.setResultMsg("添加失败");
			throw new RuntimeException(e);
		}
		return postLostDealResVO;
		
	}
	
	public IPostLostDealDao getPostLostDealDao() {
		return postLostDealDao;
	}

	public void setPostLostDealDao(IPostLostDealDao postLostDealDao) {
		this.postLostDealDao = postLostDealDao;
	}

	
}
