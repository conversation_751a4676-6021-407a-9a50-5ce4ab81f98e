package com.nci.tunan.pa.impl.boxpa.service.impl;

import java.util.List;
import java.util.Map;

import com.nci.tunan.pa.dao.IInvestUnitsDaysumDao;
import com.nci.tunan.pa.impl.boxpa.service.IInvestUnitsDaysumService;
import com.nci.tunan.pa.interfaces.model.bo.InvestUnitsDaysumBO;
import com.nci.tunan.pa.interfaces.model.po.InvestUnitsDaysumPO;
import com.nci.udmp.util.bean.BeanUtils;

/**
 * InvestUnitsDaysumServiceImpl实现类
 * @description 根据客户投资代码和计算日期查询单位数总数Service实现类
 * <AUTHOR> <EMAIL> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2017年8月16日 上午10:37:39
 */
public class InvestUnitsDaysumServiceImpl implements  IInvestUnitsDaysumService {
	/**
	 * investUnitsDaysumDao : 根据客户投资代码和计算日期查询单位数总数Dao
	 */
    private IInvestUnitsDaysumDao investUnitsDaysumDao;
    
    public IInvestUnitsDaysumDao getInvestUnitsDaysumDao() {
        return investUnitsDaysumDao;
    }

    public void setInvestUnitsDaysumDao(IInvestUnitsDaysumDao investUnitsDaysumDao) {
        this.investUnitsDaysumDao = investUnitsDaysumDao;
    }
	/**
	 * 
	 * @description 根据客户投资代码和计算日期查询单位数总数
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL> 
	 * @see com.nci.tunan.pa.impl.boxpa.service.IInvestUnitsDaysumService#findInvestUnitsDaysum(com.nci.tunan.pa.interfaces.model.bo.InvestUnitsDaysumBO)
	 * @param investUnitsDaysumBO investUnitsDaysumBO 方法入参VO
	 * @return
	 */
    @Override
    public InvestUnitsDaysumBO findInvestUnitsDaysum(InvestUnitsDaysumBO investUnitsDaysumBO) {
        InvestUnitsDaysumPO investUnitsDaysumPO = BeanUtils.copyProperties(InvestUnitsDaysumPO.class, investUnitsDaysumBO);
        InvestUnitsDaysumPO investUnitsDaysumpo =  investUnitsDaysumDao.findInvestUnitsDaysum(investUnitsDaysumPO);
        return BeanUtils.copyProperties(InvestUnitsDaysumBO.class, investUnitsDaysumpo);
    }
    /**
     * 根据交易日期查询基金代码、交易单位数总数sum(TRANS_UNITS)
     * @description
     * @version
     * @title
     * <AUTHOR>
     * @param date 交易日期
     * @return
     */
    public List<Map<String,Object>> findAccountCodeBydate(String date){
        return investUnitsDaysumDao.findAccountCodeBydate(date);
    }
    /**
     * 根据交易日期、基金代码、查询各交易类型的 单位数总和
     * @description
     * @version
     * @title
     * <AUTHOR>
     * @param date 交易日期
     * @param accountCode 基金代码
     * @return
     */
    public List<Map<String,Object>> findAccountCodeByDCode(String date,String accountCode){
        return investUnitsDaysumDao.findAccountCodeByDCode(date,accountCode);
        
    }
    /**
     * 根据基金代码和交易日期获取基金的上一次交易日期 
     * @description
     * @version
     * @title
     * <AUTHOR>
     * @param date 交易日期
     * @param accountCode 基金代码
     * @return
     */
    public List<Map<String,Object>> findAccounDateBydate(String date,String accountCode){
        return investUnitsDaysumDao.findAccounDateBydate(date,accountCode);
    }
    
}
