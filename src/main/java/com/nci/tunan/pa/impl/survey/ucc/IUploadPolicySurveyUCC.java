package com.nci.tunan.pa.impl.survey.ucc;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.nci.tunan.pa.interfaces.model.vo.ClaimSurveyBatchVO;
import com.nci.tunan.pa.interfaces.model.vo.PolicyListVO;
import com.nci.udmp.framework.exception.app.BizException;
import com.nci.udmp.framework.model.CurrentPage;

/**
 * 上载保单后发起前置调查UCC
 * @description 
 * <AUTHOR> <EMAIL>
 * @date 2015-10-26 下午4:53:46
 * @.belongToModule PA-保单管理系统-上载保单后发起前置调查
 */
public interface IUploadPolicySurveyUCC {
	
	/**
	 * 上载报案列表
	 * @description
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param policyListVO 上载保单列表后自动发起前置调查入参VO
	 * @return
	 * @throws Exception 异常处理
	 */
    public String uploadPolicyList(PolicyListVO policyListVO) throws Exception;
    
    /**
     * 
     * @description 查询保单列表
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyCodeS 保单号
     * @param currentPage 分页信息
     * @return
     * @throws BizException 异常处理
     */
    public CurrentPage<PolicyListVO> queryPolicyList(String policyCodeS,CurrentPage<PolicyListVO> currentPage) throws BizException;
    
    /**
     * 
     * @description 查询保单分页列表跟新增的调查批次信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyListVO 上载保单列表后自动发起前置调查入参VO
     * @param policyCodeS 保单号
     * @param currentPage 分页信息
     * @return
     * @throws BizException 异常处理
     */
    public Map<String, Object> queryPolicyListAndBatch(PolicyListVO policyListVO, String policyCodeS,CurrentPage<PolicyListVO> currentPage) throws BizException;
    
    /**
     * 
     * @description 发起前置调查
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyListVO 上载保单列表后自动发起前置调查入参VO
     * @throws BizException 异常处理
     */
    public void addSurveyPlan(PolicyListVO policyListVO) throws BizException;
    
    /**
     * 
     * @description 回显
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyCodeS 保单号
     * @param currentPage 分页信息
     * @param claimSurveyBatchVO 当前页对象
     * @return
     * @throws Exception 异常处理
     */
    public CurrentPage<PolicyListVO> showUploadPolicy(String policyCodeS, CurrentPage<PolicyListVO> currentPage, ClaimSurveyBatchVO claimSurveyBatchVO) throws Exception;
    
    /**
     * 
     * @description 查询保单上载历史
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSurveyBatchVO 当前页对象
     * @param startDate 起止时间
     * @param endDate 结束时间
     * @param currentPage 分页信息
     * @return
     * @throws BizException 异常处理
     */
    public CurrentPage<ClaimSurveyBatchVO> queryPolicyUploadHistory(ClaimSurveyBatchVO claimSurveyBatchVO, Date startDate, Date endDate, CurrentPage<ClaimSurveyBatchVO> currentPage) throws BizException;
    
    /**
     * 
     * @description 删除保单列表中的记录
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param policyListVO 上载保单列表后自动发起前置调查入参VO
     * @param claimSurveyBatchVO 当前页对象
     * @throws BizException 异常处理
     */
    public void deletePolicyCode(PolicyListVO policyListVO, ClaimSurveyBatchVO claimSurveyBatchVO) throws BizException;
    
    /**
     * 
     * @description 查询批次信息
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param claimSurveyBatchVO 当前页对象
     * @return
     */
    public boolean rechecknameValidate(ClaimSurveyBatchVO claimSurveyBatchVO);
    
}
