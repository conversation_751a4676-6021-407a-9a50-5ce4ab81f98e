package com.nci.tunan.pa.impl.peripheral.ucc.r00101000011;

import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000011.vo.InputData;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000011.vo.OutputData;

/**
 * 
 * @description 个人保单信息查询接口
 * <AUTHOR> 
 * @.belongToModule PA-保单管理系统-个人保单信息查询接口
 * @date 2017年12月6日 下午6:27:59
 */
public interface IQueryPersonalPolicyDetailUCC {
	
	/**
	 * 
	 * @description 查询个人保单详细信息
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param inputData 个人保单信息查询接口入参对象
	 * @return
	 */
	public OutputData queryPersonalPolicy(InputData inputData);

}
