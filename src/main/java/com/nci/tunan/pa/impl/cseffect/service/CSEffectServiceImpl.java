package com.nci.tunan.pa.impl.cseffect.service;

import com.nci.tunan.cs.model.bo.CsAcceptChangeBO;
import com.nci.tunan.cs.model.bo.CsEffectBO;
import com.nci.tunan.pa.impl.cseffect.ICSEffectService;
import com.nci.tunan.pa.interfaces.model.bo.ContractMasterBO;

/**
 * CSEffectServiceImpl实现类
 * @description 保全业务公共Service实现类
 * <AUTHOR> <EMAIL> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2017年8月16日 上午10:54:37
 */
public class CSEffectServiceImpl implements ICSEffectService {
	/**
	 * 
	 * @description 基本信息备注项变更生效
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL> 
	 * @see com.nci.tunan.pa.impl.cseffect.ICSEffectService#updateBasicRemark(com.nci.tunan.pa.interfaces.model.bo.ContractMasterBO)
	 * @param contractMasterBO 保单基本信息BO
	 * @return
	 */
	@Override
	public String updateBasicRemark(ContractMasterBO contractMasterBO) {
		// 
		//判断是否为空
		return null;
	}
	/**
	 * 
	 * @description 工作流调用的生效方法入口
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL> 
	 * @see com.nci.tunan.pa.impl.cseffect.ICSEffectService#csEffect(com.nci.tunan.cs.model.bo.CsAcceptChangeBO)
	 * @param csAcceptChangeBO 保单变更管理BO
	 * @return
	 */
	@Override
	public CsEffectBO csEffect(CsAcceptChangeBO csAcceptChangeBO) {
		// 
		//判断是否为空
		return null;
	}

}
