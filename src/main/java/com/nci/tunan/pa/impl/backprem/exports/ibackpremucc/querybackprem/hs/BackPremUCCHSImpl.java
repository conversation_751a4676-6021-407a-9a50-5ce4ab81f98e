package com.nci.tunan.pa.impl.backprem.exports.ibackpremucc.querybackprem.hs;

import com.nci.udmp.component.serviceinvoke.message.ServiceResult;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.component.serviceinvoke.util.CommonDealManagement;
import com.nci.udmp.component.dealmanagement.constant.DealTrigger;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.SrvReqBody;
import com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.SrvResBizBody;
import com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.SrvResBody;
import com.nci.tunan.pa.interfaces.backprem.exports.ibackpremucc.querybackprem.hs.IBackPremUCCHS;
import com.nci.tunan.pa.impl.backprem.ucc.IBackPremUCC;
import org.slf4j.Logger;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import java.util.Map;

/**
 * BackPremUCCHSImpl
 * @description 
 * <AUTHOR> <EMAIL> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2017年12月4日 下午6:46:22
 */
public class BackPremUCCHSImpl implements IBackPremUCCHS {

    /** 
     * @Fields logger : 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();
    
    private IBackPremUCC ucc = null;
    
    public IBackPremUCC getUcc() {
        return ucc;
    }
    public void setUcc(IBackPremUCC ucc) {
        this.ucc = ucc;
    }
    
    public ServiceResult queryBackPrem(SysHeader parametersReqHeader, SrvReqBody parametersReqBody) {
        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        String dealTime = DateUtilsEx.getTodayTime();
        String systemName = StringUtilsEx.getStr("com.nci.tunan.pa.impl.backprem.exports.ibackpremucc.querybackprem.hs", 4, ".");
        String dealNo = systemName + "_" + "IBackPremUCC" + "_" + "queryBackPrem";
        if (dealSwitch) {
            logger.debug("开始记录交易请求日志");
            CommonDealManagement.beforeCommonDealManagement(parametersReqHeader, parametersReqBody.getBizHeader(),
                parametersReqBody.getBizBody(), DealTrigger.HESSIAN, dealNo, dealTime);
        }
        CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
        CommonHeaderDeal.setBIZHEADERTHREAD(parametersReqBody.getBizHeader());
        com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData inputVO = parametersReqBody.getBizBody().getInputData();
        try {
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(parametersReqHeader));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersReqBody.getBizHeader()));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersReqBody.getBizBody()));
        } catch (Exception e1) {
            e1.printStackTrace();
            logger.debug("解析调用前报文的JSON字符串发生异常");
        }
        SrvResBody srvResBody = new SrvResBody();
		SysHeader sysHeader = new SysHeader();
		ServiceResult result = new ServiceResult();
		String dealStatus = "2";
		try {
		    com.nci.tunan.pa.interfaces.serviceData.endcase.BackPremResData output = ucc.queryBackPrem(inputVO);
		    sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
		    BizHeader bizHeader = CommonHeaderDeal.dealBizHeader(parametersReqBody.getBizHeader());
		    SrvResBizBody srvResBizBody = new SrvResBizBody();
            srvResBizBody.setOutputData(output);
            srvResBody = new SrvResBody();
            srvResBody.setBizHeader(bizHeader);
            srvResBody.setBizBody(srvResBizBody);
            result.setSysHeader(sysHeader);
            result.setResponse(srvResBody);
		} catch (Exception e2) {
			dealStatus = "3";
			logger.error("调用接口过程中产生异常!", e2);
		} finally {
			if (dealSwitch) {
				logger.debug("开始记录交易响应日志");
				CommonDealManagement.afterCommonDealManagement(sysHeader,
						srvResBody.getBizHeader(), srvResBody.getBizBody(),
						DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
			}
		}
        
        try {
            logger.debug(" Hessian返回结果的消息id：" + result.getSysHeader().getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(result.getSysHeader()));
            logger.debug(" Hessian返回结果的消息id：" + result.getSysHeader().getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(result.getResponse()));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析调用后报文的JSON字符串发生异常");
        }
        return result;
    }
}

