package com.nci.tunan.pa.impl.overprem.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.nci.tunan.pa.common.Constants;
import com.nci.tunan.pa.dao.IBusinessProductDao;
import com.nci.tunan.pa.dao.IContractBusiProdDao;
import com.nci.tunan.pa.dao.IContractInvestDao;
import com.nci.tunan.pa.dao.IContractMasterDao;
import com.nci.tunan.pa.dao.IFundTransDao;
import com.nci.tunan.pa.dao.IPremArapDao;
import com.nci.tunan.pa.impl.overprem.service.IOverPremService;
import com.nci.tunan.pa.interfaces.model.bocomp.EndCaseReqBO;
import com.nci.tunan.pa.interfaces.model.bocomp.OverPremResBO;
import com.nci.tunan.pa.interfaces.model.po.BusinessProductPO;
import com.nci.tunan.pa.interfaces.model.po.ContractBusiProdPO;
import com.nci.tunan.pa.interfaces.model.po.ContractInvestPO;
import com.nci.tunan.pa.interfaces.model.po.ContractMasterPO;
import com.nci.tunan.pa.interfaces.model.po.FundTransPO;
import com.nci.tunan.pa.interfaces.model.po.PremArapPO;

/**
 * 理赔结案-出险日之后的已交保费查询接口实现类
 * @description 
 * <AUTHOR> <EMAIL> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2017年8月16日 上午11:19:49
 */
public class OverPremServiceImpl implements IOverPremService {
	
	/**
	 * @Fields contractMasterDao ： 保单基本信息表Dao
	 */
	private IContractMasterDao contractMasterDao;
	
	/**
	 * @Fields contractBusiProdDao ：险种表Dao
	 */
	private IContractBusiProdDao contractBusiProdDao;
	
	/**
	 * @Fields premArapDao ： 应收应付Dao
	 */
	private IPremArapDao premArapDao;
	
	/**
	 * @Fields businessProductDao ： 业务产品Dao
	 */
	private IBusinessProductDao businessProductDao;
	/**
	 * @Fields fundTransDao ： 保单投资连结交易表Dao
	 */
	private IFundTransDao fundTransDao;
	/**
	 * @Fields contractInvestDao ： 保单投资连结表Dao
	 */
	private IContractInvestDao contractInvestDao;
	/**
	 * 
	 * @description  理赔结案-出险日之后的已交保费查询
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.pa.impl.overprem.service.IOverPremService#queryOverPrem(com.nci.tunan.pa.interfaces.model.bocomp.EndCaseReqBO)
	 * @param inputBO 终止原因对象
	 * @return
	 */
	@Override
	public OverPremResBO queryOverPrem(EndCaseReqBO inputBO) {
		OverPremResBO result = new OverPremResBO();
		BigDecimal  overPrem = new BigDecimal(0);
		//1、根据保单号查询保单基本信息
		ContractMasterPO contractMasterPO = new ContractMasterPO();
		contractMasterPO.setPolicyCode(inputBO.getPolicyCode());
		contractMasterPO = contractMasterDao.findContractMaster(contractMasterPO);
		//2、根据保单号,险种ID查询对应的险种信息
		ContractBusiProdPO contractBusiProdPO = new ContractBusiProdPO();
		contractBusiProdPO.setPolicyCode(inputBO.getPolicyCode());
		contractBusiProdPO.setBusiItemId(inputBO.getBusiItemId());
		contractBusiProdPO = contractBusiProdDao.findContractBusiProd(contractBusiProdPO);
		//3、根据保单号,险种查询对应的应收应付信息
		PremArapPO  premArapPO = new PremArapPO();
		premArapPO.setPolicyCode(inputBO.getPolicyCode());
		premArapPO.setBusiProdCode(contractBusiProdPO.getBusiProdCode());
		List<PremArapPO> premArapPOs = premArapDao.findMoreFeeAmount(premArapPO);
		//4、到账时间在出险日之后并且应缴时间在出险日之后的实收记录
		if(CollectionUtils.isNotEmpty(premArapPOs)){
			for(PremArapPO po : premArapPOs){
				if(null!=po.getFinishTime() && null!=po.getDueTime()){
					if((po.getDueTime().after(inputBO.getDateFlag())&&po.getFeeType().equals("G003010000"))||(po.getDueTime().after(inputBO.getDateFlag())&&po.getFeeType().equals("G003010001"))){
						overPrem = overPrem.add(po.getFeeAmount());
					}
				}
			}
		}
		//5、判断是否万能险
		BusinessProductPO businessProductPO = new BusinessProductPO();
		businessProductPO.setBusinessPrdId(contractBusiProdPO.getBusiPrdId());
		businessProductPO = businessProductDao.findBusinessProduct(businessProductPO);
		if(businessProductPO.getProductCategory1().equals(Constants.PROD_BIZ_CATEGORY__20003)){
			ContractInvestPO contractInvestPO = new ContractInvestPO();
			contractInvestPO.setBusiItemId(inputBO.getBusiItemId());
			List<ContractInvestPO> contractInvestListPO = contractInvestDao.findAllContractInvest(contractInvestPO);
			for(ContractInvestPO feContractInvestPO : contractInvestListPO){
				FundTransPO fundTransPO = new FundTransPO();
				fundTransPO.setListId(feContractInvestPO.getListId());
				List<String> transCodeList = new ArrayList<String>();
				transCodeList.add(Constants.TRANSACTION_CODE__17);// @invalid 生存金或年金转入即万能账户的保费
				fundTransPO.setTransType(Constants.INVEST_TRANS_TYPE__IN);
				fundTransPO.getData().put("trans_code_list", transCodeList);
				fundTransPO.getData().put("start_date", inputBO.getDateFlag());
				List<FundTransPO> fundTransPOList = fundTransDao.findAllFundTrans(fundTransPO);
				for(FundTransPO feFundTransPO : fundTransPOList){
					if(overPrem == null){
						overPrem = feFundTransPO.getTransAmount();
					} else {
						overPrem = overPrem.add(feFundTransPO.getTransAmount());
					}
				}
			}
		}
		result.setOverPrem(overPrem);
		return result;
	}

	public IContractMasterDao getContractMasterDao() {
		return contractMasterDao;
	}

	public void setContractMasterDao(IContractMasterDao contractMasterDao) {
		this.contractMasterDao = contractMasterDao;
	}

	public IPremArapDao getPremArapDao() {
		return premArapDao;
	}

	public void setPremArapDao(IPremArapDao premArapDao) {
		this.premArapDao = premArapDao;
	}
	
	public IContractBusiProdDao getContractBusiProdDao() {
		return contractBusiProdDao;
	}

	public void setContractBusiProdDao(IContractBusiProdDao contractBusiProdDao) {
		this.contractBusiProdDao = contractBusiProdDao;
	}

	public IBusinessProductDao getBusinessProductDao() {
		return businessProductDao;
	}

	public void setBusinessProductDao(IBusinessProductDao businessProductDao) {
		this.businessProductDao = businessProductDao;
	}

	public IFundTransDao getFundTransDao() {
		return fundTransDao;
	}

	public void setFundTransDao(IFundTransDao fundTransDao) {
		this.fundTransDao = fundTransDao;
	}

	public IContractInvestDao getContractInvestDao() {
		return contractInvestDao;
	}

	public void setContractInvestDao(IContractInvestDao contractInvestDao) {
		this.contractInvestDao = contractInvestDao;
	}
	
	
}
