package com.nci.tunan.pa.impl.shMedical;

import java.math.BigDecimal;

import com.nci.tunan.cs.model.bo.CsPayerAccountBO;
import com.nci.tunan.pa.interfaces.model.bo.ContractMedicalBO;

/**
 * @description  上海医保缴费方式修改service接口
 * <AUTHOR> <EMAIL> 
 * @date 2019-08-07 下午5:06:54 
 * @.belongToModule PA-保单管理系统-上海医保缴费方式修改
 */
public interface ShMedicalService {
	
	/**
	 * @description 根据保单号policyId判断是否是上海的保单
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param policyId 保单id
	 * @return
	 */
 	boolean judgeShByPolicyId(BigDecimal policyId);
 	
 	/**
 	 * @description 根据policyCode判断该保单是不是上海的保单
 	 * @version
 	 * @title
 	 * @<NAME_EMAIL>
 	 * @param policyCode 保单号
 	 * @return
 	 */
 	boolean judgeShByPolicyCode(String policyCode);
 	
 	/**
 	 * @description 根据保单号policyId判断是否是医保标识（目前是如果该保单的交费形式是社保缴纳的话就给ta医保标识）
 	 * @version
 	 * @title
 	 * @<NAME_EMAIL>
 	 * @param policyId 保单id
 	 * @return
 	 */
 	String judgeMedicalByPolicyId(BigDecimal policyId);
 	
 	/**
 	 * @description 根据保单号policyId判断是否是上海医保的保单（判断方式：查询T_PREM_ARAP_MEDICAL表是否有数据，如果有的话说明是上海医保的保单）
 	 * @version
 	 * @title
 	 * @<NAME_EMAIL>
 	 * @param policyId 保单id
 	 * @return
 	 */
 	boolean judgeShMedicalByPolicyId(BigDecimal policyId);

 	/**
 	 * @description  根据changeId查询表T_CS_CONTRACT_MEDICAL得到医保卡号
 	 * @version
 	 * @title
 	 * @<NAME_EMAIL>
 	 * @param changeId 变更id
 	 * @param oldNew   新旧医保卡号
 	 * @return
 	 */
	String selectMedicalNo(BigDecimal changeId,String oldNew);

	/**
	 * @description 根据changeId和acceptId查询是否是上海医保的保单
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param changeId  变更id
	 * @param acceptId  acceptid
	 * @return
	 */
	boolean judgeShMedicalByChangeIdAndAcceptId(BigDecimal changeId,
			BigDecimal acceptId);

	/**
	 * @description 修改医保卡号
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param csPayerAccountForPABO csPayerAccountForPABO对象入参
	 * @return
	 */
	boolean updateMedicalNO(CsPayerAccountBO csPayerAccountForPABO);
	/**
	 * 根据保单号码policyCode查询续保状态是否是续保
	 * @param policyCode
	 * @return 
	 */
    public  String selectIsRenew(String policyCode);

    /**
	 * 根据保单号policyCode查询保单信息
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param contractMedicalPO
	 * @return
	 */
    public ContractMedicalBO queryPolicyMedicalId(String policySequenceNo);

    /**
	 * 更新保单是否续保确认标识
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param contractMedicalPO
	 * @return
	 */
    public boolean updatePolicyRenewConfirmFlag(ContractMedicalBO contractMedicalBO);

}
