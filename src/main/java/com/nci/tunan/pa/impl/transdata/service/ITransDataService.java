package com.nci.tunan.pa.impl.transdata.service;

import java.util.List;

import com.nci.tunan.pa.interfaces.model.bo.TransDataBO;

/**
 * ITransDataService
 * @description 保单变更履历查询Service接口
 * <AUTHOR> <EMAIL> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统-保单变更履历查询
 * @date 2017年12月4日 下午5:08:46
 */
public interface ITransDataService {
	/**
	 * 
	 * @description 保单变更履历查询
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL> 
	 * @param inputBO 保单变更履历查询BO
	 * @return
	 */
	public List<TransDataBO> findAllBo(TransDataBO inputBO);
	
}
