package com.nci.tunan.pa.impl.peripheral.ucc.r00101000184.impl;

import java.text.ParseException;
import java.util.Date;

import com.nci.tunan.pa.impl.peripheral.service.r00101000184.IRenewalMessageQueryService;
import com.nci.tunan.pa.impl.peripheral.ucc.r00101000184.IRenewalMessageQueryUcc;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000184.vo.OutputData;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000184.vo.InputData;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.util.lang.StringUtilsEx;


/**
 * @description 续收查询接口实现类
 * <AUTHOR> 
 * @.belongToModule PA-保单管理系统-续收查询接口
 * @date 2017年12月6日 下午5:58:09
 */
public class RenewalMessageQueryUccImpl implements IRenewalMessageQueryUcc {


	/**
	 * @Fields renewalMessageQueryService ： 续收查询Service
	 */
	private IRenewalMessageQueryService renewalMessageQueryService;

	public IRenewalMessageQueryService getRenewalMessageQueryService() {
		return renewalMessageQueryService;
	}

	public void setRenewalMessageQueryService(
			IRenewalMessageQueryService renewalMessageQueryService) {
		this.renewalMessageQueryService = renewalMessageQueryService;
	}
/**
 * 
 * @description 续收查询方法
 * @version
 * @title
 * <AUTHOR>
 * @see com.nci.tunan.pa.impl.peripheral.ucc.r00101000184.IRenewalMessageQueryUcc#renewalMessageQuery(com.nci.tunan.pa.interfaces.peripheral.exports.r00101000184.vo.InputData)
 * @param inputData 续收查询接口入参对象
 * @return
 */
	@Override
	public OutputData renewalMessageQuery(InputData inputData) {

		OutputData outputData = new OutputData();
		//@invalid 带有响应吗
		SysHeader sysHeader = CommonHeaderDeal.getSYSHEADERTHREAD();

		//@invalid 收费类型
		String recieveFeeCode = inputData.getReceiveFeeCode();
		//@invalid 保单起始日
		Date startPayDate = inputData.getStartPayDate();
		//@invalid 保单结束日期
		Date endPayDate = inputData.getEndPayDate();
		//@invalid 业务员编号
		String agentCode = inputData.getAgentCode();
		if (StringUtilsEx.isNullOrEmpty(agentCode)
				|| StringUtilsEx.isNullOrEmpty(recieveFeeCode)
				|| StringUtilsEx.isNullOrEmpty(startPayDate.toString())
				|| StringUtilsEx.isNullOrEmpty(endPayDate.toString())) {
			//@invalid 如果这几个必填项的值为null或者"",直接结束
			sysHeader.setBizResCd("1");
			sysHeader.setBizResText("必填项为空");
		}else{
			//@invalid 当必填项不为null时，进行方法的调用
			try {
				//1、调用续收查询方法
				outputData = renewalMessageQueryService.renewalMessageQuery(inputData);
				if(outputData.getResultList()!=null&&outputData.getResultList().size()>0){
					sysHeader.setBizResCd("0");
					sysHeader.setBizResText("成功");
				}else{
					sysHeader.setBizResCd("0");
					sysHeader.setBizResText("查询结果为空");
				}
			} catch (ParseException e) {
				sysHeader.setBizResCd("1");
				sysHeader.setBizResText("失败");
				throw new RuntimeException(e);
			}
		}
		return outputData;
	}

}
