package com.nci.tunan.pa.impl.risk.exports.iriskamountucc.queryproductriskdetail.hs;

import com.nci.udmp.component.serviceinvoke.message.ServiceResult;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;
import com.nci.udmp.component.serviceinvoke.util.CommonDealManagement;
import com.nci.udmp.component.dealmanagement.constant.DealTrigger;
import com.nci.udmp.framework.para.ParaDefInitConst;
import com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.SrvReqBody;
import com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.SrvResBizBody;
import com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.SrvResBody;
import com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.hs.IRiskAmountUCCHS;
import com.nci.tunan.pa.impl.risk.ucc.IRiskAmountUCC;
import org.slf4j.Logger;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.udmp.util.json.DataSerialJSon;
import com.nci.udmp.util.lang.DateUtilsEx;
import com.nci.udmp.util.lang.StringUtilsEx;
import com.nci.udmp.framework.util.Constants;
import com.nci.udmp.app.bizservice.bo.ParaDefBO;
import java.util.Map;

public class RiskAmountUCCHSImpl implements IRiskAmountUCCHS {

    /** 
     * @Fields logger : 日志工具 
     */ 
    private static Logger logger = LoggerFactory.getLogger();
    
    private IRiskAmountUCC ucc = null;
    
    public IRiskAmountUCC getUcc() {
        return ucc;
    }
    public void setUcc(IRiskAmountUCC ucc) {
        this.ucc = ucc;
    }
    
    /**
     * 
     * @description
     * @version V1.0.0
     * @title
     * <AUTHOR> 
     * @see com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.hs.IRiskAmountUCCHS#queryProductRiskDetail(com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader, com.nci.tunan.pa.interfaces.risk.exports.iriskamountucc.queryproductriskdetail.SrvReqBody)
     * @param parametersReqHeader
     * @param parametersReqBody
     * @return
     */
    public ServiceResult queryProductRiskDetail(SysHeader parametersReqHeader, SrvReqBody parametersReqBody) {
        Map<String, Object> applicationMap = ParaDefInitConst.getApplicationConst();
        boolean dealSwitch = false;
        if (applicationMap.get(Constants.DEALSWITCH) != null) {
            dealSwitch = "1".equals(((ParaDefBO) applicationMap
                    .get(Constants.DEALSWITCH)).getParaValue());
        }
        String dealTime = DateUtilsEx.getTodayTime();
        String systemName = StringUtilsEx.getStr("com.nci.tunan.pa.impl.risk.exports.iqueryriskamountucc.queryproductriskdetail.hs", 4, ".");
        String dealNo = systemName + "_" + "IQueryRiskAmountUCC" + "_" + "queryProductRiskDetail";
        if (dealSwitch) {
            logger.debug("开始记录交易请求日志");
            CommonDealManagement.beforeCommonDealManagement(parametersReqHeader, parametersReqBody.getBizHeader(),
                parametersReqBody.getBizBody(), DealTrigger.HESSIAN, dealNo, dealTime);
        }
        CommonHeaderDeal.setSYSHEADERTHREAD(parametersReqHeader);
        CommonHeaderDeal.setBIZHEADERTHREAD(parametersReqBody.getBizHeader());
            com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailReqVO inputVO = parametersReqBody.getBizBody().getInputData();
        try {
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-技术报文头：" + DataSerialJSon.fromObject(parametersReqHeader));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文头：" + DataSerialJSon.fromObject(parametersReqBody.getBizHeader()));
            logger.debug(" 消息id：" + parametersReqHeader.getMsgId() + "-业务报文体：" + DataSerialJSon.fromObject(parametersReqBody.getBizBody()));
        } catch (Exception e1) {
            e1.printStackTrace();
            logger.debug("解析调用前报文的JSON字符串发生异常");
            throw new RuntimeException(e1);
        }
        SrvResBody srvResBody = new SrvResBody();
        SysHeader sysHeader = new SysHeader();
        ServiceResult result = new ServiceResult();
        String dealStatus = "2";
        try {
            com.nci.tunan.pa.interfaces.serviceData.riskdetail.QueryProductRiskDetailResVO output = ucc.queryProductRiskDetail(inputVO);
            sysHeader = CommonHeaderDeal.dealSysHeader(parametersReqHeader);
            BizHeader bizHeader = CommonHeaderDeal.dealBizHeader(parametersReqBody.getBizHeader());
            SrvResBizBody srvResBizBody = new SrvResBizBody();
            srvResBizBody.setOutputData(output);
            srvResBody = new SrvResBody();
            srvResBody.setBizHeader(bizHeader);
            srvResBody.setBizBody(srvResBizBody);
            result.setSysHeader(sysHeader);
            result.setResponse(srvResBody);
        } catch (Exception e2) {
            dealStatus = "3";
            logger.error("调用接口过程中产生异常!", e2);
            throw new RuntimeException(e2);
        } finally {
            if (dealSwitch) {
                logger.debug("开始记录交易响应日志");
                CommonDealManagement.afterCommonDealManagement(sysHeader,
                        srvResBody.getBizHeader(), srvResBody.getBizBody(),
                        DealTrigger.HESSIAN, dealNo, dealTime, dealStatus);
            }
        }
        
        try {
            logger.debug(" Hessian返回结果的消息id：" + result.getSysHeader().getMsgId() + "-技术报文头："
                    + DataSerialJSon.fromObject(result.getSysHeader()));
            logger.debug(" Hessian返回结果的消息id：" + result.getSysHeader().getMsgId() + "-技术报文体：" + DataSerialJSon.fromObject(result.getResponse()));
        } catch (Exception e3) {
            e3.printStackTrace();
            logger.debug("解析调用后报文的JSON字符串发生异常");
            throw new RuntimeException(e3);
        }
        return result;
    }
}

