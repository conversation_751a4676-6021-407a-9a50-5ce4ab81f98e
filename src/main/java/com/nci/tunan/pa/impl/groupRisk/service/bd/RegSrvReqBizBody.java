package com.nci.tunan.pa.impl.groupRisk.service.bd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>RegSrvReqBizBody complex type�� Java �ࡣ
 * 
 * <p>����ģʽƬ��ָ�����ڴ����е�Ԥ�����ݡ�
 * 
 * <pre>
 * &lt;complexType name="RegSrvReqBizBody"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="InputData" type="{http://www.newchinalife.com/service/bd}InputData"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RegSrvReqBizBody", propOrder = {
    "inputData"
})
public class RegSrvReqBizBody {

    @XmlElement(name = "InputData", required = true)
    protected InputData inputData;

    /**
     * ��ȡinputData���Ե�ֵ��
     * 
     * @return
     *     possible object is
     *     {@link InputData }
     *     
     */
    public InputData getInputData() {
        return inputData;
    }

    /**
     * ����inputData���Ե�ֵ��
     * 
     * @param value
     *     allowed object is
     *     {@link InputData }
     *     
     */
    public void setInputData(InputData value) {
        this.inputData = value;
    }

}
