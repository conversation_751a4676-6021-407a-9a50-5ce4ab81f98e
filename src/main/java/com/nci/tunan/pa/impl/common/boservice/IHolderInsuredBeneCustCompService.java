package com.nci.tunan.pa.impl.common.boservice;

import java.math.BigDecimal;
import java.util.List;

import com.nci.tunan.pa.interfaces.model.bo.BeneCustomerCompBO;
import com.nci.tunan.pa.interfaces.model.bo.CustomerBO;
import com.nci.tunan.pa.interfaces.model.bo.HolderCustomerCompBO;
import com.nci.tunan.pa.interfaces.model.bo.InsuredCustomerCompBO;
import com.nci.udmp.framework.bizservice.IBizService;

/**
 * @description 投保人、被保人、受益人信息（包含Customer所有信息）Service接口类
 * <AUTHOR> <EMAIL>
 * @date 2015-1-28 上午9:52:49
 * @.belongToModule PA-保单管理系统-查询投保人、被保人、受益人信息
 */
public interface IHolderInsuredBeneCustCompService extends IBizService  {

	/**
	 * @description 检索投保人信息（包含Customer所有信息）
	 * @version 0.1
	 * @title 
	 * <AUTHOR> <EMAIL>
	 * @date 2015-1-28 上午9:57:07
	 * @param holderCustomerCompBO 投保人信息BO
	 * @return
	 */
	public List<HolderCustomerCompBO> findHolderCustomerInfo(HolderCustomerCompBO holderCustomerCompBO);
	
	/**
	 * @description 检索被保人信息（包含Customer所有信息）
	 * @version 0.1
	 * @title 
	 * <AUTHOR> <EMAIL>
	 * @date 2015-1-28 上午9:57:17
	 * @param insuredCustomerCompBO 被保人信息BO
	 * @return
	 */
	public List<InsuredCustomerCompBO> findInsuredCustomerInfo(InsuredCustomerCompBO insuredCustomerCompBO);
	
	/**
	 * @description 检索受益人信息（包含Customer所有信息及受益人险种关系）
	 * @version 0.1
	 * @title 
	 * <AUTHOR> <EMAIL>
	 * @date 2015-1-28 上午9:57:27
	 * @param beneCustomerCompBO 受益人信息BO
	 * @return
	 */
	public List<BeneCustomerCompBO> findBeneCustomerInfo(BeneCustomerCompBO beneCustomerCompBO);
	
	/**
	 * @description 查询客户作为保险角色投保人、受益人或者被保人的policyId
	 * @version 0.1
	 * @title 
	 * <AUTHOR> <EMAIL>
	 * @date 2015-2-25 上午10:33:11
	 * @param customerBO 客户信息BO
	 * @return
	 */
	public List<BigDecimal> findPolicyIdsByCustomerBO(CustomerBO customerBO);
}
