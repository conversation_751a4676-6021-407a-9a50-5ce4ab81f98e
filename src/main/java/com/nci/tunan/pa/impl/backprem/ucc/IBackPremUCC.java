package com.nci.tunan.pa.impl.backprem.ucc;

import com.nci.tunan.pa.interfaces.serviceData.endcase.BackPremResData;
import com.nci.tunan.pa.interfaces.serviceData.endcase.EndCaseReqData;

/**
 * 
 * @description 理赔结案-出险日之后已领的生存金/年金查询/满期金
 * <AUTHOR> <EMAIL> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2017年8月16日 上午10:34:52
 */
public interface IBackPremUCC {
	/**
	 * 
	 * @description 查询出险日后发放的生存金年金满期金
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param inputData inputData
	 * @return
	 * @throws Exception
	 */
	public BackPremResData queryBackPrem(EndCaseReqData inputData) throws Exception;

}
