package com.nci.tunan.pa.impl.nbdatadeal.po;

import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BasePO;

/** 
 * @description PolicyPrintListPO对象
 * <AUTHOR> 
 * @date 2017-11-03 16:04:00  
 */
public class PolicyPrintListPO extends BasePO {
	/** 属性  --- java类型  --- oracle类型_数据长度_小数位长度_注释信息  */
	// isReissue  ---  BigDecimal  ---  NUMBER_1_0_null;
	// printCause  ---  String  ---  VARCHAR2_3_0_null;
	// printTime  ---  Date  ---  DATE_7_0_null;
	// policyCode  ---  String  ---  VARCHAR2_20_0_null;
	// printDate  ---  Date  ---  DATE_7_0_null;
	// listId  ---  BigDecimal  ---  NUMBER_16_0_null;

	public void setIsReissue(BigDecimal isReissue){
		setBigDecimal("is_reissue", isReissue);
	}

	public BigDecimal getIsReissue(){
		return getBigDecimal("is_reissue");
	}
	public void setPrintCause(String printCause){
		setString("print_cause", printCause);
	}

	public String getPrintCause(){
		return getString("print_cause");
	}
	public void setPrintTime(Date printTime){
		setUtilDate("print_time", printTime);
	}

	public Date getPrintTime(){
		return getUtilDate("print_time");
	}
	public void setPolicyCode(String policyCode){
		setString("policy_code", policyCode);
	}

	public String getPolicyCode(){
		return getString("policy_code");
	}
	public void setPrintDate(Date printDate){
		setUtilDate("print_date", printDate);
	}

	public Date getPrintDate(){
		return getUtilDate("print_date");
	}
	public void setListId(BigDecimal listId){
		setBigDecimal("list_id", listId);
	}

	public BigDecimal getListId(){
		return getBigDecimal("list_id");
	}

	@Override
	public String toString() {
		return "PolicyPrintListPO [" +
				"isReissue="+getIsReissue()+","+
				"printCause="+getPrintCause()+","+
				"printTime="+getPrintTime()+","+
				"policyCode="+getPolicyCode()+","+
				"printDate="+getPrintDate()+","+
				"listId="+getListId()+"]";
	}	
}
