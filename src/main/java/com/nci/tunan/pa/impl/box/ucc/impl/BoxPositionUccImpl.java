package com.nci.tunan.pa.impl.box.ucc.impl;

import com.nci.tunan.pa.impl.box.service.IBoxPositionService;
import com.nci.tunan.pa.impl.box.ucc.IBoxPositionUcc;
import com.nci.tunan.pa.interfaces.model.bo.BoxPositionBO;
import com.nci.tunan.pa.interfaces.model.vo.BoxPositionVO;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.bean.BeanUtils;
/**
 * 
 * @description BOX资金划拨数据ucc实现类
 * <AUTHOR> 
 * @date 2020年1月6日 下午7:32:59
 */
public class BoxPositionUccImpl implements IBoxPositionUcc {
    
	/**
	 * box 资金划拨service
	 */
	private IBoxPositionService boxPositionService;
	
	/**
     * 
     * @description box资金划拨确认
     * @version
     * @title
     * <AUTHOR>
     * @see com.nci.tunan.pa.impl.box.ucc.IBoxPositionUcc#saveTransferAmount(com.nci.tunan.pa.interfaces.model.vo.BoxPositionVO)
     * @param boxPositionVO
     */
	@Override
	public boolean saveTransferAmount(BoxPositionVO boxPositionVO) {
		BoxPositionBO boxPositionBO = BeanUtils.copyProperties(BoxPositionBO.class, boxPositionVO);
		return boxPositionService.saveTransferAmount(boxPositionBO);
	}
	
	/**
	 * 分页查询资金划拨确认
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.pa.impl.box.ucc.IBoxPositionUcc#queryReviewBoxPosition(com.nci.tunan.pa.interfaces.model.vo.BoxPositionVO, com.nci.udmp.framework.model.CurrentPage)
	 * @param boxPositionVO
	 * @param currentPage
	 * @return
	 */
	@Override
	public CurrentPage<BoxPositionVO> queryReviewBoxPosition(
			BoxPositionVO boxPositionVO,
			CurrentPage<BoxPositionVO> currentPage) {
		BoxPositionBO boxPositionBO = BeanUtils.copyProperties(BoxPositionBO.class, boxPositionVO);
		CurrentPage<BoxPositionBO>currentPageInfo = BeanUtils.copyCurrentPage(BoxPositionBO.class, currentPage);
	    return BeanUtils.copyCurrentPage(BoxPositionVO.class, boxPositionService.queryReviewBoxPosition(boxPositionBO,BeanUtils.copyCurrentPage(BoxPositionBO.class, currentPageInfo)));
	}
	
	/**
	 * 查询批处理计算结果
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.pa.impl.box.ucc.IBoxPositionUcc#queryBoxPositionBatchResult(com.nci.tunan.pa.interfaces.model.vo.BoxPositionVO)
	 * @param boxPositionVO
	 * @return
	 */
	@Override
	public BoxPositionVO queryBoxPositionBatchResult(BoxPositionVO boxPositionVO) {
		BoxPositionBO boxPositionBO = BeanUtils.copyProperties(BoxPositionBO.class, boxPositionVO);
		return BeanUtils.copyProperties(BoxPositionVO.class, boxPositionService.queryBoxPositionBatchResult(boxPositionBO));
	}

	/**
	 * 测算
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.pa.impl.box.ucc.IBoxPositionUcc#calculation(com.nci.tunan.pa.interfaces.model.vo.BoxPositionVO)
	 * @param boxPositionVO
	 * @return
	 */
	@Override
	public BoxPositionVO calculation(BoxPositionVO boxPositionVO) {
		BoxPositionBO boxPositionBO = BeanUtils.copyProperties(BoxPositionBO.class, boxPositionVO);
		return BeanUtils.copyProperties(BoxPositionVO.class, boxPositionService.calculation(boxPositionBO));
	}

	/**
	 * 保存资金划拨录入
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.pa.impl.box.ucc.IBoxPositionUcc#saveBoxPositionEnter(com.nci.tunan.pa.interfaces.model.vo.BoxPositionVO)
	 * @param boxPositionVO
	 * @return
	 */
	@Override
	public BoxPositionVO saveBoxPositionEnter(BoxPositionVO boxPositionVO) {
		BoxPositionBO boxPositionBO = BeanUtils.copyProperties(BoxPositionBO.class, boxPositionVO);
		return BeanUtils.copyProperties(BoxPositionVO.class, boxPositionService.saveBoxPositionEnter(boxPositionBO));
	}
	
	public IBoxPositionService getBoxPositionService() {
		return boxPositionService;
	}

	public void setBoxPositionService(IBoxPositionService boxPositionService) {
		this.boxPositionService = boxPositionService;
	}
}
