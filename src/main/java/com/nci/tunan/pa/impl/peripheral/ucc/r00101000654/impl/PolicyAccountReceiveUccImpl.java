package com.nci.tunan.pa.impl.peripheral.ucc.r00101000654.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import com.nci.tunan.cs.common.Constants;
import com.nci.tunan.cs.impl.peripheral.service.common.ICsEndorseForOutterService;
import com.nci.tunan.pa.impl.peripheral.service.r00101000654.IPolicyAccountReceiveService;
import com.nci.tunan.pa.impl.peripheral.ucc.r00101000654.IPolicyAccountReceiveUcc;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000654.vo.Input;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000654.vo.Output;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000654.vo.Result;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.udmp.component.serviceinvoke.util.CommonHeaderDeal;

/**
 * @description 客户账户领取查询接口实现类
 * <AUTHOR> 
 * @.belongToModule PA-保单管理系统-客户账户领取查询接口
 * @date 2017年12月4日 下午3:21:54
 */
public class PolicyAccountReceiveUccImpl implements IPolicyAccountReceiveUcc {
	/**
	 * @Fields policyAccountReceiveService ： 客户账户领取查询
	 */
	private  IPolicyAccountReceiveService  policyAccountReceiveService;
	
	/**
	 * @Fields csEndorseForOutterService ： 保全外围公共服务Service
	 */
	 @Autowired
	 @Qualifier("PA_csEndorseForOutterService")
	 private ICsEndorseForOutterService csEndorseForOutterService;
	 
	public IPolicyAccountReceiveService getPolicyAccountReceiveService() {
		return policyAccountReceiveService;
	}

    public void setPolicyAccountReceiveService(
			IPolicyAccountReceiveService policyAccountReceiveService) {
		this.policyAccountReceiveService = policyAccountReceiveService;
	}

 /**
 * 
 * @description 客户账号领取查询
 * @version
 * @title
 * <AUTHOR>
 * @see com.nci.tunan.pa.impl.peripheral.ucc.r00101000654.IPolicyAccountReceiveUcc#queryPolicyAccountReceive(com.nci.tunan.pa.interfaces.peripheral.exports.r00101000654.vo.Input)
 * @param inputData 客户账户领取查询接口入参对象
 * @return
 */
	@Override
	public Output queryPolicyAccountReceive(Input inputData) {
		Output  outputData  = new Output();
		SysHeader sysHeader = CommonHeaderDeal.getSYSHEADERTHREAD();
		if (inputData.getContNo()==null||"".equals(inputData.getContNo())) {
			sysHeader.setBizResCd("0001");//@invalid 业务响应码赋值失败    
			sysHeader.setBizResText("保单号为空");//@invalid 失败描述
			CommonHeaderDeal.setSYSHEADERTHREAD(sysHeader);
			Result result=new Result();
			result.setResultCode("0001");
			result.setResultMsg("保单号为空");
			outputData.setResult(result);
			return outputData;
		}
		//1、保单下主险为非身故理赔终止，且保单下849险种为“有效”状态时，BizResText”返回“主险已终止，不能操作该保全项目。”
		String flag = csEndorseForOutterService.cheak849Rules(inputData.getContNo());
		if(!Constants.SUCCESS.equals(flag)){
			Output  outData  = new Output();
			sysHeader.setBizResCd("0001");//@invalid 业务响应码赋值失败
			sysHeader.setBizResText("主险已终止，不能操作该保全项目。");//@invalid 失败描述
			CommonHeaderDeal.setSYSHEADERTHREAD(sysHeader);
			Result result=new Result();
			result.setResultCode("0001");
			result.setResultMsg("主险已终止，不能操作该保全项目。");
			outData.setResult(result);
			return outData;
		}
		//2、客户账号领取查询方法
		outputData = policyAccountReceiveService.queryPolicyAccountReceive(inputData);
		
		return outputData;
	}

}
