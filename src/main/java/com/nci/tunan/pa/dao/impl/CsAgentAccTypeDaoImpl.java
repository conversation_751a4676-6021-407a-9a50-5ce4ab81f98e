package com.nci.tunan.pa.dao.impl;

import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.pa.interfaces.model.po.CsAgentAccTypePO;
import com.nci.tunan.pa.dao.ICsAgentAccTypeDao;


/** 
 * @description CsAgentAccTypeDaoImpl实现类
 * <AUTHOR> 
 * @date 2025-06-09 11:42:01  
 */
public class CsAgentAccTypeDaoImpl extends BaseDaoImpl implements ICsAgentAccTypeDao {
	/**
	 * @Fields logger : 日志工具
	 */
	private static Logger logger = LoggerFactory.getLogger();

	/**
	 * @description 增加数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAccTypePO
	 * 对象
	 * @return CsAgentAccTypePO 添加结果
	 */
	public CsAgentAccTypePO addCsAgentAccType(CsAgentAccTypePO csAgentAccTypePO) {
		logger.debug("<======CsAgentAccTypeDaoImpl--addCsAgentAccType======>");
		return createObject("addCsAgentAccType", csAgentAccTypePO);
	}

	/**
	 * @description 删除数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAccTypePO
	 * 对象
	 * @return boolean 删除是否成功
	 */
	public boolean deleteCsAgentAccType(CsAgentAccTypePO csAgentAccTypePO) {
		logger.debug("<======CsAgentAccTypeDaoImpl--deleteCsAgentAccType======>");
		return deleteObject("deleteCsAgentAccType", csAgentAccTypePO);
	}

	/**
	 * @description 修改数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAccTypePO
	 * 对象
	 * @return CsAgentAccTypePO 修改结果
	 */
	public CsAgentAccTypePO updateCsAgentAccType(CsAgentAccTypePO csAgentAccTypePO) {
		logger.debug("<======CsAgentAccTypeDaoImpl--updateCsAgentAccType======>");
		return updateObject("updateCsAgentAccType", csAgentAccTypePO);
	}

	/**
	 * @description 查询单条数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAccTypePO
	 * 对象
	 * @return CsAgentAccTypePO 查询结果对象
	 */
	public CsAgentAccTypePO findCsAgentAccType(CsAgentAccTypePO csAgentAccTypePO) {
		logger.debug("<======CsAgentAccTypeDaoImpl--findCsAgentAccType======>");
		return findObject("findCsAgentAccType", csAgentAccTypePO);
	}

	/**
	 * @description 查询所有数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAccTypePO
	 * 对象
	 * @return List<CsAgentAccTypePO> 查询结果List
	 */
	public List<CsAgentAccTypePO> findAllCsAgentAccType(CsAgentAccTypePO csAgentAccTypePO) {
		logger.debug("<======CsAgentAccTypeDaoImpl--findAllCsAgentAccType======>");
		return findAll("findAllCsAgentAccType", csAgentAccTypePO);
	}

	/**
	 * @description 查询数据条数
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAccTypePO
	 * 对象
	 * @return int 查询结果条数
	 */
	public int findCsAgentAccTypeTotal(CsAgentAccTypePO csAgentAccTypePO) {
		logger.debug("<======CsAgentAccTypeDaoImpl--findCsAgentAccTypeTotal======>");
		return findCount("findCsAgentAccTypeTotal", csAgentAccTypePO);
	}

	/**
	 * @description 分页查询数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param currentPage
	 * 当前页对象
	 * @return CurrentPage<CsAgentAccTypePO> 查询结果的当前页对象
	 */
	public CurrentPage<CsAgentAccTypePO> queryCsAgentAccTypeForPage(CsAgentAccTypePO csAgentAccTypePO,
			CurrentPage<CsAgentAccTypePO> currentPage) {
		logger.debug("<======CsAgentAccTypeDaoImpl--queryCsAgentAccTypeForPage======>");
		currentPage.setParamObject(csAgentAccTypePO);
		return queryForPage("findCsAgentAccTypeTotal", "queryCsAgentAccTypeForPage", currentPage);
	}

	/**
	 * @description 批量增加数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAccTypePOList
	 * 对象列表
	 * @return boolean 批量添加是否成功
	 */
	public boolean batchSaveCsAgentAccType(List<CsAgentAccTypePO> csAgentAccTypePOList) {
		logger.debug("<======CsAgentAccTypeDaoImpl--batchSaveCsAgentAccType======>");
		return batchSave("addCsAgentAccType", csAgentAccTypePOList);
	}

	/**
	 * @description 批量修改数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAccTypePOList
	 * 对象列表
	 * @return boolean 批量修改是否成功
	 */
	public boolean batchUpdateCsAgentAccType(List<CsAgentAccTypePO> csAgentAccTypePOList) {
		logger.debug("<======CsAgentAccTypeDaoImpl--batchUpdateCsAgentAccType======>");
		return batchUpdate("updateCsAgentAccType", csAgentAccTypePOList);
	}

	/**
	 * @description 批量删除数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAccTypePOList
	 * 对象列表
	 * @return boolean 批量删除是否成功
	 */
	public boolean batchDeleteCsAgentAccType(List<CsAgentAccTypePO> csAgentAccTypePOList) {
		logger.debug("<======CsAgentAccTypeDaoImpl--batchDeleteCsAgentAccType======>");
		return batchDelete("deleteCsAgentAccType", csAgentAccTypePOList);
	}

	/**
	 * @description 查询所有数据 ，重新组装为map
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param csAgentAccTypePO
	 * 对象
	 * @return List<Map<String, Object>> 查询结果存放到map中
	 */
	public List<Map<String, Object>> findAllMapCsAgentAccType(CsAgentAccTypePO csAgentAccTypePO) {
		logger.debug("<======CsAgentAccTypeDaoImpl--findAllMapCsAgentAccType======>");
		return findAllMap("findAllMapCsAgentAccType", csAgentAccTypePO);
	}
}
