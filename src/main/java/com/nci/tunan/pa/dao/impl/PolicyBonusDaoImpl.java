package com.nci.tunan.pa.dao.impl;

import java.util.List;

import org.slf4j.Logger;

import com.nci.tunan.pa.dao.IPolicyBonusDao;
import com.nci.tunan.pa.interfaces.model.po.ContractBounsPo;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.util.logging.LoggerFactory;

/**
 * 
 * @description 保单红利dao实现类
 * <AUTHOR> <EMAIL> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统-保单红利dao实现类
 * @date 2017年12月6日 下午7:21:20
 */
public class PolicyBonusDaoImpl extends BaseDaoImpl implements IPolicyBonusDao {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
	  * 
	  * @description 保单红利查询
	  * @version
	  * @title
	  * <AUTHOR>
	  * @see com.nci.tunan.pa.dao.IPolicyBonusDao#findPolicyBoundsByPolicyCode(com.nci.tunan.pa.interfaces.model.po.ContractBounsPo)
	  * @param contractBounsPo 保单红利对象
	  * @return
	  */
	@Override
	public List<ContractBounsPo> findPolicyBoundsByPolicyCode(
			ContractBounsPo contractBounsPo) {
		logger.debug("<======PolicyBonusDaoImpl--findPolicyBoundsByPolicyCode======>");
		return findAll("findPolicyBoundsByPolicyCode",  contractBounsPo) ;
	}

}
