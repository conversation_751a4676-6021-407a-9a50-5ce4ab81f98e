package com.nci.tunan.pa.dao.impl;


import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.pa.dao.IRenewChangeDao;
import com.nci.tunan.pa.interfaces.model.po.EnNextDateCfgPO;
import com.nci.tunan.pa.interfaces.model.po.RenewChangePO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;


/**
 * 
 * @description 续保险种变更Dao实现类
 * <AUTHOR> 
 * @date 2019-12-16 下午3:46:44 
 * @.belongToModule PA-保单管理系统-续保险种变更Dao实现类
 */
public class RenewChangeDaoImpl  extends BaseDaoImpl  implements IRenewChangeDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加续保险种变更信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return RenewChangePO 添加结果
     */
	 public RenewChangePO addRenewChange(RenewChangePO renewChangePO) {
	 	 logger.debug("<======RenewChangeDaoImpl--addRenewChange======>");
		 return createObject("addRenewChange",  renewChangePO) ;
	 }
    
     /**
     * @description 删除续保险种变更信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteRenewChange(RenewChangePO renewChangePO) {
 	 	 logger.debug("<======RenewChangeDaoImpl--deleteRenewChange======>");
		 return deleteObject("deleteRenewChange",  renewChangePO) ;
	 }

	 /**
     * @description 修改续保险种变更信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return RenewChangePO 修改结果
     */
 	 public RenewChangePO updateRenewChange(RenewChangePO renewChangePO) {
 	 	 logger.debug("<======RenewChangeDaoImpl--updateRenewChange======>");
		 return updateObject("updateRenewChange",  renewChangePO) ;
 	 }	

     /**
     * @description 查询续保险种变更信息单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return RenewChangePO 查询结果续保险种变更对象
     */
	 public RenewChangePO findRenewChange(RenewChangePO renewChangePO) {
	  	logger.debug("<======RenewChangeDaoImpl--findRenewChange======>");
		return findObject("com.nci.tunan.pa.dao.impl.IRenewChangeDao.PA_findRenewChange",  renewChangePO) ;
	 }
	
	 /**
     * @description 查询续保险种变更信息所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return List<RenewChangePO> 查询结果List
     */
	 public List<RenewChangePO> findAllRenewChange(RenewChangePO renewChangePO) {
	 	logger.debug("<======RenewChangeDaoImpl--findAllRenewChange======>");
		return findAll("findAllRenewChange",  renewChangePO) ;
	 }
	 
	 /**
     * @description 查询所有续保险种变更数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return List<RenewChangePO> 查询结果List
     */
	 public List<RenewChangePO> findAllRenewChanges(RenewChangePO renewChangePO) {
	 	logger.debug("<======RenewChangeDaoImpl--findAllRenewChanges======>");
		return findAll("findAllRenewChanges",  renewChangePO) ;
	 }
	
	 /**
     * @description 查询续保险种变更信息数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return int 查询结果续保险种变更对象条数
     */
	 public int findRenewChangeTotal(RenewChangePO renewChangePO) {
	 	logger.debug("<======RenewChangeDaoImpl--findRenewChangeTotal======>");
		return findCount("findRenewChangeTotal",  renewChangePO) ;
	 }
	 
	 /**
     * @description 根据保单ID查询续保险种变更信息
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return renewChangePO 查询结果续保险种变更对象
     */
	 public RenewChangePO findLastRenewChangeByPolicyId(RenewChangePO renewChangePO) {
		logger.debug("<======RenewChangeDaoImpl--findLastRenewChangeByPolicyId======>");
		return findObject("findLastRenewChangeByPolicyId", renewChangePO);
	 }
	 
	 /**
     * @description 根据日期查询续保险种变更信息
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return renewChangePO 查询结果续保险种变更对象
     */
	 public RenewChangePO findLastRenewChangeByValDate(RenewChangePO renewChangePO) {
		logger.debug("<======RenewChangeDaoImpl--findLastRenewChangeByValDate======>");
		return findObject("findLastRenewChangeByValDate", renewChangePO);
	 }
	 
	 /**
     * @description 分页查询续保险种变更信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页续保险种变更对象
     * @return CurrentPage<RenewChangePO> 查询结果的当前页续保险种变更对象
     */
	 public CurrentPage<RenewChangePO> queryRenewChangeForPage(RenewChangePO renewChangePO, CurrentPage<RenewChangePO> currentPage) {
		logger.debug("<======RenewChangeDaoImpl--queryRenewChangeForPage======>");
		currentPage.setParamObject(renewChangePO);
		return queryForPage("findRenewChangeTotal", "queryRenewChangeForPage",  currentPage) ;
	 }
	
	 /**
     * @description 批量增加续保险种变更信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePOList 续保险种变更对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveRenewChange(List<RenewChangePO> renewChangePOList) {
	 	logger.debug("<======RenewChangeDaoImpl--batchSaveRenewChange======>");
		return batchSave("addRenewChange", renewChangePOList) ;
	 }
	
	 /**
     * @description 批量修改续保险种变更信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePOList 续保险种变更对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateRenewChange(List<RenewChangePO> renewChangePOList) {
	 	logger.debug("<======RenewChangeDaoImpl--batchUpdateRenewChange======>");
		return batchUpdate("updateRenewChange", renewChangePOList) ;
	 }
	
	 /**
     * @description 批量删除续保险种变更信息数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePOList 续保险种变更对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteRenewChange(List<RenewChangePO> renewChangePOList) {
	 	logger.debug("<======RenewChangeDaoImpl--batchDeleteRenewChange======>");
		return batchDelete("deleteRenewChange", renewChangePOList) ;
	 }
	
	 /**
     * @description 查询续保险种变更信息所有数据 ,重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapRenewChange(RenewChangePO renewChangePO) {
	 	logger.debug("<======RenewChangeDaoImpl--findAllMapRenewChange======>");
		return findAllMap("findAllMapRenewChange", renewChangePO) ;
	 }
	 
	 /**
     * @description 保全操作任务状态查询接口
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return List<RenewChangePO> 查询结果List
     */
	 public List<RenewChangePO> findListByContNo_renewc(RenewChangePO renewChangePO) {
		 	logger.debug("<======RenewChangeDaoImpl--findAllByContNo======>");
			return findAll("findListByContNo_renewc",  renewChangePO) ;
	 }
	 
	 /**
     * @description 查询续保险种变更信息最新的一条记录
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return RenewChangePO 查询结果续保险种变更对象
     */
	 public RenewChangePO findRenewChangeByBusiItemId(RenewChangePO renewChangePO) {
	  	logger.debug("<======RenewChangeDaoImpl--findRenewChangeByBusiItemId======>");
		return findObject("findRenewChangeByBusiItemId",  renewChangePO) ;
	 }
	 
     /**
     * @description 查询续保险种转换保全申请方式
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return RenewChangePO 查询结果续保险种变更对象
     */
	 public List<RenewChangePO> queryServiceType(RenewChangePO renewChangePO) {
	  	logger.debug("<======RenewChangeDaoImpl--queryServiceType======>");
		return findAll("com.nci.tunan.pa.dao.impl.IRenewChangeDao.PA_queryServiceType",  renewChangePO) ;
	 }
	 /**
     * @description 查询最新的一条记录续期回退使用
     * @version
     * @title
     * <AUTHOR>
     * @param renewChangePO 续保险种变更对象
     * @return RenewChangePO 查询结果续保险种变更对象
     */
	 public RenewChangePO findRenewChangeByBusiItemIdForXQ(RenewChangePO renewChangePO) { 
	  	logger.debug("<======RenewChangeDaoImpl--findRenewChangeByBusiItemIdForXQ======>");
		return findObject("findRenewChangeByBusiItemIdForXQ",  renewChangePO) ;
	 }


	public EnNextDateCfgPO findAllNewRenewalProcess(
			EnNextDateCfgPO enNextDateCfgPO) {
		logger.debug("<======RenewChangeDaoImpl--findAllNewRenewalProcess======>");
		return findObject("findAllNewRenewalProcess",  enNextDateCfgPO) ;
	}

	@Override
	public List<RenewChangePO> findAllRenewChangeInfo(RenewChangePO renewChangePO) {
		logger.debug("<======RenewChangeDaoImpl--findAllRenewChangeInfo======>");
		return findAll("findAllRenewChangeInfo",  renewChangePO) ;
	}	 
}
