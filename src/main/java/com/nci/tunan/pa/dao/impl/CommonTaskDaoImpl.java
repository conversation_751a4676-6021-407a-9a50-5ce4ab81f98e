package com.nci.tunan.pa.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.pa.dao.ICommonTaskDao;
import com.nci.tunan.pa.interfaces.model.po.CommonTaskPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;


/**
 * 
 * @description 任务控制接口实现类 
 * <AUTHOR> 
 * @.belongToModule PA-保单管理系统-任务控制接口
 * @date 2019-12-17 下午3:25:27 
 */
public class CommonTaskDaoImpl  extends BaseDaoImpl  implements ICommonTaskDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加任务控制表数据
     * @version
     * @title
     * <AUTHOR>
     * @param commonTaskPO 任务控制接口入参对象
     * @return CommonTaskPO 添加结果
     */
	 public CommonTaskPO addCommonTask(CommonTaskPO commonTaskPO) {
	 	 logger.debug("<======CommonTaskDaoImpl--addCommonTask======>");
		 return createObject("com.nci.tunan.pa.dao.ICommonTaskDao.addCommonTask",  commonTaskPO) ;
	 }
    
     /**
     * @description 删除任务控制表数据
     * @version
     * @title
     * <AUTHOR>
     * @param commonTaskPO 任务控制接口入参对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteCommonTask(CommonTaskPO commonTaskPO) {
 	 	 logger.debug("<======CommonTaskDaoImpl--deleteCommonTask======>");
		 return deleteObject("com.nci.tunan.pa.dao.ICommonTaskDao.deleteCommonTask",  commonTaskPO) ;
	 }

	/**
     * @description 修改任务控制表数据
     * @version
     * @title
     * <AUTHOR>
     * @param commonTaskPO 任务控制接口入参对象
     * @return CommonTaskPO 修改结果
     */
 	 public CommonTaskPO updateCommonTask(CommonTaskPO commonTaskPO) {
 	 	 logger.debug("<======CommonTaskDaoImpl--updateCommonTask======>");
		 return updateObject("com.nci.tunan.pa.dao.ICommonTaskDao.updateCommonTask",  commonTaskPO) ;
 	 }	

    /**
     * @description 查询任务控制表单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param commonTaskPO 任务控制接口入参对象
     * @return CommonTaskPO 查询结果对象
     */
	 public CommonTaskPO findCommonTask(CommonTaskPO commonTaskPO) {
	  	logger.debug("<======CommonTaskDaoImpl--findCommonTask======>");
		return findObject("com.nci.tunan.pa.dao.ICommonTaskDao.findCommonTask",  commonTaskPO) ;
	 }
	
	 /**
     * @description 查询任务控制表所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param commonTaskPO 任务控制接口入参对象
     * @return List<CommonTaskPO> 查询结果List
     */
	 public List<CommonTaskPO> findAllCommonTask(CommonTaskPO commonTaskPO) {
	 	logger.debug("<======CommonTaskDaoImpl--PA_findAllCommonTask======>");
		return findAll("com.nci.tunan.pa.dao.ICommonTaskDao.PA_findAllCommonTask",  commonTaskPO) ;
	 }
	
	 /**
     * @description 查询任务控制表数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param commonTaskPO 任务控制接口入参对象
     * @return int 查询结果条数
     */
	 public int findCommonTaskTotal(CommonTaskPO commonTaskPO) {
	 	logger.debug("<======CommonTaskDaoImpl--findCommonTaskTotal======>");
		return findCount("com.nci.tunan.pa.dao.ICommonTaskDao.findCommonTaskTotal",  commonTaskPO) ;
	 }
	
	 /**
     * @description 分页查询任务控制表数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<CommonTaskPO> 查询结果的当前页对象
     */
	 public CurrentPage<CommonTaskPO> queryCommonTaskForPage(CommonTaskPO commonTaskPO, CurrentPage<CommonTaskPO> currentPage) {
		logger.debug("<======CommonTaskDaoImpl--queryCommonTaskForPage======>");
		currentPage.setParamObject(commonTaskPO);
		return queryForPage("com.nci.tunan.pa.dao.ICommonTaskDao.findCommonTaskTotal", "queryCommonTaskForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加任务控制表数据
     * @version
     * @title
     * <AUTHOR>
     * @param commonTaskPOList 任务控制接口入参对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCommonTask(List<CommonTaskPO> commonTaskPOList) {
	 	logger.debug("<======CommonTaskDaoImpl--batchSaveCommonTask======>");
		return batchSave("com.nci.tunan.pa.dao.ICommonTaskDao.addCommonTask", commonTaskPOList) ;
	 }
	
	 /**
     * @description 批量修改任务控制表数据
     * @version
     * @title
     * <AUTHOR>
     * @param commonTaskPOList 任务控制接口入参对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCommonTask(List<CommonTaskPO> commonTaskPOList) {
	 	logger.debug("<======CommonTaskDaoImpl--batchUpdateCommonTask======>");
		return batchUpdate("com.nci.tunan.pa.dao.ICommonTaskDao.updateCommonTask", commonTaskPOList) ;
	 }
	
	/**
     * @description 批量删除任务控制表数据
     * @version
     * @title
     * <AUTHOR>
     * @param commonTaskPOList 任务控制接口入参对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCommonTask(List<CommonTaskPO> commonTaskPOList) {
	 	logger.debug("<======CommonTaskDaoImpl--batchDeleteCommonTask======>");
		return batchDelete("com.nci.tunan.pa.dao.ICommonTaskDao.deleteCommonTask", commonTaskPOList) ;
	 }
	
	 /**
     * @description 查询任务控制表所有数据重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param commonTaskPO 任务控制接口入参对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapCommonTask(CommonTaskPO commonTaskPO) {
	 	logger.debug("<======CommonTaskDaoImpl--findAllMapCommonTask======>");
		return findAllMap("com.nci.tunan.pa.dao.ICommonTaskDao.findAllMapCommonTask", commonTaskPO) ;
	 }
}
