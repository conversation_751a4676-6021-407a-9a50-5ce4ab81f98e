package com.nci.tunan.pa.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.pa.dao.IBusinessProductDao;
import com.nci.tunan.pa.interfaces.model.po.BusinessProductPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description BusinessProductDaoImpl业务产品实现类
 * <AUTHOR> <EMAIL>
 * @date 2015-01-27 17:44:40  
 * @.belongToModule PA-保单管理系统-业务产品实现类
 */
public class BusinessProductDaoImpl  extends BaseDaoImpl  implements IBusinessProductDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加业务产品表数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param businessProductPO 业务产品对象
     * @return CsBusinessProductPO 添加结果
     */
	 public BusinessProductPO addBusinessProduct(BusinessProductPO businessProductPO) {
	 	 logger.debug("<======CsBusinessProductDaoImpl--addBusinessProduct======>");
		 return createObject("PA_addBusinessProduct",  businessProductPO) ;
	 }
    
     /**
     * @description 删除业务产品表数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param businessProductPO 业务产品对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteBusinessProduct(BusinessProductPO businessProductPO) {
 	 	 logger.debug("<======CsBusinessProductDaoImpl--deleteBusinessProduct======>");
		 return deleteObject("PA_deleteBusinessProduct",  businessProductPO) ;
	 }

	/**
     * @description 修改业务产品表数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param businessProductPO 业务产品对象
     * @return CsBusinessProductPO 修改结果
     */
 	 public BusinessProductPO updateBusinessProduct(BusinessProductPO businessProductPO) {
 	 	 logger.debug("<======CsBusinessProductDaoImpl--updateBusinessProduct======>");
		 return updateObject("PA_updateBusinessProduct",  businessProductPO) ;
 	 }	

    /**
     * @description 查询业务产品表单条数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param businessProductPO 业务产品对象
     * @return CsBusinessProductPO 查询结果对象
     */
	 public BusinessProductPO findBusinessProduct(BusinessProductPO businessProductPO) {
	  	logger.debug("<======CsBusinessProductDaoImpl--findBusinessProduct======>");
	  	businessProductPO.setString("SqlPrint", "1");
		return findObject("PA_findBusinessProduct",  businessProductPO) ;
	 }
	
	 /**
     * @description 查询业务产品表所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param businessProductPO 业务产品对象
     * @return List<CsBusinessProductPO> 查询结果List
     */
	 public List<BusinessProductPO> findAllBusinessProduct(BusinessProductPO businessProductPO) {
	 	logger.debug("<======CsBusinessProductDaoImpl--findAllBusinessProduct======>");
		return findAll("PA_findAllBusinessProduct",  businessProductPO) ;
	 }
	
	 /**
     * @description 查询业务产品表数据条数
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param businessProductPO 业务产品对象
     * @return int 查询结果条数
     */
	 public int findBusinessProductTotal(BusinessProductPO businessProductPO) {
	 	logger.debug("<======CsBusinessProductDaoImpl--findBusinessProductTotal======>");
		return findCount("PA_findBusinessProductTotal",  businessProductPO) ;
	 }
	
	 /**
     * @description 分页查询业务产品表数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param currentPage 当前页对象
     * @param businessProductPO 业务产品对象
     * @return CurrentPage<CsBusinessProductPO> 查询结果的当前页对象
     */
	 public CurrentPage<BusinessProductPO> queryBusinessProductForPage(BusinessProductPO businessProductPO, CurrentPage<BusinessProductPO> currentPage) {
		logger.debug("<======CsBusinessProductDaoImpl--queryBusinessProductForPage======>");
		currentPage.setParamObject(businessProductPO);
		return queryForPage("PA_findBusinessProductTotal", "PA_queryBusinessProductForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加业务产品表数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param businessProductPOList 业务产品对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveBusinessProduct(List<BusinessProductPO> businessProductPOList) {
	 	logger.debug("<======CsBusinessProductDaoImpl--batchSaveBusinessProduct======>");
		return batchSave("PA_addBusinessProduct", businessProductPOList) ;
	 }
	
	 /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param businessProductPOList 业务产品对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateBusinessProduct(List<BusinessProductPO> businessProductPOList) {
	 	logger.debug("<======CsBusinessProductDaoImpl--batchUpdateBusinessProduct======>");
		return batchUpdate("PA_updateBusinessProduct", businessProductPOList) ;
	 }
	
	/**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param businessProductPOList 业务产品对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteBusinessProduct(List<BusinessProductPO> businessProductPOList) {
	 	logger.debug("<======CsBusinessProductDaoImpl--batchDeleteBusinessProduct======>");
		return batchDelete("PA_deleteBusinessProduct", businessProductPOList) ;
	 }
	
	 /**
     * @description 查询业务产品表所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @param businessProductPO 业务产品对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapBusinessProduct(BusinessProductPO businessProductPO) {
	 	logger.debug("<======CsBusinessProductDaoImpl--findAllMapBusinessProduct======>");
		return findAllMap("PA_findAllMapBusinessProduct", businessProductPO) ;
	 }
    
	/**
	 * 
	 * @description 查询产品ID和是否银代新政签约产品
	 * @version V1.0.0
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.pa.dao.IBusinessProductDao#findBusiProdAndSignFlag(com.nci.tunan.pa.interfaces.model.po.BusinessProductPO)
	 * @param busiProdTempPO 业务产品对象
	 * @return BusinessProductPO 返回对象
	 */
    @Override
    public BusinessProductPO findBusiProdAndSignFlag(BusinessProductPO busiProdTempPO) {
        logger.debug("<======CsBusinessProductDaoImpl--findBusiProdAndSignFlag======>");
        return findObject("PA_findBusiProdAndSignFlag",  busiProdTempPO) ;
    }
    /**
     * 
     * @description 查询业务产品表所有数据
     * @version
     * @title
     * <AUTHOR> <EMAIL>
     * @see com.nci.tunan.pa.dao.IBusinessProductDao#findAllBusinessProductByProductId(com.nci.tunan.pa.interfaces.model.po.BusinessProductPO)
     * @param businessProductPO 业务产品对象
     * @return
     */
    @Override
	public List<BusinessProductPO> findAllBusinessProductByProductId(
			BusinessProductPO businessProductPO) {
		logger.debug("<======CsBusinessProductDaoImpl--findAllBusinessProductByProductId======>");
		return findAll("PA_findAllBusinessProductByProductId",  businessProductPO) ;
	}
	/**
	 * 
	 * @description 根据busiItemId查询是否为投连万能
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.pa.dao.IBusinessProductDao#findBusinessProductByBusiItemId(com.nci.tunan.pa.interfaces.model.po.BusinessProductPO)
	 * @param businessProductPO 业务产品对象
	 * @return
	 */
	@Override
	public BusinessProductPO findBusinessProductByBusiItemId(
			BusinessProductPO businessProductPO) {
		logger.info("<================BusinessProductDaoImpl====PA_findBusinessProductByBusiItemId=>");
		return findObject("PA_findBusinessProductByBusiItemId", businessProductPO);
	}
	/**
	 * 
	 * @description 根据保单号查询险种id
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.pa.dao.IBusinessProductDao#findBusinessProductByPolicyCode(com.nci.tunan.pa.interfaces.model.po.BusinessProductPO)
	 * @param businessProductPO 业务产品对象
	 * @return
	 */
	@Override
	public List<BusinessProductPO> findBusinessProductByPolicyCode(
			BusinessProductPO businessProductPO) {
		logger.info("<=====BusinessProductDaoImpl=====businessProduct.findBusinessProductByPolicyCode=>");
		return findAll("businessProduct.findBusinessProductByPolicyCode", businessProductPO);
	}
	/**
	 * 
	 * @description 根据保单号查询是否是长期险
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.pa.dao.IBusinessProductDao#findlongTermProductByPolicyCode(com.nci.tunan.pa.interfaces.model.po.BusinessProductPO)
	 * @param businessProductPO 业务产品对象
	 * @return
	 */
	@Override
	public List<BusinessProductPO> findlongTermProductByPolicyCode(
			BusinessProductPO businessProductPO) {
		logger.info("<=====BusinessProductDaoImpl=====businessProduct.findlongTermProductByPolicyCode=>");
		return findAll("businessProduct.findlongTermProductByPolicyCode", businessProductPO);
	}
	/**
	 * 
	 * @description 可续保转换险种清单列表
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.pa.dao.IBusinessProductDao#findBusinessProductByRiskCode(com.nci.tunan.pa.interfaces.model.po.BusinessProductPO)
	 * @param businessProductPO 业务产品对象
	 * @return
	 */
	@Override
	public BusinessProductPO findBusinessProductByRiskCode(
			BusinessProductPO businessProductPO) {
		logger.info("<================BusinessProductDaoImpl====findBusinessProductByRiskCode=>");
		return findObject("PA_findBusinessProductByRiskCode", businessProductPO);
	}

	/**
	 * 
	 * @description 新核心-接口需求-移动保全系统2.0-短期健康险新规应对需求（续保险种转换被保险人签字优化）
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.pa.dao.IBusinessProductDao#findDieInsuFlag(com.nci.tunan.pa.interfaces.model.po.BusinessProductPO)
	 * @param businessProductPO 业务产品对象
	 * @return
	 */
	@Override
	public List<BusinessProductPO> findDieInsuFlag(BusinessProductPO businessProductPO) {
		logger.info("<=====BusinessProductDaoImpl=====findDieInsuFlag=>");
		return findAll("PA_findDieInsuFlag", businessProductPO);
	}
	/**
	 * 
	 * @description 查询是否是短期健康险产品
	 * @version
	 * @title
	 * <AUTHOR>
	 * @see com.nci.tunan.pa.dao.IBusinessProductDao#queryHealthInsurance(com.nci.tunan.pa.interfaces.model.po.BusinessProductPO)
	 * @param businessProductPO 业务产品对象
	 * @return
	 */
	@Override
	public BusinessProductPO queryHealthInsurance(BusinessProductPO businessProductPO) {
		logger.debug("<================BusinessProductDaoImpl====queryHealthInsurance=>");
		return findObject("PA_queryHealthInsurance", businessProductPO);
	}

	/**
	 * 
	 * @description 查询产品配置项-计算生存金/年金领取标准时点为承保签单时点的险种
	 * @version
	 * @title
     * <AUTHOR> <EMAIL>
	 * @param businessProductPO 业务产品对象
	 * @return
	 */
	@Override
	public BusinessProductPO findProductAnnuityPaymentPlanType(BusinessProductPO businessProductPO) {
		logger.debug("<================BusinessProductDaoImpl====findProductAnnuityPaymentPlanType=>");
		return findObject("PA_findProductAnnuityPaymentPlanType", businessProductPO);
	}
	
	/**
	 * 
	 * @description 查询可独立承保附加险产品
	 * @version
	 * @title
     * <AUTHOR>
	 * @param businessProductPO 业务产品对象
	 * @return
	 */
	@Override
	public BusinessProductPO findProductRiskSingleInsureFlag(BusinessProductPO businessProductPO) {
		logger.debug("<================BusinessProductDaoImpl====findProductAnnuityPaymentPlanType=>");
		return findObject("PA_findProductRiskSingleInsureFlag", businessProductPO);
	}
	/**
	 * 
	 * @description 根据保单号查询业务产品信息
	 * @version
	 * @title
     * <AUTHOR>
	 * @param businessProductPO 业务产品对象
	 * @return
	 */
	@Override
	public List<BusinessProductPO> findProductInfos(BusinessProductPO businessProductPO) {
		logger.info("<=====BusinessProductDaoImpl=====findProductInfos=>");
		return findAll("PA_findProductInfos", businessProductPO);
	}
	/**
	 * @description 根据保单号查询险种信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @param businessProductPO
	 * @return
	 */
	@Override
	public List<BusinessProductPO> findBusiCodeAndName(
			BusinessProductPO businessProductPO) {
		logger.info("<=====BusinessProductDaoImpl=====findProductInfos=>");
		return findAll("PA_findBusiCodeAndName", businessProductPO);
	}

	
	/**
	 * @description 根据责任组代码查询主副险
	 * @version
	 * @title
	 * @<NAME_EMAIL>
	 * @param businessProductPO
	 * @return
	 */
	@Override
	public BusinessProductPO queryBusiProdMainFlagByPorductCode(
			BusinessProductPO businessProductPO) {
		logger.debug("<================BusinessProductDaoImpl====queryBusiProdMainFlagByPorductCode=>");
		return findObject("PA_queryBusiProdMainFlagByPorductCode", businessProductPO);
		
	}
	/**
	 * @description 查询保单下有效附加万能险
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param businessProductPO
	 * @return
	 */
	public List<BusinessProductPO> findYXWNByPolicyCode(BusinessProductPO businessProductPO){
		logger.debug("<================BusinessProductDaoImpl====findYXWNByPolicyCode=>");
		return findAll("findYXWNByPolicyCode", businessProductPO);
		
	}
	/**
	 * 
	 * @description 查询保单下主险产品信息
	 * @version
	 * @title
	 * <AUTHOR> <EMAIL>
	 * @see com.nci.tunan.pa.dao.IBusinessProductDao#queryBusiProdMainFlagByPorductCode(com.nci.tunan.pa.interfaces.model.po.BusinessProductPO)
	 * @param businessProductPO
	 * @return
	 */
	@Override
	public List<BusinessProductPO> queryMasterPorductCodeInfo(
			BusinessProductPO businessProductPO) {
		logger.debug("<================BusinessProductDaoImpl====queryMasterPorductCodeInfo=>");
		return findAll("PA_queryMasterPorductCodeInfo", businessProductPO);
		
	}
}
