package com.nci.tunan.pa.dao;


import com.nci.tunan.pa.interfaces.model.po.PolicyStatusTracePO;
import com.nci.udmp.framework.model.*;

import java.util.Map;
import java.util.List;

/** 
 * @description  保单状态轨迹信息表 IPolicyStatusTraceDao接口
 * <AUTHOR> 
 * @date 2022-05-24 10:13:16  
 */
 public interface IPolicyStatusTraceDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatusTracePO 对象
     * @return PolicyStatusTracePO 添加结果
     */
	 public PolicyStatusTracePO addPolicyStatusTrace(PolicyStatusTracePO policyStatusTracePO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatusTracePO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deletePolicyStatusTrace(PolicyStatusTracePO policyStatusTracePO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatusTracePO 对象
     * @return PolicyStatusTracePO 修改结果
     */
	 public PolicyStatusTracePO updatePolicyStatusTrace(PolicyStatusTracePO policyStatusTracePO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatusTracePO 对象
     * @return PolicyStatusTracePO 查询结果对象
     */
	 public PolicyStatusTracePO findPolicyStatusTrace(PolicyStatusTracePO policyStatusTracePO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatusTracePO 对象
     * @return List<PolicyStatusTracePO> 查询结果List
     */
	 public List<PolicyStatusTracePO> findAllPolicyStatusTrace(PolicyStatusTracePO policyStatusTracePO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatusTracePO 对象
     * @return int 查询结果条数
     */
	 public int findPolicyStatusTraceTotal(PolicyStatusTracePO policyStatusTracePO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<PolicyStatusTracePO> 查询结果的当前页对象
     */
	 public CurrentPage<PolicyStatusTracePO> queryPolicyStatusTraceForPage(PolicyStatusTracePO policyStatusTracePO, CurrentPage<PolicyStatusTracePO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatusTracePOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSavePolicyStatusTrace(List<PolicyStatusTracePO> policyStatusTracePOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatusTracePOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdatePolicyStatusTrace(List<PolicyStatusTracePO> policyStatusTracePOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatusTracePOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeletePolicyStatusTrace(List<PolicyStatusTracePO> policyStatusTracePOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param policyStatusTracePO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapPolicyStatusTrace(PolicyStatusTracePO policyStatusTracePO);
	 
	 /**
	  * 
	  * @description 查询一条最新的轨迹信息
	  * @version
	  * @title
	  * @<NAME_EMAIL>
	  * @param policyStatusTracePO
	  * @return
	  */
	 public PolicyStatusTracePO findNewPolicyStatusTraceListid(PolicyStatusTracePO policyStatusTracePO);
	 
 }
 