package com.nci.tunan.pa.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.pa.dao.ILogScopeDao;
import com.nci.tunan.pa.interfaces.model.po.LogScopePO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description 履历范围配置Dao实现类
 * <AUTHOR> 
 * @date 2016-06-23 14:09:51  
 * @.belongToModule PAS-保单管理系统-保单日志
 */
public class LogScopeDaoImpl  extends BaseDaoImpl  implements ILogScopeDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加履历范围配置表数据
     * @version
     * @title
     * <AUTHOR>
     * @param logScopePO 履历范围配置PO对象
     * @return LogScopePO 添加结果
     */
	 public LogScopePO addLogScope(LogScopePO logScopePO) {
	 	 logger.debug("<======LogScopeDaoImpl--addLogScope======>");
		 return createObject("PA_addLogScope",  logScopePO) ;
	 }
    
     /**
     * @description 删除履历范围配置表数据
     * @version
     * @title
     * <AUTHOR>
     * @param logScopePO 履历范围配置PO对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteLogScope(LogScopePO logScopePO) {
 	 	 logger.debug("<======LogScopeDaoImpl--deleteLogScope======>");
		 return deleteObject("PA_deleteLogScope",  logScopePO) ;
	 }

	/**
     * @description 修改履历范围配置表数据
     * @version
     * @title
     * <AUTHOR>
     * @param logScopePO 履历范围配置PO对象
     * @return LogScopePO 修改结果
     */
 	 public LogScopePO updateLogScope(LogScopePO logScopePO) {
 	 	 logger.debug("<======LogScopeDaoImpl--updateLogScope======>");
		 return updateObject("PA_updateLogScope",  logScopePO) ;
 	 }	

    /**
     * @description 查询履历范围配置表单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param logScopePO 履历范围配置PO对象
     * @return LogScopePO 查询结果对象
     */
	 public LogScopePO findLogScope(LogScopePO logScopePO) {
	  	logger.debug("<======LogScopeDaoImpl--findLogScope======>");
		return findObject("PA_findLogScope",  logScopePO) ;
	 }
	
	 /**
     * @description 查询履历范围配置表所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param logScopePO 履历范围配置PO对象
     * @return List<LogScopePO> 查询结果List
     */
	 public List<LogScopePO> findAllLogScope(LogScopePO logScopePO) {
	 	logger.debug("<======LogScopeDaoImpl--findAllLogScope======>");
	 	logScopePO.setString("SqlPrint", "1");
		return findAll("PA_findAllLogScope",  logScopePO) ;
	 }
	
	 /**
     * @description 查询履历范围配置表数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param logScopePO 履历范围配置PO对象
     * @return int 查询结果条数
     */
	 public int findLogScopeTotal(LogScopePO logScopePO) {
	 	logger.debug("<======LogScopeDaoImpl--findLogScopeTotal======>");
		return findCount("PA_findLogScopeTotal",  logScopePO) ;
	 }
	
	 /**
     * @description 分页查询履历范围配置表数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<LogScopePO> 查询结果的当前页对象
     */
	 public CurrentPage<LogScopePO> queryLogScopeForPage(LogScopePO logScopePO, CurrentPage<LogScopePO> currentPage) {
		logger.debug("<======LogScopeDaoImpl--queryLogScopeForPage======>");
		currentPage.setParamObject(logScopePO);
		return queryForPage("PA_findLogScopeTotal", "PA_queryLogScopeForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加履历范围配置表数据
     * @version
     * @title
     * <AUTHOR>
     * @param logScopePOList 履历范围配置PO对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveLogScope(List<LogScopePO> logScopePOList) {
	 	logger.debug("<======LogScopeDaoImpl--batchSaveLogScope======>");
		return batchSave("PA_addLogScope", logScopePOList) ;
	 }
	
	 /**
     * @description 批量修改履历范围配置表数据
     * @version
     * @title
     * <AUTHOR>
     * @param logScopePOList 履历范围配置PO对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateLogScope(List<LogScopePO> logScopePOList) {
	 	logger.debug("<======LogScopeDaoImpl--batchUpdateLogScope======>");
		return batchUpdate("PA_updateLogScope", logScopePOList) ;
	 }
	
	/**
     * @description 批量删除履历范围配置表数据
     * @version
     * @title
     * <AUTHOR>
     * @param logScopePOList 履历范围配置PO对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteLogScope(List<LogScopePO> logScopePOList) {
	 	logger.debug("<======LogScopeDaoImpl--batchDeleteLogScope======>");
		return batchDelete("PA_deleteLogScope", logScopePOList) ;
	 }
	
	 /**
     * @description 查询履历范围配置表所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param logScopePO 履历范围配置PO对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapLogScope(LogScopePO logScopePO) {
	 	logger.debug("<======LogScopeDaoImpl--findAllMapLogScope======>");
		return findAllMap("PA_findAllMapLogScope", logScopePO) ;
	 }
}
