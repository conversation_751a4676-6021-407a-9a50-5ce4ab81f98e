package com.nci.tunan.pa.dao.impl;

import com.nci.tunan.pa.dao.IPayerCxDao;
import com.nci.tunan.pa.interfaces.model.po.PayerCxPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/**
 * 
 * @description 保单付款人变更索引Dao实现类
 * <AUTHOR> <EMAIL> 
 * @date 2021年1月9日 下午5:41:41 
 * @.belongToModule PAS-保单管理系统-保单日志
 */
public class PayerCxDaoImpl  extends BaseDaoImpl  implements IPayerCxDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加保单付款人变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param payerCxPO 保单付款人变更索引表PO对象
     * @return PayerCxPO 添加结果
     */
	 public PayerCxPO addPayerCx(PayerCxPO payerCxPO) {
	 	 logger.debug("<======PayerCxDaoImpl--addPayerCx======>");
		 return createObject("PA_addPayerCx",  payerCxPO) ;
	 }
    
     /**
     * @description 删除保单付款人变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param payerCxPO 保单付款人变更索引表PO对象
     * @return boolean 删除是否成功
     */
 	 public boolean deletePayerCx(PayerCxPO payerCxPO) {
 	 	 logger.debug("<======PayerCxDaoImpl--deletePayerCx======>");
		 return deleteObject("PA_deletePayerCx",  payerCxPO) ;
	 }

	/**
     * @description 修改保单付款人变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param payerCxPO 保单付款人变更索引表PO对象
     * @return PayerCxPO 修改结果
     */
 	 public PayerCxPO updatePayerCx(PayerCxPO payerCxPO) {
 	 	 logger.debug("<======PayerCxDaoImpl--updatePayerCx======>");
		 return updateObject("PA_updatePayerCx",  payerCxPO) ;
 	 }	

    /**
     * @description 查询保单付款人变更索引表单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param payerCxPO 保单付款人变更索引表PO对象
     * @return PayerCxPO 查询结果对象
     */
	 public PayerCxPO findPayerCx(PayerCxPO payerCxPO) {
	  	logger.debug("<======PayerCxDaoImpl--findPayerCx======>");
		return findObject("PA_findPayerCx",  payerCxPO) ;
	 }
	
	 /**
     * @description 查询保单付款人变更索引表所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param payerCxPO 保单付款人变更索引表PO对象
     * @return List<PayerCxPO> 查询结果List
     */
	 public List<PayerCxPO> findAllPayerCx(PayerCxPO payerCxPO) {
	 	logger.debug("<======PayerCxDaoImpl--findAllPayerCx======>");
		return findAll("PA_findAllPayerCx",  payerCxPO) ;
	 }
	
	 /**
     * @description 查询保单付款人变更索引表数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param payerCxPO 保单付款人变更索引表PO对象
     * @return int 查询结果条数
     */
	 public int findPayerCxTotal(PayerCxPO payerCxPO) {
	 	logger.debug("<======PayerCxDaoImpl--findPayerCxTotal======>");
		return findCount("PA_findPayerCxTotal",  payerCxPO) ;
	 }
	
	 /**
     * @description 分页查询保单付款人变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<PayerCxPO> 查询结果的当前页对象
     */
	 public CurrentPage<PayerCxPO> queryPayerCxForPage(PayerCxPO payerCxPO, CurrentPage<PayerCxPO> currentPage) {
		logger.debug("<======PayerCxDaoImpl--queryPayerCxForPage======>");
		currentPage.setParamObject(payerCxPO);
		return queryForPage("PA_findPayerCxTotal", "PA_queryPayerCxForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加保单付款人变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param payerCxPOList 保单付款人变更索引表PO对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSavePayerCx(List<PayerCxPO> payerCxPOList) {
	 	logger.debug("<======PayerCxDaoImpl--batchSavePayerCx======>");
		return batchSave("PA_addPayerCx", payerCxPOList) ;
	 }
	
	 /**
     * @description 批量修改保单付款人变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param payerCxPOList 保单付款人变更索引表PO对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdatePayerCx(List<PayerCxPO> payerCxPOList) {
	 	logger.debug("<======PayerCxDaoImpl--batchUpdatePayerCx======>");
		return batchUpdate("PA_updatePayerCx", payerCxPOList) ;
	 }
	
	/**
     * @description 批量删除保单付款人变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param payerCxPOList 保单付款人变更索引表PO对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeletePayerCx(List<PayerCxPO> payerCxPOList) {
	 	logger.debug("<======PayerCxDaoImpl--batchDeletePayerCx======>");
		return batchDelete("PA_deletePayerCx", payerCxPOList) ;
	 }
	
	 /**
     * @description 查询保单付款人变更索引表所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param payerCxPO 保单付款人变更索引表PO对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapPayerCx(PayerCxPO payerCxPO) {
	 	logger.debug("<======PayerCxDaoImpl--findAllMapPayerCx======>");
		return findAllMap("PA_findAllMapPayerCx", payerCxPO) ;
	 }
}
