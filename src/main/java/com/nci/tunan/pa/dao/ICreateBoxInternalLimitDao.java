package com.nci.tunan.pa.dao;

import java.util.List;

import com.nci.tunan.pa.interfaces.model.po.BoxSuperviseLimitPO;
import com.nci.tunan.pa.interfaces.model.po.UserPO;
/**
 * 
 * @description BOX内部限额录入Dao
 * <AUTHOR> 
 * @.belongToModule PA_保单管理子系统
 * @date 2020年11月23日 上午10:08:36
 */
public interface ICreateBoxInternalLimitDao {
	
	/**
	 * 查询投资账户信息
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param boxInternalLimitPO
	 * @return
	 */
	public BoxSuperviseLimitPO findBOXSuperviseLimitByFundCode(BoxSuperviseLimitPO boxSuperviseLimitPO);

	/**
	 * 查询有相应菜单权限的用户
	 * @description
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param userPO
	 * @return
	 */
	public List<UserPO> queryUserByPermission(UserPO userPO);
}
