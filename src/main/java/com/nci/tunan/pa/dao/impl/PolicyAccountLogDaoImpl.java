package com.nci.tunan.pa.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.pa.dao.IPolicyAccountLogDao;
import com.nci.tunan.pa.interfaces.model.po.PolicyAccountLogPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;


/**
 * 
 * @description IPolicyAccountLogDao 保单账户基本信息变更履历实现类
 * <AUTHOR> 
 * @date 2019-12-18 上午10:13:55 
 * @.belongToModule PA-保单管理系统-保单账户基本信息变更履历实现类
 */
public class PolicyAccountLogDaoImpl  extends BaseDaoImpl  implements IPolicyAccountLogDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加保单账户基本信息变更履历表数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPO 保单账户基本信息变更履历表对象
     * @return PolicyAccountLogPO 添加结果
     */
	 public PolicyAccountLogPO addPolicyAccountLog(PolicyAccountLogPO policyAccountLogPO) {
	 	 logger.debug("<======PolicyAccountLogDaoImpl--addPolicyAccountLog======>");
		 return createObject("PA_addPolicyAccountLog",  policyAccountLogPO) ;
	 }
    
     /**
     * @description 删除保单账户基本信息变更履历表数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPO 保单账户基本信息变更履历表对象
     * @return boolean 删除是否成功
     */
 	 public boolean deletePolicyAccountLog(PolicyAccountLogPO policyAccountLogPO) {
 	 	 logger.debug("<======PolicyAccountLogDaoImpl--deletePolicyAccountLog======>");
		 return deleteObject("PA_deletePolicyAccountLog",  policyAccountLogPO) ;
	 }

	/**
     * @description 修改保单账户基本信息变更履历表数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPO 保单账户基本信息变更履历表对象
     * @return PolicyAccountLogPO 修改结果
     */
 	 public PolicyAccountLogPO updatePolicyAccountLog(PolicyAccountLogPO policyAccountLogPO) {
 	 	 logger.debug("<======PolicyAccountLogDaoImpl--updatePolicyAccountLog======>");
		 return updateObject("PA_updatePolicyAccountLog",  policyAccountLogPO) ;
 	 }	

    /**
     * @description 查询保单账户基本信息变更履历表单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPO 保单账户基本信息变更履历表对象
     * @return PolicyAccountLogPO 查询结果对象
     */
	 public PolicyAccountLogPO findPolicyAccountLog(PolicyAccountLogPO policyAccountLogPO) {
	  	logger.debug("<======PolicyAccountLogDaoImpl--findPolicyAccountLog======>");
		return findObject("PA_findPolicyAccountLogByLogId",  policyAccountLogPO) ;
	 }
	
	 /**
     * @description 查询保单账户基本信息变更履历表所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPO 保单账户基本信息变更履历表对象 
     * @return List<PolicyAccountLogPO> 查询结果List
     */
	 public List<PolicyAccountLogPO> findAllPolicyAccountLog(PolicyAccountLogPO policyAccountLogPO) {
	 	logger.debug("<======PolicyAccountLogDaoImpl--findAllPolicyAccountLog======>");
		return findAll("PA_findAllPolicyAccountLog",  policyAccountLogPO) ;
	 }
	
	 /**
     * @description 查询保单账户基本信息变更履历表数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPO 保单账户基本信息变更履历表对象
     * @return int 查询结果条数
     */
	 public int findPolicyAccountLogTotal(PolicyAccountLogPO policyAccountLogPO) {
	 	logger.debug("<======PolicyAccountLogDaoImpl--findPolicyAccountLogTotal======>");
		return findCount("PA_findPolicyAccountLogTotal",  policyAccountLogPO) ;
	 }
	
	 /**
     * @description 分页查询保单账户基本信息变更履历表数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<PolicyAccountLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<PolicyAccountLogPO> queryPolicyAccountLogForPage(PolicyAccountLogPO policyAccountLogPO, CurrentPage<PolicyAccountLogPO> currentPage) {
		logger.debug("<======PolicyAccountLogDaoImpl--queryPolicyAccountLogForPage======>");
		currentPage.setParamObject(policyAccountLogPO);
		return queryForPage("PA_findPolicyAccountLogTotal", "PA_queryPolicyAccountLogForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加保单账户基本信息变更履历表数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPOList 保单账户基本信息变更履历表对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSavePolicyAccountLog(List<PolicyAccountLogPO> policyAccountLogPOList) {
	 	logger.debug("<======PolicyAccountLogDaoImpl--batchSavePolicyAccountLog======>");
		return batchSave("PA_addPolicyAccountLog", policyAccountLogPOList) ;
	 }
	
	 /**
     * @description 批量修改保单账户基本信息变更履历表数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPOList 保单账户基本信息变更履历表对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdatePolicyAccountLog(List<PolicyAccountLogPO> policyAccountLogPOList) {
	 	logger.debug("<======PolicyAccountLogDaoImpl--batchUpdatePolicyAccountLog======>");
		return batchUpdate("PA_updatePolicyAccountLog", policyAccountLogPOList) ;
	 }
	
	/**
     * @description 批量删除保单账户基本信息变更履历表数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPOList 保单账户基本信息变更履历表对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeletePolicyAccountLog(List<PolicyAccountLogPO> policyAccountLogPOList) {
	 	logger.debug("<======PolicyAccountLogDaoImpl--batchDeletePolicyAccountLog======>");
		return batchDelete("PA_deletePolicyAccountLog", policyAccountLogPOList) ;
	 }
	
	 /**
     * @description 查询保单账户基本信息变更履历表所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPO 保单账户基本信息变更履历表对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapPolicyAccountLog(PolicyAccountLogPO policyAccountLogPO) {
	 	logger.debug("<======PolicyAccountLogDaoImpl--findAllMapPolicyAccountLog======>");
		return findAllMap("PA_findAllMapPolicyAccountLog", policyAccountLogPO) ;
	 }
	 
	 /**
     * @description 根据保单id和时间区间查询账户信息
     * @version
     * @title
     * <AUTHOR>
     * @param policyAccountLogPO 保单账户基本信息变更履历表对象
     * @return List<PolicyAccountLogPO> 查询结果List
     */
	@Override
	public List<PolicyAccountLogPO> findAllByPolicyIdBetweenDate(
			PolicyAccountLogPO policyAccountLogPO) {
	 	logger.debug("<======PolicyAccountLogDaoImpl--findAllByPolicyIdBetweenDate======>");
		return findAll("PA_findAllByPolicyIdBetweenDate",  policyAccountLogPO) ;
	}
}
