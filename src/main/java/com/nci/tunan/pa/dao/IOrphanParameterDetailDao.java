package com.nci.tunan.pa.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.pa.interfaces.model.po.OrphanParameterDetail;
import com.nci.tunan.pa.interfaces.model.po.OrphanParameterDetailPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description IOrphanParameterDetailDao接口
 * <AUTHOR> 
 * @date 2020-03-21 22:15:49  
 */
 public interface IOrphanParameterDetailDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param orphanParameterDetailPO 对象
     * @return OrphanParameterDetailPO 添加结果
     */
	 public OrphanParameterDetailPO addOrphanParameterDetail(OrphanParameterDetailPO orphanParameterDetailPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param orphanParameterDetailPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteOrphanParameterDetail(OrphanParameterDetailPO orphanParameterDetailPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param orphanParameterDetailPO 对象
     * @return OrphanParameterDetailPO 修改结果
     */
	 public OrphanParameterDetailPO updateOrphanParameterDetail(OrphanParameterDetailPO orphanParameterDetailPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param orphanParameterDetailPO 对象
     * @return OrphanParameterDetailPO 查询结果对象
     */
	 public OrphanParameterDetailPO findOrphanParameterDetail(OrphanParameterDetailPO orphanParameterDetailPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param orphanParameterDetailPO 对象
     * @return List<OrphanParameterDetailPO> 查询结果List
     */
	 public List<OrphanParameterDetailPO> findAllOrphanParameterDetail(OrphanParameterDetailPO orphanParameterDetailPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param orphanParameterDetailPO 对象
     * @return int 查询结果条数
     */
	 public int findOrphanParameterDetailTotal(OrphanParameterDetailPO orphanParameterDetailPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<OrphanParameterDetailPO> 查询结果的当前页对象
     */
	 public CurrentPage<OrphanParameterDetailPO> queryOrphanParameterDetailForPage(OrphanParameterDetailPO orphanParameterDetailPO, CurrentPage<OrphanParameterDetailPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param orphanParameterDetailPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveOrphanParameterDetail(List<OrphanParameterDetailPO> orphanParameterDetailPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param orphanParameterDetailPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateOrphanParameterDetail(List<OrphanParameterDetailPO> orphanParameterDetailPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param orphanParameterDetailPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteOrphanParameterDetail(List<OrphanParameterDetailPO> orphanParameterDetailPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param orphanParameterDetailPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapOrphanParameterDetail(OrphanParameterDetailPO orphanParameterDetailPO);

	 /**
	  * 批量添加孤儿单明细表数据
	  * @description
	  * @version V1.0.0
	  * @title
	  * <AUTHOR> <EMAIL>
	  * @param orphanParameterDetailPO
	  * @return
	  */
	public boolean batchSaveDetail(List<OrphanParameterDetail> detailList);
	 
 }
 