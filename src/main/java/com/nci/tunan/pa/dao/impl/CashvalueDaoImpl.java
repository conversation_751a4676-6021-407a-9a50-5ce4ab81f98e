package com.nci.tunan.pa.dao.impl;

import com.nci.tunan.pa.dao.ICashvalueDao;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.pa.interfaces.model.po.CashvaluePO;


/** 
 * @description 现金价值表dao实现类
 * <AUTHOR> 
 * @date 2021-10-28 16:24:31  
 */
public class CashvalueDaoImpl  extends BaseDaoImpl  implements ICashvalueDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePO 现金价值表PO对象
     * @return CashvaluePO 添加结果
     */
	 public CashvaluePO addCashvalue(CashvaluePO cashvaluePO) {
	 	 logger.debug("<======CashvalueDaoImpl--addCashvalue======>");
		 return createObject("addCashvalue",  cashvaluePO) ;
	 }
    
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePO 现金价值表PO对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteCashvalue(CashvaluePO cashvaluePO) {
 	 	 logger.debug("<======CashvalueDaoImpl--deleteCashvalue======>");
		 return deleteObject("deleteCashvalue",  cashvaluePO) ;
	 }

	/**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePO 现金价值表PO对象
     * @return CashvaluePO 修改结果
     */
 	 public CashvaluePO updateCashvalue(CashvaluePO cashvaluePO) {
 	 	 logger.debug("<======CashvalueDaoImpl--updateCashvalue======>");
		 return updateObject("updateCashvalue",  cashvaluePO) ;
 	 }	

    /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePO 现金价值表PO对象
     * @return CashvaluePO 查询结果对象
     */
	 public CashvaluePO findCashvalue(CashvaluePO cashvaluePO) {
	  	logger.debug("<======CashvalueDaoImpl--findCashvalue======>");
		return findObject("findCashvalue",  cashvaluePO) ;
	 }
	
	 /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePO 现金价值表PO对象
     * @return List<CashvaluePO> 查询结果List
     */
	 public List<CashvaluePO> findAllCashvalue(CashvaluePO cashvaluePO) {
	 	logger.debug("<======CashvalueDaoImpl--findAllCashvalue======>");
		return findAll("findAllCashvalue",  cashvaluePO) ;
	 }
	
	 /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePO 现金价值表PO对象
     * @return int 查询结果条数
     */
	 public int findCashvalueTotal(CashvaluePO cashvaluePO) {
	 	logger.debug("<======CashvalueDaoImpl--findCashvalueTotal======>");
		return findCount("findCashvalueTotal",  cashvaluePO) ;
	 }
	
	 /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<CashvaluePO> 查询结果的当前页对象
     */
	 public CurrentPage<CashvaluePO> queryCashvalueForPage(CashvaluePO cashvaluePO, CurrentPage<CashvaluePO> currentPage) {
		logger.debug("<======CashvalueDaoImpl--queryCashvalueForPage======>");
		currentPage.setParamObject(cashvaluePO);
		return queryForPage("findCashvalueTotal", "queryCashvalueForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePOList 现金价值表PO对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveCashvalue(List<CashvaluePO> cashvaluePOList) {
	 	logger.debug("<======CashvalueDaoImpl--batchSaveCashvalue======>");
		return batchSave("addCashvalue", cashvaluePOList) ;
	 }
	
	 /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePOList 现金价值表PO对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateCashvalue(List<CashvaluePO> cashvaluePOList) {
	 	logger.debug("<======CashvalueDaoImpl--batchUpdateCashvalue======>");
		return batchUpdate("updateCashvalue", cashvaluePOList) ;
	 }
	
	/**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePOList 现金价值表PO对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteCashvalue(List<CashvaluePO> cashvaluePOList) {
	 	logger.debug("<======CashvalueDaoImpl--batchDeleteCashvalue======>");
		return batchDelete("deleteCashvalue", cashvaluePOList) ;
	 }
	
	 /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePO 现金价值表PO对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapCashvalue(CashvaluePO cashvaluePO) {
	 	logger.debug("<======CashvalueDaoImpl--findAllMapCashvalue======>");
		return findAllMap("findAllMapCashvalue", cashvaluePO) ;
	 }
	 
	 /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param cashvaluePO 现金价值表PO对象
     * @return List<CashvaluePO> 查询结果List
     */
	 public List<CashvaluePO> XJfindAllCashvalue(CashvaluePO cashvaluePO) {
	 	logger.debug("<======CashvalueDaoImpl--findAllCashvalue======>");
		return findAll("PA_ALLfindAllCashvalue",  cashvaluePO) ;
	 }
}
