package com.nci.tunan.pa.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.pa.interfaces.model.po.PolicyPrepaymentLogPO;
import com.nci.udmp.framework.model.CurrentPage;


/**
 * 
 * @description IPolicyPrepaymentLogDao 接口
 * <AUTHOR> 
 * @date 2019-12-31 上午9:07:39 
 * @.belongToModule PA-保单管理系统
 */
 public interface IPolicyPrepaymentLogDao {
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrepaymentLogPO 对象
     * @return PolicyPrepaymentLogPO 添加结果
     */
	 public PolicyPrepaymentLogPO addPolicyPrepaymentLog(PolicyPrepaymentLogPO policyPrepaymentLogPO);
	 
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrepaymentLogPO 对象
     * @return boolean 删除是否成功
     */
	 public boolean deletePolicyPrepaymentLog(PolicyPrepaymentLogPO policyPrepaymentLogPO);
	 
     /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrepaymentLogPO 对象
     * @return PolicyPrepaymentLogPO 修改结果
     */
	 public PolicyPrepaymentLogPO updatePolicyPrepaymentLog(PolicyPrepaymentLogPO policyPrepaymentLogPO);
	 
     /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrepaymentLogPO 对象
     * @return PolicyPrepaymentLogPO 查询结果对象
     */
	 public PolicyPrepaymentLogPO findPolicyPrepaymentLog(PolicyPrepaymentLogPO policyPrepaymentLogPO);
	 
     /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrepaymentLogPO 对象
     * @return List<PolicyPrepaymentLogPO> 查询结果List
     */
	 public List<PolicyPrepaymentLogPO> findAllPolicyPrepaymentLog(PolicyPrepaymentLogPO policyPrepaymentLogPO);
	 
     /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrepaymentLogPO 对象
     * @return int 查询结果条数
     */
	 public int findPolicyPrepaymentLogTotal(PolicyPrepaymentLogPO policyPrepaymentLogPO);

     /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<PolicyPrepaymentLogPO> 查询结果的当前页对象
     */
	 public CurrentPage<PolicyPrepaymentLogPO> queryPolicyPrepaymentLogForPage(PolicyPrepaymentLogPO policyPrepaymentLogPO, CurrentPage<PolicyPrepaymentLogPO> currentPage);

     /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrepaymentLogPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSavePolicyPrepaymentLog(List<PolicyPrepaymentLogPO> policyPrepaymentLogPOList);

     /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrepaymentLogPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdatePolicyPrepaymentLog(List<PolicyPrepaymentLogPO> policyPrepaymentLogPOList);

     /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrepaymentLogPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeletePolicyPrepaymentLog(List<PolicyPrepaymentLogPO> policyPrepaymentLogPOList);

     /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param policyPrepaymentLogPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapPolicyPrepaymentLog(PolicyPrepaymentLogPO policyPrepaymentLogPO);
	 
 }
 