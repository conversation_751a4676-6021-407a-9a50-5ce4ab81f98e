package com.nci.tunan.pa.dao;

import com.nci.udmp.framework.model.*;
import java.util.Map;
import java.util.List;
import com.nci.tunan.pa.batch.survivalAnnuityMaturity.po.SurvivalAnnuityMaturityPO;
import com.nci.tunan.pa.dao.IPayPlanCreateTaskDao;
import com.nci.tunan.pa.interfaces.model.po.PayPlanCreateTaskPO;

/** 
 * @description IPayPlanCreateTaskDao 生存给付计划生成任务表接口
 * <AUTHOR> 
 * @date 2022-03-07 15:51:44  
 */
public interface IPayPlanCreateTaskDao {
	/**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param payPlanCreateTaskPO 对象
     * @return PayPlanCreateTaskPO 添加结果
     */
	public PayPlanCreateTaskPO addPayPlanCreateTask(PayPlanCreateTaskPO payPlanCreateTaskPO);
	 
    /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param payPlanCreateTaskPO 对象
     * @return boolean 删除是否成功
     */
	public boolean deletePayPlanCreateTask(PayPlanCreateTaskPO payPlanCreateTaskPO);
	 
    /**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param payPlanCreateTaskPO 对象
     * @return PayPlanCreateTaskPO 修改结果
     */
	public PayPlanCreateTaskPO updatePayPlanCreateTask(PayPlanCreateTaskPO payPlanCreateTaskPO);
	 
    /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param payPlanCreateTaskPO 对象
     * @return PayPlanCreateTaskPO 查询结果对象
     */
	public PayPlanCreateTaskPO findPayPlanCreateTask(PayPlanCreateTaskPO payPlanCreateTaskPO);
	
	/**
	 * @description 按索引ListId查询单条
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param payPlanCreateTaskPO 对象
	 * @return PayPlanCreateTaskPO 查询结果对象
	 */
	 public PayPlanCreateTaskPO findPayPlanCreateTaskByListId(PayPlanCreateTaskPO payPlanCreateTaskPO);
	 
    /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param payPlanCreateTaskPO 对象
     * @return List<PayPlanCreateTaskPO> 查询结果List
     */
	public List<PayPlanCreateTaskPO> findAllPayPlanCreateTask(PayPlanCreateTaskPO payPlanCreateTaskPO);
	 
    /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param payPlanCreateTaskPO 对象
     * @return int 查询结果条数
     */
	public int findPayPlanCreateTaskTotal(PayPlanCreateTaskPO payPlanCreateTaskPO);

    /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<PayPlanCreateTaskPO> 查询结果的当前页对象
     */
	public CurrentPage<PayPlanCreateTaskPO> queryPayPlanCreateTaskForPage(PayPlanCreateTaskPO payPlanCreateTaskPO, CurrentPage<PayPlanCreateTaskPO> currentPage);

    /**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param payPlanCreateTaskPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	public boolean batchSavePayPlanCreateTask(List<PayPlanCreateTaskPO> payPlanCreateTaskPOList);

    /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param payPlanCreateTaskPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	public boolean batchUpdatePayPlanCreateTask(List<PayPlanCreateTaskPO> payPlanCreateTaskPOList);

    /**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param payPlanCreateTaskPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	public boolean batchDeletePayPlanCreateTask(List<PayPlanCreateTaskPO> payPlanCreateTaskPOList);

    /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param payPlanCreateTaskPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	public List<Map<String, Object>> findAllMapPayPlanCreateTask(PayPlanCreateTaskPO payPlanCreateTaskPO);
	
	/**
	 * 
	 * @description 已生成给付计划未计算领取标准的存量数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param payPlanCreateTaskPO
	 * @return
	 */
	public List<PayPlanCreateTaskPO> queryOldPayPlan(PayPlanCreateTaskPO payPlanCreateTaskPO);

	/**
	 * 
	 * @description 未生成给付计划信息的保单数据
	 * @version
	 * @title
	 * <AUTHOR>
	 * @param payPlanCreateTaskPO
	 * @return
	 */
	public List<PayPlanCreateTaskPO> queryCreatePayPlanTask(PayPlanCreateTaskPO payPlanCreateTaskPO);

    /**
     * 
     * @description 查询满足条件的记录集
     * @version
     * @title
     * <AUTHOR>
     * @param surAnnMatPO
     * @return
     */
	public List<SurvivalAnnuityMaturityPO> queryPayPlanList(SurvivalAnnuityMaturityPO surAnnMatPO);
	 
 }
 