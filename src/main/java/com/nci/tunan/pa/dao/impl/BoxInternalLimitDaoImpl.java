package com.nci.tunan.pa.dao.impl;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;

import com.nci.tunan.pa.dao.IBoxInternalLimitDao;
import com.nci.tunan.pa.interfaces.model.po.BoxInternalLimitPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.CurrentPage;
import com.nci.udmp.util.logging.LoggerFactory;


/** 
 * @description BoxInternalLimitDaoImpl实现类
 * <AUTHOR> 
 * @date 2021-01-13 17:35:22  
 */
public class BoxInternalLimitDaoImpl  extends BaseDaoImpl  implements IBoxInternalLimitDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param boxInternalLimitPO 对象
     * @return BoxInternalLimitPO 添加结果
     */
	 public BoxInternalLimitPO addBoxInternalLimit(BoxInternalLimitPO boxInternalLimitPO) {
	 	 logger.debug("<======BoxInternalLimitDaoImpl--addBoxInternalLimit======>");
		 return createObject("addBoxInternalLimit",  boxInternalLimitPO) ;
	 }
    
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param boxInternalLimitPO 对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteBoxInternalLimit(BoxInternalLimitPO boxInternalLimitPO) {
 	 	 logger.debug("<======BoxInternalLimitDaoImpl--deleteBoxInternalLimit======>");
		 return deleteObject("deleteBoxInternalLimit",  boxInternalLimitPO) ;
	 }

	/**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param boxInternalLimitPO 对象
     * @return BoxInternalLimitPO 修改结果
     */
 	 public BoxInternalLimitPO updateBoxInternalLimit(BoxInternalLimitPO boxInternalLimitPO) {
 	 	 logger.debug("<======BoxInternalLimitDaoImpl--updateBoxInternalLimit======>");
		 return updateObject("updateBoxInternalLimit",  boxInternalLimitPO) ;
 	 }	

    /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param boxInternalLimitPO 对象
     * @return BoxInternalLimitPO 查询结果对象
     */
	 public BoxInternalLimitPO findBoxInternalLimit(BoxInternalLimitPO boxInternalLimitPO) {
	  	logger.debug("<======BoxInternalLimitDaoImpl--findBoxInternalLimit======>");
		return findObject("findBoxInternalLimit",  boxInternalLimitPO) ;
	 }
	
	 /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param boxInternalLimitPO 对象
     * @return List<BoxInternalLimitPO> 查询结果List
     */
	 public List<BoxInternalLimitPO> findAllBoxInternalLimit(BoxInternalLimitPO boxInternalLimitPO) {
	 	logger.debug("<======BoxInternalLimitDaoImpl--findAllBoxInternalLimit======>");
		return findAll("findAllBoxInternalLimit",  boxInternalLimitPO) ;
	 }
	
	 /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param boxInternalLimitPO 对象
     * @return int 查询结果条数
     */
	 public int findBoxInternalLimitTotal(BoxInternalLimitPO boxInternalLimitPO) {
	 	logger.debug("<======BoxInternalLimitDaoImpl--findBoxInternalLimitTotal======>");
		return findCount("findBoxInternalLimitTotal",  boxInternalLimitPO) ;
	 }
	
	 /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<BoxInternalLimitPO> 查询结果的当前页对象
     */
	 public CurrentPage<BoxInternalLimitPO> queryBoxInternalLimitForPage(BoxInternalLimitPO boxInternalLimitPO, CurrentPage<BoxInternalLimitPO> currentPage) {
		logger.debug("<======BoxInternalLimitDaoImpl--queryBoxInternalLimitForPage======>");
		currentPage.setParamObject(boxInternalLimitPO);
		return queryForPage("findBoxInternalLimitTotal", "queryBoxInternalLimitForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param boxInternalLimitPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveBoxInternalLimit(List<BoxInternalLimitPO> boxInternalLimitPOList) {
	 	logger.debug("<======BoxInternalLimitDaoImpl--batchSaveBoxInternalLimit======>");
		return batchSave("addBoxInternalLimit", boxInternalLimitPOList) ;
	 }
	
	 /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param boxInternalLimitPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateBoxInternalLimit(List<BoxInternalLimitPO> boxInternalLimitPOList) {
	 	logger.debug("<======BoxInternalLimitDaoImpl--batchUpdateBoxInternalLimit======>");
		return batchUpdate("updateBoxInternalLimit", boxInternalLimitPOList) ;
	 }
	
	/**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param boxInternalLimitPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteBoxInternalLimit(List<BoxInternalLimitPO> boxInternalLimitPOList) {
	 	logger.debug("<======BoxInternalLimitDaoImpl--batchDeleteBoxInternalLimit======>");
		return batchDelete("deleteBoxInternalLimit", boxInternalLimitPOList) ;
	 }
	
	 /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param boxInternalLimitPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapBoxInternalLimit(BoxInternalLimitPO boxInternalLimitPO) {
	 	logger.debug("<======BoxInternalLimitDaoImpl--findAllMapBoxInternalLimit======>");
		return findAllMap("findAllMapBoxInternalLimit", boxInternalLimitPO) ;
	 }
}
