package com.nci.tunan.pa.dao.impl;

import com.nci.tunan.pa.dao.*;
import com.nci.tunan.pa.interfaces.model.po.*;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.*;

import java.util.Map;

import org.slf4j.Logger;

import java.util.List;

import com.nci.udmp.util.logging.LoggerFactory;


/**
 * 
 * @description IContractExtendCxDao 保单应缴日变更索引表Dao接口实现类 
 * <AUTHOR> 
 * @date 2019-12-17 下午4:45:57 
 * @.belongToModule PA-保单管理系统-保单应缴日变更索引表Dao接口实现类
 */
public class ContractExtendCxDaoImpl  extends BaseDaoImpl  implements IContractExtendCxDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加保单应缴日变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractExtendCxPO 保单应缴日变更索引表PO对象
     * @return ContractExtendCxPO 添加结果
     */
	 public ContractExtendCxPO addContractExtendCx(ContractExtendCxPO contractExtendCxPO) {
	 	 logger.debug("<======ContractExtendCxDaoImpl--addContractExtendCx======>");
		 return createObject("PA_addContractExtendCx",  contractExtendCxPO) ;
	 }
    
     /**
     * @description 删除保单应缴日变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractExtendCxPO 保单应缴日变更索引表PO对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteContractExtendCx(ContractExtendCxPO contractExtendCxPO) {
 	 	 logger.debug("<======ContractExtendCxDaoImpl--deleteContractExtendCx======>");
		 return deleteObject("PA_deleteContractExtendCx",  contractExtendCxPO) ;
	 }

	/**
     * @description 修改保单应缴日变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractExtendCxPO 保单应缴日变更索引表PO对象
     * @return ContractExtendCxPO 修改结果
     */
 	 public ContractExtendCxPO updateContractExtendCx(ContractExtendCxPO contractExtendCxPO) {
 	 	 logger.debug("<======ContractExtendCxDaoImpl--updateContractExtendCx======>");
		 return updateObject("PA_updateContractExtendCx",  contractExtendCxPO) ;
 	 }	

    /**
     * @description 查询保单应缴日变更索引表单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractExtendCxPO 保单应缴日变更索引表PO对象
     * @return ContractExtendCxPO 查询结果对象
     */
	 public ContractExtendCxPO findContractExtendCx(ContractExtendCxPO contractExtendCxPO) {
	  	logger.debug("<======ContractExtendCxDaoImpl--findContractExtendCx======>");
		return findObject("PA_findContractExtendCx",  contractExtendCxPO) ;
	 }
	
	 /**
     * @description 查询保单应缴日变更索引表所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractExtendCxPO 保单应缴日变更索引表PO对象
     * @return List<ContractExtendCxPO> 查询结果List
     */
	 public List<ContractExtendCxPO> findAllContractExtendCx(ContractExtendCxPO contractExtendCxPO) {
	 	logger.debug("<======ContractExtendCxDaoImpl--findAllContractExtendCx======>");
		return findAll("PA_findAllContractExtendCx",  contractExtendCxPO) ;
	 }
	
	 /**
     * @description 查询保单应缴日变更索引表数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param contractExtendCxPO 保单应缴日变更索引表PO对象
     * @return int 查询结果条数
     */
	 public int findContractExtendCxTotal(ContractExtendCxPO contractExtendCxPO) {
	 	logger.debug("<======ContractExtendCxDaoImpl--findContractExtendCxTotal======>");
		return findCount("PA_findContractExtendCxTotal",  contractExtendCxPO) ;
	 }
	
	 /**
     * @description 分页查询保单应缴日变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<ContractExtendCxPO> 查询结果的当前页对象
     */
	 public CurrentPage<ContractExtendCxPO> queryContractExtendCxForPage(ContractExtendCxPO contractExtendCxPO, CurrentPage<ContractExtendCxPO> currentPage) {
		logger.debug("<======ContractExtendCxDaoImpl--queryContractExtendCxForPage======>");
		currentPage.setParamObject(contractExtendCxPO);
		return queryForPage("PA_findContractExtendCxTotal", "PA_queryContractExtendCxForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加保单应缴日变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractExtendCxPOList 保单应缴日变更索引表PO对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveContractExtendCx(List<ContractExtendCxPO> contractExtendCxPOList) {
	 	logger.debug("<======ContractExtendCxDaoImpl--batchSaveContractExtendCx======>");
		return batchSave("PA_addContractExtendCx", contractExtendCxPOList) ;
	 }
	
	 /**
     * @description 批量修改保单应缴日变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractExtendCxPOList 保单应缴日变更索引表PO对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateContractExtendCx(List<ContractExtendCxPO> contractExtendCxPOList) {
	 	logger.debug("<======ContractExtendCxDaoImpl--batchUpdateContractExtendCx======>");
		return batchUpdate("PA_updateContractExtendCx", contractExtendCxPOList) ;
	 }
	
	/**
     * @description 批量删除保单应缴日变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param contractExtendCxPOList 保单应缴日变更索引表PO对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteContractExtendCx(List<ContractExtendCxPO> contractExtendCxPOList) {
	 	logger.debug("<======ContractExtendCxDaoImpl--batchDeleteContractExtendCx======>");
		return batchDelete("PA_deleteContractExtendCx", contractExtendCxPOList) ;
	 }
	
	 /**
     * @description 查询保单应缴日变更索引表所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param contractExtendCxPO 保单应缴日变更索引表PO对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapContractExtendCx(ContractExtendCxPO contractExtendCxPO) {
	 	logger.debug("<======ContractExtendCxDaoImpl--findAllMapContractExtendCx======>");
		return findAllMap("PA_findAllMapContractExtendCx", contractExtendCxPO) ;
	 }
}
