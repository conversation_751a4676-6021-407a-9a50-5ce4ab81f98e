package com.nci.tunan.pa.dao.impl;

import com.nci.tunan.pa.dao.ISaleComDao;
import com.nci.tunan.pa.interfaces.model.po.SaleComPO;
import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;


/**
 * 
 * @description 合作机构信息表接口实现类 
 * <AUTHOR> 
 * @.belongToModule PA-保单管理系统-合作机构信息表接口
 * @date 2019-12-17 下午2:36:27 
 */
public class SaleComDaoImpl  extends BaseDaoImpl  implements ISaleComDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加合作机构信息表数据
     * @version
     * @title
     * <AUTHOR>
     * @param saleComPO 合作机构信息表接口入参对象
     * @return SaleComPO 添加结果
     */
	 public SaleComPO addSaleCom(SaleComPO saleComPO) {
	 	 logger.debug("<======SaleComDaoImpl--addSaleCom======>");
		 return createObject("PA_addSaleCom",  saleComPO) ;
	 }
    
     /**
     * @description 删除合作机构信息表数据
     * @version
     * @title
     * <AUTHOR>
     * @param saleComPO 合作机构信息表接口入参对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteSaleCom(SaleComPO saleComPO) {
 	 	 logger.debug("<======SaleComDaoImpl--deleteSaleCom======>");
		 return deleteObject("deleteSaleCom",  saleComPO) ;
	 }

	/**
     * @description 修改合作机构信息表数据
     * @version
     * @title
     * <AUTHOR>
     * @param saleComPO 合作机构信息表接口入参对象
     * @return SaleComPO 修改结果
     */
 	 public SaleComPO updateSaleCom(SaleComPO saleComPO) {
 	 	 logger.debug("<======SaleComDaoImpl--updateSaleCom======>");
		 return updateObject("PA_updateSaleCom",  saleComPO) ;
 	 }	

    /**
     * @description 查询合作机构信息表单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param saleComPO 合作机构信息表接口入参对象
     * @return SaleComPO 查询结果对象
     */
	 public SaleComPO findSaleCom(SaleComPO saleComPO) {
	  	logger.debug("<======SaleComDaoImpl--findSaleCom======>");
		return findObject("PA_findSaleComBySaleComCode",  saleComPO) ;
	 }
	
	 /**
     * @description 查询合作机构信息表所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param saleComPO 合作机构信息表接口入参对象
     * @return List<SaleComPO> 查询结果List
     */
	 public List<SaleComPO> findAllSaleCom(SaleComPO saleComPO) {
	 	logger.debug("<======SaleComDaoImpl--findAllSaleCom======>");
		return findAll("findAllSaleCom",  saleComPO) ;
	 }
	
	 /**
     * @description 查询合作机构信息表数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param saleComPO 合作机构信息表接口入参对象
     * @return int 查询结果条数
     */
	 public int findSaleComTotal(SaleComPO saleComPO) {
	 	logger.debug("<======SaleComDaoImpl--findSaleComTotal======>");
		return findCount("findSaleComTotal",  saleComPO) ;
	 }
	
	 /**
     * @description 分页查询合作机构信息表数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<SaleComPO> 查询结果的当前页对象
     */
	 public CurrentPage<SaleComPO> querySaleComForPage(SaleComPO saleComPO, CurrentPage<SaleComPO> currentPage) {
		logger.debug("<======SaleComDaoImpl--querySaleComForPage======>");
		currentPage.setParamObject(saleComPO);
		return queryForPage("findSaleComTotal", "querySaleComForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加合作机构信息表数据
     * @version
     * @title
     * <AUTHOR>
     * @param saleComPOList 合作机构信息表接口入参对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveSaleCom(List<SaleComPO> saleComPOList) {
	 	logger.debug("<======SaleComDaoImpl--batchSaveSaleCom======>");
		return batchSave("addSaleCom", saleComPOList) ;
	 }
	
	 /**
     * @description 批量修改合作机构信息表数据
     * @version
     * @title
     * <AUTHOR>
     * @param saleComPOList 合作机构信息表接口入参对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateSaleCom(List<SaleComPO> saleComPOList) {
	 	logger.debug("<======SaleComDaoImpl--batchUpdateSaleCom======>");
		return batchUpdate("updateSaleCom", saleComPOList) ;
	 }
	
	/**
     * @description 批量删除合作机构信息表数据
     * @version
     * @title
     * <AUTHOR>
     * @param saleComPOList 合作机构信息表接口入参对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteSaleCom(List<SaleComPO> saleComPOList) {
	 	logger.debug("<======SaleComDaoImpl--batchDeleteSaleCom======>");
		return batchDelete("deleteSaleCom", saleComPOList) ;
	 }
	
	 /**
     * @description 查询合作机构信息表所有数据重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param saleComPO 合作机构信息表接口入参对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapSaleCom(SaleComPO saleComPO) {
	 	logger.debug("<======SaleComDaoImpl--findAllMapSaleCom======>");
		return findAllMap("findAllMapSaleCom", saleComPO) ;
	 }
}
