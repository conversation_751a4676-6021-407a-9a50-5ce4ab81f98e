package com.nci.tunan.pa.dao;

import java.util.List;
import java.util.Map;

import com.nci.tunan.pa.interfaces.model.po.InvestUnitPriceCxPO;
import com.nci.udmp.framework.model.CurrentPage;


/** 
 * @description 投资单位价格信息Dao接口
 * <AUTHOR> 
 * @date 2016-06-14 17:20:08  
 * @.belongToModule PA-保单管理系统-投资单位价格信息Dao接口
 */
 public interface IInvestUnitPriceCxDao {
	 /**
     * @description 增加投资单位价格变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPO 投资单位价格管理对象
     * @return InvestUnitPriceCxPO 添加结果
     */
	 public InvestUnitPriceCxPO addInvestUnitPriceCx(InvestUnitPriceCxPO investUnitPriceCxPO);
	 
     /**
     * @description 删除投资单位价格变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPO 投资单位价格管理对象
     * @return boolean 删除是否成功
     */
	 public boolean deleteInvestUnitPriceCx(InvestUnitPriceCxPO investUnitPriceCxPO);
	 
     /**
     * @description 修改投资单位价格变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPO 投资单位价格管理对象
     * @return InvestUnitPriceCxPO 修改结果
     */
	 public InvestUnitPriceCxPO updateInvestUnitPriceCx(InvestUnitPriceCxPO investUnitPriceCxPO);
	 
     /**
     * @description 查询投资单位价格变更索引表单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPO 投资单位价格管理对象
     * @return InvestUnitPriceCxPO 查询结果投资单位价格管理对象
     */
	 public InvestUnitPriceCxPO findInvestUnitPriceCx(InvestUnitPriceCxPO investUnitPriceCxPO);
	 
     /**
     * @description 查询投资单位价格变更索引表所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPO 投资单位价格管理对象
     * @return List<InvestUnitPriceCxPO> 查询结果List
     */
	 public List<InvestUnitPriceCxPO> findAllInvestUnitPriceCx(InvestUnitPriceCxPO investUnitPriceCxPO);
	 
	 
     /**
     * @description 查询投资单位价格变更索引表所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPO 投资单位价格管理对象
     * @return List<InvestUnitPriceCxPO> 查询结果List
     */
	 public List<InvestUnitPriceCxPO> findInvestUnitPriceCxByPolicyChgId(InvestUnitPriceCxPO investUnitPriceCxPO);
     /**
     * @description 查询投资单位价格变更索引表数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPO 投资单位价格管理对象
     * @return int 查询结果条数
     */
	 public int findInvestUnitPriceCxTotal(InvestUnitPriceCxPO investUnitPriceCxPO);

     /**
     * @description 分页查询投资单位价格变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页投资单位价格管理对象
     * @return CurrentPage<InvestUnitPriceCxPO> 查询结果的当前页投资单位价格管理对象
     */
	 public CurrentPage<InvestUnitPriceCxPO> queryInvestUnitPriceCxForPage(InvestUnitPriceCxPO investUnitPriceCxPO, CurrentPage<InvestUnitPriceCxPO> currentPage);

     /**
     * @description 批量增加投资单位价格变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPOList 投资单位价格管理对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveInvestUnitPriceCx(List<InvestUnitPriceCxPO> investUnitPriceCxPOList);

     /**
     * @description 批量修改投资单位价格变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPOList 投资单位价格管理对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateInvestUnitPriceCx(List<InvestUnitPriceCxPO> investUnitPriceCxPOList);

     /**
     * @description 批量删除投资单位价格变更索引表数据
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPOList 投资单位价格管理对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteInvestUnitPriceCx(List<InvestUnitPriceCxPO> investUnitPriceCxPOList);

     /**
     * @description 查询投资单位价格变更索引表所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param investUnitPriceCxPO 投资单位价格管理对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String, Object>> findAllMapInvestUnitPriceCx(InvestUnitPriceCxPO investUnitPriceCxPO);
	 
 }
 