package com.nci.tunan.pa.dao.impl;

import com.nci.udmp.framework.dao.impl.BaseDaoImpl;
import com.nci.udmp.framework.model.*;
import java.util.Map;
import org.slf4j.Logger;
import java.util.List;
import com.nci.udmp.util.logging.LoggerFactory;
import com.nci.tunan.pa.dao.IRenewalLimitDao;
import com.nci.tunan.pa.interfaces.model.po.RenewalLimitPO;


/** 
 * @description RenewalLimitDaoImpl实现类
 * <AUTHOR> 
 * @date 2024-05-27 17:16:04  
 */
public class RenewalLimitDaoImpl  extends BaseDaoImpl  implements IRenewalLimitDao  {
	/** 
	 * @Fields logger :  日志工具
 	 */
	 private static Logger logger = LoggerFactory.getLogger();
	
	 /**
     * @description 增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPO 对象
     * @return RenewalLimitPO 添加结果
     */
	 public RenewalLimitPO addRenewalLimit(RenewalLimitPO renewalLimitPO) {
	 	 logger.debug("<======RenewalLimitDaoImpl--PAS_addRenewalLimit======>");
		 return createObject("PAS_addRenewalLimit",  renewalLimitPO) ;
	 }
    
     /**
     * @description 删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPO 对象
     * @return boolean 删除是否成功
     */
 	 public boolean deleteRenewalLimit(RenewalLimitPO renewalLimitPO) {
 	 	 logger.debug("<======RenewalLimitDaoImpl--deleteRenewalLimit======>");
		 return deleteObject("deleteRenewalLimit",  renewalLimitPO) ;
	 }

	/**
     * @description 修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPO 对象
     * @return RenewalLimitPO 修改结果
     */
 	 public RenewalLimitPO updateRenewalLimit(RenewalLimitPO renewalLimitPO) {
 	 	 logger.debug("<======RenewalLimitDaoImpl--updateRenewalLimit======>");
		 return updateObject("updateRenewalLimit",  renewalLimitPO) ;
 	 }	

    /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPO 对象
     * @return RenewalLimitPO 查询结果对象
     */
	 public RenewalLimitPO findRenewalLimit(RenewalLimitPO renewalLimitPO) {
	  	logger.debug("<======RenewalLimitDaoImpl--findRenewalLimit======>");
		return findObject("findRenewalLimit",  renewalLimitPO) ;
	 }
	
	 /**
     * @description 查询所有数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPO 对象
     * @return List<RenewalLimitPO> 查询结果List
     */
	 public List<RenewalLimitPO> findAllRenewalLimit(RenewalLimitPO renewalLimitPO) {
	 	logger.debug("<======RenewalLimitDaoImpl--findAllRenewalLimit======>");
		return findAll("findAllRenewalLimit",  renewalLimitPO) ;
	 }
	
	 /**
     * @description 查询数据条数
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPO 对象
     * @return int 查询结果条数
     */
	 public int findRenewalLimitTotal(RenewalLimitPO renewalLimitPO) {
	 	logger.debug("<======RenewalLimitDaoImpl--findRenewalLimitTotal======>");
		return findCount("findRenewalLimitTotal",  renewalLimitPO) ;
	 }
	
	 /**
     * @description 分页查询数据
     * @version
     * @title
     * <AUTHOR>
     * @param currentPage 当前页对象
     * @return CurrentPage<RenewalLimitPO> 查询结果的当前页对象
     */
	 public CurrentPage<RenewalLimitPO> queryRenewalLimitForPage(RenewalLimitPO renewalLimitPO, CurrentPage<RenewalLimitPO> currentPage) {
		logger.debug("<======RenewalLimitDaoImpl--queryRenewalLimitForPage======>");
		currentPage.setParamObject(renewalLimitPO);
		return queryForPage("findRenewalLimitTotal", "queryRenewalLimitForPage",  currentPage) ;
	 }
	
	/**
     * @description 批量增加数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPOList 对象列表
     * @return boolean 批量添加是否成功
     */
	 public boolean batchSaveRenewalLimit(List<RenewalLimitPO> renewalLimitPOList) {
	 	logger.debug("<======RenewalLimitDaoImpl--batchSaveRenewalLimit======>");
		return batchSave("addRenewalLimit", renewalLimitPOList) ;
	 }
	
	 /**
     * @description 批量修改数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPOList 对象列表
     * @return boolean 批量修改是否成功
     */
	 public boolean batchUpdateRenewalLimit(List<RenewalLimitPO> renewalLimitPOList) {
	 	logger.debug("<======RenewalLimitDaoImpl--batchUpdateRenewalLimit======>");
		return batchUpdate("updateRenewalLimit", renewalLimitPOList) ;
	 }
	
	/**
     * @description 批量删除数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPOList 对象列表
     * @return boolean 批量删除是否成功
     */
	 public boolean batchDeleteRenewalLimit(List<RenewalLimitPO> renewalLimitPOList) {
	 	logger.debug("<======RenewalLimitDaoImpl--batchDeleteRenewalLimit======>");
		return batchDelete("deleteRenewalLimit", renewalLimitPOList) ;
	 }
	
	 /**
     * @description 查询所有数据 ，重新组装为map
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPO 对象
     * @return List<Map<String, Object>> 查询结果存放到map中
     */
	 public List<Map<String,  Object>> findAllMapRenewalLimit(RenewalLimitPO renewalLimitPO) {
	 	logger.debug("<======RenewalLimitDaoImpl--findAllMapRenewalLimit======>");
		return findAllMap("findAllMapRenewalLimit", renewalLimitPO) ;
	 }
	 
    /**
     * @description 查询单条数据
     * @version
     * @title
     * <AUTHOR>
     * @param renewalLimitPO 对象
     * @return RenewalLimitPO 查询结果对象
     */
	 public RenewalLimitPO findRenewalLimitByBusiItemIdAndItemId(RenewalLimitPO renewalLimitPO) {
	  	logger.debug("<======RenewalLimitDaoImpl--findRenewalLimitByBusiItemIdAndItemId======>");
		return findObject("PAS_findRenewalLimitByBusiItemIdAndItemId",  renewalLimitPO) ;
	 }
}
