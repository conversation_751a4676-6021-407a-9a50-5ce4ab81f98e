package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900982.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class SubRiskList implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	// 险种信息
	private List<SubRiskInfo> SubRiskInfo;
	@XmlElement(name="SubRiskInfo")
	public List<SubRiskInfo> getSubRiskInfo() {
		return SubRiskInfo;
	}
	public void setSubRiskInfo(List<SubRiskInfo> subRiskInfo) {
		SubRiskInfo = subRiskInfo;
	}
}
