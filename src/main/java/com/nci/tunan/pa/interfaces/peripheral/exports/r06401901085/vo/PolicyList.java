package com.nci.tunan.pa.interfaces.peripheral.exports.r06401901085.vo;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;


/**
 * @description  保单信息列表
 * <AUTHOR> 
 * @date 2025年3月18日
 * @.belongToModule PA-保单管理系统-保单关联保单列表查询接口
 */
public class PolicyList {

	//保单信息列表
	private List<PolicyInfo> policyInfo;

	@XmlElement(name = "policyInfo")
	public List<PolicyInfo> getPolicyInfo() {
		return policyInfo;
	}

	public void setPolicyInfo(List<PolicyInfo> policyInfo) {
		this.policyInfo = policyInfo;
	}
	
}
