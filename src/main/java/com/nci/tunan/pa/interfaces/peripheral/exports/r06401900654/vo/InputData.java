package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900654.vo;

import javax.xml.bind.annotation.XmlElement;

public class InputData {
	
	/**
	 * 业务来源
	 */
	private String Source;
	
	/**
	 * 保单号
	 */
	private String ContNo;
	
	/**
	 * 是否928/9281险种
	 */
	private String Is928Risk;
	
	/**
	 * 是否投资组合基础账户
	 */
	private String IsInvestPortfolio;
	
	/**
	 * 是否税延账户轨迹	
	 */
	private String IsTaxTrace;
	
	/**
	 * 投资组合账户编号	
	 */
	private String IPAccountID;
	/**
	 * 账户变更开始日期
	 */
	private String AIChangeStartDate;	
	/**
	 * 账户变更结束日期
	 */
	private String AIChangeEndDate;
	@XmlElement(name="AIChangeStartDate")
	public String getAIChangeStartDate() {
		return AIChangeStartDate;
	}
	public void setAIChangeStartDate(String aIChangeStartDate) {
		AIChangeStartDate = aIChangeStartDate;
	}
	@XmlElement(name="AIChangeEndDate")
	public String getAIChangeEndDate() {
		return AIChangeEndDate;
	}
	public void setAIChangeEndDate(String aIChangeEndDate) {
		AIChangeEndDate = aIChangeEndDate;
	}
	@XmlElement(name="ContNo")
	public String getContNo() {
		return ContNo;
	}
	public void setContNo(String contNo) {
		ContNo = contNo;
	}

	@XmlElement(name="Source")
	public String getSource() {
		return Source;
	}
	public void setSource(String source) {
		Source = source;
	}
	
	@XmlElement(name="Is928Risk")
	public String getIs928Risk() {
		return Is928Risk;
	}
	public void setIs928Risk(String is928Risk) {
		Is928Risk = is928Risk;
	}
	
	@XmlElement(name="IsInvestPortfolio")
	public String getIsInvestPortfolio() {
		return IsInvestPortfolio;
	}
	public void setIsInvestPortfolio(String isInvestPortfolio) {
		IsInvestPortfolio = isInvestPortfolio;
	}
	
	@XmlElement(name="IPAccountID")
	public String getIPAccountID() {
		return IPAccountID;
	}
	public void setIPAccountID(String iPAccountID) {
		IPAccountID = iPAccountID;
	}
	
	@XmlElement(name="IsTaxTrace")
	public String getIsTaxTrace() {
		return IsTaxTrace;
	}
	public void setIsTaxTrace(String isTaxTrace) {
		IsTaxTrace = isTaxTrace;
	}
	
	

}
