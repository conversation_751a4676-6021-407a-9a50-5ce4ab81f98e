package com.nci.tunan.pa.interfaces.peripheral.exports.r00101000684.ipremarapquerylistucc.querypremaraplist;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody")
public class SrvResBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(name = "Output")
    protected com.nci.tunan.pa.interfaces.peripheral.exports.r00101000684.vo.Output output;

    public com.nci.tunan.pa.interfaces.peripheral.exports.r00101000684.vo.Output getOutput() {
        return output;
    }
    public void setOutput(com.nci.tunan.pa.interfaces.peripheral.exports.r00101000684.vo.Output output) {
        this.output = output;
    }
}



