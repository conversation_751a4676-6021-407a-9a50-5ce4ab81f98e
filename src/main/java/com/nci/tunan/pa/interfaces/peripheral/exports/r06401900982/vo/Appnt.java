package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900982.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
/**
 * 投保人信息
 * @description 
 * <AUTHOR> 
 * @date 2024年07月08日 下午3:29:49
 * @.belongToModule PA-保单管理系统
 */
@XmlType(name = "Appnt", propOrder = {
		"newCustId",
		"oldCustId",
		"lifeStatus",
		"isHighRiskCust",
		"custName",
		"custGender",
		"countryCode",
		"countryNmae",
		"custBirthday",
		"custCertiType",
		"custCertiName",
		"custCertiCode",
		"custCertiStartDate",
		"custCertiEndDate",
		"mobile",
		"fixedPhone",
		"jobName",
		"jobCode",
		"jobUwLevel",
		"commpany",
		"province",
		"provinceId",
		"city",
		"cityId",
		"district",
		"districtId",
		"address",
		"street",
		"streetCode",
		"taxResidentType"
		})
public class Appnt implements Serializable  {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 新核心客户号
	 */
	private String NewCustId;
	/**
	 * 老核心客户号
	 */
	private String OldCustId;
	/**
	 * 生存状态
	 */
	private String LifeStatus;
	/**
	 * 是否高风险客户
	 */
	private String IsHighRiskCust;	
	/**
	 * 姓名
	 */
	private String CustName;
	/**
	 * 性别
	 */
	private String CustGender;
	/**
	 * 国籍代码
	 */
	private String CountryCode;
	/**
	 * 国籍名称
	 */
	private String CountryNmae;
	/**
	 * 出生日期
	 */
	private String CustBirthday;	
	/**
	 * 证件类型代码
	 */
	private String CustCertiType;	
	/**
	 * 证件类型描述
	 */
	private String CustCertiName;
	/**
	 * 证件号码
	 */
	private String CustCertiCode;	
	/**
	 * 证件有效期起期
	 */
	private String CustCertiStartDate;	
	/**
	 * 证件有效期止期
	 */
	private String CustCertiEndDate;	
	/**
	 * 移动电话
	 */
	private String Mobile;
	/**
	 * 固定电话
	 */
	private String FixedPhone;
	/**
	 * 职业名称
	 */
	private String JobName;	
	/**
	 * 职业编码
	 */
	private String JobCode;	
	/**
	 * 	职业类别代码
	 */
	private String JobUwLevel;	
	/**
	 * 工作单位
	 */
	private String Commpany;	
	/**
	 * 省
	 */
	private String Province;
	/**
	 * 省编码
	 */
	private String ProvinceId;	
	/**
	 * 市
	 */
	private String City;
		
	/**
	 * 市编码
	 */
	private String CityId;	
	/**
	 * 区
	 */
	private String District;	
		
	/**
	 * 区编码
	 */
	private String DistrictId;	
	/*技术需求任务 #166558 新核心-接口需求-移动保全2.0-新增职业信息变更功能-街道 start*/
	/**
	 * 详细地址
	 */
	private String Address;
	/**
	 * 	街道/镇
	 */
	private String street;
	/**
	 * 	街道/镇 码值
	 */
	private String streetCode;
	/*技术需求任务 #166558 新核心-接口需求-移动保全2.0-新增职业信息变更功能-街道 end*/
	/**
	 * 个人税收居民身份标识
	 */
	private String TaxResidentType;
	
	@XmlElement(name="street")
	public String getStreet() {
		return street;
	}
	public void setStreet(String street) {
		this.street = street;
	}
	@XmlElement(name="streetCode")
	public String getStreetCode() {
		return streetCode;
	}
	public void setStreetCode(String streetCode) {
		this.streetCode = streetCode;
	}
	@XmlElement(name="NewCustId")
	public String getNewCustId() {
		return NewCustId;
	}
	public void setNewCustId(String newCustId) {
		NewCustId = newCustId;
	}
	@XmlElement(name="OldCustId")
	public String getOldCustId() {
		return OldCustId;
	}
	public void setOldCustId(String oldCustId) {
		OldCustId = oldCustId;
	}
	@XmlElement(name="LifeStatus")
	public String getLifeStatus() {
		return LifeStatus;
	}
	public void setLifeStatus(String lifeStatus) {
		LifeStatus = lifeStatus;
	}
	@XmlElement(name="IsHighRiskCust")
	public String getIsHighRiskCust() {
		return IsHighRiskCust;
	}
	public void setIsHighRiskCust(String isHighRiskCust) {
		IsHighRiskCust = isHighRiskCust;
	}
	@XmlElement(name="CustName")
	public String getCustName() {
		return CustName;
	}
	public void setCustName(String custName) {
		CustName = custName;
	}
	@XmlElement(name="CustGender")
	public String getCustGender() {
		return CustGender;
	}
	public void setCustGender(String custGender) {
		CustGender = custGender;
	}
	@XmlElement(name="CountryCode")
	public String getCountryCode() {
		return CountryCode;
	}
	public void setCountryCode(String countryCode) {
		CountryCode = countryCode;
	}
	@XmlElement(name="CountryNmae")
	public String getCountryNmae() {
		return CountryNmae;
	}
	public void setCountryNmae(String countryNmae) {
		CountryNmae = countryNmae;
	}
	@XmlElement(name="CustBirthday")
	public String getCustBirthday() {
		return CustBirthday;
	}
	public void setCustBirthday(String custBirthday) {
		CustBirthday = custBirthday;
	}
	@XmlElement(name="CustCertiType")
	public String getCustCertiType() {
		return CustCertiType;
	}
	public void setCustCertiType(String custCertiType) {
		CustCertiType = custCertiType;
	}
	@XmlElement(name="CustCertiName")
	public String getCustCertiName() {
		return CustCertiName;
	}
	public void setCustCertiName(String custCertiName) {
		CustCertiName = custCertiName;
	}
	@XmlElement(name="CustCertiCode")
	public String getCustCertiCode() {
		return CustCertiCode;
	}
	public void setCustCertiCode(String custCertiCode) {
		CustCertiCode = custCertiCode;
	}
	@XmlElement(name="CustCertiStartDate")
	public String getCustCertiStartDate() {
		return CustCertiStartDate;
	}
	public void setCustCertiStartDate(String custCertiStartDate) {
		CustCertiStartDate = custCertiStartDate;
	}
	@XmlElement(name="CustCertiEndDate")
	public String getCustCertiEndDate() {
		return CustCertiEndDate;
	}
	public void setCustCertiEndDate(String custCertiEndDate) {
		CustCertiEndDate = custCertiEndDate;
	}
	@XmlElement(name="Mobile")
	public String getMobile() {
		return Mobile;
	}
	public void setMobile(String mobile) {
		Mobile = mobile;
	}
	@XmlElement(name="FixedPhone")
	public String getFixedPhone() {
		return FixedPhone;
	}
	public void setFixedPhone(String fixedPhone) {
		FixedPhone = fixedPhone;
	}
	@XmlElement(name="JobName")
	public String getJobName() {
		return JobName;
	}
	public void setJobName(String jobName) {
		JobName = jobName;
	}
	@XmlElement(name="JobCode")
	public String getJobCode() {
		return JobCode;
	}
	public void setJobCode(String jobCode) {
		JobCode = jobCode;
	}
	@XmlElement(name="JobUwLevel")
	public String getJobUwLevel() {
		return JobUwLevel;
	}
	public void setJobUwLevel(String jobUwLevel) {
		JobUwLevel = jobUwLevel;
	}
	@XmlElement(name="Commpany")
	public String getCommpany() {
		return Commpany;
	}
	public void setCommpany(String commpany) {
		Commpany = commpany;
	}
	@XmlElement(name="Province")
	public String getProvince() {
		return Province;
	}
	public void setProvince(String province) {
		Province = province;
	}
	@XmlElement(name="ProvinceId")
	public String getProvinceId() {
		return ProvinceId;
	}
	public void setProvinceId(String provinceId) {
		ProvinceId = provinceId;
	}
	@XmlElement(name="City")
	public String getCity() {
		return City;
	}
	public void setCity(String city) {
		City = city;
	}
	@XmlElement(name="CityId")
	public String getCityId() {
		return CityId;
	}
	public void setCityId(String cityId) {
		CityId = cityId;
	}
	@XmlElement(name="District")
	public String getDistrict() {
		return District;
	}
	public void setDistrict(String district) {
		District = district;
	}
	@XmlElement(name="DistrictId")
	public String getDistrictId() {
		return DistrictId;
	}
	public void setDistrictId(String districtId) {
		DistrictId = districtId;
	}
	@XmlElement(name="Address")
	public String getAddress() {
		return Address;
	}
	public void setAddress(String address) {
		Address = address;
	}
	@XmlElement(name="TaxResidentType")
	public String getTaxResidentType() {
		return TaxResidentType;
	}
	public void setTaxResidentType(String taxResidentType) {
		TaxResidentType = taxResidentType;
	}
	
	
	
}
