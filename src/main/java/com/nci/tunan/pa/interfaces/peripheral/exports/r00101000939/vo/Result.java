package com.nci.tunan.pa.interfaces.peripheral.exports.r00101000939.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class Result implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -742822630410931924L;
	// 险种代码
	private String riskCode;
	// 账户类型
	private String busyType;
	// UnitPrice
	private String unitPrice;
	// 计算日期
	private String calDate;
	@XmlElement(name="RiskCode")
	public String getRiskCode() {
		return riskCode;
	}
	public void setRiskCode(String riskCode) {
		this.riskCode = riskCode;
	}
	@XmlElement(name="BusyType")
	public String getBusyType() {
		return busyType;
	}
	public void setBusyType(String busyType) {
		this.busyType = busyType;
	}
	@XmlElement(name="UnitPrice")
	public String getUnitPrice() {
		return unitPrice;
	}
	public void setUnitPrice(String unitPrice) {
		this.unitPrice = unitPrice;
	}
	@XmlElement(name="CalDate")
	public String getCalDate() {
		return calDate;
	}
	public void setCalDate(String calDate) {
		this.calDate = calDate;
	}
	
	
}