package com.nci.tunan.pa.interfaces.peripheral.exports.r06402900875.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class InputData implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String applyCode;	//投保单号
	private String policyCode;	//保单号
	private String partiAppSheetNo;	//申请单编号
	private String platInvestAcc;	//银保信账户
	private String accountBank;	//开户行代码
	private String accountName;	//账户名称
	private String account;	//账户号
	private String deductType;	//扣款类型
	private String deductAmount;	//扣款金额
	private String deductPeriod;	//扣款频率
	private String limitTerm;	//有效期限
	private String productCode;	//产品代码
	private String status;	//绑定状态
	
	@XmlElement(name="ApplyCode")
	public String getApplyCode() {
		return applyCode;
	}
	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}
	@XmlElement(name="PolicyCode")
	public String getPolicyCode() {
		return policyCode;
	}
	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}
	@XmlElement(name="PartiAppSheetNo")
	public String getPartiAppSheetNo() {
		return partiAppSheetNo;
	}
	public void setPartiAppSheetNo(String partiAppSheetNo) {
		this.partiAppSheetNo = partiAppSheetNo;
	}
	@XmlElement(name="PlatInvestAcc")
	public String getPlatInvestAcc() {
		return platInvestAcc;
	}
	public void setPlatInvestAcc(String platInvestAcc) {
		this.platInvestAcc = platInvestAcc;
	}
	@XmlElement(name="AccountBank")
	public String getAccountBank() {
		return accountBank;
	}
	public void setAccountBank(String accountBank) {
		this.accountBank = accountBank;
	}
	@XmlElement(name="AccountName")
	public String getAccountName() {
		return accountName;
	}
	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}
	@XmlElement(name="Account")
	public String getAccount() {
		return account;
	}
	public void setAccount(String account) {
		this.account = account;
	}
	@XmlElement(name="DeductType")
	public String getDeductType() {
		return deductType;
	}
	public void setDeductType(String deductType) {
		this.deductType = deductType;
	}
	@XmlElement(name="DeductAmount")
	public String getDeductAmount() {
		return deductAmount;
	}
	public void setDeductAmount(String deductAmount) {
		this.deductAmount = deductAmount;
	}
	@XmlElement(name="DeductPeriod")
	public String getDeductPeriod() {
		return deductPeriod;
	}
	public void setDeductPeriod(String deductPeriod) {
		this.deductPeriod = deductPeriod;
	}
	@XmlElement(name="LimitTerm")
	public String getLimitTerm() {
		return limitTerm;
	}
	public void setLimitTerm(String limitTerm) {
		this.limitTerm = limitTerm;
	}
	@XmlElement(name="ProductCode")
	public String getProductCode() {
		return productCode;
	}
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	@XmlElement(name="Status")
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	@Override
	public String toString() {
		return "InputData [applyCode=" + applyCode + ", policyCode="
				+ policyCode + ", partiAppSheetNo=" + partiAppSheetNo
				+ ", platInvestAcc=" + platInvestAcc + ", accountBank="
				+ accountBank + ", accountName=" + accountName + ", account="
				+ account + ", deductType=" + deductType + ", deductAmount="
				+ deductAmount + ", deductPeriod=" + deductPeriod
				+ ", limitTerm=" + limitTerm + ", productCode=" + productCode
				+ ", status=" + status + "]";
	}
	
	
}
