package com.nci.tunan.pa.interfaces.peripheral.exports.r00101000184.vo;

import java.io.Serializable;
import java.util.Date;

import javax.xml.bind.annotation.XmlElement;

public class InputData implements Serializable{

	//保单号
	private String contNo;
	//投保人姓名
	private String appName;
	//收费类型
	private String receiveFeeCode;
	//保单起始日期
	private Date startPayDate;
	//保单结束日期
	private Date endPayDate;
	//业务员编号
	private String agentCode;

	@XmlElement(name = "ContNo")
	public String getContNo() {
		return contNo;
	}

	public void setContNo(String contNo) {
		this.contNo = contNo;
	}

	@XmlElement(name = "AppName")
	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	@XmlElement(name = "ReceiveFeeCode")
	public String getReceiveFeeCode() {
		return receiveFeeCode;
	}

	public void setReceiveFeeCode(String receiveFeeCode) {
		this.receiveFeeCode = receiveFeeCode;
	}

	@XmlElement(name = "StartPayDate")
	public Date getStartPayDate() {
		return startPayDate;
	}

	public void setStartPayDate(Date startPayDate) {
		this.startPayDate = startPayDate;
	}

	@XmlElement(name = "EndPayDate")
	public Date getEndPayDate() {
		return endPayDate;
	}

	public void setEndPayDate(Date endPayDate) {
		this.endPayDate = endPayDate;
	}

	@XmlElement(name = "AgentCode")
	public String getAgentCode() {
		return agentCode;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

}
