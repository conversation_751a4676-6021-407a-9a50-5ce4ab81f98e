package com.nci.tunan.pa.interfaces.add.exports.createpolicy.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.nci.udmp.framework.model.BaseVO;

/**
 * ContractAgentVO对象
 * @description 
 * <AUTHOR> <EMAIL> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2017年8月16日 上午9:51:42
 */
public class ContractAgentVO extends BaseVO implements Serializable {

	private static final long serialVersionUID = 6571913940655197859L;

	/**
	 * @Fields agentName : null
	 */
	private String agentName;
	/**
	 * @Fields agentStartDate : null
	 */
	private Date agentStartDate;
	/**
	 * @Fields agentMobile : null
	 */
	private String agentMobile;
	/**
	 * @Fields relationToPh : null
	 */
	private String relationToPh;
	/**
	 * @Fields agentType : null
	 */
	private BigDecimal agentType;
	/**
	 * @Fields applyCode : null
	 */
	private String applyCode;
	/**
	 * @Fields policyCode : null
	 */
	private String policyCode;
	/**
	 * @Fields agentEndDate : null
	 */
	private Date agentEndDate;
	/**
	 * @Fields listId : null
	 */
	private BigDecimal listId;
	/**
	 * @Fields agentOrganCode : null
	 */
	private String  agentOrganCode;
	/**
	 * @Fields policyId : null
	 */
	private BigDecimal policyId;
	/**
	 * @Fields agentCode : null
	 */
	private String agentCode;
	
	private String lastAgentCode;
	private String lastAgentName;
	private BigDecimal isCurrentAgent;
	private BigDecimal isNbAgent;
	private String organCode;
	private String channelType;
    //需求变更增加字段 #52031
	private String cooperationCode;
	
	private String cooperationName;
	/**
	 * @Fields 合作中介机构协议号
	 */
	private String  cooperationProtocolId;
	
	//需求变更新增字段结束
	public String getLastAgentCode() {
		return lastAgentCode;
	}

	public void setLastAgentCode(String lastAgentCode) {
		this.lastAgentCode = lastAgentCode;
	}

	public String getLastAgentName() {
		return lastAgentName;
	}

	public void setLastAgentName(String lastAgentName) {
		this.lastAgentName = lastAgentName;
	}

	public BigDecimal getIsCurrentAgent() {
		return isCurrentAgent;
	}

	public void setIsCurrentAgent(BigDecimal isCurrentAgent) {
		this.isCurrentAgent = isCurrentAgent;
	}

	public BigDecimal getIsNbAgent() {
		return isNbAgent;
	}

	public void setIsNbAgent(BigDecimal isNbAgent) {
		this.isNbAgent = isNbAgent;
	}

	public String getOrganCode() {
		return organCode;
	}

	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}



	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentStartDate(Date agentStartDate) {
		this.agentStartDate = agentStartDate;
	}

	public Date getAgentStartDate() {
		return agentStartDate;
	}

	public void setAgentMobile(String agentMobile) {
		this.agentMobile = agentMobile;
	}

	public String getAgentMobile() {
		return agentMobile;
	}

	public void setRelationToPh(String relationToPh) {
		this.relationToPh = relationToPh;
	}

	public String getRelationToPh() {
		return relationToPh;
	}

	public void setAgentType(BigDecimal agentType) {
		this.agentType = agentType;
	}

	public BigDecimal getAgentType() {
		return agentType;
	}

	public void setApplyCode(String applyCode) {
		this.applyCode = applyCode;
	}

	public String getApplyCode() {
		return applyCode;
	}

	public void setPolicyCode(String policyCode) {
		this.policyCode = policyCode;
	}

	public String getPolicyCode() {
		return policyCode;
	}

	public void setAgentEndDate(Date agentEndDate) {
		this.agentEndDate = agentEndDate;
	}

	public Date getAgentEndDate() {
		return agentEndDate;
	}

	public void setListId(BigDecimal listId) {
		this.listId = listId;
	}

	public BigDecimal getListId() {
		return listId;
	}


	public String getAgentOrganCode() {
		return agentOrganCode;
	}

	public void setAgentOrganCode(String agentOrganCode) {
		this.agentOrganCode = agentOrganCode;
	}

	public void setPolicyId(BigDecimal policyId) {
		this.policyId = policyId;
	}

	public BigDecimal getPolicyId() {
		return policyId;
	}

	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}

	public String getAgentCode() {
		return agentCode;
	}
	

	public String getCooperationCode() {
		return cooperationCode;
	}

	public void setCooperationCode(String cooperationCode) {
		this.cooperationCode = cooperationCode;
	}

	public String getCooperationName() {
		return cooperationName;
	}

	public void setCooperationName(String cooperationName) {
		this.cooperationName = cooperationName;
	}

	public String getCooperationProtocolId() {
		return cooperationProtocolId;
	}

	public void setCooperationProtocolId(String cooperationProtocolId) {
		this.cooperationProtocolId = cooperationProtocolId;
	}

	@Override
	public String getBizId() {
		return null;
	}

	@Override
	public String toString() {
		return "ContractAgentVO [agentName=" + agentName + ", agentStartDate="
				+ agentStartDate + ", agentMobile=" + agentMobile
				+ ", relationToPh=" + relationToPh + ", agentType=" + agentType
				+ ", applyCode=" + applyCode + ", policyCode=" + policyCode
				+ ", agentEndDate=" + agentEndDate + ", listId=" + listId
				+ ", agentOrganCode=" + agentOrganCode + ", policyId="
				+ policyId + ", agentCode=" + agentCode + ", lastAgentCode="
				+ lastAgentCode + ", lastAgentName=" + lastAgentName
				+ ", isCurrentAgent=" + isCurrentAgent + ", isNbAgent="
				+ isNbAgent + ", organCode=" + organCode + ", channelType="
				+ channelType + ", cooperationCode=" + cooperationCode
				+ ", cooperationName=" + cooperationName
				+ ", cooperationProtocolId=" + cooperationProtocolId + "]";
	}

}
