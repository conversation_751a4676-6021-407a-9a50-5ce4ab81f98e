package com.nci.tunan.pa.interfaces.peripheral.exports.r00101002633.vo;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class Result {

	//保单号
	private String contNo;	
	//投保单号
	private String prtNo;	
	//保单生效日
	private String cvaliDate;	
	//投保人姓名
	private String appName;	
	//被保人姓名
	private String insName;	
	//投保人联系电话
	private String appntPhone;	
	//险种信息
	private List<RiskInfo> riskInfo;
	
	//产品组合名称
	private String contPlanName;
	//销售渠道名称
	private String saleChnlName;
	//下次交费对应日
	private String payToDate;
	//交费次数
	private String paycount;
	//续期代收银行代码
	private String bankCode;
	//续期银行账户
	private String bankAcc;	
	//续期户名
	private String bankAccName;	
	//续期交费形式代码
	private String payLocationCode;
	//续期交费形式
	private String payLocation;
	//续期银行名称
	private String bankName;
	//当前服务人员代码
	private String agentCode;
	//当前服务人员姓名
	private String agentName;
	//当前服务人员性别
	private String agentSex;
	//当前服务人员状态代码
	private String agentStateCode;
	//当前服务人员状态名称
	private String agentState;
	//当前服务人员手机号
	private String mobile; 
	//是否税延型保险标识
	private String isTax;
	//投被保人关系
	private String appntRelationToInsured;
	//是否有税收标识
	private String isTaxRevenue;
	//保单产品是否属于CRS产品范围标识
	private String isCrs;
	//保单状态
	private String ContState;
	//专户投保标识
	private String specialinsureflag;

	@XmlElement(name="ContNo")
	public String getContNo() {
		return contNo;
	}
	public void setContNo(String contNo) {
		this.contNo = contNo;
	}
	
	@XmlElement(name="PrtNo")
	public String getPrtNo() {
		return prtNo;
	}
	public void setPrtNo(String prtNo) {
		this.prtNo = prtNo;
	}
	
	@XmlElement(name="CvaliDate")
	public String getCvaliDate() {
		return cvaliDate;
	}
	public void setCvaliDate(String cvaliDate) {
		this.cvaliDate = cvaliDate;
	}
	
	@XmlElement(name="AppName")
	public String getAppName() {
		return appName;
	}
	public void setAppName(String appName) {
		this.appName = appName;
	}
	
	@XmlElement(name="InsName")
	public String getInsName() {
		return insName;
	}
	public void setInsName(String insName) {
		this.insName = insName;
	}
	
	@XmlElement(name="AppntPhone")
	public String getAppntPhone() {
		return appntPhone;
	}
	public void setAppntPhone(String appntPhone) {
		this.appntPhone = appntPhone;
	}
	
	@XmlElement(name="RiskInfo")
	public List<RiskInfo> getRiskInfo() {
		return riskInfo;
	}
	public void setRiskInfo(List<RiskInfo> riskInfo) {
		this.riskInfo = riskInfo;
	}
	
	@XmlElement(name="ContPlanName")
	public String getContPlanName() {
		return contPlanName;
	}
	public void setContPlanName(String contPlanName) {
		this.contPlanName = contPlanName;
	}
	
	@XmlElement(name="SaleChnlName")
	public String getSaleChnlName() {
		return saleChnlName;
	}
	public void setSaleChnlName(String saleChnlName) {
		this.saleChnlName = saleChnlName;
	}
	
	@XmlElement(name="PayToDate")
	public String getPayToDate() {
		return payToDate;
	}
	public void setPayToDate(String payToDate) {
		this.payToDate = payToDate;
	}
	
	@XmlElement(name="Paycount")
	public String getPaycount() {
		return paycount;
	}
	public void setPaycount(String paycount) {
		this.paycount = paycount;
	}
	
	@XmlElement(name="BankCode")
	public String getBankCode() {
		return bankCode;
	}
	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}
	
	@XmlElement(name="BankAcc")
	public String getBankAcc() {
		return bankAcc;
	}
	public void setBankAcc(String bankAcc) {
		this.bankAcc = bankAcc;
	}
	
	@XmlElement(name="BankAccName")
	public String getBankAccName() {
		return bankAccName;
	}
	public void setBankAccName(String bankAccName) {
		this.bankAccName = bankAccName;
	}
	
	@XmlElement(name="PayLocationCode")
	public String getPayLocationCode() {
		return payLocationCode;
	}
	public void setPayLocationCode(String payLocationCode) {
		this.payLocationCode = payLocationCode;
	}
	
	@XmlElement(name="PayLocation")
	public String getPayLocation() {
		return payLocation;
	}
	public void setPayLocation(String payLocation) {
		this.payLocation = payLocation;
	}
	
	@XmlElement(name="BankName")
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	
	@XmlElement(name="AgentCode")
	public String getAgentCode() {
		return agentCode;
	}
	public void setAgentCode(String agentCode) {
		this.agentCode = agentCode;
	}
	
	@XmlElement(name="AgentName")
	public String getAgentName() {
		return agentName;
	}
	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}
	
	@XmlElement(name="AgentSex")
	public String getAgentSex() {
		return agentSex;
	}
	public void setAgentSex(String agentSex) {
		this.agentSex = agentSex;
	}
	
	@XmlElement(name="AgentStateCode")
	public String getAgentStateCode() {
		return agentStateCode;
	}
	public void setAgentStateCode(String agentStateCode) {
		this.agentStateCode = agentStateCode;
	}
	
	@XmlElement(name="AgentState")
	public String getAgentState() {
		return agentState;
	}
	public void setAgentState(String agentState) {
		this.agentState = agentState;
	}
	
	@XmlElement(name="Mobile")
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	@XmlElement(name="isTax")
	public String getIsTax() {
		return isTax;
	}
	public void setIsTax(String isTax) {
		this.isTax = isTax;
	}
	@XmlElement(name="AppntRelationToInsured")
	public String getAppntRelationToInsured() {
		return appntRelationToInsured;
	}
	public void setAppntRelationToInsured(String appntRelationToInsured) {
		this.appntRelationToInsured = appntRelationToInsured;
	}
	@XmlElement(name="isTaxRevenue")
	public String getIsTaxRevenue() {
		return isTaxRevenue;
	}
	public void setIsTaxRevenue(String isTaxRevenue) {
		this.isTaxRevenue = isTaxRevenue;
	}
	@XmlElement(name="isCrs")
	public String getIsCrs() {
		return isCrs;
	}
	public void setIsCrs(String isCrs) {
		this.isCrs = isCrs;
	}
	@XmlElement(name="ContState")
	public String getContState() {
		return ContState;
	}
	public void setContState(String contState) {
		ContState = contState;
	}
	@XmlElement(name="SpecialInsureFlag")
	public String getSpecialinsureflag() {
		return specialinsureflag;
	}
	public void setSpecialinsureflag(String specialinsureflag) {
		this.specialinsureflag = specialinsureflag;
	}



	
	
	
}
