package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900549.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

import java.util.List;

public class AdditionRiskList implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	private List<AdditionRisk> AdditionRisk;

	@XmlElement(name = "AdditionRisk")
	public List<AdditionRisk> getAdditionRisk() {
		return AdditionRisk;
	}

	public void setAdditionRisk(List<AdditionRisk> additionRisk) {
		AdditionRisk = additionRisk;
	}
	
}
