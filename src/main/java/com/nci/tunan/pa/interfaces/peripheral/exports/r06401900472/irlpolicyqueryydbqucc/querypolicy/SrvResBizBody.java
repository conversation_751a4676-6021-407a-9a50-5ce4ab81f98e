package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900472.irlpolicyqueryydbqucc.querypolicy;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody")
public class SrvResBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(name = "OutputData")
    protected com.nci.tunan.pa.interfaces.peripheral.exports.r06401900472.vo.OutputData outputData;

    public com.nci.tunan.pa.interfaces.peripheral.exports.r06401900472.vo.OutputData getOutputData() {
        return outputData;
    }
    public void setOutputData(com.nci.tunan.pa.interfaces.peripheral.exports.r06401900472.vo.OutputData outputData) {
        this.outputData = outputData;
    }
}



