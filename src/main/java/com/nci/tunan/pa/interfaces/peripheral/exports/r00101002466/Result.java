package com.nci.tunan.pa.interfaces.peripheral.exports.r00101002466;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class Result {

	
	//销售渠道
	private String SaleChnlName;	
	//	业务员名称
	private String AgentName;	
	//	业务员代码
	private String AgentCode;	
	//	服务状态
	private String ServeState;	
	//	投保人邮编
	private String ClientGrade;	
	//	客户级别
	private String AppntPostCode;	
	//	投保人地址
	private String AppntAddress;	
	//	投保人姓名
	private String AppntName;	
	//	投保人性别
	private String AppntSex;	
	//	会计年度
	private String AccountYear;	
	//	分红险种名称
	private String BonusRiskName;	
	//	保单号
	private String PolicyNo;	
	//	被保人姓名
	private String InsuredName;	
	//	保单生效日期
	private String Cvalidate;	
	//	基本责任名称
	private String BasicDutyName;	
	//	可选责任名称
	private String OptionalDutyName;	
	//	红利信息
	private List<BounsInfo>  BounsInfos;	
	//	分红年度
	private String BonusYear;	
	//	年度红利保额（元）
	private String AnnvBonusAmount;	
	//	现金价值（元）
	private String CashValue;	
	//	终了红利（元）
	private String FinalBonus;	
	//	单证生成日期
	private String DocumentDate;	
	//	业务员所在的分公司名称
	private String BranchName;	
	//	支公司
	private String SubBranchCompany;	
	//	营业部
	private String BusinessDeptName;	
	//	银行网点名称
	private String BankWebName;	
	//	银行网点代码
	private String BankWebCode;	
	//	总公司地址
	private String HeadOfficeAddress;	
	//	总公司邮政编码
	private String HeadOfficePostCode;	
	//	通知书名称
	private String NoticeName;	
	//	公司网址
	private String CompanyWebSite;
	//公司咨询电话
	private String CompanyPhone;
	//年度
	private String SvlYear;
	//季度
	private String SvlQuarter;
	//综合偿付能力充足率
	private String SvlRate;
	//风险综合评级
	private String SvlRisk;
	//领取方式
	private String ReceiveMode;
	//历史红利合计
	private String SumHisSumBonus;
	//历史利息合计
	private String SumHisIntr;
	//已经领取的红利账户本利和
	private String SumReceivedBonus;
	//本次分红前的红利账户本利和
    private String SumBeforeBonus;
    //报告期间
    private String ReportPeriod;
    //红利应派发日
    private String SDispatchDate;
    //红利实际派发日
    private String ADispatchDate;
    //本期红利
    private String CashBonus;
    //迟发利息
    private String DelayIntrest;
    //年利率
    private String YearRate;
    private String IsBasicDuty;
    //标准保费
    private String PayStandard;
    
    
    
    
    //以下为分红通知书打印新增字段
    private String distrChannel;//销售渠道
    private String serviceState;//服务状态
    private String customerLevel;//客户级别
    private String dividendCategory;//红利类别
    private String insuranceCode;//险种编码
    private String servicPerEchannels;//现有服务人员渠道
    private String VIPIdentification; //vip标识
    private String policySerIdentification;//保单服务状态标识
    private String formPayment ;//交费形式
    private String lastPremium;//最近一次已交保费
    private String branchAddress;//分公司地址
    private String businessDistrict;//营业区
    private String businessGroup;//营业组
    private String bankOutlets;//银行网点
    private String bankCode;//网点代码

    @XmlElement(name = "DistrChannel")
    public String getDistrChannel() {
		return distrChannel;
	}
	public void setDistrChannel(String distrChannel) {
		this.distrChannel = distrChannel;
	}
	@XmlElement(name = "ServiceState")
	public String getServiceState() {
		return serviceState;
	}
	public void setServiceState(String serviceState) {
		this.serviceState = serviceState;
	}
	@XmlElement(name = "CustomerLevel")
	public String getCustomerLevel() {
		return customerLevel;
	}
	public void setCustomerLevel(String customerLevel) {
		this.customerLevel = customerLevel;
	}
	@XmlElement(name = "DividendCategory")
	public String getDividendCategory() {
		return dividendCategory;
	}
	public void setDividendCategory(String dividendCategory) {
		this.dividendCategory = dividendCategory;
	}
	@XmlElement(name = "InsuranceCode")
	public String getInsuranceCode() {
		return insuranceCode;
	}
	public void setInsuranceCode(String insuranceCode) {
		this.insuranceCode = insuranceCode;
	}
	@XmlElement(name = "ServicPerEchannels")
	public String getServicPerEchannels() {
		return servicPerEchannels;
	}
	public void setServicPerEchannels(String servicPerEchannels) {
		this.servicPerEchannels = servicPerEchannels;
	}
	@XmlElement(name = "VIPIdentification")
	public String getVIPIdentification() {
		return VIPIdentification;
	}
	public void setVIPIdentification(String vIPIdentification) {
		VIPIdentification = vIPIdentification;
	}
	@XmlElement(name = "PolicySerIdentification")
	public String getPolicySerIdentification() {
		return policySerIdentification;
	}
	public void setPolicySerIdentification(String policySerIdentification) {
		this.policySerIdentification = policySerIdentification;
	}
	@XmlElement(name = "FormPayment")
	public String getFormPayment() {
		return formPayment;
	}
	public void setFormPayment(String formPayment) {
		this.formPayment = formPayment;
	}
	@XmlElement(name = "LastPremium")
	public String getLastPremium() {
		return lastPremium;
	}
	public void setLastPremium(String lastPremium) {
		this.lastPremium = lastPremium;
	}
	@XmlElement(name = "BranchAddress")
	public String getBranchAddress() {
		return branchAddress;
	}
	public void setBranchAddress(String branchAddress) {
		this.branchAddress = branchAddress;
	}
	@XmlElement(name = "BusinessDistrict")
	public String getBusinessDistrict() {
		return businessDistrict;
	}
	public void setBusinessDistrict(String businessDistrict) {
		this.businessDistrict = businessDistrict;
	}
	@XmlElement(name = "BusinessGroup")
	public String getBusinessGroup() {
		return businessGroup;
	}
	public void setBusinessGroup(String businessGroup) {
		this.businessGroup = businessGroup;
	}
	@XmlElement(name = "BankOutlets")
	public String getBankOutlets() {
		return bankOutlets;
	}
	public void setBankOutlets(String bankOutlets) {
		this.bankOutlets = bankOutlets;
	}
	@XmlElement(name = "BankCode")
	public String getBankCode() {
		return bankCode;
	}
	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}
	//新增代码结束
	@XmlElement(name = "IsBasicDuty")
	public String getIsBasicDuty() {
		return IsBasicDuty;
	}
	public void setIsBasicDuty(String isBasicDuty) {
		IsBasicDuty = isBasicDuty;
	}


	@XmlElement(name = "SaleChnlName")
	public String getSaleChnlName() {
		return SaleChnlName;
	}
	public void setSaleChnlName(String saleChnlName) {
		SaleChnlName = saleChnlName;
	}
	@XmlElement(name = "AgentName")
	public String getAgentName() {
		return AgentName;
	}
	public void setAgentName(String agentName) {
		AgentName = agentName;
	}
	@XmlElement(name = "AgentCode")
	public String getAgentCode() {
		return AgentCode;
	}
	public void setAgentCode(String agentCode) {
		AgentCode = agentCode;
	}
	@XmlElement(name = "ServeState")
	public String getServeState() {
		return ServeState;
	}
	public void setServeState(String serveState) {
		ServeState = serveState;
	}
	@XmlElement(name = "ClientGrade")
	public String getClientGrade() {
		return ClientGrade;
	}
	public void setClientGrade(String clientGrade) {
		ClientGrade = clientGrade;
	}
	@XmlElement(name = "AppntPostCode")
	public String getAppntPostCode() {
		return AppntPostCode;
	}
	public void setAppntPostCode(String appntPostCode) {
		AppntPostCode = appntPostCode;
	}
	@XmlElement(name = "AppntAddress")
	public String getAppntAddress() {
		return AppntAddress;
	}
	public void setAppntAddress(String appntAddress) {
		AppntAddress = appntAddress;
	}
	@XmlElement(name = "AppntName")
	public String getAppntName() {
		return AppntName;
	}
	public void setAppntName(String appntName) {
		AppntName = appntName;
	}
	@XmlElement(name = "AppntSex")
	public String getAppntSex() {
		return AppntSex;
	}
	public void setAppntSex(String appntSex) {
		AppntSex = appntSex;
	}
	@XmlElement(name = "AccountYear")
	public String getAccountYear() {
		return AccountYear;
	}
	public void setAccountYear(String accountYear) {
		AccountYear = accountYear;
	}
	@XmlElement(name = "BonusRiskName")
	public String getBonusRiskName() {
		return BonusRiskName;
	}
	public void setBonusRiskName(String bonusRiskName) {
		BonusRiskName = bonusRiskName;
	}
	@XmlElement(name = "PolicyNo")
	public String getPolicyNo() {
		return PolicyNo;
	}
	public void setPolicyNo(String policyNo) {
		PolicyNo = policyNo;
	}
	@XmlElement(name = "InsuredName")
	public String getInsuredName() {
		return InsuredName;
	}
	public void setInsuredName(String insuredName) {
		InsuredName = insuredName;
	}
	@XmlElement(name = "Cvalidate")
	public String getCvalidate() {
		return Cvalidate;
	}
	public void setCvalidate(String cvalidate) {
		Cvalidate = cvalidate;
	}
	@XmlElement(name = "BasicDutyName")
	public String getBasicDutyName() {
		return BasicDutyName;
	}
	public void setBasicDutyName(String basicDutyName) {
		BasicDutyName = basicDutyName;
	}
	@XmlElement(name = "OptionalDutyName")
	public String getOptionalDutyName() {
		return OptionalDutyName;
	}
	public void setOptionalDutyName(String optionalDutyName) {
		OptionalDutyName = optionalDutyName;
	}
	@XmlElement(name = "BounsInfo")
	public void setBounsInfos(List<BounsInfo> bounsInfos) {
		BounsInfos = bounsInfos;
	}
	public void setBonusYear(String bonusYear) {
		BonusYear = bonusYear;
	}
	@XmlElement(name = "BonusYear")
	public String getBonusYear() {
		return BonusYear;
	}
	public List<BounsInfo> getBounsInfos() {
		return BounsInfos;
	}
	@XmlElement(name = "AnnvBonusAmount")
	public String getAnnvBonusAmount() {
		return AnnvBonusAmount;
	}
	public void setAnnvBonusAmount(String annvBonusAmount) {
		AnnvBonusAmount = annvBonusAmount;
	}
	@XmlElement(name = "CashValue")
	public String getCashValue() {
		return CashValue;
	}
	public void setCashValue(String cashValue) {
		CashValue = cashValue;
	}
	@XmlElement(name = "FinalBonus")
	public String getFinalBonus() {
		return FinalBonus;
	}
	public void setFinalBonus(String finalBonus) {
		FinalBonus = finalBonus;
	}
	@XmlElement(name = "DocumentDate")
	public String getDocumentDate() {
		return DocumentDate;
	}
	public void setDocumentDate(String documentDate) {
		DocumentDate = documentDate;
	}
	@XmlElement(name = "BranchName")
	public String getBranchName() {
		return BranchName;
	}
	public void setBranchName(String branchName) {
		BranchName = branchName;
	}
	@XmlElement(name = "SubBranchCompany")
	public String getSubBranchCompany() {
		return SubBranchCompany;
	}
	public void setSubBranchCompany(String subBranchCompany) {
		SubBranchCompany = subBranchCompany;
	}
	@XmlElement(name = "BusinessDeptName")
	public String getBusinessDeptName() {
		return BusinessDeptName;
	}
	public void setBusinessDeptName(String businessDeptName) {
		BusinessDeptName = businessDeptName;
	}
	@XmlElement(name = "BankWebName")
	public String getBankWebName() {
		return BankWebName;
	}
	public void setBankWebName(String bankWebName) {
		BankWebName = bankWebName;
	}
	@XmlElement(name = "BankWebCode")
	public String getBankWebCode() {
		return BankWebCode;
	}
	public void setBankWebCode(String bankWebCode) {
		BankWebCode = bankWebCode;
	}
	@XmlElement(name = "HeadOfficeAddress")
	public String getHeadOfficeAddress() {
		return HeadOfficeAddress;
	}
	public void setHeadOfficeAddress(String headOfficeAddress) {
		HeadOfficeAddress = headOfficeAddress;
	}
	@XmlElement(name = "HeadOfficePostCode")
	public String getHeadOfficePostCode() {
		return HeadOfficePostCode;
	}
	public void setHeadOfficePostCode(String headOfficePostCode) {
		HeadOfficePostCode = headOfficePostCode;
	}
	@XmlElement(name = "NoticeName")
	public String getNoticeName() {
		return NoticeName;
	}
	public void setNoticeName(String noticeName) {
		NoticeName = noticeName;
	}
	@XmlElement(name = "CompanyWebSite")
	public String getCompanyWebSite() {
		return CompanyWebSite;
	}
	public void setCompanyWebSite(String companyWebSite) {
		CompanyWebSite = companyWebSite;
	}
	@XmlElement(name = "CompanyPhone")
	public String getCompanyPhone() {
		return CompanyPhone;
	}
	public void setCompanyPhone(String companyPhone) {
		CompanyPhone = companyPhone;
	}
	@XmlElement(name = "SvlYear")
	public String getSvlYear() {
		return SvlYear;
	}
	public void setSvlYear(String svlYear) {
		SvlYear = svlYear;
	}
	@XmlElement(name = "SvlQuarter")
	public String getSvlQuarter() {
		return SvlQuarter;
	}
	public void setSvlQuarter(String svlQuarter) {
		SvlQuarter = svlQuarter;
	}
	@XmlElement(name = "SvlRate")
	public String getSvlRate() {
		return SvlRate;
	}
	public void setSvlRate(String svlRate) {
		SvlRate = svlRate;
	}
	@XmlElement(name = "SvlRisk")
	public String getSvlRisk() {
		return SvlRisk;
	}
	public void setSvlRisk(String svlRisk) {
		SvlRisk = svlRisk;
	}
	@XmlElement(name = "ReceiveMode")
	public String getReceiveMode() {
		return ReceiveMode;
	}
	public void setReceiveMode(String receiveMode) {
		ReceiveMode = receiveMode;
	}
	@XmlElement(name = "SumHisSumBonus")
	public String getSumHisSumBonus() {
		return SumHisSumBonus;
	}
	public void setSumHisSumBonus(String sumHisSumBonus) {
		SumHisSumBonus = sumHisSumBonus;
	}
	@XmlElement(name = "SumHisIntr")
	public String getSumHisIntr() {
		return SumHisIntr;
	}
	public void setSumHisIntr(String sumHisIntr) {
		SumHisIntr = sumHisIntr;
	}
	@XmlElement(name = "SumReceivedBonus")
	public String getSumReceivedBonus() {
		return SumReceivedBonus;
	}
	public void setSumReceivedBonus(String sumReceivedBonus) {
		SumReceivedBonus = sumReceivedBonus;
	}
	@XmlElement(name = "SumBeforeBonus")
	public String getSumBeforeBonus() {
		return SumBeforeBonus;
	}
	public void setSumBeforeBonus(String sumBeforeBonus) {
		SumBeforeBonus = sumBeforeBonus;
	}
	@XmlElement(name = "ReportPeriod")
	public String getReportPeriod() {
		return ReportPeriod;
	}
	public void setReportPeriod(String reportPeriod) {
		ReportPeriod = reportPeriod;
	}
	@XmlElement(name = "SDispatchDate")
	public String getSDispatchDate() {
		return SDispatchDate;
	}
	public void setSDispatchDate(String sDispatchDate) {
		SDispatchDate = sDispatchDate;
	}
	@XmlElement(name = "ADispatchDate")
	public String getADispatchDate() {
		return ADispatchDate;
	}
	public void setADispatchDate(String aDispatchDate) {
		ADispatchDate = aDispatchDate;
	}
	@XmlElement(name = "CashBonus")
	public String getCashBonus() {
		return CashBonus;
	}
	public void setCashBonus(String cashBonus) {
		CashBonus = cashBonus;
	}
	@XmlElement(name = "DelayIntrest")
	public String getDelayIntrest() {
		return DelayIntrest;
	}
	public void setDelayIntrest(String delayIntrest) {
		DelayIntrest = delayIntrest;
	}
	@XmlElement(name = "YearRate")
	public String getYearRate() {
		return YearRate;
	}
	public void setYearRate(String yearRate) {
		YearRate = yearRate;
	}
	@XmlElement(name = "PayStandard")
	public String getPayStandard() {
		return PayStandard;
	}
	public void setPayStandard(String payStandard) {
		PayStandard = payStandard;
	}
	
	

	
	
	

}
