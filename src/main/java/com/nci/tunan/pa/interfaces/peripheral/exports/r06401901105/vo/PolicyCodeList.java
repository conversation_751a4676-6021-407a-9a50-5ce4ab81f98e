package com.nci.tunan.pa.interfaces.peripheral.exports.r06401901105.vo;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class PolicyCodeList {
	private List<PolicyInfo> policyInfo;	//保单信息

	@XmlElement(name = "policyInfo")
	public List<PolicyInfo> getPolicyInfo() {
		return policyInfo;
	}

	public void setPolicyInfo(List<PolicyInfo> policyInfo) {
		this.policyInfo = policyInfo;
	}
}
