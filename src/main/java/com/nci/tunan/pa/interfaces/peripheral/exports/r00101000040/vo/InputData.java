package com.nci.tunan.pa.interfaces.peripheral.exports.r00101000040.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class InputData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String ContNo;
	
	private String IdNo;
	
	@XmlElement(name="ContNo")
	public String getContNo() {
		return ContNo;
	}

	public void setContNo(String contNo) {
		ContNo = contNo;
	}
	
	@XmlElement(name="IdNo")
	public String getIdNo() {
		return IdNo;
	}

	public void setIdNo(String idNo) {
		IdNo = idNo;
	}
	

}
