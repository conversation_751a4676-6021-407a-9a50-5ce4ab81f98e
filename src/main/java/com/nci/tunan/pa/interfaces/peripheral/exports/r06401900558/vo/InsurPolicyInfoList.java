package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900558.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class InsurPolicyInfoList implements Serializable {
	
	private static final long serialVersionUID = 1L;
	/**
	 * 保单关系信息列表
	 */
	private List<InsurPolicyInfo> insurPolicyInfo;

	@XmlElement(name="InsurPolicyInfo")
	public List<InsurPolicyInfo> getInsurPolicyInfo() {
		return insurPolicyInfo;
	}
	public void setInsurPolicyInfo(List<InsurPolicyInfo> insurPolicyInfo) {
		this.insurPolicyInfo = insurPolicyInfo;
	}

}
