package com.nci.tunan.pa.interfaces.peripheral.exports.r00101002512.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class InputData implements Serializable {
	
	/** serialVersionUID */
	private static final long serialVersionUID = -7111012011896003505L;
	
	/** 保单号 */
	private String contNo;

	@XmlElement(name="contNo")
	public String getContNo() {
		return contNo;
	}

	public void setContNo(String contNo) {
		this.contNo = contNo;
	}
	

}
