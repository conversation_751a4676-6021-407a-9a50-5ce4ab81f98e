package com.nci.tunan.pa.interfaces.peripheral.exports.r06801900662.vo;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlType(name = "CustomerItem", propOrder = {"customerDes"})
public class CustomerItem {
	/**
	 * 序列
	 */
	private static final long serialVersionUID = 1L;
	
	/**
	 * 客户信息
	 */
	private List<CustomerDes> customerDes;

	@XmlElement(name="CustomerDes")
	public List<CustomerDes> getCustomerDes() {
		return customerDes;
	}

	public void setCustomerDes(List<CustomerDes> customerDes) {
		this.customerDes = customerDes;
	}
}
