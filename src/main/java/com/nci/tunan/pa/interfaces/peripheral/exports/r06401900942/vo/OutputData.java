package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900942.vo;

import java.io.Serializable;

import com.nci.tunan.pa.interfaces.peripheral.exports.r06401900942.vo.result.PolicyInfoList;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
/**
 * 出参对象
 * @description 
 * <AUTHOR>
 * @date 2024年04月01日 下午2:29:49
 * @.belongToModule PA-保单管理系统
 */
@XmlType(name = "OutputData", propOrder = {
		"resultCode",
		"resultMsg",
		"policyInfoList"
		})
public class OutputData implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 查询结果 0成功；1失败
	 */
	private String resultCode;
	
	/**
	 * 结果描述 当为1-失败，返回具体描述
	 */
	private String resultMsg;
	/**
	 * 保单信息列表
	 */
	private PolicyInfoList policyInfoList;
	
	@XmlElement(name="PolicyInfoList")
	public PolicyInfoList getPolicyInfoList() {
		return policyInfoList;
	}
	public void setPolicyInfoList(PolicyInfoList policyInfoList) {
		this.policyInfoList = policyInfoList;
	}
	@XmlElement(name="ResultCode")
	public String getResultCode() {
		return resultCode;
	}
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}
	
	@XmlElement(name="ResultMsg")
	public String getResultMsg() {
		return resultMsg;
	}
	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}
	@Override
	public String toString() {
		return "OutputData [resultCode=" + resultCode + ", resultMsg="
				+ resultMsg + ", policyInfoList=" + policyInfoList + "]";
	}
	

	

}
