package com.nci.tunan.pa.interfaces.peripheral.exports.r06402900875.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class OutputData implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private String resultCode;	//确认结果   0-成功；1-失败
	private String resultMsg;	//原因
	
	@XmlElement(name = "ResultCode")
	public String getResultCode() {
		return resultCode;
	}
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}
	@XmlElement(name = "ResultMsg")
	public String getResultMsg() {
		return resultMsg;
	}
	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}
	@Override
	public String toString() {
		return "OutputData [resultCode=" + resultCode + ", resultMsg="
				+ resultMsg + "]";
	}
	
	
}
