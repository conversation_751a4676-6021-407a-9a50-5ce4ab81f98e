package com.nci.tunan.pa.interfaces.peripheral.exports.r06802003102.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class InputData implements Serializable {
	
	//变更后的职业代码
	private String jobCode;
	//变更后的职业类别代码
	private String jobKind;
	//申请方式
	private String serviceType;
	//申请人
	private String applyName;
	//客户号
	private String customerId;
	//保单号集合
	private List<InsuredPolicyCodeVO> policyCodeList;
	//申请机构
	private String organCode;
	//RM：163668 新增字段：工作单位
	private String WorkUnit;
	
	public String getOrganCode() {
		return organCode;
	}
	public void setOrganCode(String organCode) {
		this.organCode = organCode;
	}
	public String getJobCode() {
		return jobCode;
	}
	public void setJobCode(String jobCode) {
		this.jobCode = jobCode;
	}
	public String getJobKind() {
		return jobKind;
	}
	public void setJobKind(String jobKind) {
		this.jobKind = jobKind;
	}
	public String getServiceType() {
		return serviceType;
	}
	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}
	public String getApplyName() {
		return applyName;
	}
	public void setApplyName(String applyName) {
		this.applyName = applyName;
	}
	public String getCustomerId() {
		return customerId;
	}
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	public List<InsuredPolicyCodeVO> getPolicyCodeList() {
		return policyCodeList;
	}
	public void setPolicyCodeList(List<InsuredPolicyCodeVO> policyCodeList) {
		this.policyCodeList = policyCodeList;
	}
	
	@XmlElement(name="WorkUnit") 
	public String getWorkUnit() {
		return WorkUnit;
	}
	public void setWorkUnit(String workUnit) {
		this.WorkUnit = workUnit;
	}
	
	
	
	

//	万能险保单号码
/*	private String contNo;

	@XmlElement(name="ContNo")
	public String getContNo() {
		return contNo;
	}

	public void setContNo(String contNo) {
		this.contNo = contNo;
	}*/
}
