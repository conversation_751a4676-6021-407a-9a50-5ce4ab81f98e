package com.nci.tunan.pa.interfaces.peripheral.exports.r00101000031.vo;

import java.io.Serializable;
import java.util.Date;

import javax.xml.bind.annotation.XmlElement;

public class Result implements Serializable {

//	险种代码
	private String riskCode;
//	险种名称
	private String riskName;
//	投保人客户号码
	private String appntNo;
//	累计交费,保留两位小数
	private String sumPrem;
//	累计金额
	private String sumAmnt;
//	期初保单价值,保留两位小数
	private String inceptContValue;
//	最新保单价值,保留两位小数
	private String currentContValue;
//	最近一期结算后资金贡献,保留两位小数
	private String moneyContribute;
//	终了结算利率
	private String rate;
//	终了结算利息
	private String interest;
//	最近保证利率,返回值为百分比
	private String gRate;
//	最近保证利率适用期间起期
	private String gRatestartdate;
//	最近保证利率适用期间止期
	private String gRateenddate;
//	账户编码
	private String insuAccNo;
//	账户建立时间
	private String accFoundDate;
//	结算日期
	private String lastMakeDate;
//	账户名称
	private String insuAccName;
//	保单状态
	private String contState;
//	保全补退费金额,保留两位小数
	private String pgPrem;
//	结算期内交费扣除费用,保留两位小数
	private String deductPrem;
//	保单管理费,保留两位小数
	private String glPrem;
//	账户净增加值,保留两位小数
	private String incrAccPrem;
//	账户累计增加值,保留两位小数
	private String incrAccSumPrem;
//	账户更新时间
	private String modifyDate;
//	累积领取
	private String sumPaym;
//	账户可领金额
	private String insuAccGetMoney;
//	冻结金额
	private String frozenMoney;
	//主险险种保单号
	private String MainPolNo;            
	
	@XmlElement(name="MainPolNo")
	public String getMainPolNo() {
		return MainPolNo;
	}
	public void setMainPolNo(String mainPolNo) {
		MainPolNo = mainPolNo;
	}
		
	@XmlElement(name="RiskCode")
	public String getRiskCode() {
		return riskCode;
	}
	public void setRiskCode(String riskCode) {
		this.riskCode = riskCode;
	}
	@XmlElement(name="RiskName")
	public String getRiskName() {
		return riskName;
	}
	public void setRiskName(String riskName) {
		this.riskName = riskName;
	}
	@XmlElement(name="AppntNo")
	public String getAppntNo() {
		return appntNo;
	}
	public void setAppntNo(String appntNo) {
		this.appntNo = appntNo;
	}
	@XmlElement(name="SumPrem")
	public String getSumPrem() {
		return sumPrem;
	}
	public void setSumPrem(String sumPrem) {
		this.sumPrem = sumPrem;
	}
	@XmlElement(name="SumAmnt")
	public String getSumAmnt() {
		return sumAmnt;
	}
	public void setSumAmnt(String sumAmnt) {
		this.sumAmnt = sumAmnt;
	}
	@XmlElement(name="InceptContValue")
	public String getInceptContValue() {
		return inceptContValue;
	}
	public void setInceptContValue(String inceptContValue) {
		this.inceptContValue = inceptContValue;
	}
	@XmlElement(name="CurrentContValue")
	public String getCurrentContValue() {
		return currentContValue;
	}
	public void setCurrentContValue(String currentContValue) {
		this.currentContValue = currentContValue;
	}
	@XmlElement(name="MoneyContribute")
	public String getMoneyContribute() {
		return moneyContribute;
	}
	public void setMoneyContribute(String moneyContribute) {
		this.moneyContribute = moneyContribute;
	}
	@XmlElement(name="Rate")
	public String getRate() {
		return rate;
	}
	public void setRate(String rate) {
		this.rate = rate;
	}
	@XmlElement(name="Interest")
	public String getInterest() {
		return interest;
	}
	public void setInterest(String interest) {
		this.interest = interest;
	}
	@XmlElement(name="GRate")
	public String getgRate() {
		return gRate;
	}
	public void setgRate(String gRate) {
		this.gRate = gRate;
	}
	@XmlElement(name="GRatestartdate")
	public String getgRatestartdate() {
		return gRatestartdate;
	}
	public void setgRatestartdate(String gRatestartdate) {
		this.gRatestartdate = gRatestartdate;
	}
	@XmlElement(name="GRateenddate")
	public String getgRateenddate() {
		return gRateenddate;
	}
	public void setgRateenddate(String gRateenddate) {
		this.gRateenddate = gRateenddate;
	}
	@XmlElement(name="InsuAccNo")
	public String getInsuAccNo() {
		return insuAccNo;
	}
	public void setInsuAccNo(String insuAccNo) {
		this.insuAccNo = insuAccNo;
	}
	@XmlElement(name="AccFoundDate")
	public String getAccFoundDate() {
		return accFoundDate;
	}
	public void setAccFoundDate(String accFoundDate) {
		this.accFoundDate = accFoundDate;
	}
	@XmlElement(name="LastMakeDate")
	public String getLastMakeDate() {
		return lastMakeDate;
	}
	public void setLastMakeDate(String lastMakeDate) {
		this.lastMakeDate = lastMakeDate;
	}
	@XmlElement(name="InsuAccName")
	public String getInsuAccName() {
		return insuAccName;
	}
	public void setInsuAccName(String insuAccName) {
		this.insuAccName = insuAccName;
	}
	@XmlElement(name="ContState")
	public String getContState() {
		return contState;
	}
	public void setContState(String contState) {
		this.contState = contState;
	}
	@XmlElement(name="PGPrem")
	public String getPgPrem() {
		return pgPrem;
	}
	public void setPgPrem(String pgPrem) {
		this.pgPrem = pgPrem;
	}
	@XmlElement(name="DeductPrem")
	public String getDeductPrem() {
		return deductPrem;
	}
	public void setDeductPrem(String deductPrem) {
		this.deductPrem = deductPrem;
	}
	@XmlElement(name="GLPrem")
	public String getGlPrem() {
		return glPrem;
	}
	public void setGlPrem(String glPrem) {
		this.glPrem = glPrem;
	}
	@XmlElement(name="IncrAccPrem")
	public String getIncrAccPrem() {
		return incrAccPrem;
	}
	public void setIncrAccPrem(String incrAccPrem) {
		this.incrAccPrem = incrAccPrem;
	}
	@XmlElement(name="IncrAccSumPrem")
	public String getIncrAccSumPrem() {
		return incrAccSumPrem;
	}
	public void setIncrAccSumPrem(String incrAccSumPrem) {
		this.incrAccSumPrem = incrAccSumPrem;
	}
	@XmlElement(name="ModifyDate")
	public String getModifyDate() {
		return modifyDate;
	}
	public void setModifyDate(String modifyDate) {
		this.modifyDate = modifyDate;
	}
	@XmlElement(name="SumPaym")
	public String getSumPaym() {
		return sumPaym;
	}
	public void setSumPaym(String sumPaym) {
		this.sumPaym = sumPaym;
	}
	@XmlElement(name="InsuAccGetMoney")
	public String getInsuAccGetMoney() {
		return insuAccGetMoney;
	}
	public void setInsuAccGetMoney(String insuAccGetMoney) {
		this.insuAccGetMoney = insuAccGetMoney;
	}
	@XmlElement(name="FrozenMoney")
	public String getFrozenMoney() {
		return frozenMoney;
	}
	public void setFrozenMoney(String frozenMoney) {
		this.frozenMoney = frozenMoney;
	}
	
}
