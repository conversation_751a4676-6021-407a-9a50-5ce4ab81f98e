package com.nci.tunan.pa.interfaces.peripheral.exports.r00101000550.vo;

import java.io.Serializable;
import java.util.Date;

import javax.xml.bind.annotation.XmlElement;
/**
 * 
 * @description 
 * <AUTHOR> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2018年3月22日 下午2:11:39
 */
public class Bnf implements Serializable {

	/**
	 * 显示主险信息
	 */
	private static final long serialVersionUID = 1L;
	//受益人姓名
	private String BnfName;
	//受益人性别
	private String BnfSex;
	//受益人出生日期
	private Date BnfBirthDay;
	//受益人证件类型
	private String BnfIDType;
	//受益人证件号
	private String BnfIDNO;
	//受益人类别
	private String BnfType;
	//受益人序号
	private Number BnfNo;
	//受益人所属被保人序号
	private String InsuredSequenceNo;
	//受益人所属被保人姓名
	private String InsuredName;
	//受益人与被保人关系
	private String BnfRelationToInsured;
	//受益人职业类别
	private String BnfOccupationType;
	//受益人职业代码
	private String BnfOccupationCode;
	//受益人联系电话
	private String BnfPhone;
	//受益人通讯地址
	private String BnfPostalAddress;
	//受益人通讯邮政编码
	private String BnfZIPCode;
	//受益人级别
	private String BnfGrade;
	//受益份额
	private String BnfLot;
	@XmlElement(name="BnfName")
	public String getBnfName() {
		return BnfName;
	}
	public void setBnfName(String bnfName) {
		BnfName = bnfName;
	}
	@XmlElement(name="BnfSex")
	public String getBnfSex() {
		return BnfSex;
	}
	public void setBnfSex(String bnfSex) {
		BnfSex = bnfSex;
	}
	@XmlElement(name="BnfBirthDay")
	public Date getBnfBirthDay() {
		return BnfBirthDay;
	}
	public void setBnfBirthDay(Date bnfBirthDay) {
		BnfBirthDay = bnfBirthDay;
	}
	@XmlElement(name="BnfIDType")
	public String getBnfIDType() {
		return BnfIDType;
	}
	public void setBnfIDType(String bnfIDType) {
		BnfIDType = bnfIDType;
	}
	@XmlElement(name="BnfIDNO")
	public String getBnfIDNO() {
		return BnfIDNO;
	}
	public void setBnfIDNO(String bnfIDNO) {
		BnfIDNO = bnfIDNO;
	}
	@XmlElement(name="BnfType")
	public String getBnfType() {
		return BnfType;
	}
	public void setBnfType(String bnfType) {
		BnfType = bnfType;
	}
	@XmlElement(name="BnfNo")
	public Number getBnfNo() {
		return BnfNo;
	}
	public void setBnfNo(Number bnfNo) {
		BnfNo = bnfNo;
	}
	@XmlElement(name="InsuredSequenceNo")
	public String getInsuredSequenceNo() {
		return InsuredSequenceNo;
	}
	public void setInsuredSequenceNo(String insuredSequenceNo) {
		InsuredSequenceNo = insuredSequenceNo;
	}
	@XmlElement(name="InsuredName")
	public String getInsuredName() {
		return InsuredName;
	}
	public void setInsuredName(String insuredName) {
		InsuredName = insuredName;
	}
	@XmlElement(name="BnfRelationToInsured")
	public String getBnfRelationToInsured() {
		return BnfRelationToInsured;
	}
	public void setBnfRelationToInsured(String bnfRelationToInsured) {
		BnfRelationToInsured = bnfRelationToInsured;
	}
	@XmlElement(name="BnfOccupationType")
	public String getBnfOccupationType() {
		return BnfOccupationType;
	}
	public void setBnfOccupationType(String bnfOccupationType) {
		BnfOccupationType = bnfOccupationType;
	}
	@XmlElement(name="BnfOccupationCode")
	public String getBnfOccupationCode() {
		return BnfOccupationCode;
	}
	public void setBnfOccupationCode(String bnfOccupationCode) {
		BnfOccupationCode = bnfOccupationCode;
	}
	@XmlElement(name="BnfPhone")
	public String getBnfPhone() {
		return BnfPhone;
	}
	public void setBnfPhone(String bnfPhone) {
		BnfPhone = bnfPhone;
	}
	@XmlElement(name="BnfPostalAddress")
	public String getBnfPostalAddress() {
		return BnfPostalAddress;
	}
	public void setBnfPostalAddress(String bnfPostalAddress) {
		BnfPostalAddress = bnfPostalAddress;
	}
	@XmlElement(name="BnfZIPCode")
	public String getBnfZIPCode() {
		return BnfZIPCode;
	}
	public void setBnfZIPCode(String bnfZIPCode) {
		BnfZIPCode = bnfZIPCode;
	}
	@XmlElement(name="BnfGrade")
	public String getBnfGrade() {
		return BnfGrade;
	}
	public void setBnfGrade(String bnfGrade) {
		BnfGrade = bnfGrade;
	}
	@XmlElement(name="BnfLot")
	public String getBnfLot() {
		return BnfLot;
	}
	public void setBnfLot(String bnfLot) {
		BnfLot = bnfLot;
	}

}
