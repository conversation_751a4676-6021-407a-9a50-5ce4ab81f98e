package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900620.vo;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;


public class SubRiskList implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	/**
	 * 保单列表
	 */
	private List<SubRisk> SubRisk;
	@XmlElement(name = "SubRisk")
	public List<SubRisk> getSubRisk() {
		return SubRisk;
	}

	public void setSubRisk(List<SubRisk> subRisk) {
		SubRisk = subRisk;
	}
	
}
