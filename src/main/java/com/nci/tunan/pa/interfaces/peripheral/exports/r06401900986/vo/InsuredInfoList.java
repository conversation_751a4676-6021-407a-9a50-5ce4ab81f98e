package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900986.vo;

import java.io.Serializable;
import java.util.List;

import com.nci.tunan.pa.interfaces.peripheral.exports.r06401900986.vo.InsuredInfo;

public class InsuredInfoList implements Serializable{

	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	// 被保人信息
    private List<InsuredInfo> insuredInfo;
	public List<InsuredInfo> getInsuredInfo() {
		return insuredInfo;
	}
	public void setInsuredInfo(List<InsuredInfo> insuredInfo) {
		this.insuredInfo = insuredInfo;
	}
}
