package com.nci.tunan.pa.interfaces.peripheral.exports.r06402900835.inoticereportviewanddownloaducc.querynoticereportinfo;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.nci.tunan.pa.interfaces.peripheral.exports.r06402900835.vo.OutputData;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody")
public class SrvResBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(name = "OutputData")
    protected OutputData outputData;

    public OutputData getOutputData() {
        return outputData;
    }
    public void setOutputData(OutputData outputData) {
        this.outputData = outputData;
    }
}



