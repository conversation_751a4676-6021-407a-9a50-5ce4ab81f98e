package com.nci.tunan.pa.interfaces.peripheral.exports.r00101000043.irenewinfoqueryucc.queryrenewinfo.ws;

import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.ws.Holder;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000043.irenewinfoqueryucc.queryrenewinfo.SrvReqBody;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000043.irenewinfoqueryucc.queryrenewinfo.SrvResBody;

/**
 * IRenewInfoQueryUccWS
 * @description 
 * <AUTHOR> <EMAIL> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2017年12月7日 上午11:15:01
 */
@WebService
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface IRenewInfoQueryUccWS {
	
	/**
	 * 
	 * @description
	 * @version V1.0.0
	 * @title
	 * <AUTHOR>
	 * @param parametersReqHeader
	 * @param parametersReqBody
	 * @param parametersResHeader
	 * @param parametersResBody
	 */
    public void queryRenewInfo(
            @WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, partName = "parametersReqHeader") SysHeader parametersReqHeader,
            @WebParam(name = "request", targetNamespace = "http://www.newchinalife.com/service/bd", partName = "parametersReqBody") SrvReqBody parametersReqBody,
            @WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, mode = WebParam.Mode.OUT, partName = "parametersResHeader") Holder<SysHeader> parametersResHeader,
            @WebParam(name = "response", targetNamespace = "http://www.newchinalife.com/service/bd", mode = WebParam.Mode.OUT, partName = "parametersResBody") Holder<SrvResBody> parametersResBody);
}

