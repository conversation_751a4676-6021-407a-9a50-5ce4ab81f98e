package com.nci.tunan.pa.interfaces.peripheral.exports.r00101002647.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

/**
 * 保单未领生存金查询
 * <AUTHOR>
 *
 */
public class Result implements Serializable {

	private static final long serialVersionUID = 5441501473885238019L;

	private PolicyList policyList;

	@XmlElement(name="PolicyList")
	public PolicyList getPolicyList() {
		return policyList;
	}

	public void setPolicyList(PolicyList policyList) {
		this.policyList = policyList;
	}
}
