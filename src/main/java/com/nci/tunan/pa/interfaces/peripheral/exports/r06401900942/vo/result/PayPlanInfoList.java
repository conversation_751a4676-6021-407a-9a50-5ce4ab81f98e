package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900942.vo.result;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
/**
 * 生存金年金信息列表
 * @description 
 * <AUTHOR>
 * @date 2024年04月01日 下午2:29:49
 * @.belongToModule PA-保单管理系统
 */
public class PayPlanInfoList implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 生存金年金信息列表
	 */
	private List<PayPlanInfo> payPlanInfo;
	@XmlElement(name="PayPlanInfo")
	public List<PayPlanInfo> getPayPlanInfo() {
		return payPlanInfo;
	}
	public void setPayPlanInfo(List<PayPlanInfo> payPlanInfo) {
		this.payPlanInfo = payPlanInfo;
	}
	@Override
	public String toString() {
		return "PayPlanInfoList [payPlanInfo=" + payPlanInfo + "]";
	}
	


}
