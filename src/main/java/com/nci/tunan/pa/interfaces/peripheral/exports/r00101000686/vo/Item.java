package com.nci.tunan.pa.interfaces.peripheral.exports.r00101000686.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class Item implements Serializable{
	
	//保单号
	private   String   contNo;
	//身份类别
	private   String   idType;
	
	
	
	@XmlElement(name = "ContNo")
	public String getContNo() {
		return contNo;
	}
	public void setContNo(String contNo) {
		this.contNo = contNo;
	}
	@XmlElement(name = "IdType")
	public String getIdType() {
		return idType;
	}
	public void setIdType(String idType) {
		this.idType = idType;
	}
	
	
	
	
}
