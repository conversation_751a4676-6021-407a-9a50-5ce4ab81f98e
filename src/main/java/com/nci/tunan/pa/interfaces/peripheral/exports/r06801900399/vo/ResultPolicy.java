package com.nci.tunan.pa.interfaces.peripheral.exports.r06801900399.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

/**
 * 
 * @description 保单贷款续贷ResultPolicy类
 * <AUTHOR> <EMAIL> 
 * @date 2020年9月30日 下午3:04:51 
 * @.belongToModule 保单系统-ResultPolicy类
 */
public class ResultPolicy implements Serializable {

	/** 
	* @Fields serialVersionUID : 
	*/ 
	
	private static final long serialVersionUID = 1L;
	
	/**
	 * 保单号
	 */
	private String policyno ;
	
	/**
	 * 递交渠道
	 */
	private String submitChannel ;
	
	/**
	 * 被保险人姓名(保单第一被保人姓名)
	 */
	private String insuredName;
	
	/**
	 * 险种贷款信息
	 */
	private List<ResultList> resultList;

	/**
	 * 印花税
	 */
	private String revenueRate ;
	
	/**
	 * 保单状态
	 */
	private String policyLiabStatus;

	/**
	 * 保单失效原因码值
	 */
	private String policyLapseCauseCode;

	/**
	 * 保单失效原因码值描述
	 */
	private String policyLapseCauseDesc;
	
	@XmlElement(name="Policyno")
	public String getPolicyno() {
		return policyno;
	}


	public void setPolicyno(String policyno) {
		this.policyno = policyno;
	}
	
	@XmlElement(name="SubmitChannel")
	public String getSubmitChannel() {
		return submitChannel;
	}


	public void setSubmitChannel(String submitChannel) {
		this.submitChannel = submitChannel;
	}


	@XmlElement(name="InsuredName")
	public String getInsuredName() {
		return insuredName;
	}


	public void setInsuredName(String insuredName) {
		this.insuredName = insuredName;
	}

	@XmlElement(name="ResultList")
	public List<ResultList> getResultList() {
		return resultList;
	}


	public void setResultList(List<ResultList> resultList) {
		this.resultList = resultList;
	}



	@XmlElement(name="RevenueRate")
	public String getRevenueRate() {
		return revenueRate;
	}


	public void setRevenueRate(String revenueRate) {
		this.revenueRate = revenueRate;
	}

	@XmlElement(name="policyLiabStatus")
	public String getPolicyLiabStatus() {
		return policyLiabStatus;
	}

	public void setPolicyLiabStatus(String policyLiabStatus) {
		this.policyLiabStatus = policyLiabStatus;
	}

	@XmlElement(name="policyLapseCauseCode")
	public String getPolicyLapseCauseCode() {
		return policyLapseCauseCode;
	}

	public void setPolicyLapseCauseCode(String policyLapseCauseCode) {
		this.policyLapseCauseCode = policyLapseCauseCode;
	}

	@XmlElement(name="policyLapseCauseDesc")
	public String getPolicyLapseCauseDesc() {
		return policyLapseCauseDesc;
	}

	public void setPolicyLapseCauseDesc(String policyLapseCauseDesc) {
		this.policyLapseCauseDesc = policyLapseCauseDesc;
	}
	
}
