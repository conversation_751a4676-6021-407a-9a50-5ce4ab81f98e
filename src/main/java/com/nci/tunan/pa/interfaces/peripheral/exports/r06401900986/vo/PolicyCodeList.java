package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900986.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class PolicyCodeList implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	
	//保单号	
	private String policyCode;

	@XmlElement(name = "policyCode")
	public String getPolicyCode() {
		return policyCode;
	}

    public void setPolicyCode(String policyCode) {
	    this.policyCode = policyCode;
	}
}
