package com.nci.tunan.pa.interfaces.peripheral.exports.r06801901056.iquerycustomerinfoucc.findcustomerinfobyacceptcode;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 
 * @description 查询保全受理号下客户信息（空中柜面）SrvReqBizBody类
 * <AUTHOR> <EMAIL> 
 * @date 2024年11月22日 下午3:30:19 
 * @.belongToModule 保全子系统
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvReqBizBody")
public class SrvReqBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(name = "inputData")
    protected com.nci.tunan.pa.interfaces.peripheral.exports.r06801901056.vo.InputData inputData;
    
    public com.nci.tunan.pa.interfaces.peripheral.exports.r06801901056.vo.InputData getInputData() {
        return inputData;
    }
    public void setInputData(com.nci.tunan.pa.interfaces.peripheral.exports.r06801901056.vo.InputData inputData) {
        this.inputData = inputData;
    }
}


