package com.nci.tunan.pa.interfaces.peripheral.exports.judgepolicysequencenoexist.ipolicysequencenoqueryucc.policysequencenoquery;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBizBody")
public class SrvResBizBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(name = "OutputData")
    protected com.nci.tunan.pa.interfaces.peripheral.exports.judgepolicysequencenoexist.vo.OutputData outputData;

    public com.nci.tunan.pa.interfaces.peripheral.exports.judgepolicysequencenoexist.vo.OutputData getOutputData() {
        return outputData;
    }
    public void setOutputData(com.nci.tunan.pa.interfaces.peripheral.exports.judgepolicysequencenoexist.vo.OutputData outputData) {
        this.outputData = outputData;
    }
}
