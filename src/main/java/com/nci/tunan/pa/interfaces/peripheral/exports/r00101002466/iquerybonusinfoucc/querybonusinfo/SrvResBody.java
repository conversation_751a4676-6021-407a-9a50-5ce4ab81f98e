package com.nci.tunan.pa.interfaces.peripheral.exports.r00101002466.iquerybonusinfoucc.querybonusinfo;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeader;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExB;
import com.nci.udmp.component.serviceinvoke.message.body.hd.BizHeaderExA;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101002466.iquerybonusinfoucc.querybonusinfo.SrvResBizBody;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SrvResBody", propOrder = { "bizHeader", "bizBody" })
public class SrvResBody implements Serializable {
    /**
     * serialVersionUID 
     */
    private static final long serialVersionUID = 1L;
    
    @XmlElement(required = true)
    protected  BizHeaderExB  bizHeader;
    
    @XmlElement(required = true)
    protected SrvResBizBody bizBody;
    
    public  BizHeaderExB  getBizHeader() {
        return bizHeader;
    }
    public void setBizHeader( BizHeaderExB  bizHeader) {
        this.bizHeader = bizHeader;
    }
    public SrvResBizBody getBizBody() {
        return bizBody;
    }
    public void setBizBody(SrvResBizBody bizBody) {
        this.bizBody = bizBody;
    }
}


