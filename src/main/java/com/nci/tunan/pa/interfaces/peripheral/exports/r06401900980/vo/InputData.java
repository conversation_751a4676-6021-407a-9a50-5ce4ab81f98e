package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900980.vo;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlElement;
/**
 * 查询客户名下保单号接口入参对象
 * @description 
 * <AUTHOR> 
 * @date 2024年06月27日 下午16:51:49
 * @.belongToModule PA-保单管理系统
 */
public class InputData implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	// 客户姓名
	private String CustName;
	// 客户性别
	private String CustGender;
	// 客户出生日期
	private String CustBirthDay;
	// 客户证件类型
	private String CustCertiType;
	// 客户证件号码
	private String CustCertiCode;
	// 客户号
	private String OldCustomerNo;
	// 客户角色
	private String CustRole;
	// 业务员代码
	private String AgentCode;
	
	@XmlElement(name="CustName")
	public String getCustName() {
		return CustName;
	}
	public void setCustName(String custName) {
		CustName = custName;
	}
	@XmlElement(name="CustGender")
	public String getCustGender() {
		return CustGender;
	}
	public void setCustGender(String custGender) {
		CustGender = custGender;
	}
	@XmlElement(name="CustBirthDay")
	public String getCustBirthDay() {
		return CustBirthDay;
	}
	public void setCustBirthDay(String custBirthDay) {
		CustBirthDay = custBirthDay;
	}
	@XmlElement(name="CustCertiType")
	public String getCustCertiType() {
		return CustCertiType;
	}
	public void setCustCertiType(String custCertiType) {
		CustCertiType = custCertiType;
	}
	@XmlElement(name="CustCertiCode")
	public String getCustCertiCode() {
		return CustCertiCode;
	}
	public void setCustCertiCode(String custCertiCode) {
		CustCertiCode = custCertiCode;
	}
	@XmlElement(name="OldCustomerNo")
	public String getOldCustomerNo() {
		return OldCustomerNo;
	}
	public void setOldCustomerNo(String oldCustomerNo) {
		OldCustomerNo = oldCustomerNo;
	}
	@XmlElement(name="CustRole")
	public String getCustRole() {
		return CustRole;
	}
	public void setCustRole(String custRole) {
		CustRole = custRole;
	}
	@XmlElement(name="AgentCode")
	public String getAgentCode() {
		return AgentCode;
	}
	public void setAgentCode(String agentCode) {
		AgentCode = agentCode;
	}
	
	
	
	
	
}

