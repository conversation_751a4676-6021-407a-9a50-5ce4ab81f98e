package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900932.vo.response;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class InsuredInfoList implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	//被保人信息
	private List<InsuredInfo> insuredInfo;
	
	@XmlElement(name = "InsuredInfo")
	public List<InsuredInfo> getInsuredInfo() {
		return insuredInfo;
	}
	public void setInsuredInfo(List<InsuredInfo> insuredInfo) {
		this.insuredInfo = insuredInfo;
	}

}
