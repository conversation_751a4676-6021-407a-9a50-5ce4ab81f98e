package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900980.vo;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlElement;

public class OutputData implements Serializable {
	/**
	 * 查询客户名下保单号接口出参对象
	 * @description 
	 * <AUTHOR> 
	 * @date 2024年06月27日 下午14:51:49
	 * @.belongToModule PA-保单管理系统
	 */
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 查询结果代码
	 */
	private String ResultCode;
	
	/**
	 * 查询结果提示语
	 */
	private String ResultMsg;
	
	//保单信息列表
	private PolicyList policyList;
	

	@XmlElement(name="ResultCode")
	public String getResultCode() {
		return ResultCode;
	}

	public void setResultCode(String resultCode) {
		ResultCode = resultCode;
	}
	@XmlElement(name="ResultMsg")
	public String getResultMsg() {
		return ResultMsg;
	}

	public void setResultMsg(String resultMsg) {
		ResultMsg = resultMsg;
	}
	@XmlElement(name="PolicyList")
	public PolicyList getPolicyList() {
		return policyList;
	}

	public void setPolicyList(PolicyList policyList) {
		this.policyList = policyList;
	}
}
