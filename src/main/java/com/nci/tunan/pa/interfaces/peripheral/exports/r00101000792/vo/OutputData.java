package com.nci.tunan.pa.interfaces.peripheral.exports.r00101000792.vo;

import java.io.Serializable;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

import com.nci.tunan.pa.interfaces.peripheral.exports.r00101000792.vo.Result;

public class OutputData implements Serializable{
    /** 
    * @Fields serialVersionUID : @invalid TODO(用一句话描述这个变量表示什么) 
    */ 
    
    private static final long serialVersionUID = 1L;
    private List<Result> resultList;
    @XmlElement(name="Result")
    public List<Result> getResultList() {
        return resultList;
    }
    public void setResultList(List<Result> resultList) {
        this.resultList = resultList;
    }
    public static long getSerialversionuid() {
        return serialVersionUID;
    }
    
}
