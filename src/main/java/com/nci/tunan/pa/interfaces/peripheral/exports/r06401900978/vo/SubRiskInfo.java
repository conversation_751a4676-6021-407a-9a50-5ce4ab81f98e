package com.nci.tunan.pa.interfaces.peripheral.exports.r06401900978.vo;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * @description  附加险险种信息
 * <AUTHOR> 
 * @date 2024年6月27日
 * @.belongToModule PA-保单管理系统-领取形式变更-保单列表查询接口
 */
@XmlType(name = "SubRiskInfo", propOrder = {
		"subRiskCode",
		"subRiskName",
		"subRiskShortName",
		"subRiskValiDate",
		"subRiskStats",
		"subRiskBusiItemId",
		"subRiskOriginatorId",
		"subRiskSurvivalMode",
		"subRiskSurvivalWMode",
		"subRiskBankCode",
		"subRiskBankName",
		"subRiskAccoName",
		"subRiskBankAccount"
		})
public class SubRiskInfo {

	//附加险代码
	private String subRiskCode;	
	//附加险全称
	private String subRiskName;	
	//附加险简称
	private String subRiskShortName;	
	//附加险生效日期
	private String subRiskValiDate;	
	//附加险险种状态
	private String subRiskStats;		
	//附加险险种ID
	private String subRiskBusiItemId;	
	//年金、生存金权益人客户号
	private String subRiskOriginatorId;	
	//年金生存金领取形式
	private String subRiskSurvivalMode;	
	//支取形式
	private String subRiskSurvivalWMode;	
	//开户银行代码
	private String subRiskBankCode;		
	//开户行名称
	private String subRiskBankName;	
	//户名
	private String subRiskAccoName;	
	//银行账号
	private String subRiskBankAccount;
	
	@XmlElement(name = "SubRiskCode")
	public String getSubRiskCode() {
		return subRiskCode;
	}
	public void setSubRiskCode(String subRiskCode) {
		this.subRiskCode = subRiskCode;
	}
	@XmlElement(name = "SubRiskName")
	public String getSubRiskName() {
		return subRiskName;
	}
	public void setSubRiskName(String subRiskName) {
		this.subRiskName = subRiskName;
	}
	@XmlElement(name = "SubRiskShortName")
	public String getSubRiskShortName() {
		return subRiskShortName;
	}
	public void setSubRiskShortName(String subRiskShortName) {
		this.subRiskShortName = subRiskShortName;
	}
	@XmlElement(name = "SubRiskValiDate")
	public String getSubRiskValiDate() {
		return subRiskValiDate;
	}
	public void setSubRiskValiDate(String subRiskValiDate) {
		this.subRiskValiDate = subRiskValiDate;
	}
	@XmlElement(name = "SubRiskStats")
	public String getSubRiskStats() {
		return subRiskStats;
	}
	public void setSubRiskStats(String subRiskStats) {
		this.subRiskStats = subRiskStats;
	}
	@XmlElement(name = "SubRiskBusiItemId")
	public String getSubRiskBusiItemId() {
		return subRiskBusiItemId;
	}
	public void setSubRiskBusiItemId(String subRiskBusiItemId) {
		this.subRiskBusiItemId = subRiskBusiItemId;
	}
	@XmlElement(name = "SubRiskOriginatorId")
	public String getSubRiskOriginatorId() {
		return subRiskOriginatorId;
	}
	public void setSubRiskOriginatorId(String subRiskOriginatorId) {
		this.subRiskOriginatorId = subRiskOriginatorId;
	}
	@XmlElement(name = "SubRiskSurvivalMode")
	public String getSubRiskSurvivalMode() {
		return subRiskSurvivalMode;
	}
	public void setSubRiskSurvivalMode(String subRiskSurvivalMode) {
		this.subRiskSurvivalMode = subRiskSurvivalMode;
	}
	@XmlElement(name = "SubRiskSurvivalWMode")
	public String getSubRiskSurvivalWMode() {
		return subRiskSurvivalWMode;
	}
	public void setSubRiskSurvivalWMode(String subRiskSurvivalWMode) {
		this.subRiskSurvivalWMode = subRiskSurvivalWMode;
	}
	@XmlElement(name = "SubRiskBankCode")
	public String getSubRiskBankCode() {
		return subRiskBankCode;
	}
	public void setSubRiskBankCode(String subRiskBankCode) {
		this.subRiskBankCode = subRiskBankCode;
	}
	@XmlElement(name = "SubRiskBankName")
	public String getSubRiskBankName() {
		return subRiskBankName;
	}
	public void setSubRiskBankName(String subRiskBankName) {
		this.subRiskBankName = subRiskBankName;
	}
	@XmlElement(name = "SubRiskAccoName")
	public String getSubRiskAccoName() {
		return subRiskAccoName;
	}
	public void setSubRiskAccoName(String subRiskAccoName) {
		this.subRiskAccoName = subRiskAccoName;
	}
	@XmlElement(name = "SubRiskBankAccount")
	public String getSubRiskBankAccount() {
		return subRiskBankAccount;
	}
	public void setSubRiskBankAccount(String subRiskBankAccount) {
		this.subRiskBankAccount = subRiskBankAccount;
	}	
}
