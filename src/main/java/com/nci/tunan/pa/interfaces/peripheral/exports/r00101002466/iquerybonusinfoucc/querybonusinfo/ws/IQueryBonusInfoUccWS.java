package com.nci.tunan.pa.interfaces.peripheral.exports.r00101002466.iquerybonusinfoucc.querybonusinfo.ws;

import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.ws.Holder;
import com.nci.udmp.component.serviceinvoke.message.header.in.SysHeader;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101002466.iquerybonusinfoucc.querybonusinfo.SrvReqBody;
import com.nci.tunan.pa.interfaces.peripheral.exports.r00101002466.iquerybonusinfoucc.querybonusinfo.SrvResBody;

/**
 * IQueryBonusInfoUccWS
 * @description 
 * <AUTHOR> <EMAIL> 
 * @version V1.0.0
 * @.belongToModule PA-保单管理系统
 * @date 2017年12月7日 上午9:44:19
 */
@WebService
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface IQueryBonusInfoUccWS {
    public void queryBonusInfo(
            @WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, partName = "parametersReqHeader") SysHeader parametersReqHeader,
            @WebParam(name = "request", targetNamespace = "http://www.newchinalife.com/service/bd", partName = "parametersReqBody") SrvReqBody parametersReqBody,
            @WebParam(name = "sysHeader", targetNamespace = "http://www.newchinalife.com/common/header/in", header = true, mode = WebParam.Mode.OUT, partName = "parametersResHeader") Holder<SysHeader> parametersResHeader,
            @WebParam(name = "response", targetNamespace = "http://www.newchinalife.com/service/bd", mode = WebParam.Mode.OUT, partName = "parametersResBody") Holder<SrvResBody> parametersResBody);
}

