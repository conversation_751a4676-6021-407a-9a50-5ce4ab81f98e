package com.nci.tunan.pa.interfaces.peripheral.exports.r00101000109.vo;

import java.io.Serializable;
import java.util.Date;
import javax.xml.bind.annotation.XmlElement;

public class InputData implements Serializable {
	// 保单号
	private String contNo;

	// 客户登录日期
	private String CurrDate;
	
	@XmlElement(name = "ContNo")
	public String getContNo() {
		return contNo;
	}

	public void setContNo(String contNo) {
		this.contNo = contNo;
	}

	@XmlElement(name = "CurrDate")
    public String getCurrDate() {
        return CurrDate;
    }

    public void setCurrDate(String currDate) {
        CurrDate = currDate;
    }


	
}
