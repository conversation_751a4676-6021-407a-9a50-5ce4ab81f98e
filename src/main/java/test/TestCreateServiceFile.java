package test;


//import com.nci.udmp.tools.generate.mapper.DBConfig;
//import com.nci.udmp.tools.generate.mapper.DaoImplObject;
//import com.nci.udmp.tools.generate.mapper.DaoObject;
//import com.nci.udmp.tools.generate.mapper.GenerateDaoImplBean;
//import com.nci.udmp.tools.generate.mapper.GenerateServiceBean;
//import com.nci.udmp.tools.generate.mapper.GenerateServiceImplBean;
//import com.nci.udmp.tools.generate.mapper.GenerateUtil;
//import com.nci.udmp.tools.generate.mapper.ServiceImplObject;
//import com.nci.udmp.tools.generate.mapper.ServiceObject;

public class TestCreateServiceFile {
	public static void main(String[] args) {
//		String driverClassName ="oracle.jdbc.driver.OracleDriver", url="*******************************************", userName="app_clm", password="app_clm";
//		DBConfig config = new DBConfig(driverClassName, url, userName, password);
//	 
////		//生成dao
//		ArrayList<ServiceObject> list = new ArrayList<ServiceObject> ();
//		Set<String> impSet = new HashSet<String>();
//		ServiceObject serviceObject = new ServiceObject("com.nci.tunan.clm.impl.report.service","T_CUSTOMER",impSet,"" );
//		list.add(serviceObject);
//		GenerateServiceBean serviceObjectBean  = new GenerateServiceBean(config,list);
//		GenerateUtil.createServiceFile(serviceObjectBean);  
//		
//		
//		ArrayList<ServiceImplObject> list2 = new ArrayList<ServiceImplObject> ();
//		ServiceImplObject implObject = new ServiceImplObject("com.nci.tunan.clm.impl.report.service.impl","T_CUSTOMER",impSet,"" ,"ICustomerService");
//		list2.add(implObject);
//		GenerateServiceImplBean implBean = new GenerateServiceImplBean(config,list2);
//		GenerateUtil.createServiceImplFile(implBean);
	}
}
