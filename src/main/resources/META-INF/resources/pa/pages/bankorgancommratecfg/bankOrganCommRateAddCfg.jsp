<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css"
	media="screen">
<!-- 插入查询结果页面 -->
					<table class="list" width="100%" id="bankOrganCommRateAddCfgTable">
						<thead>
							<tr>
								<th>交费方式</th>
								<th>交费年期</th>
								<th>保单年度</th>
								<th>总公司佣金率</th>
								<th>机构佣金率<font color="red">*</font></th>
							</tr>
						</thead>
						<tbody id="bankOrganCommRateAddCfgTbody">
							<s:iterator value="pageInfo.valueList" status="st" id="bankOrganCommRateAdds" >
								<tr>
									<td>
										<s:if test="#bankOrganCommRateAdds.payintv == ''"></s:if>
										<s:elseif test="#bankOrganCommRateAdds.payintv == 0">0趸交</s:elseif>
										<s:elseif test="#bankOrganCommRateAdds.payintv == '12'">12期交</s:elseif>
										<s:elseif test="#bankOrganCommRateAdds.payintv == '88'">88（长期月交）</s:elseif>
										<s:elseif test="#bankOrganCommRateAdds.payintv == '99'">99（不定期交）</s:elseif>
									</td>
									<td>
										<s:if test="#bankOrganCommRateAdds.payintv == ''"></s:if>
										<s:elseif test="#bankOrganCommRateAdds.payintv == 0">1</s:elseif>
										<s:elseif test="#bankOrganCommRateAdds.payintv == 99"></s:elseif>
										<s:else>${year }</s:else>
									</td>
									<td>${curyear }</td>
									<td>${companyRate }</td>
									<td><input type="text" name = "${st.index+1}" class="number" max="1" maxlength="6"/></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>