<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>

<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css"
	media="screen">

<!-- 银代机构佣金率管理配置页面 -->
<style type="text/css">
.pagetableContent {
	border-radius: 5px;
	background: #f5f6f8;
	border: 1px solid #d3e4f8;
	margin: 0px 20px;
	padding-bottom: 10px;
	text-align: left;
	*overflow: hidden;
	padding-top: 10px;
}

/* 	.dialog{
	  width: 80% !important;
	} */
</style>
<div layoutH="0">
	<div class="divfclass">
		<h1>
			<IMG src="udmp/dwz/themes/default/images/skin_img/tubiao.png">查询条件
		</h1>
	</div>
	<form id="pagerForm" method="post" action="pa/bankorgancommratecfg/search_PA_bankOrganCommRateCfgAction.action?menuId=${menuId }">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo}" /> 
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" /> 
    </form>
	<form id="bankOrganCommRateQueryCfgForm"
		onsubmit="return navTabSearch(this)"
		action="pa/bankorgancommratecfg/search_PA_bankOrganCommRateCfgAction.action?menuId=${menuId }"
		method="post" class="pageForm required-validate" rel="pagerForm">
		<div class="pageFormInfoContent">
			<dl>
				<dt>
					管理机构<font color="red">*</font>
				</dt>
				<dd>
					<input type="text" id="organCodeQueryCfg" class="autocomplete textLeft"
						value="${fycrateComVO.organCode }"
						name="fycrateComVO.organCode" data-width="151px"
						data-showValue="organCodeQueryCfgInput" data-mode="2"
						data-tableName="APP___PAS__DBUSER.T_UDMP_ORG_REL"
						data-tableCol="ORGAN_CODE"
						data-tableDesc="ORGAN_CODE||'-'||ORGAN_NAME " data-separator="-"
						data-like="3" data-queryCounts="600" data-view="ORGAN_NAME"
						data-orderBy="ORGAN_CODE" /> <input type='text'
						id='organCodeQueryCfgInput' class="textRight" readOnly />
				</dd>
			</dl>
			<dl>
				<dt>银行渠道</dt>
				<dd>
					<input id="bankBranchCodeQueryCfg" type="text" class="autocomplete textLeft"
						name="fycrateComVO.bankBranchCode" 
						value="${fycrateComVO.bankBranchCode }"
						data-showValue="bankBranchCodeQueryCfgInput" data-mode="2"
						data-disCurrentValFlag="1" data-width="151px"
						data-tableName="APP___PAS__DBUSER.T_BANK_BRANCH T"
						data-tableCol="BANK_BRANCH_CODE" data-queryCounts="600" 
						data-tableDesc="BANK_BRANCH_CODE||' - '||BANK_BRANCH_NAME" data-separator=" - "
						data-whereClause=" T.ORGAN_CODE = '${fycrateComVO.organCode}' AND T.SALES_CHANNEL = '03' AND T.STATE = '0' " data-like="1"
						data-orderBy="BANK_BRANCH_CODE" data-view="BANK_BRANCH_NAME"/>
					<input type="text" id="bankBranchCodeQueryCfgInput" class="textRight" readOnly />
				</dd>
				
			</dl>
			<dl>
				<dt>险种</dt>
				<dd>
					<input type="text" id="busiProdCodeQueryCfg"
						class="autocomplete textLeft"
						value="${fycrateComVO.riskcode }"
						name="fycrateComVO.riskcode" data-width="151px"
						data-showValue="busiProdCodeQueryCfgInput" data-mode="2"
						data-tableName="APP___PDS__DBUSER.T_BUSINESS_PRODUCT P"
						data-tableCol="P.PRODUCT_CODE_SYS"
						data-tableDesc="P.PRODUCT_CODE_SYS||'-'||P.PRODUCT_NAME_STD "
						data-separator="-" data-like="3" data-queryCounts="600"
						data-view="P.PRODUCT_NAME_STD" data-orderBy="P.PRODUCT_CODE_SYS" /> <input
						type='text' id='busiProdCodeQueryCfgInput' class="textRight" readOnly />
				</dd>
			</dl>
			<dl>
				<dt>交费方式</dt>
				<dd>
				<select class="selectToInput" name="fycrateComVO.payintv">
						<option value="">请选择</option>
						<option value="0"
							<s:if test="fycrateComVO.payintv=='0'.toString()">selected="selected"</s:if>>0趸交
						</option>
						<option value="12"
							<s:if test="fycrateComVO.payintv==12">selected="selected"</s:if>>12期交
						</option>
						<option value="88"
							<s:if test="fycrateComVO.payintv==88">selected="selected"</s:if>>88（长期月交）
						</option>
						<option value="99"
							<s:if test="fycrateComVO.payintv==99">selected="selected"</s:if>>99（不定期交）
						</option>
						
					</select>	
				</dd>
			</dl>
			<dl>
				<dt>交费年期</dt>
				<dd>
					<input type="text" class="digits" min="1" max="1000" name="fycrateComVO.year" id="yearQuery"
						value="${fycrateComVO.year }" />
				</dd>
			</dl>

			<div class="formBarButton">
				<ul>
					<li>
						<button class="but_blue" type="button" 
							onclick="queryBankOrganCommRateCfgList();">查询</button>
					</li>
					<li><a id="exportBankOrganCommRateExcel" onclick="exportBankOrganCommRateExcel()"
									target="dwzExport" targetType="navTab"><button
											class="but_orange">导出</button></a></li>
					<!-- <li><a id="exportBankOrganCommRateExcel" target="dwzExport" targetType="navTab" 
					href="pa/bankorgancommratecfg/exportData_PA_bankOrganCommRateCfgAction.action?" >
					<button onclick="exportBankOrganCommRateExcel()"
											class="but_blue">导出</button> -->
					</a></li>
				</ul>
			</div>
		</div>


		<div class="divfclass">
			<h1>
				<IMG src="udmp/dwz/themes/default/images/skin_img/tubiao.png">查询结果
			</h1>
		</div>
		<div id="bankOrganCommRateQueryCfgDIV" class="tabdivclass main_tabtop">
			<!-- 插入查询结果页面 -->
			<table class="list" width="100%" id="resultTable">
				<thead>
					<tr>
						<th>选择</th>
						<th>管理机构</th>
						<th>银行渠道</th>
						<th>险种编码</th>
						<th>险种名称</th>
						<th>交费方式</th>
						<th>交费年期</th>
						<th>保单年度</th>
						<th>总公司佣金率</th>
						<th>机构佣金率</th>
						<th>佣金率起期</th>
						<th>佣金率止期</th>
						<th>删除</th>
					</tr>
				</thead>
				<tbody id = "bankOrganCommRateQueryCfgTbody">
				<s:if
					test="currentPage.pageItems==null || currentPage.pageItems.size()==0">
					<tr align="center" height="50">
						<td colspan="36">
							<div align="center" style="color: red;"><div style="margin-left: 15%;">没有符合条件的查询结果！</div></div>
						</td>
					</tr>
				</s:if>
					<s:iterator value="currentPage.pageItems" status="st" id="fycrateComVOs">
						<tr>
							<td><input type="radio" id="listIdQuery" name = "listIdQuery" value="${listId }"/></td>
							<td>${organCode }</td>
							<td>${bankBranchCode }</td>
							<td>${riskcode }</td>
							<td>${riskname }</td>
							<td>
								<s:if test="#fycrateComVOs.payintv == ''"></s:if>
								<s:elseif test="#fycrateComVOs.payintv == 0">0趸交</s:elseif>
								<s:elseif test="#fycrateComVOs.payintv == 12">12期交</s:elseif>
								<s:elseif test="#fycrateComVOs.payintv == 88">88（长期月交）</s:elseif>
								<s:else>99（不定期交）</s:else>
							</td>
							<td>${year }</td>
							<td>${curyear }</td>
							<td>${companyRate }</td>
							<td>${branchRate }</td>
							<td><s:date format="yyyy-MM-dd" name="startdate" /></td>
							<td><s:date format="yyyy-MM-dd" name="enddate" /></td>
							<td><a title="删除" class="btnDel" onclick="deleteFycrateCom(this,'${listId}','${startdate}');">删除</a></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
			<!-- 分页查询区域 -->
			<div class="panelBar">
				<div class="pages">
					<span>每页</span>
					<s:select list="#{5:'5',10:'10',20:'20',50:'50'}" name="select"
						onchange="navTabPageBreak({numPerPage:this.value})"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total }条</span>
				</div>
				<div class="pagination"
					totalCount="${currentPage.total }"
					numPerPage="${currentPage.pageSize }" pageNumShown="10"
					currentPage="${currentPage.pageNo }"></div>
			</div>
		</div>
	</form>
	<div class="formBarButton">
		<ul>
			<li>
				<button class="but_blue" type="button"
					onclick="addOrganCommonRate();">新增</button>&nbsp;&nbsp;&nbsp;
				<button class="but_blue" type="button"
					onclick="updateOrganCommonRate();">修改</button>&nbsp;&nbsp;&nbsp;
			</li>
		</ul>
	</div>
	<div class="divfclass" id="divAddOrganCommonRateTuBiao" style="display: none">
		<h1>
			<IMG src="udmp/dwz/themes/default/images/skin_img/tubiao.png">新增机构佣金率
		</h1>
	</div>
	<div class="pageInfoContent" id="divAddOrganCommonRate"
		style="display: none">
		<!-- pageFormInfoContent -->
		<form id="divAddOrganCommonRateForm" action="pa/bankorgancommratecfg/queryCommisionRate_PA_bankOrganCommRateCfgAction.action" method="post"
			enctype="multipart/form-data" style="margin-top: 10px"
			onsubmit="return navTabSearch(this,'bankOrganCommRateAddCfgDIV')">

			<div class="pageFormInfoContent">
				<dl>
					<dt>
						管理机构<font color="red">*</font>
					</dt>
					<dd>
						<%-- <input type="text" id="organCodeAddCfg" class="autocomplete textLeft"
							value="${fycrateComAddVO.organCode }"
							name="fycrateComAddVO.organCode" data-width="151px"
							data-showValue="organCodeCfgAddInput" data-mode="2"
							data-tableName="APP___PAS__DBUSER.T_UDMP_ORG_REL"
							data-tableCol="ORGAN_CODE"
							data-tableDesc="ORGAN_CODE||'-'||ORGAN_NAME " data-separator="-"
							data-like="3" data-queryCounts="6000" data-view="ORGAN_NAME"
							data-orderBy="ORGAN_CODE"/> <input type='text'
							id='organCodeCfgAddInput' class="textRight" readOnly /> --%>
							
							<input id="organCodeAddCfg" name="fycrateComAddVO.organCode"
										value="${fycrateComAddVO.organCode}" type="text"
										style="float: left; width: 30px; border-right: 0px;"
										class="organ searchValue" clickId="organMenuBtn" showOrgName="organCodeCfgAddInput" />
									<input id="organCodeCfgAddInput" name="fycrateComAddVO.organName"
										value="${fycrateComAddVO.organName}"
										style="float: left; width: 110px;" type="text" readOnly /> <a
										id="organMenuBtn" class="btnLook" href="#"
										style="width: 20px;"></a>
					</dd>
				</dl>
				<dl>
					<dt>银行渠道</dt>
					<dd>
					<input id="bankBranchCodeAddCfg" type="text" class="autocomplete textLeft"
						name="fycrateComAddVO.bankBranchCode" 
						value="${fycrateComAddVO.bankBranchCode }"
						data-showValue="bankBranchCodeAddCfgInput" data-mode="2"
						data-disCurrentValFlag="1" data-width="151px"
						data-tableName="APP___PAS__DBUSER.T_BANK_BRANCH T"
						data-tableCol="BANK_BRANCH_CODE" data-queryCounts="1000" 
						data-tableDesc="BANK_BRANCH_CODE||' - '||BANK_BRANCH_NAME" data-separator=" - "
						data-whereClause=" T.ORGAN_CODE = '${fycrateComAddVO.organCode}' AND T.SALES_CHANNEL = '03' AND T.STATE = '0' AND T.BANK_BRANCH_TYPE IN ('00','01','02','03') " data-like="1"
						data-orderBy="BANK_BRANCH_CODE" data-view="BANK_BRANCH_NAME"/>
					<input type="text" id="bankBranchCodeAddCfgInput" class="textRight" readOnly />
					</dd>
				</dl>
				<dl>
					<dt>
						佣金率起期<font color="red">*</font>
					</dt>
					<dd>
						<input type="expandDateYMDRO" id="startDateAddCfg" name="fycrateComAddVO.startdate"/>
						<input type="hidden" name="startDateAddCfgHidden" id="startDateAddCfgHidden" value="" /> 
					</dd>
				</dl>
				<dl>
					<dt>
						佣金率止期<font color="red">*</font>
					</dt>
					<dd>
						<input type="expandDateYMDRO" id="endDateAddCfg" name="fycrateComAddVO.enddate" value ="9999-12-31"/>
					</dd>
				</dl>
				<dl>
					<dt>险种编码<font color="red">*</font></dt>
					<dd>
					<input type="text" id="busiProdCodeAddCfg"
						class="autocomplete textLeft"
						value="${fycrateComAddVO.riskcode }"
						name="fycrateComAddVO.riskcode" data-width="151px"
						data-showValue="busiProdCodeAddCfgInput" data-mode="2"
						data-tableName="APP___PDS__DBUSER.T_BUSINESS_PRODUCT P"
						data-tableCol="P.PRODUCT_CODE_SYS"
						data-tableDesc="P.PRODUCT_CODE_SYS||'-'||P.PRODUCT_NAME_STD "
						data-separator="-" data-like="3" data-queryCounts="600"
						data-view="P.PRODUCT_NAME_STD" data-orderBy="P.PRODUCT_CODE_SYS" /> <input
						type='text' id='busiProdCodeAddCfgInput' class="textRight" readOnly />
						<input type="hidden" name="busiProdCodeAddCfgHidden" id="busiProdCodeAddCfgHidden" value="" /> 
					</dd>
				</dl>

				<div id="bankOrganCommRateAddCfgDIV">
					<!-- 插入查询结果页面 -->
					<%-- <table class="list" width="100%" id="resultTable">
						<thead>
							<tr>
								<th></th>
								<th>交费方式</th>
								<th>交费年期</th>
								<th>保单年度</th>
								<th>总公司佣金率</th>
								<th>机构佣金率<font color="red">*</font></th>
							</tr>
						</thead>
						<tbody>
							<s:iterator value="currentPage1.pageItems" status="st"
								var="salseAmountCfgVO">
								<tr>
									<td><input type="checkbox" name='listId'
										value="${listId }" /></td>
									<td>${bankCode }</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BANK"
											value="${bankCode }" /></td>
									<td>${organCode }</td>
									<td><Field:codeValue
											tableName="APP___PAS__DBUSER.T_UDMP_ORG_REL"
											value="${organCode }" /></td>
									<td><input type="hidden" value="${channelType }" />
									<Field:codeValue tableName="APP___PAS__DBUSER.T_SALES_CHANNEL"
											value="${channelType }" /></td>
								</tr>
							</s:iterator>
						</tbody>
					</table> --%>
				</div>
				<div class="formBarButton">
					<ul style="text-align: left;">
						<li
							style="width: 99%; text-align: center; margin: 0px; padding: 0px">
							<button class="but_blue" type="button"
								onclick="saveBankOrganCommRate();">确认新增</button>&nbsp;&nbsp;&nbsp;
						</li>
					</ul>
				</div>
			</div>
		</form>
	</div>
	<div class="divfclass" id="divUpdateOrganCommonRateTuBiao" style="display:none;">
		<h1>
			<IMG src="udmp/dwz/themes/default/images/skin_img/tubiao.png">修改机构佣金率
		</h1>
	</div>
	<div class="pageInfoContent" id="divUpdateOrganCommonRate"
		style="display:none;">
		<!-- pageFormInfoContent -->
		<form id="divUpdateOrganCommonRateForm" action="" method="post"
			enctype="multipart/form-data" style="margin-top: 10px"
			onsubmit="return iframeCallback(this);">
			<input type="hidden" name="fycrateComUpdateVO.listId" id="fycrateComUpdateVOListId" value="" /> 
			<input type="hidden" name="fycrateComUpdateVO.updateFlag" id="fycrateComUpdateVOUpdateFlag" value="" /> 
			<div class="pageFormInfoContent">
				<dl>
					<dt>管理机构</dt>
					<dd>
					    <input type="text" name="fycrateComUpdateVO.organCode" id="fycrateComUpdateVOrganCode" value=""/> 
					</dd>
				</dl>
				<dl>
					<dt>银行渠道</dt>
					<dd>
					<input type="text" name="fycrateComUpdateVO.bankBranchCode" id="fycrateComUpdateVObankBranchCode" value="" readOnly/> 
					</dd>
				</dl>

				<dl>
					<dt>险种编码</dt>
					<dd>
					<input type="text" name="fycrateComUpdateVO.riskcode" id="fycrateComUpdateVOriskcode" value="" readOnly/> 
					</dd>
				</dl>
				<dl>
					<dt>交费方式</dt>
					<dd>
						<input type="text" id="fycrateComUpdateVOPayintv"
							name="fycrateComUpdateVO.payintv" value="" readOnly/>
					</dd>
				</dl>
				<dl>
					<dt>交费年期</dt>
					<dd>
					    <input type="text" id="fycrateComUpdateVOYear"
							name="fycrateComUpdateVO.Year" value="" readOnly/>
					</dd>
				</dl>
				<dl>
					<dt>保单年度</dt>
					<dd>
						<input type="text" id="fycrateComUpdateVOCuryear"
							name="fycrateComUpdateVO.curyear" value="" readOnly/>
					</dd>
				</dl>
				<dl>
					<dt>总公司佣金率</dt>
					<dd>
						<input type="text" id="fycrateComUpdateVOCompanyRate"
							name="fycrateComUpdateVO.companyRate" value="" readOnly/>
					</dd>
				</dl>
				<dl>
					<dt>分公司佣金率</dt>
					<dd>
						<input type="text" id="fycrateComUpdateVOBranchRate"
							name="fycrateComUpdateVO.branchRate" value="" />
					</dd>
				</dl>
				<dl>
					<dt>佣金率起期</dt>
					<dd>
						<input id="fycrateComUpdateVOStartdate"
							name="fycrateComUpdateVO.startdate"" />
					</dd>
				</dl>
				<dl>
					<dt>
						佣金率止期<font color="red">*</font>
					</dt>
					<dd>
						<input type="expandDateYMDRO" id="fycrateComUpdateVOEnddate"
							name="fycrateComUpdateVO.enddate"
							value="" />
					</dd>
				</dl>

				<div class="formBarButton">
					<ul style="text-align: left;">
						<li
							style="width: 99%; text-align: center; margin: 0px; padding: 0px">
							<button class="but_blue" type="button"
								onclick="updateBankOrganCommRate();">确认修改</button>&nbsp;&nbsp;&nbsp;
						</li>
					</ul>
				</div>
			</div>
		</form>
	</div>
</div>
<script type="text/javascript" charset="utf-8">
  var $n = navTab.getCurrentPanel(); 
    //查询页面刷新银代渠道下拉
	$("#organCodeQueryCfg", $n).blur(function() {
		setTimeout(function() {
			var organCode = $("#organCodeQueryCfg", $n).val();
			var whereClause = " T.ORGAN_CODE = '"+organCode+ "' AND T.SALES_CHANNEL = '03' AND T.STATE = '0' ";
			$("#bankBranchCodeQueryCfg", $n).attr("data-whereClause", whereClause);
			initInputAutocomplete("bankBranchCodeQueryCfg", "bankBranchCodeQueryCfgInput");
		},100);
		//
	});
	
	//新增页面刷新银代渠道下拉
	$("#organCodeAddCfg", $n).bind("input propertychange",function() {
		setTimeout(function() {
			var organCode = $("#organCodeAddCfg", $n).val();
			var whereClause = " T.ORGAN_CODE = '"+organCode+ "' AND T.SALES_CHANNEL = '03' AND T.STATE = '0' AND T.BANK_BRANCH_TYPE IN ('00','01','02','03')  ";
			$("#bankBranchCodeAddCfg", $n).attr("data-whereClause", whereClause);
			initInputAutocomplete("bankBranchCodeAddCfg", "bankBranchCodeAddCfgInput");
		},100);
		//
	});
	
	//新增页面刷新银代渠道下拉
	$("#startDateAddCfg", $n).bind("input propertychange",function() {
			var startDate = $("#startDateAddCfg", $n).val();
			var busiProdCode = $("#busiProdCodeAddCfg", $n).val();
			var startDateAddCfgHidden = $("#startDateAddCfgHidden", $n).val();
			if(startDate.length == 10 && startDate == startDateAddCfgHidden) {
				$("#startDateAddCfgHidden", $n).val(startDate);
				return false;
			}
			if (isNulOrEmpty(startDate) || isNotDefined(startDate) || startDate.length != 10) {
				$("#bankOrganCommRateAddCfgDIV", $n).find("table").remove();
				return false;
			}
			if (isNulOrEmpty(busiProdCode) || isNotDefined(busiProdCode) || busiProdCode.length != 8) {
				$("#bankOrganCommRateAddCfgDIV", $n).find("table").remove();
				return false;
			}
			if(startDate.length == 10 && startDate != startDateAddCfgHidden) {
				// 查询总公司佣金率列表
				setTimeout(function() {
					$("#startDateAddCfgHidden", $n).val(startDate);
					$("#divAddOrganCommonRateForm", $n).submit();
				},500);
			}
			$("#bankOrganCommRateAddCfgDIV", $n).initUI();
	});
	
	//新增页面刷新银代渠道下拉
	$("#busiProdCodeAddCfg", $n).bind("input propertychange",function() {
		var startDate = $("#startDateAddCfg", $n).val();
		var busiProdCode = $("#busiProdCodeAddCfg", $n).val();
		var busiProdCodeAddCfgHidden = $("#busiProdCodeAddCfgHidden", $n).val();
		if(busiProdCode.length == 8 && busiProdCode == busiProdCodeAddCfgHidden) {
			$("#busiProdCodeAddCfgHidden", $n).val(busiProdCode);
			return false;
		}
		if (isNulOrEmpty(startDate) || isNotDefined(startDate) || startDate.length != 10) {	
			$("#bankOrganCommRateAddCfgDIV", $n).find("table").remove();
			return false;
		}
		if (isNulOrEmpty(busiProdCode) || isNotDefined(busiProdCode) || (busiProdCode.length != 8 && busiProdCodeAddCfgHidden.length == 8)) {
			$("#bankOrganCommRateAddCfgDIV", $n).find("table").remove();
			$("#busiProdCodeAddCfgHidden", $n).val(busiProdCode);
			return false;
		}
		if(busiProdCode.length == 8 && busiProdCode != busiProdCodeAddCfgHidden) {
			// 查询总公司佣金率列表
			setTimeout(function() {
				$("#busiProdCodeAddCfgHidden", $n).val(busiProdCode);
				$("#divAddOrganCommonRateForm", $n).submit();
			},500);
		}
		$("#bankOrganCommRateAddCfgDIV", $n).initUI();
	});
	function addOrganCommonRate() {
		$("#divAddOrganCommonRateTuBiao", $n).attr("style","display:block");
		$("#divAddOrganCommonRate", $n).attr("style","display:block");
		$("#divUpdateOrganCommonRateTuBiao", $n).attr("style","display:none");
		$("#divUpdateOrganCommonRate", $n).attr("style","display:none");
	}
	//确认修改
	function updateBankOrganCommRate() {
		//1、	修改后佣金率止期不能早于等于系统当前日期，否则阻断提示：‘佣金率止期不能早有等于系统当前日期。’
		var flag = false;
		var endDate = $("#fycrateComUpdateVOEnddate",$n).val();
       	var currentDate = new Date();
       	var year1 = currentDate.getFullYear();
		var month1 = ('0'+(currentDate.getMonth() + 1)).slice(-2);
		var date1 = ('0'+currentDate.getDate()).slice(-2);
		
    	var now = year1+'-'+month1+'-'+date1;
   	    if (endDate <= now) {
   	    	flag = true;
   	    }
   	    if(flag) {
			alertMsg.warn("佣金率止期不能早于等于系统当前日期。");
			return false;
		}
   	 // 3、	修改后的佣金率起止日期与数据库已有的“管理机构+银行渠道+险种+交费方式+交费年期+保单年度”相同的机构佣金率配置信息日期重叠，系统阻断提示：‘与系统已有的机构佣金率的佣金率起止日期重叠，不支持修改！’。
   	    $("#fycrateComUpdateVOUpdateFlag", $n).val("1");
   	    $.ajax({
	        url : "pa/bankorgancommratecfg/updateBankOrganCommRateCfg_PA_bankOrganCommRateCfgAction.action",
	        type : "post",
	        data : $("#divUpdateOrganCommonRateForm",$n).serializeArray(),
	        async : true,
	        dataType : "json",
	        success : function(s) {
	            if (s.statusCode == DWZ.statusCode.ok) {
	            	$("#fycrateComUpdateVOUpdateFlag", $n).val("2");
	            	// 校验通过后，更跟新佣金率止期；更新成功后刷新“查询结果”不显示此修改模块
	            	$.ajax({
	        	        url : "pa/bankorgancommratecfg/updateBankOrganCommRateCfg_PA_bankOrganCommRateCfgAction.action",
	        	        type : "post",
	        	        data : $("#divUpdateOrganCommonRateForm",$n).serializeArray(),
	        	        async : true,
	        	        dataType : "json",
	        	        success : function(s) {
	        	            if (s.statusCode == DWZ.statusCode.ok) {
	        	                alertMsg.correct(s.message);
	        	                queryBankOrganCommRateCfgList(); 
	        	            } else {
	        	                alertMsg.error(s.message);
	        	            }
	        	        }
	        	    });
	            } else {
	            	$("#fycrateComUpdateVOUpdateFlag", $n).val("");
	            	if (s.message=="更新失败") {
		        		 alertMsg.error("更新失败.");
		        	 }else {
			             alertMsg.warn("与系统已有的机构佣金率的佣金率起止日期重叠，不支持修改！");
		        	 }
	            	
	            }
	        }
	    });
   	    
   	    
		
	};
	
	function queryBankOrganCommRateCfgList() {
		//判断必填项是否为空
		var organCode = $("#organCodeQueryCfg", $n).val().trim();
		if (organCode == null || organCode == '') {	
			alertMsg.warn('管理机构条件不能为空。');
			return false;
		} 
		// 调用dwz，对输入数据进行校验
		if (!$("#bankOrganCommRateQueryCfgForm", navTab.getCurrentPanel()).valid()) {
			return false;
		}
		$("#bankOrganCommRateQueryCfgForm", $n).attr("action","pa/bankorgancommratecfg/search_PA_bankOrganCommRateCfgAction.action");
	    $("#bankOrganCommRateQueryCfgForm", $n).submit();
	};
	
	//导出清单数据 
	function exportBankOrganCommRateExcel(){
		//判断必填项是否为空
		var organCode = $("#organCodeQueryCfg", $n).val().trim();
		if (organCode == null || organCode == '') {	
			alertMsg.warn('管理机构条件不能为空。');
			return false;
		} 
		alertMsg.confirm("确实要导出这些记录吗?", {
	        okCall: function(){
	        	var $form = $("#bankOrganCommRateQueryCfgForm", $n);
	        	var action="pa/bankorgancommratecfg/exportData_PA_bankOrganCommRateCfgAction.action";
	        	$form.attr('action', action);
	        	$form.attr('onsubmit', null);
	        	$form.submit();
 			}   
		});
	};
	
	// 点击删除按钮
	function deleteFycrateCom(obj,listId,startDate) {
		var currentDate = new Date();
		var compareDate = new Date(startDate);
	    if (compareDate <= currentDate) {
	        alertMsg.warn("此配置已生效，不允许删除。");
	        return false;
	    }
	    $.ajax({
	        url : "pa/bankorgancommratecfg/deletebankOrganCommRateCfg_PA_bankOrganCommRateCfgAction.action?fycrateComVO.listId=" + listId,
	        type : "post",
	        global : false,
	        cache : false,
	        async : true,
	        dataType : "json",
	        success : function(s) {
	            if (s.statusCode == DWZ.statusCode.ok) {
	                alertMsg.correct(s.message);
	                queryBankOrganCommRateCfgList(); 
	            } else {
	                alertMsg.error(s.message);
	            }
	        }
	    });
	};
	
	function updateOrganCommonRate() {
		//1、如果未选择要修改的配置信息，系统阻断提示：‘请选择要修改的配置信息。’。
		var modifySize = $("tbody#bankOrganCommRateQueryCfgTbody", $n).find("input:radio:checked").size();
	    if (modifySize == 0) {
	        alertMsg.warn("请选择要修改的配置信息。");
	        return false;
	    }
		//2、如果“佣金率止期”早于等于系统当前日期，则系统阻断提示：‘佣金率止期早于等于系统当前日期，不支持修改。’。
		var flag = false;
		$("tbody#bankOrganCommRateQueryCfgTbody", $n).find("input:radio:checked").parent().parent().find("td").each(function(i) {
	        var $obj = $(this);
	        if(i==11) {
	        	var endDate = $obj.text();
	        	var currentDate = new Date();
	        	var year1 = currentDate.getFullYear();
	    		var month1 = ('0'+(currentDate.getMonth() + 1)).slice(-2);
	    		var date1 = ('0'+currentDate.getDate()).slice(-2);
	    		
	        	var now = year1+'-'+month1+'-'+date1;
	    	    if (endDate <= now) {
	    	    	flag = true;
	    	        return false;
	    	    }
	        }
	    });
		if(flag) {
			alertMsg.warn("佣金率止期早于等于系统当前日期，不支持修改。");
			return false;
		}
		//3、	如果“佣金率起期”晚于系统当前日期，则系统阻断提示：‘该配置未启用，请直接删除！’
        var flag1 = false;
		$("tbody#bankOrganCommRateQueryCfgTbody", $n).find("input:radio:checked").parent().parent().find("td").each(function(i) {
	        var $obj = $(this);
	        if(i==10) {
	        	var startDate = $obj.text();
	        	var currentDate = new Date();
	        	var year1 = currentDate.getFullYear();
	    		var month1 = ('0'+(currentDate.getMonth() + 1)).slice(-2);
	    		var date1 = ('0'+currentDate.getDate()).slice(-2);
	    		
	        	var now = year1+'-'+month1+'-'+date1;
	    	    if (startDate > now) {
	    	    	flag1 = true;
	    	        return false;
	    	    }
	        }
	    });
		if(flag1) {
			alertMsg.warn("该配置未启用，请直接删除！");
			return false;
		}
		$("#divAddOrganCommonRateTuBiao", $n).attr("style","display:none");
		$("#divAddOrganCommonRate", $n).attr("style","display:none");
		$("#divUpdateOrganCommonRateTuBiao", $n).attr("style","display:block");
		$("#divUpdateOrganCommonRate", $n).attr("style","display:block");
		
		$("tbody#bankOrganCommRateQueryCfgTbody", $n).find("input:radio:checked").parent().parent().find("td").each(function(i) {
	        var $obj = $(this);
	        if(i==0) {
	        	var listId = $obj.find("input").val();
	        	$("#fycrateComUpdateVOListId",$n).val(listId);
	        }
	        if(i==1) {
	        	var organCode = $obj.text();
	        	$("#fycrateComUpdateVOrganCode",$n).val(organCode);
	        }
	        if(i==2) {
	        	var bankBranchCode = $obj.text();
	        	$("#fycrateComUpdateVObankBranchCode",$n).val(bankBranchCode);
	        }
	        if(i==3) {
	        	var riskcode = $obj.text();
	        	$("#fycrateComUpdateVOriskcode",$n).val(riskcode);
	        }
	        if(i==5) {
	        	var Payintv = $obj.text();
	        	$("#fycrateComUpdateVOPayintv",$n).val(Payintv);
	        }
	        if(i==6) {
	        	var Year = $obj.text();
	        	$("#fycrateComUpdateVOYear",$n).val(Year);
	        }
	        if(i==7) {
	        	var listId = $obj.text();
	        	$("#fycrateComUpdateVOCuryear",$n).val(listId);
	        }
	        if(i==8) {
	        	var CompanyRate = $obj.text();
	        	$("#fycrateComUpdateVOCompanyRate",$n).val(CompanyRate);
	        }
	        if(i==9) {
	        	var BranchRate = $obj.text();
	        	$("#fycrateComUpdateVOBranchRate",$n).val(BranchRate);
	        }
	        if(i==10) {
	        	var Startdate = $obj.text();
	        	$("#fycrateComUpdateVOStartdate",$n).val(Startdate);
	        }
	        
	        if(i==11) {
	        	var enddate = $obj.text();
	        	$("#fycrateComUpdateVOEnddate",$n).val(enddate);
	        }
	    });
	};
	
	
	
	
	function saveBankOrganCommRate() {
		//管理机构
		var organCode = $("#organCodeAddCfg", $n).val().trim();
		//佣金率起期
		var startDate = $("#startDateAddCfg", $n).val().trim();
		//佣金率止期
		var endDate = $("#endDateAddCfg", $n).val().trim();
		// 险种代码
		var busiProdCode = $("#busiProdCodeAddCfg", $n).val().trim();
		// 银代渠道
		var bankBranchCode = $("#bankBranchCodeAddCfg", $n).val().trim();
		//1、	管理机构、佣金率起期、佣金率止起、险种不能为空，否则系统阻断提示：‘必填项未录入，请确认！’
		if (isNulOrEmpty(organCode) || isNulOrEmpty(startDate) || isNulOrEmpty(endDate) || isNulOrEmpty(busiProdCode)) {	
			alertMsg.warn('必填项未录入，请确认！');
			return false;
		} 
		//2、	“佣金率起期”不能早于等于系统当前日期，否则系统阻断提示：‘佣金率起期不能早于等于系统当前日期。’
		var currentDate = new Date();
		var year1 = currentDate.getFullYear();
		var month1 = ('0'+(currentDate.getMonth() + 1)).slice(-2);
		var date1 = ('0'+currentDate.getDate()).slice(-2);
		
    	var now = year1+'-'+month1+'-'+date1;
	    if (startDate <= now) {
	        alertMsg.warn("佣金率起期不能早于等于系统当前日期。");
	        return false;
	    }
	    //3、	“佣金率止期”不能早于“佣金率起期”，否则系统阻断提示：‘佣金率止期不能早于佣金率起期。’
	    if (endDate < startDate) {
	        alertMsg.warn("佣金率止期不能早于佣金率起期。");
	        return false;
	    }
	    // 4、“机构佣金率”全部都没录入，则阻断提示：‘请录入机构佣金率！’
	    var flag = false;
	    $("table#bankOrganCommRateAddCfgTable tr:gt(0)").each(function(i){
			var branchRate = $(this).children("td:eq(4)").children().val();
			if(isNulOrEmpty(branchRate)) {
				flag = true;
				return false;
			}
		});
	    if(flag) {
	    	alertMsg.warn("请录入机构佣金率！");
	        return false;
	    }
	    
	    var flag3 = false;
	    $("table#bankOrganCommRateAddCfgTable tr:gt(0)").each(function(i){
			var branchRate = $(this).children("td:eq(4)").children().val();
			if(!(/^0|(0\.[0-9]\d{1,4}?)$/.test(branchRate))) {
				flag3 = true;
				return false;
			}
		});
	    if(flag3) {
	    	alertMsg.warn("机构佣金率仅支持录入大于等于0小于1，精度为小数点后四位的数字");
	        return false;
	    }
	    
		// 5、	如果此产品存在“险种代码+交费方式+交费年期+保单年度”相同但单“总公司佣金率”不同的数据，则阻断提示：‘此产品存在“险种代码+交费方式+交费年期+保单年度”相同但单“总公司佣金率”不同的数据，不支持配置机构佣金率！’
		var flag2 = false;
		$("table#bankOrganCommRateAddCfgTable tr:gt(0)").each(function(i){
			//交费方式
	    	var payintv = $(this).find("td:eq(0)").text();
	    	//交费年期
	    	var year = $(this).find("td:eq(1)").text();
	    	//保单年度
	    	var curyear = $(this).find("td:eq(2)").text();
	    	//总公司佣金率
	    	var companyRate = $(this).find("td:eq(3)").text();
	    	var flag1 = false;
			$("table#bankOrganCommRateAddCfgTable tr:gt("+i+")").each(function(j) {
				//交费方式
		    	var payintvj = $(this).find("td:eq(0)").text();
		    	//交费年期
		    	var yearj = $(this).find("td:eq(1)").text();
		    	//保单年度
		    	var curyearj = $(this).find("td:eq(2)").text();
		    	//总公司佣金率
		    	var companyRatej = $(this).find("td:eq(3)").text();
		    	if(payintv==payintvj && year==yearj && curyear==curyearj && companyRate!=companyRatej) {
		    		flag1 = true;
		    		return false;
		    	}
			});
			if(flag1) {
				flag2 = true;
				return false;
			}
		}); 
		if(flag2) {
	    	alertMsg.warn("此产品存在“险种代码+交费方式+交费年期+保单年度”相同但“总公司佣金率”不同的数据，不支持配置机构佣金率！");
	        return false;
	    }
	    
		var jsonArray = new Array();
    	var bankOrganCommRateArr =["organCode", "bankBranchCode", "riskcode","startDate", "endDate", "payintv","year","curyear","companyRate","branchRate"];
    	jsonArray = "[";
	    $("table#bankOrganCommRateAddCfgTable tr:gt(0)").each(function(i){
	    	//交费方式
	    	var payintv = $(this).find("td:eq(0)").text();
	    	//交费年期
	    	var year = $(this).find("td:eq(1)").text();
	    	//保单年度
	    	var curyear = $(this).find("td:eq(2)").text();
	    	//总公司佣金率
	    	var companyRate = $(this).find("td:eq(3)").text();
	    	//机构佣金率
	    	var branchRate = $(this).find("td:eq(4)").find("input").val();
	    	jsonArray += "{";
	    	jsonArray = jsonArray + "'" +bankOrganCommRateArr[0] + "'" + ":'" + organCode + "',";
	    	jsonArray = jsonArray + "'" +bankOrganCommRateArr[1] + "'" + ":'" + bankBranchCode + "',";
	    	jsonArray = jsonArray + "'" +bankOrganCommRateArr[2] + "'" + ":'" + busiProdCode + "',";
	    	jsonArray = jsonArray + "'" +bankOrganCommRateArr[3] + "'" + ":'" + startDate + "',";
	    	jsonArray = jsonArray + "'" +bankOrganCommRateArr[4] + "'" + ":'" + endDate + "',";
	    	jsonArray = jsonArray + "'" +bankOrganCommRateArr[5] + "'" + ":'" + payintv + "',";
	    	jsonArray = jsonArray + "'" +bankOrganCommRateArr[6] + "'" + ":'" + year + "',";
	    	jsonArray = jsonArray + "'" +bankOrganCommRateArr[7] + "'" + ":'" + curyear + "',";
	    	jsonArray = jsonArray + "'" +bankOrganCommRateArr[8] + "'" + ":'" + companyRate + "',";
	    	jsonArray = jsonArray + "'" +bankOrganCommRateArr[9] + "'" + ":'" + branchRate + "',";
	    	jsonArray = jsonArray.substring(0, jsonArray.length-1);
			jsonArray += "},";
		}); 
	    jsonArray = jsonArray.substring(0, jsonArray.length-1);
    	jsonArray += "]";
    	//判断json的数据是否为空
    	if(jsonArray.length == 1 ){
    		alertMsg.warn("请录入机构佣金率！");
    		return false;
    	}
    	//6、	如果系统已存在“管理机构+银行渠道+险种+交费方式+交费年期+保单年度”相同且佣金率起止期间重叠的机构佣金率配置数据时，系统阻断提示：‘已存在此佣金率配置数据，不支持再次增加。’
    	$.ajax({
  	          url : "pa/bankorgancommratecfg/queryOrganCommRateIsRepeat_PA_bankOrganCommRateCfgAction.action",
			  type : "post",
			  async: true,
			  data : {"fycrateComVOs":jsonArray},
			  dataType : "json",
			  success : function (json){
				  if (json.statusCode == DWZ.statusCode.ok) {
					  alertMsg.confirm("请确认该险种佣金率调整已按照要求完成审批、公示等相关流程.", {
				            okCall : function() {
				            	$.ajax({
						  	          url : "pa/bankorgancommratecfg/saveOrganCommRate_PA_bankOrganCommRateCfgAction.action",
									  type : "post",
									  async: true,
									  data : {"fycrateComVOs":jsonArray},
									  dataType : "json",
									  success : function (json){
										  if (json.statusCode == DWZ.statusCode.ok) {
											  alertMsg.warn("保存成功");
											  queryBankOrganCommRateCfgList(); 
								            } else {
								                alertMsg.warn("保存失败");
								            }
								      }
				            	}); 
				            },
				            cancelCall : function() {}
		            }); 
		         } else if(json.statusCode == DWZ.statusCode.error){
		        	 if (json.message=="保存失败") {
		        		 alertMsg.error("保存失败.");
		        	 }else {
			             alertMsg.warn("已存在此佣金率配置数据，不支持再次增加。");
		        	 }
						
		         } 
			  }
    	});
	}; 
	
</script>