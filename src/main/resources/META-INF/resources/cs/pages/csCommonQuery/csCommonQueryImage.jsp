<!-- 综合查询列表页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<style type="text/css">
	th,td {
		white-space:normal;
	}
	.searchBar li label{
		width: 120px;
	}
</style>
<input type="hidden" id="errorMsg" value="${errorMsg}"/>
<form style="display:none;" id="pagerForm" method="post" action="pa/commonQuery/findPolicyInfos_PA_commonQueryAction.action?menuId=${menuId}">
    <input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
    <input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
</form>

<form method="post" action="${ctx}/cs/csCommonQuery/findPolicyInfos2_PA_csCommonQueryAction.action?menuId=${menuId}"
	class="required-validate" onsubmit="return navTabSearch(this);" id="commonQueryForm" rel="pagerForm">
	<input type="hidden" id="menuId" value="${menuId}"/>
	<div class="pageContent" layoutH="35">
		<div class="panel">
			<h1>查询条件</h1>
			<div class="searchBar" id="">
				<input id="checkedRadios" type="hidden" value="${commonQueryVO.checkedRadios}"/>
				<ul class="searchContent" style="height: 30px;">
					<li class="nowrap">
						<label>
							<input name="commonQueryVO.checkedRadio" value="policyCode" type="radio" />保单号
						</label>
						<input type="text" id="policyCodeRadio" name="policyInfoQueryVO.policyCode" 
							onfocus="checkSearch('policyCode')" value="${policyInfoQueryVO.policyCode}"/>
					</li>
				</ul>
				<ul class="searchContent" style="height: 30px;">
					<li class="nowrap">
						<label>
							<input name="commonQueryVO.checkedRadio" value="applyCode" type="radio" />投保单号
						</label>
						<input type="text" id="applyCodeRadio" name="commonQueryVO.applyCode" 
							onfocus="checkSearch('applyCode')" value="${commonQueryVO.applyCode}"/>
					</li>
				</ul>
				<ul class="searchContent" style="height: 30px;">
					<li class="nowrap">
						<label>
							<input name="commonQueryVO.checkedRadio" value="customerName" type="radio" />客户姓名
						</label>
						<input id="customerNameRadio" type="text" name="commonQueryVO.customerVO.customerName" 
							onfocus="checkSearch('customerName')" value="${commonQueryVO.customerVO.customerName}"/>
					</li>
				</ul>
				<ul class="searchContent" style="height: 30px;">
					<li class="nowrap">
						<label>
							<input name="commonQueryVO.checkedRadio" value="customerCertiCode" type="radio" />客户证件号码
						</label>
						<input type="text" id="customerCertiCodeRadio" name="commonQueryVO.customerVO.customerCertiCode" 
							onfocus="checkSearch('customerCertiCode')" value="${commonQueryVO.customerVO.customerCertiCode}"/>
					</li>
				</ul>
				<ul class="searchContent" style="height: 30px;">
					<li class="nowrap">
						<label>
							<input name="commonQueryVO.checkedRadio" value="customerBirthday" type="radio" />客户出生日期
						</label>
						<input type="expandDateYMD" id="customerBirthdayRadio" name="commonQueryVO.customerVO.customerBirthday" 
							onfocus="checkSearch('customerBirthday')" value="<s:date name="commonQueryVO.customerVO.customerBirthday" format="yyyy-MM-dd"/>"/>
					</li> 
				</ul>
				<ul class="searchContent" style="height: 30px;">
					<li class="nowrap">
						<label>
							<input name="commonQueryVO.checkedRadio" value="account" type="radio" />银行转账帐户号
						</label>
						<input type="text" name="commonQueryVO.account" id="accountRadio"
							onfocus="checkSearch('account')" value="${commonQueryVO.account}"/>
					</li>
				</ul>
				<ul class="searchContent" style="height: 30px;">
					<li class="nowrap">
						<label>
							<input name="commonQueryVO.checkedRadio" value="agentOrBankBranch" type="radio" />代理人/网点代码
						</label>
						<input type="text" name="commonQueryVO.agentOrBankBranch" id="agentOrBankBranchRadio"
							onfocus="checkSearch('agentOrBankBranch')" value="${commonQueryVO.agentOrBankBranch}"/>
					</li>
				</ul>
				<div class="subBar">
					<table style="width: 100%;">
						<tr>
							<td width="45%">&nbsp;</td>
							<td width="10%">
								<div class="buttonActive">
									<div class="buttonContent">
										<button type="button" style="width: 50px;" onclick="commonQuery_checkSubmit()">查询</button>
									</div>
								</div>
							</td>
							<td width="45%">&nbsp;</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
		<s:if test="0 != currentPage.pageItems.size">
		<div class="panel" minH="95" id="customerPanel">
			<h1>客户清单</h1>
			<div class="searchBar" id="">
				<table class="list" style="width: 100%">
					<thead>
						<tr>
							<th>客户编号</th>
							<th>姓名</th>
							<th>性别</th>
							<th>出生日期</th>
							<th>证件类型</th>
							<th>证件号码</th>
						</tr>
					</thead>
					<tbody>
						<s:iterator value="currentPage.pageItems" id="">
							<tr>
								<td><a href="pa/commonQuery/customerDetailInfo_PA_customerDetailInfoAction.action?role=${customerRoles}
										&customerVO.customerId=${customerId}&menuId=${menuId}" 
									rel="customerDetail" title="客户个人信息" target="navTab">${customerId}</a></td>
								<td>
									<a href="${ctx}/cs/csCommonQuery/findPolicyInfosByCustomerId_PA_csCommonQueryAction.action?commonQueryVO.customerVO.customerId=${customerId}
										<%-- &commonQueryVO.customerVO.customerName=${commonQueryVO.customerVO.customerName}&commonQueryVO.customerVO.customerBirthday=${commonQueryVO.customerVO.customerBirthday}
										&commonQueryVO.customerVO.customerCertiCode=${commonQueryVO.customerVO.customerCertiCode}&commonQueryVO.policyCode=${commonQueryVO.policyCode}
										&commonQueryVO.applyCode=${commonQueryVO.applyCode}&commonQueryVO.account=${commonQueryVO.account}
										&commonQueryVO.agentOrBankBranch=${commonQueryVO.agentOrBankBranch} --%>
										&commonQueryVO.checkedRadio=${commonQueryVO.checkedRadios}&menuId=${menuId}" rel="resultPanel"  
										onclick="showResultPanel()" target="ajax">
										${customerName}
									</a>
								</td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER" value='${customerGender}'/></td>
								<td><s:date name="customerBirthday" format="yyyy-MM-dd"/></td>
								<td><Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value='${customerCertType}'/></td>
								<td>${customerCertiCode}</td>
							</tr>
						</s:iterator>
					</tbody>
				</table>
		        <div class="panelBar" >
			        <div class="pages">
			            <span>显示</span>
						<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}" name="select"
 							onchange="navTabPageBreak({numPerPage:this.value})" value="currentPage.pageSize">
 						</s:select> 
<%-- 			            <select class="combox" name="numPerPage" onchange="navTabPageBreak({numPerPage:this.value})">
			            	<option value="10">10</option>
			            	<option value="20">20</option>
			            	<option value="50">50</option>
			            	<option value="100">100</option>
			            	<option value="200">200</option>
			            </select>
 --%>			            
 						<span>条，共${currentPage.total}条</span>        
			        </div>
			        <div class="pagination" targetType="navTab" totalCount="${currentPage.total}" numPerPage="${currentPage.pageSize}" 
			        	pageNumShown="10" currentPage="${currentPage.pageNo}"></div>
			    </div>
			</div>
		</div>
		</s:if>
		<div id="resultPanel" >
			<s:if test="policyList!=null&&policyList.size()>0">
			<%@include file="/cs/pages/csCommonQuery/csCommonQueryResultPanel.jsp" %>
			</s:if>
		</div>
	</div>
	
	<div class="formBar" style="height: auto;">
	<table style="width: 100%;">
		<tr>
			<td width="45%">&nbsp;</td>
			<td width="10%">
				<div class="buttonActive">
					<div class="buttonContent">
						<button type="button" style="width: 50px;" class="close">退出</button>
					</div>
				</div>
			</td>
			<td width="45%">&nbsp;</td>
		</tr>
	</table>
	</div>
</form>
<script type="text/javascript">
	$(document).ready(function() {
		
		
		// 客户列表面板默认隐藏
		$("#customerPanel", navTab.getCurrentPanel()).hide();
//	$("#resultPanel", navTab.getCurrentPanel()).hide();
		// 回显选中
		var checkedRadios = $("#checkedRadios", navTab.getCurrentPanel()).val();
		var checkedRadioArray = checkedRadios.split(",");
		for(var i = 0;i < checkedRadioArray.length;i++){
			var checkedRadio = checkedRadioArray[i];
			if(checkedRadio != '' && checkedRadio != undefined){
				$("input[type='radio'][value=" + checkedRadio + "]", navTab.getCurrentPanel()).attr(
						"checked", "checked");
				// 当检索条件为客户信息时显示客户列表面板
				if(checkedRadio == "customerName" || checkedRadio == "customerCertiCode" || checkedRadio == "customerBirthday"){
					$("#customerPanel", navTab.getCurrentPanel()).show();
				}
			}
		};
		
		var menuId = $("#menuId", navTab.getCurrentPanel()).val();
		// 确定数据是保单还是投保单
		var isApplyOrPolicy = $("#isApplyOrPolicy", navTab.getCurrentPanel()).val();
		if(isApplyOrPolicy != '' && isApplyOrPolicy != undefined){
			var applyCodeTD = $("#applyCodeTD", navTab.getCurrentPanel()).text();
			if(isApplyOrPolicy != "PA"){
				var $applyCodeTD = $("#applyCodeTD", navTab.getCurrentPanel());
				$applyCodeTD.replaceWith('<td id="applyCodeTD"><a href="pa/commonQuery/getPolicyInfo_PA_commonQueryAction.action?commonQueryVO.applyCode='+applyCodeTD+'&commonQueryVO.isApplyOrPolicy='+isApplyOrPolicy+'&menuId='+menuId+'" rel="'+menuId+'" title="综合查询" target="navTab">'+applyCodeTD+'</a></td>');
			}
		};
		
		// 异常信息
		var errorMsg = $("#errorMsg").val();
		if(undefined != errorMsg && errorMsg != ""){
			alertMsg.warn(errorMsg);
		}
	});

	function showResultPanel(){
		$("#resultPanel", navTab.getCurrentPanel()).show();
	};
	
	function checkSearch(radioId) {
		var para = radioId;
		$("input[type='radio'][value=" + para + "]", navTab.getCurrentPanel()).attr(
				"checked", "checked");
	};
	
	//查询按钮
	function commonQuery_checkSubmit(){
		var para = $("input[type='radio']:checked",navTab.getCurrentPanel()).val();
		if (para == null) {
			alertMsg.warn('您还没有填写查询条件，请填写条件后再查询！');
			return;
		} else {
			var param = $('#'+para+'Radio',navTab.getCurrentPanel()).val();
// 			alert(param);
			if (param == '' || param == undefined) {
				alertMsg.warn('您还没有填写查询条件，请填写条件后再查询！');
				return;
			} else {
				var $form = $("#commonQueryForm", navTab.getCurrentPanel());
				var checkedRadios = $("input[type='radio'][checked='checked']",navTab.getCurrentPanel()).val();
// 				alert(checkedRadios);
				var checkedRadioArray = checkedRadios.split(",");
				for(var i = 0;i < checkedRadioArray.length;i++){
					var checkedRadio = checkedRadioArray[i];
					if(checkedRadio != '' && checkedRadio != undefined){
						// 录入查询条件为保单号和投保单号时跳转到保单信息页面
						var menuId = $("#menuId", navTab.getCurrentPanel()).val();
						if(checkedRadio == "policyCode"){
							var action = "${ctx}/cs/csCommonQuery/getPolicyInfo2_PA_csCommonQueryAction.action?policyInfoQueryVO.isApplyOrPolicy=CS&menuId="+menuId;
							$form.attr("action",action);
						} else if(checkedRadio == "applyCode"){
// 							var action = "pa/commonQuery/getPolicyInfo_PA_commonQueryAction.action?commonQueryVO.isApplyOrPolicy=NB&menuId="+menuId;
							var action = "pa/commonQuery/getCommonQueryTabs2_PA_commonQueryAction.action?commonQueryVO.isApplyOrPolicy=NB&menuId="+menuId;
							$form.attr("action",action);
						}
// 						alert($form.attr("action"));
						$form.submit();
					};
				};
			};
		};
	};
	
	
</script>
