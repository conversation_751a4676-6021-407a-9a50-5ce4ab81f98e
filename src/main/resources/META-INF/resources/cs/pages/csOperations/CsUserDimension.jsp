<!-- 总体业务维度页面 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include
	file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css"
	type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<style type="text/css">
th,td {
	white-space: normal;
}

.searchBar li label {
	width: 105px;
}
</style>
<div class="pageFormContent"  id="queryCostFeeCfgDiv" >
<div class="divfclass"> 
	<h1>
	   <img src="images/tubiao.png" >保全集中复核-用户维度运营视图
	</h1>
</div>	
<div class="pageFormContent">
		<form id="pagerForm" onsubmit="return navTabSearch(this);" action=" ${ctx}/cs/csOperationsQuery/selectUserDimension_PA_csOperationsAction.action"  method="post">
			<input type="hidden" name="pageNum" value="${pageNum}" />
			<input type="hidden" name="numPerPage" value="${page.pageSize}" />
		</form>
	<form id="messageForm" action="${ctx}/cs/csOperationsQuery/selectUserDimension_PA_csOperationsAction.action" onsubmit="return navTabSearch(this);" method="post" rel="pagerForm">
	<div class="pageContent">
		<div class="pageContent">
			<table border="solid 1px #d-1d0d0" cellspacing=1 cellpadding=1 class="list" id="csviewtabuduser" width="100%" table_saveStatus="0">
				<tr>
					<td rowspan="2">序号</td>
					<td colspan="2">保全流程</td>
					<td colspan="2">当前情况</td>
					<td colspan="2">完成情况</td>
				</tr>
				<tr>
					<td>用户代码</td>
					<td>用户姓名</td>
					<td>我的任务</td>
					<td>复核修改</td>
					<td>复核通过</td>
					<td>复核终止</td>
				</tr>
				<tr id="csviewProjectTrud">
					<td colspan="3">总计</td>
				</tr>
				<s:iterator value="page.pageItems"  status="L">
					<tr>
					    <td><s:property value="#L.index+1"/></td>
						
						<s:iterator value="page.pageItems.get(#L.index)">
							<td><s:property/></td>
						</s:iterator>
					</tr>
				</s:iterator>
			</table>
			<div class="panelBar">
					<div class="pages">
						<span>显示</span>
						<s:select  list="#{5:'5',10:'10',20:'20',50:'50'}"
							name="page.pageSize" onchange="navTabPageBreak({numPerPage:this.value}, 'queryCostFeeCfgDiv')">
						</s:select>
						<span>条，共${page.total}条</span>
					</div>
					<div class="pagination" rel="queryCostFeeCfgDiv" totalCount="${page.total}"currentPage="${page.pageNo}"
				     numPerPage="${page.pageSize}" pageNumShown="10"></div>
			</div>
		</div>
	</div>
	</form>
</div>
</div>

<script>
$(function(){
	var $csviewtable=$("#csviewtabuduser",navTab.getCurrentPanel());
	var $csviewtr=$("#csviewProjectTrud",navTab.getCurrentPanel());
	 	var tr=[0,0,0,0];
	 	$csviewtable.find("tr").each(function(i){
	 		if(i>2){
	 			$(this).find("td").each(function(j){
	 				if(j>2){
	 					tr.splice(j-3,1,parseInt($(this).text())+parseInt(tr[j-3]));
	 				}
		 		});
	 		}
	 		
	 	});
	 	for(var i=0; i<tr.length; i++){ 
	 		$csviewtr.append("<td>"+tr[i]+"</td>")
	 	}
	 	
	 	$csviewtable.css({"font-family":"verdana arial sans-serif","color":"#333333","border-color":"#b1cfda"}); 
	 	$csviewtable.find("td").css({"border-style":"solid","border-color":"#b1cfda","text-align":"center"}); 
	 	$csviewtable.find("tr").css("background-color","#f5f6f8");
	 	
});
</script>