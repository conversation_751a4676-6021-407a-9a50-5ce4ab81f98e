<!-- 保险合同打印 -->
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="Field" uri="http://www.git.com.cn/taglib/udmp/field" %>
<%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<link rel="stylesheet" href="${ctx}/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/cs_policyprint.js"></script>
<div class="pageContent" layoutH="0">
	<form id="pagerForm" method="post"
		action="${ctx }/cs/csPolicyPrint/queryTask_PA_csPolicyPrintAction.action?menuId=${menuId}">
		<input type="hidden" name="pageNum" value="${currentPage.pageNo}" />
		<input type="hidden" name="numPerPage" value="${currentPage.pageSize}" />
	</form>
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png">查询条件
		</h1>
	</div>
	<form id="queryForm" onsubmit="return navTabSearch(this);" rel="pagerForm"
		action="${ctx }/cs/csPolicyPrint/queryTask_PA_csPolicyPrintAction.action?menuId=${menuId}"
		method="post" class="printForm required-validate">
		<input type="hidden" name="cusPermissionFlag" value="${queryDataVO.cusPermissionFlag}" />
		<div class="pageFormInfoContent">
			<div style="display: inline-block; height: 24px; padding-top: 8px; position: relative; text-align: center;">
				<dl>
					<dt>状态</dt>
					<dd>
						<Field:codeTable cssClass="combox" id="acceptStatus" name="queryDataVO.acceptStatus" nullOption="true"
							tableName="APP___PAS__DBUSER.T_ACCEPT_STATUS" 
							whereClause="ACCEPT_STATUS IN ('13','18')" 
							value="${queryDataVO.acceptStatus}"></Field:codeTable>
					</dd>
				</dl>
			</div>
			<dl>
				<dt>保全受理号</dt>
				<dd>
					<input id="csAcceptCode" name="queryDataVO.acceptCode" value="${queryDataVO.acceptCode}" />
				</dd>
			</dl>
			<dl>
				<dt>保单号</dt>
				<dd>
					<input id="policyCode" name="queryDataVO.policyCode" value="${queryDataVO.policyCode}" />
				</dd>
			</dl>
			<dl>
				<dt>保单管理机构</dt>
				<dd>
					<select id="organFlag" class="combox" name="queryDataVO.organFlag">
						<option value="">请选择</option>
						<option value="0" <c:if test="${queryDataVO.organFlag == '0'}">selected</c:if>>非本机构</option>
						<option value="1" <c:if test="${queryDataVO.organFlag == '1'}">selected</c:if>>本机构</option>
					</select>
				</dd>
			</dl>
			<dl>
				<dt>业务受理日期</dt>
				<dd>
					<input id="reviewTime" class="date" name="queryDataVO.reviewTime" nullOption="true" 
						value="<s:date format="yyyy-MM-dd" name="queryDataVO.reviewTime" />"/>
				</dd>
			</dl>
			<div class="formBarButton">
				<button type="submit" class="but_blue" onClick="return csBeforeQuery();">查询</button>
			</div>
		</div>
		<div id="printPreview" style="display: block; width: 0px; height: 0px"></div>
	</form>
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png"">查询结果
		</h1>
	</div>
	<div class="tabdivclass">
		<table class="list" width="100%">
			<thead>
				<tr>
					<th><input type="checkbox" class="checkboxCtrl" group="query" style="border:0; backrgound:none; background: 0px;"/>选择</th>
					<th>保单管理机构</th>
					<th>受理号</th>
					<th>保全项目</th>
					<th>保单号</th>
					<th hidden>打印状态</th>
					<th>状态</th>
					<th>禁止外包打印</th>
					<th>打印次数</th>
					<th>打印方式</th>
					<th>打印时间</th>
					<th>操作员</th>
				</tr>
			</thead>
			<tbody align="center" id="queryPolicyPrintData">
				<s:iterator value="%{currentPage.pageItems}" status="st">
					<tr align="center" class="ch" >
						<s:if test="organFlag eq 0">
							<td width="60" id="queryCheckId"><input type="checkbox" name="query" value="<s:property value="listId" />" id="showButton" style="border:0; backrgound:none; background: 0px;" /></td>
							<!-- 保单管理机构 -->
							<td><font color="red"><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG_REL" value="${organCode }" />(${organCode})</font></td>
							<!-- 受理号 -->
							<td id="acceptCode"><font color="red"><a style="color: red" onclick="searchOprationHistory(${bpoResendFlag})">${acceptCode }</a></font></td>
							<!-- 保全项目 -->
							<td><font color="red"><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE" value="${serviceCode }" /></font></td>
							<!-- 保单号 -->
							<td id="policyCode"><font color="red">${policyCode }</font></td>
							<!-- 打印状态 -->
							<td title="taskStatus" id="taskStatus1" hidden>
								<s:if test="taskStatus == 0">待打印</s:if>
								<s:if test="taskStatus == 1">已推送</s:if>
								<s:if test="taskStatus == 2">已打印</s:if>
							</td>
							<!-- 状态 -->
							<td><font color="red"><Field:codeValue tableName="APP___PAS__DBUSER.T_ACCEPT_STATUS" value="${acceptStatus}" /></font></td>
							<!-- 禁止外包打印 -->
							<td><font color="red"><Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO" value="${printFlag}" /></font></td>
							<!-- 打印次数 -->
							<td title="printTimes"><font color="red">${printTimes }</font></td>
							<!-- 打印方式 -->
							<td title="printType"><font color="red">
								<s:if test="printType == 1">柜面打印</s:if>
								<s:if test="printType == 2">外包商打印</s:if>
								<s:if test="printType == 3">外包商打印/柜面打印</s:if></font>
							</td>
							<!-- 打印时间 -->
	 						<td><font color="red"><s:date name="printTime" format="yyyy-MM-dd HH:mm:ss" /></font></td>
	 						<!-- 操作员 -->
							<td><font color="red">${operator}</font></td>
						</s:if>
						<s:else>
							<td width="60" id="queryCheckId"><input type="checkbox" name="query" value="<s:property value="listId" />" id="showButton" style="border:0; backrgound:none; background: 0px;" /></td>
							<!-- 保单管理机构 -->
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_ORG_REL" value="${organCode }" />(${organCode})</td>
							<!-- 受理号 -->
							<td id="acceptCode"><a onclick="searchOprationHistory(${bpoResendFlag})">${acceptCode }</a></td>
							<!-- 保全项目 -->
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_SERVICE" value="${serviceCode }" /></td>
							<!-- 保单号 -->
							<td id="policyCode">${policyCode }</td>
							<!-- 打印状态 -->
							<td title="taskStatus" id="taskStatus1" hidden>
								<s:if test="taskStatus == 0">待打印</s:if>
								<s:if test="taskStatus == 1">已推送</s:if>
								<s:if test="taskStatus == 2">已打印</s:if>
							</td>
							<!-- 状态 -->
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_ACCEPT_STATUS" value="${acceptStatus}" /></td>
							<!-- 禁止外包打印 -->
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO" value="${printFlag}" /></td>
							<!-- 打印次数 -->
							<td title="printTimes">${printTimes }</td>
							<!-- 打印方式 -->
							<td title="printType">
								<s:if test="printType == 1">柜面打印</s:if>
								<s:if test="printType == 2">外包商打印</s:if>
								<s:if test="printType == 3">外包商打印/柜面打印</s:if>
							</td>
							<!-- 打印时间 -->
	 						<td><s:date name="printTime" format="yyyy-MM-dd HH:mm:ss" /></td>
	 						<!-- 操作员 -->
							<td>${operator}</td>
						</s:else>
					</tr>
				</s:iterator>
			</tbody>
		</table>
		<div class="panelBar">
			<div class="pages">
				<span>显示</span>
				<s:select class="combox"
					list="#{5:'5',10:'10',20:'20',50:'50',100:'100',200:'200'}"
					name="select" onchange="navTabPageBreak({numPerPage:this.value})"
					value="currentPage.pageSize">
				</s:select>
				<span>条，共${currentPage.total}条</span>
			</div>
			<div class="pagination" targetType="navTab"
				totalCount="${currentPage.total}"
				numPerPage="${currentPage.pageSize}" pageNumShown="10"
				currentPage="${currentPage.pageNo}"></div>
		</div>
	</div>
	<div class="formBarButton">
		<ul>
			<li>
				<div class="pageFormdiv">
					<button class="but_blue" type="button" id="12" onclick="csChangePrint('0')">推送外包打印</button>
					<button class="but_blue" type="button" id="23" onclick="csChangePrint('1')">禁止外包打印</button>
					<button class="but_blue" type="button" id="dPrint" onclick="csPolicyPrint()">公司打印</button>
				</div>
			</li>
			<li>
				<div class="pageFormdiv">
					<span>重打原因：</span><input name="queryDataVO.csreprintremake" id="csreprintremake"/>
				</div>
			</li>
		</ul>
	</div>
</div>
