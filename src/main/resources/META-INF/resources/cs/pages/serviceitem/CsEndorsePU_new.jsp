<!-- 减额缴清、险种转换   变更后信息 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<div class="panelPageFormContent" <s:if test="queryFlag==1">disabled="disabled"</s:if>>
	<div class="divfclass">
		<h1>
			<img src="${ctx}/cs/img/icon/tubiao.png">变更后信息
		</h1>
	</div>
	<div class="tabdivclass">
		<table class="list" width="100%" id="ReducedPaidUpAFTable">
			<thead>
				<tr <s:if test="queryFlag==1">disabled="disabled"</s:if>>
					<th>保单号</th>
					<th>险种代码</th>
					<th>险种名称</th>
					<th>保额/份数</th>
					<th>每份保额</th>
					<th>保费</th>
					<th>保障年期类型</th>
					<th>保障年期</th>
					<th>险种状态</th>
					<s:if test="reducePaidUpChangeVO.receiveAge != null">
						<th>约定年金领取年龄</th>
						<th>年金领取方式</th>
					</s:if>

				</tr>
			</thead>
			<tbody id="ReducedPaidUpAFtbody">
				<s:iterator value="reducePaidUpChangeAFVOs" status="AFst" id="AFList">

					<tr align="center">
						<td>${policyCode}</td>
						<td>${productCodeSys}</td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${busiPrdId}" /></td>
						<td><s:property value="amout" /></td>
						<!-- 每份保额 -->
						<td><s:property value="perAmout" /></td>
						<!-- 保费 -->
						<td><s:property value="stdPremAf" /></td>
						<!-- 基本保额 -->
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_COVERAGE_PERIOD" value="${coveragePeriod}" /></td>
						<td>${coverageYear}</td>
						<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}" /></td>
						<s:if test="reducePaidUpChangeVO.receiveAge != null">
							<td>${receiveAge}</td>
							<td><s:if test="receiveMode == 1">年领</s:if> <s:elseif test="receiveMode == 4">月领</s:elseif> <s:if test="receiveMode == 5">趸领</s:if>
							</td>
						</s:if>
					</tr>

				</s:iterator>
			</tbody>
		</table>
	</div>
</div>



