<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript">
	function check() {
		 var cName = $("#countryName").val();
		 var cNo = $("#countryNo").val();
		 if(null==cName||"".equals("cName")||null==cNo||"".equals("cNo")){
			 alertMsg("请输入查询条件");
		 }
	}
</script>
<form id="pagerForm" action="${ctx}/demo/database/dwzOrgLookup2.html">
	<input type="hidden" name="pageNum" value="1" /> <input type="hidden"
		name="numPerPage" value="${model.numPerPage}" /> <input type="hidden"
		name="orderField" value="${param.orderField}" /> <input type="hidden"
		name="orderDirection" value="${param.orderDirection}" />
</form>

	<form rel="pagerForm" method="post"
		action="${ctx }/cs/serviceitem_md/showPartDestinationMsg_PA_csEndorseMDAction.action"
		onsubmit="return dwzSearch(this, 'dialog');">
		<div class="searchBar">
			<ul class="searchContent" id="select">
				<li><label>国家名称:</label> <input id="countryName" class="textInput"
					name="orgName" value="" type="text"></li>
				<li><label>编号:</label> <input id="countryCode" class="textInput" name="orgNum"
					value="" type="text"></li>
			</ul>
			
		</div>
	</form>
	<div class="subBar">
			<table style="width:100%">
					<tr>
						<td></td>
						<td style="width:200px">
								<button type="submit" class="but_blue" onclick="check();">查询</button>
								<button type="button" class="but_blue" multLookup="orgId" warn="请选择国家">选择带回</button>
						</td>
						<td></td>
					</tr>
				</table>
			</div>
<div class="pageContent">

	<table class="table" layoutH="118" targetType="dialog" width="100%">
		<thead>
			<tr>
				<th width="30">选择</th>
				<th orderfield="orgName">国家名称</th>
				<th orderfield="orgNum">国家编号</th>
			</tr>
		</thead>
		<tbody id="">
			<s:iterator value="listcountry" id="" status="st">
				<tr>
					<td><input type="checkbox" name="orgId"
						value="{id:'1', orgName:'${country }', orgNum:'${countryCode }'}" /></td>
					<td>${country }</td>
					<td>${countryCode }</td>
				</tr>
			</s:iterator>
		</tbody>
	</table>

	<div class="panelBar">
		<div class="pages">
			<span>每页</span> <select name="numPerPage"
				onchange="dwzPageBreak({targetType:dialog, numPerPage:'10'})">
				<option value="10" selected="selected">5</option>
				<option value="20">20</option>
				<option value="50">50</option>
				<option value="100">100</option>
			</select> <span>条，共2条</span>
		</div>
		<div class="pagination" targetType="dialog" totalCount="3"
			numPerPage="10" pageNumShown="1" currentPage="1"></div>
	</div>
</div>