<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css"> 


<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<!-- *********帮助菜单***********end********* -->
<!-- 步骤标识 -->
<s:include value="csEndorseProgress.jsp" />

<div class="pageFormInfoContent" layoutH="175px">
		<input type="hidden" id="customerId" name="customerId" value="${customerId}"/> 
		<input type="hidden" id="acceptId" name="acceptId" value="${acceptId}"/> 
		<input type="hidden" id="changeId" name="changeId" value="${changeId}"/> 

		<div class="divfclass">
			<h1>
				<img src="${ctx}/cs/img/icon/tubiao.png">延长宽限期
			</h1>
		</div>
		<div class="pageContent" layoutH="180">
			<!-- 客户信息查询 -->
			<s:include value="customerInfo_list.jsp" />
			
			<!-- 变更信息录入 -->
			<div class="divfclass">
				<h1>
					<img src="cs/img/icon/tubiao.png">变更前信息
				</h1>
			</div>
			<div class="pageFormdiv">						
				<div class="tabdivclass" id="">
					<input id="jsons" name="jsons" type="hidden" value="" />
					<table id="epTableList" class="list" width="100%">
						<thead>
							<tr>
								<th colName="policyCode" >保单号</th>
								<th colName="busiProdCode" >险种代码</th>
								<th >被保险人</th>
								<th colName="">险种名称</th>
								<th colName="amount" >保额</th>
								<th colName="stdPremAf" >保费</th>
								<th colName="" >保单状态</th>
								<th colName="" >保费状态</th>
								<th colName="validateDate" >生效日期</th>
								<th colName="payDueDate" >下次交费日期</th>
								<th colName="payEndDate" >宽限期止期</th>
								<th colName="deadlineNum" inputType="select" >宽限期延长日期</th>
								
								<th colName="busiPrdId" style="display: none">险种id</th>
								<th colName="liabilityState" style="display: none">保单状态</th>
								<th colName="premStatus" style="display: none">保费状态</th>
								<th colName="changeId" style="display: none">changeId</th>
								<th colName="policyId" style="display: none">policyId</th>
								<th colName="policyChgId" style="display: none">policyChgId</th>
								<th colName="acceptId" style="display: none">acceptId</th>
							</tr>
						</thead>
						<tbody align="center" id="">
							<s:iterator value="bfCsEndorseEPVOs" status="s">
								
								<tr align="center"  <s:if test="#s.index==0">tr_saveStatus="1"</s:if> class="instalment">
									<td>${policyCode}</td>
									<td>${busiProdCode}</td>
									<td>${insuredNames}</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
										value="${busiPrdId}" /></td>
									<td>${amount}</td>
									<td>${stdPremAf}</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
										value="${liabilityState}" /></td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_PREM_STATUS"
										value="${premStatus}" /></td>
									<td>${validateDate}</td>
									<td>${payDueDate }</td>
									<td>${payEndDate }</td>
									<td>
										<s:if test="#s.index==0">
										<select id="deadlineNum">
											<option value="30">30</option>
											<option value="60">60</option>
											<option value="120">120</option>
											<option value="180">180</option>
	                                     </select>
	                                     </s:if>
									</td>
									<td style="display: none">${busiPrdId}</td>
									<td style="display: none">${liabilityState}</td>
									<td style="display: none">${premStatus}</td>
									<td style="display: none">${changeId}</td>
									<td style="display: none">${policyId}</td>
									<td style="display: none">${policyChgId}</td>
									<td style="display: none">${acceptId}</td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
				</div>
				<div class="pageFormdiv">
					<button class="but_blue" type="button" onclick="saveEPChoice()">保存</button>
			    </div>	
			</div>
	        
	        <!-- 变更后信息 -->
	        <div id = "">
				<div class="divfclass">
					<h1>
						<img src="cs/img/icon/tubiao.png">变更后信息
					</h1>
				</div>
				<div class="tabdivclass">
					<table class="list" width="100%">
						<thead>
							<tr>
								<th>保单号</th>
								<th>险种代码</th>
								<th>被保险人</th>
								<th>险种名称</th>
								<th>保额</th>
								<th>保费</th>
								<th>保单状态</th>
								<th>保费状态</th>
								<th>生效日期</th>
								<th>下次交费日期</th>
								<th>宽限期止期</th>
							</tr>
						</thead>
						<tbody align="center" id="changeDeadlineNum">
								<s:iterator value="afCsEndorseEPVOs">
									<tr align="center" tr_saveStatus="1" class="instalment">
										<td>${policyCode}</td>
										<td>${busiProdCode}</td>
										<td>${insuredNames}</td>
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
											value="${busiPrdId}" /></td>
										<td>${amount }</td>
										<td>${stdPremAf }</td>
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
										value="${liabilityState}" /></td>
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_PREM_STATUS"
										value="${premStatus}" /></td>
										<td>${validateDate}</td>
										<td>${payDueDate }</td>
										<td>${payEndDate }</td>
									</tr>
								</s:iterator>
						</tbody>
					</table>
				</div>
			</div>
			
	</div>
</div>
<s:include value="/cs/pages/serviceitem/serviceItemBottomBar.jsp"></s:include>
<script>
	
	function saveEPChoice(){
		alertMsg.confirm("确认保存以上信息？",{
			okCall : function() {
				debugger;
				var _jsons = "";
				var $jsonsText = $("input[name='jsons']",navTab.getCurrentPanel());
				var $table = $("#epTableList", navTab.getCurrentPanel());
				/* alert($table.html()); */
				_jsons += _cs_tableToJson($table);
				/* alert(_jsons); */
				$jsonsText.val(_jsons);
				/* alert($("#jsons", navTab.getCurrentPanel()).val()); */
				
				var changeId=$("#changeId",navTab.getCurrentPanel()).val();
				var acceptId=$("#acceptId",navTab.getCurrentPanel()).val();
				$.ajax({
					url:"${ctx}/cs/serviceitem_ep/saveEPChoice_PA_csEndorseEPAction.action?changeId="+changeId+"&acceptId="+acceptId,
					type:"post",
					dataType:'html',
					data:"jsonString="+$("#jsons", navTab.getCurrentPanel()).val(),
					cache : false,
					success : function(response) {
						alertMsg.correct("信息保存成功");
						var json = DWZ.jsonEval(response);
						if (undefined == json.statusCode) {
							$("#changeDeadlineNum", navTab.getCurrentPanel()).html("");
							$("#changeDeadlineNum", navTab.getCurrentPanel()).html(response);
						} else {
							alertMsg.error(json.message);
									
						}
					}
				});
				
			},
			cancelCall : function() {
			}
		});

	}

</script>

