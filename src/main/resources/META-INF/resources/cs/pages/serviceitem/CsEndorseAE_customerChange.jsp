<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"
	type="text/css">

<div class="pageContent" layoutH="136px">
	<form action="cs/serviceitem_ae/saveCustomer_PA_csEndorseAEAction.action"
		id="customerForm"
		onsubmit="return divSearch(this, 'customer_infoDiv');">
		<input type="hidden" name="aeHolderChangeVO.policyChgId"/> 
		<input type="hidden" name="aeHolderChangeVO.policyId"/>
		<input type="hidden" name="csAddressVO.addressId" />
			<div class="divfclass">
						<h1>
							<img src="images/tubiao.png" >投保人信息
						</h1>
			</div>
			<div class="pageFormContent">
				<dl style="display:none;">
					<dt>客户编码</dt>
					<dd>
						<input id="cus" type="text" name="customerVO.customerId"
							value="${csCustomerVO.customerId}"/>
					</dd>
				</dl>
				<dl>
					<dt></dt>
					<dd></dd>
				</dl>
				<dl>
					<dt></dt>
					<dd></dd>
				</dl>
				<dl>
					<dt>姓名*</dt>
					<dd>
						<input type="text" name="customerVO.customerName"
							value="${csCustomerVO.customerName}" />
					</dd>
				</dl>
				<dl>
					<dt>证件类型*</dt>
					<dd>
						<Field:codeTable nullOption="TRUE" cssClass="combox"
							name="customerVO.customerCertType" tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
							value="${csCustomerVO.customerCertType}" />
					</dd>
				</dl>
				<dl>
					<dt>证件号码*</dt>
					<dd>
						<input type="text" name="customerVO.customerCertiCode"
							value="${csCustomerVO.customerCertiCode}" />
					</dd>
				</dl>
				<dl>
					<dt>证件有效期起</dt>
					<dd>
						<input type="expandDateYMD" name="customerVO.custCertiStarDate"
							value="<s:date name="csCustomerVO.custCertiStarDate" format="yyyy-MM-dd" />" />
					</dd>
				</dl>
				<dl>
					<dt>证件有效期止</dt>
					<dd>
						<input type="expandDateYMD" name="customerVO.custCertiEndDate"
							value="<s:date name="csCustomerVO.custCertiEndDate" format="yyyy-MM-dd" />" />
					</dd>
				</dl>
				<dl>
					<dt>国籍*</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true"
							name="customerVO.countryCode" tableName="APP___PAS__DBUSER.T_COUNTRY"
							value="${csCustomerVO.countryCode}" />
					</dd>
				</dl>
				<dl>
					<dt>性别*</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true"
							name="customerVO.customerGender" tableName="APP___PAS__DBUSER.T_GENDER"
							value="${csCustomerVO.customerGender}" />
					</dd>
				</dl>
				<dl>
					<dt>出生日期*</dt>
					<dd>
						<input type="expandDateYMD" name="customerVO.customerBirthday"
							value="<s:date name="csCustomerVO.customerBirthday" format="yyyy-MM-dd" />" />
					</dd>
				</dl>
				<dl class="shouyi" style="display: none";>
					<dt>婚姻状况*</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true"
							name="customerVO.marriageStatus" tableName="APP___PAS__DBUSER.T_MARRIAGE"
							value="${csCustomerVO.marriageStatus}" />
					</dd>
				</dl class="shouyi" style="display: none";>
				<dl class="shouyi" style="display: none";>
					<dt>子女状况</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true"
							name="customerVO.isParent" tableName="APP___PAS__DBUSER.T_MARITAL_STATUS"
							value="${csCustomerVO.isParent}" />
					</dd>
				</dl>
				<dl>
					<dt>职业编码*</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true"
							name="customerVO.jobCode" tableName="APP___PAS__DBUSER.T_JOB_CATEGORY"
							value="${csCustomerVO.jobCode}" />
					</dd>
				</dl>
				<dl>
					<dt>职业类别</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true"
							name="customerVO.jobNature" tableName="APP___PAS__DBUSER.T_JOB_NATURE"
							value="${csCustomerVO.jobNature}" />
					</dd>
				</dl>
				<dl>
					<dt>职务</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true"
							name="customerVO.jobTitle" tableName="APP___PAS__DBUSER.T_JOB_UNDERWRITE"
							value="${csCustomerVO.jobTitle}" />
					</dd>
				</dl>
				<dl>
					<dt>驾照类型</dt>
					<dd>
						<Field:codeTable cssClass="combox" nullOption="true"
							name="customerVO.driverLicenseType" tableName="APP___PAS__DBUSER.T_LICENSE_TYPE"
							value="${csCustomerVO.driverLicenseType}" />
					</dd>
				</dl>
				<dl>
					<dt>工作单位*</dt>
					<dd>
						<input type="text" name="customerVO.companyName"
							value="${csCustomerVO.companyName}" />
					</dd>
				</dl>
				<dl>
					<dt>通讯地址</dt>
					<dd>
						<select name="address" onchange="selectAddress(this)"
							id="addressId">
							<option value="">请选择
							<s:iterator value="csAddressVOs" var="st">
								<option value="${st.addressId}">
									${st.addressId}${st.address}								
								</option>																
							</s:iterator>
						</select>
					</dd>
				</dl>
					<dl>
						<dt>省/直辖市*</dt>
						<dd>				
										
							<input type="text" name="csAddressVO.state"
								value="${csAddressVO.state}" />
						</dd>
					</dl>
					<dl>
						<dt>市</dt>
						<dd>
							<input type="text" name="csAddressVO.city"
								value="${csAddressVO.city}" />
						</dd>
					</dl>
					<dl>
						<dt>区县</dt>
						<dd>
							<input type="text" name="csAddressVO.district"
								value="${csAddressVO.district}" />
						</dd>
					</dl>
					<dl>
						<dt>街道/镇</dt>
						<dd>
							<input type="text" name="csAddressVO.townStreetCode"
								value="${csAddressVO.townStreetCode}" />
						</dd>
					</dl>
					<dl>
						<dt>地址*</dt>
						<dd>
							<input type="text" name="csAddressVO.address"
								value="${csAddressVO.address}" />
						</dd>
					</dl>
					<dl>
						<dt>邮政编码*</dt>
						<dd>
							<input type="text" name="csAddressVO.postCode"
								value="${csAddressVO.postCode}" />
						</dd>
					</dl>
					<dl>
						<dt>移动电话</dt>
						<dd>
							<input type="expandMobile" name="csAddressVO.mobileTel"
								value="${csAddressVO.mobileTel}" />
						</dd>
					</dl>
					<dl>
						<dt>固定电话</dt>
						<dd>
							<input type="expandPhone" name="csAddressVO.fixedTel"
								value="${csAddressVO.fixedTel}" />
						</dd>
					</dl>
					<dl>
						<dt>电子邮箱</dt>
						<dd>
							<input type="text" name="csAddressVO.email"
								value="${csAddressVO.email}" />
						</dd>
					</dl>
			<table style="width:100%">
					<tr>
						<td></td>
							<td style="width:200px">
								<button type="button" class="but_blue" onclick="addAddress()">保存</button>
								<button type="button" class="but_blue" onclick="addVisitCfg()">告知信息录入</button>
							</td>
						<td></td>
					</tr>
			</table>
			</div>
	</form>
</div>
<script type="text/javascript">
	function selectAddress(obj) {
		var addressId = obj.value;
		var customerId= $("#cus").val();
		$.ajax({
			type: "post",
			dataType : "text", 														    
			url:  "cs/serviceitem_ae/queryAddress_PA_csEndorseAEAction.action",
			data:  'aeHolderChangeVO.customerId='+customerId+'&aeHolderChangeVO.addressId='+addressId,      
			success: function(data){				
			var adr=jQuery.parseJSON(data);
			$("input[name='csAddressVO.state']").val(adr.state);
			$("input[name='csAddressVO.city']").val(adr.city);
			$("input[name='csAddressVO.district']").val(adr.district);
			$("input[name='csAddressVO.address']").val(adr.address);
			$("input[name='csAddressVO.postCode']").val(adr.postCode);
			$("input[name='csAddressVO.email']").val(adr.email);
			$("input[name='csAddressVO.mobileTel']").val(adr.mobileTel);
			$("input[name='csAddressVO.mobileTel']").val(adr.mobileTel);
			}
		});
	}
	
	function addAddress(){
		var addressId = $("#addressId").val();
		var policyChgId=$("#policyChgId").val();
		var policyId=$("#policyId").val();
		var $obj = $("#customerForm", navTab.getCurrentPanel());
		if(addressId != ""){
			$obj.find("input[name='csAddressVO.addressId']").val(addressId);
		}
		$obj.find("input[name='aeHolderChangeVO.policyChgId']").val(policyChgId);
		$obj.find("input[name='aeHolderChangeVO.policyId']").val(policyId);
		$("#customerForm").submit();
	}
</script>