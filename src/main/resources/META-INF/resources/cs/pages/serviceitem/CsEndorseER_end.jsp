<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<!-- 引入点击下一步返回保全录入的js -->
<%-- <s:set var="ctx">${pageContext.request.contextPath}</s:set> --%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript"
	src="cs/pages/common/js/returnAcceptAndPay.js"></script>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<input type="hidden" name="menuId" id="menuId" value="${menuId}">
<input type="hidden" name="itemFlag" id="itemFlag" value="mainProd">
<input type="hidden" name="itemName" id="itemName" value="附加险满期降低保额续保">
<!-- ********帮助菜单***********begin********* -->
<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<s:include value="csEndorseProgress.jsp" />
<!-- *********帮助菜单***********end********* -->
<%-- 进度条 --%>
<!-- <div class="step_header">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
		<tr>
			<td rowspan="2" class="n1d" id="n1"></td>
			<td class="step" id="step1">受理信息修改</td>
			<td rowspan="2" class="n2d" id="n2"></td>
			<td class="step" id="step2">项目信息录入</td>
			<td rowspan="2" class="n3d" id="n3"></td>
			<td class="step" id="step3">录入完成</td>
		</tr>
	</table>
</div> -->
<div layoutH="140">
<div class="pageContent">
	<s:include value="customerInfo_list.jsp" />
		<!-- validateCallback  -->
			<form id="ErForm" action="${ctx}/cs/serviceitem_er/saveDownAmountOrUnit_PA_csEndorseERAction.action"
				  class="pageForm required-validate" method="post" onsubmit="return validateCallback(this,updateEndAjaxDone)">
				<input type="hidden" id="customerId" name="customerId" value="${customerId}" /> 
				<input type="hidden" id="acceptId" name="acceptId" value="${acceptId}" /> 
				<input type="hidden" id="changeId" name="changeId" value="${changeId}" /> 
				<input type="hidden" id="downAmountAndUnit" name="downAmountAndUnit" value="" />
				<input type="hidden" name="workDate" value="<s:date name='workDate' format='yyyy-MM-dd'></s:date>" />
				<div class="panel" style="display: none">
					<div class="divfclass" >
								<h1>
							    <img src="images/tubiao.png" >客户信息
								</h1>
					</div>
					<s:include value="customerInfo_list.jsp" />
				</div>
					<div class="divfclass">
							<h1>
							    <img src="images/tubiao.png" >变更前数据
							</h1>
					</div>
					<div class="pageFormContent" >
						<table id="dataTable" class="list" width="100%">
							<thead>
								<tr>
									<th colName="policyChgId" style="display: none"></th>
									<th colName="countWay" style="display: none"></th>
									<th colName="downType" style="display: none">减保类型</th>
									<th colName="policyId" style="display: none">保单ID</th>
									<th colName="addtionBusiItemId" style="display: none">附加险种ID</th>
									<th colName="policyCode">保单号</th>
									<th>被保险人</th>
									<th colName="addtionBusiProdCode">险种代码</th>
									<th>险种名称</th>
									<th colName="oldData">当前保额/份数</th>
									<th colName="onesAmount">每份保额</th>
									<th>续保保额/份数</th>
									<th colName="downData" inputType="input">减少的保额/份数</th>
									<th colName="totalPremAf">保费</th>
									<th>下期交费日</th>
								</tr>
							</thead>
							<tbody>
								<s:iterator value="csEndorseErVOs" status="st" id="BFList">
									<tr align="center" tr_saveStatus="1">
										<!-- 0：减份数   1：减保额 -->
										<td style="display: none">${policyChgId}</td>
										<td style="display: none">${countWay}</td>
										<td name="downType" style="display: none"><s:if
												test="countWay == 5">0</s:if> <s:if test="countWay == 1">1</s:if>
										</td>
										<td style="display: none">${policyId}</td>
										<td style="display: none">${addtionBusiItemId}</td>
										<td>${policyCode }</td>
										<td>${insuredName }</td>
										<td>${addtionBusiProdCode }</td>
										<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
												value="${addtionBusiPrdId}" /></td>
										<td name="oldData"><s:if test="countWay == 1">
												${basicAmount}
											</s:if> <s:if test="countWay ==5">
												${unit}
											</s:if></td>
										<td><s:if test="onesAmount != 0">
												${onesAmount}
											</s:if></td>
										<td id="renewal"><s:if test="renewalAmount != 0">
												${renewalAmount}
											</s:if> <s:if test="renewalUnit != 0">
												${renewalUnit}
											</s:if></td>
										<td>
										${afterAmount}
										</td>
										<td>${totalPremAf}</td>
										<td id="payDueDate"><s:date name="payDueDate" format="yyyy-MM-dd"></s:date></td>
									</tr>
								</s:iterator>
							</tbody>
						</table>
				</div>
					
			</form>
		
			<div id="updateEndDiv"></div>
			<div class="divfclass">
			<h1>
				   <img src="images/tubiao.png" >变更保单信息
			</h1>
	</div>
		<div>
		<div class="pageFormContent" >
			<table class="list" width="100%" id="decreaseAFDetail">
				<thead>
					<tr>
						<th colName="policyChgId" style="display: none"></th>
						<th colName="countWay" style="display: none"></th>
						<th colName="downType" style="display: none">减保类型</th>
						<th colName="policyId" style="display: none">保单ID</th>
						<th colName="addtionBusiItemId" style="display: none">附加险种ID</th>
						<th colName="policyCode">保单号</th>
						<th>被保险人</th>
						<th colName="addtionBusiProdCode">险种代码</th>
						<th>险种名称</th>
						<th colName="oldData">保额/份数</th>
						<th>每份保额</th>
						<th colName="totalPremAf">保费</th>
						<th>下期交费日</th>
					</tr>
				</thead>
				<tbody id="allInfoContent">
					<s:iterator value="updateDowncsEndorseErVOs" status="st" id="AFList">
						<tr align="center" tr_saveStatus="1">
							<!-- 0：减份数   1：减保额 -->
							<td style="display: none">${policyChgId}</td>
							<td style="display: none">${countWay}</td>
							<td name="downType" style="display: none">
								<s:if test="countWay == 5">0</s:if>
								<s:if test="countWay == 1">1</s:if>
							</td>
							<td style="display: none">${policyId}</td>
							<td style="display: none">${addtionBusiItemId}</td>
							<td>${policyCode }</td>
							<td>${insuredName }</td>
							<td>${addtionBusiProdCode}</td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${addtionBusiPrdId}"/></td>
							<td name="oldData">
								<s:if test="countWay == 1">
									${basicAmount}
								</s:if>
								<s:if test="countWay == 5">
									${unit}
								</s:if>
							</td>
							<td>
								<s:if test="onesAmount != 0">
									${onesAmount}
								</s:if>
							</td>
							<td>${totalPremAf}</td>
							<td><s:date name="payDueDate" format="yyyy-MM-dd"></s:date></td>
						</tr>
					</s:iterator>
				</tbody>
			</table>
	</div>
</div>

	</div>
</div>
