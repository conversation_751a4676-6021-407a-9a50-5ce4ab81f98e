
<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp"%>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css"type="text/css">
<script type="text/javascript" src="${ctx}/cs/js/DomZoomZX.js"></script>
<script>
	$(document).ready(function(){
        var D = DomZoomZX('input,select,textarea,td')
	})
</script>
<!-- ********帮助菜单***********begin********* -->

<s:include value="/cs/pages/common/jsp/helpMenu.jsp"></s:include>
<s:if test="queryFlag!=1&&acceptStatus!='07'">
<s:include value="csEndorseProgress.jsp"></s:include>
</s:if><s:else>
<s:include value="entryProgressBar.jsp"></s:include>
</s:else>

<!-- *********帮助菜单***********end********* -->
<div class="backgroundCollor" style="height: 620px; overflow: auto;"  layoutH="140" >
	<div>
		<div class="divfclass">
		<h1 >
		 <!--  <img src="cs/img/icon/tubiao.png">保全回退信息录入 -->
		</h1>
		</div>
		
		<div>
		<div>
			
		<div class="divfclass" style="padding: 20px 20px 4px;">
		<h1>
		  <img src="cs/img/icon/tubiao.png">保单信息
		</h1>
		</div>
				
				<div class="pageFormInfoContent">
					<dl>
						<dt>保单号</dt>
						<dd>
							<input type="text" id="policyCodeInfo" value="${csEndorseRBMainVO.policyCode}"
								readonly="true" maxlength="20" />
						</dd>
					</dl>
					<dl>
						<dt>保单生效日</dt>
						<dd>
							<input type="text"
								value="<s:date format="yyyy-MM-dd" name="csEndorseRBMainVO.validateDate"/>"
								readonly="true" maxlength="20" />
						</dd>
					</dl>
					<dl>
						<dt>保单状态</dt>
						<dd>
							<input type="text"
								value="<Field:codeValue tableName="APP___PAS__DBUSER.t_liability_status"
									value="${csEndorseRBMainVO.liabilityState}" />"
								readonly="true" maxlength="20" />
						</dd>
					</dl>
					<dl>
						<dt>投保人</dt>
						<dd>
							<input type="text" value="${csEndorseRBMainVO.policyHolderName}"
								readonly="true" maxlength="20" />
						</dd>
					</dl>
					<dl style="width: 350px">
						<dt>被保人</dt>
						<dd>
							<input type="text" value="${csEndorseRBMainVO.insuredName}"
								readonly="true" maxlength="20" />
						</dd>
					</dl>
				</div>
			</div>
				<form action="" onsubmit="" id="changeAge" method="post">
			<div>
			<!-- 保全实名核验信息  start-->
			<div>
				<s:if test="verifyFlag==1">
					<s:include value="/cs/pages/serviceitem/verifyInfo_list.jsp" />
				</s:if>
			</div>
			<div class="divfclass" style="padding: 20px 20px 4px;">
				<h1>
		  		<img src="cs/img/icon/tubiao.png">保全交易轨迹
				</h1>
			</div>
			
				<div class="tabdivclass">
					<table class="list" width="100%">
						<thead>
							<tr>
								<th>序号</th>
								<th>保全受理号</th>
								<th>保全项目</th>
								<th>生效日期</th>
								<th>补退费金额</th>
								<th>操作用户</th>
								<th>是否可跳过</th>
							</tr>
						</thead>
						<tbody id="csChangeMarks">
							<s:iterator value="csEndorseRBMainVO.csChangeMarkVOs" status="st" var="csChangeMarkVO">
								<tr align="center">
							 		<td>${st.index+1 }</td>
									<td>${csChangeMarkVO.acceptCode }</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BACK_CROSS_SERVICE" value="${csChangeMarkVO.serviceCode }" /></td>
									<td><s:date name="validateTime" format="yyyy-MM-dd"></s:date></td>
									<td>${csChangeMarkVO.feeAmount}</td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value="${insertBy}"/></td>
									<td><Field:codeValue tableName="APP___PAS__DBUSER.T_YES_NO" value="${csChangeMarkVO.rollbackJumpFlag}" /></td>
								</tr>
							</s:iterator>
						</tbody>
					</table>
					
				</div>
				
			</div>
			
				
			</form>
			<div id="rollbackView" style="display:none">
				<a href="" class="but_blue" id ="rollbackReiewA" target="navTab" title="回退复核"></a>
			</div>
		</div>
		<div id="rbEntryInformation" >
			<s:include value="/cs/pages/serviceitem/CsEndorseRB_entryInformation.jsp"/>
		</div>
			<div>
			</div>
			
	</div>
	
	