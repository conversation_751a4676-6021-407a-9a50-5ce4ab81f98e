<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8" import="java.util.*,java.text.*"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/cs/pages/common/jsp/common_ctx.jsp" %>
<script type="text/javascript" src="${ctx}/cs/js/cs_common.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/check_name.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/certificateValidate.js"></script>
<script type="text/javascript" src="${ctx}/cs/js/ChinesePostcode.js"></script>
<link rel="stylesheet" href="${ctx }/cs/css/cs_common.css" type="text/css">
<link rel="stylesheet" href="${ctx}/cs/css/public.css" type="text/css">

<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/jquery.ui.core.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/jquery.ui.widget.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/assets/prettify.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.js"></script>
<script type="text/javascript" src="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.filter.js"></script>

<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/jquery-ui.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/assets/prettify.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.css" />
<link rel="stylesheet" type="text/css" href="${ctx}/udmp/plugins/jquery_ui/multiselect/jquery.multiselect.filter.css" />

 <div  class="pageContent">  
 <form action="" id="beneForm" onsubmit="return validateCallback(this);" method="post">
	<input id="customerId1" type="hidden" name="customerId"value="${customerId}" /> 
	<input id="acceptId1" type="hidden"name="acceptId" value="${acceptId}" />
	<input id="changeId1" type="hidden" name="changeId" value="${changeId}" />
	<input type="hidden" id="policyChgId" name="policyChgId" value="${policyChgId}"/>
	<input id="busiItemId1" type="hidden" name="busiItemId" value="${busiItemId}" />
	<input id="policyId1" type="hidden" name="policyId" value="${policyId}" />
	<input id="listId1" type="hidden" name="listId" value="${listId}" />
	<input id="type1" type="hidden" name="type" value="${type}" />	
	<input id="logId1" type="hidden" name="logId" value="${logId}" />
	<input id="insuredId" type="hidden" name="insuredId" value="${insuredId}" />
	<input id="customerLogId" type="hidden" name="customerLogId" value="${customerLogId}" />
	
</form>

<form method="post" action="" id="updateBeneForm"  onsubmit="return validateCallback(this)">
<input id="logId" type="hidden" name="beneCustomerVO.logId" value="${beneCustomerVO.logId}" />
<input id="isUpdate" type="hidden" />
<input id="changQiFlag" name="beneCustomerVO.changQiFlag" type="hidden" value="">
<input type="hidden" id="bankAccountOld" name="beneCustomerVO.bankAccountOld" value="${beneCustomerVO.bankAccount}" />
<input type="hidden" id="addressId" name="beneCustomerVO.addressId" value="${beneCustomerVO.addressId}" />
<input type="hidden" id="townStreetName" value="${beneCustomerVO.townStreetName}" name="beneCustomerVO.townStreetName">
		<div >
			<div class="divfclass">
			<h1>
				<img src="${ctx}/cs/img/icon/tubiao.png" />受益人信息BC
			</h1>
		</div>
				
				 <div class="pageFormInfoContent" id="formDiv">
					<dl>
					    <dt>所属险种<font color="red">&nbsp;&nbsp;*</font></dt>
					    <dd>
					     <Field:codeTable id="busiPrdId" name="beneCustomerVO.busiPrdId" nullOption="true" whereClause="" cssClass="combox"
							tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${beneCustomerVO.busiPrdId}"></Field:codeTable> 
					    </dd>
					</dl> 
					
					<dl>
						<dt>与被保险人关系<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<Field:codeTable id="designationBene" name="beneCustomerVO.designation" cssClass="combox" 
								 tableName="APP___PAS__DBUSER.T_LA_PH_RELA" nullOption="true" whereClause=""
 								value="${beneCustomerVO.designation}"></Field:codeTable>
						</dd>
					</dl>
					
					<dl>
						<dt>受益性质<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<Field:codeTable id="beneType" name="beneCustomerVO.beneType" cssClass="combox" nullOption="true"  tableName="APP___PAS__DBUSER.T_BENEFICIARY_TYPE" whereClause="beneficiary_type !=2" value="${beneCustomerVO.beneType }" onChange="changeBeneType(this)"/>
						</dd>
					</dl>
					
					 <dl>
						<dt>受益顺序<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<select name="beneCustomerVO.shareOrder" id="beneShareOrder" style="width:140px;">
								<option value="">请选择</option>
								<option value="1"
									<s:if test="beneCustomerVO.shareOrder eq 1"> selected </s:if>>第一受益人</option>
								<option value="2"
									<s:if test="beneCustomerVO.shareOrder eq 2"> selected </s:if>>第二受益人</option>
								<option value="3"
									<s:if test="beneCustomerVO.shareOrder eq 3"> selected </s:if>>第三受益人</option>
							</select> 
						</dd>
					</dl> 
					
					<dl>
						<dt>受益比例(%)<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<input id="shareRate"  type="expandNumber" name="beneCustomerVO.shareRate" value="${beneCustomerVO.shareRate}"/>
						</dd>
					</dl>
					
					<dl>
					   <div id="holderName_DIV" class="focus_bind_holder">
						<dt>姓名<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<input type="text" id = "csCustomerVO_customerName" name="beneCustomerVO.customerName"  value="${beneCustomerVO.customerName}" 
								onchange="check_name(this)" readonly="readonly" /><span id="holdernameDIV"></span>
						    <input type="hidden" id="policyHoldername" value=""/>
						</dd>
						</div>
					</dl>
					
					<dl>
						<dt>性别<font color="red">&nbsp;&nbsp;*</font></dt>
							<dd>
								<Field:codeTable id="beneGenderId" cssClass="combox" disabled="true"
 									name="beneCsGenderId" nullOption="true"
								 tableName="APP___PAS__DBUSER.T_GENDER" value="${beneCustomerVO.customerGender }"></Field:codeTable>
								<input type="hidden"  id="beneCsGenderId" name="beneCustomerVO.customerGender"/>
							</dd>
					</dl>
					
					<dl>
					  <div id="holderCertiType_DIV" class="focus_bind_holder">
						<dt>证件类型<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<Field:codeTable id="holderCertiType" name="customerCertType" nullOption="true" cssClass="combox" disabled="true"
											 tableName="APP___PAS__DBUSER.T_CERTI_TYPE" whereClause="" value="${beneCustomerVO.customerCertType }" 
											 onChange="changeCertiType(this)" />
											 
							<input type="hidden"  id="becustomerCertType" name="beneCustomerVO.customerCertType"/>
						</dd>
					  </div>
					</dl>
					
					<dl>
					  <div id="certiCode_DIV" class="focus_bind_holder">
						<dt>证件号<font color="red">&nbsp;&nbsp;*</font></dt>
						<s:if test="beneCustomerVO.customerCertType eq 1" >
						<dd>					
						    <input name="beneCustomerVO.customerIdCode" value="${ beneCustomerVO.customerIdCode}"  id="certiCode"  onchange="policyHolderCodeMethod(this)" type="hidden" onblur="holderCertiCodeOnblur(this);"oncopy="return false" oncut="return false" onpaste="return false" readonly="readonly"><span id="certiCodeDIV"></span>${ beneCustomerVO.customerIdCode}
				            <input name="beneCustomerVO.customerCertiCode" value="${ beneCustomerVO.customerCertiCode}" id="_customerCertiCode"   style="display: none;" type="text" onchange="checkForCertiType(this)" oncopy="return false" oncut="return false" onpaste="return false" readonly="readonly">
				            <input type="hidden" id="policyHoldercertiCode" value=""/>
						</dd>
						</s:if>
						<s:else>
						<dd>
						    <input name="beneCustomerVO.customerIdCode" value="${ beneCustomerVO.customerIdCode}"  id="certiCode"  onchange="policyHolderCodeMethod(this)" type="expandCertiCode" style="display: none;" onblur="holderCertiCodeOnblur(this);" oncopy="return false" oncut="return false" onpaste="return false" readonly="readonly">
				            <input name="beneCustomerVO.customerCertiCode"   value="${ beneCustomerVO.customerCertiCode}" id="_customerCertiCode"  type="text" onchange="if(checkForCertiType(this)){policyHolderCodeMethod(this)}" oncopy="return false" oncut="return false" onpaste="return false" readonly="readonly"><span id="certiCodeDIV"></span>
				            <input type="hidden" id="policyHoldercertiCode" value=""/>
						</dd>    
						</s:else>
					  </div>
					</dl>
					
					<dl>
						<dt>出生日期<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<input name="beneCustomerVO.customerBirthday" class="text"
								value="<s:date name="beneCustomerVO.customerBirthday" format="yyyy-MM-dd"/>"
								 id="beneBirthDate"  
								onchange="addressIdsAndRuleQuestionBene(this);" readonly="readonly" />
						</dd>
					</dl>
					
					<dl>
						<dt>证件有效期起<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<input name="beneCustomerVO.custCertStarDate" class="date" style="width:135px;"
								value="<s:date name="beneCustomerVO.custCertStarDate" format="yyyy-MM-dd"/>" type="expandDateYMD"
								id="beneCertiStartDt"  />
						</dd>
					</dl>
					
					<dl>
						<dt>证件有效期止<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<input name="beneCustomerVO.custCertEndDate" style="width:135px;" value="<s:date name="beneCustomerVO.custCertEndDate" format="yyyy-MM-dd"/>" class="date"  type="expandDateYMD" id="certiEndDate"
								onblur="endDateOnblur()"/>
				            <input type="checkbox" onclick="endDateClear()"  id="longDate" value="" />长期
						</dd>
					</dl>
					
				 	
					<dl>
						<dt>国籍<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<Field:codeTable id="beneCountryCode" 
								name="beneCustomerVO.countryCode" nullOption="true"
								 tableName="APP___PAS__DBUSER.T_COUNTRY" whereClause="1=1 and country_code not in('ZAR','ACW','AZR','GAT')"
								value="${beneCustomerVO.countryCode }" defaultValue="CHN"></Field:codeTable>
						</dd>
					</dl>

					 <dl>
						<dt>职业编码<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<Field:codeTable id="jobCode" name="beneCustomerVO.jobCode" nullOption="true" multiple="multiple"
							tableName="APP___PAS__DBUSER.T_JOB_CODE" whereClause="JOB_STATUS !=0 " value="${beneCustomerVO.jobCode}"></Field:codeTable> 
						</dd>
					</dl> 
					
					<dl>
						<dt>联系电话<font class="bene_required" color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
						<%-- 	<input type="expandMobile" name="beneCustomerVO.mobileTel" value="${beneCustomerVO.mobileTel}" id="beneMobileTel" /> --%>
						 	 <input type="text" name="beneCustomerVO.mobileTel" 
								  value="${beneCustomerVO.mobileTel}" id="beneMobileTel" />
						</dd>
					</dl>
					<dl>
						<dt>省/直辖市<font color="red">&nbsp;&nbsp;*</font></dt>
						<%-- <dd>
							<input type="text" id="stateName" readonly="readonly"/>
							<input type="hidden" name="beneCustomerVO.state" 
							  	  value="${beneCustomerVO.state}" id="state"/>
						</dd>
					</dl> 
					<dl>
					
						<dt> <font>* </font>省/直辖市</dt> --%>
						
						<dd>
						<s:if test='beneCustomerVO.state!=null' >
						
						<s:if test="bspecialaccountflag=='Special'">
							<s:select list="districtMap" listKey="key" listValue="value" headerKey="all" headerValur="全部" cssClass="combox"
								id="province_panelInfoAdd" name="beneCustomerVO.state"
								ref="city_panelInfoAdd"  initval="${beneCustomerVO.city }"
								refUrl="${ctx }/cs/common/getCity_PA_distictUtilAction.action?provinceCode={value}&Special=1"
								>
							</s:select>
						</s:if>
						<s:else>
							<s:select list="districtMap" listKey="key" listValue="value" headerKey="all" headerValur="全部" cssClass="combox"
								id="province_panelInfoAdd" name="beneCustomerVO.state"
								ref="city_panelInfoAdd"  initval="${beneCustomerVO.city }"
								refUrl="${ctx }/cs/common/getCity_PA_distictUtilAction.action?provinceCode={value}"
								>
							</s:select>
						</s:else>	
							
							
						</s:if>
						<s:else>
						
						  <s:if test="specialaccountflag=='Special'">
							<s:select list="districtMap" listKey="key" listValue="value" headerKey="all" headerValur="全部" cssClass="combox"
								id="province_panelInfoAdd" name="beneCustomerVO.state"
								ref="city_panelInfoAdd"  initval=" "
								refUrl="${ctx }/cs/common/getCity_PA_distictUtilAction.action?provinceCode={value}&Special=1"
								>
							</s:select>
							</s:if>
							<s:else>
							<s:select list="districtMap" listKey="key" listValue="value" headerKey="all" headerValur="全部" cssClass="combox"
								id="province_panelInfoAdd" name="beneCustomerVO.state"
								ref="city_panelInfoAdd"  initval=" "
								refUrl="${ctx }/cs/common/getCity_PA_distictUtilAction.action?provinceCode={value}"
								>
							</s:select>
							</s:else>		
							
						</s:else>
						</dd>
					</dl>
					
					<dl>
						<dt>市<font color="red">&nbsp;&nbsp;*</font></dt>
						<%-- <dd>
							<input type="text" id="cityName" readonly="readonly"/>
							<input type="hidden" name="beneCustomerVO.city" 
							  	  value="${beneCustomerVO.city}" id="city"/>
						</dd>
					</dl>
					<dl>
						<dt>市</dt> --%>
						<dd>
							<s:if test="specialaccountflag=='Special'">
								<select id="city_panelInfoAdd" name="beneCustomerVO.city"
									ref="district_panelInfoAdd"
									refUrl="${ctx }/cs/common/getDistrict_PA_distictUtilAction.action?cityCode={value}&Special=1"
									class="combox" initval="${beneCustomerVO.district }"
									>
									<option value=""> 全部</option>
								</select>
							</s:if>
							<s:else>
								<select id="city_panelInfoAdd" name="beneCustomerVO.city"
									ref="district_panelInfoAdd"
									refUrl="${ctx }/cs/common/getDistrict_PA_distictUtilAction.action?cityCode={value}"
									class="combox" initval="${beneCustomerVO.district }"
									>
									<option value=""> 全部</option>
								</select>
							</s:else>		
						</dd>
					</dl>
					
					<dl>
						<dt>区/县<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<select id="district_panelInfoAdd" name="beneCustomerVO.district"
								ref="townStreetCode_panelInfoAdd" onchange="changePostCodeLX()"
								refUrl="${ctx }/cs/common/getTownStreet_PA_distictUtilAction.action?districtCode={value}"
								class="combox required" initval="${beneCustomerVO.townStreetCode }" >
							</select>
						</dd>
					</dl>
					<dl>
						<dt>街道/镇<font color="red">&nbsp;&nbsp;*</font></dt>
						<dd>
							<select id="townStreetCode_panelInfoAdd" name="beneCustomerVO.townStreetCode"
								class="combox required">
							</select>
						</dd>
					</dl>
					
					 
					
					<dl>
					   <dt>通讯地址<font class="bene_required" color="red">&nbsp;&nbsp;*</font></dt>
					   <dd>
							<input type="text" name="beneCustomerVO.address" 
								  value="${beneCustomerVO.address}" id="address" />
						</dd>
					</dl>
					<dl>
					   <dt>邮政编码</dt>
					   <dd>
							<input type="text" name="beneCustomerVO.postCode"
								class="number" value="${beneCustomerVO.postCode}" id="postCode"  />
						</dd>
					</dl>
					
					<dl>
						<dt>固定电话</dt>
						<dd>
							<%-- <input type="expandPhone" auto="true" name="beneCustomerVO.officeTel" 
								 value="${beneCustomerVO.officeTel}" id="houseTel"  nullOption="true"/>
								 
								  --%>
								 <input type="text" name="beneCustomerVO.officeTel" 
								  value="${beneCustomerVO.officeTel}" id="houseTel" />
						</dd>
					</dl>
					<dl class="shouyi" >
					   <dt>生存金领取形式</dt>
					   <dd>
						<select id="survivalModeBC" cssClass="combox" name="beneCustomerVO.survivalMode" onchange="newChanglq(this)">
							<s:if test="beneCustomerVO.survivalMode==2">
								<option value="2">累积生息</option>
							</s:if>
							<s:elseif test="beneCustomerVO.survivalMode==3">
								<option value="3">抵缴保费</option>
							</s:elseif>
							<s:elseif test="beneCustomerVO.survivalMode==4">
								<option value="4">转万能账户</option>
							</s:elseif>
							<s:else>
								<option value="0">现金领取-需办理保全项目领取</option>
								<option value="1">现金领取-约定银行转账</option>
							</s:else>
						</select>
					</dd>
					</dl>
					<dl class="shouyi" id="liunong">
					   <dt>收付费方式</dt>
					   <dd>
						<select id="survivalWModeBC" cssClass="combox" name="beneCustomerVO.survivalWMode" onchange="newModeChange(this)">
						        <option value="0" >请选择</option>
								<option value="2" >银行转账（制返盘）</option>
								<option value="3" >网上银行</option>
						</select>
					</dd>
					</dl>
					<dl class="survivalModeShow" >
					   <dt>银行代码/开户银行</dt>
					   <dd>
					   <div id="bankAccount2"><input type="text" class="autocomplete textLeft"
						    id="bankCode" name="beneCustomerVO.bankCode"
						  	style="width:60px;margin-right: -2px;"
							value="${beneCustomerVO.bankCode}" data-width="321px"
							data-showValue="bankName" data-mode="2"
							data-disCurrentValFlag="1"
							data-tableName="APP___PAS__DBUSER.T_BANK"
							data-tableCol="BANK_CODE"
							data-tableDesc="BANK_CODE||'-'||BANK_NAME" data-separator="-"
							data-like="1" data-orderBy="BANK_CODE" data-view="BANK_NAME"
							data-view="TYPE" data-whereClause="BANK_NAME LIKE 'NTS%' " />
							<!-- BANK_NAME LIKE 'NTS%' AND  -->
							<input type="text" id="bankName" class="textRight" readOnly style="width: 160px; margin-right: -134px;" />	</div>
							<div id="bankAccount3">
											    	<input class="inputOrg1" name="org1.orgNum" value="" type="hidden"/><!-- 联行号 -->
											    	<input class="inputCreator" name= "org1.creator" value="" type="hidden"/><!-- 联行号对应的银行代码 -->
											   		<input class="required" name="org1.orgName" type="text" postField="keyword" readonly="readonly"
									suggestFields="orgNum,orgName,creator" suggestUrl="cs/pages/csEntry/districtBankCodeQuery.jsp" lookupGroup="org1"/>
													<a class="btnLook" href="${ctx}/cs/csEntry/selectReturnNumber_PA_csEntryAction.action" 
													lookupGroup="org1" style="float: right;">查找带回</a>
											    </div>
						</dd>
						
						<input type="hidden" name="beneCustomerVO.correspondentNo"  id="correspondentNoBC"  value="" />
						<input type="hidden" name="beneCustomerVO.correspondentBankCode"  id="correspondentBankCode"  value="" />
					</dl>
					
					<dl class="survivalModeShow" >
					   <dt>银行账号</dt>
					   <dd>
							<input type="text" name="beneCustomerVO.bankAccount" onchange="checkAccountBank(this)" id="bankAccount" class="number" value="" />
							<font style="color: red; font-size: 11px;" ></font>
						</dd>
					</dl>
					
					<dl class="survivalModeShow" >
					   <dt>户名</dt>
					   <dd>
							<input type="text" name="beneCustomerVO.accoName"
								class="" value="${beneCustomerVO.accoName}" id="accoName" onchange="checkAccountBank(this)"/>
								<font style="color: red; font-size: 11px;" ></font>
						</dd>
					</dl>
					<dl style = "display:none">
						<dt>是否免填单</dt>
						<dd>
							<input type="text"  id="nofillFlag" name="beneCustomerVO.nofillFlag" readonly="readonly" 
									value="${beneCustomerVO.nofillFlag }"    />
						</dd>
					</dl>
					<dl style = "display:none">
						<dt>客户原邮政编码</dt>
						<dd>
							<input id="oldPostCode" type="text" class="number" name="beneCustomerVO.oldPostCode" readonly="readonly" value="${beneCustomerVO.oldPostCode}" />
						</dd>
					</dl>
					<dl style = "display:none">
						<dt>邮编与地址信息匹配功能生成</dt>
						<dd>
							<input id="creatPostCode" type="text" class="number" name="beneCustomerVO.creatPostCode" readonly="readonly" value="${beneCustomerVO.postCode}" />
						</dd>
					</dl>
					<table style="width:100%;padding-top:5px;">
				        <tbody>
					        <tr>
					            <td></td>
					            <td style="width:100px">
					               <!-- <div class="button" >
										<div class="buttonContent" >
											<button type="button" onclick="saveBeneInfo()" >修改</button>
									    </div>
								  	</div> -->
								  	<div class="pageFormbut" >
										<button type="button" class="but_blue" onclick="saveBeneInfo()" >修改</button>
								  	</div>
					            </td>
					            <td></td>
					        </tr>
				    	 </tbody>
				    </table>
				</div> 
 			</div> 
 	</form>
 </div>
<script type="text/javascript">
  //初始化页面时将受益人基本信息设置为只读
	$(function (){		
		//alert("修改页面初始化--start---");
		$("[id='busiPrdId']").attr("disabled",true);
		//var isUpdate = $("#busiItemId1").val();
		searchCountryCode3();
		searchJobCode3();
	}); 
	function searchCountryCode3(){
		$("#beneCountryCode").multiselect({
	        noneSelectedText: "===请选择===",	        
	        selectedList:2,
	        multiple:false
	    }).multiselectfilter({
			label: "筛选",
	        placeholder: "请输入",
	        width: 90
		});
	}
	function searchJobCode3(){
		$("#jobCode").multiselect({
	        noneSelectedText: "===请选择===",	        
	        selectedList:2,
	        multiple:false,
	    }).multiselectfilter({
			label: "筛选",
	        placeholder: "请输入",
	        width: 90
		});
	}
	function changePostCodeLX() {
		var code = $("#district_panelInfoAdd").val();
		var postCode = "";	
		$.ajax({
			type : 'POST',
			dataType : 'json',
			async : false,
			cache : false,
			url : "${ctx}/cs/csConfig/queryPostCodeForJSP_PA_csPostcodeAddressCfgAction.action" ,
			data : 'code='+code ,
			success : function(data) {
				debugger;
				var json = DWZ.jsonEval(data);
				debugger;
				postCode = json.message;
			}
		});
		// 邮编重新赋值
		$("#postCode", navTab.getCurrentPanel()).val(postCode);
		$("#creatPostCode", navTab.getCurrentPanel()).val(postCode);
	}
	
	 function saveBeneInfo(){
		 debugger;
		 var updateFlag = $("#isUpdate",navTab.getCurrentPanel()).val();
		 var benetypeFlag=$("#beneType",navTab.getCurrentPanel()).val();
		 var customerBirthday = $("#beneBirthDate", navTab.getCurrentPanel()).val();// 出生日期
	     var policyHoldercertiCode = $("#policyHoldercertiCode",navTab.getCurrentPanel()).val();
		 
		 //获取当前被选中的街道编码和名字，然后保存
		 var townStree = $("#townStreetCode_panelInfoAdd", navTab.getCurrentPanel()).val();
		 var townStreeName = $("#townStreetCode_panelInfoAdd option:selected", navTab.getCurrentPanel()).text();
		 if("1" == townStree || townStree == "" || townStree == null ){
			townStreeName = "";
			$("#townStreetCode_panelInfoAdd", navTab.getCurrentPanel()).val("")
		 }
		 $("#townStreetName", navTab.getCurrentPanel()).val(townStreeName);
		 debugger;
		 if(check()){
		    var customerId=$("#customerId1").val();		    
			var changeId = $("#changeId1").val();
			var busiItemId=$("#busiItemId1").val();
			var logId=$("#logId1").val();			
			var acceptId = $("#acceptId").val();
			var policyId =$("#policyId1").val();
			var policyChgId = $("#policyChgId").val();			
			var customerLogId=$("#customerLogId").val();
			var postCode=$("#postCode").val();
			var insuredId = $("#insuredId").val();
			debugger;
			var beneMobileTel=$("#beneMobileTel").val().replaceAll(" ","");
			debugger;
			var becustomerCertType=$("#becustomerCertType").val();
			if(becustomerCertType != null && becustomerCertType != '' && (becustomerCertType == 'b' || becustomerCertType == '8')){
				 alertMsg.info("证件类型有误，请根据实际情况修改对应的证件类型");
					return false;
			}
			
			if(becustomerCertType == '4'){
				//var birthDate = birthdayObj.val();
				var customerBirthday = $("#beneBirthDate", navTab.getCurrentPanel()).val();// 出生日期
		 		var age = GetAge(customerBirthday.replace("-","/"));
		 		if (age >3) {//16周岁以上不可使用出生证明/户口本作为有效身份证明文件
						alertMsg.error("客户年龄大于3周岁，不允许使用出生证明！");
						return false;	
				}
		 		if(policyHoldercertiCode.length != 10){
					alertMsg.error("出生证明证件号码应为10位。");
					return;
				}else{   
					var checkFlag = /^[A-Z]{1}.*/.test(policyHoldercertiCode);
					if (!checkFlag){
						alertMsg.error("出生证明证件号码首位应为英文字母（大写）。");
						return;
					}else{  
						var checkFlag = /.*(\d)(?!\1{8})[\d]{8}$/.test(policyHoldercertiCode);
						if(!checkFlag){
							alertMsg.error("数字必须为9位，且9位数字不能完全相同。");
							return;
						}
					}
				}
			}
			
			
			var rootPath= getRootPath();
			
			debugger;
			var nofillFlag = $("#nofillFlag", navTab.getCurrentPanel()).val();// 是否勾选免填单  如果未勾选，存值为“YES”。
			var oldPostCode = $("#oldPostCode", navTab.getCurrentPanel()).val();
			var creatPostCode = $("#creatPostCode", navTab.getCurrentPanel()).val();
			var postCode = $("#postCode",navTab.getCurrentPanel()).val();
			var provinceName = $("#province_panelInfoAdd option:selected", navTab.getCurrentPanel()).text();
			var cityName = $("#city_panelInfoAdd option:selected", navTab.getCurrentPanel()).text();
			var districtName = $("#district_panelInfoAdd option:selected", navTab.getCurrentPanel()).text();
			var getPostCode = findPostCode(provinceName,cityName,districtName);
			var survivalWMode=$("#survivalWModeBC", navTab.getCurrentPanel()).val();
			var survivalMode=$("#survivalModeBC", navTab.getCurrentPanel()).val();
			if(survivalMode == 0){
				$("#survivalWModeBC", navTab.getCurrentPanel()).val(1);
				$("#survivalModeBC", navTab.getCurrentPanel()).val(1);
			}
			if(survivalWMode == 3){
				var correspondentNo = $(".inputOrg1", navTab.getCurrentPanel()).val();
				var correspondentBankCode = $(".inputCreator", navTab.getCurrentPanel()).val();
				$("#correspondentNoBC", navTab.getCurrentPanel()).val(correspondentNo);
				$("#correspondentBankCode", navTab.getCurrentPanel()).val(correspondentBankCode);
			}
			debugger;
	        // 经与需求确认，如下提示话术需要提示场景，需要符合如下校验：
	        // 校验规则 1：本次保全未勾选免填单  "YES" == nofillFlag；校验规则2：变更前邮政编码为空 oldPostCode == ''；校验规则3：变更后的邮政编码为 自动匹配出的邮政编码
	        if ("YES" == nofillFlag && oldPostCode == '' && postCode != "" && postCode != null &&  postCode == creatPostCode) {
				alertMsg.info("请与客户确认系统自动匹配的邮编信息并在保全申请书中补充填写！");
				// return false;
			}
	        debugger;
			if (null != postCode && postCode != "" ){
				debugger;
				if(postCode.length != 6 || !(/^\d{6}$/.test(postCode))){
					alertMsg.confirm("邮政编码应为6位数字。",{	
						okCall:function(){
							if(getPostCode != postCode){
								alertMsg.confirm("地址与邮编信息不匹配，请重新录入。",{
									okCall:function(){
										formSubmit();
									},
									cancelCall:function(){
										
									}	
								});
							}else{
								formSubmit();
							}
						},
						cancelCall:function(){
						}
					});
				}else{
					if(getPostCode != postCode){
						alertMsg.confirm("地址与邮编信息不匹配，请重新录入。",{
							okCall:function(){
								formSubmit();
							},
							cancelCall:function(){
								
							}	
						});
					}else{
						formSubmit();
					}
				}
			}else{
				formSubmit();
			}
			
		 }	
	 }
	 
	 function formSubmit(){
		 debugger;
		 var $form = $("#updateBeneForm");
			var onsubmit = "return validateCallback(this,updateEndAjaxDoneForadd)";
			$form.attr('onsubmit', onsubmit);
			var logId=$("#logId1").val();
			var changeId = $("#changeId1").val();
			var customerLogId=$("#customerLogId").val();
			var customerId=$("#customerId1").val();	
			var acceptId = $("#acceptId").val();
			var policyId =$("#policyId1").val();
			var busiItemId=$("#busiItemId1").val();
			var policyChgId = $("#policyChgId").val();		
			var insuredId = $("#insuredId").val();
			var postCode=$("#postCode").val();
			debugger;
			var beneMobileTel=$("#beneMobileTel").val().replaceAll(" ","");
			debugger;
			var becustomerCertType=$("#becustomerCertType").val();
			var rootPath= getRootPath();
			debugger;
			$.ajax({
				type : 'POST',
				url : rootPath+"/cs/serviceitem_bc/updateBeneInfo_PA_csEndorseBCAction.action?logId="+logId
						+"&changeId="+changeId
						+"&customerLogId="+customerLogId
						+"&customerId="+customerId
						+"&acceptId="+acceptId
						+"&policyId="+policyId
						+"&policyChgId="+policyChgId
						+"&insuredId="+insuredId
						+"&beneMobileTel="+beneMobileTel
						+"&busiItemId="+busiItemId,
				data : $form.serialize(),
				cache : false,
				async : true,
				dataType : "json",
				success : function(data) {
					
					var json = DWZ.jsonEval(data);
					if (json.statusCode == "200" && json.message != "") {
						debugger;
						alertMsg.confirm(json.message, {
							okCall : function() {
								    //校验重复电话
									checkRepeat();
							},
							cancelCall : function() {
								return false;
							}
						});
					} else if (json.statusCode == "200" && json.message == "") {
						updateBeneInfoExecute();
					}else if(json.statusCode == "300"){
						alertMsg.error(json.message);
					}
				},
				error : DWZ.ajaxError
			});
	 }
	 
	 
	 
	 function checkRepeat(){
		 debugger;
		var logId=$("#logId1").val();	
		var changeId = $("#changeId1").val();
		var customerLogId=$("#customerLogId").val();
		var acceptId = $("#acceptId").val();
		var beneMobileTel=$("#beneMobileTel").val().replaceAll(" ","");;
		var rootPath= getRootPath();
		var $form = $("#updateBeneForm");			
		//var onsubmit = "return validateCallback(this,updateEndAjaxDoneForadd)";
		//$form.attr('onsubmit', onsubmit);
		$.ajax({
			type : 'POST',
			url : rootPath+"/cs/serviceitem_bc/checkRepeat_PA_csEndorseBCAction.action?logId="+logId
					+"&changeId="+changeId
					+"&customerLogId="+customerLogId
					+"&acceptId="+acceptId
					+"&beneMobileTel="+beneMobileTel,
			data : $form.serialize(),
			cache : false,
			async : true,
			dataType : "json",
			success : function(data) {
				var json = DWZ.jsonEval(data);
				if (json.statusCode == "200" && json.message != "") {
					alertMsg.confirm(json.message, {
						okCall : function() {
							updateBeneInfoExecute();
						},
						cancelCall : function() {
							return false;
						}
					});
				} else if (json.statusCode == "200" && json.message == "") {
					updateBeneInfoExecute();
				}else if(json.statusCode == "300"){
					alertMsg.error(json.message);
				}
			},
			error : DWZ.ajaxError
		});
	 }
	 
	 function updateBeneInfoExecute(){
		 	var logId=$("#logId1").val();	
			var changeId = $("#changeId1").val();
			var customerLogId=$("#customerLogId").val();
			var acceptId = $("#acceptId").val();
			var beneMobileTel=$("#beneMobileTel").val().replaceAll(" ","");
			var rootPath= getRootPath();
			var $form = $("#updateBeneForm");			
			//var onsubmit = "return validateCallback(this,updateEndAjaxDoneForadd)";
			//$form.attr('onsubmit', onsubmit);
			$.ajax({
				type : 'POST',
				url : rootPath+"/cs/serviceitem_bc/updateBeneInfoExecute_PA_csEndorseBCAction.action?logId="+logId
						+"&changeId="+changeId
						+"&customerLogId="+customerLogId
						+"&acceptId="+acceptId
						+"&beneMobileTel="+beneMobileTel,
				data : $form.serialize(),
				cache : false,
				async : true,
				dataType : "json",
				success : function(data) {
					var json = DWZ.jsonEval(data);
					if (json.statusCode == "200" && json.message == "") {
						alertMsg.correct("修改成功！");
						updateEndAjaxDoneForadd(json);
					}else if(json.statusCode == "300"){
						alertMsg.error(json.message);
					}
				},
				error : DWZ.ajaxError
			});
		 }
	 
	 
	 
	 
	 
	 
	 
	 
	 
	  function check(){
		 //所属险种
		 var busiItemId=$("#busiItemId",navTab.getCurrentPanel()).val();
		 //受益人顺
		 var beneShareOrder=$("#beneShareOrder",navTab.getCurrentPanel()).val(); 
		 //与被保人关系
		 var designationBene=$("#designationBene",navTab.getCurrentPanel()).val();
		 //名字
		 var customerName=$("#holderName",navTab.getCurrentPanel()).val();
		 var cscustomerName=$("#csCustomerVO_customerName",navTab.getCurrentPanel()).val();
		 //证件类型
		 var holderCertiType=$("#holderCertiType",navTab.getCurrentPanel()).val();
		 var customerIdCode=$("#certiCode",navTab.getCurrentPanel()).val().trim();//居民身份证
		 //证件号
		 //var customerCertiCode=$("#policyHoldercertiCode").val();
		 var customerCertiCode=$("#_customerCertiCode",navTab.getCurrentPanel()).val().trim();
		 //有效期起
		 var beneCertiStartDt=$("#beneCertiStartDt",navTab.getCurrentPanel()).val();
		 //有效止
		 var certiEndDate=$("#certiEndDate",navTab.getCurrentPanel()).val();
		 //国籍
		 var beneCountryCode=$("#beneCountryCode",navTab.getCurrentPanel()).val();
		 //性别
		 var beneGenderId=$("#beneGenderId",navTab.getCurrentPanel()).val();
		 //出生日期
		 var beneBirthDate=$("#beneBirthDate",navTab.getCurrentPanel()).val();
		 //职业编码
		 var jobCode=$("#jobCode",navTab.getCurrentPanel()).val();
		 //联系方法
		 var beneMobileTel=$("#beneMobileTel",navTab.getCurrentPanel()).val().trim();
		 //固定电话
		var houseTel=$("#houseTel",navTab.getCurrentPanel()).val().trim();
	 	//收益类型
	 	var shareRate=$("#shareRate",navTab.getCurrentPanel()).val();
	 	//受益性质
	 	var beneType=$("#beneType",navTab.getCurrentPanel()).val();
	 	var isUpdate = $("#isUpdate",navTab.getCurrentPanel()).val();
	 	var changQiFlag =  $("#changQiFlag", navTab.getCurrentPanel()).val(); //是否长期
		var nofillFlag = $("#nofillFlag", navTab.getCurrentPanel()).val(); // 是否展示免填单标识
		var oldPostCode = $("#oldPostCode", navTab.getCurrentPanel()).val();
		var creatPostCode = $("#creatPostCode", navTab.getCurrentPanel()).val();
	 	/*  if(houseTel!=""){
	 		var rel =/^([0-9]{3,4}-)?[0-9]{7,8}$/;
			if(!rel.test(houseTel)){
				alertMsg.info("固定电话不正确,请核实！yy1");
			}
	 	}  */
	 	
	 	//户名
		var accoName=$("#accoName", navTab.getCurrentPanel()).val();
		//领取形式
		var survivalMode=$("#survivalModeBC", navTab.getCurrentPanel()).val();
		//收付费方式
		var survivalWMode=$("#survivalWModeBC", navTab.getCurrentPanel()).val();
		//银行代码
		var bankCode=$("#bankCode", navTab.getCurrentPanel()).val();
		if(survivalWMode == 3){
			bankCode = $(".inputCreator", navTab.getCurrentPanel()).val()
		}
		//银行
		var bankAccount=$("#bankAccount", navTab.getCurrentPanel()).val();
		//alert(accoName +"--"+customerName);
		//alert(accoName != customerName);
		if(isUpdate == 1 && beneType == 0 && accoName != cscustomerName){
			alert("户名与客户姓名不一致,请确认!");
			//alertMsg.info("户名与客户姓名不一致,请确认!");
		}
		if(isUpdate == 1 && beneType == 0 && survivalMode == 5){
			alertMsg.info("请选择生存金领取形式！");
			return false;
		}
		if(isUpdate == 1 && beneType == 0 && (survivalMode == '' && bankCode == '' && bankAccount == '' && accoName == '')){
			//alertMsg.info("生存受益人领取信息为空,请确认!");
			alert("生存受益人领取信息为空,请确认!");
		}
		if(isUpdate == 1 && (survivalWMode == 2 || survivalWMode == 3) && (bankCode == '' || bankAccount == '' || accoName == '')){
			alertMsg.info("领取形式不为自行领取,请输入银行账号信息");
			return false;
		}
		if(isUpdate == 1 && (bankCode != '' || bankAccount != '' || accoName != '') && survivalWMode == ''){
			alertMsg.info("填写了银行信息,请选择相应的领取形式!");
			return false;
		}
		if(isUpdate == 1 && (bankCode != '' || bankAccount != '' || accoName != '') && (bankCode == '' || bankAccount == '' || accoName == '')){
			alertMsg.info("银行信息填写不完整!");
			return false;
		}
		//校验手机号和固定电话的格式
		if(!checkMobileOrPhoneFormat(beneMobileTel,"0",holderCertiType,beneCountryCode)){
			return false
		}
		if(!checkMobileOrPhoneFormat(houseTel,"1",holderCertiType,beneCountryCode)){
			return false
		}
		
		checkThePhone($("#houseTel",navTab.getCurrentPanel()),"houseTel",beneCountryCode);
		
	 	/* var phoneRegex = /^(([0\+]\d{2,3}-)?(0\d{2,3})-)?(\d{7,8})(-(\d{3,}))?$/;
		if (!isNulOrEmpty(houseTel) && !isNulOrEmpty(houseTel)) {
			if(!phoneRegex.test(houseTel)){
				alertMsg.info("固定电话不正确,请核实！");
				return false;
			}
		} */
	 	if($("#longDate").attr("checked") == "checked"){
	 		certiEndDate = "9999-12-31";
	 		$("#changQiFlag").val("1");
//	 		$("#certiEndDate").val("9999-12-31");
	 		
	 	}
		if (busiItemId == '') {
			alertMsg.info("请选择所属险种");
			return false;
		}
		if (beneShareOrder == '') {
			alertMsg.info("请选择受益人顺序");
			return false;
		} 
		if (designationBene == '') {
			alertMsg.info("请选择与被保险人关系");
			return false;
		}
		if (customerName == '' ||cscustomerName=='') {
			alertMsg.info("姓名不能为空");
			return false;
		}
		if (holderCertiType == '') {
			alertMsg.info("请选择证件类型");
			return false;
		}
		/* if(customerIdCode==''&&holderCertiType=='01'){
			alertMsg.info("请填写18位身份证号");
			return false;
		}  */
		//如果原先客户信息不正确，提示阻断信息 add by yangyl
		
		/*  if(holderCertiType=='0' &&(customerIdCode==''||customerIdCode.length != 18) ){
			alertMsg.info("身份证信息不正确不允许修改，请操作客户重要资料变更保全项更改客户信息");
			return false;
		}  */ 
		if(customerIdCode!=''&&holderCertiType=='01'){
			$("input#_customerCertiCode", navTab.getCurrentPanel()).val(customerIdCode);
			return true;
		}
		
		 /* if (customerIdCode == ''&&holderCertiType!='01') {
			//alert(customerCertiCode);
			alertMsg.info("请填写证件号");
			return false;
		}   */
		if (beneCertiStartDt == '') {
			alertMsg.info("请选择证件有效期起");
		
			return false;
		}
		if (certiEndDate == '' && changQiFlag == '') {
			alertMsg.info("请选择证件有效期止");
			return false;
		}
		if (beneCountryCode == '') {
			alertMsg.info("请选择国籍");
			return false;
		}
		if (beneGenderId == '') {
			alertMsg.info("性别不能为空");
			return false;
		}
		if (beneBirthDate == '') {
			alertMsg.info("出生日期不能为空");
			return false;
		}
		if (jobCode == '') {
			alertMsg.info("请选择职业编码");
			return false;
		}
		
		/* if (beneMobileTel == '' && houseTel == "") {
			alertMsg.info("请填写联系方式");
			return false;
		} */
		
		
		
		if (shareRate == '') {
			alertMsg.info("请填受益比例");
			return false;
		}
		if (beneType == '') {
			alertMsg.info("请填写受益性质");
			return false;
		}
		if(beneCertiStartDt<beneBirthDate){
			alertMsg.info("证件有效期起  不能早于  出生日期");
			return false;
		}
		
		if(beneCertiStartDt>certiEndDate ){
			alertMsg.info("证件有效期起  不能晚于  证件有效期止");
			return false;
		}
		
		if(!checkEndTime()){//uat环境注释//checkStartTime
			alertMsg.info("证件有效止期不能早于当前日期,请确认");
			return false;
		}  
		if(!checkStartTime()){//checkStartTime
			alertMsg.info("证件有效起期不能晚于当前日期,请确认");
			return false;
		} 
		
		 var certiEndDate=$("#certiEndDate",navTab.getCurrentPanel()).val();
	     var beneCertiStartDt=$("#beneCertiStartDt", navTab.getCurrentPanel()).val();
	     
		
		if(holderCertiType == '0'){
			if (customerCertiCode.length == 15) {
	    		 if((!idCardNoUtil.check15IdCardNo(customerCertiCode))){
	 	 			alertMsg.warn("请输入正确身份证号码");
	 	 			return false;
	 	 		}
	    	 }
	    	 
			if (customerCertiCode.length == 18) {
				if((!idCardNoUtil.check18IdCardNo(customerCertiCode))){
		 			alertMsg.warn("请输入正确身份证号码");
		 			return false;
		 		}
	    	 }
			
	 	}else if(holderCertiType == '1'){
	 		if(!CV_checkPassport(customerCertiCode)){
	 			alertMsg.warn("请输入正确护照号码");
	 			return false;
	 		}
	 	}else if(holderCertiType == '2'){
	 		if(!CV_checkOfficerNo(customerCertiCode)){
	 			alertMsg.warn("证件号码应为10（含）-18（含）个字符，且只能含有汉字、数字、字母、-，请重新录入。");
	 			return false;
	 		}
	 	}else if(holderCertiType == '4'){
			if(customerCertiCode.length != 10){
				alertMsg.error("出生证明证件号码应为10位。");
				return;
			}else{   
				var checkFlag = /^[A-Z]{1}.*/.test(customerCertiCode);
				if (!checkFlag){
					alertMsg.error("出生证明证件号码首位应为英文字母（大写）。");
					return;
				}else{  
					var checkFlag = /.*(\d)(?!\1{8})[\d]{8}$/.test(customerCertiCode);
					if(!checkFlag){
						alertMsg.error("数字必须为9位，且9位数字不能完全相同。");
						return;
					}
				}
			}
	 	}else if(holderCertiType == '5'){
	 		if(!CV_checkHouseholdRegisterNo(customerCertiCode)){
	 			alertMsg.warn("请输入正确户口簿号码");
	 			return false;
	 		}
	 	}else if(holderCertiType == 'b'){
	 		if(!CV_checkGATPassport(customerCertiCode)){
	 			alertMsg.warn("请输入正确港澳台居民内地通行证");
	 			return false;
	 		}
	 	}else if(holderCertiType == 'e'){
	 		var result = CV_checkForeignIdCrad(customerCertiCode,beneBirthDate);
	 		if(result!=true){
	 			alertMsg.warn(result);
	 			return false;
	 		}
	 	}else{
	 		if(customerCertiCode.length>30){
	 			alertMsg.warn("证件号码长度不能超过30个字符！");
	 			return false;
	 		}
	 	}
		
		 var postCode=$("#postCode", navTab.getCurrentPanel()).val();//邮政编码
		 //var jobCode=$("#jobCode", navTab.getCurrentPanel()).val();//职业编码
		 //beneMobileTel 联系电话
		 var address=$("#address", navTab.getCurrentPanel()).val();//
		 //alert(postCode);
		 if(address == ''){
				alertMsg.info("请填写通讯地址");
				return false;
		  }else if(address.length < 5){
				alertMsg.info("街道录入不得少于5个字，请重新录入。");
				return false;
		  }
		 var reg1= /^(?=.*?[\u4E00-\u9FA5]){5,100}/;
		 if(!reg1.test(address)){
			alertMsg.info("街道录入5个字须包含汉字，且5个字不能全相同，请重新录入。");
			return false;
		 }
		 var tem=0;
		 for(var i=0;i<address.length;i++){
            if(address.charAt(i)==address.charAt(0)){
                tem++
            }
         }
         if(address.length==tem){
        	alertMsg.info("街道录入5个字须包含汉字，且5个字不能全相同，请重新录入。");
        	return false;
         }
         debugger;
        var radio = $(".myClass:checked",navTab.getCurrentPanel());
        var organCode = $(radio).parent().parent().find("td:eq(6)").text();
 		if (!checkAddressHeBei(organCode, address)){
			return false;
		}
 		
 		if ("YES" == nofillFlag && oldPostCode == '' && postCode != "" && postCode != null &&  postCode == creatPostCode) {
			alertMsg.info("请与客户确认系统自动匹配的邮编信息并在保全申请书中补充填写！");
			// return false;
		}
 		
		 if(changeShareRate()){
			 return false;
		 }
		 if(designationBene != '01' && designationBene != '02' && designationBene != '03' && designationBene != '04' && designationBene != '07'){
			 if(jobCode == '' || (beneMobileTel == '' && houseTel == "") || address == ''){
				 alertMsg.info("当于被保险人关系为非父子、父女、母子、母女、夫妻关系时受益人的职业编码、联系电话、通讯地址为必录项。");
				 return false;
			 }
		 }
		if (beneCountryCode == "HKG" || beneCountryCode == "MAC") {
			if ( holderCertiType != "h"  && holderCertiType != "i") {
				alertMsg.error("国籍为中国香港或中国澳门时，证件类型应为“ 港澳台居民居住证”或“港澳居民来往内地通行证”，请重新录入。");
				return false;
			}
		}else if(beneCountryCode == "TWN"){
			if(holderCertiType != "d" && holderCertiType != "h"){
			  alertMsg.error("国籍为中国台湾时，证件类型应为 “台湾居民来往大陆通行”，请重新录入。");
			  return false;
			}
		}else if (beneCountryCode == "CHN") {//中国
			//如果年龄小于等于5nowAge，可用护照
			if (holderCertiType == "e") {//如果选择护照
				alertMsg.error("投保人国籍为中国，证件类型不能为外国人永久居留证件，请重新录入。");
				return false
			}
		
			if (holderCertiType == "1") {//如果选择护照
				alertMsg.warn("投保人国籍为中国，证件类型不能为护照，请重新录入。");
			}
		} else if (beneCountryCode != "CHN" && beneCountryCode != "HKG" && beneCountryCode != "MAC" && beneCountryCode != "TWN" && beneCountryCode != "GAT" ) {//客户国籍非中国、中国香港、中国澳门、中国台湾时，证件类型只能为护照或外国人永久居留证
			if (holderCertiType != "1" && holderCertiType != "e") {//如果未选择护照  外国人永久居留证件
				var holdercountryCode = $("#holdercountryCode",
						navTab.getCurrentPanel()).val();
				alertMsg.error("国籍非中国、中国香港、中国澳门、中国台湾时，证件类型只能为护照或外国人永久身份证，请重新录入。");
				return false;
			}
		}
		return true;
	}

    /**
     * 方法说明：姓名校验
     */

    function policyHolderNameMethod(obj) {
	var holderName = obj.value;
	if ($("#policyHoldername").val() == ""|| $("#policyHoldername").val() == null) {
	    $("#policyHoldername").attr("value", holderName);
	    obj.value = "";
	    $("#holdernameDIV").html('<font>&nbsp;&nbsp;&nbsp;&nbsp;请再次输入</font>');
	} else {
	    if (obj.value == $("#policyHoldername").val()) {
			$("#policyHoldername").attr("value", "");
			$("#csCustomerVO_customerName").attr("class", "textInput holder");
			$("#holdernameDIV").html("");
	    } else {
			$("#csCustomerVO_customerName").attr("class","textInput error");
			$("#csCustomerVO_customerName").val("");
			$("#holdernameDIV").html("");
			$("#policyHoldername").attr("value", "");
			alertMsg.warn("两次输入姓名不一致");
	    }
	}
  }
    /**
     * 方法说明：选择证件类型
     */
    function changeCertiType(obj){  	
	var certiType = $(obj).val();
	var customerBirthday = $("#beneBirthDate", navTab.getCurrentPanel()).val();// 出生日期
	if(certiType == '01'){
		$("#certiCode").show();
		$("#_customerCertiCode").hide();
		$("#_customerCertiCode").removeAttr("class");
		$("#_customerCertiCode").val(null);
	}else if(certiType =='4'){		
		//当证件类型为“出生证明”时，本次自动调整为其4周岁生日前一日，不允许修改
		var customerBirthdayForDate = new Date(customerBirthday.replace(/-/g, "/"));
   	    customerBirthdayForDate = customerBirthdayForDate.setFullYear(customerBirthdayForDate.getFullYear()+4);
   	    var date2 = dateChange(-1,customerBirthdayForDate);
		 $("#certiEndDate", navTab.getCurrentPanel()).attr("value",date2);
		 $("#certiEndDate", navTab.getCurrentPanel()).attr('readonly', true);
		 //$("#custCertEndDate", navTab.getCurrentPanel()).attr('disabled', true);
   	     $("#longDate", navTab.getCurrentPanel()).attr("checked", false);
 	     $("#longDate", navTab.getCurrentPanel()).attr('disabled', true);
   	     
   	     
	}else{
		$("#certiCode").hide();
		$("#_customerCertiCode").show();
		$("#_customerCertiCode").attr("class","textInput holder");
		$("#certiCode").val(null);
		$("#certiCodeDIV").html("");
	}	
   }
    /**
     * 方法说明：证件号码校验
     */
    function policyHolderCodeMethod(obj){    	
     //alert($("#policyHoldercertiCode").val());
	var holderCertiCode=obj.value;
	if($("#policyHoldercertiCode").val()=="" || $("#policyHoldercertiCode").val()==null){
		$("#policyHoldercertiCode").attr("value",holderCertiCode);
		//$("#policyHoldercertiCode").val(holderCertiCode);
		obj.value="";
		$("#certiCodeDIV").html('<font>请再次输入</font>') ;
	}else{
		if(obj.value ==  $("#policyHoldercertiCode").val()){
		    $("#"+obj.id).attr("class","textInput holder");
			$("#policyHoldercertiCode").val("");
			$("#certiCodeDIV").html("");
			//带出生日和性别信息
			if($("#holderCertiType").val() == '01'){
				saxAndBirthday_holder();
			}
		}else{
			$("#"+obj.id).attr("class","error");
			$("#"+obj.id).val("");
			$("#certiCodeDIV").html("");
			alertMsg.warn("两次输入证件号码不一致!");
			$("#policyHoldercertiCode").attr("value","");
			return;
		}
	}
}
    /**
     * 方法说明：根据证件号码校验证件类型
     */
    function checkForCertiType(obj){
    	if($("#holderCertiType").val() == "" || $("#holderCertiType").val() == null){
    		obj.value="";
    		alertMsg.warn("请先选择证件类型！");
    		return false;
    	}
    	return true;
    }
	function endDateOnblur() {
		var certyType = $("#becustomerCertType", navTab.getCurrentPanel()).val();
		var certiEndDate = $("#certiEndDate", navTab.getCurrentPanel()).val();//长期是否
		if (certiEndDate == '9999-12-31') {
			if (certyType == "h") {//港澳台居民居住证
				$("#certiEndDate", navTab.getCurrentPanel()).val("");
				$("#certiEndDate", navTab.getCurrentPanel()).attr( 'disabled', false);
				$("#longDate", navTab.getCurrentPanel()).attr("checked", false);
				$("#changQiFlag", navTab.getCurrentPanel()).val("0");
				return;
			}
			$("#certiEndDate", navTab.getCurrentPanel()).val("");
			$("#certiEndDate", navTab.getCurrentPanel()).attr( 'disabled', true);
			$("#longDate", navTab.getCurrentPanel()).attr("checked", true);
			$("#changQiFlag", navTab.getCurrentPanel()).val("1");
		} else {
			$("#certiEndDate", navTab.getCurrentPanel()).attr( 'disabled', false);
			$("#longDate", navTab.getCurrentPanel()).attr("checked", false);
			$("#changQiFlag", navTab.getCurrentPanel()).val("0");
		}
	}
	
	function endDateClear() {
		var certyType = $("#becustomerCertType", navTab.getCurrentPanel()).val();
		if ($("#longDate", navTab.getCurrentPanel()).attr("checked") == "checked") {
			if (certyType == "h") {//港澳台居民居住证
				$('#longDate', navTab.getCurrentPanel()).removeAttr("checked");
				$("#changQiFlag", navTab.getCurrentPanel()).val("0");
				return;
			}
			$("#certiEndDate", navTab.getCurrentPanel()).attr( 'disabled', true);
			$("#certiEndDate", navTab.getCurrentPanel()).val("");
			$("#changQiFlag", navTab.getCurrentPanel()).val("1");
		}else{
			$("#certiEndDate", navTab.getCurrentPanel()).attr( 'disabled', false);
			$("#changQiFlag", navTab.getCurrentPanel()).val("0");
			$("#certiEndDate", navTab.getCurrentPanel()).val("");
		}
	}
  //保存时,校验当前操作日期和证件有效止期
    function checkEndTime(){
    	//var time = new Date();
    	var seperator = "/";
    	var time = getUserTimeForPA();//获取当前操作时间
    	time = time.substring(0,10);
    	var ndates = time.split("-");
    	var ntime = ndates[0] + seperator + ndates[1] + seperator + ndates[2];
    	var nowTime = new Date(ntime);//当前时间的时间格式
    	
    	var certiEndDate=$("#certiEndDate", navTab.getCurrentPanel()).val();
		var changQiFlag =  $("#changQiFlag").val();//是否长期
        if("1" == changQiFlag){
        	certiEndDate = "9999-12-31";
        }
    	
    	var dates = certiEndDate.split("-");
    	var etime = dates[0] + seperator + dates[1] + seperator + dates[2];
    	var endTime = new Date(etime);//证件有效止期的时间格式
    	
    	if(endTime < nowTime){
    		return false;
    	}
    	return true;
    }
    
    //保存时,校验当前操作日期和证件有效止期
    function checkStartTime(){
    	//var time = new Date();
    	var seperator = "/";
    	var time = getUserTimeForPA();//获取当前操作时间
    	time = time.substring(0,10);
    	var ndates = time.split("-");
    	var ntime = ndates[0] + seperator + ndates[1] + seperator + ndates[2];
    	var nowTime = new Date(ntime);//当前时间的时间格式
    	
    	var certiStartDate=$("#beneCertiStartDt", navTab.getCurrentPanel()).val();
    	var dates = certiStartDate.split("-");
    	var stime = dates[0] + seperator + dates[1] + seperator + dates[2];
    	var startTime = new Date(stime);//证件有效止期的时间格式
    	
    	if(startTime > nowTime){
    		return false;
    	}
    	return true;
    }
    
    /*  受益比例的校验 */
    function changeShareRate(){
    	var shareRate = $("#shareRate").val();
    	var beneShareOrder = $("#beneShareOrder").val();
     	if(shareRate>100 || shareRate<=0 || shareRate != shareRate-0){
     		alertMsg.warn("受益人比例只能是0到100的数字!");
     		return true;
     	}
     	shareRate = shareRate-0;
     	if(beneShareOrder == '1'){
	     	if(shareRate>100){
	     		alertMsg.warn("同一所属被保险人、受益顺序的受益比例之和不能大于100%!");
		     	$("#shareRate").val("");
		     	return true;
	     	} 
     	}
     	if(beneShareOrder == '2'){
	     	if(shareRate>100){
	     		alertMsg.warn("同一所属被保险人、受益顺序的受益比例之和不能大于100%!");
		     	$("#shareRate").val("");
		     	return true;
	     	} 
     	}
     	if(beneShareOrder == '3'){
	     	if(shareRate>100){
	     		alertMsg.warn("同一所属被保险人、受益顺序的受益比例之和不能大于100%!");
		     	$("#shareRate").val("");
		     	return false;
	     	} 
     	}
    }
    
    function dateChange(num,date) {
		　　if (!date) {
		　　　　date = new Date();//没有传入值时,默认是当前日期
		　　　　date = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
		　　}
		　　date = date/1000 - 86400;//修改后的时间戳
		　　var newDate = new Date(parseInt(date) * 1000);//转换为时间
		   var year = newDate.getFullYear();
 	   var month = newDate.getMonth()+1, month = month < 10 ? '0' + month : month;
 	   var day = newDate.getDate(), day =day < 10 ? '0' + day : day;
		　　return year + '-' + month + '-' + day;
		}
  //级联下拉框的Id数组 province_panelInfoAdd  city_panelInfoAdd district_panelInfoAdd
	dwz_combox_myarray = new Array('province_panelInfoAdd',
			'city_panelInfoAdd', 'district_panelInfoAdd');
	//可选参数
	dwz_combox_mybox = navTab.getCurrentPanel();
	
	//点击领取形式变更后的状态
	function newChanglq(line) {
		if (line.value == 1) {
			$("#survivalWModeBC", navTab.getCurrentPanel()).show();
			$(".survivalModeShow", navTab.getCurrentPanel()).show();
		} else {
			$("#survivalWModeBC", navTab.getCurrentPanel()).hide();
			$(".survivalModeShow", navTab.getCurrentPanel()).hide();
		}
	}
	function newModeChange(line) {
		if (line.value == 2 || line.value == 3) {
			$(".survivalModeShow", navTab.getCurrentPanel()).show();
			if(line.value == 2){
				$("#bankAccount2", navTab.getCurrentPanel()).show();
				$("#bankAccount3", navTab.getCurrentPanel()).hide();
			}else{
				$("#bankAccount2", navTab.getCurrentPanel()).hide();
				$("#bankAccount3", navTab.getCurrentPanel()).show();
			}
			
		} else {
			$(".survivalModeShow", navTab.getCurrentPanel()).hide();
		}
	}
	//银行账号二次重复校验
	var bankAccountCheckTime = 1;//账号输入次数
	var bankAccountFirst="";//第一次输入
	function checkAccountBank(obj){
		if (bankAccountCheckTime==1) {
			bankAccountFirst=obj.value;//第一次输入
			bankAccountCheckTime+=1;
			obj.value="";
			$(obj).parent().children("font").html('请再次输入');
		}else if (bankAccountCheckTime==2) {	
			if (bankAccountFirst!="" && bankAccountFirst!=obj.value) {
				$(obj).parent().children("font").attr("class", "error");
				$(obj).parent().children("font").html("");
				alertMsg.info("两次输入不一致，请重新输入！");	
				$(obj).parent().children("font").val("");
				obj.value="";
				bankAccountCheckTime=1;
				return;
			}else{
				$(obj).parent().children("font").html("");
				bankAccountCheckTime=1;
			}
		}		
	}
	function changeBeneType(obj){
	    //var beneType = $('a[name="beneCustomerVO.beneType"]', navTab.getCurrentPanel()).text();
	    if(obj.value == null || obj.value == ''){//请选择
	    	$(".survivalModeShow", navTab.getCurrentPanel()).hide();
    		$(".shouyi", navTab.getCurrentPanel()).hide();
	    }else if(obj.value == 0){//生存受益人
    		$(".survivalModeShow", navTab.getCurrentPanel()).show();
    		$(".shouyi", navTab.getCurrentPanel()).show();
    		//var survivalMode = $(obj).parents("tr").find("#survivalModetd",navTab.getCurrentPanel()).text();
    		//var survivalWMode = $(obj).parents("tr").find("#survivalWModetd",navTab.getCurrentPanel()).text();
    		//newChanglq(line);
    		newModeChange($("#survivalWModeBC",navTab.getCurrentPanel())[0])
    	}else{//身故受益人
    		$(".survivalModeShow", navTab.getCurrentPanel()).hide();
    		$(".shouyi", navTab.getCurrentPanel()).hide();
    	}
    	
    }
</script>