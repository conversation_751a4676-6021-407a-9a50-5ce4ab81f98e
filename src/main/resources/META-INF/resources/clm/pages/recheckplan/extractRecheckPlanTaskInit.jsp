<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="UTF-8"%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<%@ include file="/clm/pages/common/commonJsCss.jsp" %>
 <script type="text/javascript" src="clm/js/maintainrecheckplan.js"></script>

<script type="text/javascript">
$(function(){
	var obj=$("div#extractRecheckTaskDIV", navTab.getCurrentPanel());
    //输入框 复选框 单选按钮  控制
	obj.find("input").each(function(){
		if($(this).val() == "意外" || $(this).val() == "疾病"){
			$(this).attr("disabled",true);
		}
	});
	//下拉框
	/* obj.find("select").each(function(){
		$(this).attr("disabled",true);
	}); */
	//a标签
	/* obj.find("a").each(function(){
		$(this).attr("disabled",true);
	}); */
	$("#approveStartDate", navTab.getCurrentPanel()).attr("disabled",false);
	$("#approveEndDate", navTab.getCurrentPanel()).attr("disabled",false);
	$("#planId", navTab.getCurrentPanel()).attr("disabled",false);
	$("#autoAdd", navTab.getCurrentPanel()).attr("disabled",false);
	//$("#extractPlanName").attr("disabled",false);
});  

//查询抽取复勘任务
function queryExtractRecheckTask() {
	var planId = $("#rePlanNameId", navTab.getCurrentPanel()).val();
	var PlanName = $("#rePlanNameId", navTab.getCurrentPanel()).find("option:selected").text();
	if(planId == ""){
		alertMsg.error("复勘计划名称必选！");
		return false;
	}
	//$("#extractPlanName").val(planName);
	$.ajax({
		'url':'clm/maintainrecheckPlan/showExtractRecheckTask_CLM_maintainRecheckPlanAction.action?claimReSurveyPlanVO.planId='+planId,
		'type':'post',
		'datatype':'json',
		'success':function(data){
		    var data = eval("("+data+")");
		    var planId = data.planId;
		    $("#planId",navTab.getCurrentPanel()).val(planId);
		    $("#planName",navTab.getCurrentPanel()).val(PlanName);
		    $("#planType",navTab.getCurrentPanel()).selectMyComBox(data.planType);
		    $("#claimMoney",navTab.getCurrentPanel()).val(data.claimMoney);
		    $("#payeeNum",navTab.getCurrentPanel()).val(data.payeeNum);
		    $("#claimRate",navTab.getCurrentPanel()).val(data.claimRate);
		    //先清除
		    $(".paraValue", navTab.getCurrentPanel()).each(function(){
	    		$(this).removeAttr("checked");
		    });	
		    //清除理赔类型
    		$("input[name='claimType']", navTab.getCurrentPanel()).each(function(){
	    		$(this).removeAttr("checked");
		    });
		    //渠道类型、出险原因、保项结论
    		for ( var i = 0; i < data.claimReSurveyRuleVOs.length; i++) {
    			var paraName = data.claimReSurveyRuleVOs[i].paraName;
    			var paraType = data.claimReSurveyRuleVOs[i].paraType;
    			if(paraType == "01" || paraType == "02" || paraType == "03"){
	    			$(".paraValue", navTab.getCurrentPanel()).each(function(){
			   			if(this.value == paraName){
			   				$(this).attr("checked","checked");
			   				if(paraName == "疾病"){
			   					$(this).next().val("疾病");
			   				}
			   				if(paraName == "意外"){
			   					$(this).next().val("意外");
			   				}
						}
	    			});
    			}
    			if(paraType == "05"){
    				$("input[name='claimType']", navTab.getCurrentPanel()).each(function(){
        				if(this.value == paraName){
        				   $(this).attr("checked","checked");
        				}
        			}); 
    			}
    			if(paraType == "04"){
		    		$("#autoAdd", navTab.getCurrentPanel()).val(paraName); //查勘人回显
		    		$("#autoAddtext", navTab.getCurrentPanel()).val(paraName);
		    	}
    		}
		    //先清除理赔类型
    		$(".claimTypes", navTab.getCurrentPanel()).each(function(){
	    		$(this).removeAttr("checked");
	    		$(this).parent().parent().find("td").eq(2).find("input").val("");
		    });

		    //理赔类型
	    	 for ( var j = 0; j < data.claimReSurveyAccTypeVOs.length; j++) {
    			var claimType = data.claimReSurveyAccTypeVOs[j].claimType;
    			var accDays = data.claimReSurveyAccTypeVOs[j].accDays;
    			if(accDays != ""){
	    			$(".claimTypes", navTab.getCurrentPanel()).each(function(){
	    				if(this.value == claimType){
			   				$(this).attr("checked","checked");
			   				$(this).parent().parent().find("td").eq(2).find("input").val(accDays);
	    				}
	    			});
    			}
    		 }
		    var url = 'clm/maintainrecheckPlan/queryClaimReSurveyOrg_CLM_maintainRecheckPlanAction.action?claimReSurveyPlanVO.planId='+planId+'&isMaintain=extract';
			$("#claimReSurveyOrgIdExtract",navTab.getCurrentPanel()).loadUrl(url);
		}
	});
}

/*  $("#autoAdd",navTab.getCurrentPanel()).bigAutocomplete({
	'url':'clm/maintainrecheckPlan/getAutoAdd_CLM_maintainRecheckPlanAction.action'
}); */  
//弹出是否
/* function exitExtractRecheckTask(){
	 alertMsg.confirm("当前页面录入的信息将不被保存，是否确定退出？",{
	 	okCall:function(){
			navTab.closeCurrentTab();
	 	}
	 });
} */
function compareDateValue(obj){
	var startTime = $("#approveStartDate", navTab.getCurrentPanel()).val();
	var endTime =  $(obj).val();
	if(startTime > endTime){
		alertMsg.error("审批结束的开始日期不能大于结束日期！");
		return;
	}
}
//点击抽取按钮
/* function extractRecheckTaskResult() {
	 var url = "clm/maintainrecheckPlan/extractRecheckTaskResult_CLM_maintainRecheckPlanAction.action"
	 $("#extractId", navTab.getCurrentPanel()).attr("href",url);
}   */
</script>

<div class="divfclass">
	<h1><img src="clm/images/tubiao.png">抽取复勘查询条件</h1>
</div>
<div class="pageFormInfoContent">
	<dl>
		<dt>复勘计划名称</dt>
		<dd>
			<select class="combox title"  id="rePlanNameId" name="claimReSurveyPlanVO.planName">
				<option value="" class=" ">请选择</option> 
			 	<s:iterator value="claimReSurveyPlanVOs">
			 		<option value="<s:property value='planId'/>"><s:property value='planName'/></option>
			 	</s:iterator>
	   		</select>
		</dd>
	</dl>
	<div class="main_butarow"><button type="button"class="but_blue" onclick="queryExtractRecheckTask();">查询</button></div>		
</div>

	<div id="extractRecheckTaskDIV">
		<form method="post" action="" id="extractRecheckTaskId"  class="pageForm required-validate"
			onsubmit="return navTabSearch(this,'caseQueryTabs2');" novalidate="novalidate">
			<input type="hidden" id="planId" name="claimReSurveyPlanVO.planId" />
			<input type="hidden" id="planName" name="claimReSurveyPlanVO.planName" />
			<!-- <input type="hidden" id="extractPlanName" name="claimReSurveyPlanVO.planName" /> -->
			<div class="divfclass" id="reSurveyPlanId">
				<h1><img src="clm/images/tubiao.png">抽取复勘计划</h1>
			</div>
				<div class="panelPageFormContent">
					<dl style="width: 100%;">
						<dt>审批结束起止日期</dt>
						<dd >
							<input id="approveStart0Date" type="expandDateYMD" class="date" name="claimCaseVO.documentSDate" /> <a class="inputDateButton" href="javascript:;">选择</a>
							<span style="padding-left:5px;">至</span>
						</dd>
						<dd>	
							<input id="approveEndDate" type="expandDateYMD" class="date" name="claimCaseVO.documentEDate" /> <a class="inputDateButton" href="javascript:;">选择</a>
						</dd>
					</dl>
					<dl>
						<dt>复勘类型</dt>
						<dd >
						   <Field:codeTable cssClass="combox title" name="claimReSurveyPlanVO.planType"  tableName="APP___CLM__DBUSER.T_RESURVEY_TYPE" disabled="true" nullOption="true" id="planType"/>  
						</dd>
					</dl>
					<dl>
						<dt style="width: 165px;">渠道类型</dt>
						<dd  style="width:300px;" disabled="true">
							<span>
								<input type="checkbox" name="sourceType" value="个人" class="paraValue" style="border:0px;background:0px;width:auto;"/>个人
							</span>
							<span>
								<input type="checkbox" name="sourceType" value="财富" class="paraValue" style="border:0px;background:0px;width:auto;"/>财富 
							</span>
							<span>
								<input type="checkbox" name="sourceType" value="银贷" class="paraValue" style="border:0px;background:0px;width:auto;"/>银贷 
							</span>
							<span>
								<input type="checkbox" name="sourceType" value="网销" class="paraValue" style="border:0px;background:0px;width:auto;"/>网销 
							</span>
							<span>
								<input type="checkbox" name="sourceType" value="团体" class="paraValue" style="border:0px;background:0px;width:auto;"/>团体
							</span>
						</dd>
					</dl>
					<dl>
						<dt>领款人数量  > = </dt>
						<dd >
						   <input type="text" readonly="readonly" name="claimReSurveyPlanVO.payeeNum" id="payeeNum"/>&nbsp;&nbsp;人
						</dd>
					</dl>
					<dl>
						<dt style="width: 165px;">出险原因</dt>
						<dd>
							<span>
						  		<input type="checkbox" name="clmReason" value="意外" class="paraValue" style="border:0px;background:0px;width:auto;"/> 意外
						  		<input type="hidden" name="clmReason" value=""/>
						  	</span>
						  	<span>
							  	<input type="checkbox" name="clmReason" value="疾病" class="paraValue" style="border:0px;background:0px;width:auto;"/> 疾病
							  	<input type="hidden" name="clmReason" value=""/>
						  	</span>
						</dd>
					</dl>
					<dl>
						<dt style="width: 160px;">赔付金额与已缴纳的保费比>=</dt>
						<dd>
						   <input type="text" readonly="readonly" name="claimReSurveyPlanVO.claimRate" id="claimRate"/>&nbsp;&nbsp;倍
						</dd>
					</dl>
					<dl>
						<dt>理赔金额 > = </dt>
						<dd >
						   <input type="text" readonly="readonly" name="claimReSurveyPlanVO.claimMoney" id="claimMoney"/>&nbsp;&nbsp;元
						</dd>
					</dl>
					<dl>
						<dt style="width: 165px;">保项结论</dt>
						<dd style="width:300px;" disabled="true">
							<span>
								<input type="checkbox" name="clmConclusion" value="完全给付" class="paraValue" style="border:0px;background:0px;width:auto;"/> 完全给付
							</span>
							<span>
								<input type="checkbox" name="clmConclusion" value="部分给付" class="paraValue" style="border:0px;background:0px;width:auto;"/> 部分给付
							</span>
							<span>
								 <input type="checkbox" name="clmConclusion" value="拒付" class="paraValue" style="border:0px;background:0px;width:auto;"/>拒付
							</span>
						</dd>
					</dl>
				</div>
			

			<div class="divfclass" id="claimTypeCheckedId">
				<h1><img src="clm/images/tubiao.png">保单生效日距出险日天数</h1>
			</div>
				<div class="tabdivclassbr">
				<table class="list" width="100%" style="height:20%;">
					<thead>
						<tr>
							<th nowrap>序号</th>
							<th nowrap>理赔类型</th>
							<th nowrap>保单生效日距出险日天数（< =）</th>
						</tr>
					</thead>
					<tbody id="claimReSurveyAccTypeTbody">
						<tr>
							<td align="center">1</td>
							<td> <input type="checkbox" disabled="true" name="claimTypeChecked" id="身故"
									onclick="aftercheck()" value="01" class="claimTypes"/>身故 &nbsp; &nbsp; &nbsp; &nbsp;</td>
							<td><input id="day1" readonly="readonly" type="text" name="accDays" class="digits" maxlength="4"/></td>
						</tr>
						<tr>
							<td align="center">2</td>
							<td> <input type="checkbox" disabled="true" name="claimTypeChecked" id="高残"
									onclick="aftercheck()" value="04" class="claimTypes"/>高残  &nbsp; &nbsp; &nbsp; &nbsp;</td>
							<td><input id="day2" readonly="readonly" type="text" name="accDays" class="digits" maxlength="4"/></td>
						</tr>
						<tr>
							<td align="center">3</td>
							<td> <input type="checkbox" disabled="true" name="claimTypeChecked"
									id="重大疾病" onclick="aftercheck()" value="03" class="claimTypes"/>重大疾病 </td>
							<td><input type="text" readonly="readonly" id="day3" name="accDays" class="digits" maxlength="4"/></td>
						</tr>
						<tr>
							<td align="center">4</td>
							<td> <input type="checkbox" disabled="true" name="claimTypeChecked"
									id="伤残" onclick="aftercheck()" value="02" class="claimTypes"/>伤残  &nbsp; &nbsp; &nbsp; &nbsp;</td>
							<td><input type="text" readonly="readonly" id="day4" name="accDays" class="digits" maxlength="4"/></td>
						</tr>
						<tr>
							<td align="center">5</td>
							<td> <input type="checkbox" disabled="true" name="claimTypeChecked"
									id="豁免" onclick="aftercheck()" value="11" class="claimTypes"/>豁免 &nbsp; &nbsp; &nbsp; &nbsp; </td>
							<td><input type="text" readonly="readonly" id="day5" name="accDays" class="digits" maxlength="4"/></td>
						</tr>
						<tr>
							<td align="center">6</td>
							<td> <input type="checkbox" disabled="true" name="claimTypeChecked"
									id="医疗" onclick="aftercheck()" value="08" class="claimTypes"/>医疗 &nbsp; &nbsp; &nbsp; &nbsp; </td>
							<td><input type="text" readonly="readonly" id="day6" name="accDays" class="digits" maxlength="4"/></td>
						</tr>
						<tr>
							<td align="center">7</td>
							<td> <input type="checkbox" disabled="true" name="claimTypeChecked"
									id="特种疾病" onclick="aftercheck()" value="10" class="claimTypes"/>特种疾病 </td>
							<td><input type="text" readonly="readonly" id="day7" name="accDays" class="digits" maxlength="4"/></td>
						</tr>
						<tr>
							<td align="center">8</td>
							<td> <input type="checkbox" disabled="true" name="claimTypeChecked"
									id="一般失能" onclick="aftercheck()" value="06" class="claimTypes"/>一般失能 </td>
							<td><input type="text" readonly="readonly" id="day8" name="accDays" class="digits" maxlength="4"/></td>
						</tr>
						<tr>
							<td align="center">9</td>
							<td> <input type="checkbox" disabled="true" name="claimTypeChecked"
									id="重度失能" onclick="aftercheck()" value="07" class="claimTypes"/>重度失能 </td>
							<td><input type="text" readonly="readonly" id="day9" name="accDays" class="digits" maxlength="4"/></td>
						</tr>				
					</tbody>
				</table>
				</div>
			

			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">理赔类型</h1>
			</div>
				<div class="pageFormContent">
					<dl>
						<dt></dt>
						<dd style="width:800px;padding-left:35px;">
							<span>
								<input type="checkbox" disabled="true" name="claimType" value="01" style="border:0px;background:0px;width:auto;" />身故
							</span>
							<span>
								<input type="checkbox" disabled="true" name="claimType" value="04" style="border:0px;background:0px;width:auto;" />高残
							</span>
							<span>
								<input type="checkbox" disabled="true" name="claimType" value="03" style="border:0px;background:0px;width:auto;" />重大疾病
							</span>
							<span>
								<input type="checkbox" disabled="true" name="claimType" value="02" style="border:0px;background:0px;width:auto;" />伤残
							</span>
							<span>
								<input type="checkbox" disabled="true" name="claimType" value="11" style="border:0px;background:0px;width:auto;" />豁免
							</span>
							<span>
								<input type="checkbox" disabled="true" name="claimType" value="08" style="border:0px;background:0px;width:auto;" />医疗
							</span>
							<span>
								<input type="checkbox" disabled="true" name="claimType" value="10" style="border:0px;background:0px;width:auto;" />特种疾病
							</span>
							<span>
								<input type="checkbox" disabled="true" name="claimType" value="06" style="border:0px;background:0px;width:auto;" />一般失能
							</span>
							<span>
								<input type="checkbox" disabled="true" name="claimType" value="07" style="border:0px;background:0px;width:auto;" />重度失能
							</span>
						</dd>
					</dl>
				</div>
			
	   <div id="claimReSurveyOrgIdExtract">
			<div class="divfclass" id="claimReSurveyOrgId">
				<h1><img src="clm/images/tubiao.png">管理机构</h1>
			</div>
			<!--数据编号        必录  class="required" 浮点数 class="number" 整数  class="digits" -->
			<div class="tabdivclassbr" >
					<table class="list nowrap" style="width:100%;">
							<thead>
								<tr>
									<th nowrap type="text" name="items[#index#].itemInt" readonly defaultVal="#index#" size="9" fieldClass="digits">序号</th>
									<th nowrap type="enum" enumUrl="clm/pages/html/maintainRecheckPlanManageOrg.jsp" size="12">管理机构</th>
									<th nowrap type="enum" enumUrl="clm/pages/html/maintainRecheckPlanRate.jsp" size="12">抽取比例（%）</th>
									<th nowrap type="enum" enumUrl="clm/pages/html/maintainRecheckPlanLimit.jsp" size="12">抽取限额</th>
								</tr>
							</thead>
							<tbody class="list" id="manageOrgId">
								<tr>
									<td>
										<input  name="items[0].itemInt" class="digits textInput focus" type="text" size="9"  value="1" readonly="readonly"/>
									</td>
									<td>
										<Field:codeTable   disabled="true" cssClass="combox title" name="claimReSurveyOrgVOs[0].organCode" tableName="APP___CLM__DBUSER.T_UDMP_ORG" nullOption="true" whereClause="LENGTH(ORGAN_CODE)=6 or LENGTH(ORGAN_CODE)=4"/>
								    </td>
									<td>
										<input type="text" readonly="readonly" value="100" onblur="checkextractrate(this.value)" class="number" max="100"/>
										<input type="hidden" value="1" name="claimReSurveyOrgVOs[0].rate"  class="number"/>
									</td>
									<td>	
									    <input type="text" readonly="readonly" name="claimReSurveyOrgVOs[0].limit" onblur="checkextractamout(this.value)" class="digits" max="1000000000000000" min="1"/>
									</td>
								</tr>
							</tbody>
						</table>
				</div>
			</div>
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">按查勘人抽取</h1>
			</div>
			<div class="panelPageFormContent">
				<dl style="width: 100%">
					<dt>用户名</dt>
					<dd>
						<input type="text" id="autoAdd" readonly="readonly" style="width: 90px;border-right:0px"/>
						<input type="text" id="autoAddtext" name="keyword" readonly style="width:90px;"/>
					</dd>
				</dl>
			</div>

			<!-- 按钮区域 -->
		<div class="formBarButton">
			<ul>
				<li>
						<a href="javaScript:void(0)" class="but_blue main_buta" onclick="extractRecheckTaskResult('2');">抽取</a>	
				</li>
				<li>
						<button type="button" class="but_gray" onclick="exit()">关闭</button>   
				</li>
			</ul>
		</div>
		</form>
	</div>
