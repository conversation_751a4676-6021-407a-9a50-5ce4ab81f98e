<%@ page language="java" pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
 
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script src="${ctx}/udmp/plugins/ribbon/jquery.orgtree.js" type="text/javascript"></script>
<script type="text/javascript" src="${ctx}/clm/pages/taskmanage/clmWorkPlatform.js" ></script>

<div style="background: #fafafa" id="AllotDiscussionPersonalTask">
		<div class="divfclass">
		<h1>
			<img src="clm/images/tubiao.png">分配合议任务
		</h1>
	</div>
		<div class="tabdivclassbr">
			<table class="list" width="100%" id="">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>赔案号</th>
						<th nowrap>出险人姓名</th>
						<th nowrap>证件号码</th>
						<th nowrap>绩优等级</th>
						<th nowrap>总时效</th>
						<th nowrap>合议发起时间</th>
						<th nowrap>期望回复时间</th>
					</tr>
				</thead>
				
				<tbody id="" align="center">
				
					<s:iterator value="queryAllotPanelDiscussionTaskMessages" status="st">
						<tr ondblclick="allotDiscussionPersonalTaskForWard(${caseId},${discussId},'${taskId}')">
							<td>
								<div align="center" class="index">${st.index+1}
							</div>
							<td>
								<div align="center">${caseNo}</div>
							</td>
							<td>
								<div align="center">${customerName}</div>
							</td>
							<td>
								<div align="center">${customerCertiCode}</div>
							</td>
							<td>
								<div align="center">${greenFlag}</div>
							</td>
							<td>
								<div align="center">${sumTime}</div>
							</td>
							<td>
								<div align="center">${discussionStartTime}</div>
							</td>
							<td>
								<div align="center">${expectReturnTime}</div>
							</td>
						</tr>
					</s:iterator>
					
				</tbody>
			</table>
		</div>
</div>