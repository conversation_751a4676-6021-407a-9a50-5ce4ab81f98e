<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/JavaScript">
	function query() {
		var TimeStart =$("#TimeStart",navTab.getCurrentPanel()).val().trim();
		var TimeEnd=$("#TimeEnd",navTab.getCurrentPanel()).val().trim();
		var currentOrganCode = $("input#operatorOrganMana1",
				navTab.getCurrentPanel()).val();
	
		if (TimeStart > TimeEnd) {
			alertMsg.warn('预警起期不能大于预警止期！');
			return false;
		}
		$("#queryRiskWarningForm").submit();
	}

	function exportExcle(pageForm, pageAction) {

		var formAction = $("#" + pageForm, navTab.getCurrentPanel()).attr(
				"action");
		var formSubmit = $("#" + pageForm, navTab.getCurrentPanel()).attr(
				"onsubmit");
		$("#" + pageForm, navTab.getCurrentPanel()).attr("action", pageAction);
		$("#" + pageForm, navTab.getCurrentPanel()).attr("onsubmit", "");
		$("#" + pageForm, navTab.getCurrentPanel()).submit();

		//导出加载
		$
				.ajax({
					type : 'post',
					url : 'clm/drawInspectTask/exportWarnList1_CLM_claimRiskWarnQueryAction.action',
					success : function(data) {
						$("#background").hide();
						$("#progressBar").hide();
					}
				});
		$("#" + pageForm, navTab.getCurrentPanel())
				.attr("onsubmit", formSubmit);
		$("#" + pageForm, navTab.getCurrentPanel()).attr("action", formAction);
	}
</script>

<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>风控预警明细及导出</title>
</head>
<body>

	<form id="queryRiskWarningForm"
		action="clm/drawInspectTask/queryWarnList_CLM_claimRiskWarnQueryAction.action"
		onsubmit="return navTabSearch(this,'claimRiskWarningList')"
		rel="pagerForm" method="post" class="pagerForm required-validate"
		name=fm>
		<div class="panelPageFormContent">
			<div class="divfclass">
				<h1>
					<img src="clm/images/tubiao.png">预警风控要点
				</h1>
			</div>

			<div class="pageFormInfoContent" id="pageFormContentId">
				<dl>
					<dt><font class="point" style="color: red">*</font>机构</dt>
					<dd>
						<input style="width: 30px;border-right:0px" type="text" size="2" name="claimRiskLibraryVO.organCode"
								id="menuBtn" value="${claimRiskLibraryVO.organCode}" class="organ" clickId="epiManageBtn"
								showOrgName="branchname" data-grade-in="02" /> 
						<input style="width:110px;" type="text" size="11" 
								id="branchname" value="<Field:codeValue value="${claimRiskLibraryVO.organCode}" tableName="APP___CLM__DBUSER.T_UDMP_ORG" />" readOnly class="" />
						<a id="epiManageBtn" class="btnLook" href="#"></a>	
					</dd>
				</dl>
				<dl>
					<dt>风险阈值类型</dt>
					<dd>
							<Field:codeTable cssClass="combox title"
								name="claimRiskLibraryVO.thresholdType" nullOption="true"
									tableName="APP___CLM__DBUSER.T_THRESHOLD_TYPE"
									value="${claimRiskLibraryVO.thresholdType}" />
					</dd>
				</dl>
				<dl>
					<dt>定制层级</dt>
					<dd>
						<Field:codeTable cssClass="combox title"
							name="claimRiskLibraryVO.libMakeLevel" nullOption="true"
							tableName="APP___CLM__DBUSER.T_LIB_MAKE_LEVEL" 
							value="${claimRiskLibraryVO.libMakeLevel}"/>
					</dd>
				</dl>
				<dl>
					<dt>预警起期</dt>
					<dd>
						<input type="expandDateYMD" flag="flag" class="date"
							id ="TimeStart"
							name="claimRiskLibraryVO.libMakeTimeStart"
							value="<s:date name='claimRiskLibraryVO.libMakeTimeStart' format='yyyy-MM-dd'/>"
							size="22" id="ccDateIdId" vertical-align:middle /> <a
							class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl>
					<dt>预警止期</dt>
					<dd>
						<input type="expandDateYMD" flag="flag" class="date"
							id ="TimeEnd"
							name="claimRiskLibraryVO.libMakeTimeEnd"
							value="<s:date name='claimRiskLibraryVO.libMakeTimeEnd' format='yyyy-MM-dd'/>"
							size="22" id="ccDateIdId" vertical-align:middle /> <a
							class="inputDateButton" href="javascript:;">选择</a>
					</dd>
				</dl>
				<dl>
					<dt>风险编号</dt>
					<dd>
						<input id="riskLibType" type="text"
							name="claimRiskLibraryVO.riskLibCode"
							value="${claimRiskLibraryVO.riskLibCode}" size="25"
							vertical-align:middle />
					</dd>
				</dl>
				<div class="pageFormdiv">
					<button class="but_blue" type="button" onclick="query();">查询</button>
				</div>
			</div>
		</div>
	</form>

	<div id="claimRiskWarningList">
		<div class="divfclass">
			<h1>
				<img src="clm/images/tubiao.png">预警风控要点统计
			</h1>
		</div>
		<div class="tabdivclassbr">
			<table class="list main_dbottom" width="100%">
				<thead>
					<tr>
						<th nowrap>序号</th>
						<th nowrap>风险编号</th>
						<th nowrap>风控要点</th>
						<th nowrap>检查要求</th>
						<th nowrap>定制层级</th>
						<th nowrap>定制机构</th>
						<th nowrap>风险阈值类型</th>
						<th nowrap>风险阈值</th>
						<th nowrap>机构代码</th>
						<th nowrap>预警数量</th>
					</tr>
				</thead>
				<tbody align="center">
					<tr>
						<td colspan="10">
							<div class="noRueryResult">请选择条件查询数据！</div>
						</td>
					</tr>
				</tbody>
			</table>
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{10:'10',20:'20',50:'50',100:'100',200:'200'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value})"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab" 
					totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10"
					currentPage="${currentPage.pageNo}"></div>
			</div>
			<div class="formBarButton">
				<button type="button" class="but_blue"
					onclick="exportExcle('queryRiskWarningForm','clm/drawInspectTask/exportWarnList_CLM_claimRiskWarnQueryAction.action')">
					预警明细下载</button>
			</div>
		</div>

	</div>

</body>
</html>