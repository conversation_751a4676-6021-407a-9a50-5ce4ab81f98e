<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include file="/clm/pages/common/commonJsCss.jsp" %>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>

<script type="text/javascript">

//非86机构用户登录默认为所属二级机构，不可修改。 86机构用户登录可选择
var organCode = $("#searOrganCode",navTab.getCurrentPanel()).val();
var currentOrganCode =$("#currentOrganCode",navTab.getCurrentPanel()).val();
var currentOrganName =$("#currentOrganName",navTab.getCurrentPanel()).val();
if("86" != currentOrganCode){
	$("#searOrganCode",navTab.getCurrentPanel()).attr("readonly","readonly");
	$("#searOrganName",navTab.getCurrentPanel()).attr("readonly","readonly");
	$("#menuBtn01",navTab.getCurrentPanel()).attr("disabled","disabled");
	$("#searOrganCode",navTab.getCurrentPanel()).val(currentOrganCode);
	$("#searOrganName",navTab.getCurrentPanel()).val(currentOrganName);
}

function checkSubmit() {
	var timeStart = $("#startDate", navTab.getCurrentPanel()).val();
	var timeEnd = $("#endDate", navTab.getCurrentPanel()).val();
	if(timeStart !=null && timeStart !="" && timeEnd !=null && timeEnd !=""){
		if(new Date(timeStart.replace("-","/")) > new Date(timeEnd.replace("-","/"))){
			alertMsg.error("检查起期晚于检查止期,请重新输入。");
			$("#startDate", navTab.getCurrentPanel()).val("");
			$("#endDate", navTab.getCurrentPanel()).val("");
		    return false;
		}
	}
	$("#inspectTaskPoolJspForm", navTab.getCurrentPanel()).submit();
}

function getInspectResult(taskCode,obj) {
	var caseNo = $(obj).children().eq(2).find("input[name='caseNo']").val();
	var inspectWay = $(obj).children().eq(8).find("input[name='inspectWay']").val();
	//校验该任务是否满足分配规则
	$.ajax({
		'url':"clm/drawInspectTask/checkTaskAsignRule_CLM_claimInspectTaskPoolAction.action",
		'type':'post',
		'data':{'taskCode':taskCode, 'caseNo':caseNo, 'claimInspectTaskPoolResClientVO.inspectWay':inspectWay},
		'success':function (data){
			var res = eval("(" + data + ")");
			var flag = res.flag;
			var msg = res.message;
			if(flag){
				//将选中的数据从共享池中移除
				$(obj).remove();
				var url = "clm/drawInspectTask/applyTask_CLM_claimInspectTaskPoolAction.action?taskCode="+taskCode+"&caseNo="+caseNo+"&claimInspectTaskPoolResClientVO.inspectWay="+inspectWay;
				$("#inputInspectResult", navTab.getCurrentPanel()).attr("href", url).click();
			}else{
				alertMsg.error(msg);
			}
		}
	});
}

</script>
<div layoutH="10">
	<!-- 查询事件访问路径 -->
	<form id="inspectTaskPoolJspForm"
		action="clm/drawInspectTask/queryTask_CLM_claimInspectTaskPoolAction.action" method="post" 
		onsubmit="return navTabSearch(this,'inspectTaskPoolResult')"
		class="pagerForm required-validate" rel="inspectTaskPoolResult">
		<input type="hidden" id="currentOrganCode" value="${currentOrganCode }" />
		<input type="hidden" id="currentOrganName" value="${currentOrganName }" />
	<!-- 查询区域 -->
		<div class="panelPageFormContent" >
			<div class="divfclass">
				<h1><img src="clm/images/tubiao.png">查询条件</h1>
			</div>
			<div class="pageFormInfoContent" >
				<dl>	
					<dt>机构</dt>
					<dd>
						<input id="searOrganCode" name="requestVO.organCode"
							type="text" class="required organ" clickId="menuBtn01"
							showOrgName="searOrganName" needAll="true" size="8"
							style="width: 30px; border-right: 0;"
							value="${requestVO.organCode}" />
						<input
							id="searOrganName" type="text"  size="15"
							name="requestVO.organName" style="width: 110px"
							value="<s:property value='requestVO.organName'/>" />
						<a id="menuBtn01" class="btnLook" href="#"></a>
					</dd> 
				</dl>
				<dl>	
					<dt>任务编号</dt>
					<dd>
						<input type="text" id="taskCode" name="requestVO.taskCode" value="${requestVO.taskCode}"/>
					</dd> 
				</dl>
				<dl>	
					<dt>赔案号</dt>
					<dd>
						<input type="text" id="caseNo" name="requestVO.caseNo" value="${requestVO.caseNo}"/>
					</dd> 
				</dl>
				<dl>	
					<dt>检查批次号</dt>
					<dd>
						<input type="text" id="inspectProCode" name="requestVO.inspectProCode" value="${requestVO.inspectProCode}"/>
					</dd> 
				</dl>
				<dl>
					<dt>检查起止期</dt>
					<dd>
						<input type="expandDateYMD" style="width: 70px;" name="requestVO.startDate" id="startDate" value="<s:date name="requestVO.startDate" format="yyyy-MM-dd"/>"/>
						<input type="expandDateYMD" style="width: 70px;" name="requestVO.endDate" id="endDate" value="<s:date name="requestVO.endDate" format="yyyy-MM-dd"/>"/>
					</dd>
				</dl>
				<dl>
					<dt>风控类型</dt>
					<dd>
						<select class="combox title" name="requestVO.riskLibType">
							<option value="">请选择 </option>
							<s:iterator value="riskLibTypeVOList" status="st">
								<option value="${typeId}">${libType} </option>
							</s:iterator>
						</select>
					</dd>
				</dl>
				<div class="pageFormdiv">
					<button class="but_blue" type="button" onclick="checkSubmit();">查询</button>
				</div>
			</div>
    	</div>
   	</form>
	
	<!-- 显示数据列表区域 -->	
	<div>
		<div class="divfclass">
			<h1><img src="clm/images/tubiao.png">共享池</h1>
		</div>
		<div  class="tabdivclassbr" id="inspectTaskPoolResult">
			<table class="list" width="100%">
				<thead>
					<tr align="center">
						<th nowrap>机构</th>
						<th nowrap>任务编号</th>
						<th nowrap>赔案号</th>
						<th nowrap>检查批次号</th>
						<th nowrap>检查单位</th>
						<th nowrap>检查大类</th>
						<th nowrap>检查小类</th>
						<th nowrap>检查方式</th>
						<th nowrap>抽取时间</th>
					</tr>
				</thead>
				<tbody>
					<tr align="center">
						<s:if test="imageFlag != null">
							<tr>
								<td colspan="10">
									<div class="noRueryResult">请选择条件查询数据！</div>
								</td>
							</tr>
				    	</s:if>
					</tr>
				</tbody>
			</table>
			<!-- 分页查询区域 -->
			<div class="panelBar">
				<div class="pages">
					<span>显示</span>
					<s:select list="#{3:'3',5:'5',10:'10',20:'20',50:'50'}"
						name="select" onchange="navTabPageBreak({numPerPage:this.value},'inspectTaskPoolPage')"
						value="currentPage.pageSize">
					</s:select>
					<span>条，共${currentPage.total}条</span>
				</div>
				<div class="pagination" targetType="navTab" rel="inspectTaskPoolResult"
					totalCount="${currentPage.total}"
					numPerPage="${currentPage.pageSize}" pageNumShown="10"
					currentPage="${currentPage.pageNo}"></div>
			</div>
		</div>
	</div>
	
	<!-- 个人池显示数据列表区域 -->
	<!-- 个人池分页查询访问路径 -->
	<div id="inspectTaskPoolJsp">
 		<%@ include file="inspectTaskPoolSelf.jsp"%>
	</div>
	
	<div class="formBarButton" >
		<ul>  
			<li>
				<button type="button" class="but_gray close">返回</button>
			</li>
		</ul>
	</div>
</div>
