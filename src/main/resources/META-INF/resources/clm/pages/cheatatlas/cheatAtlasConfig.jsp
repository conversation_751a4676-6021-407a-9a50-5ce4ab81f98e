<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="UTF-8"%><%@ include
	file="/clm/pages/common/commonJsCss.jsp"%>
<%@ taglib uri="/struts-tags" prefix="s"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<script type="text/javascript" language="javascript"
	src="clm/js/commonMianBox.js">
	
</script>
<style>
.panelPageFormContentCopy dt {
	width: fit-content !important;
}
</style>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<script type="text/javascript" src="${ctx}/clm/js/sorttable.js"></script>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<script type="text/javascript">
	//点击配置查询按钮
	function selectSurcey() {
		var dangerType = $("#dangerType", navTab.getCurrentPanel()).val();
		var startTime = $("#startTime", navTab.getCurrentPanel()).val();
		var endTime = $("#endTime", navTab.getCurrentPanel()).val();
		if (dangerType == '' && startTime == '' && endTime == '') {
			alertMsg.error("请至少录入风险类型，查询起期，查询止期的一个查询条件!");
			return false;
		}
		$("#selectSurcey", navTab.getCurrentPanel()).submit();
	};
	//新增按钮
	function insert() {
		$("#saveCheatAtlas").show();
		$("#saveCheatAtlas").find(".main_foldContent").slideDown();
		if ($("#main_3", navTab.getCurrentPanel()).find("#two",
				navTab.getCurrentPanel()).hasClass("main_plus")) {
			$("#main_3", navTab.getCurrentPanel()).find("h5",
					navTab.getCurrentPanel()).click();
		}
		$("#saveRule", navTab.getCurrentPanel()).removeAttr("disabled", false);
		cleanSave();
	}
	//保存按钮
	function saveSet() {
		if ($("#dangerTypeNO", navTab.getCurrentPanel()).val() == ""
				|| $("#dangerTypeNO", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("风险类型不能为空");
			return;
		}
		if ($("#ruleCodeNO", navTab.getCurrentPanel()).val() == ""
				|| $("#ruleCodeNO", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("规则编码不能为空");
			return;
		}
		if ($("#ruleNameNO", navTab.getCurrentPanel()).val() == ""
				|| $("#ruleNameNO", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("规则名称不能为空");
			return;
		}
		if ($("#contentNO", navTab.getCurrentPanel()).val() == ""
				|| $("#contentNO", navTab.getCurrentPanel()).val() == null) {
			alertMsg.info("提示内容不能为空");
			return;
		}
		$("#saveSurvey", navTab.getCurrentPanel()).submit();
		$("#saveCheatAtlas", navTab.getCurrentPanel()).hide();
		$("#selectSurcey", navTab.getCurrentPanel()).submit();
	}
	//选择按钮

	function choseCheatAtlas(arg) {
		$("#saveCheatAtlas", navTab.getCurrentPanel()).show();
		var selectedValue = $(arg).val().split("|");
		var listId = selectedValue[0];
		$
				.ajax({
					url : "clm/parameter/findCheatAtlasConfigAndLog_CLM_cheatAtlasConfigAction.action?cheatAtlasConfigVO.listId="
							+ listId,
					type : "POST",
					dataType : "json",
					async : false,
					success : function(cheatAtlasConfigVOList) {
						if (cheatAtlasConfigVOList.length != 0) {
							var insertHtml = "";
							var annuityPayCount = 0;
							var ruleName = $("#ruleNameNO",
									navTab.getCurrentPanel()).val(
									cheatAtlasConfigVOList[0].ruleName);
							var content = $("#contentNO",
									navTab.getCurrentPanel()).val(
									cheatAtlasConfigVOList[0].content);
							var ruleCode = $("#ruleCodeNO",
									navTab.getCurrentPanel()).val(
									cheatAtlasConfigVOList[0].ruleCode);
							var dangerType = $("#dangerTypeNO",
									navTab.getCurrentPanel()).selectMyComBox(
									cheatAtlasConfigVOList[0].dangerType);
							var logId = $("#logId", navTab.getCurrentPanel())
									.val(cheatAtlasConfigVOList[0].listId);
							var cheatAtlasConfiglistId = $(
									"#cheatAtlasConfiglistId",
									navTab.getCurrentPanel()).val(
									cheatAtlasConfigVOList[0].listId);

						}
					}
				});
		$("#saveRule", navTab.getCurrentPanel()).removeAttr("disabled", false);
		//轨迹查询
		$("#cheatAtalsConfigVOList", navTab.getCurrentPanel()).submit();

	}
</script>
<div layouth="36" id="saveCheatAtlasId">
	<!-- 查询访问路径 -->
	<form id="pagerFormSurcey" method="post"
		action="clm/parameter/selectSurcey_CLM_cheatAtlasConfigAction.action">
		<input type="hidden" name="pageNum" vaule="${currentPage1.pageNo} " />
		<input type="hidden" name="numPerPage"
			value="${currentPage1.pageSize}" />
	</form>
	<div id="main_2" class="main_borderbg">
		<ul class="main_ul">
			<li class="clearfix">
				<h5 hasborder="true">
					<b id="two" class="main_minus"></b><span>关联图谱实时规则提示配置</span>
				</h5>
				<div class="main_foldContent">
					<div class="main_bqtabdivbr">
						<div class="panelPageFormContent">
							<div class="pageFormInfoContent">
								<form id="selectSurcey"
									action="clm/parameter/selectSurcey_CLM_cheatAtlasConfigAction.action?leftFlag=0&menuId=${menuId} "
									onsubmit="return navTabSearch(this,'cheatAtlasSurcey')"
									rel="pagerFormSurcey" method="post"
									class="pagerForm required-validate">
									<dl style="width: 32%">
										<dt style="width: 32%">风险类型</dt>
										<dd style="width: 60%">
											<Field:codeTable cssClass="combox title"
												name="cheatAtlasConfigVO.dangerType" id="dangerType"
												value="${cheatAtlasConfigVO.dangerType}" nullOption="true"
												tableName="APP___CLM__DBUSER.T_DANGER_TYPE" />
										</dd>
									</dl>
									<dl style="width: 32%">
										<dt style="width: 32%">查询起期</dt>
										<dd style="width: 60%">
											<input id="startTime" type="expandDateYMD"
												name="cheatAtlasConfigVO.startTime"
												value="<s:date name='cheatAtlasConfigVO.startTime' format='yyyy-MM-dd'/>" />
											<a class="inputDateButton" href="javascript:;">选择</a>
										</dd>
									</dl>
									<dl style="width: 32%">
										<dt style="width: 32%">查询止期</dt>
										<dd style="width: 60%">
											<input id="endTime" type="expandDateYMD"
												name="cheatAtlasConfigVO.endTime"
												value="<s:date name='cheatAtlasConfigVO.endTime' format='yyyy-MM-dd'/>" />
											<a class="inputDateButton" href="javascript:;">选择</a>
										</dd>
									</dl>
								</form>
								<div class="pageFormdiv">
									<button type="button" class="but_blue"
										onclick="selectSurcey();">查询</button>
								</div>
								<div id="cheatAtlasSurcey">
									<form id="pagerForm" method="post"
										action="clm/parameter/selectSurcey_CLM_cheatAtlasConfigAction.action?leftFlag=0&menuId=${menuId}">
										<input type="hidden" name="pageNum"
											vaule="${currentPage1.pageNo} " /> <input type="hidden"
											name="numPerPage" value="${currentPage1.pageSize}" />
										<!-- 查询条件回调 -->
										<input type="hidden" name="cheatAtlasConfigVO.dangerType"
											value="${cheatAtlasConfigVO.dangerType}" /> <input
											type="hidden" name="cheatAtlasConfigVO.startTime"
											value="${cheatAtlasConfigVO.startTime}" /> <input
											type="hidden" name="cheatAtlasConfigVO.endTime"
											value="${cheatAtlasConfigVO.endTime}" />
									</form>
									<div>
										<table class="list" id="surveyApplyOperTable" width="100%">
											<thead>
												<tr>
													<th nowrap>选择</th>
													<th nowrap>风险类型</th>
													<th nowrap>规则编码</th>
													<th nowrap>规则名称</th>
													<th nowrap>提示内容</th>
													<th nowrap>提交人</th>
													<th nowrap>提交日期</th>
												</tr>
											</thead>
											<tbody>
												<s:if
													test="currentPage1.pageItems == null || currentPage1.pageItems.size()==0">
													<tr>
														<td colspan="100">
															<div class="noRueryResult">没有符合条件的查询结果！</div>
														</td>
													</tr>
												</s:if>
												<s:iterator value="currentPage1.pageItems" status="st">
													<tr align="center" target="listIdInfo">
														<td><input type="radio" class="radioIndex" name="r1"
															onclick='choseCheatAtlas(this);'
															value="${listId}|${dangerName}|${ruleCode}|${ruleName}|${content}|${realName}|${subTime}" /></td>
														<td align="center" style="word-break: break-all;">${dangerName}</td>
														<td align="center" style="word-break: break-all;">${ruleCode}</td>
														<td align="center" style="word-break: break-all;">${ruleName}</td>
														<td align="center" style="word-break: break-all;">${content}</td>
														<td align="center" style="word-break: break-all;">${realName}</td>
														<td><s:date name='subTime' format='yyyy-MM-dd' /></td>

													</tr>
												</s:iterator>
											</tbody>
										</table>
										<div class="panelBar">
											<div class="pages">
												<span>显示</span>
												<s:select list="#{5:'5',10:'10',20:'20',50:'50'}"
													name="select"
													onchange="navTabPageBreak({numPerPage:this.value},'cheatAtlasSurcey')"
													value="currentPage1.pageSize">
												</s:select>
												<span>条，共${currentPage1.total}条</span>
											</div>
											<div class="pagination" targetType="navTab"
												totalCount="${currentPage1.total}"
												numPerPage="${currentPage1.pageSize}" pageNumShown="20"
												currentPage="${currentPage1.pageNo}" rel="cheatAtlasSurcey"></div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
			</li>
		</ul>
	</div>
	<div id="saveCheatAtlas" style="display: none;">
		<div id="main_3">
			<ul class="main_ul">
				<li class="clearfix fold">
					<h5 hasborder="true">
						<b id="two" class="main_plus"></b><span>关联图谱实时规则提示录入</span>
					</h5>
					<div class="main_foldContent" style="display: none;">
						<div id="medicalSurveyDiv"
							class="panelPageFormContent panelPageFormContentCopy">
							<div class="main_bqtabdivbr">
								<form id="saveSurvey"
									action="clm/parameter/saveCheatAtlas_CLM_cheatAtlasConfigAction.action"
									onsubmit="return validateCallback(this)" rel="pagerFormSurcey"
									method="post">
									<input name="cheatAtlasConfigVO.listId"
										value="${cheatAtlasConfigVO.listId}" type="hidden"
										id="cheatAtlasConfiglistId" />
									<dl style="width: 32%">
										<dt>风险类型</dt>
										<dd style="width: 60%">
											<Field:codeTable cssClass="combox title"
												name="cheatAtlasConfigVO.dangerType" id="dangerTypeNO"
												value="${cheatAtlasConfigVO.dangerType}" nullOption="true"
												tableName="APP___CLM__DBUSER.T_DANGER_TYPE" />
										</dd>
									</dl>
									<dl style="width: 32%">
										<dt>规则编码</dt>
										<dd>
											<input name="cheatAtlasConfigVO.ruleCode" type="text"
												id="ruleCodeNO" onblur="checkNum(this)" />
										</dd>
									</dl>
									<dl style="width: 32%">
										<dt>规则名称</dt>
										<dd>
											<input name="cheatAtlasConfigVO.ruleName"
												value="${cheatAtlasConfigLogVO.ruleName}" type="text"
												id="ruleNameNO" />
										</dd>
									</dl>
									<dl style="width: 32%">
										<dt>提示内容</dt>
										<dd>
											<input name="cheatAtlasConfigVO.content"
												value="${cheatAtlasConfigVO.content}" type="text"
												id="contentNO" />
										</dd>
									</dl>
								</form>
							</div>
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
	<div class="formBarButton main_bottom">
		<ul>
			<li>
				<button class="but_blue" type="button" onclick="insert()">新增</button>
			</li>
			<li>
				<button class="but_blue" type="button" onclick="saveSet()"
					id="saveRule" disabled="disabled">保存</button>
			</li>
			<button class="but_gray" type="button" id="exitbtn" onclick="exit()">退出</button>
			</li>
		</ul>
	</div>

	<!-- 轨迹查询-子页面 -->
	<div id="reportCommPoolSelfId">
		<div class="pageContent" id="insureListJsp">
			<div style="background: #fafafa" id="allForce">
				<form id="cheatAtalsConfigVOList"
					action="clm/parameter/selectCheatAtlasConfigLog_CLM_cheatAtlasConfigAction.action"
					onsubmit="return navTabSearch(this,'cheatAtalsConfigVOList')"
					rel="cheatAtalsConfigVOList" method="post"
					class="pagerForm required-validate">
					<input name="cheatAtlasConfigLogVO.mainListId" type="hidden"
						id="logId" />
					<div id="main_1">
						<ul class="main_ul">
							<li class="clearfix fold">
								<h5 hasborder="true">
									<b id="two" class="main_plus"></b><span>轨迹查询</span>
								</h5>
								<div id="insureListSurceyLog">
									<div class="main_bqtabdivbr">
										<table class="list" id="cheatAtlasConfigLog" width="100%">
											<thead>
												<tr>
													<th nowrap>风险类型</th>
													<th nowrap>规则编码</th>
													<th nowrap>规则名称</th>
													<th nowrap>提示内容</th>
													<th nowrap>提交人</th>
													<th nowrap>提示时间</th>
												</tr>
											</thead>
											<tbody>
												<s:if test="imageFlag != null">
													<tr>
														<td colspan="100">
															<div class="noRueryResult">请选择条件查询数据！</div>
														</td>
													</tr>
												</s:if>
												<s:iterator value="cheatAtlasConfigLogVOList.pageItems"
													status="st">
													<tr align="center" target="listIdInfo">
														<td align="center" style="word-break: break-all;">${dangerName}</td>
														<td align="center" style="word-break: break-all;">${ruleCode}</td>
														<td align="center" style="word-break: break-all;">${ruleName}</td>
														<td align="center" style="word-break: break-all;">${content}</td>
														<td align="center" style="word-break: break-all;">${realName}</td>
														<td align="center" style="word-break: break-all;">${subTime}</td>
													</tr>
												</s:iterator>
											</tbody>
										</table>
										<%-- <div class="panelBar">
											<div class="pages">
												<span>显示</span>
												<s:select list="#{10:'20',50:'50',80:'80',100:'100'}"
													name="select"
													onchange="navTabPageBreak({numPerPage:this.value},'cheatAtalsConfigLogVOList')"
													value="cheatAtlasConfigLogVOList.pageSize">
												</s:select>
												<span>条，共${cheatAtlasConfigLogVOList.total}条</span>
											</div>
											<div class="pagination" targetType="navTab"
												totalCount="${cheatAtlasConfigLogVOList.total}"
												numPerPage="${cheatAtlasConfigLogVOList.pageSize}"
												pageNumShown="20"
												currentPage="${cheatAtlasConfigLogVOList.pageNo}"
												rel="cheatAtalsConfigLogVOList"></div>
										</div> --%>
									</div>
								</div>
							</li>
						</ul>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>