//$(document).ready(function(){
//		 	$("#right-height").height($(window).height());
//		 });
		$(document).ready(function () {
		  $("#btnAddAccountInfo").click(
		  	function()
		  	{
		  		$("#table").addRow($("#template tr:first").clone());
		  	}
		  );
		});
		function removeRow(obj)
		{	
			$("#table").removeRow($(obj).parent().parent().index(),1);
		}
		function showBaoDanAccount()
		{
			$("#collapseOne").collapse('hide');
			$("#collapseOne1").collapse('show');
		}
		$(document).ready(function () {
		 	
		 	$("#ApplyInfo").openDiv(
		 		{
			 		divTitle:'申请信息',
			 	    isOpen:true,
			 	    isAccordion:true
		 	   }
		 	);
		 	$("#ReviewInfo").openDiv(
		 		{
			 		divTitle:'审核信息',
			 	    isOpen:false,
			 	    isAccordion:true
		 	   }
		 	);
		});
	$(function(){
		  
      $("#applyDate").mobiscroll($.extend(opt['date'],opt['default']));
      $("#AuditDate").mobiscroll($.extend(opt['date'],opt['default']));
});