document.write('<script src="'+getRootPath()+'/mobcss/plugins/bootstrap/js/collapse.js"></script>');  
jQuery.fn.openDiv = function(optss) {
	optss = jQuery.extend({
		divTitle: 10,
		divContent: 0,
		contLoadUrl: '',
		isOpen: true,
		isAccordion: true,
		callback: function() {
			return false;
		}
	}, optss || {});

	return this.each(function() {
		var divId = $(this).attr("id");
		var panel = jQuery(this);
		var iconCLass, panelClass;
		if ($(this).html() != '') {
			optss.divContent = $(this).html();
		}
		//初始展开 不予收缩  
		if (!optss.isAccordion) {
			var str = '<div class="panel-mcss"><div class="panel-heading-mcss"><span class="panel-icon"></span>';
			str += '' + optss.divTitle + '</div><div class="panel-body-mcss" >' + optss.divContent + ' </div></div>';
			$(this).html(str);
		} else {
			if (optss.isOpen) {
				iconCLass = 'icon-minus';
				panelClass = 'panel-collapse collapse in';
			} else {
				iconCLass = 'icon-plus';
				panelClass = 'panel-collapse collapse';
			}
			var str1 = '<div class="panel-group-mcss" id="accordion' + divId + '" >';
			str1 += '<div class="panel-mcss"> <div class="panel-heading-mcss" id="headingOne">';
			str1 += '<h4 class="panel-title-mcss"> <a role="button" data-toggle="collapse" data-parent="#accordion' + divId + '" href="#collapseOne' + divId + '" aria-expanded="true" aria-controls="collapseOne' + divId + '">';
			str1 += '<i class="' + iconCLass + '"></i>' + optss.divTitle + '</a></h4></div>';
			str1 += '<div id="collapseOne' + divId + '" class="' + panelClass + '" role="tabpanel" aria-labelledby="headingOne">';
			str1 += '<div class="panel-body-mcss">' + optss.divContent + '</div></div></div></div>';
			$(this).html(str1);
			optss.callback(1, panel)
		}



	});
}

function checkDiv(ids, type) {
	if (ids.indexOf(",") != -1) {
		var id = ids.split(',');
		if (type == 'auto') { //自动折叠
			for (var i = 0; i < id.length; i++) {
				ishidden(id[i]);
			}
		} else {
			if ($("#"+id[0]).css("display")=="none")
				{
					$("#"+id[0]).show();	
				}
			if (!$("#collapseOne" + id[0]).is(':hidden') && 'hide' == id[1])
				$("#collapseOne" + id[0]).collapse('hide');
			else if ($("#collapseOne" + id[0]).is(':hidden') && 'show' == id[1])
			{
				$("#collapseOne" + id[0]).collapse('show');
			}
		}

	} else
		ishidden(ids);
}


function ishidden(id) {
	if ($("#collapseOne" + id).is(':hidden')) {
		$("#collapseOne" + id).collapse('show');
	} else {
		$("#collapseOne" + id).collapse('hide');
	}
}


 	
