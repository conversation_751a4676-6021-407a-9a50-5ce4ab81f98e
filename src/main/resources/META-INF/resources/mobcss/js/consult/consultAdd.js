function addPolToJson() {
	var json = new Array();
	var consult = ["policyType", "policyCode",
		"clientRole"
	];
	json += "[";
	var checkedCount=0;
	$("#consultPoliylist tr").each(
		function() {
			var checkedPolicy=$($(this).find("input[type='checkbox']").get(0)).is(':checked');
			if (checkedPolicy)
			{
				checkedCount++;
				json += "{";
				$(this).children("td:lt(3)").each(
					function(i) {
						if (i == 0) {
							var Value = $(this).find(
								"input").val().trim();
							json = json + "'" + consult[i] + "'" + ":'1',";
						} else {
							var Value = $(this).text().trim();
							json = json + "'" + consult[i] + "'" + ":'" + Value + "',";
						}
					});
				json = json.substring(0, json.length - 1);
				json += "},";
			}
		});
	if (checkedCount>0)
	{
		json = json.substring(0, json.length - 1);
		json += "]";
		$("#tabJson").val(json);
	}
}
function saveConsult() {
	addPolToJson();
	$("#formConsultInfo").submit();
}

$(document).ready(function() {
	var customerId = $("#customerId_customerInfo", window.parent.document).val();
	var clientName = $("#customerName", window.parent.document).html().replace("&nbsp;", "");
	var clientGender = $("#customerGender", window.parent.document).html().replace("&nbsp;", "");
	var clientIdtype = $("#customerCardType", window.parent.document).html().replace("&nbsp;", "");
	var clientIdnumber = $("#customerCardId", window.parent.document).html().replace("&nbsp;", "");
	var json = "{customerId:" + customerId + ",clientName:'" + clientName + "',clientGender:" + clientGender + ",clientIdtype:'" + clientIdtype + "',clientIdnumber:" + clientIdnumber + "}";
	var d;
	if (customerId != null && customerId != "") {
		var url = getRootPath() + "/css/consulting/toAddPage_PA_consultAction.action?json=" + encodeURI(encodeURI(json)) + "&from=1";
		$.ajax({
			type: "post",
			url: url,
			async: false,
			dataType: "json",
			success: function(data) {
				$("#consultInfoContent").empty(); 
				$("#consultInfoTmpl").tmpl(data).appendTo("#consultInfoContent");  //加载数据到模版
				$("#consultInfo").openDiv({
					 divTitle:'咨询人基本信息',
					 isOpen:true,
					 isAccordion:false
			     });
			     $("#insuranceinfor").openDiv({
					 divTitle:'保单信息',
					 isOpen:true,
					 isAccordion:false
			     });
			     $("#consultimport").openDiv({
					 divTitle:'咨询要点',
					 isOpen:true,
					 isAccordion:false
			     });
//			     $.getScript("../../plugins/checkBoxRadio/checkBoxRadio.js", function() {});
			     $.getScript("../../js/select.js", function() {});
			}
		});
	}
	
	$("#formConsultInfo").validationEngine('attach', {
		onValidationComplete: function(form, status) {
			if(status)
			{
				BootstrapDialog.confirm({
					type: BootstrapDialog.TYPE_PRIMARY,
					title: '温馨提示',
					message: '保存后信息不可变更，请确认是否保存',
					closable: true,
					draggable: false,
					btnCancelLabel: '取消',
					btnOKLabel: '确定',
					btnOKClass: null,
					callback: confirmok
				});
			}else
			{
				return status;
			}
		}
	});
});

function confirmok(s)  {
   if(s)
   {
		$.ajax({
			url: getRootPath() + "/css/consulting/addConsulting_PA_consultAction.action?from=1",
			type: "post",
			data: $("#formConsultInfo").serialize(),
			dataType: "json",
			success: function(json) {
				var tipTitle="";
				if (200 == json.statusCode) {
					tipTitle="保存成功";
				}else {
					tipTitle="保存失败";
				}
				BootstrapDialog.alert({
					type: BootstrapDialog.TYPE_PRIMARY,
					title: tipTitle,
					message: json.message,
					closable: false,
					draggable: false,
					buttonLabel: BootstrapDialog.DEFAULT_TEXTS.OK
				});	
			},
			error: function(xhr, error, exception) {
				alert(exception.toString() + '---statue:' + xhr.status + '--statetext:' + xhr.statusText + 'error！');

			}
		});
	}
}
