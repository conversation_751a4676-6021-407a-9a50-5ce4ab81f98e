
document.write('<script src="'+getRootPath()+'/mobcss/plugins/mobiscroll/js/mobiscroll.js"></script>');
document.write('<script src="'+getRootPath()+'/mobcss/plugins/mobiscroll/js/mobiscroll_002.js"></script>');
document.write('<script src="'+getRootPath()+'/mobcss/plugins/mobiscroll/js/mobiscroll_003.js"></script>');
document.write('<script src="'+getRootPath()+'/mobcss/plugins/mobiscroll/js/mobiscroll_004.js"></script>');
document.write('<script src="'+getRootPath()+'/mobcss/plugins/mobiscroll/js/mobiscroll_005.js"></script>');
   var currYear=(new Date()).getFullYear();
   var opt={};
   opt.date={preset:'date'};
   function pushDates(arrs){
   opt.default={
	        theme:'android-ics light',//皮肤样式
	        display:'modal',//显示方式
	        mode:'scroller',//日期选择模式
	        dateFormat:'yyyy-mm-dd',
	        lang:'zh',
	        showNow:true,
	        nowText:"今天",
	        startYear:currYear-70,//开始年份
	        endYear:currYear//结束年份0
		   };

   for(var i=0;i<arrs.length;i++){
	   $("#"+arrs[i]).mobiscroll($.extend(opt['date'],opt['default']));
   }
}

