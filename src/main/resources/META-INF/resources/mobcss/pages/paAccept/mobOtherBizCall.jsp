<%@ page contentType="text/html; charset=utf-8"%>
<%@ page pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path;
%>
<!doctype html>
<html>
<head>
<head>
<meta name="keywords" content="">
<meta charset="utf-8">
<meta name="description" content="mcss">
<meta name="viewport"
	content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
<title>发起回访-电话回访</title>

<link rel="stylesheet" href="<%=basePath%>/mobcss/css/common.css" />
<link rel="stylesheet" href="<%=basePath%>/mobcss/css/customSelect.css" />
<script src="<%=basePath%>/mobcss/js/comm.js"></script>

<script>
	function lastStep(url) {
		$("#upBizCallA").attr('href', url);
		$("#upBizCallA").click();
	}

	function submit(type) {
		if ('save' == type) {
			$("#otherBizCallAddForm")
					.attr("action",
							"bizCall/saveBizCall_PA_bizCallCSAction.action?leftFlag=0&menuId=${menuId}");
			alert("***");
			//$("#otherBizCallAddForm").submit();
			
			$.ajax({
				"url" : "bizCall/saveBizCall_PA_bizCallCSAction.action?leftFlag=0&menuId=${menuId}",
				"data" : $('#otherBizCallAddForm').serialize(),
				"type" : "post",
				"success" : function(data) {
					alert('电话回访保存成功！');
				}
			}); 
		}
	}

	function uploadFile() {
		var fileName = $("#uploadfileID").val();
		if (fileName == '' || fileName == null) {
			alert('请选择上传文件');
			return false;
		}

		$("#otherBizCallAddForm").attr('action',
				'bizCall/upload_PA_bizCallAttachmentAction.action');
		alert($("#otherBizCallAddForm").attr('action'));
		$("#otherBizCallAddForm").submit();

	}

	function save() {
		$.ajax({
			"url" : "<%=basePath%>/bizCall/saveBizCallReply_PA_bizCallCSAction.action",
			"data" : $('#otherBizCallAddForm').serialize(),
			"type" : "post",
			"success" : function(data) {
				alert('电话回访保存成功！');
			}
		});
	}
</script>

</head>
<body>
<article class="container-fluid ">
			<section class="row">
				<article
					class="col-md-26 col-sm-26 col-xs-26 distance-right contain">
	<form
		action="bizCall/saveBizCall_PA_bizCallCSAction.action?leftFlag=0&menuId=${menuId}"
		method="post" name="otherBizCallAddForm" id="otherBizCallAddForm"
		class="pageForm required-validate" enctype="multipart/form-data"
		onsubmit="return iframeCallback(this,dialogAjaxDone);">
		<input type="hidden" name="callFlag" value="2" /> <input type="hidden"
			name="" value="${busiSource}" /> <input type="hidden" name=""
			value="${busiCode}" />
		<!--保全业务信息start-->
		<div class="container-fluid ">
			<div class="panel-mcss">
				<div class="panel-heading-mcss panel-mcss">
					<span class="panel-icon"></span>保全业务信息
				</div>
			</div>
			<div class="row-mcss col-xs-28 col-md-28 col-sm-28"></div>
		</div>
		<!--保全业务信息 end-->

		<!--申请信息start-->
		<div class="container-fluid ">
			<div class="panel-mcss">
				<div class="panel-heading-mcss panel-mcss">
					<span class="panel-icon"></span>申请信息
				</div>
			</div>
			<div class="row-mcss-form">
				<div class="col-md-28 col-sm-28 col-xs-28   ">
					<div class="col-md-4 col-sm-7 col-xs-10 column-left ">
						<span class="text-required">*</span>电话回访申请内容
					</div>
					<div class="col-md-15 col-sm-21 col-xs-18 column-right">
						<textarea id="applyComment" cols="40" rows="4"
							class="form-control" name="bcVO.applyComment" readonly="readonly">${VO.applyComment}</textarea>
					</div>
				</div>
				<div class="col-md-4 col-sm-7 col-xs-10 column-left ">
					<span class="text-required">*</span>紧急程度
				</div>
				<div class="col-md-5 col-sm-7 col-xs-18 column-right">
					<div class="button custom-select">
						<select name="bcVO.level" class="form-control ">
							<option value="1">高</option>
							<option value="2">中</option>
							<option value="3">低</option>
						</select>
					</div>
				</div>

				<div class="col-md-4 col-sm-7 col-xs-10 column-left ">申请人</div>
				<div class="col-md-5 col-sm-7 col-xs-18 column-right">
					<input class="form-control " name="bcVO.applyUser" type="text"
						value="" readonly="readonly">
				</div>
				<div class="col-md-4 col-sm-7 col-xs-10 column-left ">申请提交日期</div>
				<div class="col-md-5 col-sm-7 col-xs-18 column-right">
					<input class="form-control" name="bcVO.applyDate" type="text"
						readonly="readonly">
				</div>
				<div class="col-md-4 col-sm-7 col-xs-10 column-left ">回访人员</div>
				<div class="col-md-5 col-sm-7 col-xs-18 column-right">
					<input type="text" class="form-control "
						value="<Field:codeValue tableName="APP___PAS__DBUSER.T_UDMP_USER" value='${bcVO.replyUser}' />"
						readonly="readonly">
				</div>
			</div>
		</div>
		<!--申请信息end-->
		<!--回访内容开始-->
		<div class="container-fluid ">
			<div class="panel-mcss col-md-28 col-sm-28 col-xs-28">
				<div class="panel-heading-mcss panel-mcss">
					<span class="panel-icon"></span>回访内容
				</div>
			</div>
			<div class="row-mcss-form">
				<div class="row col-md-28 col-sm-28 col-xs-28   ">
					<div class="col-md-4 col-sm-7 col-xs-10 column-left ">
						<span class="text-required">*</span>回访内容
					</div>
					<div class="col-md-15 col-sm-21 col-xs-18 column-right">
						<textarea id="replyComment" rows="3" class="form-control"
							name="bcVO.replyComment">${bcVO.replyComment}</textarea>
					</div>
				</div>
				<div class="col-md-4 col-sm-7 col-xs-10 column-left ">回访结果</div>
				<div class="col-md-5 col-sm-7 col-xs-18 column-right">
					<div class="button custom-select">
						<select name="" class="form-control ">
							<option value="">请选择</option>
							<option value="1">回访成功</option>
						</select>
					</div>
				</div>
				<div class="col-md-4 col-sm-7 col-xs-10 column-left ">服务日期</div>
				<div class="col-md-5 col-sm-7 col-xs-18 column-right">
					<input type="text" class="form-control" name="bcVO.replyDate">
				</div>
				<div class="col-md-28 col-sm-28 col-xs-28">
					<div class="col-md-4 col-sm-7 col-xs-10 column-left ">上传录音附件</div>
					<div class="col-md-8 col-sm-8 col-xs-18 column-right">
						<div class="col-md-28 col-xs-28">
							<label class="lblFile" for="file1"></label> <input type="file"
								name="uploadfile" id="uploadfileID" accept="image/*"
								onchange="uploadImg(this)" /> <img
								src="<%=basePath%>/mobcss/img/imgDemo.png" width="100%" />
						</div>
						<div class="col-md-28 col-xs-28 text-center">
							<div class="row splitRow"></div>
							<input type="button" class="btn btn-save" onclick="uploadFile();"
								value="上传" />
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--回访内容结束-->

		<div class="col-xs-28 col-sm-28 col-md-28 slxxlr-but">
<!-- 			<button class="btn btn-save btn-lg " type="button" -->
<%-- 				onclick="lastStep('<%=basePath%>/.action?menuId=${menuId}')">上一步</button> --%>
<!-- 			<a id="upBizCallA" type="hidden" href="" target="navTab" -->
<!-- 				title="指定回访对象"></a> -->
			<button class="btn btn-save btn-lg " type="button" id="saveButton"
				onclick="save();">保存</button>
<!-- 			<button class="btn btn-save btn-lg " type="button" id="submitButton" -->
<!-- 				onclick="submit('submit');">提交</button> -->
			<button class="btn btn-next btn-lg " type="button">退出</button>
			<button class="btn btn-next btn-lg " type="button">关闭任务</button>
			<!--     	<a href="bizCall/initbizCallClose_PA_bizCallCSAction.action" target="dialog"></a> -->
		</div>
	</form>
</article>
				<%-- 			</section> --%>
				<!-- 		</article> -->
	  	<!--右边开始-->
			<s:include value="<%=basePath%>/mobcss/pages/bqshouli/right.jsp"></s:include>
		<!--右边结束-->
			</section>
		</article>
</body>
</html>
