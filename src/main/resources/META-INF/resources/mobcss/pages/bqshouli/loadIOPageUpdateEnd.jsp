<!--险种录入信息 -->
<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field" %>
<div class="container-fluid" id="updateEndInfo">
	<div class="panel-mcss">
	<div class="panel-heading-mcss panel-mcss"><span class="panel-icon"></span>变更后保单险种列表信息</div>
	</div>
        <div class="row-mcss-form"> 
            <div class="col-xs-28 col-md-28 col-sm-28  table-responsive">
               <table class="table table-bordered text-nowrap" id="basicRemarkTable">
                  <tr class="active">
                    <th>保单号</th>
						<th>投保人</th>
						<th>被保人</th>
						<th>险种代码</th>
						<th>险种名称</th>
						<th>保额</th>
						<th>保费</th>
						<th>职业加费</th>
						<th>险种状态</th>
                  </tr>
                  <tbody>
					<s:iterator value="csContractMasterJobCGVOs_new" status="st" var="var">
						<tr align="center" <s:if test="queryFlag==1">disabled="disabled"</s:if>>
							<td><s:property value="policyCode"/></td>
							<td><s:property value="policyHolderName"/></td>
							<td><s:property value="insuredName"/></td>
							<td><s:property value="busiProdCode"/></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"  value="${busiPrdId}"/></td>
							<td><s:property value="basicAmount"/></td>
							<td><s:property value="totalPremAf"/></td>
							<td><s:property value="extraPrem"/></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS" value="${liabilityState}"/></td>
						</tr>
					</s:iterator>
				</tbody>
               </table>
            </div>
        </div>
    </div>