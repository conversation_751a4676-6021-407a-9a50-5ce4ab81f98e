<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path;
%>
<!--客户重要资料变更-->
<script type="text/javascript">
function custImportInfoFormUpdate(){
	$.ajax({
		type : "post",
		url : getRootPath()	+ "/mob/csAccept/updateContractInfoAboutCustomer_PA_mobCsEndorseCMAction.action",
		dataType : 'html',
		data : $("#custImportInfoForm").serializeArray(),
		cache : false,
		success : function(response){
			var json = myEvalFun(response);
			if('300'==json.statusCode){
				alert(json.message);
			}else{
				$("#xzxxlr").html(response);
			}
		}
	});
}
//自定义格式化json数据方法
function myEvalFun(data){
	try{
		if ($.type(data) == 'string')
			return eval('(' + data + ')');
		else return data;
	} catch (e){
		return {};
	}
}
function checkMseeage(){
	var acceptId = $("#acceptId").val();
	var changeId = $("#changeId").val();
	$.ajax({
		type : "post",
		url : getRootPath()	+ "/mob/csAccept/checkLifeFlag_PA_mobCsEndorseCMAction.action",
		dataType : 'text',
		data : "changeId=" + changeId + "&acceptId=" + acceptId,
		success : function(data) {
			if (data.indexOf("{") == 0) {
				var json = jQuery.parseJSON(data);
				if (json.statusCode == 300) {
					var rs=confirm(json.message + " 是否继续？");
					if(rs){
						custImportInfoFormSave();
					}
				}
			}else{
				custImportInfoFormSave();
			}
		}
	});
}
function custImportInfoFormSave(){
	$.ajax({
		type : "post",
		url : getRootPath()	+ "/mob/csAccept/updateCustomer_PA_mobCsEndorseCMAction.action",
		dataType : 'html',
		data : $("#custImportInfoForm").serializeArray(),
		cache : false,
		success : function(response){
			var json = myEvalFun(response);
			if('300'==json.statusCode){
				alert(json.message);
			}else{
				$("#xzxxlr").html(response);
			}
		}
	});
	var rs=confirm("请确认是否需要保存录入的信息？");
	if(rs){
		$.ajax({
			type : "post",
			url : getRootPath()	+ "/mob/csAccept/updateCustomer_PA_mobCsEndorseCMAction.action",
			dataType : 'html',
			data : $("#custImportInfoForm").serializeArray(),
			cache : false,
			success : function(response){
				var json = myEvalFun(response);
				if('300'==json.statusCode){
					alert(json.message);
				}else{
					$("#xzxxlr").html(response);
				}
			}
		});
	}
}
function showCustomerBd(){
	var rs=confirm("请确认是否需要保存录入的信息？");
	if(rs){
		var tableObj = "[";
		$("#busPrdListTable").find("tr").each(function(){
			var productCodeSys = $(this).find("td:eq(3)").text();
			var coverageYear = $(this).find("td:eq(6)").find("input").val();
			var coveragePeriod = $(this).find("td:eq(7)").text();
			tableObj = tableObj+"{'productCodeSys':'"+productCodeSys+"','coverageYear':'"+coverageYear+"','coveragePeriod':'"+coveragePeriod+"'},";
		});
		tableObj = tableObj.substring(0,tableObj.length-1)+"]";
		$("#busPrdListJsonStr").val(tableObj);
		$.ajax({
			type : 'POST',
			url :  getRootPath()+ "/mob/csAccept/contractMasterNewInfo_PA_mobCsEndorseCMAction.action",
			data : $("#itemForm").serializeArray(),
			cache : false,
			success : function(response) {
				var json = myEvalFun(response);
				if (json.statusCode == '300') {
					alertMsg.error(json.message);
				}else{
					$("#bdhlb").html(response);
					var returnMsg = $("#CM_policyMsg").val();
					if(returnMsg != null && returnMsg != ""){
						alert(returnMsg);//存在表外费率，给不阻断提示
					} else{
						alert("保存成功!");
					}						
				}
			}
		});
	}
}
</script>
<!--保单险种列表信息-->
<div id="bdqlb">
	<div class="container-fluid">
		<div class="row-mcss-form">
			<div class="col-xs-28 col-md-28 col-sm-28 table-responsive">
				<table class="table table-bordered text-center text-nowrap">
					<tr class="active">
						<th>保单号</th>
						<th>投保人</th>
						<th>被保人</th>
						<th>险种代码</th>
						<th>险种名称</th>
						<th>被保人投保年龄</th>
						<th>保额(万元)</th>
						<th>保费(元)</th>
						<th>交费期限(年)</th>
						<th>下次交费对应日</th>
						<th>领取标准(元)</th>
						<th>保单状态</th>
						<th>是否参与本次变更</th>
					</tr>
					<s:iterator value="custImportInfoUpdateVOS">
						<tr align="center"
							<s:if test="queryFlag==1">disabled="disabled"</s:if>>
							<td><s:property value="policyCode" /></td>
							<td><s:property value="policyHolder" /></td>
							<td><s:property value="insured" /></td>
							<td><s:property value="productCodeSys" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
									value="${productNameSys}" /></td>
							<td><s:property value="insuredAge" /></td>
							<td><s:property value="amout" /></td>
							<td><s:property value="totalPremAf" /></td>
							<td><s:property value="chargeYaer" /></td>
							<td><s:date name="payDueDate" format="yyyy-MM-dd"/></td>
							<td><s:property value="" /></td>
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS"
									value="${abilityState}" /></td>
							<td><s:property value="isChange" /></td>
						</tr>
					</s:iterator>
				</table>
			</div>
		</div>
	</div>
</div>
<!--客户相关保单列表结束-->
<!--变更后的客户要件信息开始-->
<div id="bdhyj">
<form id="custImportInfoForm">
	<div class="container-fluid">
		<div class="row-mcss-form">
		<input type="hidden" name="customerId" value="${customerId}">
		<input type="hidden" name="changeId" value="${changeId }" id="changeId"> 
		<input type="hidden" name="acceptId" value="${acceptId }" id="acceptId">
			<div class="col-xs-7 col-sm-3 col-md-3 column-left">姓名</div>
			<div class="col-xs-21 col-sm-5 col-md-5 column-right">
				<input type="text" class="form-control" name="csCustomerNewVO.customerName"
					value="${csCustomerNewVO.customerName}" ></input>
			</div>
			<div class="col-xs-7 col-sm-4 col-md-4 column-left">性别</div>
			<div class="col-xs-21 col-sm-6 col-md-6 column-right">
				<div class='button custom-select'><Field:codeTable name="csCustomerNewVO.customerGender" tableName="APP___PAS__DBUSER.T_GENDER"
						nullOption="true" value="${csCustomerNewVO.customerGender}" /></div>
			</div>
			<div class="col-xs-7 col-sm-4 col-md-4 column-left">出生日期</div>
			<div class="col-xs-21 col-sm-6 col-md-6 column-right">
				<div class="has-feedback">
	            <input type="text" class="form-control" dateFmt="yyyy-MM-dd" value="<s:date format="yyyy-MM-dd" name="csCustomerNewVO.customerBirthday" />"
							id="jobCateGoryBeginTime" name="csCustomerNewVO.customerBirthday" />
						<a class="inputDateButton"  disabled="true" href="#"></a> 
	            <span class="glyphicon glyphicon-calendar form-control-feedback text-required dataColor" aria-hidden="true"> </span>
	            </div>
			</div>

			<div class="col-xs-9 col-sm-5 col-md-5 column-left">证件类型</div>
			<div class="col-xs-21 col-sm-5 col-md-5 column-right">
				<div class='button custom-select'><Field:codeTable name="csCustomerNewVO.customerCertType"
						tableName="APP___PAS__DBUSER.T_CERTI_TYPE" nullOption="true" value="${csCustomerNewVO.customerCertType}" /></div>
			</div>
			<div class="col-xs-7 col-sm-4 col-md-4 column-left">证件号码</div>
			<div class="col-xs-21 col-sm-6 col-md-6 column-right">
				<input class="form-control" name="csCustomerNewVO.customerCertiCode"
					value="${csCustomerNewVO.customerCertiCode}" ></input>
			</div>
			<div class="col-xs-10 col-sm-10 col-md-10 column-right">
				<button class="btn btn-save btn-lg" type="button" onclick="custImportInfoFormUpdate()">更新要件信息</button>
			</div>
			<div class="col-xs-18 col-sm-18 col-md-18 column-right">
				<button class="btn btn-save btn-lg" type="button" onclick="checkMseeage()">保存要件信息</button>
			</div>

		</div>
	</div></form>
</div>
<!--变更后的客户要件信息结束-->
<!--险种信息录入开始-->
<div id="xzxxlr">
	<div class="container-fluid">
		<div class="row-mcss-form">
		<form id="itemForm">
		<input  type="hidden" name="customerId" value="${customerId}">
		<input type="hidden" name="changeId" value="${changeId }">
		<input type="hidden" name="acceptId" value="${acceptId }">
		<input type="hidden" id="busPrdListJsonStr" name="busPrdListJsonStr" value="">
			<div class="col-xs-28 col-md-28 col-sm-28 table-responsive">
				<table class="table table-bordered text-center text-nowrap">
					<tr class="active">
						<th>保单号</th>
						<th>投保人</th>
						<th>被保人</th>
						<th>险种代码</th>
						<th>险种名称</th>
						<th>新的保障年期类型</th>
						<th>新的保障年期</th>

					</tr>
					<tbody id="busPrdListTable">
					<s:iterator value="custImportInfoUpdateAFVOS" id="plist1" status ="st">
						<tr align="center" tr_saveStatus='1' <s:if test="queryFlag==1">disabled="disabled"</s:if>>
							<td><s:property value="policyCode"/></td> 												
							<td><s:property value="policyHolder"/></td> 			
							<td><s:property value="insured"/></td> 			
							<td>${productCodeSys}</td> 			
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${productNameSys}"/></td> 												
							<td>
							    <Field:codeTable id="coveragePeriod" name="coveragePeriod" disabled="true"
                              			tableName="APP___PAS__DBUSER.T_COVERAGE_PERIOD" nullOption="true" value="${coveragePeriod}"/> 
							</td>
							<td>
								<%-- <Field:codeTable id="coverageYear" name="coverageYear"
                              			tableName="APP___PAS__DBUSER." nullOption="true" value="${coverageYear }"/>  --%>
							 	<input type ="text" name = "coverageYear"  value="${coverageYear }" disabled="disabled" > 				
								
							</td>
							<td style="display:none">${coveragePeriod}</td>
						</tr>
					</s:iterator>
					</tbody>
				</table>
				</div></form>
				<div class="col-xs-28 col-sm-28 col-md-28 column-right">
					<button class="btn btn-lg btn-save" onclick="showCustomerBd()">保存险种信息</button>
				</div>
			</div>
		</div>
</div>
<!--险种录入信息结束-->
<!--变更后客户相关保单列表开始-->
<div id="bdhlb">
	<div class="container-fluid">
		<div class="row-mcss-form">
		<input type="hidden" id="CM_policyMsg" value="${policyMsg}"/>
<div class="col-xs-28 col-md-28 col-sm-28 table-responsive">
				<table class="table table-bordered text-center text-nowrap" >
					<tr class="active">
						<th>保单号</th>
						<th>投保人</th>
						<th>被保人</th>
						<th>险种代码</th>
						<th>险种名称</th>
						<th>被保人投保年龄</th>
						<th>保额(万元)</th>
						<th>保费(元)</th>
						<th>交费期限(年)</th>
						<th>下次交费对应日</th>
						<th>领取标准(元)</th>
						<th>保单状态</th>
						<th>是否参与本次变更</th>
					</tr>
					<s:iterator value="custImportInfoUpdateAFVOS" id="plist2" >
						<tr align="center" <s:if test="queryFlag==1">disabled="disabled"</s:if>>
						    <td><s:property value="policyCode"/></td> 												
							<td><s:property value="policyHolder"/></td> 			
							<td><s:property value="insured"/></td> 			
							<td><s:property value="productCodeSys"/></td> 			
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT" value="${productNameSys}"/></td> 
							<td><s:property value="insuredAge"/></td> 
							<td><s:property value="amout"/></td> 
							<td><s:property value="totalPremAf"/></td> 
							<td><s:property value="chargeYaer"/></td>
							<td><s:date name="payDueDate" format="yyyy-MM-dd"/></td>	
							<td><s:property value=""/></td> 
							<td><Field:codeValue tableName="APP___PAS__DBUSER.T_LIABILITY_STATUS" value="${abilityState}"/></td>
							<td><s:property value="isChange"/></td> 
						</tr>
					</s:iterator>
				</table>
			</div>
		</div>
	</div>
</div>
<!--变更后客户相关保单列表结束-->