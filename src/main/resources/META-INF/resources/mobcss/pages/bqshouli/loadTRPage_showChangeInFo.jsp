<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path;
%>


<!--变更后信息开始-->
<div class="container-fluid" >
	<div class="panel-mcss">
	<div class="panel-heading-mcss panel-mcss"><span class="panel-icon"></span>变更后信息</div>
	</div>
        <div class="row-mcss-form"> 
            <div class="col-xs-28 col-md-28 col-sm-28  table-responsive">
               <table class="table table-bordered text-nowrap">
                  <tr class="active">
                  	<th>保单号</th>
					<th>险种代码</th>
					<th>险种名称</th>
					<th>自垫本金</th>
					<th>自垫起期</th>
					<th>自垫利息</th>
                  </tr>
                  <s:iterator value="csEndorseTRChgVOs" status="st" id="qr">
                  <tr class="text-center">
                   <td><s:property value="policyCode" /></td>
					<td><s:property value="busiItemId" /></td>
					<td><Field:codeValue tableName="APP___PAS__DBUSER.T_BUSINESS_PRODUCT"
							value="${busiItemName}" /></td>
					<td><s:property value="capitalBalance" /></td>
					<td><s:date format="yyyy-MM-dd" name="startDate" /></td>
					<td><s:property value="interestSum" /></td>	
                  </tr>
                  </s:iterator>
               </table>
            </div>
        </div>
    </div>
<!--变更后信息结束-->
