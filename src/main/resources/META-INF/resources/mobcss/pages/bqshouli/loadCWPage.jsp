<%@ page contentType="text/html; charset=utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path;
%>
<body>
<!--保单信息开始-->
<s:iterator id="fzFreezePolicyVOs" value="fzFreezePolicyVOs" status="status">
<div class="container-fluid" >
<form method="post" action="<%=basePath%>/mob/csAccept/updateUnfreezePolicyInfo_PA_mobCsEndorseCFAction.action"
	class="required-validate" onsubmit="return save1(this);" id="saveUnFreezeInfoForm">
	<div class="panel-mcss">
	<div class="panel-heading-mcss panel-mcss"><span class="panel-icon"></span>保单详情</div>
	</div>
     <div class="row-mcss-form col-xs-28 col-sm-28 col-md-28 ">
        <div class="col-xs-8 col-sm-4 col-md-4 column-left" >保单号</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
           <input type="text" class="form-control" readonly="readonly" value="${csContractMasterVO.policyCode}" />
         </div>
         <div class="col-xs-8 col-sm-4 col-md-4 column-left" >保单生效日</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
            <input readonly="readonly" class="form-control" value="<s:date format="yyyy-MM-dd" name="csContractMasterVO.validateDate"></s:date>" />
         </div>
         <div class="col-xs-8 col-sm-4 col-md-4 column-left" >保险止期</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
            <input readonly="readonly" class="form-control" value="<s:date format="yyyy-MM-dd" name="csContractMasterVO.expiryDate"></s:date>" />
         </div>
          <div class="col-xs-8 col-sm-4 col-md-4 column-left" >执行通知书编号</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
            <input type="text" class="form-control" readonly="readonly" name="cfFreezePolicyInfoVO.execDocNo"
			  value="${cfFreezePolicyInfoVO.execDocNo }" />
         </div>
         <div class="col-xs-8 col-sm-4 col-md-4 column-left" >执行单位</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
            <input type="text" readonly="readonly" id="" name="cfFreezePolicyInfoVO.execOrg" class="form-control"
				value="${cfFreezePolicyInfoVO.execOrg}">
         </div>
          <div class="col-xs-8 col-sm-4 col-md-4 column-left" >组织机构代码</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
           <input type="text" class="form-control"  readonly="readonly" id="" name="cfFreezePolicyInfoVO.organCode"
				value="${cfFreezePolicyInfoVO.organCode}">
         </div>
         
         <input type="hidden" value="${cfFreezePolicyInfoVO.freezeId }" name="cfFreezePolicyInfoVO.freezeId" />
        
        <div class="col-xs-8 col-sm-4 col-md-4 column-left" >投保人姓名</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
           <input type="text" id="policyHolderName" class="form-control"
				name="policyHolderName" readonly="readonly" value="${policyHolderName}">
         </div>
         <div class="col-xs-8 col-sm-4 col-md-4 column-left" >投保人证件类型</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
            <input type="text" id="policyHolderCertiType" class="form-control"
				name="policyHolderCertiType" readonly="readonly" value="<Field:codeValue  tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="${policyHolderCertiType}" /> ">
         </div>
          <div class="col-xs-8 col-sm-4 col-md-4 column-left" >投保人证件号</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
           <input type="text" class="form-control" id="" name="" readonly="readonly" value="${policyHolderCertiCode} ">
         </div>
        <div class="col-xs-8 col-sm-4 col-md-4 column-left" >冻结日期 </div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
            <input type="text" readonly="readonly" name="cfFreezePolicyInfoVO.freezeDate" class="form-control"
				value="<s:date format="yyyy-MM-dd" name="cfFreezePolicyInfoVO.freezeDate"></s:date>" />
         </div>
          <div class="col-xs-8 col-sm-4 col-md-4 column-left" >冻结原因</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
           <input type="text" class="form-control" name="cfFreezePolicyInfoVO.freezeCause" readonly="readonly"
				value='<Field:codeValue    tableName="APP___PAS__DBUSER.T_FREEZE_CAUSE" value="${cfFreezePolicyInfoVO.freezeCause}"/> ' />
         </div>
    </div>
    
    <!--被保险人信息开始-->
<div class="container-fluid padding-lf20" >
	<div class="panel-mcss">
	<div class="panel-heading-mcss panel-mcss"><span class="panel-icon"></span>被保险人信息</div>
	</div>
        <div class="row-mcss-form"> 
            <div class="col-xs-28 col-md-28 col-sm-28  table-responsive">
               <table class="table table-bordered text-nowrap">
                  <tr class="active">
                  	<th>姓名</th>
					<th>证件类型</th>
					<th>证件号码</th>
					<th>出生日期</th>
					<th>性别</th>
                  </tr>
                  <tbody align="center">
													<s:iterator value="csInsuredListVO" status="st" id="qr">

														<tr>
															<td><s:property value="customerName" /></td>
															<td><Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
																	value="${customerCertType}" /></td>
															<td><s:property value="customerCertiCode" /></td>
															<%-- 										<td><s:property value="customerBirthday"/></td>
				 --%>
															<td><s:date format="yyyy-MM-dd"
																	name="customerBirthday" /></td>
															<td><Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER"
																	value="${customerGender}" /></td>
														</tr>

													</s:iterator>
												</tbody>
               </table>
            </div>
            
        </div>
    </div>
<!--被保险人信息结束-->
  <!--受益人信息开始-->
<div class="container-fluid padding-lf20" >
	<div class="panel-mcss">
	<div class="panel-heading-mcss panel-mcss"><span class="panel-icon"></span>受益人信息</div>
	</div>
    <div class="row-mcss-form"> 
            <div class="col-xs-28 col-md-28 col-sm-28  table-responsive">
               <table class="table table-bordered text-nowrap">
                  <tr class="active">
					<th>险种代码</th>
					<th>被保险人</th>
					<th>被保人证件类型</th>
					<th>被保人证件号</th>
					<th>受益人姓名</th>
					<th>受益人证件类型</th>
					<th>受益人证件号码</th>
					<th>受益人出生日期</th>
					<th>受益人性别</th>
					<th>受益顺序</th>
					<th>受益份额</th>
                  </tr>
                  <tbody align="center" id="changeCW">
													<s:iterator value="cfFreezePolicyContractInsuredVO"
														status="st" id="qi">
														<tr>
															<td><s:property value="instureTypeCode" /></td>
															<td><s:property value="insuredName" /></td>
															<td><Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
																	value="${beneficiaryBeInsturerCertiType}" /></td>
															<td><s:property
																	value="beneficiaryBeInsturerCertiNumber" /></td>
															<td><s:property value="beneficiaryName" /></td>
															<td><Field:codeValue tableName="APP___PAS__DBUSER.T_CERTI_TYPE"
																	value="${beneficiaryCertiType}" /></td>
															<td><s:property value="beneficiaryCertiNum" /></td>
															<td><s:date format="yyyy-MM-dd"
																	name="beneficiaryBirthday" /></td>
															<td><Field:codeValue tableName="APP___PAS__DBUSER.T_GENDER"
																	value="${ beneficiarySex}" /></td>
															<td><s:property value="shareOrder" /></td>
															<td><s:property value="shareRate" /></td>
														</tr>
													</s:iterator>
												</tbody>
               </table>
            </div>
            
        </div>
        <div id="changedPanel" class="unitBox"></div>
    </div>
    </form>
<!--受益人信息结束-->
</div>
</s:iterator>
<!--保单信息结束-->

<!--解冻信息开始-->
<div class="container-fluid" >
	<div class="panel-mcss">
	<div class="panel-heading-mcss panel-mcss"><span class="panel-icon"></span>解冻信息</div>
	</div>
	<form method="post" action="<%=basePath%>/mob/csAccept/updateUnfreezePolicyInfo_PA_mobCsEndorseCFAction.action"
		class="required-validate" onsubmit="return save2(this);" id="saveUnFreezeInfoForm">
		<input type="hidden" id ="changeId" name="changeId"
				value="${changeId }"> <input type="hidden" id ="acceptId" name="acceptId"
				value="${acceptId }">
     <div class="row-mcss-form">
         <div class="col-xs-8 col-sm-4 col-md-4 column-left" ><span class="text-required">*</span>解冻日期</div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
            <input id="unfreezeDate" class="form-control" name="cfFreezePolicyResultInfoVO.unfreezeDate"
				type="expandDateYMD" value="<s:date format="yyyy-MM-dd" name="cfFreezePolicyResultInfoVO.unfreezeDate"></s:date>" />
         </div>
         <div class="col-xs-8 col-sm-4 col-md-4 column-left" ><span class="text-required">*</span>解冻原因 </div>
         <div class="col-xs-20 col-sm-5 col-md-5 column-right">
            <div class="button custom-select">
               <Field:codeTable cssClass="combox" nullOption="true"
					name="cfFreezePolicyResultInfoVO.unfreezeCause" id="unfreezeCause"
					value="${cfFreezePolicyResultInfoVO.unfreezeCause}" tableName="APP___PAS__DBUSER.T_UNFREEZE_CAUSE" /> 
        	</div>
         </div>
       
       <table style="display: none" id="updateUnFreezedTable"
										table_saveStatus="1">
										<!-- style="display:none" -->
										<thead>
											<tr>
												<!-- <th  colName="freezeId" >保单冻结号</th> -->
												<th colName="policyCode">保单号</th>
											</tr>
										</thead>
										<tbody>
											<s:iterator value="fzFreezePolicyVOs" status="status">
												<tr align="center" id="showTr" tr_saveStatus="1">
													<td>
														<%-- <s:property   value="cfFreezePolicyInfoVO.freezeId"/> --%>
														<s:property value="csContractMasterVO.policyCode" />
													</td>
												</tr>
											</s:iterator>
										</tbody>

									</table>
									<input type="hidden" id="jsons" value="jsons">
									
         <div class="col-xs-8 col-sm-5 col-md-5"  id="saveMesId">
         <button type="button" class="btn btn-save btn-md" onclick="updateUnfreezePolicyInfo('saveUnFreezeInfoForm')">保存 </button>	
         </div>
    </div>
    </form>
</div>
<!--解冻信息结束-->
</body>

<script type="text/javascript">
function updateUnfreezePolicyInfo(formId){
	 //获取解冻原因
	var unfreezeCause=$("#unfreezeCause option:selected").val();
	//获取解冻日期的值
	var unfreezeDate=$('#unfreezeDate').val();
	if(unfreezeCause==""){
		alert("请先选择解冻原因！");
	}else if(unfreezeDate.length==0){
		alert("请先选择解冻日期！");
	}else{
		var check=confirm("请确认是否需要保存解冻信息");
		if(check==true){
			var _jsons="";
			var $table = $("#updateUnFreezedTable");		
			//_jsons+= _cs_tableToJson($table); 
			var _jsons = "[";
			$("#updateUnFreezedTable").find("tr").each(function(){
				var policyCode = $(this).find("td:eq(0)").text().trim();
				_jsons = _jsons+"{'policyCode':'"+policyCode+"'},";
			});
			_jsons =_jsons.substring(0,_jsons.length-1)+"]";
			$("#jsons").val(_jsons);
			var acceptId=document.getElementById("acceptId").value;
			var changeId=document.getElementById("changeId").value;
			var  rootPath= getRootPath();
			$.ajax({	
				type:"post",
				url:rootPath+'/mob/csAccept/updateUnfreezePolicyInfo_PA_mobCsEndorseCFAction.action',
				cache:false,
				data:"jsonString="+$("#jsons").val()+"&jsonUnfreezeCause="+unfreezeCause+"&jsonUnfreezeDate="+unfreezeDate+"&changeId="+changeId+"&acceptId="+acceptId,
				dataType:"text",
				success:function(data){
						if(trim(data)=="1"){
						alert("保单解冻保存成功!");
					}
					else
					if(trim(data)=="0")
					{
						alert("保单没有被冻结，请确认。!");
					}
					else{
						alert("必填项信息未完整录入，不能受理保单冻结保全项，请确认。!");
					}
				},
				error:function(){alert("必填项信息未完整录入，不能受理保单冻结保全项，请确认。");}
			});	
		}else{return false;}
	
	}
};
function save1(){
	$.ajax({
		type : "POST",
		url : getRootPath()
				+ "/mob/csAccept/updateUnfreezePolicyInfo_PA_mobCsEndorseCFAction.action",
		data : $("#saveUnFreezeInfoForm").serializeArray(),
		cache : false,
		async : false,
		dataType : "html",
		success : function(data){
			alert("保存成功！");
		},
	});
}
function save2(){
	$.ajax({
		type : "POST",
		url : getRootPath()
				+ "/mob/csAccept/updateUnfreezePolicyInfo_PA_mobCsEndorseCFAction.action",
		data : $("#saveUnFreezeInfoForm").serializeArray(),
		cache : false,
		async : false,
		dataType : "html",
		success : function(data){
			alert("保存成功！");
		},
	});
}
</script>
