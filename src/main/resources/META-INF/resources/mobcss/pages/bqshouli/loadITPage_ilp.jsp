<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://"
			+ request.getServerName() + ":" + request.getServerPort()
			+ path;
%>


 <!--账户信息开始-->
<div class="container-fluid " >
	<div class="panel-mcss">
	<div class="panel-heading-mcss panel-mcss"><span class="panel-icon"></span>账户信息</div>
	</div>
    <div class="row-mcss-form"> 
        <div class="col-xs-28 col-md-28 col-sm-28  table-responsive">
           <table class="table table-bordered text-nowrap">
              <tr class="active">
				<th colName="investAccountCode">账户代码</th>
				<th>账户名称</th>
				<th colName="accumUnits">当前单位数</th>
				<th>最近计价日</th>
				<th>单位卖出价</th>
				<th>当前的账户价值</th>
                      <th>累计缴费金额</th>
              </tr>
              <s:iterator value="csAccountTIVOList" status="st" var="var">
              <tr class="text-center">
             	<td>${investAccountCode }</td>
				<td>${investAccountId }</td>
				<td>${accumUnits }</td>
				<td style="width: 10%"><s:date name="PricingDate"
						format="yyyy-MM-dd"></s:date>
				<td>${bigPrice }</td>
				<td>${accountValue }</td>
				<td>${totalPrem}</td>
              </tr>
              </s:iterator>
           </table>
        </div>
    </div>

</div>
<!--账户信息结束-->
  <!--退保信息开始-->
<div class="container-fluid " >
	<div class="panel-mcss">
	<div class="panel-heading-mcss panel-mcss"><span class="panel-icon"></span>退保信息</div>
	</div>
    <div class="row-mcss-form"> 
            <div class="col-xs-8 col-sm-4 col-md-4 column-left">是否犹豫期内</div>
            <div class="col-xs-20 col-sm-5 col-md-5 column-right">
                <input type="text" class="form-control"  readonly="readonly"
						value='<Field:codeValue   tableName="APP___PAS__DBUSER.T_YES_NO"  value="${surrenderVO.isInHesitate}"/>'>
            </div>
             <div class="col-xs-8 col-sm-4 col-md-4 column-left" >退保原因</div>
             <div class="col-xs-20 col-sm-5 col-md-5 column-right">
                <div class="button custom-select">
                    <s:if test="queryFlag==1">
					<Field:codeTable id="ilpSurrenderCause" cssClass="combox"
						nullOption="true" name="surrenderVO.ilpSurrenderCause" value="${surrenderVO.ilpSurrenderCause}"
						tableName="APP___PAS__DBUSER.t_surrender_cause"/>
					</s:if>
					<s:else>
					<Field:codeTable id="ilpSurrenderCause" cssClass="combox"
						nullOption="true" name="surrenderVO.ilpSurrenderCause" value="${surrenderVO.ilpSurrenderCause}"
						tableName="APP___PAS__DBUSER.t_surrender_cause"/>
					</s:else>
            	</div>
             </div>
             <div class="col-xs-8 col-sm-4 col-md-4 column-left" >投保人与业务员关系</div>
             <div class="col-xs-20 col-sm-5 col-md-5 column-right">
                <div class="button custom-select">
                   <s:if test="queryFlag==1">
						<Field:codeTable id="agentHolderRelation" cssClass="combox"
							nullOption="true" name="surrenderVO.agentHolderRelation" value="${surrenderVO.agentHolderRelation}"
							tableName="APP___PAS__DBUSER.T_LA_PH_RELA" />
					</s:if>
					<s:else>
					<Field:codeTable id="agentHolderRelation" cssClass="combox"
							nullOption="true" name="surrenderVO.agentHolderRelation" value="${surrenderVO.agentHolderRelation}"
							tableName="APP___PAS__DBUSER.T_LA_PH_RELA" />
					</s:else>
            	</div>
             </div>
              
            <div class="col-xs-8 col-sm-4 col-md-4 column-left">预估退保金额</div>
            <div class="col-xs-20 col-sm-5 col-md-5 column-right">
	            <input type="text" class="form-control"  name="surrenderVO.actualReturnPremium"
						value="${surrenderVO.actualReturnPremium }" readonly="readonly">
            </div>
            <div class="col-xs-8 col-sm-4 col-md-4 column-left">退保费用</div>
            <div class="col-xs-20 col-sm-5 col-md-5 column-right">
	            <input type="text" class="form-control" name="surrenderVO.surrenderMoney"
						value="${surrenderVO.surrenderMoney }" readonly="readonly" >
						<input type="hidden" name="feeDetailJson" id="feeDetailJson"  />
            </div>

            <div class="col-xs-8 col-sm-4 col-md-4 column-left">延迟原因</div>
            <div class="col-xs-20 col-sm-5 col-md-5 column-right">
               <textarea cols="3" class="form-control" name="surrenderVO.delayCause" 
						id="delayCause" <s:if test="queryFlag==1">readonly="readonly"</s:if>>${surrenderVO.delayCause}</textarea>
            </div>
     
    </div>
</div>

<!--退保信息结束-->