<%@ page language="java" pageEncoding="utf-8"%>
<%@ taglib uri="/struts-tags" prefix="s"%><%@include file="/udmpCommon.jsp"%>
<%@ taglib uri="http://www.git.com.cn/taglib/udmp/field" prefix="Field"%>
<s:set var="ctx">${pageContext.request.contextPath}</s:set>
<%
String path = request.getContextPath();
String basePath = request.getScheme() + "://"
		+ request.getServerName() + ":" + request.getServerPort()
		+ path ;
%>

<!DOCTYPE html>
<html>
<head>
<meta name="keywords" content="">
<meta charset="utf-8">
<meta name="description" content="mcss">
<meta name="viewport"
	content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
<title>试算1</title>
<link rel="icon" href="<%=basePath%>/mobcss/img/favicon.ico">
<link rel="stylesheet" href="<%=basePath%>/mobcss/css/common.css" />
<link rel="stylesheet"
	href="<%=basePath%>/mobcss/plugins/checkBoxRadio/customCheckBoxRadio.css">
<link rel="stylesheet" href="<%=basePath%>/mobcss/css/customSelect.css" />
<link rel="stylesheet"
	href="<%=basePath%>/mobcss/plugins/mobiscroll/css/mobiscroll_all.css" />
<link rel="stylesheet" href="<%=basePath%>/mobcss/css/bqsl1.css">
</head>
<body>
	<article class="container-fluid ">
		<section class="row">
			<article class="col-md-28 col-xs-28 distance-right  contain">
				<!--进度条开始-->
				<section class="container-fluid ">
					<div class="row-mcss col-md-28 col-xs-28">
						<div class="col-md-7 col-xs-7 slxxlr-center">
							<span class="slxxlr-orange-yuan">1</span> <span
								class="slxxlr-orange"></span> <span class="slxxlr-orange-font">客户查询</span>
						</div>
						<div class="col-md-7 col-xs-7 slxxlr-center">
							<span class="slxxlr-blue-yuan">2</span> <span class="slxxlr-blue"></span>
							<span class="slxxlr-blue-font">受理信息录入</span>
						</div>
						<div class="col-md-7 col-xs-7 slxxlr-center">
							<span class="slxxlr-gray-yuan">3</span> <span class="slxxlr-gray"></span>
							<span class="slxxlr-gray-font">项目信息录入</span>
						</div>
						<div class="col-md-7 col-xs-7 slxxlr-center">
							<span class="slxxlr-gray-yuan">4</span> <span class="slxxlr-gray"></span>
							<span class="slxxlr-gray-font">试算结果查询</span>
						</div>
					</div>

				</section>
				<!--进度条结束-->
				<!--申请信息录入开始-->
				<section class="row-mcss-form" id="Information-input">
					<form
						action="<%=basePath%>/mob/csAccept/recordAppInfo_PA_mobAcceptAction.action"
						method="post" onsubmit="return saveApplyMassage(this)">
						<input type="hidden" id="changeId"
							name="csAcceptInfoEntryVO.csApplicationVO.changeId"
							value="${csAcceptInfoEntryVO.csApplicationVO.changeId }" /> <input
							type="hidden" id="applyCode"
							name="csAcceptInfoEntryVO.csApplicationVO.applyCode"
							value="${csAcceptInfoEntryVO.csApplicationVO.applyCode }" /> <input
							type="hidden" value="01" id="changeSource"
							name="csAcceptInfoEntryVO.changeVO.changeSource" /> <input
							type="hidden"
							value="${csAcceptInfoEntryVO.customerVO.customerName}"
							id="applyName"
							name="csAcceptInfoEntryVO.csApplicationVO.applyName" /> <input
							type="hidden"
							value="${csAcceptInfoEntryVO.customerVO.customerId}"
							id="customerId"
							name="csAcceptInfoEntryVO.csApplicationVO.customerId" /> <input
							name="flag" type="hidden" value="${csAcceptInfoEntryVO.flag }" />
						<div class="col-xs-8 col-sm-5 col-md-5 column-left">
							<span class="text-required">*</span>申请提交日期
						</div>
						<div class="col-xs-20 col-sm-7 col-md-7 column-right">
							<div class="has-feedback">
								<input type="text" class="form-control" id="applyDate"
									name="csAcceptInfoEntryVO.csApplicationVO.applyTime"
									value="<s:date name="csAcceptInfoEntryVO.csApplicationVO.applyTime" format="yyyy-MM-dd"/>" />
								<span
									class="glyphicon glyphicon-calendar form-control-feedback text-required dataColor"
									aria-hidden="true"> </span>
							</div>
						</div>
						<div class="col-xs-8 col-sm-5 col-md-5 column-left">
							<span class="text-required">*</span>申请方式
						</div>
						<div class="col-xs-20 col-sm-7 col-md-7 column-right">
							<div class="button custom-select">
								<Field:codeTable id="select_fs"
									name="csAcceptInfoEntryVO.csApplicationVO.serviceType"
									value="${csAcceptInfoEntryVO.csApplicationVO.serviceType}"
									nullOption="true" tableName="APP___PAS__DBUSER.T_SERVICE_TYPE" />
							</div>
						</div>
						<div class="col-xs-28 col-sm-4 col-md-4 column-left "
							id="xqhinput">
							<label><input type="checkbox" name="flag" data-nolabel="false"
								data-intable="false" data-title="免填单" />免填单</label>
						</div>
						<!--业务员代办跳转-->
						<div class="hide" id="ywyu_db">
							<div class="col-xs-8 col-sm-5 col-md-5 column-left">业务员代码</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<div class="button custom-select">
									<select>
										<option>无</option>
									</select>
									<%-- 								<Field:codeTable id="agentId" cssClass="combox" name="" --%>
									<%-- 									tableName="APP___PAS__DBUSER.T_CERTI_TYPE" value="" nullOption="true" /> --%>
								</div>
							</div>
							<div class="col-xs-8 col-sm-5 col-md-5 column-left">新业务员代码</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<input type="text" class="form-control"
									name="csAcceptInfoEntryVO.csApplicationVO.agentId" id="agentId" />
							</div>
							<div class="col-xs-28 col-sm-4 col-md-4"></div>
							<div class="col-xs-8 col-sm-5 col-md-5 column-left">代办人姓名</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<input type="text" class="form-control"
									name="csAcceptInfoEntryVO.csApplicationVO.agentName" />
							</div>
							<div class="col-xs-8 col-sm-5 col-md-5 column-left">代办人联系电话</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<input type="expandMobile" class="form-control"
									name="csAcceptInfoEntryVO.csApplicationVO.agentTel" />
							</div>
							<div class="col-xs-28 col-sm-4 col-md-4 "></div>
							<div class="col-xs-8 col-sm-5 col-md-5 column-left">代办人证件类型</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<div class="button custom-select">
									<Field:codeTable nullOption="true"
										name="csAcceptInfoEntryVO.csApplicationVO.agentCertiType"
										tableName="APP___PAS__DBUSER.T_CERTI_TYPE" />
								</div>
							</div>
							<div class="col-xs-8 col-sm-5 col-md-5 column-left">代办人证件号码</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<input type="text" class="form-control"
									name="csAcceptInfoEntryVO.csApplicationVO.agentCertiCode" />
							</div>
							<div class="col-xs-8 col-sm-4 col-md-4 "></div>
							<div class="col-xs-8 col-sm-5 col-md-5 column-left">绩优等级</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<input type="text" class="form-control"
									name="csAcceptInfoEntryVO.csApplicationVO.agentLevel" />
							</div>
						</div>
						<!--业务员代办跳转结束-->
						<!--其他人代办跳转-->
						<div class="hide" id="other_db">

							<div class="col-xs-8 col-sm-5 col-md-5 column-left">代办人姓名</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<input type="text" class="form-control"
									name="csAcceptInfoEntryVO.csApplicationVO.agentName" />
							</div>
							<div class="col-xs-8 col-sm-5 col-md-5 column-left">代办人联系电话</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<input type="text" class="form-control"
									name="csAcceptInfoEntryVO.csApplicationVO.agentTel" />
							</div>
							<div class="col-xs-28 col-sm-4 col-md-4 "></div>
							<div class="col-xs-8 col-sm-5 col-md-5 column-left">代办人证件类型</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<div class="button custom-select">
									<Field:codeTable nullOption="true"
										name="csAcceptInfoEntryVO.csApplicationVO.agentCertiType"
										tableName="APP___PAS__DBUSER.T_CERTI_TYPE" />
								</div>
							</div>
							<div class="col-xs-8 col-sm-5 col-md-5 column-left">代办人证件号码</div>
							<div class="col-xs-20 col-sm-7 col-md-7 column-right">
								<input type="text" class="form-control"
									name="csAcceptInfoEntryVO.csApplicationVO.agentCertiCode" />
							</div>
							<div class="col-xs-8 col-sm-4 col-md-4 "></div>
						</div>
						<div class="col-xs-28 col-sm-28 col-md-28 slxxlr-but">
							<button type="submit" class="btn btn-save btn-lg">保存申请信息</button>
						</div>
					</form>
				</section>
				<!--申请信息录入结束-->
				<!--申请变更项目开始-->
				<section id="panelAddAply" hidden="hidden">
					<div class="container-fluid">
						<div class="row-mcss rowheight" id="panelAddAplyDiv">
							<s:iterator value="servicePrds" var="var">
								<s:if test="ROWNUM == 10">
									<div id="all" class="hide">
								</s:if>
								<div class="col-xs-28 col-sm-9 col-md-9 ">
									<label><input type="checkbox" value="${SERVICE_CODE}" onclick="panelAddAplyClick(this)"
										data-title="${SERVICE_NAME}" />${SERVICE_NAME}</label>
								</div>
					
							</s:iterator>
						</div>
						<div class="col-xs-28 col-sm-10 col-md-10"></div>
						<DIV id="showAll" class="dataColor">显示所有</DIV>
					</div>
					<div class="col-xs-28 col-sm-28 col-md-28 slxxlr-but row-mcss-from">
						<button class="btn btn-save btn-md" type="button"
							onclick="queryAcceptPolicy()">确定</button>
					</div>
					<div class="row-mcss hide">
						<div class="col-xs-28 col-sm-28 col-md-28" id="applyTableDiv"></div>
						<div class="col-xs-28 col-sm-28 col-md-28 slxxlr-but">
							<button class="btn btn-save btn-lg" type="button"
								onclick="saveAcceptMessage()">保存受理信息</button>
						</div>
					</div>
				</section>
				<!--申请变更项目结束-->
				<!-- 标识信息开始-->
				<section id="identity" hidden="hidden">
					<div class="container-fluid">
						<div class="row-mcss rowheight">
							<div class="col-xs-28 col-sm-28 col-md-28"
								id="acceptResultTableDiv"></div>
							<form
								action="<%=basePath%>/mob/csAccept/showAcceptAndArapInfo_PA_mobCsEntryAction.action?tryFlag=1"
								method="post">
															<input type="hidden" id="changeIdFlag" name="changeId"/>
							<input type="hidden" id="customerIdFlag" name="customerId" value="${csAcceptInfoEntryVO.customerVO.customerId }" /> 
								<div class="col-xs-28 col-sm-28 col-md-28 slxxlr-but">
									<button class="btn btn-save btn-lg" type="button"
										onclick="saveFlagInfo()">保存标识信息</button>
									<button class="btn btn-next btn-lg" type="submit">下一步</button>
								</div>
							</form>
						</div>
					</div>
				</section>
				<!-- 标识信息结束-->
				<!--申请信息录入结束-->
			</article>
		</section>
	</article>
</body>
<script src="<%=basePath%>/mobcss/js/comm.js"></script>
<script src="<%=basePath%>/mobcss/plugins/bootstrap/js/collapse.js"></script>
<script src="<%=basePath%>/mobcss/js/openView.js"></script>
<script src="<%=basePath%>/mobcss/js/bqshouli/step1AcceptInfoEntry1.js"></script>
<script src="<%=basePath%>/mobcss/plugins/mobiscroll/js/mobiscroll_all.js"></script>
</html>