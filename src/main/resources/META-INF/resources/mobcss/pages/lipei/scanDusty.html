<!DOCTYPE html>
<html>
<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
		<title>扫描回销</title>
		<link rel="stylesheet" href="../../css/common.css" />
		<link rel="stylesheet" href="../../plugins/checkBoxRadio/customCheckBoxRadio.css" />
		<link rel="stylesheet" href="../../css/customSelect.css" />
		<link rel="stylesheet"  href="../../css/lipei.css"/>
	</head>
	<body>
		<article class="container-fluid fixedtop">
			<!--进度条开始-->
			   <nav class="row text-center jdt">
			      <div class="col-md-7 col-xs-7">
			         <span class="slxxlr-orange-yuan">1</span>
			          <span class="slxxlr-orange"></span>
			          <span class="slxxlr-orange-font">签收核对案件信息</span>
			      </div>
			      <div class="col-md-7 col-xs-7">
			          <span class="slxxlr-blue-yuan">2</span>
			          <span class="slxxlr-blue"></span> 
			          <span class="slxxlr-blue-font">扫描/回销索赔单证</span>
			      </div>
			      <div class="col-md-7 col-xs-7">
			          <span class="slxxlr-gray-yuan">3</span>
			          <span class="slxxlr-gray"></span> 
			          <span class="slxxlr-gray-font">保单挂起/解挂</span>
			      </div>
			      <div class="col-md-7 col-xs-7">
			          <span class="slxxlr-gray-yuan">4</span>
			          <span class="slxxlr-gray"></span>
			          <span class="slxxlr-gray-font">签收确认</span> 
			      </div>
			   </nav>
			<!--进度条结束-->
			
			<!--单证列表开始-->
     <section id="ybdzlb" >
        <table class="table table-bordered text-center">
    		<tr class="active" >
    			<th>序号</th>
    			<th>单证代码-单证名称</th>
    		</tr>
    		<tr>
    			<td>1</td>
    			<td class="text-left">clm3401-保险合同原件、复印件。非保险责任终止类理赔申请，可不提交</td>
    		</tr>
    		<tr>
    			<td>2</td>
    			<td class="text-left">clm3402-索赔申请书（包含委托授权书和付款转账授权书）及申请人身份证明原件</td>
    		</tr>
    		<tr>
    			<td>3</td>
    			<td class="text-left">clm3403-受托人身份证明原件</td>
    		</tr>
    		<tr>
    			<td>4</td>
    			<td class="text-left">clm3404-申请人与出险人身份关系证明原件</td>
    		</tr>
    		<tr>
    			<td>5</td>
    			<td class="text-left">clm3405-申请人存折或银行卡复印件</td>
    		</tr>
    		<tr>
    			<td>6</td>
    			<td class="text-left">clm3406-出险事故证明原件</td>
    		</tr>
    		<tr>
    			<td>7</td>
    			<td class="text-left">clm3407-医疗诊断证明书原件和门/急诊病历</td>
    		</tr>
    		<tr>
    			<td>8</td>
    			<td class="text-left">clm3408-出院小结或住院病历</td>
    		</tr>
    		<tr>
    			<td>9</td>
    			<td class="text-left">clm3409-医疗发票和费用明细清单（门诊可为处方、住院为住院费用明细汇总清单）</td>
    		</tr>
    		<tr>
    			<td>10</td>
    			<td class="text-left">clm3410-医疗费用分割单/第三方给付凭证原件</td>
    		</tr>
    	</table>		    
	  </section>
     <!--单证列表结束-->
			
			
			<!--资料上载开始-->
	        <section id="fileLoad">
	  		   <article class="well-mcss col-md-7 col-xs-7">
	  			<h5>身份证正面</h5>
	  			<div class="col-md-28 col-xs-28">
	  				<label class="lblFile" for="file1"></label>
	  				<input type="file" class="hide" id="file1" accept="image/*" onchange="uploadImg(this)"/>
	  			   <img src="../../img/imgDemo.png" width="100%" />
	  			</div>
	  		    <div class="col-md-28 col-xs-28 text-center">
	  		    	<div class="row splitRow"></div>
	  		    	<input type="button" class="btn btn-save" value="上传" />
	  		    </div>
	  		</article>
	  		<article class="well-mcss col-md-7 col-xs-7">
	  			<h5>身份证反面</h5>
	  			<div class="col-md-28 col-xs-28">
	  				<label class="lblFile" for="file2"></label>
	  				<input type="file" class="hide" id="file2" accept="image/*" onchange="uploadImg(this)"/>
	  			   <img src="../../img/imgDemo.png" width="100%"/>
	  			</div>
	  		    <div class="col-md-28 col-xs-28 text-center">
	  		    	<div class="row splitRow"></div>
	  		    	<input type="button" class="btn btn-save" value="上传" />
	  		    </div>
	  		</article>
	  		<article class="well-mcss col-md-7 col-xs-7">
	  			<h5>保险合同</h5>
	  			<div class="col-md-28 col-xs-28">
	  				<label class="lblFile" for="file3"></label>
	  				<input type="file" class="hide" id="file3" accept="image/*" onchange="uploadImg(this)"/>
	  			   <img src="../../img/imgDemo.png" width="100%"/>
	  			</div>
	  		    <div class="col-md-28 col-xs-28 text-center">
	  		    	<div class="row splitRow"></div>
	  		    	<input type="button" class="btn btn-save" value="上传" />
	  		    </div>
	  		</article>
	  		<article class="well-mcss col-md-7 col-xs-7">
	  			<h5>银行存折</h5>
	  			<div class="col-md-28 col-xs-28">
	  				<label class="lblFile" for="file4"></label>
	  				<input type="file" class="hide" id="file4" accept="image/*" onchange="uploadImg(this)"/>
	  			   <img src="../../img/imgDemo.png" width="100%"/>
	  			</div>
	  		    <div class="col-md-28 col-xs-28 text-center">
	  		    	<div class="row splitRow"></div>
	  		    	<input type="button" class="btn btn-save" value="上传" />
	  		    </div>
	  		 </article>
			</section>
			<!--资料上载结束-->
			
			  <div class="row splitRow"></div>
			  <section class="row text-center">
			  		<button class="btn btn-back btn-lg" type="button" onclick="javascript:history.back()">返回</button>
		          	<button class="btn btn-save btn-lg" type="button">添加资料</button>
		          	<button class="btn btn-save btn-lg" type="button">保存</button>
		          <div class="btn-group dropup">
					  <button type="button" class="btn btn-save btn-lg">打印</button>
					  <button type="button" class="btn btn-save dropdown-toggle btn-lg" data-toggle="dropdown" aria-expanded="false">
					    <span class="caret"></span>
					    <span class="sr-only">Toggle Dropdown</span>
					  </button>
					  <ul class="dropdown-menu" role="menu">
					    <li><a href="#">赔案号条形码</a></li>
					    <li><a href="#">索赔文件签收清单</a></li>
					    <li><a href="#">其他一</a></li>
					    <li role="separator" class="divider"></li>
					    <li><a href="#">其他二</a></li>
					  </ul>
					</div>
		          	<a href="hang.html" class="btn btn-next btn-lg" type="button">下一步</a>
			  	</section>
			 </div>
			</div>
			<!--资料上载结束-->
		</article>
	</body>
	        
	</body>
	<script src="../../js/comm.js" ></script>
	<!--<script  src="../../plugins/bootstrap/js/collapse.js" ></script>-->
	<!--<script type="text/javascript" src="../../plugins/checkBoxRadio/checkBoxRadio.js" ></script>-->
	<!--<script type="text/javascript" src="../../res/jquery/jquery-table-plugin.js" ></script>-->
    <script src="../../js/openView.js" ></script>
    <script src="../../js/lipei/scanDusty.js"></script>
</html>
