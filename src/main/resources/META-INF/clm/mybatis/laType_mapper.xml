<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.impl.util">

	<sql id="laTypeWhereCondition">
		<if test=" name != null and name != ''  "><![CDATA[ AND A.NAME = #{name} ]]></if>
		<if test=" code != null and code != ''  "><![CDATA[ AND A.CODE = #{code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryLaTypeByCodeCondition">
		<if test=" code != null and code != '' "><![CDATA[ AND A.CODE = #{code} ]]></if>
	<if test="disease_type==1 "> 
	       	<![CDATA[ and A.DISEASE_TYPE  =${disease_type}]]>
	</if>
	<if test="disease_type==2 "> 
	       	<![CDATA[ and A.DISEASE_TYPE  =${disease_type} ]]>
	</if>
	<if test="disease_type==3 "> 
	       	<![CDATA[ and A.DISEASE_TYPE  =${disease_type} ]]>
	</if>	
	</sql>
	

<!-- 添加操作 -->
	<insert id="addLaType"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_LA_TYPE                      (
				NAME, CODE ) 
			VALUES (
				#{name, jdbcType=VARCHAR}, #{code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteLaType" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_LA_TYPE                       WHERE CODE = #{code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateLaType" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_LA_TYPE                       ]]>
		<set>
		<trim suffixOverrides=",">
			NAME = #{name, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE CODE = #{code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findLaTypeByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.CODE ,A.DISEASE_TYPE  FROM APP___CLM__DBUSER.T_LA_TYPE                       A WHERE 1 = 1  ]]>
		<include refid="queryLaTypeByCodeCondition" />
		<![CDATA[ ORDER BY A.CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapLaType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.CODE FROM APP___CLM__DBUSER.T_LA_TYPE                       A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllLaType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.NAME, A.CODE FROM APP___CLM__DBUSER.T_LA_TYPE                       A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findLaTypeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_LA_TYPE                       A WHERE 1 = 1  ]]>
		<if test="code !=null and code != ''"> 
	       	<![CDATA[ and A.CODE  like '%${code}%' ]]>
		</if>		
		<if test="name !=null and name != ''"> 
	       	<![CDATA[ and A.NAME  like '%${name}%' ]]>
		</if>
		<if test="disease_type==1 "> 
	       	<![CDATA[ and A.DISEASE_TYPE  =${disease_type}]]>
		</if>	
		<if test="disease_type==2 "> 
	       	<![CDATA[ and A.DISEASE_TYPE  =${disease_type} ]]>
		</if>
		<if test="disease_type==3 "> 
	       	<![CDATA[ and A.DISEASE_TYPE  =${disease_type} ]]>
		</if>	
	</select>

<!-- 分页查询操作 -->
	<select id="queryLaTypeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.NAME, B.CODE FROM (
					SELECT ROWNUM RN, A.NAME, A.CODE FROM APP___CLM__DBUSER.T_LA_TYPE                       A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<if test="code !=null and code != ''"> 
	       	<![CDATA[ and A.CODE  like '%${code}%' ]]>
		</if>		
		<if test="name !=null and name != ''"> 
	       	<![CDATA[ and A.NAME  like '%${name}%' ]]>
		</if>	
		<if test="disease_type==1 "> 
	       	<![CDATA[ and A.DISEASE_TYPE  =${disease_type} ]]>
		</if>	
		<if test="disease_type==2 "> 
	       	<![CDATA[ and A.DISEASE_TYPE  =${disease_type} ]]>
		</if>
		<if test="disease_type==3 "> 
	       	<![CDATA[ and A.DISEASE_TYPE  =${disease_type}]]>
		</if>	
		<![CDATA[ ORDER BY A.CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
