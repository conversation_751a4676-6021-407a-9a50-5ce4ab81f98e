<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimLiabDetailInfoPO">

	<sql id="claimLiabDetailInfoWhereCondition">
		<if test=" detail_info_id  != null "><![CDATA[ AND A.DETAIL_INFO_ID = #{detail_info_id} ]]></if>
		<if test=" item_code != null and item_code != ''  "><![CDATA[ AND A.ITEM_CODE = #{item_code} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimLiabDetailInfoByDetailInfoIdCondition">
		<if test=" detail_info_id  != null "><![CDATA[ AND A.DETAIL_INFO_ID = #{detail_info_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimLiabDetailInfo"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="detail_info_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_LIAB_DETAIL_INFO__DETA.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_LIAB_DETAIL_INFO(
				DETAIL_INFO_ID, INSERT_TIMESTAMP, UPDATE_BY, INSERT_TIME, ITEM_CODE, UPDATE_TIMESTAMP, UPDATE_TIME, 
				INSERT_BY, CASE_ID ) 
			VALUES (
				#{detail_info_id, jdbcType=NUMERIC}, CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , SYSDATE , #{item_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, SYSDATE 
				, #{insert_by, jdbcType=NUMERIC} , #{case_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimLiabDetailInfo" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_LIAB_DETAIL_INFO WHERE DETAIL_INFO_ID = #{detail_info_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimLiabDetailInfo" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_LIAB_DETAIL_INFO ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			ITEM_CODE = #{item_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			UPDATE_TIME = SYSDATE , 
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE DETAIL_INFO_ID = #{detail_info_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimLiabDetailInfoByDetailInfoId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DETAIL_INFO_ID, A.ITEM_CODE, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_LIAB_DETAIL_INFO A WHERE 1 = 1  ]]>
		<include refid="queryClaimLiabDetailInfoByDetailInfoIdCondition" />
		<![CDATA[ ORDER BY A.DETAIL_INFO_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimLiabDetailInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DETAIL_INFO_ID, A.ITEM_CODE, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_LIAB_DETAIL_INFO A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DETAIL_INFO_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimLiabDetailInfo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DETAIL_INFO_ID, A.ITEM_CODE, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_LIAB_DETAIL_INFO A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimLiabDetailInfoWhereCondition" />
		<![CDATA[ ORDER BY A.DETAIL_INFO_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimLiabDetailInfoTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_LIAB_DETAIL_INFO A WHERE 1 = 1  ]]>
		<include refid="claimLiabDetailInfoWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimLiabDetailInfoForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.DETAIL_INFO_ID, B.ITEM_CODE, 
			B.CASE_ID FROM (
					SELECT ROWNUM RN, A.DETAIL_INFO_ID, A.ITEM_CODE, 
			A.CASE_ID FROM APP___CLM__DBUSER.T_CLAIM_LIAB_DETAIL_INFO A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DETAIL_INFO_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
