<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.QualityCheckProjectPO">

<!-- 根据TASKID查询质检项目 -->
<select id="findQualityCheckProjectByTaskId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.REVISE_CHECK_RESULT,A.REVISE_REMARK,A.CHECK_RESULT,
								A.REMARK,
								B.GIST_DESC,
								B.GIST_ID 
						from APP___CLM__DBUSER.T_CLAIM_AFC_DETAIL A,APP___CLM__DBUSER.T_CLAIM_AFC_GIST B 
						where A.GIST_ID=B.GIST_ID AND A.TASK_ID=#{task_id}]]>
	</select>
 <!-- 修改操作 -->
    <update id="updateQualityCheckProjectByGistId" parameterType="java.util.Map">
        <![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_AFC_DETAIL ]]>
        <set>
            <trim suffixOverrides=",">
                REVISE_CHECK_RESULT = #{revise_check_result, jdbcType=NUMERIC} ,
                REVISE_REMARK=#{revise_remark, jdbcType=VARCHAR} ,
            </trim>
        </set>
        <![CDATA[ WHERE gist_id = #{gist_id} ]]>
    </update>
</mapper>
