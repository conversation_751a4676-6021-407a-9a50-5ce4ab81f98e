<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimBeneMobilePO">

	<sql id="claimPayeeMobileWhereCondition">
		<if test=" mobile_payee_id  != null "><![CDATA[ AND A.MOBILE_PAYEE_ID = #{mobile_payee_id} ]]></if>
		<if test=" mobile_payee_no  != null "><![CDATA[ AND A.MOBILE_PAYEE_NO = #{mobile_payee_no} ]]></if>
		<if test=" mobile_payee_relation != null and mobile_payee_relation != ''  "><![CDATA[ AND A.MOBILE_PAYEE_RELATION = #{mobile_payee_relation} ]]></if>
		<if test=" mobile_case_id  != null "><![CDATA[ AND A.MO<PERSON><PERSON>_CASE_ID = #{mobile_case_id} ]]></if>
		<if test=" mobile_payee_sex  != null "><![CDATA[ AND A.MOBILE_PAYEE_SEX = #{mobile_payee_sex} ]]></if>
		<if test=" mobile_payee_nation != null and mobile_payee_nation != ''  "><![CDATA[ AND A.MOBILE_PAYEE_NATION = #{mobile_payee_nation} ]]></if>
		<if test=" mobile_payee_certi_no != null and mobile_payee_certi_no != ''  "><![CDATA[ AND A.MOBILE_PAYEE_CERTI_NO = #{mobile_payee_certi_no} ]]></if>
		<if test=" mobile_account_name != null and mobile_account_name != ''  "><![CDATA[ AND A.MOBILE_ACCOUNT_NAME = #{mobile_account_name} ]]></if>
		<if test=" mobile_pay_mode != null and mobile_pay_mode != ''  "><![CDATA[ AND A.MOBILE_PAY_MODE = #{mobile_pay_mode} ]]></if>
		<if test=" mobile_payee_certi_type != null and mobile_payee_certi_type != ''  "><![CDATA[ AND A.MOBILE_PAYEE_CERTI_TYPE = #{mobile_payee_certi_type} ]]></if>
		<if test=" mobile_payee_certi_end  != null  and  mobile_payee_certi_end  != ''  "><![CDATA[ AND A.MOBILE_PAYEE_CERTI_END = #{mobile_payee_certi_end} ]]></if>
		<if test=" mobile_payee_certi_start  != null  and  mobile_payee_certi_start  != ''  "><![CDATA[ AND A.MOBILE_PAYEE_CERTI_START = #{mobile_payee_certi_start} ]]></if>
		<if test=" mobile_account_no != null and mobile_account_no != ''  "><![CDATA[ AND A.MOBILE_ACCOUNT_NO = #{mobile_account_no} ]]></if>
		<if test=" mobile_payee_name != null and mobile_payee_name != ''  "><![CDATA[ AND A.MOBILE_PAYEE_NAME = #{mobile_payee_name} ]]></if>
		<if test=" mobile_payee_birth  != null  and  mobile_payee_birth  != ''  "><![CDATA[ AND A.MOBILE_PAYEE_BIRTH = #{mobile_payee_birth} ]]></if>
		<if test=" mobile_bank_code != null and mobile_bank_code != ''  "><![CDATA[ AND A.MOBILE_BANK_CODE = #{mobile_bank_code} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimPayeeMobileByMobilePayeeIdCondition">
		<if test=" mobile_payee_id  != null "><![CDATA[ AND A.MOBILE_PAYEE_ID = #{mobile_payee_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimPayeeMobile"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="mobile_payee_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_PAYEE_MOBILE_MOBILE_PA.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_PAYEE_MOBILE(
				MOBILE_PAYEE_ID, MOBILE_PAYEE_NO, MOBILE_PAYEE_RELATION, INSERT_TIME, MOBILE_CASE_ID, UPDATE_TIME, MOBILE_PAYEE_SEX, 
				MOBILE_PAYEE_NATION, INSERT_TIMESTAMP, MOBILE_PAYEE_CERTI_NO, UPDATE_BY, MOBILE_ACCOUNT_NAME, MOBILE_PAY_MODE, MOBILE_PAYEE_CERTI_TYPE, 
				MOBILE_PAYEE_CERTI_END, MOBILE_PAYEE_CERTI_START, UPDATE_TIMESTAMP, MOBILE_ACCOUNT_NO, MOBILE_PAYEE_NAME, INSERT_BY, MOBILE_PAYEE_BIRTH, 
				MOBILE_BANK_CODE ) 
			VALUES (
				#{mobile_payee_id, jdbcType=NUMERIC}, #{mobile_payee_no, jdbcType=NUMERIC} , #{mobile_payee_relation, jdbcType=VARCHAR} , SYSDATE , #{mobile_case_id, jdbcType=NUMERIC} , SYSDATE , #{mobile_payee_sex, jdbcType=NUMERIC} 
				, #{mobile_payee_nation, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{mobile_payee_certi_no, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{mobile_account_name, jdbcType=VARCHAR} , #{mobile_pay_mode, jdbcType=VARCHAR} , #{mobile_payee_certi_type, jdbcType=VARCHAR} 
				, #{mobile_payee_certi_end, jdbcType=DATE} , #{mobile_payee_certi_start, jdbcType=DATE} , CURRENT_TIMESTAMP, #{mobile_account_no, jdbcType=VARCHAR} , #{mobile_payee_name, jdbcType=VARCHAR} , #{insert_by, jdbcType=NUMERIC} , #{mobile_payee_birth, jdbcType=DATE} 
				, #{mobile_bank_code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimPayeeMobile" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_PAYEE_MOBILE WHERE MOBILE_PAYEE_ID = #{mobile_payee_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimPayeeMobile" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_PAYEE_MOBILE ]]>
		<set>
		<trim suffixOverrides=",">
		    MOBILE_PAYEE_NO = #{mobile_payee_no, jdbcType=NUMERIC} ,
			MOBILE_PAYEE_RELATION = #{mobile_payee_relation, jdbcType=VARCHAR} ,
		    MOBILE_CASE_ID = #{mobile_case_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    MOBILE_PAYEE_SEX = #{mobile_payee_sex, jdbcType=NUMERIC} ,
			MOBILE_PAYEE_NATION = #{mobile_payee_nation, jdbcType=VARCHAR} ,
			MOBILE_PAYEE_CERTI_NO = #{mobile_payee_certi_no, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			MOBILE_ACCOUNT_NAME = #{mobile_account_name, jdbcType=VARCHAR} ,
			MOBILE_PAY_MODE = #{mobile_pay_mode, jdbcType=VARCHAR} ,
			MOBILE_PAYEE_CERTI_TYPE = #{mobile_payee_certi_type, jdbcType=VARCHAR} ,
		    MOBILE_PAYEE_CERTI_END = #{mobile_payee_certi_end, jdbcType=DATE} ,
		    MOBILE_PAYEE_CERTI_START = #{mobile_payee_certi_start, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			MOBILE_ACCOUNT_NO = #{mobile_account_no, jdbcType=VARCHAR} ,
			MOBILE_PAYEE_NAME = #{mobile_payee_name, jdbcType=VARCHAR} ,
		    MOBILE_PAYEE_BIRTH = #{mobile_payee_birth, jdbcType=DATE} ,
			MOBILE_BANK_CODE = #{mobile_bank_code, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE MOBILE_PAYEE_ID = #{mobile_payee_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimPayeeMobileByMobilePayeeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MOBILE_PAYEE_ID, A.MOBILE_PAYEE_NO, A.MOBILE_PAYEE_RELATION, A.MOBILE_CASE_ID, A.MOBILE_PAYEE_SEX, 
			A.MOBILE_PAYEE_NATION, A.MOBILE_PAYEE_CERTI_NO, A.MOBILE_ACCOUNT_NAME, A.MOBILE_PAY_MODE, A.MOBILE_PAYEE_CERTI_TYPE, 
			A.MOBILE_PAYEE_CERTI_END, A.MOBILE_PAYEE_CERTI_START, A.MOBILE_ACCOUNT_NO, A.MOBILE_PAYEE_NAME, A.MOBILE_PAYEE_BIRTH, 
			A.MOBILE_BANK_CODE FROM APP___CLM__DBUSER.T_CLAIM_PAYEE_MOBILE A WHERE 1 = 1  ]]>
		<include refid="queryClaimPayeeMobileByMobilePayeeIdCondition" />
		<![CDATA[ ORDER BY A.MOBILE_PAYEE_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimPayeeMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MOBILE_PAYEE_ID, A.MOBILE_PAYEE_NO, A.MOBILE_PAYEE_RELATION, A.MOBILE_CASE_ID, A.MOBILE_PAYEE_SEX, 
			A.MOBILE_PAYEE_NATION, A.MOBILE_PAYEE_CERTI_NO, A.MOBILE_ACCOUNT_NAME, A.MOBILE_PAY_MODE, A.MOBILE_PAYEE_CERTI_TYPE, 
			A.MOBILE_PAYEE_CERTI_END, A.MOBILE_PAYEE_CERTI_START, A.MOBILE_ACCOUNT_NO, A.MOBILE_PAYEE_NAME, A.MOBILE_PAYEE_BIRTH, 
			A.MOBILE_BANK_CODE FROM APP___CLM__DBUSER.T_CLAIM_PAYEE_MOBILE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.MOBILE_PAYEE_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimPayeeMobile" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.MOBILE_PAYEE_ID, A.MOBILE_PAYEE_NO, A.MOBILE_PAYEE_RELATION, A.MOBILE_CASE_ID, A.MOBILE_PAYEE_SEX, 
			A.MOBILE_PAYEE_NATION, A.MOBILE_PAYEE_CERTI_NO, A.MOBILE_ACCOUNT_NAME, A.MOBILE_PAY_MODE, A.MOBILE_PAYEE_CERTI_TYPE, 
			A.MOBILE_PAYEE_CERTI_END, A.MOBILE_PAYEE_CERTI_START, A.MOBILE_ACCOUNT_NO, A.MOBILE_PAYEE_NAME, A.MOBILE_PAYEE_BIRTH, 
			A.MOBILE_BANK_CODE FROM APP___CLM__DBUSER.T_CLAIM_PAYEE_MOBILE A WHERE ROWNUM <=  1000  ]]>
		 <include refid="claimPayeeMobileWhereCondition" /> 
		<![CDATA[ ORDER BY A.MOBILE_PAYEE_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimPayeeMobileTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_PAYEE_MOBILE A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimPayeeMobileForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.MOBILE_PAYEE_ID, B.MOBILE_PAYEE_NO, B.MOBILE_PAYEE_RELATION, B.MOBILE_CASE_ID, B.MOBILE_PAYEE_SEX, 
			B.MOBILE_PAYEE_NATION, B.MOBILE_PAYEE_CERTI_NO, B.MOBILE_ACCOUNT_NAME, B.MOBILE_PAY_MODE, B.MOBILE_PAYEE_CERTI_TYPE, 
			B.MOBILE_PAYEE_CERTI_END, B.MOBILE_PAYEE_CERTI_START, B.MOBILE_ACCOUNT_NO, B.MOBILE_PAYEE_NAME, B.MOBILE_PAYEE_BIRTH, 
			B.MOBILE_BANK_CODE FROM (
					SELECT ROWNUM RN, A.MOBILE_PAYEE_ID, A.MOBILE_PAYEE_NO, A.MOBILE_PAYEE_RELATION, A.MOBILE_CASE_ID, A.MOBILE_PAYEE_SEX, 
			A.MOBILE_PAYEE_NATION, A.MOBILE_PAYEE_CERTI_NO, A.MOBILE_ACCOUNT_NAME, A.MOBILE_PAY_MODE, A.MOBILE_PAYEE_CERTI_TYPE, 
			A.MOBILE_PAYEE_CERTI_END, A.MOBILE_PAYEE_CERTI_START, A.MOBILE_ACCOUNT_NO, A.MOBILE_PAYEE_NAME, A.MOBILE_PAYEE_BIRTH, 
			A.MOBILE_BANK_CODE FROM APP___CLM__DBUSER.T_CLAIM_PAYEE_MOBILE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.MOBILE_PAYEE_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
