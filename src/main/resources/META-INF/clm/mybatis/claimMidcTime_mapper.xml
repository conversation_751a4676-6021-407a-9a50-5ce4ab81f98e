<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ClaimMidcTime">
    <sql id="claimMidcTimeWhereCondition">
		<if test=" qc_end_time != null and qc_end_time != ''  "><![CDATA[ AND A.QC_END_TIME = #{qc_end_time} ]]></if>
		<if test=" check_day  != null  and  check_day  != ''  "><![CDATA[ AND A.CHECK_DAY = #{check_day} ]]></if>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" qc_start_time != null and qc_start_time != ''  "><![CDATA[ AND A.QC_START_TIME = #{qc_start_time} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimMidcTimeByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimMidcTime"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_MIDC_TIME__LIST_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_MIDC_TIME(
				INSERT_TIMESTAMP, QC_END_TIME, CHECK_DAY, UPDATE_BY, PLAN_ID, INSERT_TIME, QC_START_TIME, 
				LIST_ID, UPDATE_TIME, UPDATE_TIMESTAMP, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{qc_end_time, jdbcType=VARCHAR} , #{check_day, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{plan_id, jdbcType=NUMERIC} , SYSDATE , #{qc_start_time, jdbcType=VARCHAR} 
				, #{list_id, jdbcType=NUMERIC} , SYSDATE , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimMidcTime" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TIME WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 删除操作 按计划Id-->	
	<delete id="deleteClaimMidcTimeByPlanId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TIME WHERE PLAN_ID = #{plan_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimMidcTime" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_MIDC_TIME ]]>
		<set>
		<trim suffixOverrides=",">
			QC_END_TIME = #{qc_end_time, jdbcType=VARCHAR} ,
		    CHECK_DAY = #{check_day, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
			QC_START_TIME = #{qc_start_time, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimMidcTimeByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QC_END_TIME, A.CHECK_DAY, A.PLAN_ID, A.QC_START_TIME, 
			A.LIST_ID FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TIME A WHERE 1 = 1  ]]>
		<include refid="queryClaimMidcTimeByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimMidcTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QC_END_TIME, A.CHECK_DAY, A.PLAN_ID, A.QC_START_TIME, 
			A.LIST_ID FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TIME A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimMidcTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.QC_END_TIME, A.CHECK_DAY, A.PLAN_ID, A.QC_START_TIME, 
			A.LIST_ID FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TIME A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimMidcTimeWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimMidcTimeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TIME A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimMidcTimeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.QC_END_TIME, B.CHECK_DAY, B.PLAN_ID, B.QC_START_TIME, 
			B.LIST_ID FROM (
					SELECT ROWNUM RN, A.QC_END_TIME, A.CHECK_DAY, A.PLAN_ID, A.QC_START_TIME, 
			A.LIST_ID FROM APP___CLM__DBUSER.T_CLAIM_MIDC_TIME A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
