<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimChecklistTrackPO">

	<sql id="claimChecklistTrackWhereCondition">
		<if test=" receive_by  != null "><![CDATA[ AND A.RECEIVE_BY = #{receive_by} ]]></if>
		<if test=" sup_cancel_reason != null and sup_cancel_reason != ''  "><![CDATA[ AND A.SUP_CANCEL_REASON = #{sup_cancel_reason} ]]></if>
		<if test=" receive_time  != null  and  receive_time  != ''  "><![CDATA[ AND A.RECEIVE_TIME = #{receive_time} ]]></if>
		<if test=" is_new_track  != null "><![CDATA[ AND A.IS_NEW_TRACK = #{is_new_track} ]]></if>
		<if test=" sup_status != null and sup_status != ''  "><![CDATA[ AND A.SUP_STATUS = #{sup_status} ]]></if>
		<if test=" receive_name != null and receive_name != ''  "><![CDATA[ AND A.RECEIVE_NAME = #{receive_name} ]]></if>
		<if test=" apply_time  != null  and  apply_time  != ''  "><![CDATA[ AND A.APPLY_TIME = #{apply_time} ]]></if>
		<if test=" apply_by  != null "><![CDATA[ AND A.APPLY_BY = #{apply_by} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" receive_org != null and receive_org != ''  "><![CDATA[ AND A.RECEIVE_ORG = #{receive_org} ]]></if>
		<if test=" apply_org != null and apply_org != ''  "><![CDATA[ AND A.APPLY_ORG = #{apply_org} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" sup_checklist != null and sup_checklist != ''  "><![CDATA[ AND A.SUP_CHECKLIST = #{sup_checklist} ]]></if>
		<if test=" sup_remark != null and sup_remark != ''  "><![CDATA[ AND A.SUP_REMARK = #{sup_remark} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimChecklistTrackByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	
	<sql id="queryClaimChecklistTrackByListIdConditionCaseId">
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimChecklistTrack"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="list_id">
			SELECT DEV_CLM.S_CLAIM_CHECKLIST_TRACK__LIST.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_CHECKLIST_TRACK(
				RECEIVE_BY, sup_cancel_reason, RECEIVE_TIME, IS_NEW_TRACK, INSERT_TIME, sup_status, RECEIVE_NAME, 
				APPLY_TIME, UPDATE_TIME, APPLY_BY, CASE_ID, INSERT_TIMESTAMP, RECEIVE_ORG, UPDATE_BY, 
				APPLY_ORG, LIST_ID, UPDATE_TIMESTAMP, INSERT_BY, SUP_CHECKLIST, SUP_REMARK ) 
			VALUES (
				#{receive_by, jdbcType=NUMERIC}, #{sup_cancel_reason, jdbcType=VARCHAR} , #{receive_time, jdbcType=TIMESTAMP} , #{is_new_track, jdbcType=NUMERIC} , SYSDATE , #{sup_status, jdbcType=VARCHAR} , #{receive_name, jdbcType=VARCHAR} 
				, #{apply_time, jdbcType=TIMESTAMP} , SYSDATE , #{apply_by, jdbcType=NUMERIC} , #{case_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{receive_org, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} 
				, #{apply_org, jdbcType=VARCHAR} , #{list_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{sup_checklist, jdbcType=VARCHAR} , #{sup_remark, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimChecklistTrack" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_CHECKLIST_TRACK WHERE LIST_ID = #{list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimChecklistTrack" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CHECKLIST_TRACK ]]>
		<set>
		<trim suffixOverrides=",">
		    RECEIVE_BY = #{receive_by, jdbcType=NUMERIC} ,
			sup_cancel_reason = #{sup_cancel_reason, jdbcType=VARCHAR} ,
		    RECEIVE_TIME = #{receive_time, jdbcType=TIMESTAMP} ,
		    IS_NEW_TRACK = #{is_new_track, jdbcType=NUMERIC} ,
			sup_status = #{sup_status, jdbcType=VARCHAR} ,
			RECEIVE_NAME = #{receive_name, jdbcType=VARCHAR} ,
		    APPLY_TIME = #{apply_time, jdbcType=TIMESTAMP} ,
			UPDATE_TIME = SYSDATE , 
		    APPLY_BY = #{apply_by, jdbcType=NUMERIC} ,
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
			RECEIVE_ORG = #{receive_org, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			APPLY_ORG = #{apply_org, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			SUP_CHECKLIST = #{sup_checklist, jdbcType=VARCHAR} ,
			SUP_REMARK = #{sup_remark, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimChecklistTrackByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_BY, A.SUP_CANCEL_REASON, A.RECEIVE_TIME, A.IS_NEW_TRACK, A.SUP_STATUS, A.RECEIVE_NAME, 
			A.APPLY_TIME, A.APPLY_BY, A.CASE_ID, A.RECEIVE_ORG, 
			A.APPLY_ORG, A.LIST_ID, A.SUP_CHECKLIST, A.SUP_REMARK FROM APP___CLM__DBUSER.T_CLAIM_CHECKLIST_TRACK A WHERE 1 = 1  ]]>
		<include refid="queryClaimChecklistTrackByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimChecklistTrack" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_BY, A.SUP_CANCEL_REASON, A.RECEIVE_TIME, A.IS_NEW_TRACK, A.SUP_STATUS, A.RECEIVE_NAME, 
			A.APPLY_TIME, A.APPLY_BY, A.CASE_ID, A.RECEIVE_ORG, 
			A.APPLY_ORG, A.LIST_ID, A.SUP_CHECKLIST, A.SUP_REMARK FROM APP___CLM__DBUSER.T_CLAIM_CHECKLIST_TRACK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimChecklistTrack" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT 
				A.APPLY_ORG ||'('||D.ORGAN_NAME||')' AS APPLY_ORG,
				B.REAL_NAME ||'('||B.USER_NAME||')' AS APPLY_BY_NAME,
				A.APPLY_TIME,
				A.SUP_CHECKLIST,
				A.SUP_REMARK,
				A.SUP_CANCEL_REASON,
				A.SUP_STATUS,
				A.RECEIVE_BY,
				A.RECEIVE_TIME,
				A.RECEIVE_ORG ||'('||E.ORGAN_NAME||') 'AS RECEIVE_ORG,
				(CASE 
				WHEN A.RECEIVE_BY IS NULL THEN 
				(SELECT S.SERVCOM_DESC FROM APP___CLM__DBUSER.T_SERVCOM S WHERE S.SERVCOM=A.RECEIVE_NAME)
				WHEN A.RECEIVE_BY IS NOT NULL THEN
				C.REAL_NAME ||'('||C.USER_NAME||')'
				END 
				) AS RECEIVE_NAME
				FROM APP___CLM__DBUSER.T_CLAIM_CHECKLIST_TRACK A 
				LEFT JOIN  APP___CLM__DBUSER.T_UDMP_USER B ON A.APPLY_BY=B.USER_ID
				LEFT JOIN  APP___CLM__DBUSER.T_UDMP_USER C ON  A.RECEIVE_BY=C.USER_ID
				LEFT JOIN  APP___CLM__DBUSER.T_UDMP_ORG D  ON A.APPLY_ORG=D.ORGAN_CODE
				LEFT JOIN  APP___CLM__DBUSER.T_UDMP_ORG E  ON A.RECEIVE_ORG=E.ORGAN_CODE  
				WHERE 1=1]]>
		<!-- <include refid="请添加查询条件" /> -->
		<include refid="queryClaimChecklistTrackByListIdConditionCaseId"/>
		<![CDATA[ ORDER BY A.APPLY_TIME ]]> 
	</select>
	

<!-- 查询个数操作 -->
	<select id="findClaimChecklistTrackTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_CHECKLIST_TRACK A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimChecklistTrackForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RECEIVE_BY, B.sup_cancel_reason, B.RECEIVE_TIME, B.IS_NEW_TRACK, B.sup_status, B.RECEIVE_NAME, 
			B.APPLY_TIME, B.APPLY_BY, B.CASE_ID, B.RECEIVE_ORG, 
			B.APPLY_ORG, B.LIST_ID, B.SUP_CHECKLIST, B.SUP_REMARK FROM (
					SELECT ROWNUM RN, A.RECEIVE_BY, A.SUP_CANCEL_REASON, A.RECEIVE_TIME, A.IS_NEW_TRACK, A.SUP_STATUS, A.RECEIVE_NAME, 
			A.APPLY_TIME, A.APPLY_BY, A.CASE_ID, A.RECEIVE_ORG, 
			A.APPLY_ORG, A.LIST_ID, A.SUP_CHECKLIST, A.SUP_REMARK FROM APP___CLM__DBUSER.T_CLAIM_CHECKLIST_TRACK A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	<select id="findClaimChecklistTrackByCaseIdAndIsNewTrack" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RECEIVE_BY, A.SUP_CANCEL_REASON, A.RECEIVE_TIME, A.IS_NEW_TRACK, A.SUP_STATUS, A.RECEIVE_NAME, 
			A.APPLY_TIME, A.APPLY_BY, A.CASE_ID, A.RECEIVE_ORG, 
			A.APPLY_ORG, A.LIST_ID, A.SUP_CHECKLIST, A.SUP_REMARK FROM APP___CLM__DBUSER.T_CLAIM_CHECKLIST_TRACK A WHERE 1 = 1  ]]>
		<include refid="claimChecklistTrackWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
</mapper>
