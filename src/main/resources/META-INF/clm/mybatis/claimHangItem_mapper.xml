<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimHangItemPO">

	<sql id="claimHangItemWhereCondition">
		<if test=" item_name != null and item_name != ''  "><![CDATA[ AND A.ITEM_NAME = #{item_name} ]]></if>
		<if test=" item_code != null and item_code != ''  "><![CDATA[ AND A.ITEM_CODE = #{item_code} ]]></if>
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimHangItemByItemIdCondition">
		<if test=" item_id  != null "><![CDATA[ AND A.ITEM_ID = #{item_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimHangItem"  useGeneratedKeys="true"  parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="item_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_HANG_ITEM__ITEM_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_HANG_ITEM(
				INSERT_TIMESTAMP, UPDATE_BY, INSERT_TIME, ITEM_NAME, ITEM_CODE, UPDATE_TIME, UPDATE_TIMESTAMP, 
				ITEM_ID, INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , 
				SYSDATE , #{item_name, jdbcType=VARCHAR} , #{item_code, jdbcType=VARCHAR}, 
				SYSDATE , CURRENT_TIMESTAMP, #{item_id, jdbcType=NUMERIC}, 
				#{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimHangItem" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_HANG_ITEM WHERE ITEM_CODE = #{item_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimHangItem" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_HANG_ITEM ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			ITEM_NAME = #{item_name, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    ITEM_ID = #{item_id, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE ITEM_CODE = #{item_code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimHangItemByItemId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_NAME, A.ITEM_CODE, 
			A.ITEM_ID FROM APP___CLM__DBUSER.T_CLAIM_HANG_ITEM A WHERE 1 = 1  ]]>
		<include refid="queryClaimHangItemByItemIdCondition" />
		<![CDATA[ ORDER BY A.ITEM_CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimHangItem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_NAME, A.ITEM_CODE, 
			A.ITEM_ID FROM APP___CLM__DBUSER.T_CLAIM_HANG_ITEM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ITEM_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimHangItem" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_NAME, A.ITEM_CODE, 
			A.ITEM_ID FROM APP___CLM__DBUSER.T_CLAIM_HANG_ITEM A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ITEM_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimHangItemTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_HANG_ITEM A WHERE 1 = 1  ]]>
		<include refid="claimHangItemWhereCondition" />
	</select>
	
<!-- 查询单条数据 -->
	<select id="findClaimHangItem" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT A.ITEM_ID, A.ITEM_CODE, A.ITEM_NAME, A.INSERT_TIME, A.INSERT_BY, A.INSERT_TIMESTAMP, A.UPDATE_BY, A.UPDATE_TIMESTAMP, A.UPDATE_TIME FROM APP___CLM__DBUSER.T_CLAIM_HANG_ITEM A WHERE 1 = 1  ]]>
		<include refid="claimHangItemWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimHangItemForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.ITEM_NAME, B.ITEM_CODE, 
			B.ITEM_ID FROM (
					SELECT ROWNUM RN, A.ITEM_NAME, A.ITEM_CODE, 
			A.ITEM_ID FROM APP___CLM__DBUSER.T_CLAIM_HANG_ITEM A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.ITEM_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
