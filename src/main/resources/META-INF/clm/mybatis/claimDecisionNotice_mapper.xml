<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimDecisionNoticePO">

	<sql id="claimDecisionNoticeWhereCondition">
		<if test=" claim_type != null and claim_type != ''  "><![CDATA[ AND A.CLAIM_TYPE = #{claim_type} ]]></if>
		<if test=" reject_code != null and reject_code != ''  "><![CDATA[ AND A.REJECT_CODE = #{reject_code} ]]></if>
		<if test=" specific_reasons != null and specific_reasons != ''  "><![CDATA[ AND A.SPECIFIC_REASONS = #{specific_reasons} ]]></if>
		<if test=" contract_bene_name != null and contract_bene_name != ''  "><![CDATA[ AND A.CONTRACT_BENE_NAME  like '%${contract_bene_name}%' ]]></if>
		<if test=" other_reason != null and other_reason != ''  "><![CDATA[ AND A.OTHER_REASON = #{other_reason} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" decision_notice_id  != null "><![CDATA[ AND A.DECISION_NOTICE_ID = #{decision_notice_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" decision_proof != null and decision_proof != ''  "><![CDATA[ AND A.DECISION_PROOF like '%${decision_proof}%'  ]]></if>
		<if test=" claim_decision != null and claim_decision != ''  "><![CDATA[ AND A.CLAIM_DECISION = #{claim_decision} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimDecisionNoticeByDecisionNoticeIdCondition">
		<if test=" decision_notice_id  != null "><![CDATA[ AND A.DECISION_NOTICE_ID = #{decision_notice_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimDecisionNotice"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="decision_notice_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_DECISION_NOTICE__DECIS.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE (
				CLAIM_TYPE, REJECT_CODE, INSERT_TIME, SPECIFIC_REASONS, CONTRACT_BENE_NAME, UPDATE_TIME, OTHER_REASON, 
				CASE_ID, INSERT_TIMESTAMP, DECISION_NOTICE_ID, POLICY_CODE, UPDATE_BY, DECISION_PROOF, CLAIM_DECISION, 
				UPDATE_TIMESTAMP, INSERT_BY, POLICY_ID ) 
			VALUES (
				#{claim_type, jdbcType=VARCHAR}, #{reject_code, jdbcType=VARCHAR} , SYSDATE , #{specific_reasons, jdbcType=VARCHAR} , #{contract_bene_name, jdbcType=VARCHAR} , SYSDATE , #{other_reason, jdbcType=VARCHAR} 
				, #{case_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{decision_notice_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{update_by, jdbcType=NUMERIC} , #{decision_proof, jdbcType=VARCHAR} , #{claim_decision, jdbcType=VARCHAR} 
				, CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimDecisionNotice" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE  WHERE DECISION_NOTICE_ID = #{decision_notice_id} ]]>
	</delete>
<!-- 删除操作 -->	
	<delete id="deleteClaimDecisionNoticeByCaseId" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE  WHERE CASE_ID = #{case_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimDecisionNotice" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE  ]]>
		<set>
		<trim suffixOverrides=",">
			CLAIM_TYPE = #{claim_type, jdbcType=VARCHAR} ,
			REJECT_CODE = #{reject_code, jdbcType=VARCHAR} ,
			SPECIFIC_REASONS = #{specific_reasons, jdbcType=VARCHAR} ,
			CONTRACT_BENE_NAME = #{contract_bene_name, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			OTHER_REASON = #{other_reason, jdbcType=VARCHAR} ,
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			DECISION_PROOF = #{decision_proof, jdbcType=VARCHAR} ,
			CLAIM_DECISION = #{claim_decision, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    NOTICE_FLAG = #{notice_flag, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE DECISION_NOTICE_ID = #{decision_notice_id} ]]>
	</update>
<!-- 修改操作 -->
	<update id="updateClaimDecisionNoticeNoticeFlag" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE  ]]>
		<set>
		<trim suffixOverrides=",">
		    NOTICE_FLAG = #{notice_flag, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE CASE_ID = #{case_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimDecisionNoticeByDecisionNoticeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.REJECT_CODE, A.SPECIFIC_REASONS, A.CONTRACT_BENE_NAME, A.OTHER_REASON, 
			A.CASE_ID, A.DECISION_NOTICE_ID, A.POLICY_CODE, A.DECISION_PROOF, A.CLAIM_DECISION, 
			A.POLICY_ID FROM APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE  A WHERE 1 = 1  ]]>
		<include refid="queryClaimDecisionNoticeByDecisionNoticeIdCondition" />
		<![CDATA[ ORDER BY A.DECISION_NOTICE_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimDecisionNotice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.REJECT_CODE, A.SPECIFIC_REASONS, A.CONTRACT_BENE_NAME, A.OTHER_REASON, 
			A.CASE_ID, A.DECISION_NOTICE_ID, A.POLICY_CODE, A.DECISION_PROOF, A.CLAIM_DECISION, 
			A.POLICY_ID FROM APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE  A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DECISION_NOTICE_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimDecisionNotice" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CLAIM_TYPE, A.REJECT_CODE, A.SPECIFIC_REASONS, A.CONTRACT_BENE_NAME, A.OTHER_REASON, 
			A.CASE_ID, A.DECISION_NOTICE_ID, A.POLICY_CODE, A.DECISION_PROOF, A.CLAIM_DECISION, 
			A.POLICY_ID,A.NOTICE_FLAG FROM APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE  A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimDecisionNoticeWhereCondition" />
		<![CDATA[ ORDER BY A.DECISION_NOTICE_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimDecisionNoticeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE  A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimDecisionNoticeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CLAIM_TYPE, B.REJECT_CODE, B.SPECIFIC_REASONS, B.CONTRACT_BENE_NAME, B.OTHER_REASON, 
			B.CASE_ID, B.DECISION_NOTICE_ID, B.POLICY_CODE, B.DECISION_PROOF, B.CLAIM_DECISION, 
			B.POLICY_ID FROM (
					SELECT ROWNUM RN, A.CLAIM_TYPE, A.REJECT_CODE, A.SPECIFIC_REASONS, A.CONTRACT_BENE_NAME, A.OTHER_REASON, 
			A.CASE_ID, A.DECISION_NOTICE_ID, A.POLICY_CODE, A.DECISION_PROOF, A.CLAIM_DECISION, 
			A.POLICY_ID FROM APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE  A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DECISION_NOTICE_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	<select id="findAllClaimDecisions" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.CONTRACT_BENE_NAME FROM APP___CLM__DBUSER.T_CLAIM_DECISION_NOTICE  A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimDecisionNoticeWhereCondition" />
	</select>
	
</mapper>
