<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.DisabilityPO">
<!--
	<sql id="disabilityWhereCondition">
		<if test=" rate  != null "><![CDATA[ AND A.RATE = #{rate} ]]></if>
		<if test=" name != null and name != ''  "><![CDATA[ AND A.NAME = #{name} ]]></if>
		<if test=" code != null and code != ''  "><![CDATA[ AND A.CODE = #{code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryDisabilityByCodeCondition">
		<if test=" code != null and code != '' "><![CDATA[ AND A.CODE = #{code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addDisability"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_DISABILITY(
				RATE, NAME, CODE ) 
			VALUES (
				#{rate, jdbcType=NUMERIC}, #{name, jdbcType=VARCHAR} , #{code, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteDisability" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_DISABILITY WHERE CODE = #{code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateDisability" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_DISABILITY ]]>
		<set>
		<trim suffixOverrides=",">
		    RATE = #{rate, jdbcType=NUMERIC} ,
			NAME = #{name, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE CODE = #{code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findDisabilityByCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE, A.NAME, A.CODE FROM APP___CLM__DBUSER.T_DISABILITY A WHERE 1 = 1  ]]>
		<include refid="queryDisabilityByCodeCondition" />
		<![CDATA[ ORDER BY A.CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapDisability" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE, A.NAME, A.CODE FROM APP___CLM__DBUSER.T_DISABILITY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllDisability" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE, A.NAME, A.CODE FROM APP___CLM__DBUSER.T_DISABILITY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CODE ]]> 
	</select>
<!-- 查询所有等級操作 -->
	<select id="findAllDisabilityOrder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE, A.NAME, A.CODE FROM APP___CLM__DBUSER.T_DISABILITY A WHERE A.CODE LIKE 'YB0_   ' ]]>
		<![CDATA[ ORDER BY A.CODE ]]> 
	</select>
<!-- 查询所有等級對應的損失程度操作 -->
	<select id="findAllDisabilityByOrder" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE, A.NAME, A.CODE FROM APP___CLM__DBUSER.T_DISABILITY A WHERE A.CODE LIKE SUBSTR(#{code }, 0, 4)||'%' AND A.CODE != #{code}]]>
		<![CDATA[ ORDER BY A.CODE ]]> 
	</select>
<!-- 查询給付比例操作 -->
	<select id="findPayRateByOrderAndName" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.RATE, A.NAME, A.CODE FROM APP___CLM__DBUSER.T_DISABILITY A WHERE A.CODE = #{code}]]>
		<![CDATA[ ORDER BY A.CODE ]]> 
	</select>	
<!-- 查询个数操作 -->
	<select id="findDisabilityTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_DISABILITY A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryDisabilityForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.RATE, B.NAME, B.CODE FROM (
					SELECT ROWNUM RN, A.RATE, A.NAME, A.CODE FROM APP___CLM__DBUSER.T_DISABILITY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
