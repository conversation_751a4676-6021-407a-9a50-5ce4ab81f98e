<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimPayPO">
	
	<sql id="claimPayWhereCondition">
		<if test=" prem_arap_id  != null "><![CDATA[ AND A.PREM_ARAP_ID = #{prem_arap_id} ]]></if>
		<if test=" adjust_busi_id  != null "><![CDATA[ AND A.ADJUST_BUSI_ID = #{adjust_busi_id} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" advance_flag  != null "><![CDATA[ AND A.ADVANCE_FLAG = #{advance_flag} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" is_instalment  != null "><![CDATA[ AND A.IS_INSTALMENT = #{is_instalment} ]]></if>
		<if test=" claim_pay_id  != null "><![CDATA[ AND A.CLAIM_PAY_ID = #{claim_pay_id} ]]></if>
		<if test=" bene_rate  != null "><![CDATA[ AND A.BENE_RATE = #{bene_rate} ]]></if>
		<if test=" pay_amount  != null "><![CDATA[ AND A.PAY_AMOUNT = #{pay_amount} ]]></if>
		<if test=" pay_deno  != null "><![CDATA[ AND A.PAY_DENO = #{pay_deno} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" is_back_holder  != null "><![CDATA[ AND A.IS_BACK_HOLDER = #{is_back_holder} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" payee_id  != null "><![CDATA[ AND A.PAYEE_ID = #{payee_id} ]]></if>
		<if test=" pay_mole  != null "><![CDATA[ AND A.PAY_MOLE = #{pay_mole} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" bene_id  != null "><![CDATA[ AND A.BENE_ID = #{bene_id} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	    <if test=" get_mode_code != null and get_mode_code != ''  "><![CDATA[ AND A.GET_MODE_CODE = #{get_mode_code} ]]></if>
	    <if test=" payee_relation != null and payee_relation != ''  "><![CDATA[ AND A.PAYEE_RELATION = #{payee_relation} ]]></if>
	   <!--  需求分析任务 #140393 新核心个险支付计划页面功能优化  新增复选框 选定 返回投保人信息到受益人和领款人 by zhuyanrui start -->
	   <if test=" is_holder != null and is_holder != ''  "><![CDATA[ AND A.IS_HOLDER = #{is_holder} ]]></if>
	   <!--  #140393 end -->
	   <if test=" overthirty_unsuccesspay_flag != null "><![CDATA[ AND A.OVERTHIRTY_UNSUCCESSPAY_FLAG = #{overthirty_unsuccesspay_flag} ]]></if>
	</sql>

<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimPayByClaimPayIdCondition">
		<if test=" claim_pay_id  != null "><![CDATA[ AND A.CLAIM_PAY_ID = #{claim_pay_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimPay"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="claim_pay_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_PAY__CLAIM_PAY_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_PAY(
				PREM_ARAP_ID, ADJUST_BUSI_ID, UNIT_NUMBER, REMARK, ADVANCE_FLAG, CASE_NO, BUSI_PROD_CODE, 
				IS_INSTALMENT, INSERT_TIMESTAMP, UPDATE_BY, CLAIM_PAY_ID, BENE_RATE, PAY_AMOUNT, PAY_DENO, 
				BUSI_ITEM_ID, POLICY_ID, PAYEE_ID, INSERT_TIME, UPDATE_TIME, PAY_MOLE, CASE_ID, 
				BENE_ID, POLICY_CODE, UPDATE_TIMESTAMP, INSERT_BY, GET_MODE_CODE, IS_BACK_HOLDER,PAYEE_RELATION,BENE_HOLDER_RELATION,CONTRARY_PAY_FLAG,IS_HOLDER,OVERTHIRTY_UNSUCCESSPAY_FLAG) 
			VALUES (
				#{prem_arap_id, jdbcType=NUMERIC}, #{adjust_busi_id, jdbcType=NUMERIC} , #{unit_number, jdbcType=VARCHAR} , #{remark, jdbcType=VARCHAR} , #{advance_flag, jdbcType=NUMERIC} , #{case_no, jdbcType=VARCHAR} , #{busi_prod_code, jdbcType=VARCHAR} 
				, #{is_instalment, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{claim_pay_id, jdbcType=NUMERIC} , #{bene_rate, jdbcType=NUMERIC} , #{pay_amount, jdbcType=NUMERIC} , #{pay_deno, jdbcType=NUMERIC} 
				, #{busi_item_id, jdbcType=NUMERIC} , #{policy_id, jdbcType=NUMERIC} , #{payee_id, jdbcType=NUMERIC} , SYSDATE , SYSDATE , #{pay_mole, jdbcType=NUMERIC} , #{case_id, jdbcType=NUMERIC} 
				, #{bene_id, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC}
				, #{get_mode_code, jdbcType=VARCHAR}  , #{is_back_holder, jdbcType=NUMERIC},#{payee_relation, jdbcType=VARCHAR},#{bene_holder_relation, jdbcType=VARCHAR}, #{contrary_pay_flag, jdbcType=NUMERIC}
				, #{is_holder, jdbcType=NUMERIC}, #{overthirty_unsuccesspay_flag, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimPay" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_PAY WHERE CLAIM_PAY_ID = #{claim_pay_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimPay" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_PAY ]]>
		<set>
		<trim suffixOverrides=",">
		    PREM_ARAP_ID = #{prem_arap_id, jdbcType=NUMERIC} ,
		    ADJUST_BUSI_ID = #{adjust_busi_id, jdbcType=NUMERIC} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
		    ADVANCE_FLAG = #{advance_flag, jdbcType=NUMERIC} ,
			CASE_NO = #{case_no, jdbcType=VARCHAR} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
		    IS_INSTALMENT = #{is_instalment, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    BENE_RATE = #{bene_rate, jdbcType=NUMERIC} ,
		    PAY_AMOUNT = #{pay_amount, jdbcType=NUMERIC} ,
		    PAY_DENO = #{pay_deno, jdbcType=NUMERIC} ,
		    BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
		    PAYEE_ID = #{payee_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    PAY_MOLE = #{pay_mole, jdbcType=NUMERIC} ,
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
		    BENE_ID = #{bene_id, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			GET_MODE_CODE = #{get_mode_code, jdbcType=VARCHAR},
			PAYEE_RELATION = #{payee_relation, jdbcType=VARCHAR},
			IS_BACK_HOLDER =  #{is_back_holder, jdbcType=NUMERIC},
			BENE_HOLDER_RELATION = #{bene_holder_relation, jdbcType=VARCHAR},
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    CONTRARY_PAY_FLAG = #{contrary_pay_flag, jdbcType=NUMERIC} ,
		    IS_HOLDER = #{is_holder, jdbcType=NUMERIC} ,
		    OVERTHIRTY_UNSUCCESSPAY_FLAG = #{overthirty_unsuccesspay_flag, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE CLAIM_PAY_ID = #{claim_pay_id} ]]>
	</update>
	
	
	
	
	
<!-- 修改操作 -->
	<update id="updateClaimPayPremId" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_PAY  
		    set PREM_ARAP_ID = #{prem_arap_id, jdbcType=NUMERIC} , 
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} , 
			UPDATE_TIME = SYSDATE ,  
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP 
	         WHERE CLAIM_PAY_ID = #{claim_pay_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimPayByClaimPayId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.UNIT_NUMBER, A.REMARK, A.ADVANCE_FLAG, A.CASE_NO, A.BUSI_PROD_CODE, 
			A.IS_INSTALMENT, A.CLAIM_PAY_ID, A.BENE_RATE, A.PAY_AMOUNT, A.PAY_DENO, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_ID, A.PAY_MOLE, A.CASE_ID, 
			A.BENE_ID, A.POLICY_CODE,A.GET_MODE_CODE, A.IS_BACK_HOLDER,A.PAYEE_RELATION, A.BENE_HOLDER_RELATION,A.CONTRARY_PAY_FLAG,A.IS_HOLDER,A.OVERTHIRTY_UNSUCCESSPAY_FLAG FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE 1 = 1  ]]>
		<include refid="queryClaimPayByClaimPayIdCondition" />
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID ]]>
	</select>
	
	<!-- by caoyy_wb 根据caseId查询数据 -->
	<select id="queryClaimPayByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.PAYEE_ID, A.REMARK, A.ADVANCE_FLAG, 
			A.PAY_MOLE, A.IS_INSTALMENT, A.CASE_ID, A.BUSI_PROD_CODE, A.BENE_ID, A.POLICY_CODE, 
			A.CLAIM_PAY_ID, A.BENE_RATE, A.BUSI_ITEM_ID, 
	A.PAY_DENO, A.PAY_AMOUNT, A.POLICY_ID, A.IS_BACK_HOLDER, A.PAYEE_RELATION, A.BENE_HOLDER_RELATION,A.CONTRARY_PAY_FLAG,A.IS_HOLDER,A.OVERTHIRTY_UNSUCCESSPAY_FLAG FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE 1 = 1  ]]>
	<include refid="claimPayWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimPay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.UNIT_NUMBER, A.REMARK, A.ADVANCE_FLAG, A.CASE_NO, A.BUSI_PROD_CODE, 
			A.IS_INSTALMENT, A.CLAIM_PAY_ID, A.BENE_RATE, A.PAY_AMOUNT, A.PAY_DENO, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_ID, A.PAY_MOLE, A.CASE_ID, 
			A.BENE_ID, A.POLICY_CODE,A.PAYEE_RELATION,A.CONTRARY_PAY_FLAG,A.IS_HOLDER,A.OVERTHIRTY_UNSUCCESSPAY_FLAG FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID ]]>
	</select>

<!-- 查询所有操作 -->
<!-- 如果是预付 要按照支付比例进行计算 -->
	<select id="findAllClaimPayByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
          
SELECT A.PAY_MOLE  / A.PAY_DENO  BENE_RATE,TCL.CLAIM_LIAB_ID,
       A.PREM_ARAP_ID,
       A.ADJUST_BUSI_ID,
       A.PAYEE_ID,
       A.REMARK,
       A.ADVANCE_FLAG,
       A.PAY_MOLE,
       A.IS_INSTALMENT,
       A.CASE_ID,
       A.BUSI_PROD_CODE,
       A.BENE_ID,
       A.POLICY_CODE,
       A.CLAIM_PAY_ID,
       A.BENE_RATE,
     
       A.BUSI_ITEM_ID,
       A.PAY_DENO,
       A.POLICY_ID,
       A.PAYEE_RELATION,
       A.BENE_HOLDER_RELATION,
       CASE WHEN A.ADVANCE_FLAG='0' THEN 
       (TCL.CALC_PAY * A.PAY_MOLE) / A.PAY_DENO
       ELSE (  select nvl( ADVANCE_PAY,'0')/decode(ACTUAL_PAY,'','1','0','1',ACTUAL_PAY) 
          from   APP___CLM__DBUSER.T_CLAIM_CASE  where case_id=a.case_id)
         * (TCL.CALC_PAY * A.PAY_MOLE) / A.PAY_DENO  END  PAY_AMOUNT,
       TCL.LIAB_NAME,
       TCL.CLAIM_TYPE,
       (SELECT TCA.ACC_REASON
          FROM APP___CLM__DBUSER.T_CLAIM_ACCIDENT TCA, APP___CLM__DBUSER.T_CLAIM_CASE TCC
         WHERE TCA.ACCIDENT_ID = TCC.ACCIDENT_ID
           AND TCC.CASE_ID = TCL.CASE_ID) ACC_REASON,
       DECODE(A.ADVANCE_FLAG,'0','P005010000','1','P005140100','P005010000')    FEE_TYPE
  FROM APP___CLM__DBUSER.T_CLAIM_LIAB TCL, APP___CLM__DBUSER.T_CLAIM_PAY A
 WHERE TCL.CASE_ID = A.CASE_ID
   AND TCL.BUSI_ITEM_ID = A.BUSI_ITEM_ID
   AND tcl.busi_prod_code = a.busi_prod_code
   and a.adjust_busi_id is null
   AND TCL.LIAB_CONCLUSION <5  
   and a.case_id = #{case_id}
 
   
      ]]>
    <if test=" advance_flag  != null "><![CDATA[  and A.ADVANCE_FLAG = ${advance_flag} ]]></if>
	<![CDATA[ 
union all

SELECT A.PAY_MOLE  / A.PAY_DENO  BENE_RATE,null,
       A.PREM_ARAP_ID,
       A.ADJUST_BUSI_ID,
       A.PAYEE_ID,
       A.REMARK,
       A.ADVANCE_FLAG,
       A.PAY_MOLE,
       A.IS_INSTALMENT,
       A.CASE_ID,
       A.BUSI_PROD_CODE,
       A.BENE_ID,
       A.POLICY_CODE,
       A.CLAIM_PAY_ID,
       A.BENE_RATE,
       
       A.BUSI_ITEM_ID,
       A.PAY_DENO,
       A.POLICY_ID,
       A.PAYEE_RELATION,
       A.BENE_HOLDER_RELATION,
       A.PAY_AMOUNT,
       null,
       (SELECT min(TCC.claim_type)  FROM APP___CLM__DBUSER.T_CLAIM_LIAB TCC WHERE TCC.CASE_ID=A.CASE_ID AND    TCC.BUSI_ITEM_ID = A.BUSI_ITEM_ID
        AND TCC.BUSI_PROD_CODE = A.BUSI_PROD_CODE  ) ,
        (SELECT TCA.ACC_REASON
          FROM APP___CLM__DBUSER.T_CLAIM_ACCIDENT TCA, APP___CLM__DBUSER.T_CLAIM_CASE TCC
         WHERE TCA.ACCIDENT_ID = TCC.ACCIDENT_ID
           AND TCC.CASE_ID = A.CASE_ID) ACC_REASON,(SELECT TTT.CAP_FEE_CODE
             FROM APP___CLM__DBUSER.T_CLAIM_FEE_MAPPING TTT
            WHERE TTT.CLM_FEE_CODE = TRIM(TCA.ADJUST_TYPE))

  FROM APP___CLM__DBUSER.T_CLAIM_PAY A, APP___CLM__DBUSER.T_CLAIM_ADJUST_BUSI TCA
 WHERE A.ADJUST_BUSI_ID = TCA.Adjust_Busi_Id(+)
   AND A.ADJUST_BUSI_ID is not null
   and a.case_id = #{case_id}
   
 ]]>
 <if test=" advance_flag  != null "><![CDATA[   and A.ADVANCE_FLAG = ${advance_flag} ]]></if>
	  
	</select>
	
	
	<!-- 查询所有操作 -->
	<select id="findAllClaimPay" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.UNIT_NUMBER, A.REMARK, A.ADVANCE_FLAG, A.CASE_NO, A.BUSI_PROD_CODE, 
			A.IS_INSTALMENT, A.CLAIM_PAY_ID, A.BENE_RATE, A.PAY_AMOUNT, A.PAY_DENO, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_ID, A.PAY_MOLE, A.CASE_ID, 
			A.BENE_ID, A.POLICY_CODE,A.GET_MODE_CODE, A.IS_BACK_HOLDER,A.PAYEE_RELATION, A.BENE_HOLDER_RELATION,A.CONTRARY_PAY_FLAG,A.IS_HOLDER,A.OVERTHIRTY_UNSUCCESSPAY_FLAG FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimPayWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID ]]> 
	</select>
	<!-- 查询所有操作按照金额排序 -->
	<select id="findAllClaimPayOrderByPayAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.UNIT_NUMBER, A.REMARK, A.ADVANCE_FLAG, A.CASE_NO, A.BUSI_PROD_CODE, 
			A.IS_INSTALMENT, A.CLAIM_PAY_ID, A.BENE_RATE, A.PAY_AMOUNT, A.PAY_DENO, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_ID, A.PAY_MOLE, A.CASE_ID, 
			A.BENE_ID, A.POLICY_CODE,A.GET_MODE_CODE, A.IS_BACK_HOLDER,A.PAYEE_RELATION, A.BENE_HOLDER_RELATION,A.CONTRARY_PAY_FLAG,A.IS_HOLDER,A.OVERTHIRTY_UNSUCCESSPAY_FLAG FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimPayWhereCondition" />
		<![CDATA[ ORDER BY A.PAY_AMOUNT  DESC ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimPayTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE 1 = 1  ]]>
		<include refid="claimPayWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimPayForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.PREM_ARAP_ID, B.ADJUST_BUSI_ID, B.UNIT_NUMBER, B.REMARK, B.ADVANCE_FLAG, B.CASE_NO, B.BUSI_PROD_CODE, 
			B.IS_INSTALMENT, B.CLAIM_PAY_ID, B.BENE_RATE, B.PAY_AMOUNT, B.PAY_DENO, 
			B.BUSI_ITEM_ID, B.POLICY_ID, B.PAYEE_ID, B.PAY_MOLE, B.CASE_ID, 
			B.BENE_ID, B.POLICY_CODE, B.IS_BACK_HOLDER,B.PAYEE_RELATION, B.BENE_HOLDER_RELATION,B.CONTRARY_PAY_FLAG,B.IS_HOLDER,B.OVERTHIRTY_UNSUCCESSPAY_FLAG FROM (
					SELECT ROWNUM RN, A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.UNIT_NUMBER, A.REMARK, A.ADVANCE_FLAG, A.CASE_NO, A.BUSI_PROD_CODE, 
			A.IS_INSTALMENT, A.CLAIM_PAY_ID, A.BENE_RATE, A.PAY_AMOUNT, A.PAY_DENO, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_ID, A.PAY_MOLE, A.CASE_ID, 
			A.BENE_ID, A.POLICY_CODE, A.IS_BACK_HOLDER,A.PAYEE_RELATION, A.BENE_HOLDER_RELATION,A.CONTRARY_PAY_FLAG,A.IS_HOLDER,A.OVERTHIRTY_UNSUCCESSPAY_FLAG FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="claimPayWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
     <!--查询赔案下的受益人-->
	<select id="queryBeneIdByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BENE_ID FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE A.CASE_ID = #{case_id, jdbcType=NUMERIC} ]]>
		<if test=" adjust_busi_id  != null "><![CDATA[ AND A.ADJUST_BUSI_ID = #{adjust_busi_id} ]]></if>
		<if test=" adjust_busi_id  == null "><![CDATA[ AND A.ADJUST_BUSI_ID is null ]]></if>
		<if test=" advance_flag  != null "><![CDATA[ AND A.ADVANCE_FLAG = #{advance_flag} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<![CDATA[GROUP BY A.BENE_ID ]]>
	</select>
	<!--查询赔案下的受益人去重-->
	<select id="findBeneIdByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.BENE_ID FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE A.CASE_ID = #{case_id, jdbcType=NUMERIC} ]]>
		<if test=" adjust_busi_id  != null "><![CDATA[ AND A.ADJUST_BUSI_ID = #{adjust_busi_id} ]]></if>
		<if test=" adjust_busi_id  == null "><![CDATA[ AND A.ADJUST_BUSI_ID is null ]]></if>
		<if test=" advance_flag  != null "><![CDATA[ AND A.ADVANCE_FLAG = #{advance_flag} ]]></if>
		<if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<![CDATA[GROUP BY A.BENE_ID ]]>
	</select>
	 <!--查询赔案下的受益人去重-->
	<select id="findBeneDistinctByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT A.BENE_ID FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE A.CASE_ID = #{case_id, jdbcType=NUMERIC} ]]>
		<![CDATA[GROUP BY A.BENE_ID ]]>
	</select>
	<!-- add by zhaoyq  查询受益人比例 赔付通知书使用 -->
	<select id="findClaimPayByCondition" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.UNIT_NUMBER, A.REMARK, A.ADVANCE_FLAG, A.CASE_NO, A.BUSI_PROD_CODE, 
			A.IS_INSTALMENT, A.CLAIM_PAY_ID, A.BENE_RATE, A.PAY_AMOUNT, A.PAY_DENO, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_ID, A.PAY_MOLE, A.CASE_ID, 
    		A.BENE_ID, A.POLICY_CODE, A.IS_BACK_HOLDER,A.PAYEE_RELATION, A.BENE_HOLDER_RELATION FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE 1 = 1 AND A.ADJUST_BUSI_ID IS NULL ]]>
 	<include refid="claimPayWhereCondition" />
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID ]]>
	</select>
	
		<!-- 查询所有操作   身故和其他情况并存优先取身故 只查付费项目-->
	<select id="findAllClaimPayAndClaimType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.UNIT_NUMBER, A.REMARK, A.ADVANCE_FLAG, A.CASE_NO, A.BUSI_PROD_CODE, 
			A.IS_INSTALMENT, A.CLAIM_PAY_ID, A.BENE_RATE, A.PAY_AMOUNT, A.PAY_DENO, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_ID, A.PAY_MOLE, A.CASE_ID,A.CONTRARY_PAY_FLAG, 
			A.BENE_ID, A.POLICY_CODE,
		    (SELECT min(TCC.claim_type)  FROM APP___CLM__DBUSER.T_CLAIM_LIAB TCC WHERE TCC.CASE_ID=A.CASE_ID AND    TCC.BUSI_ITEM_ID = A.BUSI_ITEM_ID
             AND TCC.BUSI_PROD_CODE = A.BUSI_PROD_CODE  ) CLAIM_TYPE ,
             (SELECT TCA.ACC_REASON
          FROM APP___CLM__DBUSER.T_CLAIM_ACCIDENT TCA, APP___CLM__DBUSER.T_CLAIM_CASE TCC
         WHERE TCA.ACCIDENT_ID = TCC.ACCIDENT_ID
           AND TCC.CASE_ID = A.CASE_ID) ACC_REASON 
			 FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimPayWhereCondition" />
		<![CDATA[ AND A.ADJUST_BUSI_ID IS NULL ]]> 
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID ]]> 
	</select>
		<!-- 查询所有操作   身故和其他情况并存优先取身故 只查付费项目  以返还保费为准-->
	<select id="findAllRestoreBalanceClaimPayAndClaimType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.UNIT_NUMBER, A.REMARK, A.ADVANCE_FLAG, A.CASE_NO, A.BUSI_PROD_CODE, 
			A.IS_INSTALMENT, A.CLAIM_PAY_ID, A.BENE_RATE, A.PAY_AMOUNT, A.PAY_DENO, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_ID, A.PAY_MOLE, A.CASE_ID, A.CONTRARY_PAY_FLAG, 
			A.BENE_ID, A.POLICY_CODE,
		    (SELECT min(TCC.claim_type)  FROM APP___CLM__DBUSER.T_CLAIM_LIAB TCC WHERE TCC.CASE_ID=A.CASE_ID AND    TCC.BUSI_ITEM_ID = A.BUSI_ITEM_ID
             AND TCC.BUSI_PROD_CODE = A.BUSI_PROD_CODE  ) CLAIM_TYPE ,
             (SELECT TCA.ACC_REASON
          FROM APP___CLM__DBUSER.T_CLAIM_ACCIDENT TCA, APP___CLM__DBUSER.T_CLAIM_CASE TCC
         WHERE TCA.ACCIDENT_ID = TCC.ACCIDENT_ID
           AND TCC.CASE_ID = A.CASE_ID) ACC_REASON 
			 FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimPayWhereCondition" />
		<![CDATA[ AND A.ADJUST_BUSI_ID = (SELECT max(cab.adjust_busi_id) FROM DEV_CLM.T_CLAIM_ADJUST_BUSI cab where cab.adjust_type IN ('8','16','17','18') and cab.CASE_ID=A.CASE_ID AND cab.BUSI_ITEM_ID=A.busi_item_id)  ]]> 
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID ]]> 
	</select>
		<!-- 查询所有操作   身故和其他情况并存优先取身故 只查付费项目  以现金红利为准-->
	<select id="findAllCashDividendClaimPayAndClaimType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.UNIT_NUMBER, A.REMARK, A.ADVANCE_FLAG, A.CASE_NO, A.BUSI_PROD_CODE, 
			A.IS_INSTALMENT, A.CLAIM_PAY_ID, A.BENE_RATE, A.PAY_AMOUNT, A.PAY_DENO, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_ID, A.PAY_MOLE, A.CASE_ID, A.CONTRARY_PAY_FLAG, 
			A.BENE_ID, A.POLICY_CODE,
		    (SELECT min(TCC.claim_type)  FROM APP___CLM__DBUSER.T_CLAIM_LIAB TCC WHERE TCC.CASE_ID=A.CASE_ID AND    TCC.BUSI_ITEM_ID = A.BUSI_ITEM_ID
             AND TCC.BUSI_PROD_CODE = A.BUSI_PROD_CODE  ) CLAIM_TYPE ,
             (SELECT TCA.ACC_REASON
          FROM APP___CLM__DBUSER.T_CLAIM_ACCIDENT TCA, APP___CLM__DBUSER.T_CLAIM_CASE TCC
         WHERE TCA.ACCIDENT_ID = TCC.ACCIDENT_ID
           AND TCC.CASE_ID = A.CASE_ID) ACC_REASON 
			 FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimPayWhereCondition" />
		<![CDATA[ AND EXISTS( 
        		SELECT 1 FROM  APP___CLM__DBUSER.T_CLAIM_ADJUST_BUSI  B  WHERE B.ADJUST_TYPE = '19' AND B.ADJUST_BUSI_ID= A.ADJUST_BUSI_ID 
         )  ]]> 
		<![CDATA[ ORDER BY A.CLAIM_PAY_ID ]]> 
	</select>
	
	<!-- by liutao 查询费用类型 t_claim_fee_mapping -->
	<select id="findAllClaimFeeMapping" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
		SELECT TCFM.Clm_Fee_Code,
        TCFM.CLM_FEE_DESC,
        TCFM.Cap_Fee_Code,
        TCFM.CAP_FEE_DESC,
        TCFM.ARAP_CODE,
        TCFM.ARAP_DESC
   		FROM APP___CLM__DBUSER.T_CLAIM_FEE_MAPPING TCFM  
   		WHERE 1=1
         ]]>
         <if test=" cap_fee_code  != null "><![CDATA[ AND   TCFM.cap_fee_code = #{cap_fee_code} ]]></if>
	</select>
	<!-- 查询保单预付分配信息 -->	
<select id="findAdvanceBeneDetail" resultType="java.util.Map" parameterType="java.util.Map">	
	<![CDATA[SELECT 
	b.bene_id,
	B.BENE_NAME bene_name,
       A.PAY_MOLE,
       A.PAY_DENO,
       sum(A.PAY_AMOUNT) pay_amount,
       C.PAYEE_NAME payee_name,
       C.PAYEE_CERTI_NO payee_certi_no,
       A.POLICY_CODE
  FROM APP___CLM__DBUSER.T_CLAIM_PAY   A,
       APP___CLM__DBUSER.T_CLAIM_BENE  B,
       APP___CLM__DBUSER.T_CLAIM_PAYEE C
 WHERE A.BENE_ID = B.BENE_ID
   AND A.PAYEE_ID = C.PAYEE_ID]]>
   <if test=" advance_flag  != null "><![CDATA[ AND A.ADVANCE_FLAG = #{advance_flag} ]]></if>
   <![CDATA[
   AND A.CASE_ID = #{case_id}
   AND A.POLICY_CODE = #{policy_code}
 group by 
 		b.bene_id,
 		B.BENE_NAME,
          C.PAYEE_NAME,
          C.PAYEE_CERTI_NO,
          A.CASE_NO,
          A.POLICY_CODE,
          A.PAY_MOLE,
          A.PAY_DENO]]>
</select>
	
	<!-- 上海医保-查询已存储的领款人针对的是理赔金还是返还保费 -->
	<select id="findClaimPaySHData" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	SELECT A.PREM_ARAP_ID, A.ADJUST_BUSI_ID, A.UNIT_NUMBER, A.REMARK, A.ADVANCE_FLAG, A.CASE_NO, A.BUSI_PROD_CODE, 
      		A.IS_INSTALMENT, A.CLAIM_PAY_ID, A.BENE_RATE, A.PAY_AMOUNT, A.PAY_DENO, 
			A.BUSI_ITEM_ID, A.POLICY_ID, A.PAYEE_ID, A.PAY_MOLE, A.CASE_ID, 
			A.BENE_ID, A.POLICY_CODE,A.GET_MODE_CODE, A.IS_BACK_HOLDER,A.PAYEE_RELATION,A.BENE_HOLDER_RELATION FROM APP___CLM__DBUSER.T_CLAIM_PAY A
			WHERE 1 = 1  AND A.BUSI_PROD_CODE IN ('00557000','00558000','00842000','00843000','00868000') ]]>
			<if test="case_id != null ">
				<![CDATA[ AND A.CASE_ID = #{case_id} ]]>
			</if>
			<if test="payee_id != null ">
				<![CDATA[ AND A.PAYEE_ID = #{payee_id} ]]>
			</if>
	</select>
	
	
	<!-- 根据赔案id和UnitNumber查询受益分配信息payee_id -->	
<select id="findAllClaimPayByCaseIdAndUnitNumber" resultType="java.util.Map" parameterType="java.util.Map">	
	<![CDATA[SELECT 
	   A.PAYEE_ID,
	   A.UNIT_NUMBER,
       A.CASE_ID,
       A.PAY_AMOUNT,
       A.ADJUST_BUSI_ID,
       B.CASE_NO
  FROM APP___CLM__DBUSER.T_CLAIM_PAY   A,APP___CLM__DBUSER.T_CLAIM_CASE   B
 WHERE A.UNIT_NUMBER = #{unit_number}
   AND B.CASE_NO = #{case_no}
   AND A.CASE_ID = B.CASE_ID
   ]]>
</select>

	
	<!-- 根据赔案号caseId去重查询保单数据 -->
	<select id="findInsurancePolicy" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT distinct A.POLICY_CODE
			 FROM APP___CLM__DBUSER.T_CLAIM_PAY A WHERE 1 = 1  ]]>
	<include refid="claimPayWhereCondition" />
	</select>
	<!-- 查询赔案报送险种受益分配信息 -->
  <select id="findSubmitMessageClaimPay" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			  SELECT distinct A.CASE_ID,A.POLICY_CODE,A.BUSI_PROD_CODE,A.UNIT_NUMBER,CB.COVERAGE_PACK_CODE,PB.PRODUCT_CODE_BOCIC,
		          (SELECT MAX(PREM.FINISH_TIME) FROM APP___CLM__DBUSER.T_PREM_ARAP PREM WHERE PREM.BUSINESS_CODE=B.CASE_NO) FINISH_TIME,
		            A.PAY_AMOUNT
		            FROM APP___CLM__DBUSER.T_CLAIM_PAY a
		           INNER JOIN APP___CLM__DBUSER.T_CLAIM_CASE B
		              ON A.CASE_ID=B.CASE_ID
		           INNER JOIN APP___CLM__DBUSER.T_BUSINESS_PRODUCT BP
		              ON A.BUSI_PROD_CODE = BP.PRODUCT_CODE_SYS
		           INNER JOIN APP___CLM__DBUSER.T_PRODUCT_BOCIC PB
		              ON BP.BUSINESS_PRD_ID = PB.BUSINESS_PRD_ID 
		           INNER JOIN APP___CLM__DBUSER.T_COVERAGE_BOCIC CB
		              ON PB.BOCIC_COVERAGE_ID=CB.BOCIC_COVERAGE_ID
		           WHERE a.unit_number is not null and A.CASE_ID = #{case_id}
	]]>
	</select>
	<!-- 查询既往分期数据 -->
  <select id="findPastInstalmentPayId" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ 
			  SELECT a.claim_pay_id
				  FROM dev_clm.t_claim_pay a
				 where 1=1
				    and a.busi_prod_code=#{busi_prod_code}
				    and a.is_instalment='1'
				    and a.case_id in  (SELECT b.case_id FROM dev_clm.t_claim_case b where b.case_status=80 and b.insured_id = #{insured_id})
	]]>
	</select>
	<!-- 睡眠保单打标批处理查询类 -->
	<select id="findOverThirtyMarkBatchData" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT B.* FROM (
			 SELECT CP.PREM_ARAP_ID, CP.ADJUST_BUSI_ID, CP.UNIT_NUMBER, CP.REMARK, CP.ADVANCE_FLAG, CC.CASE_NO, CP.BUSI_PROD_CODE, 
		      		CP.IS_INSTALMENT, CP.CLAIM_PAY_ID, CP.BENE_RATE, CP.PAY_AMOUNT, CP.PAY_DENO, 
					CP.BUSI_ITEM_ID, CP.POLICY_ID, CP.PAYEE_ID, CP.PAY_MOLE, CP.CASE_ID, 
					CP.BENE_ID, CP.POLICY_CODE,CP.GET_MODE_CODE, CP.IS_BACK_HOLDER,CP.PAYEE_RELATION,CP.BENE_HOLDER_RELATION,PAYEE.PAYEE_NAME,
			       	case when((not exists (SELECT 1
			                               FROM dev_clm.t_prem_arap a
			                              where a.fee_status in ('00', '03')
			                                and a.unit_number = cp.unit_number) or cc.case_status = '90') AND CP.OVERTHIRTY_UNSUCCESSPAY_FLAG = '1')
			            then null else 1 end OVERTHIRTY_UNSUCCESSPAY_FLAG
			  FROM dev_clm.t_claim_pay cp
			 inner join dev_clm.t_claim_case cc
			    on cp.case_id = cc.case_id
			 inner join dev_clm.t_claim_payee payee
			    on cp.payee_id=payee.payee_id
			 where cc.end_case_time >= timestamp'2023-07-01 00:00:00'
			   and (sysdate-cc.end_case_time)>=30
			   and (exists (SELECT 1  FROM dev_clm.t_prem_arap a
			                              where a.fee_status in ('00', '03')
			                                and a.unit_number = cp.unit_number) or CP.OVERTHIRTY_UNSUCCESSPAY_FLAG IS NOT NULL) 
			   and cc.case_status in ('80', '90')) B WHERE mod(B.CASE_ID, #{modNum}) = #{start}
	]]>
	</select>
	<!-- 修改睡眠保单标识 -->
	<update id="updateClaimPayOverthirty" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_PAY  
				    set OVERTHIRTY_UNSUCCESSPAY_FLAG = #{overthirty_unsuccesspay_flag, jdbcType=NUMERIC} , 
				    UPDATE_BY = #{update_by, jdbcType=NUMERIC} , 
					UPDATE_TIME = SYSDATE ,  
				    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP 
			        WHERE CLAIM_PAY_ID = #{claim_pay_id}]]>
	</update>
</mapper>
