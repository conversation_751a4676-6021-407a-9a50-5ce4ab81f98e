<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.dao.ClmReportCondoleDaoImpl">

    
	<select id="queryCaseId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ select a.CASE_ID,a.COMFORT_FLAG from APP___CLM__DBUSER.T_CLAIM_CASE a where a.CASE_NO=#{case_no} and a.INSURED_ID=#{insured_id}]]>
	</select>
	
	<!-- 修改操作 -->
	<update id="updateComfortFlag" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_CASE ]]>
		<set>
		<trim suffixOverrides=",">
		    COMFORT_FLAG = 1 ,
		   
		</trim>
		</set>
		<![CDATA[ WHERE case_id = #{case_id} ]]>
	</update>

	
	
</mapper>