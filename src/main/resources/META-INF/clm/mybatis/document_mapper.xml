<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.DocumentPO">

	<sql id="documentWhereCondition">
		<if test=" buss_source_code != null and buss_source_code != ''  "><![CDATA[ AND A.BUSS_SOURCE_CODE = #{buss_source_code} ]]></if>
		<if test=" clob_id  != null "><![CDATA[ AND A.CLOB_ID = #{clob_id} ]]></if>
		<if test=" scan_by  != null "><![CDATA[ AND A.SCAN_BY = #{scan_by} ]]></if>
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
		<if test=" print_time  != null  and  print_time  != ''  "><![CDATA[ AND A.PRINT_TIME = #{print_time} ]]></if>
		<if test=" create_time  != null  and  create_time  != ''  "><![CDATA[ AND A.CREATE_TIME = #{create_time} ]]></if>
		<if test=" send_by  != null "><![CDATA[ AND A.SEND_BY = #{send_by} ]]></if>
		<if test=" supplement_flag  != null "><![CDATA[ AND A.SUPPLEMENT_FLAG = #{supplement_flag} ]]></if>
		<if test=" send_time  != null  and  send_time  != ''  "><![CDATA[ AND A.SEND_TIME = #{send_time} ]]></if>
		<if test=" create_by  != null "><![CDATA[ AND A.CREATE_BY = #{create_by} ]]></if>
		<if test=" status != null and status != ''  "><![CDATA[ AND A.STATUS = #{status} ]]></if>
		<if test=" is_link  != null "><![CDATA[ AND A.IS_LINK = #{is_link} ]]></if>
		<if test=" buss_id  != null "><![CDATA[ AND A.BUSS_ID = #{buss_id} ]]></if>
		<if test=" print_by  != null "><![CDATA[ AND A.PRINT_BY = #{print_by} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" overdue_time  != null  and  overdue_time  != ''  "><![CDATA[ AND A.OVERDUE_TIME = #{overdue_time} ]]></if>
		<if test=" close_by  != null "><![CDATA[ AND A.CLOSE_BY = #{close_by} ]]></if>
		<if test=" reply_time  != null  and  reply_time  != ''  "><![CDATA[ AND A.REPLY_TIME = #{reply_time} ]]></if>
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
		<if test=" send_obj_type != null and send_obj_type != ''  "><![CDATA[ AND A.SEND_OBJ_TYPE = #{send_obj_type} ]]></if>
		<if test=" reply_remark != null and reply_remark != ''  "><![CDATA[ AND A.REPLY_REMARK = #{reply_remark} ]]></if>
		<if test=" document_name != null and document_name != ''  "><![CDATA[ AND A.DOCUMENT_NAME = #{document_name} ]]></if>
		<if test=" overdue_document_no != null and overdue_document_no != ''  "><![CDATA[ AND A.OVERDUE_DOCUMENT_NO = #{overdue_document_no} ]]></if>
		<if test=" template_code != null and template_code != ''  "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
		<if test=" close_time  != null  and  close_time  != ''  "><![CDATA[ AND A.CLOSE_TIME = #{close_time} ]]></if>
		<if test=" send_obj_id != null and send_obj_id != ''  "><![CDATA[ AND A.SEND_OBJ_ID = #{send_obj_id} ]]></if>
		<if test=" reprint_times  != null "><![CDATA[ AND A.REPRINT_TIMES = #{reprint_times} ]]></if>
		<if test=" document_no != null and document_no != ''  "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
		<if test=" is_merger  != null "><![CDATA[ AND A.IS_MERGER = #{is_merger} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" scan_time  != null  and  scan_time  != ''  "><![CDATA[ AND A.SCAN_TIME = #{scan_time} ]]></if>
		<if test=" reply_conclusion  != null "><![CDATA[ AND A.REPLY_CONCLUSION = #{reply_conclusion} ]]></if>
		<if test=" buss_code != null and buss_code != ''  "><![CDATA[ AND A.BUSS_CODE = #{buss_code} ]]></if>
		<if test=" reply_by  != null "><![CDATA[ AND A.REPLY_BY = #{reply_by} ]]></if>
		<if test=" reply_days  != null "><![CDATA[ AND A.REPLY_DAYS = #{reply_days} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryDocumentByDocListIdCondition">
		<if test=" doc_list_id  != null "><![CDATA[ AND A.DOC_LIST_ID = #{doc_list_id} ]]></if>
	</sql>	
	<sql id="queryDocumentByDocumentNoCondition">
		<if test=" document_no != null and document_no != '' "><![CDATA[ AND A.DOCUMENT_NO = #{document_no} ]]></if>
		<if test=" template_code != null and template_code != '' "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
		<if test=" buss_code != null and buss_code != '' "><![CDATA[ AND A.BUSS_CODE = #{buss_code} ]]></if>
	</sql>	
	<sql id="queryDocumentByPolicyCodeCondition">
		<if test=" policy_code != null and policy_code != '' "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
	</sql>	
	<sql id="queryDocumentByPolicyIdCondition">
		<if test=" policy_id  != null "><![CDATA[ AND A.POLICY_ID = #{policy_id} ]]></if>
	</sql>	
	<sql id="queryDocumentByStatusCondition">
		<if test=" status != null and status != '' "><![CDATA[ AND A.STATUS = #{status} ]]></if>
	</sql>
	<sql id="queryDocumentByBussCodeCondition">
		<if test=" buss_code != null and buss_code != '' "><![CDATA[ AND A.BUSS_CODE = #{buss_code} ]]></if>
		<if test=" template_code != null and template_code != '' "><![CDATA[ AND A.TEMPLATE_CODE = #{template_code} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addDocument"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="doc_list_id">
			SELECT APP___CLM__DBUSER.S_DOCUMENT.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_DOCUMENT(
				BUSS_SOURCE_CODE, CLOB_ID, SCAN_BY, DOC_LIST_ID, PRINT_TIME, CREATE_TIME, SEND_BY, 
				SUPPLEMENT_FLAG, SEND_TIME, CREATE_BY, STATUS, IS_LINK, BUSS_ID, PRINT_BY, 
				INSERT_TIMESTAMP, ORGAN_CODE, OVERDUE_TIME, UPDATE_BY, CLOSE_BY, REPLY_TIME, POLICY_ID, 
				SEND_OBJ_TYPE, REPLY_REMARK, DOCUMENT_NAME, OVERDUE_DOCUMENT_NO, INSERT_TIME, TEMPLATE_CODE, UPDATE_TIME, 
				CLOSE_TIME, SEND_OBJ_ID, REPRINT_TIMES, DOCUMENT_NO, IS_MERGER, POLICY_CODE, SCAN_TIME, 
				REPLY_CONCLUSION, BUSS_CODE, UPDATE_TIMESTAMP, REPLY_BY, INSERT_BY, REPLY_DAYS ) 
			VALUES (
				#{buss_source_code, jdbcType=VARCHAR}, #{clob_id, jdbcType=NUMERIC} , #{scan_by, jdbcType=NUMERIC} , #{doc_list_id, jdbcType=NUMERIC} , #{print_time, jdbcType=DATE} , #{create_time, jdbcType=DATE} , #{send_by, jdbcType=NUMERIC} 
				, #{supplement_flag, jdbcType=NUMERIC} , #{send_time, jdbcType=DATE} , #{create_by, jdbcType=NUMERIC} , #{status, jdbcType=VARCHAR} , #{is_link, jdbcType=NUMERIC} , #{buss_id, jdbcType=NUMERIC} , #{print_by, jdbcType=NUMERIC} 
				, CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{overdue_time, jdbcType=DATE} , #{update_by, jdbcType=NUMERIC} , #{close_by, jdbcType=NUMERIC} , #{reply_time, jdbcType=DATE} , #{policy_id, jdbcType=NUMERIC} 
				, #{send_obj_type, jdbcType=VARCHAR} , #{reply_remark, jdbcType=VARCHAR} , #{document_name, jdbcType=VARCHAR} , #{overdue_document_no, jdbcType=VARCHAR} , SYSDATE , #{template_code, jdbcType=VARCHAR} , SYSDATE 
				, #{close_time, jdbcType=DATE} , #{send_obj_id, jdbcType=VARCHAR} , #{reprint_times, jdbcType=NUMERIC} , #{document_no, jdbcType=VARCHAR} , #{is_merger, jdbcType=NUMERIC} , #{policy_code, jdbcType=VARCHAR} , #{scan_time, jdbcType=DATE} 
				, #{reply_conclusion, jdbcType=NUMERIC} , #{buss_code, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{reply_by, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} , #{reply_days, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteDocument" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_DOCUMENT WHERE DOC_LIST_ID = #{doc_list_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateDocument" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_DOCUMENT ]]>
		<set>
		<trim suffixOverrides=",">
			BUSS_SOURCE_CODE = #{buss_source_code, jdbcType=VARCHAR} ,
		    CLOB_ID = #{clob_id, jdbcType=NUMERIC} ,
		    SCAN_BY = #{scan_by, jdbcType=NUMERIC} ,
		    PRINT_TIME = #{print_time, jdbcType=DATE} ,
		    CREATE_TIME = #{create_time, jdbcType=DATE} ,
		    SEND_BY = #{send_by, jdbcType=NUMERIC} ,
		    SUPPLEMENT_FLAG = #{supplement_flag, jdbcType=NUMERIC} ,
		    SEND_TIME = #{send_time, jdbcType=DATE} ,
		    CREATE_BY = #{create_by, jdbcType=NUMERIC} ,
			STATUS = #{status, jdbcType=VARCHAR} ,
		    IS_LINK = #{is_link, jdbcType=NUMERIC} ,
		    BUSS_ID = #{buss_id, jdbcType=NUMERIC} ,
		    PRINT_BY = #{print_by, jdbcType=NUMERIC} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    OVERDUE_TIME = #{overdue_time, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CLOSE_BY = #{close_by, jdbcType=NUMERIC} ,
		    REPLY_TIME = #{reply_time, jdbcType=DATE} ,
		    POLICY_ID = #{policy_id, jdbcType=NUMERIC} ,
			SEND_OBJ_TYPE = #{send_obj_type, jdbcType=VARCHAR} ,
			REPLY_REMARK = #{reply_remark, jdbcType=VARCHAR} ,
			DOCUMENT_NAME = #{document_name, jdbcType=VARCHAR} ,
			OVERDUE_DOCUMENT_NO = #{overdue_document_no, jdbcType=VARCHAR} ,
			TEMPLATE_CODE = #{template_code, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    CLOSE_TIME = #{close_time, jdbcType=DATE} ,
			SEND_OBJ_ID = #{send_obj_id, jdbcType=VARCHAR} ,
		    REPRINT_TIMES = #{reprint_times, jdbcType=NUMERIC} ,
			DOCUMENT_NO = #{document_no, jdbcType=VARCHAR} ,
		    IS_MERGER = #{is_merger, jdbcType=NUMERIC} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
		    SCAN_TIME = #{scan_time, jdbcType=DATE} ,
		    REPLY_CONCLUSION = #{reply_conclusion, jdbcType=NUMERIC} ,
			BUSS_CODE = #{buss_code, jdbcType=VARCHAR} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    REPLY_BY = #{reply_by, jdbcType=NUMERIC} ,
		    REPLY_DAYS = #{reply_days, jdbcType=NUMERIC} ,
		</trim>
		</set>
		<![CDATA[ WHERE DOC_LIST_ID = #{doc_list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findDocumentByDocListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="queryDocumentByDocListIdCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	<select id="findDocumentByDocumentNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="queryDocumentByDocumentNoCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	<select id="findDocumentByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="queryDocumentByPolicyCodeCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	<select id="findDocumentByPolicyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="queryDocumentByPolicyIdCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	
	<select id="findDocumentByStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="queryDocumentByStatusCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapDocument" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllDocument" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE ROWNUM <=  1000  ]]>
		<include refid="documentWhereCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findDocumentTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryDocumentForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.BUSS_SOURCE_CODE, B.CLOB_ID, B.SCAN_BY, B.DOC_LIST_ID, B.PRINT_TIME, B.CREATE_TIME, B.SEND_BY, 
			B.SUPPLEMENT_FLAG, B.SEND_TIME, B.CREATE_BY, B.STATUS, B.IS_LINK, B.BUSS_ID, B.PRINT_BY, 
			B.ORGAN_CODE, B.OVERDUE_TIME, B.CLOSE_BY, B.REPLY_TIME, B.POLICY_ID, 
			B.SEND_OBJ_TYPE, B.REPLY_REMARK, B.DOCUMENT_NAME, B.OVERDUE_DOCUMENT_NO, B.TEMPLATE_CODE, 
			B.CLOSE_TIME, B.SEND_OBJ_ID, B.REPRINT_TIMES, B.DOCUMENT_NO, B.IS_MERGER, B.POLICY_CODE, B.SCAN_TIME, 
			B.REPLY_CONCLUSION, B.BUSS_CODE, B.REPLY_BY, B.REPLY_DAYS FROM (
					SELECT ROWNUM RN, A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 通过 业务号码 数据 -->
	<select id="findDocumentByBussCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE 1 = 1  ]]>
		<include refid="queryDocumentByBussCodeCondition" />
		<![CDATA[ ORDER BY A.DOC_LIST_ID ]]>
	</select>
	<!-- 根据赔案号和单证类型删除一条单证 -->
	<select id="deleteDocumentByCaseAndTtemplate" resultType="java.util.Map" parameterType="java.util.Map">
	    <![CDATA[ DELETE FROM APP___CLM__DBUSER.T_DOCUMENT WHERE BUSS_CODE = #{buss_code} AND TEMPLATE_CODE = #{template_code}]]>
	</select>
	
	<!-- 理赔核保照会下核保通知书查询 SQL -->
	<select id="clmUnderwritingNotice" resultType="java.util.Map" parameterType="java.util.Map">
		
		<![CDATA[
			select distinct a.DOC_LIST_ID,
                a.POLICY_CODE,
                a.DOCUMENT_NO,
                a.DOCUMENT_NAME,
                a.ORGAN_CODE,
                (select agent_code from  (select b.agent_code
                   from APP___CLM__DBUSER.t_Contract_Agent b
                  where b.policy_code = #{policy_code} order by b.Agent_Start_Date desc) where rownum  = 1 ) AGENT_CODE
  from APP___CLM__DBUSER.T_document a, APP___CLM__DBUSER.t_Claim_Case c
 where a.TEMPLATE_CODE = 'CLM_00011'
   and a.buss_code = c.case_no
   and c.case_id in (
                     
                     select distinct d.case_id
                       from APP___CLM__DBUSER.t_Contract_Master d
                      where d.case_id is not null
                        and d.policy_code = #{policy_code}
                     
                     )
		]]>
	</select>   
	
	<!-- 根据赔案号查询所有数据 -->
	<select id="findAllDocumentByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BUSS_SOURCE_CODE, A.CLOB_ID, A.SCAN_BY, A.DOC_LIST_ID, A.PRINT_TIME, A.CREATE_TIME, A.SEND_BY, 
			A.SUPPLEMENT_FLAG, A.SEND_TIME, A.CREATE_BY, A.STATUS, A.IS_LINK, A.BUSS_ID, A.PRINT_BY, 
			A.ORGAN_CODE, A.OVERDUE_TIME, A.CLOSE_BY, A.REPLY_TIME, A.POLICY_ID, 
			A.SEND_OBJ_TYPE, A.REPLY_REMARK, A.DOCUMENT_NAME, A.OVERDUE_DOCUMENT_NO, A.TEMPLATE_CODE, 
			A.CLOSE_TIME, A.SEND_OBJ_ID, A.REPRINT_TIMES, A.DOCUMENT_NO, A.IS_MERGER, A.POLICY_CODE, A.SCAN_TIME, 
			A.REPLY_CONCLUSION, A.BUSS_CODE, A.REPLY_BY, A.REPLY_DAYS
		 FROM APP___CLM__DBUSER.T_DOCUMENT A WHERE ROWNUM <=  1000 
		  AND A.BUSS_CODE = #{buss_code}
		  AND A.TEMPLATE_CODE !='CLM_00008'
		  AND A.TEMPLATE_CODE !='CLM_00012' 
		ORDER BY A.DOC_LIST_ID ]]> 
	</select>
	
</mapper>
