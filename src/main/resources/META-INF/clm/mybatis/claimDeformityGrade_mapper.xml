<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimDeformityGradePO">
<!--
	<sql id="claimDeformityGradeWhereCondition">
		<if test=" deformity_type != null and deformity_type != ''  "><![CDATA[ AND A.DEFORMITY_TYPE = #{deformity_type} ]]></if>
		<if test=" end_date  != null  and  end_date  != ''  "><![CDATA[ AND A.END_DATE = #{end_date} ]]></if>
		<if test=" deformity_grade != null and deformity_grade != ''  "><![CDATA[ AND A.DEFORMITY_GRADE = #{deformity_grade} ]]></if>
		<if test=" start_date  != null  and  start_date  != ''  "><![CDATA[ AND A.START_DATE = #{start_date} ]]></if>
		<if test=" deformity_grade_id  != null "><![CDATA[ AND A.DEFORMITY_GRADE_ID = #{deformity_grade_id} ]]></if>
		<if test=" deformity_code != null and deformity_code != ''  "><![CDATA[ AND A.DEFORMITY_CODE = #{deformity_code} ]]></if>
		<if test=" is_effective  != null "><![CDATA[ AND A.IS_EFFECTIVE = #{is_effective} ]]></if>
		<if test=" deformity_rate  != null "><![CDATA[ AND A.DEFORMITY_RATE = #{deformity_rate} ]]></if>
		<if test=" icf_code  != null "><![CDATA[ AND A.ICF_CODE = #{icf_code} ]]></if>
	</sql>
-->

<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimDeformityGradeByDeformityTypeCondition">
		<if test=" deformity_type != null and deformity_type != '' "><![CDATA[ AND A.DEFORMITY_TYPE = #{deformity_type} ]]></if>
	</sql>	
	<sql id="queryClaimDeformityGradeByDeformityGradeCondition">
		<if test=" deformity_grade != null and deformity_grade != '' "><![CDATA[ AND A.DEFORMITY_GRADE = #{deformity_grade} ]]></if>
	</sql>	
	<sql id="queryClaimDeformityGradeByDeformityCodeCondition">
		<if test=" deformity_code != null and deformity_code != '' "><![CDATA[ AND A.DEFORMITY_CODE = #{deformity_code} ]]></if>
	</sql>	
	<sql id="queryClaimDeformityGradeByDeformityGradeIdCondition">
		<if test=" deformity_grade_id  != null "><![CDATA[ AND A.DEFORMITY_GRADE_ID = #{deformity_grade_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimDeformityGrade"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="deformity_grade_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_DEFORMITY_GRADE__DEFOR.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE(
				DEFORMITY_TYPE, END_DATE, INSERT_TIME, UPDATE_TIME, DEFORMITY_GRADE, START_DATE, INSERT_TIMESTAMP, 
				DEFORMITY_GRADE_ID, UPDATE_BY, DEFORMITY_CODE, IS_EFFECTIVE, UPDATE_TIMESTAMP, DEFORMITY_RATE, INSERT_BY, 
				ICF_CODE ,deformity_code_name) 
			VALUES (
				#{deformity_type, jdbcType=VARCHAR}, #{end_date, jdbcType=DATE} , SYSDATE , SYSDATE , #{deformity_grade, jdbcType=VARCHAR} , #{start_date, jdbcType=DATE} , CURRENT_TIMESTAMP
				, #{deformity_grade_id, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{deformity_code, jdbcType=VARCHAR} , #{is_effective, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{deformity_rate, jdbcType=NUMERIC} , #{insert_by, jdbcType=NUMERIC} 
				, #{icf_code, jdbcType=NUMERIC} ,#{deformity_code_name, jdbcType=VARCHAR}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimDeformityGrade" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE WHERE DEFORMITY_GRADE_ID = #{deformity_grade_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimDeformityGrade" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE ]]>
		<set>
		<trim suffixOverrides=",">
			DEFORMITY_TYPE = #{deformity_type, jdbcType=VARCHAR} ,
		    END_DATE = #{end_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			DEFORMITY_GRADE = #{deformity_grade, jdbcType=VARCHAR} ,
		    START_DATE = #{start_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			DEFORMITY_CODE = #{deformity_code, jdbcType=VARCHAR} ,
		    IS_EFFECTIVE = #{is_effective, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    DEFORMITY_RATE = #{deformity_rate, jdbcType=NUMERIC} ,
		    ICF_CODE = #{icf_code, jdbcType=NUMERIC} ,
		    deformity_code_name= #{deformity_code_name, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE DEFORMITY_GRADE_ID = #{deformity_grade_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimDeformityGradeByDeformityType" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEFORMITY_TYPE, A.END_DATE, A.DEFORMITY_GRADE, A.START_DATE, 
			A.DEFORMITY_GRADE_ID, A.DEFORMITY_CODE, A.IS_EFFECTIVE, A.DEFORMITY_RATE, 
			A.ICF_CODE,A.deformity_code_name FROM APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE A WHERE 1 = 1  ]]>
		<include refid="queryClaimDeformityGradeByDeformityTypeCondition" />
		<![CDATA[ ORDER BY A.DEFORMITY_GRADE_ID ]]>
	</select>
	
	<select id="findClaimDeformityGradeByDeformityGrade" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEFORMITY_TYPE, A.END_DATE, A.DEFORMITY_GRADE, A.START_DATE, 
			A.DEFORMITY_GRADE_ID, A.DEFORMITY_CODE, A.IS_EFFECTIVE, A.DEFORMITY_RATE, 
			A.ICF_CODE A.deformity_code_name FROM APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE A WHERE 1 = 1  ]]>
		<include refid="queryClaimDeformityGradeByDeformityGradeCondition" />
		<![CDATA[ ORDER BY A.DEFORMITY_GRADE_ID ]]>
	</select>
	
	<select id="findClaimDeformityGradeByDeformityCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEFORMITY_TYPE, A.END_DATE, A.DEFORMITY_GRADE, A.START_DATE, 
			A.DEFORMITY_GRADE_ID, A.DEFORMITY_CODE, A.IS_EFFECTIVE, A.DEFORMITY_RATE, 
			A.ICF_CODE,A.deformity_code_name FROM APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE A WHERE 1 = 1  ]]>
		<include refid="queryClaimDeformityGradeByDeformityCodeCondition" />
		<![CDATA[ ORDER BY A.DEFORMITY_GRADE_ID ]]>
	</select>
	
	<select id="findClaimDeformityGradeByDeformityGradeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEFORMITY_TYPE, A.END_DATE, A.DEFORMITY_GRADE, A.START_DATE, 
			A.DEFORMITY_GRADE_ID, A.DEFORMITY_CODE, A.IS_EFFECTIVE, A.DEFORMITY_RATE, 
			A.ICF_CODE ,A.deformity_code_name FROM APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE A WHERE 1 = 1  ]]>
		<include refid="queryClaimDeformityGradeByDeformityGradeIdCondition" />
		<![CDATA[ ORDER BY A.DEFORMITY_GRADE_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimDeformityGrade" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEFORMITY_TYPE, A.END_DATE, A.DEFORMITY_GRADE, A.START_DATE, 
			A.DEFORMITY_GRADE_ID, A.DEFORMITY_CODE, A.IS_EFFECTIVE, A.DEFORMITY_RATE, 
			A.ICF_CODE,A.deformity_code_name FROM APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DEFORMITY_GRADE_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimDeformityGrade" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.DEFORMITY_TYPE, A.END_DATE, A.DEFORMITY_GRADE, A.START_DATE, 
			A.DEFORMITY_GRADE_ID, A.DEFORMITY_CODE, A.IS_EFFECTIVE, A.DEFORMITY_RATE, 
			A.ICF_CODE,A.deformity_code_name FROM APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.DEFORMITY_TYPE ]]> 
	</select>
	
	<select id="findDeformityGradeByTypeId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[	select A.Deformity_Type,
       		A.Deformity_Grade,
       		A.Deformity_Grade_name,
      		 A.Deformity_Rate
  		from APP___CLM__DBUSER.T_DEFORMITY_GRADE A ]]>
  		<if test="deformity_type != null and deformity_type != ''">
			<![CDATA[ where A.Deformity_Type = #{deformity_type} ]]> 
			<![CDATA[ ORDER BY TO_NUMBER(A.Deformity_Grade) ]]> 
		</if>
		
	</select>	

<!-- 查询个数操作 -->
	<select id="findClaimDeformityGradeTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE A WHERE 1 = 1]]>
		<if test="deformity_type !=null and deformity_type !=''"> 
	        	<![CDATA[  and a.deformity_type = #{deformity_type } ]]>
		</if>
		<if test="deformity_grade !=null and deformity_grade !=''"> 
	        	<![CDATA[  and a.deformity_grade = #{deformity_grade } ]]>
		</if>
		<if test="deformity_code !=null and deformity_code !=''"> 
	        	<![CDATA[  and a.deformity_code like '%'||#{deformity_code}||'%' ]]>
		</if>
		<if test="deformity_code_name !=null and deformity_code_name !=''"> 
	        	<![CDATA[  and a.deformity_code_name like '%'||#{deformity_code_name}||'%' ]]>
		</if>
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimDeformityGradeForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.DEFORMITY_TYPE,B.DEFORMITY_TYPE_NAME, B.END_DATE, B.DEFORMITY_GRADE,B.DEFORMITY_GRADE_NAME, B.START_DATE, 
			B.DEFORMITY_GRADE_ID, B.DEFORMITY_CODE,B.DEFORMITY_CODE1_NAME, B.IS_EFFECTIVE, B.DEFORMITY_RATE, 
			B.ICF_CODE,B.UPDATE_TIME,b.deformity_code_name FROM (
					SELECT ROWNUM RN, A.DEFORMITY_TYPE, (select deformity_type_name from APP___CLM__DBUSER.T_DEFORMITY_TYPE deforType where deforType.deformity_type=a.DEFORMITY_TYPE) deformity_type_name,
					A.END_DATE, 
					A.DEFORMITY_GRADE, 
					(select DEFORMITY_GRADE_NAME from APP___CLM__DBUSER.T_DEFORMITY_GRADE deformityGrade where a.deformity_type=deformityGrade.deformity_type and a.DEFORMITY_GRADE=deformityGrade.deformity_grade) deformity_grade_name,
					A.START_DATE, 
			A.DEFORMITY_GRADE_ID, 
			A.DEFORMITY_CODE, 
			(select DEFORMITY_CODE1_NAME from APP___CLM__DBUSER.T_DEFORMITY_CODE1 code1 where code1.deformity_type=a.deformity_type and a.deformity_grade=code1.deformity_grade and code1.deformity_code1=a.deformity_code) deformity_code1_name,
			A.IS_EFFECTIVE, A.DEFORMITY_RATE, 
			A.ICF_CODE,UPDATE_TIME,a.deformity_code_name FROM APP___CLM__DBUSER.T_CLAIM_DEFORMITY_GRADE A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<if test="deformity_type !=null and deformity_type !=''"> 
	        	<![CDATA[  and a.deformity_type = #{deformity_type } ]]>
		</if>
		<if test="deformity_grade !=null and deformity_grade !=''"> 
	        	<![CDATA[  and a.deformity_grade = #{deformity_grade } ]]>
		</if>
		<if test="deformity_code !=null and deformity_code !=''"> 
	        	<![CDATA[  and a.deformity_code like '%'||#{deformity_code}||'%' ]]>
		</if>
		<if test="deformity_code_name !=null and deformity_code_name !=''"> 
	        	<![CDATA[  and a.deformity_code_name like '%'||#{deformity_code_name}||'%' ]]>
		</if>
		
		<!-- <if test="deformity_code1_name !=null and deformity_code1_name !=''"> 
	        	<![CDATA[  and (select DEFORMITY_CODE1_NAME from APP___CLM__DBUSER.T_DEFORMITY_CODE1 code1 where code1.deformity_type=a.deformity_type and a.deformity_grade=code1.deformity_grade and code1.deformity_code1=a.deformity_code) like  '%'||#{deformity_code1_name}||'%' ]]>
		</if> -->
		<![CDATA[ ORDER BY A.DEFORMITY_TYPE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
	
	
</mapper>
