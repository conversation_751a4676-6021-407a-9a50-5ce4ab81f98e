<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.UwDecisionReasonPO">

	<sql id="uwDecisionReasonWhereCondition">
		<if test=" condition_list_id  != null "><![CDATA[ AND A.CONDITION_LIST_ID = #{condition_list_id} ]]></if>
		<if test=" uw_id  != null "><![CDATA[ AND A.UW_ID = #{uw_id} ]]></if>
		<if test=" extra_prem_id  != null "><![CDATA[ AND A.EXTRA_PREM_ID = #{extra_prem_id} ]]></if>
		<if test=" uw_busi_id  != null "><![CDATA[ AND A.UW_BUSI_ID = #{uw_busi_id} ]]></if>
		<if test=" uw_prd_id  != null "><![CDATA[ AND A.UW_PRD_ID = #{uw_prd_id} ]]></if>
		<if test=" limit_id  != null "><![CDATA[ AND A.LIMIT_ID = #{limit_id} ]]></if>
		<if test=" reason_type  != null "><![CDATA[ AND A.REASON_TYPE = #{reason_type} ]]></if>
		<if test=" reason_option != null and reason_option != ''  "><![CDATA[ AND A.REASON_OPTION = #{reason_option} ]]></if>
		<if test=" uw_user_code  != null "><![CDATA[ AND A.UW_USER_CODE = #{uw_user_code} ]]></if>
		<if test=" reason_id  != null "><![CDATA[ AND A.REASON_ID = #{reason_id} ]]></if>
		<if test=" reason_content != null and reason_content != ''  "><![CDATA[ AND A.REASON_CONTENT = #{reason_content} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryUwDecisionReasonByReasonIdCondition">
		<if test=" reason_id  != null "><![CDATA[ AND A.REASON_ID = #{reason_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addUwDecisionReason"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="reason_id">
			SELECT APP___CLM__DBUSER.S_UW_DECISION_REASON.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_UW_DECISION_REASON(
				CONDITION_LIST_ID, UW_ID, EXTRA_PREM_ID, INSERT_TIME, UW_BUSI_ID, UPDATE_TIME, UW_PRD_ID, 
				LIMIT_ID, REASON_TYPE, REASON_OPTION, INSERT_TIMESTAMP, UPDATE_BY, UW_USER_CODE, UPDATE_TIMESTAMP, 
				INSERT_BY, REASON_ID, REASON_CONTENT ) 
			VALUES (
				#{condition_list_id, jdbcType=NUMERIC}, #{uw_id, jdbcType=NUMERIC} , #{extra_prem_id, jdbcType=NUMERIC} , SYSDATE , #{uw_busi_id, jdbcType=NUMERIC} , SYSDATE , #{uw_prd_id, jdbcType=NUMERIC} 
				, #{limit_id, jdbcType=NUMERIC} , #{reason_type, jdbcType=NUMERIC} , #{reason_option, jdbcType=VARCHAR} , CURRENT_TIMESTAMP, #{update_by, jdbcType=NUMERIC} , #{uw_user_code, jdbcType=NUMERIC} , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} , #{reason_id, jdbcType=NUMERIC} , #{reason_content, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteUwDecisionReason" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_UW_DECISION_REASON WHERE REASON_ID = #{reason_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateUwDecisionReason" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_UW_DECISION_REASON ]]>
		<set>
		<trim suffixOverrides=",">
		    CONDITION_LIST_ID = #{condition_list_id, jdbcType=NUMERIC} ,
		    UW_ID = #{uw_id, jdbcType=NUMERIC} ,
		    EXTRA_PREM_ID = #{extra_prem_id, jdbcType=NUMERIC} ,
		    UW_BUSI_ID = #{uw_busi_id, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    UW_PRD_ID = #{uw_prd_id, jdbcType=NUMERIC} ,
		    LIMIT_ID = #{limit_id, jdbcType=NUMERIC} ,
		    REASON_TYPE = #{reason_type, jdbcType=NUMERIC} ,
			REASON_OPTION = #{reason_option, jdbcType=VARCHAR} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UW_USER_CODE = #{uw_user_code, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			REASON_CONTENT = #{reason_content, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE REASON_ID = #{reason_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findUwDecisionReasonByReasonId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONDITION_LIST_ID, A.UW_ID, A.EXTRA_PREM_ID, A.UW_BUSI_ID, A.UW_PRD_ID, 
			A.LIMIT_ID, A.REASON_TYPE, A.REASON_OPTION, A.UW_USER_CODE, 
			A.REASON_ID, A.REASON_CONTENT FROM APP___CLM__DBUSER.T_UW_DECISION_REASON A WHERE 1 = 1  ]]>
		<include refid="queryUwDecisionReasonByReasonIdCondition" />
		<![CDATA[ ORDER BY A.REASON_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapUwDecisionReason" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONDITION_LIST_ID, A.UW_ID, A.EXTRA_PREM_ID, A.UW_BUSI_ID, A.UW_PRD_ID, 
			A.LIMIT_ID, A.REASON_TYPE, A.REASON_OPTION, A.UW_USER_CODE, 
			A.REASON_ID, A.REASON_CONTENT FROM APP___CLM__DBUSER.T_UW_DECISION_REASON A WHERE ROWNUM <=  1000  ]]>
		<include refid="uwDecisionReasonWhereCondition" />
		<![CDATA[ ORDER BY A.REASON_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllUwDecisionReason" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CONDITION_LIST_ID, A.UW_ID, A.EXTRA_PREM_ID, A.UW_BUSI_ID, A.UW_PRD_ID, 
			A.LIMIT_ID, A.REASON_TYPE, A.REASON_OPTION, A.UW_USER_CODE, 
			A.REASON_ID, A.REASON_CONTENT FROM APP___CLM__DBUSER.T_UW_DECISION_REASON A WHERE ROWNUM <=  1000  ]]>
		<include refid="uwDecisionReasonWhereCondition" />
		<![CDATA[ ORDER BY A.REASON_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findUwDecisionReasonTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_UW_DECISION_REASON A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryUwDecisionReasonForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.CONDITION_LIST_ID, B.UW_ID, B.EXTRA_PREM_ID, B.UW_BUSI_ID, B.UW_PRD_ID, 
			B.LIMIT_ID, B.REASON_TYPE, B.REASON_OPTION, B.UW_USER_CODE, 
			B.REASON_ID, B.REASON_CONTENT FROM (
					SELECT ROWNUM RN, A.CONDITION_LIST_ID, A.UW_ID, A.EXTRA_PREM_ID, A.UW_BUSI_ID, A.UW_PRD_ID, 
			A.LIMIT_ID, A.REASON_TYPE, A.REASON_OPTION, A.UW_USER_CODE, 
			A.REASON_ID, A.REASON_CONTENT FROM APP___CLM__DBUSER.T_UW_DECISION_REASON A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.REASON_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
