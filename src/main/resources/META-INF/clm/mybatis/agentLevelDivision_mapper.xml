<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.AgentLevelDivisionPO">

	<sql id="agentLevelDivisionWhereConditionClm">
		<if test=" agent_merit_type_detail != null and agent_merit_type_detail != ''  "><![CDATA[ AND A.AGENT_MERIT_TYPE_DETAIL = #{merit_type} ]]></if>
		<if test=" cs != null and cs != ''  "><![CDATA[ AND A.CS = #{cs} ]]></if>
		<if test=" customer_service != null and customer_service != ''  "><![CDATA[ AND A.CUSTOMER_SERVICE = #{customer_service} ]]></if>
		<if test=" start_time  != null  and  start_time  != ''  "><![CDATA[ AND A.START_TIME = #{start_time} ]]></if>
		<if test=" agent_channel != null and agent_channel != ''  "><![CDATA[ AND A.AGENT_CHANNEL = #{agent_channel} ]]></if>
		<if test=" clm != null and clm != ''  "><![CDATA[ AND A.CLM = #{clm} ]]></if>
		<if test=" service_platform != null and service_platform != ''  "><![CDATA[ AND A.SERVICE_PLATFORM = #{service_platform} ]]></if>
		<if test=" fourteen_continue_rate  != null "><![CDATA[ AND A.FOURTEEN_CONTINUE_RATE = #{fourteen_continue_rate} ]]></if>
		<if test=" end_time  != null  and  end_time  != ''  "><![CDATA[ AND A.END_TIME = #{end_time} ]]></if>
		<if test=" nb != null and nb != ''  "><![CDATA[ AND A.NB = #{nb} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" uw != null and uw != ''  "><![CDATA[ AND A.UW = #{uw} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryAgentLevelDivisionByAgentCodeConditionClm">
		<if test=" agent_code != null and agent_code != '' "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
	</sql>	

<!-- 删除操作 -->	
	<delete id="deleteAgentLevelDivisionClm" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_AGENT_LEVEL_DIVISION WHERE AGENT_CODE = #{agent_code} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateAgentLevelDivisionClm" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_AGENT_LEVEL_DIVISION ]]>
		<set>
		<trim suffixOverrides=",">
			AGENT_MERIT_TYPE_DETAIL = #{merit_type, jdbcType=VARCHAR} ,
			CS = #{cs, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
			CUSTOMER_SERVICE = #{customer_service, jdbcType=VARCHAR} ,
		    START_TIME = #{start_time, jdbcType=DATE} ,
			AGENT_CHANNEL = #{agent_channel, jdbcType=VARCHAR} ,
			CLM = #{clm, jdbcType=VARCHAR} ,
			SERVICE_PLATFORM = #{service_platform, jdbcType=VARCHAR} ,
		    FOURTEEN_CONTINUE_RATE = #{fourteen_continue_rate, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		    END_TIME = #{end_time, jdbcType=DATE} ,
			NB = #{nb, jdbcType=VARCHAR} ,
			UW = #{uw, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE AGENT_CODE = #{agent_code} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findAgentLevelDivisionByAgentCodeClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_MERIT_TYPE_DETAIL, A.CS, A.CUSTOMER_SERVICE, A.START_TIME, A.AGENT_CHANNEL, 
			A.CLM, A.SERVICE_PLATFORM, A.FOURTEEN_CONTINUE_RATE, A.END_TIME, 
			A.NB, A.AGENT_CODE, A.UW FROM APP___CLM__DBUSER.T_AGENT_LEVEL_DIVISION A WHERE 1 = 1  ]]>
		<include refid="queryAgentLevelDivisionByAgentCodeConditionClm" />
		<![CDATA[ ORDER BY A.AGENT_CODE ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapAgentLevelDivisionClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_MERIT_TYPE_DETAIL, A.CS, A.CUSTOMER_SERVICE, A.START_TIME, A.AGENT_CHANNEL, 
			A.CLM, A.SERVICE_PLATFORM, A.FOURTEEN_CONTINUE_RATE, A.END_TIME, 
			A.NB, A.AGENT_CODE, A.UW FROM APP___CLM__DBUSER.T_AGENT_LEVEL_DIVISION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.AGENT_CODE ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllAgentLevelDivisionClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_MERIT_TYPE_DETAIL, A.CS, A.CUSTOMER_SERVICE, A.START_TIME, A.AGENT_CHANNEL, 
			A.CLM, A.SERVICE_PLATFORM, A.FOURTEEN_CONTINUE_RATE, A.END_TIME, 
			A.NB, A.AGENT_CODE, A.UW FROM APP___CLM__DBUSER.T_AGENT_LEVEL_DIVISION A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.AGENT_CODE ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findAgentLevelDivisionTotalClm" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_AGENT_LEVEL_DIVISION A WHERE 1 = 1  ]]>
		<!-- <include refid="请添加查询条件" /> -->
	</select>

<!-- 分页查询操作 -->
	<select id="queryAgentLevelDivisionForPageClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.AGENT_MERIT_TYPE_DETAIL, B.CS, B.CUSTOMER_SERVICE, B.START_TIME, B.AGENT_CHANNEL, 
			B.CLM, B.SERVICE_PLATFORM, B.FOURTEEN_CONTINUE_RATE, B.END_TIME, 
			B.NB, B.AGENT_CODE, B.UW FROM (
					SELECT ROWNUM RN, A.AGENT_MERIT_TYPE_DETAIL, A.CS, A.CUSTOMER_SERVICE, A.START_TIME, A.AGENT_CHANNEL, 
			A.CLM, A.SERVICE_PLATFORM, A.FOURTEEN_CONTINUE_RATE, A.END_TIME, 
			A.NB, A.AGENT_CODE, A.UW FROM APP___CLM__DBUSER.T_AGENT_LEVEL_DIVISION A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.AGENT_CODE ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
<!-- 查询所有操作 -->
	<select id="findAllAgentLevelDivisionPasClm" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.AGENT_MERIT_TYPE_DETAIL, A.CS, A.CUSTOMER_SERVICE, A.START_TIME, A.AGENT_CHANNEL, 
			A.CLM, A.SERVICE_PLATFORM, A.FOURTEEN_CONTINUE_RATE, A.END_TIME, 
			A.NB, A.AGENT_CODE, A.UW FROM APP___PAS__DBUSER.T_AGENT_LEVEL_DIVISION A WHERE ROWNUM <=  1000  ]]>
		<if test=" agent_code != null and agent_code != '' "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<![CDATA[ ORDER BY A.AGENT_CODE ]]> 
	</select>	
	
</mapper>
