<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="CLM_PremArapPO">

	<sql id="premArapWhereCondition">
	<if test=" cip_bank_code != null and cip_bank_code != ''  "><![CDATA[ AND A.CIP_BANK_CODE = #{cip_bank_code} ]]></if>
	<if test=" cip_branch_bank_code != null and cip_branch_bank_code != ''  "><![CDATA[ AND A.CIP_BRANCH_BANK_CODE = #{cip_branch_bank_code} ]]></if>
	<if test=" cip_district_bank_code != null and cip_district_bank_code != ''  "><![CDATA[ AND A.CIP_DISTRICT_BANK_CODE = #{cip_district_bank_code} ]]></if>
		<if test=" task_mark  != null "><![CDATA[ AND A.TASK_MARK = #{task_mark} ]]></if>
		<if test=" policy_organ_code != null and policy_organ_code != ''  "><![CDATA[ AND A.POLICY_ORGAN_CODE = #{policy_organ_code} ]]></if>
		<if test=" holder_name != null and holder_name != ''  "><![CDATA[ AND A.HOLDER_NAME = #{holder_name} ]]></if>
		<if test=" is_item_main  != null "><![CDATA[ AND A.IS_ITEM_MAIN = #{is_item_main} ]]></if>
		<if test=" bookkeeping_id  != null "><![CDATA[ AND A.BOOKKEEPING_ID = #{bookkeeping_id} ]]></if>
		<if test=" batch_no != null and batch_no != ''  "><![CDATA[ AND A.BATCH_NO = #{batch_no} ]]></if>
		<if test=" busi_prod_name != null and busi_prod_name != ''  "><![CDATA[ AND A.BUSI_PROD_NAME = #{busi_prod_name} ]]></if>
		<if test=" payee_phone != null and payee_phone != ''  "><![CDATA[ AND A.PAYEE_PHONE = #{payee_phone} ]]></if>
		<if test=" unit_number != null and unit_number != ''  "><![CDATA[ AND A.UNIT_NUMBER = #{unit_number} ]]></if>
		<if test=" paid_count  != null "><![CDATA[ AND A.PAID_COUNT = #{paid_count} ]]></if>
		<if test=" funds_rtn_code != null and funds_rtn_code != ''  "><![CDATA[ AND A.FUNDS_RTN_CODE = #{funds_rtn_code} ]]></if>
		<if test=" customer_account_flag  != null "><![CDATA[ AND A.CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag} ]]></if>
		<if test=" fee_type != null and fee_type != ''  "><![CDATA[ AND A.FEE_TYPE = #{fee_type} ]]></if>
		<if test=" seq_no  != null "><![CDATA[ AND A.SEQ_NO = #{seq_no} ]]></if>
		<if test=" busi_prod_code != null and busi_prod_code != ''  "><![CDATA[ AND A.BUSI_PROD_CODE = #{busi_prod_code} ]]></if>
		<if test=" apply_code != null and apply_code != ''  "><![CDATA[ AND A.APPLY_CODE = #{apply_code} ]]></if>
		<if test=" fee_status_date  != null  and  fee_status_date  != ''  "><![CDATA[ AND A.FEE_STATUS_DATE = #{fee_status_date} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" red_bookkeeping_time  != null  and  red_bookkeeping_time  != ''  "><![CDATA[ AND A.RED_BOOKKEEPING_TIME = #{red_bookkeeping_time} ]]></if>
		<if test=" channel_type != null and channel_type != ''  "><![CDATA[ AND A.CHANNEL_TYPE = #{channel_type} ]]></if>
		<if test=" is_bank_text_date  != null  and  is_bank_text_date  != ''  "><![CDATA[ AND A.IS_BANK_TEXT_DATE = #{is_bank_text_date} ]]></if>
		<if test=" charge_year  != null "><![CDATA[ AND A.CHARGE_YEAR = #{charge_year} ]]></if>
		<if test=" is_risk_main  != null "><![CDATA[ AND A.IS_RISK_MAIN = #{is_risk_main} ]]></if>
		<if test=" is_bank_account  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT = #{is_bank_account} ]]></if>
		<if test=" frozen_status != null and frozen_status != ''  "><![CDATA[ AND A.FROZEN_STATUS = #{frozen_status} ]]></if>
		<if test=" posted != null and posted != ''  "><![CDATA[ AND A.POSTED = #{posted} ]]></if>
		<if test=" bookkeeping_flag  != null "><![CDATA[ AND A.BOOKKEEPING_FLAG = #{bookkeeping_flag} ]]></if>
		<if test=" group_id  != null "><![CDATA[ AND A.GROUP_ID = #{group_id} ]]></if>
		<if test=" red_bookkeeping_by  != null "><![CDATA[ AND A.RED_BOOKKEEPING_BY = #{red_bookkeeping_by} ]]></if>
		<if test=" frozen_status_date  != null  and  frozen_status_date  != ''  "><![CDATA[ AND A.FROZEN_STATUS_DATE = #{frozen_status_date} ]]></if>
		<if test=" insured_name != null and insured_name != ''  "><![CDATA[ AND A.INSURED_NAME = #{insured_name} ]]></if>
		<if test=" cus_acc_details_id  != null "><![CDATA[ AND A.CUS_ACC_DETAILS_ID = #{cus_acc_details_id} ]]></if>
		<if test=" insured_id  != null "><![CDATA[ AND A.INSURED_ID = #{insured_id} ]]></if>
		<if test=" policy_type != null and policy_type != ''  "><![CDATA[ AND A.POLICY_TYPE = #{policy_type} ]]></if>
		<if test=" product_channel != null and product_channel != ''  "><![CDATA[ AND A.PRODUCT_CHANNEL = #{product_channel} ]]></if>
		<if test=" is_bank_account_date  != null  and  is_bank_account_date  != ''  "><![CDATA[ AND A.IS_BANK_ACCOUNT_DATE = #{is_bank_account_date} ]]></if>
		<if test=" policy_code != null and policy_code != ''  "><![CDATA[ AND A.POLICY_CODE = #{policy_code} ]]></if>
		<if test=" pay_mode != null and pay_mode != ''  "><![CDATA[ AND A.PAY_MODE = #{pay_mode} ]]></if>
		<if test=" operator_by  != null "><![CDATA[ AND A.OPERATOR_BY = #{operator_by} ]]></if>
		<if test=" branch_code != null and branch_code != ''  "><![CDATA[ AND A.BRANCH_CODE = #{branch_code} ]]></if>
		<if test=" business_type != null and business_type != ''  "><![CDATA[ AND A.BUSINESS_TYPE = #{business_type} ]]></if>
		<if test=" validate_date  != null  and  validate_date  != ''  "><![CDATA[ AND A.VALIDATE_DATE = #{validate_date} ]]></if>
		<if test=" red_belnr != null and red_belnr != ''  "><![CDATA[ AND A.RED_BELNR = #{red_belnr} ]]></if>
		<if test=" bank_text_status != null and bank_text_status != ''  "><![CDATA[ AND A.BANK_TEXT_STATUS = #{bank_text_status} ]]></if>
		<if test=" group_code != null and group_code != ''  "><![CDATA[ AND A.GROUP_CODE = #{group_code} ]]></if>
		<if test=" certi_type != null and certi_type != ''  "><![CDATA[ AND A.CERTI_TYPE = #{certi_type} ]]></if>
		<if test=" cus_acc_update_by  != null "><![CDATA[ AND A.CUS_ACC_UPDATE_BY = #{cus_acc_update_by} ]]></if>
		<if test=" is_bank_text_by  != null "><![CDATA[ AND A.IS_BANK_TEXT_BY = #{is_bank_text_by} ]]></if>
		<if test=" money_code != null and money_code != ''  "><![CDATA[ AND A.MONEY_CODE = #{money_code} ]]></if>
		<if test=" business_code != null and business_code != ''  "><![CDATA[ AND A.BUSINESS_CODE = #{business_code} ]]></if>
		<if test=" is_bank_account_by  != null "><![CDATA[ AND A.IS_BANK_ACCOUNT_BY = #{is_bank_account_by} ]]></if>
		<if test=" payee_name != null and payee_name != ''  "><![CDATA[ AND A.PAYEE_NAME = #{payee_name} ]]></if>
		<if test=" bank_account != null and bank_account != ''  "><![CDATA[ AND A.BANK_ACCOUNT = #{bank_account} ]]></if>
		<if test=" red_bookkeeping_flag  != null "><![CDATA[ AND A.RED_BOOKKEEPING_FLAG = #{red_bookkeeping_flag} ]]></if>
		<if test=" cus_acc_fee_amount  != null "><![CDATA[ AND A.CUS_ACC_FEE_AMOUNT = #{cus_acc_fee_amount} ]]></if>
		<if test=" customer_id  != null "><![CDATA[ AND A.CUSTOMER_ID = #{customer_id} ]]></if>
		<if test=" finish_time  != null  and  finish_time  != ''  "><![CDATA[ AND A.FINISH_TIME = #{finish_time} ]]></if>
		<if test=" due_time  != null  and  due_time  != ''  "><![CDATA[ AND A.DUE_TIME = #{due_time} ]]></if>
		<if test=" is_bank_text  != null "><![CDATA[ AND A.IS_BANK_TEXT = #{is_bank_text} ]]></if>
		<if test=" certi_code != null and certi_code != ''  "><![CDATA[ AND A.CERTI_CODE = #{certi_code} ]]></if>
		<if test=" busi_apply_date  != null  and  busi_apply_date  != ''  "><![CDATA[ AND A.BUSI_APPLY_DATE = #{busi_apply_date} ]]></if>
		<if test=" group_name != null and group_name != ''  "><![CDATA[ AND A.GROUP_NAME = #{group_name} ]]></if>
		<if test=" belnr != null and belnr != ''  "><![CDATA[ AND A.BELNR = #{belnr} ]]></if>
		<if test=" fee_amount  != null "><![CDATA[ AND A.FEE_AMOUNT = #{fee_amount} ]]></if>
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
		<if test=" service_code != null and service_code != ''  "><![CDATA[ AND A.SERVICE_CODE = #{service_code} ]]></if>
		<if test=" holder_id  != null "><![CDATA[ AND A.HOLDER_ID = #{holder_id} ]]></if>
		<if test=" withdraw_type != null and withdraw_type != ''  "><![CDATA[ AND A.WITHDRAW_TYPE = #{withdraw_type} ]]></if>
		<if test=" rollback_unit_number != null and rollback_unit_number != ''  "><![CDATA[ AND A.ROLLBACK_UNIT_NUMBER = #{rollback_unit_number} ]]></if>
		<if test=" fail_times  != null "><![CDATA[ AND A.FAIL_TIMES = #{fail_times} ]]></if>
		<if test=" policy_year  != null "><![CDATA[ AND A.POLICY_YEAR = #{policy_year} ]]></if>
		<if test=" frozen_status_by  != null "><![CDATA[ AND A.FROZEN_STATUS_BY = #{frozen_status_by} ]]></if>
		<if test=" bank_user_name != null and bank_user_name != ''  "><![CDATA[ AND A.BANK_USER_NAME = #{bank_user_name} ]]></if>
		<if test=" fee_status != null and fee_status != ''  "><![CDATA[ AND A.FEE_STATUS = #{fee_status} ]]></if>
		<if test=" fee_status_by  != null "><![CDATA[ AND A.FEE_STATUS_BY = #{fee_status_by} ]]></if>
		<if test=" pay_end_date  != null  and  pay_end_date  != ''  "><![CDATA[ AND A.PAY_END_DATE = #{pay_end_date} ]]></if>
		<if test=" deriv_type != null and deriv_type != ''  "><![CDATA[ AND A.DERIV_TYPE = #{deriv_type} ]]></if>
		<if test=" red_bookkeeping_id  != null "><![CDATA[ AND A.RED_BOOKKEEPING_ID = #{red_bookkeeping_id} ]]></if>
		<if test=" bookkeeping_by  != null "><![CDATA[ AND A.BOOKKEEPING_BY = #{bookkeeping_by} ]]></if>
		<if test=" cus_acc_update_time  != null  and  cus_acc_update_time  != ''  "><![CDATA[ AND A.CUS_ACC_UPDATE_TIME = #{cus_acc_update_time} ]]></if>
		<if test=" arap_flag != null and arap_flag != ''  "><![CDATA[ AND A.ARAP_FLAG = #{arap_flag} ]]></if>
		<if test=" bank_code != null and bank_code != ''  "><![CDATA[ AND A.BANK_CODE = #{bank_code} ]]></if>
		<if test=" bookkeeping_time  != null  and  bookkeeping_time  != ''  "><![CDATA[ AND A.BOOKKEEPING_TIME = #{bookkeeping_time} ]]></if>
		<if test=" refeflag != null and refeflag != ''  "><![CDATA[ AND A.REFEFLAG = #{refeflag} ]]></if>
		<if test=" agent_code != null and agent_code != ''  "><![CDATA[ AND A.AGENT_CODE = #{agent_code} ]]></if>
		<if test=" prem_freq  != null "><![CDATA[ AND A.PREM_FREQ = #{prem_freq} ]]></if>
		<if test=" audit_date  != null  and  audit_date  != ''  "><![CDATA[ AND A.AUDIT_DATE = #{audit_date} ]]></if>
		<if test=" product_code  != null  and  product_code  != ''  "><![CDATA[ AND A.product_code = #{product_code} ]]></if>
	    <if test=" pay_liab_code  != null  and  pay_liab_code  != ''  "><![CDATA[ AND A.pay_liab_code = #{pay_liab_code} ]]></if>
	    <if test=" source_table  != null  and  source_table  != ''  "><![CDATA[ AND A.source_table = #{source_table} ]]></if>
	    <if test=" source_table_pk  != null  and  source_table_pk  != ''  "><![CDATA[ AND A.source_table_pk = #{source_table_pk} ]]></if>
	    <if test=" charge_period  != null  and  charge_period  != ''  "><![CDATA[ AND A.CHARGE_PERIOD = #{charge_period} ]]></if>
	    <if test=" busi_item_id  != null "><![CDATA[ AND A.BUSI_ITEM_ID = #{busi_item_id} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryPremArapByListIdCondition">
		<if test=" list_id  != null "><![CDATA[ AND A.LIST_ID = #{list_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addPremArap"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.math.BigDecimal" order="BEFORE" keyProperty="list_id">
			SELECT APP___CLM__DBUSER.S_PREM_ARAP.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_PREM_ARAP(CIP_BANK_CODE,CIP_BRANCH_BANK_CODE,CIP_DISTRICT_BANK_CODE,
				TASK_MARK, POLICY_ORGAN_CODE, HOLDER_NAME, IS_ITEM_MAIN, BOOKKEEPING_ID, BATCH_NO, BUSI_PROD_NAME, 
				PAYEE_PHONE, UNIT_NUMBER, PAID_COUNT, FUNDS_RTN_CODE, CUSTOMER_ACCOUNT_FLAG, FEE_TYPE, SEQ_NO, 
				BUSI_PROD_CODE, APPLY_CODE, FEE_STATUS_DATE, ORGAN_CODE, RED_BOOKKEEPING_TIME, CHANNEL_TYPE, IS_BANK_TEXT_DATE, 
				UPDATE_BY, CHARGE_YEAR, IS_RISK_MAIN, IS_BANK_ACCOUNT, FROZEN_STATUS, POSTED, BOOKKEEPING_FLAG, 
				GROUP_ID, RED_BOOKKEEPING_BY, UPDATE_TIME, FROZEN_STATUS_DATE, INSURED_NAME, CUS_ACC_DETAILS_ID, INSURED_ID, 
				POLICY_TYPE, PRODUCT_CHANNEL, IS_BANK_ACCOUNT_DATE, POLICY_CODE, PAY_MODE, OPERATOR_BY, BRANCH_CODE, 
				BUSINESS_TYPE, VALIDATE_DATE, UPDATE_TIMESTAMP, INSERT_BY, RED_BELNR, BANK_TEXT_STATUS, GROUP_CODE, 
				CERTI_TYPE, CUS_ACC_UPDATE_BY, IS_BANK_TEXT_BY, MONEY_CODE, BUSINESS_CODE, IS_BANK_ACCOUNT_BY, PAYEE_NAME, 
				BANK_ACCOUNT, RED_BOOKKEEPING_FLAG, CUS_ACC_FEE_AMOUNT, CUSTOMER_ID, FINISH_TIME, INSERT_TIMESTAMP, DUE_TIME, 
				IS_BANK_TEXT, CERTI_CODE, BUSI_APPLY_DATE, GROUP_NAME, BELNR, FEE_AMOUNT, LIST_ID, 
				SERVICE_CODE, HOLDER_ID, WITHDRAW_TYPE, ROLLBACK_UNIT_NUMBER, FAIL_TIMES, INSERT_TIME, POLICY_YEAR, 
				FROZEN_STATUS_BY, BANK_USER_NAME, FEE_STATUS, FEE_STATUS_BY, PAY_END_DATE, DERIV_TYPE, RED_BOOKKEEPING_ID, 
				BOOKKEEPING_BY, CUS_ACC_UPDATE_TIME, ARAP_FLAG, BANK_CODE, BOOKKEEPING_TIME, REFEFLAG, AGENT_CODE, 
				PREM_FREQ,CLAIM_NATURE,CLAIM_TYPE,AUDIT_DATE ,PRODUCT_CODE,PAY_LIAB_CODE, SOURCE_TABLE,SOURCE_TABLE_PK
				,IS_CORPORATE,CHARGE_PERIOD,BUSI_ITEM_ID) 
			VALUES (#{cip_bank_code, jdbcType=VARCHAR} ,#{cip_branch_bank_code, jdbcType=VARCHAR} ,#{cip_district_bank_code, jdbcType=VARCHAR} ,
				#{task_mark, jdbcType=NUMERIC}, #{policy_organ_code, jdbcType=VARCHAR} , #{holder_name, jdbcType=VARCHAR} , #{is_item_main, jdbcType=NUMERIC} , #{bookkeeping_id, jdbcType=NUMERIC} , #{batch_no, jdbcType=VARCHAR} , #{busi_prod_name, jdbcType=VARCHAR} 
				, #{payee_phone, jdbcType=VARCHAR} , #{unit_number, jdbcType=VARCHAR} , #{paid_count, jdbcType=NUMERIC} , #{funds_rtn_code, jdbcType=VARCHAR} , #{customer_account_flag, jdbcType=NUMERIC} , #{fee_type, jdbcType=VARCHAR} , #{seq_no, jdbcType=NUMERIC} 
				, #{busi_prod_code, jdbcType=VARCHAR} , #{apply_code, jdbcType=VARCHAR} , #{fee_status_date, jdbcType=DATE} , #{organ_code, jdbcType=VARCHAR} , #{red_bookkeeping_time, jdbcType=DATE} , #{channel_type, jdbcType=VARCHAR} , #{is_bank_text_date, jdbcType=DATE} 
				, #{update_by, jdbcType=NUMERIC} , #{charge_year, jdbcType=NUMERIC} , #{is_risk_main, jdbcType=NUMERIC} , #{is_bank_account, jdbcType=NUMERIC} , #{frozen_status, jdbcType=VARCHAR} , #{posted, jdbcType=VARCHAR} , #{bookkeeping_flag, jdbcType=NUMERIC} 
				, #{group_id, jdbcType=NUMERIC} , #{red_bookkeeping_by, jdbcType=NUMERIC} , SYSDATE , #{frozen_status_date, jdbcType=DATE} , #{insured_name, jdbcType=VARCHAR} , #{cus_acc_details_id, jdbcType=NUMERIC} , #{insured_id, jdbcType=NUMERIC} 
				, #{policy_type, jdbcType=VARCHAR} , #{product_channel, jdbcType=VARCHAR} , #{is_bank_account_date, jdbcType=DATE} , #{policy_code, jdbcType=VARCHAR} , #{pay_mode, jdbcType=VARCHAR} , #{operator_by, jdbcType=NUMERIC} , #{branch_code, jdbcType=VARCHAR} 
				, #{business_type, jdbcType=VARCHAR} , #{validate_date, jdbcType=DATE} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{red_belnr, jdbcType=VARCHAR} , #{bank_text_status, jdbcType=VARCHAR} , #{group_code, jdbcType=VARCHAR} 
				, #{certi_type, jdbcType=VARCHAR} , #{cus_acc_update_by, jdbcType=NUMERIC} , #{is_bank_text_by, jdbcType=NUMERIC} , #{money_code, jdbcType=VARCHAR} , #{business_code, jdbcType=VARCHAR} , #{is_bank_account_by, jdbcType=NUMERIC} , #{payee_name, jdbcType=VARCHAR} 
				, #{bank_account, jdbcType=VARCHAR} , #{red_bookkeeping_flag, jdbcType=NUMERIC} , #{cus_acc_fee_amount, jdbcType=NUMERIC} , #{customer_id, jdbcType=NUMERIC} , #{finish_time, jdbcType=TIMESTAMP} , CURRENT_TIMESTAMP, #{due_time, jdbcType=DATE} 
				, #{is_bank_text, jdbcType=NUMERIC} , #{certi_code, jdbcType=VARCHAR} , #{busi_apply_date, jdbcType=DATE} , #{group_name, jdbcType=VARCHAR} , #{belnr, jdbcType=VARCHAR} , #{fee_amount, jdbcType=NUMERIC} , #{list_id, jdbcType=NUMERIC} 
				, #{service_code, jdbcType=VARCHAR} , #{holder_id, jdbcType=NUMERIC} , #{withdraw_type, jdbcType=VARCHAR} , #{rollback_unit_number, jdbcType=VARCHAR} , #{fail_times, jdbcType=NUMERIC} , SYSDATE , #{policy_year, jdbcType=NUMERIC} 
				, #{frozen_status_by, jdbcType=NUMERIC} , #{bank_user_name, jdbcType=VARCHAR} , #{fee_status, jdbcType=VARCHAR} , #{fee_status_by, jdbcType=NUMERIC} , #{pay_end_date, jdbcType=DATE} , #{deriv_type, jdbcType=VARCHAR} , #{red_bookkeeping_id, jdbcType=NUMERIC} 
				, #{bookkeeping_by, jdbcType=NUMERIC} , #{cus_acc_update_time, jdbcType=DATE} , #{arap_flag, jdbcType=VARCHAR} , #{bank_code, jdbcType=VARCHAR} , #{bookkeeping_time, jdbcType=DATE} , #{refeflag, jdbcType=VARCHAR} , #{agent_code, jdbcType=VARCHAR} 
				, #{prem_freq, jdbcType=NUMERIC},#{claim_nature, jdbcType=NUMERIC} ,#{claim_type, jdbcType=CHAR},#{audit_date, jdbcType=DATE}     ,#{product_code, jdbcType=VARCHAR},#{pay_liab_code, jdbcType=VARCHAR}, #{source_table, jdbcType=VARCHAR},#{source_table_pk, jdbcType=NUMERIC}
				, #{is_corporate, jdbcType=VARCHAR}, #{charge_period, jdbcType=VARCHAR}, #{busi_item_id, jdbcType=NUMERIC}) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deletePremArap" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_PREM_ARAP WHERE LIST_ID = #{list_id} ]]>
	</delete>


<!-- 修改部分数据 -->
	<update id="updatePartialPremArap" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_PREM_ARAP ]]>
		<set>
		<trim suffixOverrides=",">
		    FEE_STATUS_BY = #{fee_status_by, jdbcType=NUMERIC} , 
		</trim>
 		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>
<!-- 修改部分数据 -->
	<update id="updatePremArapBookkeepingFlag" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_PREM_ARAP ]]>
		<set>
		<trim suffixOverrides=",">
		    BOOKKEEPING_FLAG = #{bookkeeping_flag, jdbcType=NUMERIC} , 
		</trim>
 		</set>
		<![CDATA[ WHERE UNIT_NUMBER = #{unit_number} ]]>
	</update>

<!-- 修改操作 -->
	<update id="updatePremArap" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_PREM_ARAP ]]>
		<set>
		<trim suffixOverrides=",">
		CIP_BANK_CODE = #{cip_bank_code, jdbcType=VARCHAR} ,
		CIP_BRANCH_BANK_CODE = #{cip_branch_bank_code, jdbcType=VARCHAR} ,
		CIP_DISTRICT_BANK_CODE = #{cip_district_bank_code, jdbcType=VARCHAR} ,
		    TASK_MARK = #{task_mark, jdbcType=NUMERIC} ,
			POLICY_ORGAN_CODE = #{policy_organ_code, jdbcType=VARCHAR} ,
			HOLDER_NAME = #{holder_name, jdbcType=VARCHAR} ,
		    IS_ITEM_MAIN = #{is_item_main, jdbcType=NUMERIC} ,
		    BOOKKEEPING_ID = #{bookkeeping_id, jdbcType=NUMERIC} ,
			BATCH_NO = #{batch_no, jdbcType=VARCHAR} ,
			BUSI_PROD_NAME = #{busi_prod_name, jdbcType=VARCHAR} ,
			PAYEE_PHONE = #{payee_phone, jdbcType=VARCHAR} ,
			UNIT_NUMBER = #{unit_number, jdbcType=VARCHAR} ,
		    PAID_COUNT = #{paid_count, jdbcType=NUMERIC} ,
			FUNDS_RTN_CODE = #{funds_rtn_code, jdbcType=VARCHAR} ,
		    CUSTOMER_ACCOUNT_FLAG = #{customer_account_flag, jdbcType=NUMERIC} ,
			FEE_TYPE = #{fee_type, jdbcType=VARCHAR} ,
		    SEQ_NO = #{seq_no, jdbcType=NUMERIC} ,
			BUSI_PROD_CODE = #{busi_prod_code, jdbcType=VARCHAR} ,
			APPLY_CODE = #{apply_code, jdbcType=VARCHAR} ,
		    FEE_STATUS_DATE = #{fee_status_date, jdbcType=DATE} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    RED_BOOKKEEPING_TIME = #{red_bookkeeping_time, jdbcType=DATE} ,
			CHANNEL_TYPE = #{channel_type, jdbcType=VARCHAR} ,
		    IS_BANK_TEXT_DATE = #{is_bank_text_date, jdbcType=DATE} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    CHARGE_YEAR = #{charge_year, jdbcType=NUMERIC} ,
		    IS_RISK_MAIN = #{is_risk_main, jdbcType=NUMERIC} ,
		    IS_BANK_ACCOUNT = #{is_bank_account, jdbcType=NUMERIC} ,
			FROZEN_STATUS = #{frozen_status, jdbcType=VARCHAR} ,
			POSTED = #{posted, jdbcType=VARCHAR} ,
		    BOOKKEEPING_FLAG = #{bookkeeping_flag, jdbcType=NUMERIC} ,
		    GROUP_ID = #{group_id, jdbcType=NUMERIC} ,
		    RED_BOOKKEEPING_BY = #{red_bookkeeping_by, jdbcType=NUMERIC} ,
			UPDATE_TIME = SYSDATE , 
		    FROZEN_STATUS_DATE = #{frozen_status_date, jdbcType=DATE} ,
			INSURED_NAME = #{insured_name, jdbcType=VARCHAR} ,
		    CUS_ACC_DETAILS_ID = #{cus_acc_details_id, jdbcType=NUMERIC} ,
		    INSURED_ID = #{insured_id, jdbcType=NUMERIC} ,
			POLICY_TYPE = #{policy_type, jdbcType=VARCHAR} ,
			PRODUCT_CHANNEL = #{product_channel, jdbcType=VARCHAR} ,
		    IS_BANK_ACCOUNT_DATE = #{is_bank_account_date, jdbcType=DATE} ,
			POLICY_CODE = #{policy_code, jdbcType=VARCHAR} ,
			PAY_MODE = #{pay_mode, jdbcType=VARCHAR} ,
		    OPERATOR_BY = #{operator_by, jdbcType=NUMERIC} ,
			BRANCH_CODE = #{branch_code, jdbcType=VARCHAR} ,
			BUSINESS_TYPE = #{business_type, jdbcType=VARCHAR} ,
		    VALIDATE_DATE = #{validate_date, jdbcType=DATE} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			RED_BELNR = #{red_belnr, jdbcType=VARCHAR} ,
			BANK_TEXT_STATUS = #{bank_text_status, jdbcType=VARCHAR} ,
			GROUP_CODE = #{group_code, jdbcType=VARCHAR} ,
			CERTI_TYPE = #{certi_type, jdbcType=VARCHAR} ,
		    CUS_ACC_UPDATE_BY = #{cus_acc_update_by, jdbcType=NUMERIC} ,
		    IS_BANK_TEXT_BY = #{is_bank_text_by, jdbcType=NUMERIC} ,
			MONEY_CODE = #{money_code, jdbcType=VARCHAR} ,
			BUSINESS_CODE = #{business_code, jdbcType=VARCHAR} ,
		    IS_BANK_ACCOUNT_BY = #{is_bank_account_by, jdbcType=NUMERIC} ,
			PAYEE_NAME = #{payee_name, jdbcType=VARCHAR} ,
			BANK_ACCOUNT = #{bank_account, jdbcType=VARCHAR} ,
		    RED_BOOKKEEPING_FLAG = #{red_bookkeeping_flag, jdbcType=NUMERIC} ,
		    CUS_ACC_FEE_AMOUNT = #{cus_acc_fee_amount, jdbcType=NUMERIC} ,
		    CUSTOMER_ID = #{customer_id, jdbcType=NUMERIC} ,
		    FINISH_TIME = #{finish_time, jdbcType=TIMESTAMP} ,
		    DUE_TIME = #{due_time, jdbcType=DATE} ,
		    IS_BANK_TEXT = #{is_bank_text, jdbcType=NUMERIC} ,
			CERTI_CODE = #{certi_code, jdbcType=VARCHAR} ,
		    BUSI_APPLY_DATE = #{busi_apply_date, jdbcType=DATE} ,
			GROUP_NAME = #{group_name, jdbcType=VARCHAR} ,
			BELNR = #{belnr, jdbcType=VARCHAR} ,
		    FEE_AMOUNT = #{fee_amount, jdbcType=NUMERIC} ,
			SERVICE_CODE = #{service_code, jdbcType=VARCHAR} ,
		    HOLDER_ID = #{holder_id, jdbcType=NUMERIC} ,
			WITHDRAW_TYPE = #{withdraw_type, jdbcType=VARCHAR} ,
			ROLLBACK_UNIT_NUMBER = #{rollback_unit_number, jdbcType=VARCHAR} ,
		    FAIL_TIMES = #{fail_times, jdbcType=NUMERIC} ,
		    POLICY_YEAR = #{policy_year, jdbcType=NUMERIC} ,
		    FROZEN_STATUS_BY = #{frozen_status_by, jdbcType=NUMERIC} ,
			BANK_USER_NAME = #{bank_user_name, jdbcType=VARCHAR} ,
			FEE_STATUS = #{fee_status, jdbcType=VARCHAR} ,
		    FEE_STATUS_BY = #{fee_status_by, jdbcType=NUMERIC} ,
		    PAY_END_DATE = #{pay_end_date, jdbcType=DATE} ,
			DERIV_TYPE = #{deriv_type, jdbcType=VARCHAR} ,
		    RED_BOOKKEEPING_ID = #{red_bookkeeping_id, jdbcType=NUMERIC} ,
		    BOOKKEEPING_BY = #{bookkeeping_by, jdbcType=NUMERIC} ,
		    CUS_ACC_UPDATE_TIME = #{cus_acc_update_time, jdbcType=DATE} ,
			ARAP_FLAG = #{arap_flag, jdbcType=VARCHAR} ,
			BANK_CODE = #{bank_code, jdbcType=VARCHAR} ,
		    BOOKKEEPING_TIME = #{bookkeeping_time, jdbcType=DATE} ,
			REFEFLAG = #{refeflag, jdbcType=VARCHAR} ,
			AGENT_CODE = #{agent_code, jdbcType=VARCHAR} ,
		    PREM_FREQ = #{prem_freq, jdbcType=NUMERIC} ,
		     CLAIM_NATURE=#{claim_nature, jdbcType=NUMERIC} ,
		     CLAIM_TYPE=#{claim_type, jdbcType=CHAR},
		     AUDIT_DATE = #{audit_date, jdbcType=DATE},
		     PRODUCT_CODE=#{product_code, jdbcType=VARCHAR},
   			  PAY_LIAB_CODE=#{pay_liab_code, jdbcType=VARCHAR},
      		SOURCE_TABLE=#{source_table, jdbcType=VARCHAR},
     		 SOURCE_TABLE_PK=#{source_table_pk, jdbcType=NUMERIC},
     		 IS_CORPORATE = #{is_corporate, jdbcType=VARCHAR},
     		 CHARGE_PERIOD = #{charge_period, jdbcType=VARCHAR},
     		 BUSI_ITEM_ID = #{busi_item_id, jdbcType=NUMERIC},
		</trim>
 		</set>
		<![CDATA[ WHERE LIST_ID = #{list_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findPremArapByListId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CIP_BANK_CODE,A.CIP_BRANCH_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ,A.CLAIM_NATURE,A.CLAIM_TYPE,A.AUDIT_DATE,A.IS_CORPORATE,A.CHARGE_PERIOD,A.BUSI_ITEM_ID FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="queryPremArapByListIdCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>
	
  
<!-- 按map查询操作 -->
	<select id="findAllMapPremArap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CIP_BANK_CODE,A.CIP_BRANCH_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ ,A.CLAIM_NATURE,A.CLAIM_TYPE,A.AUDIT_DATE,PRODUCT_CODE,PAY_LIAB_CODE, SOURCE_TABLE,SOURCE_TABLE_PK,IS_CORPORATE,A.CHARGE_PERIOD,A.BUSI_ITEM_ID FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
		<include refid="premArapWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllPremArap" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CIP_BANK_CODE,A.CIP_BRANCH_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ,A.CLAIM_NATURE,A.CLAIM_TYPE,A.AUDIT_DATE,PRODUCT_CODE,PAY_LIAB_CODE, SOURCE_TABLE,SOURCE_TABLE_PK,IS_CORPORATE,A.CHARGE_PERIOD,A.BUSI_ITEM_ID FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000  ]]>
		<include refid="premArapWhereCondition" />
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	
	<select id="findAllPremArapByReletionNO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  
			   SELECT DISTINCT LEVEL CASE_LEVEL,
                   AAA.CASE_ID,
                   BB.UNIT_NUMBER,
                   BB.FINISH_TIME,
                   BB.FEE_TYPE,
                   BB.PAY_MODE,
                   BB.FEE_STATUS_BY,
                    NVL(nvl((SELECT MIN(A.DELIVERY_BANK_TIME)
                         FROM APP___CAP__DBUSER.T_PREM_ARAP        T,
                              APP___CAP__DBUSER.T_BANK_TEXT_DETAIL A
                        WHERE T.BUSINESS_CODE = CCC.CASE_NO
                          AND T.SEQ_NO = A.SEQ_NO),(SELECT min(tfi.pay_send_time) FROM  APP___CAP__DBUSER.t_fms_interface  tfi 
    where trade_sn = CCC.CASE_NO)),
                   BB.FINISH_TIME) DELIVERY_BANK_TIME
     FROM APP___CLM__DBUSER.T_CLAIM_PAY  AAA,
          APP___CLM__DBUSER.T_PREM_ARAP  BB,
          APP___CLM__DBUSER.T_CLAIM_CASE CCC
    WHERE AAA.PREM_ARAP_ID = BB.LIST_ID
      AND AAA.CASE_ID = CCC.CASE_ID
      AND BB.FEE_STATUS = '01'
    START WITH CCC.CASE_NO = #{business_code}
   CONNECT BY PRIOR CCC.RELATED_NO = CCC.CASE_NO
    ORDER BY LEVEL
]]> 
	</select>
	
	
			<select id="findPremArapByReletionNO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			  select DISTINCT level case_level,
                 AAA.CASE_ID,
                 BB.UNIT_NUMBER,
                 BB.FINISH_TIME,
                 BB.FEE_TYPE,
                 bb.pay_mode,
                 BB.FEE_STATUS_BY
   from APP___CLM__DBUSER.T_CLAIM_CASE CCC,
         APP___CLM__DBUSER.T_CLAIM_PAY  AAA
         left join APP___CLM__DBUSER.t_prem_arap BB
     		 on AAA.PREM_ARAP_ID = BB.LIST_ID
			  WHERE AAA.CASE_ID = CCC.CASE_ID 
			    AND BB.FEE_STATUS='01'
			  start with CCC.CASE_NO = #{business_code}
			 CONNECT BY PRIOR CCC.RELATED_NO = CCC.CASE_NO 
			  order by level
]]> 
	</select>
	
<!-- 查询分期给付案件的支付信息 -->
	<select id="findInstalmentPremArapByReletionNO" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT DISTINCT LEVEL CASE_LEVEL,
				                AAA.CASE_ID,
				                BB.UNIT_NUMBER,
				                BB.FINISH_TIME,
				                BB.FEE_TYPE,
				                BB.PAY_MODE,
				                BB.FEE_STATUS_BY
				  FROM APP___CLM__DBUSER.T_CLAIM_CASE       CCC,
				       APP___CLM__DBUSER.T_CLAIM_PAY        AAA,
				       APP___CLM__DBUSER.T_CLAIM_INSTALMENT CI
				  LEFT JOIN APP___CLM__DBUSER.T_PREM_ARAP BB ON CI.PREM_ARAP_ID = BB.LIST_ID
				 WHERE AAA.CASE_ID = CCC.CASE_ID
				   AND AAA.CLAIM_PAY_ID = CI.CLAIM_PAY_ID
				   AND BB.FEE_STATUS = '01'
				 START WITH CCC.CASE_NO = #{business_code}
				CONNECT BY PRIOR CCC.RELATED_NO = CCC.CASE_NO
				 ORDER BY LEVEL  ]]>
	</select>	

<!-- 查询个数操作 -->
	<select id="findPremArapTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE 1 = 1  ]]>
		<include refid="premArapWhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryPremArapForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber,B.CIP_BANK_CODE,B.CIP_BRANCH_BANK_CODE,B.CIP_DISTRICT_BANK_CODE, B.TASK_MARK, B.POLICY_ORGAN_CODE, B.HOLDER_NAME, B.IS_ITEM_MAIN, B.BOOKKEEPING_ID, B.BATCH_NO, B.BUSI_PROD_NAME, 
			B.PAYEE_PHONE, B.UNIT_NUMBER, B.PAID_COUNT, B.FUNDS_RTN_CODE, B.CUSTOMER_ACCOUNT_FLAG, B.FEE_TYPE, B.SEQ_NO, 
			B.BUSI_PROD_CODE, B.APPLY_CODE, B.FEE_STATUS_DATE, B.ORGAN_CODE, B.RED_BOOKKEEPING_TIME, B.CHANNEL_TYPE, B.IS_BANK_TEXT_DATE, 
			B.CHARGE_YEAR, B.IS_RISK_MAIN, B.IS_BANK_ACCOUNT, B.FROZEN_STATUS, B.POSTED, B.BOOKKEEPING_FLAG, 
			B.GROUP_ID, B.RED_BOOKKEEPING_BY, B.FROZEN_STATUS_DATE, B.INSURED_NAME, B.CUS_ACC_DETAILS_ID, B.INSURED_ID, 
			B.POLICY_TYPE, B.PRODUCT_CHANNEL, B.IS_BANK_ACCOUNT_DATE, B.POLICY_CODE, B.PAY_MODE, B.OPERATOR_BY, B.BRANCH_CODE, 
			B.BUSINESS_TYPE, B.VALIDATE_DATE, B.RED_BELNR, B.BANK_TEXT_STATUS, B.GROUP_CODE, 
			B.CERTI_TYPE, B.CUS_ACC_UPDATE_BY, B.IS_BANK_TEXT_BY, B.MONEY_CODE, B.BUSINESS_CODE, B.IS_BANK_ACCOUNT_BY, B.PAYEE_NAME, 
			B.BANK_ACCOUNT, B.RED_BOOKKEEPING_FLAG, B.CUS_ACC_FEE_AMOUNT, B.CUSTOMER_ID, B.FINISH_TIME, B.DUE_TIME, 
			B.IS_BANK_TEXT, B.CERTI_CODE, B.BUSI_APPLY_DATE, B.GROUP_NAME, B.BELNR, B.FEE_AMOUNT, B.LIST_ID, 
			B.SERVICE_CODE, B.HOLDER_ID, B.WITHDRAW_TYPE, B.ROLLBACK_UNIT_NUMBER, B.FAIL_TIMES, B.POLICY_YEAR, 
			B.FROZEN_STATUS_BY, B.BANK_USER_NAME, B.FEE_STATUS, B.FEE_STATUS_BY, B.PAY_END_DATE, B.DERIV_TYPE, B.RED_BOOKKEEPING_ID, 
			B.BOOKKEEPING_BY, B.CUS_ACC_UPDATE_TIME, B.ARAP_FLAG, B.BANK_CODE, B.BOOKKEEPING_TIME, B.REFEFLAG, B.AGENT_CODE, 
			B.PREM_FREQ,B.CLAIM_NATURE,B.CLAIM_TYPE,B.AUDIT_DATE,B.IS_CORPORATE,B.BUSI_ITEM_ID FROM (
					SELECT ROWNUM RN, A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.CIP_BANK_CODE,A.CIP_BRANCH_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,
			A.PREM_FREQ,A.CLAIM_NATURE,A.CLAIM_TYPE,A.AUDIT_DATE,PRODUCT_CODE,PAY_LIAB_CODE, SOURCE_TABLE,SOURCE_TABLE_PK,A.IS_CORPORATE,A.CHARGE_PERIOD,A.BUSI_ITEM_ID FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="premArapWhereCondition" /> 
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	<!-- 单证打印理赔回退收费通知书根据赔案号查询应收通知书号和总应付金额 -->
	<select id="findPremArapByCaseNO" resultType="java.util.Map"
		parameterType="java.util.Map">
    <![CDATA[select business_code, unit_number,sum(fee_amount) as fee_amount
                   from APP___CLM__DBUSER.T_PREM_ARAP
                  where business_code = #{business_code}
                  group by business_code,unit_number      
        ]]>
	</select>

	<!-- 查询当前操作人当前机构可以核销的赔案 add by zhaoyq -->
	<sql id="businessCodeWhereCondition">
		<if test=" business_code  != null "><![CDATA[ and arap.business_code = #{business_code} ]]></if>
	</sql>
	<select id="queryPremParaCaseNo" resultType="java.util.Map"
		parameterType="java.util.Map">
    <![CDATA[   select distinct arap.business_code, arap.insert_by, arap.organ_code, arap.fee_status
                     from APP___CLM__DBUSER.T_PREM_ARAP arap, APP___CLM__DBUSER.T_CLAIM_UW uw
                    where arap.business_code = uw.case_no
                          and arap.insert_by = #{insert_by}
                          and arap.fee_type = 'G005130000'
                          and uw.uw_status = '4'  
                          and uw.unit_number is not null 
        ]]>
        <include refid="businessCodeWhereCondition" />
	</select>
	<!-- 查询该保单此时之前的所有续期费用  业务来源为005 理赔 add by gaojh_wb-->
	<select id="findAllPremArapBeforeDueTime" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.FEE_STATUS FROM APP___CLM__DBUSER.T_PREM_ARAP A,APP___CLM__DBUSER.T_CLAIM_CASE B WHERE A.BUSINESS_CODE=B.CASE_NO AND A.DERIV_TYPE='005' AND A.POLICY_CODE=#{policy_code}
       AND B.case_id=#{case_id} AND A.DUE_TIME<SYSDATE]]>
	</select>
	<!-- 查询该保单当期保费缴费状态  业务来源为005 理赔  add by gaojh_wb-->
	<select id="findPremArapByStdPremAfStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[  SELECT A.FEE_STATUS FROM APP___CLM__DBUSER.T_PREM_ARAP A,APP___CLM__DBUSER.T_CLAIM_CASE C WHERE A.DERIV_TYPE='005' AND A.POLICY_CODE=#{policy_code} AND C.case_id=#{case_id}
          AND A.DUE_TIME=(SELECT MAX(B.DUE_TIME) FROM APP___CLM__DBUSER.T_PREM_ARAP B,APP___CLM__DBUSER.T_CLAIM_CASE D WHERE B.DERIV_TYPE='005' AND B.POLICY_CODE=#{policy_code} AND D.case_id=#{case_id})]]>
	</select>
	<!-- 已支付的长期护理金 -->
	<select id="findSumFeeAmount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT sum(b.fee_amount) as feeSum FROM APP___CLM__DBUSER.T_CLAIM_INSTALMENT a, APP___CLM__DBUSER.T_PREM_ARAP b where a.prem_arap_id = b.list_id and a.claim_pay_id = #{claim_pay_id} and a.instal_status=1]]>
	</select>
	
	<!-- add by zhaoyq start-->
	<!-- <select id="queryBusiProdCodeIsDoList" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT * FROM APP___CLM__DBUSER.T_CLAIM_BUSI_PROD a, APP___CLM__DBUSER.T_PREM_ARAP b where a.busi_prod_code = b.busi_prod_code and a.busi_prod_code=#{busi_prod_code}]]>
	</select> -->
	<!-- add by zhaoyq end -->
	<!--liulei_Wb 根据条件查询未支付的unitNumber -->
	<select id="findAllUntiNumber" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT DISTINCT A.UNIT_NUMBER,A.FEE_STATUS,A.ROLLBACK_UNIT_NUMBER FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE 1=1 AND A.BUSINESS_CODE = #{business_code}]]>
		<if test=" fee_type_list != null"><![CDATA[ AND a.fee_type not in]]>
			<foreach collection="fee_type_list" item="list" index="index"
				open="(" close=")" separator=",">#{list}</foreach> 
		</if>
		<![CDATA[ ORDER BY A.UNIT_NUMBER ]]> 
	</select>
	<!-- liulei_wb 根据赔案号查询本赔案unitNumber为空的记录 -->
	<select id="findAllUnitNumberIsNull" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[SELECT A.CIP_BANK_CODE,A.CIP_BRANCH_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ,A.CLAIM_NATURE,A.CLAIM_TYPE,A.AUDIT_DATE,PRODUCT_CODE,PAY_LIAB_CODE, SOURCE_TABLE,SOURCE_TABLE_PK,IS_CORPORATE,A.CHARGE_PERIOD,A.BUSI_ITEM_ID FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000 AND UNIT_NUMBER IS NULL]]>
		<include refid="premArapWhereCondition" /> 
		<![CDATA[ ORDER BY A.UNIT_NUMBER ]]> 
	</select>
	
	<select id="findPremArapByUnitNumber" resultType="java.util.Map" parameterType="java.util.Map" >
		<![CDATA[ SELECT A.CIP_BANK_CODE,A.CIP_BRANCH_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ,A.CLAIM_NATURE,A.CLAIM_TYPE,A.AUDIT_DATE,PRODUCT_CODE,PAY_LIAB_CODE, SOURCE_TABLE,SOURCE_TABLE_PK,IS_CORPORATE,A.CHARGE_PERIOD,A.BUSI_ITEM_ID FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE 1 = 1 and rownum < 1000 ]]>
		<if test="unit_number != null and unit_number != ''">
			<![CDATA[ and A.UNIT_NUMBER = #{unit_number} ]]>
		</if>	
	</select>
	<!-- 是否有赔付 --> 
	<!--actual_pay:实际给付金额，balance_pay：结算金额，是根据合同结算的金额，前者包括后者 2018-07-06  -->
	<select id="findAllPremArapandPayee" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
 select  A.CASE_id,A.CASE_STATUS
    from APP___CLM__DBUSER.t_Claim_Case A
    inner join APP___CLM__DBUSER.t_Claim_Accident b on a.accident_id = b.accident_id 
    where A.CASE_NO=#{business_code}
  ]]>
    <if test="accident_no !=null and accident_no !=''"> 
         <![CDATA[  and b.ACCIDENT_NO = #{accident_no}  ]]>
	</if>
	</select>
	
	<select id="findAllReceiverinformation" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[
     select a.PAYEE_RELATION,
    (select c.relation_name from APP___CLM__DBUSER.T_LA_PH_RELA C where c.relation_code=a.payee_relation) as relation_name
    from APP___CLM__DBUSER.t_claim_payee a]]>
    where a.case_id=#{case_id}
    <if test="payee_no != null and payee_no != ''">
    <![CDATA[and a.PAYEE_NO=#{payee_no}]]>
    </if>
    <if test="certi_type != null and certi_type != ''">
    <![CDATA[and a.PAYEE_CERTI_TYPE=#{certi_type}]]>
    </if>
    <if test="certi_code != null and certi_code != ''">
    <![CDATA[and a.PAYEE_CERTI_NO=#{certi_code}]]>
    </if>
    <if test="payee_name != null and payee_name != ''">
    <![CDATA[and a.PAYEE_NAME=#{payee_name}]]>
    </if>
    <![CDATA[ order by a.PAYEE_ID desc]]>
	</select>
	<!-- 根据赔案查询出finish_time -->
	<select id="findAllPremArapFinishTimeByNo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT A.FINISH_TIME FROM DEV_CLM.T_PREM_ARAP A WHERE A.BUSINESS_CODE = #{business_code}  GROUP BY A.FINISH_TIME]]>
	</select>
	<!-- 根据赔案查询出finish_time并按照大小排序 -->
	<select id="queryAllFinishTimeByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
	<![CDATA[SELECT A.FINISH_TIME FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE A.BUSINESS_CODE = #{business_code} ORDER BY A.FINISH_TIME desc]]>
	</select>
	
	
	<!-- 根据UnitNumber查出赔案领款人和受益人关系 -->
	<select id="findPayeeRelationByUnitNumber" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_CASE C
        		INNER JOIN APP___CLM__DBUSER.T_CLAIM_PAYEE P ON C.CASE_ID=P.CASE_ID
        		INNER JOIN APP___CLM__DBUSER.T_PREM_ARAP AP ON C.CASE_NO=AP.BUSINESS_CODE AND P.PAYEE_NO=AP.CUSTOMER_ID
        		WHERE P.PAYEE_RELATION = '00' AND AP.UNIT_NUMBER = #{unit_number}
		]]>
	</select>
	<!-- 根据UnitNumber查询支付失败原因 -->
	<select id="findPayeeFailByUnitNumber" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT T.RETURN_MSG,T.RETURN_CODE,T.STATUS FROM DEV_CAP.T_FMS_INTERFACE T  WHERE T.UNIT_NUMBER = #{unit_number}
		]]>
	</select>
	<!-- 根据返回信息代码查询支付失败原因 -->
	<select id="findPayeeFailByCode" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT A.BANK_RET_NAME FROM DEV_PAS.T_BANK_RET_CONF A WHERE A.BANK_RET_CODE =#{bank_ret_code}
		]]>
	</select>
	
		<!-- 超期支付金额查询-->
	<select id="findPremArapByCaseId" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
		SELECT DISTINCT D.LIST_ID,D.FEE_AMOUNT,D.UNIT_NUMBER,D.FEE_TYPE, D.BUSINESS_CODE,D.PAY_MODE
		FROM APP___CLM__DBUSER.T_CLAIM_CASE  A,
		 APP___CLM__DBUSER.T_CLAIM_BUSI_PROD B,
	       APP___CLM__DBUSER.T_CLAIM_PAY CP,
	       APP___CLM__DBUSER.T_PREM_ARAP D
	       left join APP___CLM__DBUSER.T_CLAIM_OVERCOMP_PAY COP on COP.OVER_PAY_FLAG = 0 AND COP.ARAP_LIST_ID = D.LIST_ID
		WHERE 1=1
		AND A.CASE_ID = B.CASE_ID
	   AND B.CASE_ID = A.CASE_ID
	   AND B.BUSI_PROD_CODE=D.BUSI_PROD_CODE
	   AND CP.UNIT_NUMBER = D.UNIT_NUMBER
	   AND B.POLICY_CODE=D.POLICY_CODE
		AND D.BUSINESS_CODE = #{business_code}
		AND D.PAY_MODE= #{pay_mode}
		AND D.CUSTOMER_ID= #{customer_id}
		AND D.ARAP_FLAG = 2
		AND D.FINISH_TIME IS NOT NULL
		AND D.FEE_TYPE != 'P005110000'
		AND A.CASE_NO=D.BUSINESS_CODE
		AND ceil(D.FINISH_TIME - A.APPROVE_TIME)>9
		AND D.LIST_ID NOT IN (SELECT OVERPAY.ARAP_LIST_ID FROM APP___CLM__DBUSER.T_CLAIM_OVERCOMP_PAY OVERPAY 
       WHERE OVERPAY.OVER_PAY_FLAG = 1  AND OVERPAY.CASE_ID = A.CASE_ID )
        ]]>
	</select>
	<!-- 根据入参UNIT_NUMBER更新T_PREM_ARAP表中支付方式 -->
	<update id="updatePremArapByUnitNumber" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_PREM_ARAP ]]>
		<set>
		<trim suffixOverrides=",">
		    PAY_MODE = #{pay_mode, jdbcType=VARCHAR},
		</trim>
 		</set>
		<![CDATA[ WHERE UNIT_NUMBER = #{unit_number} 
		AND BUSINESS_CODE = #{business_code} 
		]]>
	</update>
	
	<!-- 查询当前赔案下的所有支付成功的记录-->
	<select id="findPremArapFilsh" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
		SELECT A.CIP_BANK_CODE,A.CIP_BRANCH_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ,A.CLAIM_NATURE,A.CLAIM_TYPE,A.AUDIT_DATE,PRODUCT_CODE,PAY_LIAB_CODE, SOURCE_TABLE,SOURCE_TABLE_PK,IS_CORPORATE,A.CHARGE_PERIOD,A.BUSI_ITEM_ID FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000
		AND A.BUSINESS_CODE = #{business_code} AND A.FEE_STATUS = '01' AND A.FINISH_TIME IS NOT NULL
		AND (A.FEE_TYPE != 'P005110000' OR A.FEE_TYPE != 'G005130000') ]]> 
		<![CDATA[ ORDER BY A.LIST_ID ]]> 
	</select>
	<!-- 查询回退产生的应收应付表追偿数据 -->
	<select id="findBackPremArapByCaseNo" resultType="java.util.Map"
		parameterType="java.util.Map">
		<![CDATA[ SELECT a.business_code,
	       a.customer_id,
	       a.payee_name,
	       A.IS_CORPORATE,
	       SUM(a.FEE_AMOUNT * DECODE(a.ARAP_FLAG, '1', -1, 1)) fee_amount
	  FROM APP___CLM__DBUSER.t_prem_arap a
	 where a.FEE_TYPE != 'G005130000'
	   and a.UNIT_NUMBER IS NOT NULL
	   and a.fee_status<>'16'
	   and a.business_code = #{business_code}
	 group by a.business_code, a.customer_id,A.IS_CORPORATE,a.payee_name ]]>
	</select>
	
	<!-- 根据赔案号查询已支付的数据，根据支付完成时间倒序-->
	<select id="findAllPremArapByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
    <![CDATA[
		SELECT A.CIP_BANK_CODE,A.CIP_BRANCH_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ,A.CLAIM_NATURE,A.CLAIM_TYPE,A.AUDIT_DATE,PRODUCT_CODE,PAY_LIAB_CODE, SOURCE_TABLE,SOURCE_TABLE_PK,IS_CORPORATE,A.CHARGE_PERIOD,A.BUSI_ITEM_ID
		FROM APP___CLM__DBUSER.T_PREM_ARAP A WHERE ROWNUM <=  1000
		AND A.BUSINESS_CODE = #{business_code} AND A.FEE_STATUS = '01' AND A.FINISH_TIME IS NOT NULL
		 ORDER BY A.FINISH_TIME desc ]]> 
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findPremArapAndApplyId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.CIP_BANK_CODE,A.CIP_BRANCH_BANK_CODE,A.CIP_DISTRICT_BANK_CODE,A.TASK_MARK, A.POLICY_ORGAN_CODE, A.HOLDER_NAME, A.IS_ITEM_MAIN, A.BOOKKEEPING_ID, A.BATCH_NO, A.BUSI_PROD_NAME, 
			A.PAYEE_PHONE, A.UNIT_NUMBER, A.PAID_COUNT, A.FUNDS_RTN_CODE, A.CUSTOMER_ACCOUNT_FLAG, A.FEE_TYPE, A.SEQ_NO, 
			A.BUSI_PROD_CODE, A.APPLY_CODE, A.FEE_STATUS_DATE, A.ORGAN_CODE, A.RED_BOOKKEEPING_TIME, A.CHANNEL_TYPE, A.IS_BANK_TEXT_DATE, 
			A.CHARGE_YEAR, A.IS_RISK_MAIN, A.IS_BANK_ACCOUNT, A.FROZEN_STATUS, A.POSTED, A.BOOKKEEPING_FLAG, 
			A.GROUP_ID, A.RED_BOOKKEEPING_BY, A.FROZEN_STATUS_DATE, A.INSURED_NAME, A.CUS_ACC_DETAILS_ID, A.INSURED_ID, 
			A.POLICY_TYPE, A.PRODUCT_CHANNEL, A.IS_BANK_ACCOUNT_DATE, A.POLICY_CODE, A.PAY_MODE, A.OPERATOR_BY, A.BRANCH_CODE, 
			A.BUSINESS_TYPE, A.VALIDATE_DATE, A.RED_BELNR, A.BANK_TEXT_STATUS, A.GROUP_CODE, 
			A.CERTI_TYPE, A.CUS_ACC_UPDATE_BY, A.IS_BANK_TEXT_BY, A.MONEY_CODE, A.BUSINESS_CODE, A.IS_BANK_ACCOUNT_BY, A.PAYEE_NAME, 
			A.BANK_ACCOUNT, A.RED_BOOKKEEPING_FLAG, A.CUS_ACC_FEE_AMOUNT, A.CUSTOMER_ID, A.FINISH_TIME, A.DUE_TIME, 
			A.IS_BANK_TEXT, A.CERTI_CODE, A.BUSI_APPLY_DATE, A.GROUP_NAME, A.BELNR, A.FEE_AMOUNT, A.LIST_ID, 
			A.SERVICE_CODE, A.HOLDER_ID, A.WITHDRAW_TYPE, A.ROLLBACK_UNIT_NUMBER, A.FAIL_TIMES, A.POLICY_YEAR, 
			A.FROZEN_STATUS_BY, A.BANK_USER_NAME, A.FEE_STATUS, A.FEE_STATUS_BY, A.PAY_END_DATE, A.DERIV_TYPE, A.RED_BOOKKEEPING_ID, 
			A.BOOKKEEPING_BY, A.CUS_ACC_UPDATE_TIME, A.ARAP_FLAG, A.BANK_CODE, A.BOOKKEEPING_TIME, A.REFEFLAG, A.AGENT_CODE, 
			A.PREM_FREQ,A.CLAIM_NATURE,A.CLAIM_TYPE,A.AUDIT_DATE,PRODUCT_CODE,PAY_LIAB_CODE, SOURCE_TABLE,SOURCE_TABLE_PK,IS_CORPORATE,A.CHARGE_PERIOD,A.BUSI_ITEM_ID 
			,D.APPLY_ID  FROM APP___CLM__DBUSER.T_PREM_ARAP A
			  INNER JOIN APP___CLM__DBUSER.T_CLAIM_CASE K ON A.BUSINESS_CODE = K.CASE_NO
			  LEFT JOIN APP___CLM__DBUSER.T_CLAIM_PAY_CHANGE D ON A.LIST_ID = D.FEE_ID AND D.CASE_ID = K.CASE_ID
			 WHERE ROWNUM <= 1000 AND A.UNIT_NUMBER = #{unit_number}
			  ORDER BY A.LIST_ID ]]> 
	</select>
	
	<!-- 查询所有操作 -->
	<select id="findAmountByPolicyCode" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ 
			SELECT A.POLICY_CODE,
       			   SUM(A.ACTUAL_PAY) FEE_AMOUNT
  			  FROM APP___CLM__DBUSER.T_CLAIM_LIAB A
  	     LEFT JOIN APP___CLM__DBUSER.T_CLAIM_CASE T
  	            ON A.CASE_ID = T.CASE_ID
 		     WHERE A.POLICY_CODE = #{policy_code}
 		       AND T.CASE_STATUS = '80'
 		       AND A.LIAB_CONCLUSION <> '5'
          GROUP BY A.POLICY_CODE
        ]]> 
	</select>
	
</mapper>
