<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimAfcTaskPO">

	<sql id="claimAfcTaskWhereCondition">
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" remark != null and remark != ''  "><![CDATA[ AND A.REMARK = #{remark} ]]></if>
		<if test=" check_conclusion  != null "><![CDATA[ AND A.CHECK_CONCLUSION = #{check_conclusion} ]]></if>
		<if test=" check_date  != null  and  check_date  != ''  "><![CDATA[ AND A.CHECK_DATE = #{check_date} ]]></if>
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" case_id  != null "><![CDATA[ AND A.CASE_ID = #{case_id} ]]></if>
		<if test=" organ_code != null and organ_code != ''  "><![CDATA[ AND A.ORGAN_CODE = #{organ_code} ]]></if>
		<if test=" check_by  != null "><![CDATA[ AND A.CHECK_BY = #{check_by} ]]></if>
		<if test=" make_date  != null  and  make_date  != ''  "><![CDATA[ AND A.MAKE_DATE = #{make_date} ]]></if>
		<if test=" qc_status  != null "><![CDATA[ AND A.QC_STATUS = #{qc_status} ]]></if>
		<if test=" check_reason != null and check_reason != ''  "><![CDATA[ AND A.CHECK_REASON = #{check_reason} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimAfcTaskByTaskIdCondition">
		<if test=" task_id  != null "><![CDATA[ AND A.TASK_ID = #{task_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimAfcTask"  useGeneratedKeys="true" parameterType="java.util.Map">
		<selectKey resultType="java.lang.Integer" order="BEFORE" keyProperty="task_id">
			SELECT APP___CLM__DBUSER.S_CLAIM_AFC_TASK__TASK_ID.NEXTVAL FROM DUAL
		</selectKey>
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_AFC_TASK(
				PLAN_ID, INSERT_TIME, REMARK, CHECK_CONCLUSION, CHECK_DATE, UPDATE_TIME, TASK_ID, 
				CASE_NO, CASE_ID, INSERT_TIMESTAMP, ORGAN_CODE, CHECK_BY, UPDATE_BY, MAKE_DATE, 
				QC_STATUS, UPDATE_TIMESTAMP, INSERT_BY, CHECK_REASON ) 
			VALUES (
				#{plan_id, jdbcType=NUMERIC}, SYSDATE , #{remark, jdbcType=VARCHAR} , #{check_conclusion, jdbcType=NUMERIC} , #{check_date, jdbcType=DATE} , SYSDATE , #{task_id, jdbcType=NUMERIC} 
				, #{case_no, jdbcType=VARCHAR} , #{case_id, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{organ_code, jdbcType=VARCHAR} , #{check_by, jdbcType=NUMERIC} , #{update_by, jdbcType=NUMERIC} , #{make_date, jdbcType=DATE} 
				, #{qc_status, jdbcType=NUMERIC} , CURRENT_TIMESTAMP, #{insert_by, jdbcType=NUMERIC} , #{check_reason, jdbcType=VARCHAR} ) 
		 ]]>
	</insert>
<!-- 删除操作前先删除子表数据 -->
	<delete id="deleteClaimAfcTaskBefore" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_AFC_DETAIL WHERE TASK_ID = #{task_id} ]]>
	</delete>
<!-- 删除操作 -->	
	<delete id="deleteClaimAfcTask" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_AFC_TASK WHERE TASK_ID = #{task_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimAfcTask" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_AFC_TASK ]]>
		<set>
		<trim suffixOverrides=",">
		    PLAN_ID = #{plan_id, jdbcType=NUMERIC} ,
			REMARK = #{remark, jdbcType=VARCHAR} ,
		    CHECK_CONCLUSION = #{check_conclusion, jdbcType=NUMERIC} ,
		    CHECK_DATE = #{check_date, jdbcType=DATE} ,
			UPDATE_TIME = SYSDATE , 
			CASE_NO = #{case_no, jdbcType=VARCHAR} ,
		    CASE_ID = #{case_id, jdbcType=NUMERIC} ,
			ORGAN_CODE = #{organ_code, jdbcType=VARCHAR} ,
		    CHECK_BY = #{check_by, jdbcType=NUMERIC} ,
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
		    MAKE_DATE = #{make_date, jdbcType=DATE} ,
		    QC_STATUS = #{qc_status, jdbcType=NUMERIC} ,
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
			CHECK_REASON = #{check_reason, jdbcType=VARCHAR} ,
		</trim>
		</set>
		<![CDATA[ WHERE TASK_ID = #{task_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimAfcTaskByTaskId" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.REMARK, A.CHECK_CONCLUSION, A.CHECK_DATE, A.TASK_ID, 
			A.CASE_NO, A.CASE_ID, A.ORGAN_CODE, A.CHECK_BY, A.MAKE_DATE, 
			A.QC_STATUS, A.CHECK_REASON FROM APP___CLM__DBUSER.T_CLAIM_AFC_TASK A WHERE 1 = 1  ]]>
		<include refid="queryClaimAfcTaskByTaskIdCondition" />
		<![CDATA[ ORDER BY A.TASK_ID ]]>
	</select>
<!-- 按赔案号查询操作 -->	
	<select id="findClaimAfcTaskByCaseNo" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.REMARK, A.CHECK_CONCLUSION, A.CHECK_DATE, A.TASK_ID, 
			A.CASE_NO, A.CASE_ID, A.ORGAN_CODE, A.CHECK_BY, A.MAKE_DATE, 
			A.QC_STATUS, A.CHECK_REASON FROM APP___CLM__DBUSER.T_CLAIM_AFC_TASK A WHERE 1 = 1 ]]>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ AND A.CASE_NO = #{case_no} ]]></if>
		<if test=" check_by  != null "><![CDATA[ AND A.CHECK_BY = #{check_by} ]]></if>
		<![CDATA[ ORDER BY A.TASK_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimAfcTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.REMARK, A.CHECK_CONCLUSION, A.CHECK_DATE, A.TASK_ID, 
			A.CASE_NO, A.CASE_ID, A.ORGAN_CODE, A.CHECK_BY, A.MAKE_DATE, 
			A.QC_STATUS, A.CHECK_REASON FROM APP___CLM__DBUSER.T_CLAIM_AFC_TASK A WHERE ROWNUM <=  1000  ]]>
		<!-- <include refid="请添加查询条件" /> -->
		<![CDATA[ ORDER BY A.TASK_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimAfcTask" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.REMARK, A.CHECK_CONCLUSION, A.CHECK_DATE, A.TASK_ID, 
			A.CASE_NO, A.CASE_ID, A.ORGAN_CODE, A.CHECK_BY, A.MAKE_DATE, 
			A.QC_STATUS, A.CHECK_REASON FROM APP___CLM__DBUSER.T_CLAIM_AFC_TASK A WHERE 1 = 1  ]]>
		 <include refid="claimAfcTaskWhereCondition" />
		<![CDATA[ ORDER BY A.TASK_ID ]]> 
	</select>
	
<!-- 查询所有操作 -->
	<select id="findAllUserTaskCount" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT AF.USER_ID,AF.USER_NAME,AF.ORGAN_CODE, COUNT(AT.CASE_NO) NUM
  FROM (SELECT GU.USER_ID, U.ORGAN_CODE, U.USER_NAME
          FROM APP___CLM__DBUSER.T_UDMP_GROUP_USER      GU,
               APP___CLM__DBUSER.T_UDMP_GROUP_ROLE      GR,
               APP___CLM__DBUSER.T_UDMP_ROLE_PERMISSION RP,
               APP___CLM__DBUSER.T_UDMP_PERMISSION_TYPE PT,
               APP___CLM__DBUSER.T_UDMP_PERMISSION_INFO PI,
               APP___CLM__DBUSER.T_UDMP_USER            U
         WHERE GU.ROLE_GROUP_ID = GR.ROLE_GROUP_ID
           AND GR.ROLE_ID = RP.ROLE_ID
           AND RP.PERMISSION_TYPE_ID = PT.PERMISSION_TYPE_ID
           AND RP.PERMISSION_ID = PI.PERMISSION_ID
           AND PT.PERMISSION_TYPE_ID = PI.PERMISSION_TYPE_ID
           AND GU.USER_ID = U.USER_ID
           AND PI.PERMISSION_NAME = '事后质检') AF,
       		APP___CLM__DBUSER.T_CLAIM_AFC_TASK AT
 WHERE AF.USER_ID = AT.CHECK_BY(+)
 GROUP BY AF.USER_ID,AF.USER_NAME,AT.CHECK_BY, AF.ORGAN_CODE
 ORDER BY NUM]]> 
	</select>
	
<!-- 查询质检状态为null的数据 -->
	<select id="findClaimAfcTaskNullStatus" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.PLAN_ID, A.REMARK, A.CHECK_CONCLUSION, A.CHECK_DATE, A.TASK_ID, 
			A.CASE_NO, A.CASE_ID, A.ORGAN_CODE, A.CHECK_BY, A.MAKE_DATE, 
			A.QC_STATUS, A.CHECK_REASON FROM APP___CLM__DBUSER.T_CLAIM_AFC_TASK A WHERE 1 = 1 AND A.QC_STATUS is null]]>
			<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
			<if test=" taskStr  != null and taskStr !=''"><![CDATA[ AND A.task_id in (${taskStr}) ]]></if>
		<![CDATA[ ORDER BY A.TASK_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimAfcTaskTotal" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_AFC_TASK A WHERE 1 = 1  ]]>
		<include refid="claimAfcTaskWhereCondition" />
	</select>
<!-- 抽取事后质检结果页面统计个数（忽略赔案号） -->
	<select id="findClaimAfcTaskTotalExCaseNo" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_AFC_TASK A WHERE 1 = 1  AND A.QC_STATUS is null]]>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
	</select>

<!-- 分页查询质检状态为null的数据 -->
	<select id="queryClaimAfcTaskForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT  C.* FROM ( SELECT ROWNUM rn, B.PLAN_ID, B.REMARK, B.CHECK_CONCLUSION, B.CHECK_DATE, B.TASK_ID, 
			B.CASE_NO, B.CASE_ID, B.ORGAN_CODE, B.CHECK_BY, B.MAKE_DATE, 
			B.QC_STATUS, B.CHECK_REASON FROM (
					SELECT A.PLAN_ID, A.REMARK, A.CHECK_CONCLUSION, A.CHECK_DATE, A.TASK_ID, 
			A.CASE_NO, A.CASE_ID, A.ORGAN_CODE, A.CHECK_BY, A.MAKE_DATE, 
			A.QC_STATUS, A.CHECK_REASON FROM APP___CLM__DBUSER.T_CLAIM_AFC_TASK A WHERE 1=1 AND A.QC_STATUS is null]]>
		<if test=" plan_id  != null "><![CDATA[ AND A.PLAN_ID = #{plan_id} ]]></if>
		<if test=" case_no != null and case_no != ''  "><![CDATA[ORDER BY DECODE(A.CASE_NO,#{case_no},'0',A.CASE_NO)]]></if>
		<![CDATA[ )  B WHERE  ROWNUM <= #{LESS_NUM}) C WHERE C.RN > #{GREATER_NUM}  ]]>
	</select>
	
</mapper>
