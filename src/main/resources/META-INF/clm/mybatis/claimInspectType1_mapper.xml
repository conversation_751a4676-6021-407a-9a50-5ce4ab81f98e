<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nci.tunan.clm.interfaces.model.po.ClaimInspectType1PO">

	<sql id="claimInspectType1WhereCondition">
		<if test=" inspect_type1_id  != null "><![CDATA[ AND A.INSPECT_TYPE1_ID = #{inspect_type1_id} ]]></if>
		<if test=" inspect_type1_desc != null and inspect_type1_desc != ''  "><![CDATA[ AND A.INSPECT_TYPE1_DESC = #{inspect_type1_desc} ]]></if>
	</sql>


<!-- 按索引生成的查询条件 -->	
	<sql id="queryClaimInspectType1ByInspectType1IdCondition">
		<if test=" inspect_type1_id  != null "><![CDATA[ AND A.INSPECT_TYPE1_ID = #{inspect_type1_id} ]]></if>
	</sql>	

<!-- 添加操作 -->
	<insert id="addClaimInspectType1"  useGeneratedKeys="false"  parameterType="java.util.Map">
		<![CDATA[
			INSERT INTO APP___CLM__DBUSER.T_CLAIM_INSPECT_TYPE1(
				INSERT_TIMESTAMP, INSPECT_TYPE1_ID, UPDATE_BY, INSERT_TIME, INSPECT_TYPE1_DESC, UPDATE_TIME, UPDATE_TIMESTAMP, 
				INSERT_BY ) 
			VALUES (
				CURRENT_TIMESTAMP, (SELECT (NVL(MAX(INSPECT_TYPE1_ID),0)+1) FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TYPE1) , #{update_by, jdbcType=NUMERIC} , SYSDATE , #{inspect_type1_desc, jdbcType=VARCHAR} , SYSDATE , CURRENT_TIMESTAMP
				, #{insert_by, jdbcType=NUMERIC} ) 
		 ]]>
	</insert>

<!-- 删除操作 -->	
	<delete id="deleteClaimInspectType1" parameterType="java.util.Map">
		<![CDATA[ DELETE FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TYPE1 WHERE INSPECT_TYPE1_ID = #{inspect_type1_id} ]]>
	</delete>

<!-- 修改操作 -->
	<update id="updateClaimInspectType1" parameterType="java.util.Map">
		<![CDATA[ UPDATE APP___CLM__DBUSER.T_CLAIM_INSPECT_TYPE1 ]]>
		<set>
		<trim suffixOverrides=",">
		    UPDATE_BY = #{update_by, jdbcType=NUMERIC} ,
			INSPECT_TYPE1_DESC = #{inspect_type1_desc, jdbcType=VARCHAR} ,
			UPDATE_TIME = SYSDATE , 
		    UPDATE_TIMESTAMP = CURRENT_TIMESTAMP ,
		</trim>
		</set>
		<![CDATA[ WHERE INSPECT_TYPE1_ID = #{inspect_type1_id} ]]>
	</update>

<!-- 按索引查询操作 -->	
	<select id="findClaimInspectType1ByInspectType1Id" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.INSPECT_TYPE1_ID, A.INSPECT_TYPE1_DESC FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TYPE1 A WHERE 1 = 1  ]]>
		<include refid="queryClaimInspectType1ByInspectType1IdCondition" />
		<![CDATA[ ORDER BY A.INSPECT_TYPE1_ID ]]>
	</select>
	

<!-- 按map查询操作 -->
	<select id="findAllMapClaimInspectType1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.INSPECT_TYPE1_ID, A.INSPECT_TYPE1_DESC FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TYPE1 A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimInspectType1WhereCondition" />
		<![CDATA[ ORDER BY A.INSPECT_TYPE1_ID ]]>
	</select>

<!-- 查询所有操作 -->
	<select id="findAllClaimInspectType1" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT A.INSPECT_TYPE1_ID, A.INSPECT_TYPE1_DESC FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TYPE1 A WHERE ROWNUM <=  1000  ]]>
		<include refid="claimInspectType1WhereCondition" />
		<![CDATA[ ORDER BY A.INSPECT_TYPE1_ID ]]> 
	</select>

<!-- 查询个数操作 -->
	<select id="findClaimInspectType1Total" resultType="java.lang.Integer" parameterType="java.util.Map">
		<![CDATA[ SELECT COUNT(1) FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TYPE1 A WHERE 1 = 1  ]]>
		<include refid="claimInspectType1WhereCondition" />
	</select>

<!-- 分页查询操作 -->
	<select id="queryClaimInspectType1ForPage" resultType="java.util.Map" parameterType="java.util.Map">
		<![CDATA[ SELECT B.RN AS rowNumber, B.INSPECT_TYPE1_ID, B.INSPECT_TYPE1_DESC FROM (
					SELECT ROWNUM RN, A.INSPECT_TYPE1_ID, A.INSPECT_TYPE1_DESC FROM APP___CLM__DBUSER.T_CLAIM_INSPECT_TYPE1 A WHERE ROWNUM <= #{LESS_NUM} ]]>
		<include refid="claimInspectType1WhereCondition" />
		<![CDATA[ ORDER BY A.INSPECT_TYPE1_ID ]]> 
		<![CDATA[ )  B
			WHERE B.RN > #{GREATER_NUM} ]]>
	</select>
	
</mapper>
