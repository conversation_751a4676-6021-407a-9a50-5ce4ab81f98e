<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:batch="http://www.springframework.org/schema/batch" xmlns:c="http://www.springframework.org/schema/c"
	xmlns:cache="http://www.springframework.org/schema/cache"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:task="http://www.springframework.org/schema/task" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd   http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd   http://www.springframework.org/schema/batch http://www.springframework.org/schema/batch/spring-batch-2.1.xsd   http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache-3.1.xsd   http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd">
	<!-- caoyy_wb 黑名单 -->
	<bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimBlackNameUCCImpl"
		id="CLM_claimBlackNameUCC">
		<property name="claimBlackNameService" ref="CLM_claimBlackNameService" />
	</bean>
	<!-- end -->
	<!-- yangzd1_wb 报案清单 -->
	<bean class="com.nci.tunan.clm.impl.querylist.ucc.impl.ReportListUCCImpl"
		id="CLM_reportListUCC">
		<property name="reportListService" ref="CLM_reportListService" />
	</bean>
	<!-- end -->
	<!-- yangzd1_wb 实时支付开关配置 -->
	<bean
		class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimRealtimePayUCCImpl"
		id="CLM_claimRealtimePayUCC">
		<property name="claimRealtimePayService" ref="CLM_claimRealtimePayService" />
	</bean>
	<!-- end -->
	<!-- add by zhangjy_wb -->
	<!-- 数据采集 -->
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.DataCollectUccImpl"
		id="CLM_dataCollectUCC">
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="insuredListService" ref="CLM_insuredListService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
		<property name="districtService" ref="CLM_districtService" />
		<property name="claimMatchResultService" ref="CLM_claimMatchResultService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.DistrictUCCImpl"
		id="CLM_districtUCC">
		<property name="districtService" ref="CLM_districtService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.AccidentDetailsUCCImpl"
		id="CLM_accidentDetailsUCC">
		<property name="claimCommonService" ref="CLM_claimCommonService" />
	</bean>
	<!-- 理赔关怀 -->
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.ClaimCareManageUCCImpl"
		id="CLM_claimCareManageUCC">
		<property name="claimProduceTaskService" ref="CLM_claimProduceTaskService" />
		<property name="claimBPMService" ref="CLM_claimBPMService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.ProduceCareTaskManageUCCImpl"
		id="CLM_produceCareTaskManageUCC">
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="claimProduceTaskService" ref="CLM_claimProduceTaskService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
	</bean>
	<!-- 查询关怀历史信息 -->
	<bean
		class=" com.nci.tunan.clm.impl.claimcare.ucc.impl.QueryCareHistoryInfoUCCImpl"
		id="CLM_queryCareHistoryInfoUCC">
		<property name="queryCareHistoryInfoService" ref="CLM_queryCareHistoryInfoService" />
	</bean>
	<!-- 维护复勘计划 -->
	<bean
		class="com.nci.tunan.clm.impl.recheckplan.ucc.impl.ClaimStartSurveyUCCImpl"
		id="CLM_claimStartSurveyUCC">
		<property name="claimReSurveyService" ref="CLM_claimReSurveyService" />
		<property name="clmSurveyItemUCC" ref="CLM_clmSurveyItemUCC" />
		<property name="extractRecheckUCC" ref="CLM_extractRecheckUCC" />
		<property name="dataCollectUcc" ref="CLM_dataCollectUCC" />
		<property name="auditConclusionUCC" ref="CLM_auditConclusionUCC" />
		<property name="surveyObjectUCC" ref="CLM_surveyObjectUCC" />
		<property name="clmSurveyApplyUCC" ref="CLM_clmSurveyApplyUCC" />
	</bean>
	<!-- add by zhangjy_wb 业务员历史代办查询 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.QueryAgentCommissiomUCCImpl"
		id="CLM_queryAgentCommissionUCC">
		<property name="queryAgentCommissionService" ref="CLM_queryAgentCommissionService" />
	</bean>
	<!-- add by gaojh_wb -->
	<!-- 保单查询 -->
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.QueryPolicyUCCImpl"
		id="CLM_queryPolicyUCC">
		<property name="claimPASServiceImpl" ref="CLM_claimPASService" />
	</bean>
	<!-- add by zhangjy_wb 理赔案件信息查询接口 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.QueryClaimCaseMsgUCCImpl"
		id="CLM_queryClaimCaseMsgUCC">
		<property name="queryClaimCaseMsgService" ref="CLM_queryClaimCaseMsgService" />
	</bean>
	<!-- 立案共享池查询 -->
	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.RegisterSharePoolClientUCCImpl"
		id="CLM_iRegisterSharePoolClientUCC">
		<property name="registerSharePoolClientService" ref="CLM_iRegisterSharePoolClientService" />
	</bean>
	<!-- 案件撤销 -->
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.CancelCaseUCC"
		id="CLM_cancelCaseUCC">
		<property name="cancelCaseService" ref="CLM_cancelCaseService" />
		<property name="claimBPMService" ref="CLM_claimBPMService" />
		<property name="memoService" ref="CLM_memoService" />
		<property name="approveService" ref="CLM_approveService" />
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
	</bean>
	<!-- 慰问规则 -->
	<bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimComfortRuleUCC"
		id="CLM_iClaimComfortRuleUCC">
		<property name="claimComfortRuleService" ref="CLM_iClaimComfortRuleService" />
		<property name="claimComfortAccTypeService" ref="CLM_iClaimComfortAccTypeService" />
		<property name="claimComfortAccReasonService" ref="CLM_iClaimComfortAccReasonService" />
	</bean>
	<!-- 公共参数管理 -->
	<bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.ParaDefUCC"
		id="CLM_iClaimParaDefUCC">
		<property name="paraDefService" ref="CLM_iClaimParaDefService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.ClaimCommonParaUCC"
		id="CLM_iClaimCommonParaUCC">
		<property name="claimCommonParaService" ref="CLM_iClaimCommonParaService" />
		<property name="claimCommonParaTrailService" ref="CLM_iClaimCommonParaTrailService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.ClaimCommonParaTrailUCC"
		id="CLM_iClaimCommonParaTrailUCC">
		<property name="claimCommonParaTrailService" ref="CLM_iClaimCommonParaTrailService" />
	</bean>
	<!-- 定义数据复核机制 -->
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimRgtCheckRuleUCC"
		id="CLM_iClaimRgtCheckRuleUCC">
		<property name="claimRgtCheckRuleService" ref="CLM_iClaimRgtCheckRuleService" />
	</bean>
	<!-- 付费变更 -->
	<bean class="com.nci.tunan.clm.impl.pay.ucc.impl.ClaimPayChangeUCC"
		id="CLM_iClaimPayChangeUCC">
		<property name="claimPayChangeService" ref="CLM_iClaimPayChangeService" />
		<property name="claimPayChangeLogService" ref="CLM_iClaimPayChangeLogService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
		<property name="queryTaskTraceService" ref="CLM_queryTaskTraceService" />
		<property name="approveService" ref="CLM_approveService" />
		<property name="claimPayChangeDao" ref="CLM_iClaimPayChangeDao"/>
	</bean>
	<bean class="com.nci.tunan.clm.impl.pay.ucc.impl.ClaimPayChangeLogUCC"
		id="CLM_iClaimPayChangeLogUCC">
		<property name="claimPayChangeLogService" ref="CLM_iClaimPayChangeLogService" />
	</bean>
	<!-- 质检结果查询接口 -->
	<bean class="com.nci.tunan.clm.impl.exports.hs.impl.QueryCheckResultUCCImpl"
		id="CLM_iQueryCheckResultUCC">
		<property name="queryCheckResultService" ref="CLM_iQueryCheckResultService" />
	</bean>
	<!-- 事后质检共享池 -->
	<bean
		class="com.nci.tunan.clm.impl.audit.ucc.impl.AfterCheckCommonPoolUCCImpl"
		id="CLM_afterCheckCommonPoolUCC">
		<property name="afterCheckCommonPoolService" ref="CLM_afterCheckCommonPoolService" />
	</bean>
	<!-- 再保回复接口 -->
	<bean class="com.nci.tunan.clm.impl.exports.hs.impl.ClaimReplyRisUCCImpl"
		id="CLM_iClaimReplyRisUCC">
		<property name="claimReplyRisService" ref="CLM_iClaimReplyRisService" />
	</bean>
	<!-- 维护伤残等级参数 -->
	<bean
		class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimDeformityGradeUCC"
		id="CLM_iClaimDeformityGradeUCC">
		<property name="claimDeformityGradeService" ref="CLM_iClaimDeformityGradeService" />
	</bean>
	<!-- end -->
	<!-- add by xuyz_wb -->
	<bean
		class="com.nci.tunan.clm.impl.audit.ucc.impl.ClaimAuditConclusionUCCImpl"
		id="CLM_auditConclusionUCC">
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
		<property name="middleCheckConditionService" ref="CLM_middleCheckConditionService" />
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="paymentPlanService" ref="CLM_paymentPlanService" />
		<property name="claimLiabService" ref="CLM_claimLiabService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="claimBusiProdService" ref="CLM_claimBusiProdService" />
		<property name="contractProductService" ref="CLM_contractProductService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="surveyBrowseService" ref="CLM_surveyBrowseService" />
		<property name="riSuggestDetailService" ref="CLM_riSuggestDetailService" />
		<property name="cancelCaseService" ref="CLM_cancelCaseService" />
		<property name="claimInvoicesPrintService" ref="CLM_claimInvoicesPrintService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="approveService" ref="CLM_approveService" />
		<property name="approveClientService" ref="CLM_approveClientService" />
		<property name="contractBusiProdService" ref="CLM_contractBusiProdService" />
		<property name="claimTaskStaffDao" ref="CLM_claimTaskStaffDao" />
		<property name="checkRuleVerifyService" ref="CLM_checkRuleVerifyService" />
		<property name="claimSubCaseService" ref="CLM_claimSubCaseService" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
		<property name="claimSendNoticeDao" ref="CLM_claimSendNoticeDao" />
		<property name="memoService" ref="CLM_memoService" />
		<property name="claimPermissionService" ref="CLM_claimPermissionService" />
		<property name="claimBackApplyDao" ref="CLM_claimBackApplyDao" />
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
		<!-- 理赔现价计算service -->
		<property name="claimCashvalueService" ref="CLM_claimCashvalueService" />
		<property name="claimCaseDao" ref="CLM_claimCaseDao" />
		<!-- 累加器操作类 -->
		<property name="claimAccutionService" ref="CLM_claimAccutionServiceImpl" />
		<property name="claimTaskStaffService" ref="CLM_claimTaskStaffService"/>
		<property name="claimBillService" ref="CLM_claimBillService"/>
		<!-- 邮件通知 -->
		<property name="claimApplicantService" ref="CLM_claimApplicantService" />
		<property name="claimBusiProdDao" ref="CLM_claimBusiProdDao" />
	</bean>
	<!-- end -->

	<!-- add by sunjl_wb -->
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.SignRegistrationUCCImpl"
		id="CLM_signRegistrationUCC">
		<property name="signRegistrationService" ref="CLM_signRegistrationService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="districtService" ref="CLM_districtService" />
		<property name="claimSubCaseService" ref="CLM_claimSubCaseService" />
		<property name="claimApplicantService" ref="CLM_claimApplicantService" />
		<property name="signConfirmationService" ref="CLM_signConfirmationService" />
		<property name="customerService" ref="CLM_claimCustomerService" />
		<property name="contractAgentService" ref="CLM_contractAgentService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="paymentPlanService" ref="CLM_paymentPlanService" />
		<property name="applyPersonalTaskService" ref="CLM_applyPersonalTaskService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.ClaimReportUCCImpl"
		id="CLM_claimReportUCC">
		<!-- 上海医保理赔信息同步、理赔注销同步 start -->
		<property name="clmSynchronUcc" ref="CLM_synchronUcc" />
		<property name="clmLogOutUcc" ref="CLM_logOutUcc" />
		<!-- 上海医保理赔信息同步、理赔注销同步 end -->
		<property name="signRegistrationService" ref="CLM_signRegistrationService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="claimBPMService" ref="CLM_claimBPMService" />
		<property name="claimBRMService" ref="CLM_claimBRMService" />
		<property name="claimSubCaseService" ref="CLM_claimSubCaseService" />
		<property name="claimPhoneServiceService" ref="CLM_claimPhoneServiceService" />
		<property name="reportCheckConfirmService" ref="CLM_reportCheckConfirmService" />
		<property name="contractAgentService" ref="CLM_contractAgentService" />
		<property name="contractMasterService" ref="CLM_contractMasterService" />
		<property name="agentService" ref="CLM_agentService2" />
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="districtService" ref="CLM_districtService" />
		<property name="customerService" ref="CLM_claimCustomerService" />
		<property name="claimContractDealService" ref="CLM_claimContractDealService" />
		<property name="paymentPlanService" ref="CLM_paymentPlanService" />
		<property name="claimBusiProdService" ref="CLM_claimBusiProdService" />
		<property name="cancelCaseService" ref="CLM_cancelCaseService" />
		<property name="claimLiabService" ref="CLM_claimLiabService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="contractProductService" ref="CLM_contractProductService" />
		<property name="iClaimReinsuranceReportClientService" ref="CLM_iClaimReinsuranceReportClientService" />
		<property name="riSuggestDetailService" ref="CLM_riSuggestDetailService" />
		<property name="insuredListService" ref="CLM_insuredListService" />
		<property name="saveSignInfoService" ref="CLM_saveSignInfoService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
		<property name="claimInvoicesPrintService" ref="CLM_claimInvoicesPrintService" />
		<property name="contractBusiProdService" ref="CLM_contractBusiProdService" />
		<property name="claimMatchCalcService" ref="CLM_claimMatchCalcService" />
		<property name="signConfirmationService" ref="CLM_signConfirmationService" />
		<property name="claimApplicantService" ref="CLM_claimApplicantService" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
		<property name="userService" ref="CLM_userService" />
		<property name="outSourceService" ref="CLM_outSourceService" />
		<property name="approveService" ref="CLM_approveService" />
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
		<!-- 理赔现价计算service -->
		<property name="claimCashvalueService" ref="CLM_claimCashvalueService" />
		<property name="claimDirectProCaseDao" ref="CLM_claimDirectProCaseDao" />
		<property name="policyHolderService" ref="CLM_policyHolderService" />
		<property name="businessProductDao" ref="CLM_businessProductDao" />
		<!-- 累加器操作类 -->
		<property name="claimAccutionService" ref="CLM_claimAccutionServiceImpl" />
		<property name="claimDutyRegisterService" ref="CLM_claimDutyRegisterService" />
		<property name="claimMatchResultService" ref="CLM_claimMatchResultService" />
		<!-- 理赔通知书短信发送批处理 -->
		<property name="claimApplicantDao" ref="CLM_claimApplicantDao" />
		<property name="noteDocSendBatchDao" ref="CLM_noteDocSendBatchDao" />
		<property name="paraDefService" ref="CLM_iClaimParaDefService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService" />
		<property name="clmSurveyItemService" ref="CLM_clmSurveyItemService" />
		<!-- 个险核心不予立案通知书短信发送范围调整 -->
		<property name="claimBeneDao" ref="CLM_claimBeneDao" />
		<property name="caseLegalPersonInfoDao" ref="CLM_caseLegalPersonInfoDao" />
		<property name="beneAndPayeService" ref="CLM_ClaimBeneAndPayeService" />
		<!-- 理赔通知书邮件发送的需求 -->
		<property name="mailDocSendBatchDao" ref="CLM_mailDocSendBatchDao" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.CustomerUCCImpl"
		id="CLM_claimCustomerUCC">
		<property name="customerService" ref="CLM_claimCustomerService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.SaveSignInfoUCCImpl"
		id="CLM_saveSignInfoUCC">
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="signRegistrationService" ref="CLM_signRegistrationService" />
		<property name="saveSignInfoService" ref="CLM_saveSignInfoService" />
		<property name="signClientService" ref="CLM_signClientService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.ClaimAccidentUCCImpl"
		id="CLM_claimAccidentUCC">
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="claimReportservice" ref="CLM_claimReportService" />
		<property name="customerService" ref="CLM_claimCustomerService" />
		<property name="contractBeneService" ref="CLM_contractBeneService" />
		<property name="reportCheckConfirmService" ref="CLM_reportCheckConfirmService" />
		<property name="districtService" ref="CLM_districtService" />
		<property name="claimSubCaseService" ref="CLM_claimSubCaseService" />
		<property name="claimApplicantService" ref="CLM_claimApplicantService" />
		<property name="signConfirmationService" ref="CLM_signConfirmationService" />
		<property name="signRegistrationService" ref="CLM_signRegistrationService" />
		<property name="claimLiabPreService" ref="CLM_claimLiabPreService" />
		<property name="contractAgentService" ref="CLM_contractAgentService" />
		<property name="agentService" ref="CLM_agentService2" />
		<property name="claimInvoicesScanService" ref="CLM_claimInvoicesScanService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="outSourceService" ref="CLM_outSourceService" />
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
		<property name="memoService" ref="CLM_memoService" />
		<property name="claimReportUCC" ref="CLM_claimReportUCC"/>
	</bean>
	<!-- 审批 -->
	<bean class="com.nci.tunan.clm.impl.approve.ucc.impl.ClaimApproveUCCImpl"
		id="CLM_claimApproveUCC">
		<property name="approveService" ref="CLM_approveService" />
		<property name="approveClientService" ref="CLM_approveClientService" />
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="paymentPlanService" ref="CLM_paymentPlanService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
	</bean>
	<!-- 审批 发邮件短信 -->
	<bean class="com.nci.tunan.clm.impl.approve.ucc.impl.PremArapSendMsgUCCImpl"
		id="CLM_premArapSendMsgUCC">
		<property name="approveService" ref="CLM_approveService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
		<property name="claimTaskStaffService" ref="CLM_claimTaskStaffService"/>
	</bean>
	<!-- end -->
	<!-- 审核初始化调工作流接口 -->
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.AuditClinentUCCImpl"
		id="CLM_auditClinentUCC">
		<property name="auditClinentService" ref="CLM_auditClinentService" />
	</bean>
	<!-- add by gaojun_wb 理赔报案接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.ClmReportInterfaceUCCImpl"
		id="CLM_clmReportInterfaceUCC">
		<property name="clmReportService" ref="CLM_clmReportService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.OutsourceOrganMappingUCC"
		id="CLM_outsourceOrganMappingUCC">
		<property name="outsourceOrganMappingService" ref="CLM_outsourceOrganMappingService" />
	</bean>
	<!-- add by gaojun_wb 查询赔案的案件状态 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryClmCaseStatusUCCImpl"
		id="CLM_queryClmCaseStatusUCC">
		<property name="claimCommonQueryService" ref="CLM_claimCommonQueryService" />
	</bean>
	<!-- add by gaojun_wb -->
	<!-- 质检结果查询ucc -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.QueryInspectResultUCCImpl"
		id="CLM_queryInspectResultUCC">
		<property name="queryInspectResultService" ref="CLM_queryInspectResultService" />
	</bean>
	<!-- 影像回调ucc -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.ImageCorrectionUCCImpl"
		id="CLM_imageCorrectionUCC">
		<property name="imageCorrectionService" ref="CLM_imageCorrectionService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.RiSuggestDetailUCCImpl"
		id="CLM_riSuggestDetailUCC">
		<property name="riSuggestDetailService" ref="CLM_riSuggestDetailService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.FixedChannelUCCImpl"
		id="CLM_fixedChannelUCC">
		<property name="fixedChannelService" ref="CLM_fixedChannelService" />
		<property name="surveyChannelService" ref="CLM_claimSurveyChannelService" />
		<property name="surveyChannelEvaluateService" ref="CLM_claimSurveyChannelEvaluateService" />
		<property name="surveyChannelUncomService" ref="CLM_claimSurveyChannelUncomService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyConclusionQueryUCCImpl"
		id="CLM_surveyConclusionQueryUCC">
		<property name="surveyConclusionQueryService" ref="CLM_surveyConclusionQueryService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="claimTreatyTalkUCC" ref="CLM_claimTreatyTalkUCC" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyFeeUCCImpl"
		id="CLM_claimSurveyFeeUCC">
		<property name="surveyFeeService" ref="CLM_claimSurveyFeeService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyDutyUCCImpl"
		id="CLM_claimSurveyDutyUCC">
		<property name="surveyDutyService" ref="CLM_claimSurveyDutyService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.ClaimReSurveyPlanUCCImpl"
		id="CLM_claimReSurveyPlanUCC">
		<property name="claimReSurveyPlanService" ref="CLM_claimReSurveyPlanService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.ClaimSurveyBatchUCCImpl"
		id="CLM_claimSurveyBatchUCC">
		<property name="claimSurveyBatchService" ref="CLM_claimSurveyBatchService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.ClaimSurveyTaskUCCImpl"
		id="CLM_claimSurveyTaskUCC">
		<property name="claimSurveyTaskService" ref="CLM_claimSurveyTaskService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyQualityEvaluateUCCImpl"
		id="CLM_claimSurveyQualityEvaluateUCC">
		<property name="surveyQualityEvaluateService" ref="CLM_claimSurveyQualityEvaluateService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyCourseUCCImpl"
		id="CLM_claimSurveyCourseUCC">
		<property name="surveyCourseService" ref="CLM_claimSurveyCourseService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService" />
		<property name="customerService" ref="CLM_claimCustomerService" />
		<property name="surveyConclusionService" ref="CLM_claimSurveyConclusionService" />
		<property name="surveyFeeService" ref="CLM_claimSurveyFeeService" />
		<property name="claimSurveyTaskService" ref="CLM_claimSurveyTaskService" />
		<property name="claimSurveyBatchService" ref="CLM_claimSurveyBatchService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyConclusionUCCImpl"
		id="CLM_claimSurveyConclusionUCC">
		<property name="surveyConclusionService" ref="CLM_claimSurveyConclusionService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyObjectUCCImpl"
		id="CLM_claimSurveyObjectUCC">
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyApplyUCCImpl"
		id="CLM_clmSurveyApplyUCC">
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.channel.ucc.impl.SurveyChannelEvaluateUCCImpl"
		id="CLM_claimSurveyChannelEvaluateUCC">
		<property name="surveyChannelEvaluateService" ref="CLM_claimSurveyChannelEvaluateService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.channel.ucc.impl.SurveyChannelUncomUCCImpl"
		id="CLM_claimSurveyChannelUncomUCC">
		<property name="surveyChannelUncomService" ref="CLM_claimSurveyChannelUncomService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.channel.ucc.impl.SurveyChannelUCCImpl"
		id="CLM_claimSurveyChannelUCC">
		<property name="surveyChannelService" ref="CLM_claimSurveyChannelService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.ContractAgentUCCImpl"
		id="CLM_contractAgentUCC">
		<property name="contractAgentService" ref="CLM_contractAgentService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.inspect.ucc.impl.ClaimCaseUCCImpl"
		id="CLM_claimCaseUCC">
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="customerServiceImpl" ref="CLM_claimCustomerService" />
		<property name="claimSurveyTaskService" ref="CLM_claimSurveyTaskService" />
		<property name="claimSurveyBatchService" ref="CLM_claimSurveyBatchService" />
		<property name="claimReSurveyPlanService" ref="CLM_claimReSurveyPlanService" />
		<property name="surveyConclusionService" ref="CLM_claimSurveyConclusionService" />
		<property name="surveyCourseService" ref="CLM_claimSurveyCourseService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.inspect.ucc.impl.ClaimMidcTaskUCCImpl"
		id="CLM_claimMidcTaskUCC">
		<property name="claimMidcTaskService" ref="CLM_claimMidcTaskService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="signRegistrationService" ref="CLM_signRegistrationService" />
		<property name="claimOrganService" ref="CLM_claimOrganService" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
		<property name="userService" ref="CLM_userService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.inspect.ucc.impl.MiddleCheckConditionUCCImpl"
		id="CLM_middleCheckConditionUCC">
		<property name="middleCheckConditionService" ref="CLM_middleCheckConditionService" />
		<property name="claimOrganService" ref="CLM_claimOrganService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.inspect.ucc.impl.ClaimMidcPlanUCCImpl"
		id="CLM_claimMidcPlanUCC">
		<property name="claimMidcPlanService" ref="CLM_claimMidcPlanService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.inspect.ucc.impl.MonitoringQualityUCCImpl"
		id="CLM_monitoringQualityUCC">
		<property name="monitoringQualityService" ref="CLM_monitoringQualityService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.SignConfirmationUCCImpl"
		id="CLM_signConfirmationUCC">
		<property name="signConfirmationService" ref="CLM_signConfirmationService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="customerService" ref="CLM_claimCustomerService" />
		<property name="claimOrganService" ref="CLM_claimOrganService" />
		<property name="claimReportUCC" ref="CLM_claimReportUCC" />
		<property name="claimSignConfirmService" ref="CLM_claimSignConfirmService" />
		<property name="outSourceService" ref="CLM_outSourceService" />
		<property name="claimSubCaseService" ref="CLM_claimSubCaseService" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<!-- 签收确认重新抄单 -->
		<property name="checkRuleVerifyUCC" ref="CLM_checkRuleVerifyUCC"></property>
	</bean>
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.ClaimSignConfirmUCCImpl"
		id="CLM_claimSignConfirmUCC">
		<property name="claimSignConfirmService" ref="CLM_claimSignConfirmService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.ClaimSubCaseUCCImpl"
		id="CLM_claimSubCaseUCC">
		<property name="claimSubCaseService" ref="CLM_claimSubCaseService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.ClaimApplicantUCCImpl"
		id="CLM_claimApplicantUCC">
		<property name="claimApplicantService" ref="CLM_claimApplicantService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.SignSharePoolUCCImpl"
		id="CLM_iSignSharePoolUCC">
		<property name="signSharePoolClientService" ref="CLM_iSignSharePoolClientService" />
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
		<!-- <property name="claimSubCaseService" ref="CLM_claimSubCaseService"/> 
			<property name="outSourceService" ref="CLM_outSourceService"/> -->
	</bean>
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.ClaimCommonQueryUCCImpl"
		id="CLM_claimCommonQueryUCC">

		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
		<property name="claimCommonQueryService" ref="CLM_claimCommonQueryService" />
		<property name="claimPASService" ref="CLM_claimPASService" />
		<property name="policyHolderService" ref="CLM_policyHolderService" />
		<property name="insuredListService" ref="CLM_insuredListService" />
		<property name="claimApplicantService" ref="CLM_claimApplicantService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="districtService" ref="CLM_districtService" />
		<property name="customerService" ref="CLM_claimCustomerService" />
		<property name="reportCheckConfirmService" ref="CLM_reportCheckConfirmService" />
		<property name="claimLiabPreService" ref="CLM_claimLiabPreService" />
		<property name="contractAgentService" ref="CLM_contractAgentService" />
		<property name="agentService" ref="CLM_agentService2" />
		<property name="riSuggestDetailService" ref="CLM_riSuggestDetailService" />
		<property name="memoService" ref="CLM_memoService" />
		<property name="claimPolicyHangUpUCC" ref="CLM_claimPolicyHangUpUCC" />
		<property name="contractMasterService" ref="CLM_contractMasterService" />
		<property name="claimRiskLevelDoubtfulService" ref="CLM_claimRiskLevelDoubtfulService" />
		<property name="claimInvoicesPDPrintService" ref="CLM_claimInvoicesPDPrintService" />
		<property name="riskReportConfigService" ref="CLM_riskReportConfigService" />
		<property name="ciitcLinkSwitchSetService" ref="CLM_ciitcLinkSwitchSetService" />
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
		<!-- 实名查验 -->
		<property name="claimRealNameCheckUCC" ref="CLM_claimRealNameCheckUCC" />
		<property name="claimRiskLownumService" ref="CLM_claimRiskLownumService" />
		<!-- 关联图谱实时规则提示配置service-->
		<property name = "cheatAtlasConfigService" ref = "CLM_cheatAtlasConfigService"/>
	</bean>
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.ExternalSystemInfoUCC"
		id="CLM_externalSystemInfoUCC">
		<property name="externalSystemInfoService" ref="CLM_externalSystemInfoService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.ClaimDangerReportUCCImpl"
		id="CLM_claimDangerReportUCC">
		<property name="claimDangerReportService" ref="CLM_claimDangerReportService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.TaskManageUCCImpl"
		id="CLM_taskManageUCC">
		<property name="taskManageService" ref="CLM_taskManageService" />
		<property name="claimAreaService" ref="CLM_claimAreaService" />
	</bean>
	<!-- 事中质检抽取规则UCC ws服务 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.CaseExtractingUCCImpl"
		id="CLM_caseExtractingUCC">
		<property name="middleCheckConditionService" ref="CLM_middleCheckConditionService" />
		<property name="contractAgentService" ref="CLM_contractAgentService" />
		<property name="claimReportUCC" ref="CLM_claimReportUCC" />
		<property name="customerService" ref="CLM_claimCustomerService" />
		<property name="signConfirmationUCC" ref="CLM_signConfirmationUCC" />
		<property name="claimApplicantService" ref="CLM_claimApplicantService" />
		<property name="agentService" ref="CLM_agentService2" />
		<property name="cancelCaseService" ref="CLM_cancelCaseService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="userDao" ref="CLM_claimUserDao" />
		<property name="approveService" ref="CLM_approveService" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
		<property name="sendMessageUCC" ref="CLM_sendMessageUCC" />
		<property name="claimLiabService" ref="CLM_claimLiabService" />
	</bean>

	<!-- end -->
	<!-- and by caoyy_wb -->
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.HospitalInfoQueryUccImpl"
		id="CLM_hospitalInfoQueryUcc">
		<property name="claimCommonService" ref="CLM_claimCommonService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.AccResultQueryUCCImpl"
		id="CLM_accResultQueryUCC">
		<property name="claimCommonService" ref="CLM_claimCommonService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.ReportConfimUCCImpl"
		id="CLM_reportConfimUCC">
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="claimSaveLiabpreService" ref="CLM_claimSaveLiabpreService" />
		<property name="claimLiabPreService" ref="CLM_claimLiabPreService" />
		<property name="contractAgentService" ref="CLM_contractAgentService" />
		<property name="agentService" ref="CLM_agentService2" />
		<property name="claimBRMService" ref="CLM_claimBRMService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="claimOrganService" ref="CLM_claimOrganService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="clmSurveyItemService" ref="CLM_clmSurveyItemService" />
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="claimBPMService" ref="CLM_claimBPMService" />
		<property name="claimReportUCC" ref="CLM_claimReportUCC" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
		<property name="claimInvoicesPrintService" ref="CLM_claimInvoicesPrintService" />
		<property name="signConfirmationService" ref="CLM_signConfirmationService" />
		<property name="claimEmrDao" ref="CLM_claimEmrDao" />
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
		<property name="zJRapidClaimPortsService" ref="CLM_zJRapidClaimPortsService" />
		<property name="ymtpClaimPortsService" ref="CLM_ymtpClaimPortsService"/>
	</bean>
	<!-- 事后抽取质检任务 -->
	<bean
		class=" com.nci.tunan.clm.impl.inspect.ucc.impl.ExtractClaimAfcPlanProjectUCCImpl"
		id="CLM_extractClaimAfcPlanProjectUCC">
		<property name="extractClaimAfcPlanProjectService" ref="CLM_extractClaimAfcPlanProjectService" />
		<property name="middleCheckConditionService" ref="CLM_middleCheckConditionService" />
		<property name="claimAfcPlanProjectService" ref="CLM_claimAfcPlanProjectService" />
		<property name="claimAreaService" ref="CLM_claimAreaService" />
		<property name="userService" ref="CLM_userService" />
	</bean>
	<!-- 复核和预付的结论接口 -->
	<bean
		class=" com.nci.tunan.clm.impl.exports.ws.impl.RegisterAffirmConclusionUCCImpl"
		id="CLM_registerAffirmConclusionUCCImpl">
		<property name="claimBusiProdService" ref="CLM_claimBusiProdService" />
	</bean>
	<!-- end -->
	<!-- add by xuyz_wb -->
	<bean class="com.nci.tunan.clm.impl.calc.ucc.impl.ClaimMatchCalcUCCImpl"
		id="CLM_claimMatchCalcUCC">
		<property name="claimMatchCalcService" ref="CLM_claimMatchCalcService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimMatchResultUCCImpl"
		id="CLM_claimMatchResultUCC">
		<property name="claimMatchResultService" ref="CLM_claimMatchResultService" />
		<property name="contractProductService" ref="CLM_contractProductService" />
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="claimMatchCalcUCC" ref="CLM_claimMatchCalcUCC" />
		<property name="claimMatchCalcService" ref="CLM_claimMatchCalcService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="claimTaskStaffService" ref="CLM_claimTaskStaffService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.EpibolyMonitoringUCCImpl"
		id="CLM_epibolyMonitoringUCC">
		<property name="epibolyMonitoringService" ref="CLM_epibolyMonitoringService" />
	</bean>
	<!-- end -->

	<!--立案责任明细 action liuDongXu start -->
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.DisabilityUCCImpl"
		id="CLM_disabilityUCC">
		<property name="disabilityService" ref="CLM_disabilityService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.SeriousDisabilityUCCImpl"
		id="CLM_seriousDisabilityUCC">
		<property name="seriousDisabilityService" ref="CLM_seriousDisabilityServiceImpl" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.JobCategoryUCCImpl"
		id="CLM_jobCategoryUCC">
		<property name="jobCategoryService" ref="CLM_jobCategoryService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.ClaimBillPaidUCCImpl"
		id="CLM_claimBillPaidUCC">
		<property name="claimBillPaidService" ref="CLM_claimBillPaidServiceImpl" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.ClaimDisabilityUCCImpl"
		id="CLM_claimDisabilityUCC">
		<property name="claimDisabilityService" ref="CLM_claimDisabilityService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimSurgeryUCC"
		id="CLM_mClaimSurgeryUCC">
		<property name="claimSurgeryService" ref="CLM_claimSurgeryService" />
	</bean>

	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.SpecialDiseaseItemUCCImpl"
		id="CLM_mSpecialDiseaseItemUCC">
		<property name="specialDiseaseItemService" ref="CLM_specialDiseaseItemService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.SpecialDiseaseTypeUCCImpl"
		id="CLM_mSpecialDiseaseTypeUCC">
		<property name="specialDiseaseTypeService" ref="CLM_specialDiseaseTypeService" />
	</bean>

	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimInjuryUCCImpl"
		id="CLM_mClaimInjuryUCC">
		<property name="claimInjuryService" ref="CLM_claimInjuryService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimBillUCCImpl"
		id="CLM_mClaimBillUCC">
		<property name="claimBillService" ref="CLM_claimBillService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimBillItemUCCImpl"
		id="CLM_mClaimBillItemUCC">
		<property name="claimBillItemService" ref="CLM_claimBillItemService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimSpecialTypeUCCImpl"
		id="CLM_claimSpecialTypeUCC">
		<property name="claimSpecialTypeService" ref="CLM_claimSpecialTypeService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimSpecialCodeUCCImpl"
		id="CLM_claimSpecialCodeUCC">
		<property name="claimSpecialCodeService" ref="CLM_claimSpecialCodeService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimSpecialUCCImpl"
		id="CLM_mClaimSpecialUCC">
		<property name="claimSpecialService" ref="CLM_claimSpecialService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimDutyRegisterUCCImpl"
		id="CLM_claimDutyRegisterUCCImpl">
		<property name="claimDutyRegisterService" ref="CLM_claimDutyRegisterService" />
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="dataCollectUcc" ref="CLM_dataCollectUCC" />
		<property name="claimSubCaseService" ref="CLM_claimSubCaseService" />
		<property name="claimCommonService" ref="CLM_claimCommonService" />
		<property name="disabilityService" ref="CLM_disabilityService" />
		<property name="seriousDisabilityService" ref="CLM_seriousDisabilityServiceImpl" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="claimBillService" ref="CLM_claimBillService" />
	</bean>
	<!--立案责任明细 action liuDongXu end -->
	<!-- add by liulei_wb -->
	<bean
		class="com.nci.tunan.clm.impl.report.ucc.impl.ReportCommonPoolClientUCCImpl"
		id="CLM_reportCommonPoolClientUCC">
		<property name="reportCommonPoolService" ref="CLM_reportCommonPoolService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.ApplyPersonalTaskUCCImpl"
		id="CLM_applyPersonalTaskUCC">
		<property name="applyPersonalTaskService" ref="CLM_applyPersonalTaskService" />
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
		<property name="claimReportService" ref="CLM_claimReportService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.ClaimContractDealUCCImpl"
		id="CLM_contractDealUCC">
		<property name="claimContractDealService" ref="CLM_claimContractDealService" />
		<property name="claimPASServiceImpl" ref="CLM_claimPASService" />
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="remitManageService" ref="CLM_remitManageService" />
		<property name="undwerTwiceService" ref="CLM_undwerTwiceService"/>
		<property name="claimMatchCalcService" ref="CLM_claimMatchCalcService"/>
		<property name="claimPolicyDao" ref="CLM_claimPolicyDao"/>		
		<property name="contractBusiProdService" ref="CLM_contractBusiProdService"/>
		<property name="businessProductDao" ref="CLM_businessProductDao"/>
		<property name="claimProductDao" ref="CLM_claimProductDao"/>
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil"/>
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.report.ucc.impl.ReportCheckConfirmUCCImpl"
		id="CLM_reportCheckConfirmUCC">
		<property name="reportCheckConfirmService" ref="CLM_reportCheckConfirmService" />
		<property name="contractBeneService" ref="CLM_contractBeneService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.exports.hs.impl.QueryClaimCaseUCCImpl"
		id="CLM_queryClaimCaseUCC">
		<property name="claimCommonQueryService" ref="CLM_claimCommonQueryService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.invoice.ucc.impl.ClaimInvoicesScanUCCImpl"
		id="CLM_claimInvoicesScanUCC">
		<property name="claimInvoicesScanService" ref="CLM_claimInvoicesScanService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.ClaimDiscussUCCImpl"
		id="CLM_claimDiscussUCC">
		<property name="claimDiscussService" ref="CLM_claimDiscussService" />
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
	</bean>
	<!-- end -->

	<!-- add by zhaoyq_wb begin -->
	<!-- 核对案件信息/查看单证 -->
	<bean
		class="com.nci.tunan.clm.impl.audit.ucc.impl.AuditClaimCheckListUCCImpl"
		id="CLM_auditClaimCheckListUCC">
		<property name="auditClaimCheckListService" ref="CLM_auditClaimCheckListService" />
		<property name="reportCheckConfirmService" ref="CLM_reportCheckConfirmService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
		<property name="claimInvoicesPrintService" ref="CLM_claimInvoicesPrintService" />
		<property name="customerService" ref="CLM_claimCustomerService" />
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
		<property name="paraDefUCC" ref="CLM_iClaimParaDefUCC" />
		<property name="directClaimPortsUCC" ref="CLM_directClaimPortsUCC" />
		<property name="claimReportUCC" ref="CLM_claimReportUCC"/>
	</bean>

	<!-- 反馈质检 -->
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.ClaimAfterCheckUCCImpl"
		id="CLM_claimAfterCheckUCC">
		<property name="claimAfterCheckService" ref="CLM_claimAfterCheckService" />
		<property name="customerService" ref="CLM_claimCustomerService" />
		<property name="auditClaimCheckListService" ref="CLM_auditClaimCheckListService" />
	</bean>
	<!-- 单证打印 -->
	<bean
		class="com.nci.tunan.clm.impl.invoice.ucc.impl.ClaimInvoicesPrintUCCImpl"
		id="CLM_claimInvoicesPrintUCC">
		<property name="claimInvoicesPrintService" ref="CLM_claimInvoicesPrintService" />
		<property name="claimAfterCheckService" ref="CLM_claimAfterCheckService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.invoice.ucc.impl.ClaimOrganUCCImpl"
		id="CLM_claimOrganUCC">
		<property name="claimOrganService" ref="CLM_claimOrganService" />
	</bean>
	<!-- 保单条款查看 -->
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.QueryPolicyClauseUCCImpl"
		id="CLM_queryPolicyClauseUCC">
		<property name="queryPolicyClauseService" ref="CLM_queryPolicyClauseService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
	</bean>
	<!-- 维护理赔关怀信息 -->
	<bean
		class="com.nci.tunan.clm.impl.claimcare.ucc.impl.PreserveCareServiceMsgUCCImpl"
		id="CLM_preserveCareServiceMsgUCC">
		<property name="preserveCareServiceMsgService" ref="CLM_preserveCareServiceMsgService" />
	</bean>
	<!-- 二核服务方接口 根据赔案号查询信息，包含批次号和二核标志 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.UwQueryInformationCaseUCCImpl"
		id="CLM_uwQueryInformationCaseUCC">
		<property name="uwQueryInformationCaseService" ref="CLM_uwQueryInformationCaseService" />
	</bean>
	<!-- 二核 -->
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.UndwerTwiceUCCImpl"
		id="CLM_undwerTwiceUCC">
		<property name="undwerTwiceService" ref="CLM_undwerTwiceService" />
		<property name="claimMatchCalcService" ref="CLM_claimMatchCalcService" />
	</bean>
	<!-- 服务方接口-接收核保结论相关信息 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.UwAcceptUnderResultUCCImpl"
		id="CLM_uwAcceptUnderResultUCC">
		<property name="uwAcceptUnderResultService" ref="CLM_uwAcceptUnderResultService" />
	</bean>
	<!-- 预理算 -->
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.ClaimSaveLiabpreUCCImpl"
		id="CLM_claimSaveLiabpreUCC">
		<property name="claimSaveLiabpreService" ref="CLM_claimSaveLiabpreService" />
	</bean>
	<!-- end -->

	<!-- add by yanggc_wb 受益人与领款人 -->
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimBusiProdUccImpl"
		id="CLM_claimBusiProdUCC">
		<property name="claimBusiProdService" ref="CLM_claimBusiProdService" />
		<property name="contractProductService" ref="CLM_contractProductService" />
	</bean>

	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimBeneAndPayeUccImpl"
		id="CLM_ClaimBeneAndPayeUCC">
		<property name="beneAndPayeService" ref="CLM_ClaimBeneAndPayeService" />
		<property name="claimBPMService" ref="CLM_claimBPMService" />
	</bean>

	<!-- 豁免处理 yanggc_wb -->
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.RemitManageUCCImpl"
		id="CLM_remitManageUCC">
		<property name="remitManageService" ref="CLM_remitManageService" />
		<property name="auditClaimCheckListWorkflowService" ref="CLM_auditClaimCheckListWorkflowService" />
		<property name="claimAccidentUCC" ref="CLM_claimAccidentUCC" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="claimBusiProdUCC" ref="CLM_claimBusiProdUCC" />
		<!-- #102388 保存数据到理赔豁免金额推送任务表中 -->
		<property name="claimPushTaskService" ref="CLM_claimPushTaskService" />
	</bean>
	<!-- add by zhangdong_wb1 -->
	<bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimPhoneServiceUCC"
		id="CLM_claimPhoneServiceUCC">
		<property name="claimPhoneServiceService" ref="CLM_claimPhoneServiceService" />
	</bean>
	<!-- end -->
	<!-- and by caoyy_wb 风险评估报告 -->
	<bean
		class="com.nci.tunan.clm.impl.report.ucc.impl.ClaimRiskReportConfigUCCimpl"
		id="CLM_claimRiskReportConfigUCC">
		<property name="riskReportConfigService" ref="CLM_riskReportConfigService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="undwerTwiceService" ref="CLM_undwerTwiceService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="surveyConclusionService" ref="CLM_claimSurveyConclusionService" />
		<property name="contractBusiProdService" ref="CLM_contractBusiProdService" />
		<property name="insuredListService" ref="CLM_insuredListService" />
		<property name="claimMatchResultService" ref="CLM_claimMatchResultService" />
		<property name="memoService" ref="CLM_memoService" />
		<property name="contractBeneService" ref="CLM_contractBeneService" />
		<property name="claimLiabService" ref="CLM_claimLiabService" />
	</bean>
	<!-- 手工分期给付 -->
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.ClaimInstalmentUCCImpl"
		id="CLM_claimInstalmentUCC">
		<property name="claimInstalmentService" ref="CLM_claimInstalmentService" />
	</bean>
	<!-- 出具审核结论 -->
	<bean
		class=" com.nci.tunan.clm.impl.instalment.ucc.impl.ClaimInstalmentAuditConclusionUCCImpl"
		id="CLM_claimInstalmentAuditConclusionUCC">
		<property name="claimInstalmentAuditConclusionService" ref="CLM_claimInstalmentAuditConclusionService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="claimInstalmentAuditService" ref="CLM_claimInstalmentAuditService" />
	</bean>
	<!-- 分次给付审核 -->
	<bean
		class="com.nci.tunan.clm.impl.instalment.ucc.impl.ClaimInstalmentAuditUCCImpl"
		id="CLM_claimInstalmentAuditUCC">
		<property name="claimInstalmentAuditService" ref="CLM_claimInstalmentAuditService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="surveyConclusionService" ref="CLM_claimSurveyConclusionService" />
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService" />
		<property name="clmSurveyItemService" ref="CLM_clmSurveyItemService" />
	</bean>
	<!-- 制定质检项目 -->
	<bean
		class=" com.nci.tunan.clm.impl.inspect.ucc.impl.ClaimAfterCheckProjectUCCImpl"
		id="CLM_claimAfterCheckProjectUCC">
		<property name="claimAfterCheckProjectService" ref="CLM_claimAfterCheckProjectService" />
		<property name="userService" ref="CLM_userService" />
	</bean>
	<!-- 制定质检计划 -->
	<bean
		class=" com.nci.tunan.clm.impl.inspect.ucc.impl.ClaimAfcPlanProjectUCCImpl"
		id="CLM_claimAfcPlanProjectUCC">
		<property name="taskManageService" ref="CLM_taskManageService" />
		<property name="claimAfcPlanProjectService" ref="CLM_claimAfcPlanProjectService" />
		<property name="middleCheckConditionService" ref="CLM_middleCheckConditionService" />
		<property name="claimAreaService" ref="CLM_claimAreaService" />
		<property name="userService" ref="CLM_userService" />
	</bean>
	<!-- begin liupengit1 -->
	<bean
		class="com.nci.tunan.clm.impl.inspect.ucc.impl.ClaimAfcCheckProjectUCCImpl"
		id="CLM_claimAfcCheckProjectUCC">
		<property name="claimAfcCheckProjectService" ref="CLM_claimAfcCheckProjectService" />
		<property name="claimAfterCheckService" ref="CLM_claimAfterCheckService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="claimSubCaseService" ref="CLM_claimSubCaseService" />
	</bean>
	<!-- end -->
	<!-- 外包商录入 -->
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.EpibolyManageUCCImpl"
		id="CLM_epibolyManageUCC">
		<property name="epibolyManageService" ref="CLM_epibolyManageService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.EpibolyMainTainUCCImpl"
		id="CLM_epibolyMainTainUCC">
		<property name="epibolyMainTainService" ref="CLM_epibolyMainTainService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.EpilolyDeductEnterUCCImpl"
		id="CLM_epilolyDeductEnterUCC">
		<property name="epilolyDeductEnterService" ref="CLM_epilolyDeductEnterService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.EpibolyCheckoutUccImpl"
		id="CLM_epibolyCheckoutUcc">
		<property name="epibolyCheckoutService" ref="CLM_epibolyCheckoutService" />
	</bean>
	<!-- end -->

	<!-- 支付计划 renxiaodi -->
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.PaymentPlanUccImpl"
		id="CLM_paymentPlanUcc">
		<property name="paymentPlanService" ref="CLM_paymentPlanService" />
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="claimMatchResultService" ref="CLM_claimMatchResultService" />
		<property name="claimSubCaseService" ref="CLM_claimSubCaseService" />
	</bean>

	<!-- add by zhangdong_wb1 -->
	<bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.MaintHospitalUCC"
		id="CLM_maintHospitalUCC">
		<property name="maintHospitalService" ref="CLM_maintHospitalService" />
		<property name="claimHospitalServiceService" ref="CLM_claimHospitalServiceService" />
		<property name="hospitalService" ref="CLM_hospitalService" />
		<property name="districtService" ref="CLM_districtService" />
	</bean>

	<!-- add by lizd_wb 设置特定节假日 -->
	<bean
		class="com.nci.tunan.clm.impl.parameter.ucc.impl.MaintainSpecialFestivalUCC"
		id="CLM_maintainSpecialFestivalUCC">
		<property name="maintainSpecialFestivalServiceImpl" ref="CLM_maintainSpecialFestivalService" />
	</bean>
	<!-- end -->

	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.ClaimTreatyTalkUCC"
		id="CLM_claimTreatyTalkUCC">
		<property name="claimTreatyTalkService" ref="CLM_claimTreatyTalkService" />
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="signRegistrationService" ref="CLM_signRegistrationService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="claimOrganService" ref="CLM_claimOrganService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService" />
		<property name="claimDiscussService" ref="CLM_claimDiscussService" />
		<property name="undwerTwiceService" ref="CLM_undwerTwiceService" />
		<property name="clmSurveyItemService" ref="CLM_clmSurveyItemService" />
		<property name="claimCaseDao" ref="CLM_claimCaseDao" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
	    <property name="claimTaskStaffService" ref="CLM_claimTaskStaffService"/>
	</bean>
	<!-- end -->

	<!-- 支付计划 renxiaodi -->
	<bean
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.HandworkAssignTaskUccImpl"
		id="CLM_handworkAssignTaskUcc">
		<property name="handworkAssignTaskService" ref="CLM_handworkAssignTaskService" />
		<property name="claimAreaService" ref="CLM_claimAreaService" />
	</bean>
	<!-- 备注/问题件管理 renxiaodi -->
	<bean class="com.nci.tunan.clm.impl.memo.ucc.impl.MemoUccImpl" id="CLM_memoUcc">
		<property name="memoService" ref="CLM_memoService" />
	</bean>

	<!-- 历史服务查询接口 sunjl_wb -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.QueryClientHistoryUCCImpl"
		id="CLM_queryClientHistoryUCC">
		<property name="queryClientHistoryService" ref="CLM_queryClientHistoryService" />
	</bean>
	<!-- 历史服务查询接口end -->

	<!-- 自核规则校验接口 sunjl_wb -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.CheckRuleVerifyUCCImpl"
		id="CLM_checkRuleVerifyUCC">
		<property name="checkRuleVerifyService" ref="CLM_checkRuleVerifyService" />
		<property name="signRegistrationService" ref="CLM_signRegistrationService" />
		<property name="claimBusiProdService" ref="CLM_claimBusiProdService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
		<property name="cancelCaseService" ref="CLM_cancelCaseService" />
		<property name="contractMasterDao" ref="CLM_contractMasterDao" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="ciitcLinkSwitchSetService" ref="CLM_ciitcLinkSwitchSetService" />
		<property name="claimPolicyHangUpService" ref="CLM_claimPolicyHangUpService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="clmRiskWarningService" ref="CLM_clmRiskWarningService" />
    	<property name="claimTaskStaffService" ref="CLM_claimTaskStaffService"/>
    	<property name="claimBillService" ref="CLM_claimBillService" />
	</bean>
	<!-- 自核规则校验接口 end -->

	<bean
		class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimSurveyRuleUCCImpl"
		id="CLM_claimSurveyRuleUCC">
		<property name="claimSurveyRuleService" ref="CLM_claimSurveyRuleService" />
		<property name="claimRgtCheckRuleService" ref="CLM_iClaimRgtCheckRuleService" />
	</bean>
	<!-- 执行任务监控UCC -->
	<bean
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.ClaimTaskManageUCCImpl"
		id="CLM_claimTaskManageUCC">
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
		<property name="claimTaskStaffService" ref="CLM_claimTaskStaffService" />
		<property name="signRegistrationService" ref="CLM_signRegistrationService" />
		<property name="handworkAssignTaskService" ref="CLM_handworkAssignTaskService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="claimAreaPersonService" ref="CLM_claimAreaPersonService" />
		<property name="claimAreaService" ref="CLM_claimAreaService" />
		<property name="userService" ref="CLM_userService" />
		<property name="taskManageService" ref="CLM_taskManageService" />
		<property name="claimTreatyTalkService" ref="CLM_claimTreatyTalkService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="CLMWorkPlatformService" ref="CLM_CLMWorkPlatformService" />
		<property name="claimInspectTaskDao" ref="CLM_claimInspectTaskDao" />
	</bean>
	<!-- 未成年人保额标准 add by xuyz_wb start -->
	<bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimChildrenAmntUCC"
		id="CLM_claimChildrenAmntUCC">
		<property name="claimChildrenAmntService" ref="CLM_claimChildrenAmntService" />
	</bean>
	<!-- 未成年人保额标准 add by xuyz_wb end -->

	<!-- 理赔工作台UCC -->
	<bean
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.CLMWorkPlatformUCCImpl"
		id="CLM_CLMWorkPlatformUCC">
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
		<property name="clmWorkPlatformService" ref="CLM_CLMWorkPlatformService" />
		<property name="signRegistrationService" ref="CLM_signRegistrationService" />
		<property name="claimTaskStaffService" ref="CLM_claimTaskStaffService" />
		<property name="claimTreatyTalkService" ref="CLM_claimTreatyTalkService" />
		<property name="claimDiscussService" ref="CLM_claimDiscussService" />
		<property name="handworkAssignTaskService" ref="CLM_handworkAssignTaskService" />
		<property name="claimSubCaseService" ref="CLM_claimSubCaseService" />
	</bean>
	<!-- add by luming -->
	<bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.MaintStaffUCC"
		id="CLM_maintStaffUCC">
		<property name="maintStaffService" ref="CLM_maintStaffService" />
	</bean>
	<!-- end -->

	<!-- add by daizheng1 维护前置调查计划 -->
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.ClaimBfSurveyPlanUCCImpl"
		id="CLM_claimBfSurveyPlanUCC">
		<property name="claimBfSurveyPlanService" ref="CLM_claimBfSurveyPlanService" />
		<property name="claimBfSurveyRamntService" ref="CLM_claimBfSurveyRamntService" />
		<property name="claimBfSurveyOrgService" ref="CLM_claimBfSurveyOrgService" />
		<property name="claimBfSurveyChannelService" ref="CLM_claimBfSurveyChannelService" />
		<property name="claimReSurveyService" ref="CLM_claimReSurveyService" />
	</bean>
	<!-- end -->
	<!-- add by xuyz_wb 上载赔案列表 发起任务 -->
	<bean class="com.nci.tunan.clm.impl.recheckplan.ucc.impl.ExtractRecheckUCC"
		id="CLM_extractRecheckUCC">
		<property name="extractRecheckService" ref="CLM_extractRecheckService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="clmSurveyItemService" ref="CLM_clmSurveyItemService" />
	</bean>
	<!-- end -->

	<!-- yulei_wb -->
	<bean class="com.nci.tunan.clm.impl.redo.ucc.impl.ClaimRedoUCCImpl"
		id="CLM_claimRedoUCC">
		<property name="claimRedoService" ref="CLM_claimRedoService" />
		<property name="claimInvoicesScanService" ref="CLM_claimInvoicesScanService" />
	</bean>
	<!-- end -->
	<!-- add by xuyz_wb 查询任务轨迹 -->
	<bean
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.QueryTaskTraceUCCImpl"
		id="CLM_queryTaskTraceUCC">
		<property name="queryTaskTraceService" ref="CLM_queryTaskTraceService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
	</bean>
	<!-- end -->
	<!-- add by liulei_wb 扫描/回销索赔单证UCC -->
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.CheckOperateUCCImpl"
		id="CLM_checkOperateUCC">
		<property name="claimInvoicesScanService" ref="CLM_claimInvoicesScanService" />
	</bean>
	<!-- add by yangjx 归档核对清单 -->
	<bean class="com.nci.tunan.clm.impl.querylist.ucc.impl.ClaimQueryListUCCImpl"
		id="CLM_claimQueryListUCC">
		<property name="claimFileCheckListService" ref="CLM_claimFileCheckListService" />
		<property name="claimFeeReceiveListService" ref="CLM_claimFeeReceiveListService" />
		<property name="claimSurveyListService" ref="CLM_claimSurveyListService" />
		<property name="extractClaimAfcPlanProjectService" ref="CLM_extractClaimAfcPlanProjectService" />
	</bean>
	<!-- end -->
	<!-- add by xuyz_wb 分支流程查询 -->
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.QueryBranchFlowUCCImpl"
		id="CLM_queryBranchFlowUCC">
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="queryBranchFlowService" ref="CLM_queryBranchFlowService" />
		<property name="claimPayChangeService" ref="CLM_iClaimPayChangeService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
		<property name="claimRedoService" ref="CLM_claimRedoService" />

	</bean>
	<!-- end -->
	<!-- add by zhouzx_wb 时效清单ucc -->
	<bean
		class="com.nci.tunan.clm.impl.report.ucc.impl.ClaimReportHourEfficiencyUCCImpl"
		id="CLM_claimReportHourEfficiencyUCC">
		<property name="claimReportHourEfficiencyService" ref="CLM_claimReportHourEfficiencyService" />
	</bean>
	<!-- end -->
	<!-- add by gaoxyit 问题件清单查询 -->
	<bean class="com.nci.tunan.clm.impl.querylist.ucc.impl.ClaimCaseListUCCImpl"
		id="CLM_claimProblemCaseListUCC">
		<property name="claimProblemCaseListService" ref="CLM_claimProblemCaseListService" />
		<property name="queryListService" ref="CLM_iQueryListService" />

	</bean>

	<!-- 预付管理 renxiaodi -->
	<bean class="com.nci.tunan.clm.impl.advance.ucc.impl.AdvancePayUccImpl"
		id="CLM_advancePayUcc">
		<property name="advancePayService" ref="CLM_advancePayService" />
		<property name="claimMatchCalcService" ref="CLM_claimMatchCalcService" />
		<property name="signRegistrationService" ref="CLM_signRegistrationService" />
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="surveyConclusionService" ref="CLM_claimSurveyConclusionService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService" />
		<property name="clmSurveyItemService" ref="CLM_clmSurveyItemService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="paymentPlanService" ref="CLM_paymentPlanService" />
	</bean>

	<!-- add by yangjx 涉及出险人所有保单信息查询 -->
	<bean class="com.nci.tunan.clm.impl.report.ucc.impl.ClaimQueryPolicyUCCImpl"
		id="CLM_claimQueryPolicyUCC">
		<property name="policyHolderService" ref="CLM_policyHolderService" />
		<property name="insuredListService" ref="CLM_insuredListService" />
		<property name="contractMasterService" ref="CLM_contractMasterService" />
		<property name="claimCustomerService" ref="CLM_claimCustomerService" />
	</bean>
	<!-- add by gaoxyit 参数管理-可挂起的保全项维护 -->
	<bean
		class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimHangEdorItemUCCImpl"
		id="CLM_claimHangEdorItemUCC">
		<property name="claimHangEdorItemService" ref="CLM_claimHangEdorItemService" />
	</bean>
	<!-- Add By tianph 差错件 -->
	<bean class="com.nci.tunan.clm.impl.querylist.ucc.impl.ErrorDetailUCCImpl"
		id="CLM_errorDetailUCC">
		<property name="errorDetailService" ref="CLM_errorDetailService" />
	</bean>
	<!-- end -->
	<!-- add by daizheng1 应付实付清单 -->
	<bean class="com.nci.tunan.clm.impl.querylist.ucc.impl.PremPayUCCImpl"
		id="CLM_premPayUCC">
		<property name="premPayService" ref="CLM_premPayService" />
	</bean>
	<!-- end -->
	<!-- add by daizheng1 未决赔款准备金清单 -->
	<bean class="com.nci.tunan.clm.impl.querylist.ucc.impl.NoPayPrepareUCCImpl"
		id="CLM_noPayPrepareUCC">
		<property name="noPayPrepareService" ref="CLM_noPayPrepareService" />
	</bean>
	<!-- end -->
	<!-- add by yangjx 保单挂起解挂 -->
	<bean class="com.nci.tunan.clm.impl.common.ucc.impl.ClaimPolicyHangUpUCCImpl"
		id="CLM_claimPolicyHangUpUCC">
		<property name="policyHolderService" ref="CLM_policyHolderService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="auditClaimCheckListService" ref="CLM_auditClaimCheckListService" />
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
		<property name="claimCaseDao" ref="CLM_claimCaseDao" />
		<property name="contractBusiProdService" ref="CLM_contractBusiProdService" />
		<property name="claimAccidentService" ref="CLM_claimAccidentService" />
		<property name="businessProductDao" ref="CLM_businessProductDao" />
		<property name="claimSubCaseDao" ref="CLM_claimSubCaseDao" />
	</bean>
	<!-- add by gaoxyit -->
	<bean
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.ClaimTaskStaffUCCImpl"
		id="CLM_claimTaskStaffUCC">
		<property name="claimTaskStaffService" ref="CLM_claimTaskStaffService" />
		<property name="claimCommonParaService" ref="CLM_iClaimCommonParaService" />
	</bean>
	<!-- 调查项目 -->
	<bean class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyItemUCCImpl"
		id="CLM_clmSurveyItemUCC">
		<property name="clmSurveyItemService" ref="CLM_clmSurveyItemService" />
	</bean>
	<!-- add by yangjx 外包案件池 -->
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimBpoManageUCCImpl"
		id="CLM_claimBpoManageUCC">
		<property name="claimBpoManageService" ref="CLM_claimBpoManageService" />
		<property name="claimAreaService" ref="CLM_claimAreaService" />
	</bean>
	<!-- add by renxiaodi 预付审批生效 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.AdvancePayCheckEffectUCCImpl"
		id="CLM_advancePayCheckEffectUCC">
		<property name="approveService" ref="CLM_approveService" />
		<property name="approveClientService" ref="CLM_approveClientService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="advancePayService" ref="CLM_advancePayService" />
	</bean>
	<!-- add by sunjl 上载保单列表 -->
	<bean
		class="com.nci.tunan.clm.impl.survey.ucc.impl.UploadPolicySurveyUCCImpl"
		id="CLM_uploadPolicySurveyUCC">
		<property name="uploadPolicySurveyService" ref="CLM_uploadPolicySurveyService" />
	</bean>
	<!-- add by gaoxyit 审核、审批权限 -->
	<bean class="com.nci.tunan.clm.impl.inspect.ucc.impl.ClaimLiabUCCImpl"
		id="CLM_claimLiabUCC">
		<property name="claimLiabService" ref="CLM_claimLiabService" />
	</bean>

	<!-- 既往理赔查询 -->
	<bean
		class="com.nci.tunan.clm.impl.queryforgiveclm.ucc.impl.QueryForgiveClmUccImpl"
		id="CLM_queryForgiveClmUCC">
		<property name="queryForgiveClmService" ref="CLM_queryForgiveClmService" />
	</bean>

	<!-- 既往理赔详细查询 -->
	<bean
		class="com.nci.tunan.clm.impl.queryforgiveclmdetail.ucc.impl.QueryForgiveClmDetailUccImpl"
		id="CLM_queryForgiveClmDetailUCC">
		<property name="queryForgiveClmDetailService" ref="CLM_queryForgiveClmDetailService" />
	</bean>

	<!--理赔影像资料查询 -->
	<bean
		class="com.nci.tunan.clm.impl.queryclmimage.ucc.impl.QueryClmImageUccImpl"
		id="CLM_queryClmImageUCC">
		<property name="clmImageService" ref="CLM_clmImageService" />
	</bean>

	<!-- 报案查询接口 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.ReportCheckYdUCCImpl"
		id="CLM_iReportCheckYdUCC">
		<property name="mobileReportCheckService" ref="CLM_mobileReportCheckService" />
	</bean>

	<!-- 赔案状态追踪列表查询接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.MobileClaimStateTraceUCCImpl"
		id="CLM_iMobileClaimStateTraceUCC">
		<property name="iMobileClaimStateTraceService" ref="CLM_iMobileClaimStateTraceService" />
	</bean>

	<!-- 单证信息查询接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.MobileClaimCheckListUCCImpl"
		id="CLM_iMobileClaimCheckListUCC">
		<property name="iMobileClaimCheckListService" ref="CLM_iMobileClaimCheckListService" />
	</bean>

	<!-- 理赔任务查询接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.MobileClaimTashSurveyUCCImpl"
		id="CLM_iMobileClaimTashSurveyUCC">
		<property name="iMobileClaimTashSurveyService" ref="CLM_iMobileClaimTashSurveyService" />
	</bean>
	<!-- liulei_wb 保全查询理赔既往信息UCC -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryClaimCaseTrackUCCImpl"
		id="CLM_iQueryClaimCaseTrackUCC">
		<property name="mobileReportCheckService" ref="CLM_mobileReportCheckService" />
	</bean>
	<!-- yangjian_wb 保单查询理赔记录信息UCC -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryClaimCaseRecordUCCImpl"
		id="CLM_iQueryClaimCaseRecordUCC">
		<property name="claimLiabService" ref="CLM_claimLiabService" />
	</bean>
	<!-- 赔案号快速查询是否存在服务 -->
	<bean
		class="com.nci.tunan.clm.impl.peripheral.ucc.casenoisexistserv.impl.CasenoIsExistServUCCImpl"
		id="CLM_casenoIsExistServUCC">
		<property name="casenoIsExistServService" ref="CLM_casenoIsExistServService" />
	</bean>
	<!-- 请求外包录入 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.RequestBPOEnterUCCImpl"
		id="CLM_requestBPOEnterUCC">
		<property name="requestBPOEnterService" ref="CLM_requestBPOEnterService" />
	</bean>

	<!-- 再保呈报接口 -->
	<bean
		class="com.nci.tunan.clm.impl.reinsurance.ucc.impl.ClaimReinsuranceReportClientUCCImpl"
		id="CLM_iClaimReinsuranceReportClientUCC">
		<property name="iClaimReinsuranceReportClientService" ref="CLM_iClaimReinsuranceReportClientService" />
	</bean>

	<!-- 再保回复接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.ClaimReinsuranceAnswerUCCImpl"
		id="CLM_iClaimReinsuranceAnswerUCC">
		<property name="iClaimReinsuranceAnswerService" ref="CLM_iClaimReinsuranceAnswerService" />
	</bean>

	<!-- 移动理赔任务处理接口(签收) -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.MobileClaimSignManageUCCImpl"
		id="CLM_iMobileClaimSignManageUCC">
		<property name="applyPersonalTaskService" ref="CLM_applyPersonalTaskService" />
		<property name="signClientService" ref="CLM_signClientService" />
		<property name="iMobileClaimSignManageService" ref="CLM_iMobileClaimSignManageService" />
		<property name="claimCustomerService" ref="CLM_claimCustomerService" />
		<property name="auditClaimCheckListService" ref="CLM_auditClaimCheckListService" />
		<property name="claimSurveyFeeService" ref="CLM_claimSurveyFeeService" />
		<property name="clmSurveyApplyService" ref="CLM_clmSurveyApplyService" />
		<property name="clmSurveyItemService" ref="CLM_clmSurveyItemService" />
		<property name="claimSurveyConclusionService" ref="CLM_claimSurveyConclusionService" />
		<property name="claimSurveyCourseService" ref="CLM_claimSurveyCourseService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="userService" ref="CLM_userService" />
		<property name="claimOrganService" ref="CLM_claimOrganService" />
		<property name="sendMessageService" ref="CLM_sendMessageService" />
	</bean>
	<!-- 自动审核处理 -->
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.ClaimAutoAuditUCCImpl"
		id="CLM_claimAutoAuditUCC">
		<property name="claimAutoAuditService" ref="CLM_claimAutoAuditService" />
		<property name="claimMatchResultService" ref="CLM_claimMatchResultService" />
		<property name="claimMatchCalcService" ref="CLM_claimMatchCalcService" />
		<property name="remitManageService" ref="CLM_remitManageService" />
		<property name="claimContractDealService" ref="CLM_claimContractDealService" />
		<property name="claimLiabService" ref="CLM_claimLiabService" />
		<property name="contractProductService" ref="CLM_contractProductService" />
		<property name="paymentPlanService" ref="CLM_paymentPlanService" />
		<property name="claimMatchResultUCC" ref="CLM_claimMatchResultUCC" />
		 <!-- 理算金额发生变化再次发起再报 -->
		<property name="claimReportUCC" ref="CLM_claimReportUCC"/>
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.common.ucc.impl.AgainInsurSuggesDetaiUCCImpl"
		id="CLM_againInsurSuggesDetaiUCC">
		<property name="againInsurSuggesDetaiService" ref="CLM_againInsurSuggesDetaiService" />
	</bean>
	<!-- 片区 add by huangjh -->
	<bean class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.ClaimAreaUCCImpl"
		id="CLM_claimAreaUCC">
		<property name="claimAreaService" ref="CLM_claimAreaService" />
		<property name="handworkAssignTaskService" ref="CLM_handworkAssignTaskService" />
		<property name="claimAreaOrganService" ref="CLM_claimAreaOrganService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.ClaimAreaOrganUCCImpl"
		id="CLM_claimAreaOrganUCC">
		<property name="claimAreaOrganService" ref="CLM_claimAreaOrganService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.ClaimAreaPersonUCCImpl"
		id="CLM_claimAreaPersonUCC">
		<property name="claimAreaPersonService" ref="CLM_claimAreaPersonService" />
		<property name="handworkAssignTaskService" ref="CLM_handworkAssignTaskService" />
		<property name="claimAreaOrganService" ref="CLM_claimAreaOrganService" />
		<property name="claimAreaService" ref="CLM_claimAreaService" />
	</bean>
	<bean
		class="com.nci.tunan.clm.impl.claimliabpre.ucc.impl.ClaimLiabPreUCCImpl"
		id="CLM_claimLiabPreUCC">
		<property name="claimLiabPreService" ref="CLM_claimLiabPreService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.sign.ucc.impl.AgentUCCImpl"
		id="CLM_claimAgentUCC">
		<property name="agentService" ref="CLM_agentService2" />
	</bean>
	<!-- add by xinghj -->
	<bean class="com.nci.tunan.clm.impl.sendmessage.ucc.impl.SendMessageUCCImpl"
		id="CLM_sendMessageUCC">
		<property name="sendMessageService" ref="CLM_sendMessageService" />
	</bean>
	<!-- 前置调查数据返回接口 add by wujj -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.BeforeSurveyApplyBackUCCImpl"
		id="CLM_iBeforeSurveyApplyBackUCC">
		<property name="iBeforeSurveyApplyBackService" ref="CLM_iBeforeSurveyApplyBackService" />
	</bean>
	<!-- 查询用户对应的调查任务 add by wujj -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.SurveyApplyOfCustomerUCCImpl"
		id="CLM_iSurveyApplyOfCustomerUCC">
		<property name="iSurveyApplyOfCustomerService" ref="CLM_iSurveyApplyOfCustomerService" />
	</bean>
	<!-- 单证扫描时，影像操作权限控制 add by huangjh_wb -->
	<bean class="com.nci.tunan.clm.impl.exports.hs.impl.ClaimScanRegistUCCImpl"
		id="CLM_claimScanRegistUCC">
		<property name="claimScanRegistService" ref="CLM_claimScanRegistService" />
	</bean>

	<!-- 前置调查接口方法管理类 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.BeforeSurveyManageUCCImpl"
		id="CLM_iBeforeSurveyManageUCC">
		<property name="iBeforeSurveyManageService" ref="CLM_iBeforeSurveyManageService" />
	</bean>
	<!-- caoyy 回退历史查询 -->
	<bean
		class=" com.nci.tunan.clm.impl.redo.ucc.impl.QueryHistoryClaimRedoUCCImpl"
		id="CLM_queryHistoryClaimRedoUCC">
		<property name="queryHistoryClaimRedoService" ref="CLM_queryHistoryClaimRedoService" />
		<property name="auditConclusionService" ref="CLM_auditConclusionService" />
		<property name="customerService" ref="CLM_claimCustomerService" />

	</bean>
	<!-- 查询单证信息接口 -->
	<bean
		class=" com.nci.tunan.clm.impl.exports.hs.impl.QueryClaimCheckListUCCImpl"
		id="CLM_queryClaimCheckListUCC">
		<property name="queryClaimCheckListService" ref="CLM_queryClaimCheckListService" />
	</bean>
	<bean class="com.nci.tunan.clm.impl.exports.hs.impl.CsQueryInsuranceUCCImpl"
		id="CLM_queryInsuranceUCC">
		<property name="claimCommonQueryService" ref="CLM_claimCommonQueryService" />
	</bean>
	<!--查询固定渠道的查询条件信息 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryChannelConditionInfoUCCImpl"
		id="CLM_queryChannelConditionInfoUCC">
		<property name="claimSurveyChannelService" ref="CLM_claimSurveyChannelService" />
	</bean>
	<!--查询固定渠道的查询条件信息 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.QueryBeneInfoUCCImpl"
		id="CLM_queryBeneInfoUCC">
		<property name="beneAndPayeService" ref="CLM_ClaimBeneAndPayeService" />
	</bean>
	<!--查询受益人信息 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.QueryChannelInfoUCCImpl"
		id="CLM_queryChannelInfoUCC">
		<property name="claimSurveyChannelService" ref="CLM_claimSurveyChannelService" />
	</bean>

	<!-- 既往重疾新型健康险理赔史查询接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryClaimHistoryToNBUCCImpl"
		id="CLM_queryClaimHistoryToNBUCC">
		<property name="claimHistoryToNBService" ref="CLM_claimHistoryToNBService" />
	</bean>

	<!-- 既往新型健康险理赔史查询接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryNewHealthClaimHistoryToNBUCCImpl"
		id="CLM_queryNewHealthClaimHistoryToNBUCC">
		<property name="claimNewHealthHistoryToNBService" ref="CLM_claimNewHealthHistoryToNBService" />
	</bean>

	<!-- 调查轨迹 -->
	<bean id="CLM_surveyBrowseUCC"
		class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyBrowseUCCImpl">
		<property name="surveyBrowseService" ref="CLM_surveyBrowseService"></property>
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService"></property>
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService"></property>
		<property name="customerService" ref="CLM_claimCustomerService"></property>
		<property name="surveyConclusionService" ref="CLM_claimSurveyConclusionService"></property>
		<property name="surveyCourseService" ref="CLM_claimSurveyCourseService"></property>
		<property name="surveyFeeService" ref="CLM_claimSurveyFeeService"></property>
		<property name="clmSurveyItemService" ref="CLM_clmSurveyItemService"></property>
	</bean>
	<!-- add by xinghj -->
	<bean id="CLM_outSourceUCC"
		class="com.nci.tunan.clm.impl.outsource.ucc.impl.OutSourceUCCImpl">
		<property name="outSourceService" ref="CLM_outSourceService"></property>
	</bean>
	<bean id="CLM_claimUserUCC" class="com.nci.tunan.clm.impl.user.ucc.impl.IUserUCCImpl">
		<property name="userService" ref="CLM_claimUserService"></property>
	</bean>
	<bean id="CLM_surveyObjectUCC"
		class="com.nci.tunan.clm.impl.survey.ucc.impl.SurveyObjectUCCImpl">
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService"></property>
	</bean>
	<bean id="CLM_claimBFSurveyUCC"
		class="com.nci.tunan.clm.impl.exports.ws.impl.ClaimBFSurveyUCCImpl">
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService"></property>
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService"></property>
		<property name="surveyCourseService" ref="CLM_claimSurveyCourseService"></property>
		<property name="surveyConclusionService" ref="CLM_claimSurveyConclusionService"></property>
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil"></property>
	</bean>
	<bean id="CLM_claimRecheckSurveyUCC"
		class="com.nci.tunan.clm.impl.exports.ws.impl.ClaimRecheckSurveyUCCImpl">
		<property name="surveyApplyService" ref="CLM_clmSurveyApplyService"></property>
		<property name="surveyObjectService" ref="CLM_claimSurveyObjectService"></property>
		<property name="surveyCourseService" ref="CLM_claimSurveyCourseService"></property>
		<property name="surveyFeeService" ref="CLM_claimSurveyFeeService"></property>
		<property name="surveyConclusionService" ref="CLM_claimSurveyConclusionService"></property>
	</bean>
	<!-- 服务方接口-查询片区数据 -->
	<bean class="com.nci.tunan.clm.impl.exports.hs.impl.UwQueryAreaUCCImpl"
		id="CLM_uwQueryAreaUCC">
		<property name="uwQueryAreaService" ref="CLM_uwQueryAreaService" />
	</bean>
	<!-- 服务方接口-查询二核结论最新数据 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryNewUwConclusionUCCImpl"
		id="CLM_queryNewUwConclusionUCC">
		<property name="queryNewUwConclusionService" ref="CLM_queryNewUwConclusionService" />
	</bean>

	<!-- 理赔报案提醒查询接口 -->
	<bean id="CLM_queryClaimReportInfoUCC"
		class="com.nci.tunan.clm.impl.exports.ws.impl.QueryClaimReportInfoUCCImpl">
		<property name="claimCommonQueryService" ref="CLM_claimCommonQueryService" />
	</bean>

	<!-- 核保绩优业务员赔案号维护接口 -->
	<bean id="CLM_queryCaseAgentUCC"
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryCaseAgentUCCImpl">
		<property name="claimCommonQueryService" ref="CLM_claimCommonQueryService" />
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
	</bean>

	<bean id="CLM_queryCountCaseByAgentUCC"
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryCountCaseByAgentUCCImpl">
		<property name="claimCommonQueryService" ref="CLM_claimCommonQueryService" />
		<property name="clmServiceUtil" ref="CLM_clmServiceUtil" />
	</bean>
	<!-- 理赔角色维护ucc -->
	<bean id="CLM_claimRoleServicingUCC"
		class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimRoleServicingUCCImpl">
		<property name="claimRoleServicingService" ref="CLM_claimRoleServicingService" />
	</bean>
	<!-- 核赔作业大屏实时监控 -->
	<bean id="CLM_claimRealTimeMonitoringTaskUCC"
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.ClaimRealTimeMonitoringTaskUCCImpl">
		<property name="claimRealTimeMonitoringTaskService" ref="CLM_claimRealTimeMonitoringTaskService" />
	</bean>
	<!-- add by liulei_wb 发起续保人工二核 -->
	<bean class="com.nci.tunan.clm.impl.renewal.ucc.impl.ClaimRenewalUwUCC"
		id="CLM_claimRenewalUwUCC">
		<property name="claimRenewalUwService" ref="CLM_claimRenewalUwService" />
	</bean>
	<!-- end -->
	<!-- 案件模型清单 -->
	<bean
		class="com.nci.tunan.clm.impl.querylist.ucc.impl.ClaimRiskLevelDoubtfulUCCImpl"
		id="CLM_claimRiskLevelDoubtfulUCC">
		<property name="claimRiskLevelDoubtfulService" ref="CLM_claimRiskLevelDoubtfulService" />
		<property name="claimListNameService" ref="CLM_claimListNameServiceImpl" />
	</bean>
	<!-- add by liulei_wb 移动理赔单证批次号回写接口 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.UpdateScanBatchNoUCCImpl"
		id="CLM_updateScanBatchNoUCC">
		<property name="imageCorrectionService" ref="CLM_imageCorrectionService" />
	</bean>
	<!-- end -->
	<!-- 查询在途业务/更新合并客户号 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryTransitCaseOrUpdateUCCImpl"
		id="CLM_queryTransitCaseOrUpdateUCC">
		<property name="queryTransitCaseOrUpdateService" ref="CLM_queryTransitCaseOrUpdateService" />
	</bean>
	<!-- 保单服务人员信息查询接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.WechatMobileQueryPolicyAgentInfoUCCImpl"
		id="CLM_wechatMobileQueryPolicyAgentInfoUCC">
		<property name="wechatMobileQueryPolicyAgentInfoService" ref="CLM_wechatMobileQueryPolicyAgentInfoService" />
	</bean>
	<!-- 出险人信息查询接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.WechatMobileQueryCustomerUCCImpl"
		id="CLM_wechatMobileQueryCustomerUCC">
		<property name="wechatMobileQueryPolicyAgentInfoService" ref="CLM_wechatMobileQueryPolicyAgentInfoService" />
	</bean>
	<!-- 五要素查询保单信息接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.WechatMobileQueryMasterUCCImpl"
		id="CLM_wechatMobileQueryMasterUCC">
		<property name="wechatMobileQueryPolicyAgentInfoService" ref="CLM_wechatMobileQueryPolicyAgentInfoService" />
	</bean>
	<!-- 根据出险人号查询赔案信息 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.WechatMobileQueryCaseByCustomerUCCImpl"
		id="CLM_wechatMobileQueryCaseByCustomerUCC">
		<property name="wechatMobileQueryPolicyAgentInfoService" ref="CLM_wechatMobileQueryPolicyAgentInfoService" />
	</bean>
	<!-- 根据赔案号查询赔案进度信息 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.WechatMobileQueryCaseSpeedUCCImpl"
		id="CLM_wechatMobileQueryCaseSpeedUCC">
		<property name="wechatMobileQueryPolicyAgentInfoService" ref="CLM_wechatMobileQueryPolicyAgentInfoService" />
	</bean>
	<!-- 查询理赔案件信息接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.WechatMobileQueryCaseInfoUCCImpl"
		id="CLM_wechatMobileQueryCaseInfoUCC">
		<property name="wechatMobileQueryPolicyAgentInfoService" ref="CLM_wechatMobileQueryPolicyAgentInfoService" />
	</bean>

	<!-- #69356 需求取消43419个人税收递延型养老年金保险理赔系统需求start -->
	<!-- 税延客户概要信息查询接口start -->
	<!-- <bean class="com.nci.tunan.clm.claimSYServiceClient.customerProfileInfoQuery.ucc.impl.CustomerProfileInfoQueryUCCImpl" 
		id="CLM_customerProInfoQryUcc"> <property name="customerProInfoQryService" 
		ref="CLM_customerProInfoQryService"/> </bean> -->
	<!-- 税延客户概要信息查询接口end -->
	<!-- 税延理赔信息上传接口start -->
	<!-- <bean class="com.nci.tunan.clm.claimSYServiceClient.claimInfoUpload.ucc.impl.ClaimInfoUploadUCCImpl" 
		id="CLM_claimInfoUploadUCC"> <property name="claimInfoUploadService" ref="CLM_claimInfoUploadService"/> 
		</bean> -->
	<!-- 税延理赔信息上传接口end -->
	<!--43419 liupengit1 税延产品-扣税金额计算接口(END012)start -->
	<!-- <bean class="com.nci.tunan.clm.taxDeferred.ucc.TaxDeductAmountUccImpl" 
		id="CLM_taxDeductAmountUcc"> <property name="taxDeductAmountService" ref="CLM_taxDeductAmountService"/> 
		</bean> -->
	<!-- 43419 liupengit1 税延产品-扣税金额计算接口(END012) end -->
	<!-- #69356 需求取消43419个人税收递延型养老年金保险理赔系统需求end -->
	<!-- #76401 需求取消45061个人税收递延型养老年金保险保全功能需求start -->
	<!-- 税延交费可用税延额度查询接口start -->
	<!-- <bean class="com.nci.tunan.clm.claimSYServiceClient.taxAmountQuery.ucc.impl.TaxAmountQueryUCCImpl" 
		id="CLM_TaxAmountQueryUCC"> <property name="taxAmountQueryService" ref="CLM_taxAmountQueryService"/> 
		</bean> -->
	<!-- 税延交费可用税延额度查询接口end -->
	<!-- #76401 需求取消45061个人税收递延型养老年金保险保全功能需求end -->

	<!-- 个险理赔信息同步上传接口start -->
	<bean class="com.nci.tunan.clm.shhealthcare.ucc.ClmSynchronUccImp"
		id="CLM_synchronUcc">
		<property name="clmSynchronService" ref="CLM_synchronService" />
		<property name="shMedicalLogUcc" ref="CLM_shMedicalLogUcc" />
	</bean>
	<!-- 个险理赔信息同步上传接口end -->
	<!-- 个险理赔注销同步上传接口start -->
	<bean class="com.nci.tunan.clm.shhealthcare.ucc.ClmLogOutUccImp"
		id="CLM_logOutUcc">
		<property name="clmLogOutService" ref="CLM_logOutService" />
		<property name="shMedicalLogUcc" ref="CLM_shMedicalLogUcc" />
	</bean>
	<!-- 个险理赔注销同步上传接口end -->
	<!-- 个险理赔申请接口（服务方）start -->
	<bean class="com.nci.tunan.clm.shhealthcare.ucc.HealthRebatesUCCImp"
		id="CLM_healthRebatesUCCImp">
		<property name="phoneInfoService" ref="CLM_shPhoneInfoService" />
		<property name="clmSynchronService" ref="CLM_synchronService" />
	</bean>
	<!-- 个险理赔申请接口（服务方）end -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryInformationByCaseNOUCCImpl"
		id="CLM_queryInformationByCaseNOUCCImpl">
		<property name="claimCommonQueryService" ref="CLM_claimCommonQueryService" />
	</bean>
	<!-- 续保退保请求同步上传接口理赔退费 -->
	<bean class="com.nci.tunan.clm.shhealthcare.ucc.CLMKeepORExitUCCImpl"
		id="CLM_clmKeepORExitUCC">
		<property name="clmKeepOrExitService" ref="CLM_clmKeepORExitService" />
	</bean>
	<!--44202 panjj_wb 欺诈风险数据清单 -->
	<bean class="com.nci.tunan.clm.impl.querylist.ucc.impl.ClaimCheatListUCCImpl"
		id="CLM_ClaimCheatListUCC">
		<property name="claimCheatListService" ref="CLM_claimCheatListService" />
	</bean>
	<!-- 44202 panjj_wb 欺诈风险数据清单 end -->
	<!-- 服务方接口-接收核保退回的相关数据 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.UwAcceptUnderBackResultUCCImpl"
		id="CLM_uwAcceptUnderBackResultUCC">
		<property name="uwAcceptUnderBackResultService" ref="CLM_uwAcceptUnderBackResultService" />
	</bean>
	<!-- 理赔再保限额设置 -->
	<bean
		class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimReinsuranceLimitUCCImpl"
		id="CLM_claimReinsuranceLimitUCC">
		<property name="claimReinsuranceLimitService" ref="CLM_claimReinsuranceLimitService" />
	</bean>

	<!-- 上海医保-理赔报案 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.HealthcareClmApplyUCCImpl"
		id="CLM_healthcareClmApplyUCC">
		<property name="healthUCC" ref="CLM_healthRebatesUCCImp" />
		<property name="healthcareClmAppplyThread" ref="CLM_healthcareClmAppplyThread" />
		<property name="shMedicalLogUcc" ref="CLM_shMedicalLogUcc" />
	</bean>
	<bean class="com.nci.tunan.clm.shhealthcare.util.HealthcareClmAppplyThread"
		id="CLM_healthcareClmAppplyThread">
		<property name="healthUCC" ref="CLM_healthRebatesUCCImp" />
	</bean>
	
	<!-- 理赔查询，保单调用 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryAmountForPAUCCImpl" id="CLM_queryAmountForPAUCC">
		<property name="claimPayChangeService" ref="CLM_iClaimPayChangeService" />
	</bean>
	
	<!-- 上海医保日志 -->
	<bean class="com.nci.tunan.clm.shhealthcare.ucc.ShMedicalLogUccImpl"
		id="CLM_shMedicalLogUcc" scope="prototype">
		<property name="shMedicalLogService" ref="CLM_shMedicalLogService" />
		<property name="usersService" ref="usersService" />
	</bean>
	<!-- 移动签收电子签名分享轨迹回传接口 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.MobileSignElectronicSignatureUCCImpl"
		id="CLM_mobileSignElectronicSignatureUCC">
		<property name="mobileSignElectronicSignatureService" ref="CLM_mobileSignElectronicSignatureService" />
		<property name="userService" ref="CLM_userService" />
	</bean>

	<!-- 理赔反洗钱优化 -->
	<bean
		class="com.nci.tunan.clm.impl.audit.ucc.impl.ClaimAntiMoneyLaunderingUCCImpl"
		id="CLM_claimAntiMoneyLaunderingUCC">
		<property name="claimAntiMoneyLaunderingService" ref="CLM_claimAntiMoneyLaunderingService" />
	</bean>

	<!-- 核赔监测范围配置 -->
	<bean
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.MonitoringPersonUCCImpl"
		id="CLM_monitoringPersonUCC">
		<property name="monitoringPersonService" ref="CLM_monitoringPersonService" />
	</bean>
	<!-- 中保信对接设置 -->
	<bean
		class="com.nci.tunan.clm.impl.parameter.ucc.impl.CiitcLinkSwitchSetUCCImpl"
		id="CLM_ciitcLinkSwitchSetUCC">
		<property name="ciitcLinkSwitchSetService" ref="CLM_ciitcLinkSwitchSetService" />
		<property name="claimSurveyRuleService" ref="CLM_claimSurveyRuleService" />
	</bean>

	<!-- 理赔直赔 start -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.DirectClaimPortsUCCImpl"
		id="CLM_directClaimPortsUCC">
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
		<property name="claimReportService" ref="CLM_claimReportService" />
		<property name="accResultQueryDAO" ref="CLM_accResultQueryDAO" />
		<property name="claimCaseDao" ref="CLM_claimCaseDao"/>
		<property name="claimHospitalServiceDao" ref="CLM_claimHospitalServiceDao"/>
	</bean>

	<!-- 理赔直赔end -->

	<!-- 服务方接口-接收核保的相关数据查询二核结论为不自动续保的数据 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.UwShortHealthInsuranceResultUCCImpl"
		id="CLM_uwShortHealthInsuranceResultUCC">
		<property name="uwShortHealthInsuranceResultService" ref="CLM_uwShortHealthInsuranceResultService" />
	</bean>

	<!-- 服务方接口-接收核保的相关数据更新理赔子系统中该险种最近一次的核保结论和特约内容 -->
	<bean class="com.nci.tunan.clm.impl.exports.hs.impl.ModifyUwConditionUCCImpl"
		id="CLM_modifyUwConditionUCC">
		<property name="modifyUwConditionService" ref="CLM_modifyUwConditionService" />
	</bean>
	<!-- 超期核定管理 -->
	<bean
		class="com.nci.tunan.clm.impl.overdueApproved.ucc.impl.OverdueApprovedUCCImpl"
		id="CLM_overdueApprovedUCC">
		<property name="overdueApprovedService" ref="CLM_overdueApprovedService" />
	</bean>
	<!-- 外包工作时间配置 -->
	<bean
		class="com.nci.tunan.clm.impl.register.ucc.impl.ClaimBpoTimeConfigUCCImpl"
		id="CLM_claimBpoTimeConfigUCC">
		<property name="claimBpoTimeConfigService" ref="CLM_claimBpoTimeConfigService" />
	</bean>
	<!-- 核赔岗前用户权限配置 -->
	<bean
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.ClaimpermissionUCCImpl"
		id="CLM_iClaimPermissionUCC">
		<property name="claimPermissionService" ref="CLM_claimPermissionService" />
	</bean>

	<!-- 服务方接口-理赔应付校验收付费转支付方式通知 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.ClaimChangePayModeUCCImpl"
		id="CLM_claimChangePayModeUCC">
		<property name="claimChangePayModeService" ref="CLM_claimChangePayModeService" />
	</bean>
	<!-- 服务方接口-理赔已结案件保项信息查询 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryEndCaseLiabListUCCImpl"
		id="CLM_queryEndCaseLiabListUCC">
		<property name="queryEndCaseLiabListService" ref="CLM_queryEndCaseLiabListService" />
	</bean>

	<!-- 开户行信息 -->
	<bean class="com.nci.tunan.clm.impl.register.ucc.impl.BankOfDepositUCCImpl"
		id="CLM_bankOfDepositUCC">
		<property name="bankOfDepositService" ref="CLM_bankOfDepositService" />
	</bean>

	<!-- add by wangcx1_wb 法人信息查询 -->
	<bean class="com.nci.tunan.clm.impl.legalPerson.ucc.impl.LegalPersonUCCImpl"
		id="CLM_legalPersonUCC">
		<property name="legalPersonService" ref="CLM_legalPersonService" />
	</bean>

	<!-- 直连就诊信息返回 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.QryNsuInfoResUCCImpl"
		id="CLM_qryNsuInfoResUCC">
		<property name="directClaimPortsUCC" ref="CLM_directClaimPortsUCC" />
		<property name="qryNsuInfoResService" ref="CLM_qryNsuInfoResService" />
		<property name="claimDirectConnApplyDao" ref="CLM_claimDirectConnApplyDao" />
		<property name="claimDirectConnAuthDao" ref="CLM_claimDirectConnAuthDao" />
		<property name="memoService" ref="CLM_memoService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="claimBillService" ref="CLM_claimBillService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
		<property name="userDao" ref="CLM_claimUserDao" />
	</bean>

	<!-- 直连就诊信息调取 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.QryNsuInfoReqUCCImpl"
		id="CLM_qryNsuInfoReqUCC">
		<property name="directClaimPortsUCC" ref="CLM_directClaimPortsUCC" />
		<property name="paraDefUCC" ref="CLM_iClaimParaDefUCC" />
		<property name="qryNsuInfoReqService" ref="CLM_qryNsuInfoReqService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
		<property name="claimBillService" ref="CLM_claimBillService" />
		<property name="dataCollectService" ref="CLM_dataCollectService" />
		<property name="claimApplicantService" ref="CLM_claimApplicantService" />
		<property name="claimInsuredDao" ref="CLM_claimInsuredDao" />
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
	</bean>

	<!-- 理赔查验配置功能UCC -->
	<bean
		class="com.nci.tunan.clm.impl.taskmanage.ucc.impl.ClaimRealnameInspectionUCCImpl"
		id="CLM_claimRealnameInspectionUCC">
		<property name="claimRealnameInspectionService" ref="CLM_claimRealnameInspectionService" />
	</bean>
	<!-- 直连数据调取核查/直连问题案件池 -->
	<bean
		class="com.nci.tunan.clm.impl.directConn.UCC.impl.ClaimDirectConnUCCImpl"
		id="CLM_claimDirectConnUCC">
		<property name="claimDirectConnService" ref="CLM_claimDirectConnService" />
	</bean>

	<!-- 理赔实名查验ucc -->
	<bean
		class="com.nci.tunan.clm.impl.common.ucc.impl.ClaimRealNameCheckUCCImpl"
		id="CLM_claimRealNameCheckUCC">
		<property name="claimRealNameCheckService" ref="CLM_claimRealNameCheckService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
	</bean>

	<!-- 客户历史修改记录ucc -->
	<bean class="com.nci.tunan.clm.impl.audit.ucc.impl.ClaimUserHistoryUCCImpl"
		id="CLM_claimUserHistoryUCC">
		<property name="claimUserHistoryService" ref="CLM_claimUserHistoryService" />
	</bean>

	<!-- 服务方接口-某一年度是否发生理赔接口信息查询 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.QueryPolicyYearEndCaseUCCImpl"
		id="CLM_queryPolicyYearEndCaseUCC">
		<property name="queryPolicyYearEndCaseService" ref="CLM_queryPolicyYearEndCaseService" />
	</bean>


	<!-- 投保必调配置 -->
	<bean
		class="com.nci.tunan.clm.impl.claimsPortrait.ucc.impl.InsureListUCCImpl" id="CLM_insureListUCC">
		<property name="insureListService" ref="CLM_insureListService" />
	</bean>


	<!-- 智能理赔画像页面 -->
	 <bean
		class="com.nci.tunan.clm.impl.capacityClaim.ucc.impl.ClaimIntelPortraitUCCImpl"
		id="CLM_claimIntelPortraitUCC">
		<property name="claimIntelPortraitServiceImpl" ref="CLM_claimIntelPortraitService" />
	</bean> 

	<!-- 智能理赔画像配置页面 -->
	 <bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimIntelPortraitConfigUCCImpl" id="CLM_claimIntelPortraitConfigUCC"> 
		<property name="claimIntelPortraitConfigServiceImpl"  ref="CLM_claimIntelPortraitConfigServiceImpl" />
    </bean>

	 <!-- 赔案号查询 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.ws.impl.ClaimNumberInquiryUCCImpl" id="CLM_claimNumberInquiryUCC">
		<property name="claimNumberInquiryService" ref="CLM_claimNumberInquiryService" />
	</bean> 
	
	<!-- 增补告知 -->
	<bean
		class="com.nci.tunan.clm.impl.exports.hs.impl.ClaimHITaskBackUCCImpl" id="CLM_claimHITaskBackUCC">
		<property name="iClaimHITaskBackService" ref="CLM_iClaimHITaskBackService" />
	</bean> 
	
	 <!-- 待处理业务 -->
	<bean
		class="com.nci.tunan.clm.impl.claimTaskStatus.ucc.impl.BfSurveyHITaskManageUCCImpl" id="CLM_iBfSurveyHITaskManageUCC">
		<property name="iBfSurveyHITaskManageService" ref="CLM_iBfSurveyHITaskManageService" />
	</bean>  
	<!-- 险种特殊录入规则配置 -->
	<bean class="com.nci.tunan.clm.impl.memo.ucc.impl.ClaimBusiSpecialRuleUCCImpl" id="CLM_claimBusiSpecialRuleUCC">
		<property name="claimBusiSpecialRuleService" ref="CLM_claimBusiSpecialRuleService" />
	</bean>
	
	<!-- 智能风控-抽取检查任务配置 -->
	<bean class="com.nci.tunan.clm.impl.riskControl.ucc.impl.ClaimDrawInspectTaskUCCImpl" id="CLM_claimDrawInspectTaskUCC">
		<property name="claimDrawInspectTaskService"  ref="CLM_claimDrawInspectTaskService" />
    </bean>
    <!-- 智能风控-任务申请与处理 -->
    <bean class="com.nci.tunan.clm.impl.riskControl.ucc.impl.ClaimInspectTaskPoolUCCImpl" id="CLM_claimInspectTaskPoolUCC">
		<property name="claimInspectTaskPoolService"  ref="CLM_claimInspectTaskPoolService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
    </bean>
    <!-- 智能风控-差错反馈与修订 -->
    <bean class="com.nci.tunan.clm.impl.riskControl.ucc.impl.ClaimReviseTaskPoolUCCImpl" id="CLM_claimReviseTaskPoolUCC">
		<property name="claimReviseTaskPoolService"  ref="CLM_claimReviseTaskPoolService" />
		<property name="claimInspectTaskPoolService"  ref="CLM_claimInspectTaskPoolService" />
		<property name="claimCaseService" ref="CLM_claimCaseService" />
    </bean>
	
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.QueryCaseDataUCCImpl" id="CLM_queryCaseDataUCC">
		<property name="queryCaseDataService" ref="CLM_queryCaseDataService" />
	</bean>
		<!-- 定制风控库配置-->
	<bean class="com.nci.tunan.clm.impl.riskLibrary.impl.ClaimRiskLibraryUCCImpl" id="CLM_claimRiskLibraryUCC">
		<property name="claimRiskLibraryService" ref="CLM_claimRiskLibraryService" />
	</bean>
		<!-- 风控预警配置-->
	<bean class="com.nci.tunan.clm.impl.riskLibrary.impl.ClaimRiskWarnQueryUCCImpl" id="CLM_claimRiskWarnQueryUCC">
		<property name="claimRiskWarnQueryService" ref="CLM_claimRiskWarnQueryService" />
	</bean>
	<!-- 制定检查项目 -->
	<bean class="com.nci.tunan.clm.impl.riskControl.ucc.impl.ClaimCreateInspectProUCCImpl" id="CLM_claimCreateInspectProUCC">
		<property name="claimCreateInspectProService" ref="CLM_claimCreateInspectProService" />
		<property name="claimRiskLibraryService" ref="CLM_claimRiskLibraryService" />
	</bean>
	<!-- 理赔历史赔案调取 -->
	<bean class="com.nci.tunan.clm.impl.claimHiscaseRetrieval.ucc.impl.ClaimHiscaseRetrievalUCCImpl" id="CLM_claimHiscaseRetrievalUCC">
		<property name="claimHiscaseRetrievalService" ref="CLM_claimHiscaseRetrievalService" />
	</bean>
		<!-- 未决预估参数配置 -->
	<bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimPreCalcpayConfigUCCImpl" id="CLM_claimPreCalcpayConfigUCC">
		<property name="claimPreCalcpayConfigService" ref="CLM_claimPreCalcpayConfigService"/>
	</bean> 
	<!-- 事件账单查询 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.QueryEventBillingUCCImpl" id="CLM_queryEventBillingUCC">
		<property name="queryEventBillingService" ref="CLM_queryEventBillingService"/>
	</bean> 
	<!-- 理赔快赔 start -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.RapidClaimPortsUCCImpl" id="CLM_rapidClaimPortsUCC">
		<property name="rapidClaimPortsService" ref="CLM_rapidClaimPortsService" />
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
	</bean>
	<!-- 案件锁定解锁 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.ClaimCaseLockAndUnlockUCCImpl" id="CLM_claimCaseLockAndUnlockUCC">
		<property name="claimCaseLockAndUnlockService" ref="CLM_claimCaseLockAndUnlockService" />
	</bean>
	<!-- 续保核保审核（理赔） -->
	<bean class="com.nci.tunan.clm.impl.exports.hs.impl.QueryCompensateConditionUCCImpl" id="CLM_queryCompensateConditionUCC"> 
	     <property name="queryCompensateConditionService" ref="CLM_queryCompensateConditionService"/>  
	</bean>
	<!-- 苏州快赔-保险机构上传授权文件接口 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.DirectConnDateSZApplyReqUCCImpl" id="CLM_directConnDateSZApplyReqUCC"> 
	     <property name="dateSZApplyReqService" ref="CLM_directConnDateSZApplyReqService"/>
	     <property name="claimCaseService" ref="CLM_claimCaseService" />
	     <property name="claimApplicantService" ref="CLM_claimApplicantService" />
	     <property name="qryNsuInfoReqService" ref="CLM_qryNsuInfoReqService" />
	     <property name="rapidClaimPortsService" ref="CLM_rapidClaimPortsService" />
	     <property name="paraDefUCC" ref="CLM_iClaimParaDefUCC" />
	     <property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
	</bean>
	<!-- 是否存在身故的赔案（理赔） -->
	<bean class="com.nci.tunan.clm.impl.exports.hs.impl.QueyHasDeathClaimUCCImpl" id="CLM_queyHasDeathClaimUCC">
		<property name="queryHasDeathClaimService" ref="CLM_queryHasDeathClaimService" />
	</bean>
	<!-- 浙江省医疗数据信息 start -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.ZJRapidClaimPortsUCCImpl"
		id="CLM_zJRapidClaimPortsUCC">
		<property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
		<property name="zJRapidClaimPortsService" ref="CLM_zJRapidClaimPortsService" />
	</bean>
	<!-- 睡眠保单清理清单 -->
	<bean class="com.nci.tunan.clm.impl.querylist.ucc.impl.ClaimMarkingInfoListUCCImpl" id="CLM_claimMarkingInfoListUCC">
		<property name="claimMarkingInfoListService" ref="CLM_claimMarkingInfoListService" />
	</bean>
	<!-- 垫付信息查看 -->
	<bean class="com.nci.tunan.clm.impl.advance.ucc.impl.ClaimAdvanceInfoUCCImpl" id="CLM_claimAdvanceInfoUCC"> 
	     <property name="claimAdvanceInfoService" ref="CLM_claimAdvanceInfoService"/>
	</bean>
	<!-- 上海一码通赔直付接口 -->
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.YmtpClaimPortsUCCImpl" id="CLM_ymtpClaimPortsUCC"> 
	     <property name="ymtpClaimPortsService" ref="CLM_ymtpClaimPortsService"/>
	     <property name="accResultQueryDAO" ref="CLM_accResultQueryDAO" />
	     <property name="claimReportService" ref="CLM_claimReportService" />
	     <property name="claimCaseDao" ref="CLM_claimCaseDao" />
	     <property name="customerDao" ref="CLM_claimCustomerDao"/>
	     <property name="queryForgiveClmDetailDao" ref="CLM_queryForgiveClmDetailDao"/>
	     <property name="directClaimPortsService" ref="CLM_directClaimPortsService" />
	</bean>
	<!-- 直赔快赔险种管理 -->
	<bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.ClaimDirectBusiUCCImpl" id="CLM_claimDirectBusiUCC">
		<property name="claimDirectBusiService" ref="CLM_claimDirectBusiService"/>
	</bean>
	<!-- 关联图谱实时规则提示配置 -->
	<bean class="com.nci.tunan.clm.impl.parameter.ucc.impl.CheatAtlasConfigUCCImpl" id="CLM_cheatAtlasConfigUCC">
		<property name = "cheatAtlasConfigService" ref = "CLM_cheatAtlasConfigService"/>
	</bean>
	<bean class="com.nci.tunan.clm.impl.exports.ws.impl.ZsxhCloseReportUCCImpl" id="CLM_zsxhCloseReportUCC">
		<property name="zsxhCloseReportService" ref="CLM_zsxhCloseReportService" />
	</bean>
</beans>
