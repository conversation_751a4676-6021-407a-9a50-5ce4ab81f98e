<config>
    <receiveTimeout>6000000000</receiveTimeout>
    <keystore>dgIjcNbgfOo=</keystore>
    <sysname>core</sysname>
    <webPort>8092</webPort>
    <webIp>***********</webIp>
    <batchPort>8092</batchPort>
    <areaCode>zh</areaCode>
    <tempDir>c:\temp</tempDir>
    <cache>
        <cache_status>false</cache_status>
        <cache_org>470000000</cache_org>
    </cache>
    <domain>
        <current-domain>product-domain</current-domain>
        <developer-domain>ecmdev.wucb.com</developer-domain>
        <test-domain>ecmtest.wucb.com</test-domain>
        <product-domain>ecm.wucb.com</product-domain>
    </domain>

    <extZipArchivePath>/tmp/archive/</extZipArchivePath>
    <xmlSchemaArchive>/tmp/archive/</xmlSchemaArchive>
    <xmlSchemaGetContent>/tmp/archive/</xmlSchemaGetContent>
    <xmlSchemaHandover>/tmp/archive/</xmlSchemaHandover>

    <SingleContentServiceAddress>ECSS_WebServices/services/NCISingleContentService</SingleContentServiceAddress>
    <SingleContentServiceNameSpace>http://nci.service.ecss.git.com/</SingleContentServiceNameSpace>
    <SingleContentServiceName>SingleContentServiceImplService</SingleContentServiceName>

    <BatchContentServiceAddress>ECSS_WebServices/services/NCIBatchContentService</BatchContentServiceAddress>
    <BatchContentServiceNameSpace>http://nci.service.ecss.git.com/</BatchContentServiceNameSpace>
    <BatchContentServiceName>BatchContentServiceImplService</BatchContentServiceName>

    <PageRegisterServiceAddress>ECSS_WebServices/services/NCIPageRegisterService</PageRegisterServiceAddress>
    <PageRegisterServiceNameSpace>http://nci.service.ecss.git.com/</PageRegisterServiceNameSpace>
    <PageRegisterServiceName>PageRegisterServiceImplService</PageRegisterServiceName>
</config>