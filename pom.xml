<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.nci.tunan.cap</groupId>
    <artifactId>cap-impl</artifactId>
    <packaging>jar</packaging>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <common.version>0.5.5.3</common.version>
        <cap.version>0.5.0</cap.version>
    </properties>
    <version>${cap.version}</version>
    <parent>
        <groupId>com.nci.udmp</groupId>
        <artifactId>udmp-parent</artifactId>
        <version>0.5.5.5</version>
    </parent>
    <dependencies>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>cap-interface</artifactId>
            <version>${cap.version}</version>
        </dependency>
        <dependency>
			<groupId>com.nci.core.common</groupId>
			<artifactId>commonbiz-impl</artifactId>
			<version>${common.version}</version>
		</dependency>
	    <dependency>
			<groupId>com.nci.core.clm</groupId>
			<artifactId>clm-impl</artifactId>
			<version>${cap.version}</version>
		</dependency>
		<!-- VMS XML文件操作引用 -->
        <dependency>
          <groupId>com.thoughtworks.xstream</groupId>
          <artifactId>xstream</artifactId>
          <version>1.4.3</version>
        </dependency>
        <!-- SMB文件操作引用 -->
        <dependency>
           <groupId>com.nci.core.cap</groupId>
           <artifactId>jcifs</artifactId>
           <version>1.3.18</version>
        </dependency>
        
        <!-- <dependency>
 		 <groupId>com.jcraft</groupId>
  		<artifactId>jsch</artifactId>
  		<version>0.1.38</version>
		</dependency> -->
    </dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>1.7</source>
					<target>1.7</target>
					<proc>none</proc>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>